//
//  UIButton+KALabelRender.m
//  JDISVKAUIKitModule
//
//  Created by 罗静 on 2021/8/27.
//

#import "UIButton+KALabelRender.h"

#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <objc/runtime.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>

#import "KAUIKitTool.h"

@implementation UIButton (KALabelRender)

- (void)configKALabelTitle:(NSString *)title {
    [self setTitle:title forState:UIControlStateNormal];
    [self setTitle:title forState:UIControlStateHighlighted];
    [self setTitle:title forState:UIControlStateDisabled];
}

#pragma mark - L1

- (void)renderL1 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#60"];
        [self renderL1WithCornerRadius:r60];
//    }];
}

- (void)renderL1WithCornerRadius:(CGFloat)cornerRadius {
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C2") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    [self addTarget:self action:@selector(jdisv_L1_seletAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)jdisv_L1_seletAction {
    self.jdisv_selected_L1 = @(!self.jdisv_selected_L1.boolValue);
}

- (NSNumber *)jdisv_selected_L1 {
    return objc_getAssociatedObject(self, @selector(jdisv_selected_L1));
}

- (void)jdisv_setSelected_L1:(NSNumber *)isSelected {
    objc_setAssociatedObject(self, @selector(jdisv_selected_L1), isSelected, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isSelected.boolValue == NO) {
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C2") forState:UIControlStateNormal];
        [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
        if (@available(iOS 8.2, *)) {
            self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
        } else {
            // Fallback on earlier versions
        }
    } else {
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9-3", 0.07) forState:UIControlStateNormal];
        [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9-3") forState:UIControlStateNormal];
        if (@available(iOS 8.2, *)) {
            self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
        } else {
            // Fallback on earlier versions
        }
    }
}

#pragma mark - L2

- (void)renderL2 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r61 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#61"];
        [self renderL2WithCornerRadius:r61];
//    }];
}

- (void)renderL2WithCornerRadius:(CGFloat)cornerRadius {
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    
    self.adjustsImageWhenHighlighted = NO;
    // 设置圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    
    @weakify(self)
    [RACObserve(self,enabled) subscribeNext:^(NSNumber * _Nullable x) {
        @strongify(self)
        // 设置边框
        JDISVColorPicker colorPicker = [[JDISVThemeColor sharedInstance] pickerWithKey:@"#C9"];
        UIColor *color = colorPicker(JDISVThemeManager.sharedManager.themeVersion);
        self.layer.borderColor = color.CGColor;
        
        if (x.intValue == 0) {
            self.layer.borderWidth = 0.f;
        } else {
            self.layer.borderWidth = 0.5f;
        }
    }];
    
    [self addTarget:self action:@selector(jdisv_L2_seletAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)jdisv_L2_seletAction {
    self.jdisv_selected_L2 = @(!self.jdisv_selected_L2.boolValue);
}

- (NSNumber *)jdisv_selected_L2 {
    return objc_getAssociatedObject(self, @selector(jdisv_selected_L2));
}

- (void)jdisv_setSelected_L2:(NSNumber *)isSelected {
    objc_setAssociatedObject(self, @selector(jdisv_selected_L2), isSelected, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isSelected.boolValue == NO) {
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
        if (@available(iOS 8.2, *)) {
            self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
        } else {
            // Fallback on earlier versions
        }

    } else {
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateNormal];
        if (@available(iOS 8.2, *)) {
            self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
        } else {
            // Fallback on earlier versions
        }
    }
}

#pragma mark - L3
- (void)renderL3 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#60"];

        [self renderL3WithCornerRadius:r60];
//    }];
}

- (void)renderL3WithCornerRadius:(CGFloat)cornerRadius {
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateNormal];
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateNormal];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T11");
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    self.userInteractionEnabled = NO;
}

#pragma mark - L4
- (void)renderL4 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#60"];
        [self renderL4WithCornerRadius:r60];
//    }];
}

- (void)renderL4WithCornerRadius:(CGFloat)cornerRadius {
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C10-d") forState:UIControlStateNormal];
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11", UIFontWeightMedium);
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    self.userInteractionEnabled = NO;
}

#pragma mark - L5

- (void)renderL5Normal {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r61 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#61"];
        [self renderL5NormalWithCornerRadius:r61];
//    }];
}

- (void)renderL5NormalWithCornerRadius:(CGFloat)cornerRadius {
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C2") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    self.userInteractionEnabled = NO;
}

- (void)renderL5Editing {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r61 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#61"];
        [self renderL5EditingWithCornerRadius:r61];
//    }];
}

- (void)renderL5EditingWithCornerRadius:(CGFloat)cornerRadius {
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C2") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    [self setImage:[UIImage ka_uikit_imageWithName:@"ka_label_close"] forState:UIControlStateNormal];
    self.imageEdgeInsets = UIEdgeInsetsMake(10, self.bounds.size.width - 10 - 12, 10, 12);
    self.titleEdgeInsets = UIEdgeInsetsMake(10, - 10 - 12, 10, 6);
    [self jdisv_setTintColorPicker:JDISVColorPickerWithKey(@"#C5")];
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
}

#pragma mark - L6

- (void)renderL6Normal {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r61 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#61"];
        [self renderL6NormalWithCornerRadius:r61];
//    }];
}

- (void)renderL6NormalWithCornerRadius:(CGFloat)cornerRadius {
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    self.userInteractionEnabled = NO;
}

- (void)renderL6Editing {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r61 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#61"];
        [self renderL6EditingWithCornerRadius:r61];
//    }];
}

- (void)renderL6EditingWithCornerRadius:(CGFloat)cornerRadius {
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    [self setImage:[UIImage ka_uikit_imageWithName:@"ka_label_close"] forState:UIControlStateNormal];
    self.imageEdgeInsets = UIEdgeInsetsMake(10, self.bounds.size.width - 10 - 12, 10, 12);
    self.titleEdgeInsets = UIEdgeInsetsMake(10, - 10 - 12, 10, 6);
    [self jdisv_setTintColorPicker:JDISVColorPickerWithKey(@"#C5")];
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
}

#pragma mark - L7

- (void)renderL7 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r61 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#61"];
        [self renderL7WithCornerRadius:r61];
//    }];
}

- (void)renderL7WithCornerRadius:(CGFloat)cornerRadius {
    
    self.contentEdgeInsets = UIEdgeInsetsMake(0, -4, 0, -4);

    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C2") forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    
    self.adjustsImageWhenHighlighted = NO;
    // 设置圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    
    @weakify(self)
    [RACObserve(self,enabled) subscribeNext:^(NSNumber * _Nullable x) {
        @strongify(self)
        if (x.intValue == 0 || self.jdisv_selected_L7.boolValue == NO) {
            self.layer.borderWidth = 0.f;
        } else {
            self.layer.borderWidth = 0.5f;
        }
    }];
    [self addTarget:self action:@selector(jdisv_L7_seletAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)jdisv_L7_seletAction {
    self.jdisv_selected_L7 = @(!self.jdisv_selected_L7.boolValue);
}

- (NSNumber *)jdisv_selected_L7 {
    return objc_getAssociatedObject(self, @selector(jdisv_selected_L7));
}

- (void)jdisv_setSelected_L7:(NSNumber *)isSelected {
    objc_setAssociatedObject(self, @selector(jdisv_selected_L7), isSelected, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isSelected.boolValue == NO) {
        [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C2") forState:UIControlStateNormal];
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateHighlighted];
        self.layer.borderWidth = 0.f;
        if (@available(iOS 8.2, *)) {
            self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
        } else {
            // Fallback on earlier versions
        }

    } else {
        [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateNormal];
        [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateNormal];
        // 设置边框
        JDISVColorPicker colorPicker = [[JDISVThemeColor sharedInstance] pickerWithKey:@"#C9"];
        UIColor *color = colorPicker(JDISVThemeManager.sharedManager.themeVersion);
        self.layer.borderColor = color.CGColor;
        self.layer.borderWidth = 0.5f;
        if (@available(iOS 8.2, *)) {
            self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
        } else {
            // Fallback on earlier versions
        }
    }
}

#pragma mark - L8

- (void)renderL8 {
    @weakify(self)
    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        CGFloat height = self.bounds.size.height;
        [self renderL8WithCornerRadius:height/2.0];
    }];
}

- (void)renderL8WithCornerRadius:(CGFloat)cornerRadius {
    // 设置字体
    [self setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];//下架/无货都是白色
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C8", 0.7) forState:UIControlStateNormal];
    self.userInteractionEnabled = NO;
    // 设置圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
}

@end
