//
//  KSAPriceOffView.m
//  JDISVKAUIKitModule
//
//  Created by cdwutao3 on 2023/12/28.
//

#import "KSAPriceOffView.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import "KAUIKitTool.h"

@interface KSAPriceOffView()

@property(strong,nonatomic) UILabel* priceLabel;

@end

@implementation KSAPriceOffView
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){

        self.priceLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        [self addSubview:self.priceLabel];
        self.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"#FF4A17"];
        self.layer.cornerRadius = 9;
        self.layer.masksToBounds = YES;
        
        [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(self);
        }];
        self.priceLabel.font = [UIFont boldSystemFontOfSize:12];
        self.priceLabel.adjustsFontSizeToFitWidth = YES;
        self.priceLabel.textColor = UIColor.whiteColor;
        self.priceLabel.textAlignment = NSTextAlignmentCenter;
    }
    return self;
}
-(void)priceOff:(NSString*)priceOff{
    priceOff = [priceOff stringByReplacingOccurrencesOfString:@"%" withString:@""];
    if(priceOff.length == 0){
        self.hidden = YES;
    }else{
        self.hidden = NO;
        NSString* show;
        if([[NSString getKAUseLang] isEqual:@"en"]){
            show = [NSString stringWithFormat:@"%@%% Off",priceOff];
        }else{
            show = [NSString stringWithFormat:@"\u202A%@%% خصم",priceOff];
        }
        self.priceLabel.text = show;
    }
}
@end
