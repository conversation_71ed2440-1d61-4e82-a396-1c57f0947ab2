//
//  UIButton+KAUIKit.m
//  JDISVKAUIKitModule
//
//  Created by 张润峰 on 2021/8/24.
//

#import "UIButton+KAButton.h"
#import "UIButton+KAButton_Private.h"
#import <objc/runtime.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import "KAUIKitTool.h"

static NSUInteger kB9ButtonFloatViewTag = 10024;

@implementation UIButton (KAButton)

- (void)configKAButtonTitle:(NSString *)title {
    [self setTitle:title forState:UIControlStateNormal];
    [self setTitle:title forState:UIControlStateHighlighted];
    [self setTitle:title forState:UIControlStateDisabled];
}

- (void)configCornerRadius:(CGFloat)cornerRadius {
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
}

- (void)renderB1 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
        CGFloat r50 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R50"];
        [self renderB1WithCornerRadius:r50];
//    }];
}

- (void)renderB1WithCornerRadius:(CGFloat)cornerRadius {
    
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    
    [self jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C9-1") picker2:JDISVColorPickerWithKey(@"#C9-2") forState:UIControlStateNormal];
    [self jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKeyAndBlendColor(@"#C9-1", @"#000000", 0.1) picker2:JDISVColorPickerWithKeyAndBlendColor(@"#C9-2", @"#000000", 0.1) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T6 16pt Semibold
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T6", UIFontWeightMedium);
    
    self.adjustsImageWhenHighlighted = NO;
    [self configCornerRadius:cornerRadius];
}

- (void)renderB2 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
        CGFloat r50 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R50"];
        [self renderB2WithCornerRadius:r50];
//    }];
}

- (void)renderB2WithCornerRadius:(CGFloat)cornerRadius {
    
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C9-1") picker2:JDISVColorPickerWithKey(@"#C9-2") forState:UIControlStateNormal];
    [self jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKeyAndBlendColor(@"#C9-1", @"#000000", 0.1) picker2:JDISVColorPickerWithKeyAndBlendColor(@"#C9-2", @"#000000", 0.1) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt Semibold
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
    
    self.adjustsImageWhenHighlighted = NO;
    [self configCornerRadius:cornerRadius];
}

- (void)renderB3 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
        CGFloat r51 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R51"];
        [self renderB3WithCornerRadius:r51];
//    }];
}

- (void)renderB3WithCornerRadius:(CGFloat)cornerRadius {
    
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1-b") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1-b") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1-b") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C9-1") picker2:JDISVColorPickerWithKey(@"#C9-2") forState:UIControlStateNormal];
    [self jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKeyAndBlendColor(@"#C9-1", @"#000000", 0.1) picker2:JDISVColorPickerWithKeyAndBlendColor(@"#C9-2", @"#000000", 0.1) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt Medium
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
    
    self.adjustsImageWhenHighlighted = NO;
    [self configCornerRadius:cornerRadius];
}

- (void)renderB4 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r51 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R51"];
        [self renderB4WithCornerRadius:r51];
//    }];
}

- (void)renderB4WithCornerRadius:(CGFloat)cornerRadius {
    
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C7") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C8", 0.1) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    
    self.adjustsImageWhenHighlighted = NO;
    
    [self configCornerRadius:cornerRadius];
    
    @weakify(self)
    [RACObserve(self,enabled) subscribeNext:^(NSNumber * _Nullable x) {
        @strongify(self)
        // 设置边框
        JDISVColorPicker colorPicker = [[JDISVThemeColor sharedInstance] pickerWithKey:@"#C5"];
        UIColor *color = colorPicker(JDISVThemeManager.sharedManager.themeVersion);
        self.layer.borderColor = color.CGColor;
        
        if (x.intValue == 0) {
            self.layer.borderWidth = 0.f;
        } else {
            self.layer.borderWidth = 0.5f;
        }
    }];
}

- (void)renderB5 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r51 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R51"];
        [self renderB5WithCornerRadius:r51];
//    }];
}

- (void)renderB5WithCornerRadius:(CGFloat)cornerRadius {
    
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    
    self.adjustsImageWhenHighlighted = NO;
    [self configCornerRadius:cornerRadius];
    
    @weakify(self)
    [RACObserve(self,enabled) subscribeNext:^(NSNumber * _Nullable x) {
        @strongify(self)
        // 设置边框
        JDISVColorPicker colorPicker = [[JDISVThemeColor sharedInstance] pickerWithKey:@"#C9"];
        UIColor *color = colorPicker(JDISVThemeManager.sharedManager.themeVersion);
        self.layer.borderColor = color.CGColor;
        
        if (x.intValue == 0) {
            self.layer.borderWidth = 0.f;
        } else {
            self.layer.borderWidth = 0.5f;
        }
    }];
}

- (void)renderB6 {
//    @weakify(self)
//    [RACObserve(self,frame) subscribeNext:^(id  _Nullable x) {
//        @strongify(self)
//        CGFloat height = self.bounds.size.height;
    CGFloat r52 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R52"];
        [self renderB6WithCornerRadius:r52];
//    }];
}

- (void)renderB6WithCornerRadius:(CGFloat)cornerRadius {
    
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.1) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    
    self.adjustsImageWhenHighlighted = NO;
    [self configCornerRadius:cornerRadius];
    
    @weakify(self)
    [RACObserve(self,enabled) subscribeNext:^(NSNumber * _Nullable x) {
        @strongify(self)
        // 设置边框
        JDISVColorPicker colorPicker = [[JDISVThemeColor sharedInstance] pickerWithKey:@"#C9"];
        UIColor *color = colorPicker(JDISVThemeManager.sharedManager.themeVersion);
        self.layer.borderColor = color.CGColor;
        
        if (x.intValue == 0) {
            self.layer.borderWidth = 0.f;
        } else {
            self.layer.borderWidth = 0.5f;
        }
    }];
}

- (void)renderB7 {
    self.jdisv_selected_B7 = NO;
    self.jdisv_autoChangeUI_B7 = NO;
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"];
    UIImage *selected = [UIImage ka_iconWithName:JDIF_ICON_RADIO_PROHIBIT imageSize:CGSizeMake(20, 20) color:color];
    [self setBackgroundImage:selected forState:UIControlStateDisabled];
}

- (void)jdisv_B7_buttonSeletAction {
    self.jdisv_selected_B7 = !self.jdisv_selected_B7;
}

- (BOOL)jdisv_autoChangeUI_B7 {
    NSNumber *num = objc_getAssociatedObject(self, @selector(jdisv_autoChangeUI_B7));
    return num.boolValue;
}

- (void)jdisv_setAutoChangeUI_B7:(BOOL)isAuto {
    objc_setAssociatedObject(self, @selector(jdisv_autoChangeUI_B7), @(isAuto), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isAuto) {
        [self addTarget:self action:@selector(jdisv_B7_buttonSeletAction) forControlEvents:UIControlEventTouchUpInside];
    } else {
        [self removeTarget:self action:@selector(jdisv_B7_buttonSeletAction) forControlEvents:UIControlEventTouchUpInside];
    }
}

- (BOOL)jdisv_selected_B7 {
    NSNumber *num = objc_getAssociatedObject(self, @selector(jdisv_selected_B7));
    return num.boolValue;
}

- (void)jdisv_setSelected_B7:(BOOL)isSelected {
    objc_setAssociatedObject(self, @selector(jdisv_selected_B7), @(isSelected), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isSelected) {
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        UIImage *selected = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:CGSizeMake(20, 20) color:color];
        [self setBackgroundImage:selected forState:UIControlStateNormal];
    } else {
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *selected = [UIImage ka_iconWithName:JDIF_ICON_RADIO imageSize:CGSizeMake(20, 20) color:color];
        [self setBackgroundImage:selected forState:UIControlStateNormal];
    }
}

- (void)renderB8 {
    self.jdisv_selected_B8 = NO;
    self.jdisv_autoChangeUI_B8 = NO;
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    UIImage *selected = [UIImage ka_iconWithName:JDIF_ICON_RADIO_PROHIBIT imageSize:CGSizeMake(20, 20) color:color];
    [self setBackgroundImage:selected forState:UIControlStateDisabled];
}

- (void)jdisv_B8_buttonSeletAction {
    self.jdisv_selected_B8 = !self.jdisv_selected_B8;
}

- (BOOL)jdisv_autoChangeUI_B8 {
    NSNumber *num = objc_getAssociatedObject(self, @selector(jdisv_autoChangeUI_B8));
    return num.boolValue;
}

- (void)jdisv_setAutoChangeUI_B8:(BOOL)isAuto {
    objc_setAssociatedObject(self, @selector(jdisv_autoChangeUI_B8), @(isAuto), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isAuto) {
        [self addTarget:self action:@selector(jdisv_B8_buttonSeletAction) forControlEvents:UIControlEventTouchUpInside];
    } else {
        [self removeTarget:self action:@selector(jdisv_B8_buttonSeletAction) forControlEvents:UIControlEventTouchUpInside];
    }
}

- (BOOL)jdisv_selected_B8 {
    NSNumber *num = objc_getAssociatedObject(self, @selector(jdisv_selected_B8));
    return num.boolValue;
}

- (void)jdisv_setSelected_B8:(BOOL)isSelected {
    objc_setAssociatedObject(self, @selector(jdisv_selected_B8), @(isSelected), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isSelected) {
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        UIImage *selected = [UIImage ka_iconWithName:JDIF_ICON_PROCESS imageSize:CGSizeMake(20, 20) color:color];
        [self setBackgroundImage:selected forState:UIControlStateNormal];
    } else {
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *selected = [UIImage ka_iconWithName:JDIF_ICON_RADIO imageSize:CGSizeMake(20, 20) color:color];
        [self setBackgroundImage:selected forState:UIControlStateNormal];
    }
}

- (void)renderB9 {
    self.jdisv_selected_B9 = NO;
    self.jdisv_autoChangeUI_B9 = NO;
    self.adjustsImageWhenHighlighted = NO;
    
    UIView *view = [UIView new];
    view.tag = kB9ButtonFloatViewTag;

//    view.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C7");
//    和UED刘湘沟通,颜色写死白色
    view.backgroundColor = UIColor.whiteColor;
    view.layer.cornerRadius = 11;
    view.layer.shadowOpacity = 0.1;
    view.layer.shadowColor = [UIColor blackColor].CGColor;
    view.layer.shadowOffset = CGSizeMake(0, 2);
    view.layer.shadowRadius = 6;
    view.userInteractionEnabled = NO;
    [self addSubview:view];
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(22);
        make.centerY.equalTo(self);
        make.centerX.equalTo(self).offset(-12);
    }];
}

- (void)renderB18WithCornerRadius:(CGFloat)cornerRadius {
    // 设置字体颜色
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
   
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.07) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    
    self.adjustsImageWhenHighlighted = NO;
    
    [self configCornerRadius:cornerRadius];
}

- (CGSize)renderASCouponTag:(NSString*)title{
    // 设置字体颜色
    [self setTitle:title forState:UIControlStateNormal];
    
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C10-d") forState:UIControlStateNormal];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C10-d") forState:UIControlStateHighlighted];
    [self jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C1") forState:UIControlStateDisabled];
    
    // 设置背景颜色
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C10-d", 0.07) forState:UIControlStateNormal];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C10-d", 0.07) forState:UIControlStateHighlighted];
    [self jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C4") forState:UIControlStateDisabled];
    UIFont* font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
    // 设置字体大小 T7 14pt
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T11");
    
    CGSize size =  [title jdcd_getStringSize:font constraintsSize:CGSizeMake(600, 13)];
    
    CGSize resultSize = CGSizeMake(size.width+8, 13);
    
    self.adjustsImageWhenHighlighted = NO;
    [self configCornerRadius:4];
    self.frame = CGRectMake(0, 0, resultSize.width, resultSize.height);
    return resultSize;
}

- (void)jdisv_B9_buttonSeletAction {
    self.jdisv_selected_B9 = !self.jdisv_selected_B9;
}

- (BOOL)jdisv_autoChangeUI_B9 {
    NSNumber *num = objc_getAssociatedObject(self, @selector(jdisv_autoChangeUI_B9));
    return num.boolValue;
}

- (void)jdisv_setAutoChangeUI_B9:(BOOL)isAuto {
    objc_setAssociatedObject(self, @selector(jdisv_autoChangeUI_B9), @(isAuto), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    if (isAuto) {
        [self addTarget:self action:@selector(jdisv_B9_buttonSeletAction) forControlEvents:UIControlEventTouchUpInside];
    } else {
        [self removeTarget:self action:@selector(jdisv_B9_buttonSeletAction) forControlEvents:UIControlEventTouchUpInside];
    }
}

- (BOOL)jdisv_selected_B9 {
    NSNumber *num = objc_getAssociatedObject(self, @selector(jdisv_selected_B9));
    return num.boolValue;
}

- (void)jdisv_setSelected_B9:(BOOL)isSelected {
    objc_setAssociatedObject(self, @selector(jdisv_selected_B9), @(isSelected), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    UIView *view = [self viewWithTag:kB9ButtonFloatViewTag];
    if (isSelected) {
        UIImage *image = [[UIImage ka_uikit_imageWithName:@"B9_selected"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [self setImage:image forState:UIControlStateNormal];
        self.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        [view mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self).offset(12);
        }];
    } else {
        UIImage *image = [[UIImage ka_uikit_imageWithName:@"B9_not_selected"] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [self setImage:image forState:UIControlStateNormal];
        self.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        [view mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self).offset(-12);
        }];
    }
    [UIView animateWithDuration:0.25 animations:^{
        [self layoutIfNeeded];
    }];
}

- (UIImage *)imageFromColor:(UIColor *)color {
    CGRect rect = CGRectMake(0, 0, 1, 1);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

@end
