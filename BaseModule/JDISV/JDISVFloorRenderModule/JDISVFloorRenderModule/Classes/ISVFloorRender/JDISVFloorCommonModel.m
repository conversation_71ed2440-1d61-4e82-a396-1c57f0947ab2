//
//  JDISVFloorCommonModel.m
//  JDISVFloorRenderModule
//
//  Created by 罗静 on 2022/2/11.
//

#import "JDISVFloorCommonModel.h"
#import "JDISVSafeDictionary.h"
#import <pthread/pthread.h>

@interface JDISVFloorCommonModel ()

@property (nonatomic, strong, readwrite) NSMutableDictionary *commonData;

@property (nonatomic, strong, readwrite) NSDictionary *allFloorOriginData;

@property (nonatomic, strong) NSMutableArray *didChangePool;

@end

@implementation JDISVFloorCommonModel

- (void)initAllFloorOriginData:(NSDictionary *)allFloorOriginData {
    if (allFloorOriginData) {
        self.allFloorOriginData = [allFloorOriginData copy];
    }
}


- (void)commonDataChange {
    for (void(^block)(NSDictionary *) in self.didChangePool) {
        if (block) {
            block(self.commonData);
        }
    }
}

- (void)commonDataDidChange:(void (^)(NSDictionary * _Nonnull))didChange {
    if (didChange) {
        [self.didChangePool addObject:didChange];
        didChange(self.commonData);
    }
}

- (NSMutableArray *)didChangePool {
    if (!_didChangePool) {
        _didChangePool = [NSMutableArray array];
    }
    return _didChangePool;
}


- (NSDictionary *)allFloorOriginData {
    if (!_allFloorOriginData) {
        _allFloorOriginData = [NSDictionary dictionary];
    }
    return _allFloorOriginData;
}

- (NSMutableDictionary *)commonData {
    if (!_commonData) {
        _commonData = [(NSMutableDictionary *)[JDISVSafeDictionary alloc] init];
        //_commonData = (NSMutableDictionary *)[NSMutableDictionary dictionary] ;
    }
    return _commonData;
}

- (void)destroy {
    self.didChangePool = nil;
}

@end
