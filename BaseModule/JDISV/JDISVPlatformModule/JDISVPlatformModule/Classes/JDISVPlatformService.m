//
//  JDISVPlatformService.m
//  JDISVPlatformModule
//
//  Created by zhujiang on 2021/3/1.
//

#import "JDISVPlatformService.h"
#import "JDISVAppDelegate.h"
#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>
#import <JDISVDiskCacheModule/JDCDKVStore.h>
#import <JDISVDiskCacheModule/JDCDKeyChainStore.h>
#import <JDISVThemeModule/JDISVThemeManager.h>
#import <JDISVKAUIKitModule/KAEmptyView.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDBProtocolHandlerModule/JDBProtocolHandlerModule-umbrella.h>
#import <JDISVYYModelModule/YYModel.h>
#import <JDISVKAUIKitModule/KATabbarController.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVDeviceService.h"
#import "JDISVIPAddressTool.h"
#import "JDISVResourceManager.h"
#import "JDISVNetWorkTipView.h"

@import JDTCommonToolModule;

NSString* ISVLoginedNotification = @"JDCDISVLoginedNotification";
NSString* ISVLogoutNotification = @"JDCDISVLogoutNotification";
NSString* ISVAddressChangedNotification = @"JDCDISVAddressChangedNotification";

//关注商品成功
NSString* ISVFocusProductNotification = @"ISVFocusProductNotification";

//取消关注商品成功
NSString* ISVCancelFocusProductNotification = @"ISVCancelFocusProductNotification";

//关注店铺成功
NSString* ISVFocusShopNotification = @"ISVFocusShopNotification";

//取消关注店铺成功
NSString* ISVCancelFocusShopNotification = @"ISVCancelFocusShopNotification";

NSString* ISVLangChangedNotification = @"ISVLangChangedNotification";

NSString* G_ISVDefaultAddressKey = @"G_ISVDefaultAddressKey";


@interface JDISVPlatformService()<JDCDErrorViewDelegate>

@property (nonatomic, strong) JDISVNetWorkTipView *networkTipView;
@property (nonatomic, strong) JDTAddressItemModel *isvDefaultAddress;
@property (nonatomic, copy) NSString *pin;
@property (nonatomic, copy) NSString *a2;
@property (nonatomic, assign) BOOL RTL;
// KA商城使用
@property (nonatomic, copy) NSString *guid;
@property (nonatomic) BOOL isLogin;
@property (nonatomic, strong) NSMutableDictionary *networkEnviParamDic;

@property (nonatomic,assign) LogDebugInfo logInfoType;
@end

@implementation JDISVPlatformService

static Class ISVPlatformServiceClass = NULL;

+(void)load{
//    [JDBFingerPrintModule localCUID];
}

+ (JDISVPlatformService *)sharedService{
    static JDISVPlatformService *_sharedService;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (ISVPlatformServiceClass != NULL) {
            _sharedService = [[ISVPlatformServiceClass alloc] init];
        }else{
            _sharedService = [[self alloc] init];
        }
        
        [_sharedService initService];
        
        NSString* schema = [_sharedService appURLScheme];
        if(schema.length){
            [JDProtocolHandlerConfig registerProtocolSchemeClassName:@"ISVOpenappProtocolScheme"
                                                  withProtocolScheme:schema];
        }
        _sharedService.logInfoType = logTime|logInfo;
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
            ISVLogID(@"platform完成",@"platform:%@ \nuuid:%@ \nschema:%@",
                  NSStringFromClass(_sharedService.class) ,
                   [_sharedService getUUID],
                   schema);
            
        });
    });
    
    return _sharedService;
}

+ (void)setPlatformServiceClass:(Class)cls{
    if (cls) {
        if (![cls isSubclassOfClass:JDISVPlatformService.class]) {
            @throw [NSException exceptionWithName:NSInternalInconsistencyException reason:[NSString stringWithFormat:@"%@ is not Subclass of JDISVPlatformService", cls] userInfo:nil];
        }
        ISVPlatformServiceClass = cls;
    }
}
-(instancetype)init{
    self = [super init];
    if(self){
        NSString* addrStr = [[NSUserDefaults standardUserDefaults] objectForKey:G_ISVDefaultAddressKey];
        JDTAddressItemModel *addr = [JDTAddressItemModel yy_modelWithJSON:addrStr];
        if (addr) {
            self.isvDefaultAddress = addr;
        }
    }
    return self;
}
- (void)initService
{
    // Cookie设置
    [self configDefaultNetworkingCookie];
    
    // 配置Color网络参数
    [self configColorNetworkingParam];
    
    // 配置网络监听
    [[JDCDReachableService shareService] startMonitoring];
    
    // 配置主题
    [self configThemeList];
}

#pragma mark - app config

- (NSString *)appDelegateClassName{
   
    return @"JDISVAppDelegate";
}

- (NSArray *)appDelegateModules{
    return @[];
}

- (NSString *)appURLScheme{
    return nil;
}

- (NSString*)imUrl{
    NSString* url  = [self prefixUrlDomain:@"jtkfy"];
    NSString* allUrl = [NSString stringWithFormat:@"%@%@/chat/index.html?hideTitle=1&eid=155571&userApp=jsl.155571.customer",[self urlSchema],url];
    return allUrl;
}

- (NSArray*)webViewCookieDomain{
//    POC-D环境
    return @[@"m.crxn.cn",
             @"warden.crxn.cn"];
}
- (NSBundle *)defaultResourceBundle {
    NSURL *url = [[NSBundle mainBundle] URLForResource:@"JDISVPlatformModule" withExtension:@"bundle"];
    NSBundle *bundle = [NSBundle bundleWithURL:url];
    return bundle;
}

- (NSBundle *)defaultImageBundle {
    NSURL *url = [[NSBundle mainBundle] URLForResource:@"JDISVPlatformModule" withExtension:@"bundle"];
    NSBundle *bundle = [NSBundle bundleWithURL:url];
    return bundle;
}

- (NSString *)themeListFileName {
    return @"JDISVThemeList.theme";
}

- (NSString *)wxSharePath {
    return @"package_mall/pages/jd_mall/link";
}

- (NSBundle *)themeListBundle {
    NSURL *url = [[NSBundle mainBundle] URLForResource:@"JDISVPlatformModule" withExtension:@"bundle"];
    NSBundle *bundle = [NSBundle bundleWithURL:url];
    return bundle;
}

#pragma mark - 当前激活视图
- (UIViewController *)getTopViewController{
    UIViewController* result = [self topViewController];
    if(!result){
        UITabBarController *tabController = (UITabBarController *)[UIApplication sharedApplication].keyWindow.rootViewController;
        UINavigationController* nav;
        if([tabController isKindOfClass:UITabBarController.class]){
            nav = tabController.selectedViewController;
        }else{
            nav = tabController;
        }
        if([nav isKindOfClass:UINavigationController.class]){
            result =  nav.visibleViewController;
        }
    }
    return result;
}

- (UIViewController *)topViewController {
    UIViewController *resultVC;
    resultVC = [self _topViewController:[[UIApplication sharedApplication].keyWindow rootViewController]];
    while (resultVC.presentedViewController) {
        resultVC = [self _topViewController:resultVC.presentedViewController];
    }
    return resultVC;
}

- (UIViewController *)_topViewController:(UIViewController *)vc {
    if ([vc isKindOfClass:[UINavigationController class]]) {
        return [self _topViewController:[(UINavigationController *)vc topViewController]];
    } else if ([vc isKindOfClass:[UITabBarController class]]) {
        return [self _topViewController:[(UITabBarController *)vc selectedViewController]];
    } else {
        return vc;
    }
    return nil;
}

#pragma mark - <项目环境>
/// 获取当前项目环境
- (JDISVEnvironmentType)projectEnvironmentType{
    return JDISVEnvironmentTypeDefault;
}

#pragma mark - UI接口实现
- (void )setDefaultButtonStyleWith:(UIButton * __nonnull)button buttonType:(JDISVButtonType)type {
    NSString *title = button.titleLabel.text;
    switch (type) {
        case JDISVButtonLoginType:{
            [button BQSetupUIWithTitle:title fontSize:14];
        }
            
            break;
        case JDISVButtonRedBorderRedTitleWhiteBgUIType:{
            [button BQRedBorderRedTitleWhiteBgUIWithTitle:title fontSize:14];
        }
            
            break;
        case JDISVButtonGrayBorderBlackTitleType:{
            [button BQGrayBorderBlackTitleWithTitle:title fontSize:14];
        }
            
            break;
            
        case JDISVButtonOrangeGridentType:{
            [button BQSetOrangeGridentUI:title fontSize:14];
        }
            
            break;
            
        default:{
            [button BQSetupUI];
        }
            break;
    }
}

- (UIButton *)getDefaultButtonWithTitle:(NSString *)title frame:(CGRect)frame buttonType:(JDISVButtonType)type{
    UIButton *defaultButton = [UIButton buttonWithType:UIButtonTypeCustom];
    defaultButton.frame = frame;
    switch (type) {
        case JDISVButtonLoginType:{
            [defaultButton BQSetupUIWithTitle:title fontSize:14];
        }
            
            break;
        case JDISVButtonRedBorderRedTitleWhiteBgUIType:{
            [defaultButton BQRedBorderRedTitleWhiteBgUIWithTitle:title fontSize:14];
        }
            
            break;
        case JDISVButtonGrayBorderBlackTitleType:{
            [defaultButton BQGrayBorderBlackTitleWithTitle:title fontSize:14];
        }
            
            break;
            
        case JDISVButtonOrangeGridentType:{
            [defaultButton BQSetOrangeGridentUI:title fontSize:14];
        }
            
            break;
            
        default:{
            [defaultButton BQSetupUI];
        }
            break;
    }
    return defaultButton;
}

- (UIButton *)getProductDetailRedButton {
    UIButton *defaultButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultButton BQSetupUI];
    UIColor *color = [UIColor jdcd_colorWithHexColorString:@"C1C1C1"];
    UIImage *image = [UIImage jdcd_getGradientImageFromColors:@[color, color] gradientType:(GradientFromLeftToRight) imgSize:CGSizeMake(4, 4)];
    [defaultButton setBackgroundImage:image forState:(UIControlStateDisabled)];
    return defaultButton;
}

- (void)showDefaultAlertWithTitle:(NSString *)title negativeAction:(JDISVAlertAction)negativeAction positiveAction:(JDISVAlertAction)positiveAction cancelButtonTitle:(NSString *)cancelButtonTitle doneButtonTitle:(NSString *)doneButtonTitle touchDoneCloseAlert:(BOOL)touchDoneCloseAlert{
    [self showDefaultAlertWithTitle:title
                           subTitle:@""
                     negativeAction:negativeAction
                     positiveAction:positiveAction
                  cancelButtonTitle:cancelButtonTitle
                    doneButtonTitle:doneButtonTitle
                touchDoneCloseAlert:touchDoneCloseAlert];
}



- (void)showDefaultAlertWithTitle:(NSString *)title subTitle:(NSString *)subTitle negativeAction:(JDISVAlertAction)negativeAction positiveAction:(JDISVAlertAction)positiveAction cancelButtonTitle:(NSString *)cancelButtonTitle doneButtonTitle:(NSString *)doneButtonTitle touchDoneCloseAlert:(BOOL)touchDoneCloseAlert{
    if(subTitle && subTitle.length > 0){
        [KAAlert alert].config
        .renderW4(title, subTitle)
        .addLineAction(cancelButtonTitle, ^{
            if(negativeAction){
                negativeAction();
            }
        })
        .addFillAction(doneButtonTitle, ^{
            if(positiveAction){
                positiveAction();
            }
        })
        .alertShow();
    }else{
        [KAAlert alert].config
        .renderW3(title)
        .addLineAction(cancelButtonTitle, ^{
            if(negativeAction){
                negativeAction();
            }
        })
        .addFillAction(doneButtonTitle, ^{
            if(positiveAction){
                positiveAction();
            }
        })
        .alertShow();
    }
}

- (void)showAlertWithAlignmentType:(AlignmentType)alignmentType title:(NSString *)title subTitle:(NSString *)subTitle negativeAction:(JDISVAlertAction)negativeAction positiveAction:(JDISVAlertAction)positiveAction cancelButtonTitle:(NSString *)cancelButtonTitle doneButtonTitle:(NSString *)doneButtonTitle touchDoneCloseAlert:(BOOL)touchDoneCloseAlert{
    
}


- (void)showDefaultSheetWithTitle:(NSString *)title subTitle:(NSString *)subTitle buttonTitles:(NSArray<NSString*>*) titles blocks:(NSArray<JDISVAlertAction>*)blocks cancelIndex:(int)cancelIndex{
    
    
    UIAlertController* sheetController = [UIAlertController alertControllerWithTitle:title message:subTitle preferredStyle:UIAlertControllerStyleActionSheet];
    int index = 0;
    for (NSString* title in titles){
        JDISVAlertAction block;
        if(index < blocks.count){
            block = blocks[index];
        }else{
            block = nil;
        }
        UIAlertActionStyle style = index == cancelIndex?UIAlertActionStyleCancel:UIAlertActionStyleDefault;
        UIAlertAction* action = [UIAlertAction actionWithTitle:title style:style handler:^(UIAlertAction * _Nonnull action) {
            if(block){
                block();
            }
        }];
        [sheetController addAction:action];
        index++;
    }
    UIViewController* topController = [UIApplication sharedApplication].delegate.window.rootViewController;
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    [topController presentViewController:sheetController animated:YES completion:nil];
}

- (void)showDefaultToastWithIconType:(ToastType)type message:(NSString *)message{
    [self showDefaultToastWithIconType:type showTime:0 message:message];
}

- (void)showDefaultToastWithIconType:(ToastType)type showTime:(CGFloat)interval message:(NSString *)message{
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    
    switch (type) {
        case ISVInstantTypeOnlyMessage:
        {
            [JDCDProgressHUD showMessage:message inView:window];
        }
            break;
        case ISVInstantTypeFinish:
        {
            [JDCDProgressHUD showSuccessMessage:message inView:window];
        }
            break;
        case ISVInstantTypeFail:
        {
            [JDCDProgressHUD showErrorMessage:message inView:window];
        }
            break;
        default:
            break;
    }
}

- (void)showLoadingInView:(UIView *)view{
    [JDCDProgressHUD showLoadingInView:view];
}

- (void)showLoadingInView:(UIView *)view block:(bool)block{
    [JDCDProgressHUD showLoadingInView:view block:block];
}

- (BOOL)dismissInView:(UIView *)view{
    return [JDCDProgressHUD dismissInView:view];
}

- (UIView *)getLoadErrorViewWithMessage:(NSString *)message errorImage:(UIImage *)errorImage buttonText:(NSString *)buttonString reloadButtonBlock:(JDISVErrorViewReloadBlock)reloadButtonBlock{
    
    KAEmptyView* view;
    if(reloadButtonBlock){
        view = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNormal];
        view.action = ^(UIButton * _Nonnull sender) {
            reloadButtonBlock(nil);
        };
        view.coverImage = errorImage;
        view.decrible = message;
        view.actionTitle = buttonString;
    }else{
        view = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNotAction];
        view.coverImage = errorImage;
        view.decrible = message;
    }
    view.verticalAlignment = KAEmptyViewVerticalAlignmentWindowCenter;
    return view;
}

- (UIView *)getNetWorkErrorViewWithMessage:(NSString *)message errorImage:(UIImage *)errorImage{
    KAEmptyView* view;
    view = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNotAction];
    view.coverImage = errorImage;
    view.decrible = message;
    
    view.verticalAlignment = KAEmptyViewVerticalAlignmentWindowCenter;
    return view;
}

- (UIView *)getEmptyViewWithMessage:(NSString *)message errorImage:(UIImage *)errorImage{
    KAEmptyView* view;
    view = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNotAction];
    view.coverImage = errorImage;
    view.decrible = message;
    
    view.verticalAlignment = KAEmptyViewVerticalAlignmentWindowCenter;
    return view;
}

- (MJRefreshHeader *)getMJRefreshHeader:(void (^)(void))block{
    MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingBlock:block];
    
    header.automaticallyChangeAlpha = YES;
    header.lastUpdatedTimeLabel.hidden = YES;
     [header setTitle:@"下拉刷新" forState:MJRefreshStateIdle];
     [header setTitle:@"松开立即刷新" forState:MJRefreshStatePulling];
     [header setTitle:@"正在加载" forState:MJRefreshStateRefreshing];
    return header;
}

- (MJRefreshHeader *)getMJRefreshHeaderWithType:(JDISVRefreshThemeType)themeType refreshingBlock:(void (^)(void))block {
    return [self getMJRefreshHeader:block];
}

- (MJRefreshFooter *)getMJRefreshBackFooter:(void (^)(void))block{
    JDCDRefreshBackFooterView *footer = [JDCDRefreshBackFooterView footerWithRefreshingBlock:block];
    [footer setTitle:@"上拉加载" forState:MJRefreshStateIdle];
    [footer setTitle:@"加载中" forState:MJRefreshStateRefreshing];
    [footer setTitle:@"没有更多了" forState:MJRefreshStateNoMoreData];
    return footer;
}

- (MJRefreshFooter *)getMJRefreshAutoFooter:(void (^)(void))block{
    JDCDRefreshAutoFooterView *footer = [JDCDRefreshAutoFooterView footerWithRefreshingBlock:block];
    [footer setTitle:@"上拉加载" forState:MJRefreshStateIdle];
    [footer setTitle:@"加载中" forState:MJRefreshStateRefreshing];
    [footer setTitle:@"没有更多了" forState:MJRefreshStateNoMoreData];
    return footer;
}


- (void)resetMJRefreshFooterFailureState:(MJRefreshFooter *)footer {
    JDCDRefreshBackFooterView *footerView = footer;
    if ([footerView respondsToSelector:@selector(resetFailureState)]) {
        [footerView resetFailureState];
    }
    
}


- (void)setMJRefreshFooterFailureStatue:(MJRefreshFooter *)footer {
    JDCDRefreshBackFooterView *footerView = footer;
    if ([footerView respondsToSelector:@selector(setFailureStatue)]) {
        [footerView setFailureStatue];
    }
}



- (UIImage *)getDefaultPlaceholderImage:(JDISVWebImagePlaceHolderType)imageSizeType{
    //todo
    NSURL *url = [[NSBundle mainBundle] URLForResource:@"JDISVPlatformModule" withExtension:@"bundle"];
        NSBundle *bundle = [NSBundle bundleWithURL:url];
    switch (imageSizeType) {
        case JDISVWebImagePlaceHolderTypeLarge:
        {
            UIImage *defaultImage = [UIImage imageNamed:@"jdisv_image_defaultPlaceholderImage" inBundle:bundle compatibleWithTraitCollection:nil];
            return defaultImage;
        }
            break;
        case JDISVWebImagePlaceHolderTypeSmall:
        {
            UIImage *defaultImage = [UIImage imageNamed:@"jdisv_image_defaultPlaceholderImage" inBundle:bundle compatibleWithTraitCollection:nil];
            return defaultImage;
        }
            break;
        case JDISVWebImagePlaceHolderTypeGray:
        {
            UIImage *defaultImage = [UIImage imageNamed:@"jdisv_image_defaultPlaceholderImage" inBundle:bundle compatibleWithTraitCollection:nil];
            return defaultImage;
        }
            break;
    }
    
}

#pragma mark - 功能接口实现
- (void)switchLanguageEnvironment:(NSString *)lang{
    KATabbarController* tabBarController = (KATabbarController*)[JDISVAppDelegate sharedAppDelegate].tabBarController;

    NSMutableArray *controllers = [NSMutableArray array];
    controllers = tabBarController.viewControllers;
    
    tabBarController = [[KATabbarController alloc] init];
    [NSString KAUseLang:lang];
    NSNumber* RTL = [[NSUserDefaults standardUserDefaults] objectForKey:@"JDISV_POC_RightToLeft"];
    if(RTL.boolValue || [lang isEqualToString:@"ar"]){
        [[UIView appearance] setSemanticContentAttribute:UISemanticContentAttributeForceRightToLeft];
        [PlatformService setRTL:YES];
//            [NSString KAUseLang:@"ar"];
        NSArray* revers = [[controllers reverseObjectEnumerator] allObjects];
        tabBarController.viewControllers = revers;
        [[UITabBar appearance] setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];
        tabBarController.selectedIndex = 3;
        [KANavigationBarManager sharedManager].isRTL = YES;
    }else{
        [[UIView appearance] setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];
        [PlatformService setRTL:NO];
//            [NSString KAUseLang:@"zh-Hans"];
        tabBarController.viewControllers = [controllers copy];
        [KANavigationBarManager sharedManager].isRTL = NO;
    }
    [UIApplication sharedApplication].keyWindow.rootViewController = tabBarController;
    
    
    //cookie设置语言
    NSDictionary* languageCookie = nil;
    if([lang isEqualToString: @"ar"]){
        languageCookie = @{@"b2c_c_lang":@"ar_SA"};
    }else if([lang isEqualToString:@"en"]){
        languageCookie = @{@"b2c_c_lang":@"en_US"};
    }else{
        languageCookie = @{@"b2c_c_lang":@"zh_CN"};
    }
}

- (NSString *)getCompleteImageUrl:(NSString *)url
                       moduleType:(NSString*)moduleType
                        imageSize:(CGSize)imageSize{
    return [[NSString alloc] initWithFormat:@"%@%@",[self getCompleteImageUrl:url moduleType:moduleType],NSStringFromCGSize(imageSize)];
}


- (NSString *)getCompleteImageUrl:(NSString *)url
                       moduleType:(NSString*)moduleType{
    NSString *urlStr = @"";
    if ([url.lowercaseString hasPrefix:@"http"]) {
        // 完整 URL
        urlStr = url;
        
    } else if ([url hasPrefix:@"//"]) {
        // 需要根据环境添加对应前缀
        NSString *prefix = @"";
        switch ([self getCurrentNetworkEnvironment]) {
            case 0:
                prefix = @"https:";
                break;
            case 1:
                prefix = @"http:";
                break;
            case 2:
                prefix = @"http:";
                break;
            default:
                prefix = @"https:";
                break;
        }
        urlStr = [NSString stringWithFormat:@"%@%@",prefix,url];
        
    } else {
        // 需要根据环境添加对应前缀
        NSString *prefix = @"";
        switch ([self getCurrentNetworkEnvironment]) {
            case 0:
                prefix = @"https://";
                break;
            case 1:
                prefix = @"http://";
                break;
            case 2:
                prefix = @"http://";
                break;
            default:
                prefix = @"https://";
                break;
        }
        //todo
        NSString* domain = @"image.external-st.com/pop";
        // i北汽
        switch ([self getCurrentNetworkEnvironment]) {
            case 0:
                // 正式
                domain = @"image.i.bjbaicmotor.com/pop";
                break;
            case 1:
                // 测试
                domain = @"bq.image.jd.com/pop";
                break;
            case 2:
                // 宿迁
                domain = @"image.external-st.com/pop";
                break;
            default:
                domain = @"bq.image.jd.com/pop";
                break;
        }
        if ([url hasPrefix:@"/"]) {
            urlStr = [NSString stringWithFormat:@"%@%@%@",prefix,domain,url];
        } else {
            urlStr = [NSString stringWithFormat:@"%@%@/%@",prefix,domain,url];
        }
    }
    
    return urlStr;
}

- (NSString *)getCompleteImageUrl:(NSString *)url{
    // URL判断
    NSString *urlStr = @"";
    if ([url.lowercaseString hasPrefix:@"http"]) {
        // 完整 URL
        urlStr = url;
        
    } else if ([url hasPrefix:@"//"]) {
        // 需要根据环境添加对应前缀
        NSString *prefix = @"";
        switch ([self getCurrentNetworkEnvironment]) {
            case 0:
                prefix = @"https:";
                break;
            case 1:
                prefix = @"http:";
                break;
            case 2:
                prefix = @"http:";
                break;
            default:
                prefix = @"https:";
                break;
        }
        urlStr = [NSString stringWithFormat:@"%@%@",prefix,url];
        
    } else {
        // 需要根据环境添加对应前缀
        NSString *prefix = @"";
        switch ([self getCurrentNetworkEnvironment]) {
            case 0:
                prefix = @"https://";
                break;
            case 1:
                prefix = @"http://";
                break;
            case 2:
                prefix = @"http://";
                break;
            default:
                prefix = @"https://";
                break;
        }
        //todo
        NSString* domain = @"image.external-st.com/pop";
        // i北汽
        switch ([self getCurrentNetworkEnvironment]) {
            case 0:
                // 正式
                domain = @"image.i.bjbaicmotor.com/pop";
                break;
            case 1:
                // 测试
                domain = @"bq.image.jd.com/pop";
                break;
            case 2:
                // 宿迁
                domain = @"image.external-st.com/pop";
                break;
            default:
                domain = @"bq.image.jd.com/pop";
                break;
        }
        if ([url hasPrefix:@"/"]) {
            urlStr = [NSString stringWithFormat:@"%@%@%@",prefix,domain,url];
        } else {
            urlStr = [NSString stringWithFormat:@"%@%@/%@",prefix,domain,url];
        }
    }
    
    return urlStr;
}

- (NSString *)getCompleteImageUrl:(NSString *)url imageSize:(CGSize)imageSize{
    return [[NSString alloc] initWithFormat:@"%@%@",[self getCompleteImageUrl:url],NSStringFromCGSize(imageSize)];
}

- (NSString *)getJumpH5UrlByType:(JDISVJumpH5UrlType)type withAppendParam:(NSString *)appendParam{
    switch (type) {
        case JDISVJumpH5UrlTypeGroupBuyShare:
            return [NSString stringWithFormat:@"%@",appendParam ? : @""];
        case JDISVJumpH5UrlTypeInvoiceList:
            return @"";
        case JDISVJumpH5UrlTypeInvoiceDetail:
            return [NSString stringWithFormat:@"%@",appendParam ? : @""];
        default:
            break;
    }
    return @"";
}

- (void)trackerWithType:(int)trackerType eventParam:(NSDictionary *)eventParam{
//todo 埋点相关
}

#pragma mark - 获取设备信息

// UUID 通过统一接口获取UUID
- (NSString *)getUUID{
    NSString* uuidKey = @"JDISVSystemService_UUID";
    NSString* uuid = [JDCDKeyChainStore stringForKey:uuidKey];
    if(!uuid){
        uuid = [[UIDevice currentDevice].identifierForVendor UUIDString];
        [JDCDKeyChainStore setString:uuid forKey:uuidKey];
    }
    return uuid;
}

- (NSString*)deviceInfo {
    return [[JDISVDeviceService alloc] hardwareString];
}

- (NSString*)deviceIP {
    return [JDISVIPAddressTool getIPAddress];
}

- (NSString*)deviceOS {
    return [[UIDevice currentDevice] systemVersion];
}

- (NSString*)deviceScreen{
    NSString *screen = [NSString stringWithFormat:@"%.0f,%.0f", [UIScreen mainScreen].currentMode.size.width, [UIScreen mainScreen].currentMode.size.height];
    return screen;
}

- (NSString*)deviceType{
    return [[UIDevice currentDevice] model];
}

-(BOOL)isIphoneX{
    if (@available(iOS 11.0, *)) {
        if([UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom > 0.0) return YES;
    }
    return NO;
}

#pragma mark - 获取APP信息
- (NSString*)appVer {
    return  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
}

- (NSString*)appBuilder {
    return  [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"];
}

- (BOOL)releaseVersion {
    return NO;
}

#pragma mark -获取Webview的UA
- (NSString *)getWebViewUserAgen{
    NSString* userAgen = [NSString stringWithFormat:@"kaappschema=%@;iPhone;AppVerson=%@;dd=%@",[self appURLScheme],[self appVer], [self getUUID]];
    return userAgen;
}

#pragma mark - 网络环境相关
/// 设置头部请求信息
/// @param headerInfo 字典接口
- (void)setNetWorkRequestHeaderInfo:(NSDictionary *)headerInfo {
    [[JDISVColorGateFormRequestService sharedService] configColorGateRequestHeaderParamWith:headerInfo];
    [[JDISVColorGateJsonRequestService sharedService] configColorGateRequestHeaderParamWith:headerInfo];
}

- (void)setNetWorkParamType:(NSInteger)type {
    [JDISVColorGateFormRequestService sharedService].paramType = type;
    [JDISVColorGateJsonRequestService sharedService].paramType = type;
}

- (void)setNetWorkParamFormateType:(JDISVColorGateConfigCustomParamterformBlock)formateFunction {
    [JDISVColorGateFormRequestService sharedService].configCustomParamterformBlock = formateFunction;
    [JDISVColorGateJsonRequestService sharedService].configCustomParamterformBlock = formateFunction;
}

- (void)setNetWorkB2CCommomParam:(NSDictionary *)commonParam andCommonBodyParam:(NSDictionary *)commonBodyParam {
    [JDISVColorGateFormRequestService sharedService].commonB2CParamter = [commonParam copy];
    [JDISVColorGateJsonRequestService sharedService].commonB2CParamter = [commonParam copy];
    
    [JDISVColorGateFormRequestService sharedService].commonB2CBodyParamter = [commonBodyParam copy];
    [JDISVColorGateJsonRequestService sharedService].commonB2CBodyParamter = [commonBodyParam copy];
}

- (NSDictionary *)cookieParamDictionary {
    return [self defaultDictionaryOfCookieInfo];
}



- (NSDictionary *)defaultDictionaryOfCookieInfo {
    NSString *a2 = [self getUserA2];
    if (a2 == nil || ![a2 jdcd_validateString]) {
        a2 = @"";
    }
    NSString *userPin = [self getUserPin];
    NSString *encodedUserPin = @"";
    if ([userPin jdcd_validateString]) {
        encodedUserPin = (NSString *)CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(kCFAllocatorDefault,(CFStringRef)userPin,NULL,NULL,kCFStringEncodingUTF8));
    }
    if (![encodedUserPin jdcd_validateString]) {
        encodedUserPin = @"";
    }
    
    return @{@"pin":encodedUserPin, @"wskey":a2};
}

- (void)setNetWorkEnvorimentStatusOf:(NSInteger)status envorimentParamOf:(NSDictionary *)dictionary {
    if (status == -1) {
        // 修改全部环境
        _networkEnviParamDic = [NSMutableDictionary dictionaryWithDictionary: dictionary];
    } else {
        // 修改相应环境
        NSString *statusKey = [[NSNumber numberWithInt:status] stringValue];
        [_networkEnviParamDic setObject:dictionary forKey:statusKey];
    }
    // 配置网络
    [[JDISVColorGateFormRequestService sharedService] configColorGateServiceHostInfo:[NSDictionary dictionaryWithDictionary:_networkEnviParamDic]];
    [[JDISVColorGateJsonRequestService sharedService] configColorGateServiceHostInfo:[NSDictionary dictionaryWithDictionary:_networkEnviParamDic]];
}

/// 配置Color网络环境 0:正式环境 1:预发or测试环境 2:自定义mock环境
- (void)switchNetworkEnvironment:(NSInteger)networkEnvType {
    // 需要通过ColorGateJson设置同步当前环境配置
    [[JDISVColorGateJsonRequestService sharedService] configServerStatus:networkEnvType];
    [[JDISVColorGateFormRequestService sharedService] configServerStatus:networkEnvType];
}

- (NSString*)getPriceTag{
    return [NSString getJDCDPriceTag];
//    return @"¥";
//    return @"￥";
}

/// 获取当前Color环境状态
- (NSInteger)getCurrentNetworkEnvironment{
    // 直接从JDCDKVStore取
    NSNumber *status = [JDCDKVStore objectForKey:JDISVColorGateHostStatusUserDefaultKey];
    return [status integerValue];
}

#pragma mark - 用户信息相关

- (BOOL)getUserIsLogin{
    //
    return self.isLogin;
}


- (NSString *)getUserPin {
    if ([self getUserIsLogin]) {
        return self.pin ? self.pin:@"";
    }
    
    return @"";
}

- (NSString *)getUserA2 {
    if ([self getUserIsLogin]) {
        return self.a2 ? self.a2:@"";
    }
    
    return @"";
}

- (void)setUserLoginStatus:(BOOL)login pin:(NSString *)pin andA2:(NSString *)a2 {
    self.isLogin = login;
    if (login) {
        self.pin = pin;
        self.a2 = a2;
    } else {
        self.pin = @"";
        self.a2 = @"";
    }
    // BUG:违反单一性原则 所以此方法废弃
    [self configDefaultNetworkingCookie];
}

/// 设置用户登录态及登录信息/凭证 - 需要在回调中更新用户登录Cookie
/// @param login 是否已登录
/// @param pin 用户Pin
/// @param token 登录凭证(A2 key等用户登录态信息)
/// @param updateCookieBlock 更新Cookie Block
- (void)setUserLoginStatus:(BOOL)login pin:(NSString *)pin token:(NSString *)token updateCookieBlock:(void(^)(void))updateCookieBlock {
    self.isLogin = login;
    if (login) {
        self.pin = pin;
        self.a2 = token;
    } else {
        self.pin = @"";
        self.a2 = @"";
    }
    if (updateCookieBlock) {
        updateCookieBlock();
    }
}

- (JDTAddressItemModel *)getDefaultAddress {
    if(self.isvDefaultAddress)
        return self.isvDefaultAddress;
    self.isvDefaultAddress = [JDTAddressItemModel defaultAddress];
    return self.isvDefaultAddress;
}

- (void)setDefaultAddress:(JDTAddressItemModel *)address {
    if (address && address.addressId != [self getDefaultAddress].addressId) {
        self.isvDefaultAddress = address;
        NSString* addrStr = [address yy_modelToJSONString];
        if(addrStr.length){
            [[NSUserDefaults standardUserDefaults] setObject:addrStr forKey:G_ISVDefaultAddressKey];
        }
        [[NSNotificationCenter defaultCenter] postNotificationName:ISVAddressChangedNotification object:address];
    }
}

- (void)showUnavailableNetWorkViewTips{
    if (self.networkTipView == nil) {
        self.networkTipView = [JDISVNetWorkTipView showTipAtWindow];
    }
}

- (void)hideUnavailableNetWorkView{
    if (self.networkTipView) {
        [self.networkTipView hide];
        self.networkTipView = nil;
    }
}


#pragma mark - Cookie信息相关
- (void)configDefaultNetworkingCookie {
    // 设置或更改用户名需要修改此处修改cookie
    NSDictionary *param = [self defaultDictionaryOfCookieInfo];
    NSString *cookie = [self getCookieStringParamOf:param];
    [[JDISVColorGateFormRequestService sharedService] setCookie:cookie];
    [[JDISVColorGateJsonRequestService sharedService] setCookie:cookie];
}

/// 从Cookie信息字典获取 Cookie字符串
/// @param dictionary Cookie字段信息
- (NSString *)getCookieStringParamOf:(NSDictionary *)dictionary {
    NSString *cookie = @"";
    for (NSString *key in dictionary.allKeys) {
        NSString *param = [NSString stringWithFormat:@"%@=%@", key, [dictionary objectForKey:key]];
        if ([cookie jdcd_validateString]) {
            cookie = [cookie stringByAppendingString:[NSString stringWithFormat:@";%@", param]];
        } else {
            cookie = [param copy];
        }
    }
    return cookie;
}

/// 使用自定义字典配置Cookie
/// @param cookieDic Cookie信息字典
- (void)configNetworkingCookieWith:(NSDictionary *)cookieDic {
    NSString *cookie = [self getCookieStringParamOf:cookieDic];
    [[JDISVColorGateFormRequestService sharedService] setCookie:cookie];
    [[JDISVColorGateJsonRequestService sharedService] setCookie:cookie];
}

/// 使用Cookie字符串直接设置
/// @param cookie cookie字符串
- (void)configNetworkingCookieStringWith:(NSString *)cookie {
    [[JDISVColorGateFormRequestService sharedService] setCookie:cookie];
    [[JDISVColorGateJsonRequestService sharedService] setCookie:cookie];
}

#pragma mark - 网络接口
-(NSString*)detailRequest:(JDCDURLTask*)task
                 response:(NSObject*)response
                      err:(NSError*)err{
    NSString* url = task.rawUrlSessionDataTask.currentRequest.URL.absoluteString;
    NSString* params = [NSString stringWithFormat:@"%@",task.requestParameter ];
    NSString* header = [NSString stringWithFormat:@"%@",task.rawUrlSessionDataTask.currentRequest.allHTTPHeaderFields ];
    NSString* errStr = err.localizedDescription;
    NSString* resposeStr = [NSString stringWithFormat:@"respose:%@",response];
    NSString* result;
    if(err){
        result = [NSString stringWithFormat:@"%@\n\n\n%@\n%@\n\n\n%@\n\n\n%@",url,task.traceId,params,header,errStr];
    }else{
        result = [NSString stringWithFormat:@"%@\n\n\n%@\n%@\n\n\n%@\n\n\n%@",url,task.traceId,params,header,resposeStr];
    }
    return result;
    
}
- (NSString*)jnosHost{
    NSString* domain = [PlatformService prefixUrlDomain:@"jnos-api-cloud"];
    NSString* str = [NSString stringWithFormat:@"%@%@",[self urlSchema],domain];
    return str;
}

- (NSString*)urlSchema{
    return @"https://";
}

- (NSString*)urlDomain{
    return @"warden.ksaec.ksapoc.com";
}

- (NSString*)prefixUrlDomain:(NSString*)prefix{
    NSString* urlDomain = [self urlDomain];
    return [urlDomain stringByReplacingOccurrencesOfString:@"warden" withString:prefix];
}

- (NSURLSessionDataTask *)newJnosPost:(NSString *)urlPath
                              apiCode:(NSString *)apiCode
                               params:(NSDictionary *)params
                             complete:(void (^)(JDCDURLTask *urlTask, id responseObject, NSError *error))completeBlock {
    // 无需实现，会调用 KSAPlatformService 中的同名方法
    return nil;
}

- (NSURLSessionDataTask * _Nonnull)jnosRequest:(JDCDHTTPSessionRequestType)requestType
                                      path:(NSString *)urlPath
                                       apiCode:(nonnull NSString *)apiCode
                                       bodyStr:(NSString*)bodyStr
                                      complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock{
    if(!completeBlock)
        return nil;
    
    if(urlPath == nil){
        urlPath = @"";
    }
    
    NSURLSessionConfiguration* cfg = [NSURLSessionConfiguration defaultSessionConfiguration];
    NSString* cookie = [JDISVColorGateFormRequestService sharedService].cookie;
    if(cookie.length){
        cfg.HTTPAdditionalHeaders = @{@"Content-Type":@"application/json",
                                      @"Cookie":cookie};
    }else{
        cfg.HTTPAdditionalHeaders = @{@"Content-Type":@"application/json"};
    }
    cfg.timeoutIntervalForRequest = 10;
    cfg.requestCachePolicy = NSURLRequestReloadIgnoringCacheData;

    NSURLSession* sesstion =  [NSURLSession sessionWithConfiguration:cfg];
    NSString* host = [self jnosHost];
    
    NSMutableString* url;
    if([urlPath hasPrefix:@"/"]){
        urlPath = [urlPath substringFromIndex:1];
    }
    if(urlPath.length == 0){
        url = [host mutableCopy];
    }
    else if([host hasSuffix:@"/"]){
        url = [[NSString stringWithFormat:@"%@%@",host,urlPath] mutableCopy];
    }else{
        url = [[NSString stringWithFormat:@"%@/%@",host,urlPath] mutableCopy];
    }
    if(apiCode.length){
        [url appendString:@"?apiCode="];
        [url appendString:apiCode];
    }
    ISVLog(@"用户请求:%@",url);
    
    NSMutableURLRequest* request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    if(JDCDHTTPSessionRequestTypePost == requestType){
        request.HTTPMethod = @"POST";
    }else{
        request.HTTPMethod = @"GET";
    }
    
    request.HTTPBody = [bodyStr dataUsingEncoding:NSUTF8StringEncoding];
    NSURLSessionDataTask* task = [sesstion dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        
        
        if(error){
            ISVLogD(error.localizedDescription,@"请求失败:%@",url);
            dispatch_async(dispatch_get_main_queue(),^{
                completeBlock(nil,nil,error);
            });
            
        }else{
            NSError* jsonErr;
            NSDictionary* result = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:&jsonErr];
            if(jsonErr){
                ISVLogD(error.localizedDescription,@"请求失败:%@",url);
                dispatch_async(dispatch_get_main_queue(),^{
                    completeBlock(nil,nil,error);
                });
                
            }else{
                @try {
                    NSHTTPURLResponse* urlRes = (NSHTTPURLResponse*)response;
                    
                    NSString* header = [NSString stringWithFormat:@"%@",urlRes.allHeaderFields ];
                    
                    NSString* resposeStr = [NSString stringWithFormat:@"respose:%@",response];
                    NSString* traceId = result[@"traceId"];
                    NSString* resultStr = [NSString stringWithFormat:@"%@\n\n\n%@\n%@\n\n\n%@\n\n\n%@",url,traceId,bodyStr,header,resposeStr];
                    ISVLogD(resultStr,@"请求成功:%@",url);
                } @catch (NSException *exception) {
                    
                }
                dispatch_async(dispatch_get_main_queue(),^{
                    completeBlock(nil,result,nil);
                });
            }
        }
        NSLog(@"data %@ err:%@",data,error);
    }];
    [task resume];
    return task;
}
/**
 @brief Color网关请求(使用functionId)-不解析返回内容通用方法
 @see JDCDHTTPSessionRequestType
 @see JDCDURLRequestSerializerType
 @param urlPath 请求路径 e.g. http://beta-api.m.jd.com/api/product/v1/product/page 路径为@"api/product/v1/product/page"
 @param functionId functionId 方法ID
 @param version version 方法版本
 @param parameters parameters 业务参数
 @param completeBlock 完成回调(Block中是原始数据)
 @return 网络请求NSURLSessionDataTask
 */
- (NSURLSessionDataTask * _Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                     requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                                      path:(NSString *)urlPath
                                  function:(NSString * _Nonnull)functionId
                                    version:(NSString * _Nonnull)version
                                parameters:(NSDictionary * _Nullable)parameters
                                  complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock {
    return [self request:requestType requestSerializerType:requestSerializerType paramterType:JDISVColorGateParameterTypeDefault path:urlPath function:functionId version:version parameters:parameters complete:completeBlock];
}

/**
 @brief Color网关请求(使用functionId)-不解析返回内容通用方法
 @see JDCDHTTPSessionRequestType
 @see JDCDURLRequestSerializerType
 @param paramterType 请求参数格式
 @param urlPath 请求路径 e.g. http://beta-api.m.jd.com/api/product/v1/product/page 路径为@"api/product/v1/product/page"
 @param functionId functionId 方法ID
 @param version version 方法版本
 @param parameters parameters 业务参数
 @param completeBlock 完成回调(Block中是原始数据)
 @return 网络请求NSURLSessionDataTask
 */
- (NSURLSessionDataTask * _Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                     requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                              paramterType:(JDISVColorGateParameterType)paramterType
                                      path:(NSString *)urlPath
                                  function:(NSString * _Nonnull)functionId
                                    version:(NSString * _Nonnull)version
                                parameters:(NSDictionary * _Nullable)parameters
                                  complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock {
    NSString* urlLogStr = [NSString stringWithFormat:@"%@%@%@",urlPath?:@"",functionId?:@"",version?:@""];
    ISVLog(@"用户请求:%@",urlLogStr);
    void (^completeBlockLog)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error) = ^(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error){
        if(error){
            NSString* detailLog = [self detailRequest:urlTask response:responseObject err:error];
            ISVLogD(detailLog,@"请求失败:%@",urlLogStr);
        }else{
            NSString* detailLog = [self detailRequest:urlTask response:responseObject err:error];
            ISVLogD(detailLog,@"请求成功:%@",urlLogStr);
        }
        completeBlock(urlTask,responseObject,error);
    };
    
    if (requestSerializerType == JDCDURLRequestSerializerTypeJson) {
        return [[JDISVColorGateJsonRequestService sharedService] request:requestType path:urlPath function:functionId version:version paramterType:paramterType parameters:parameters complete:completeBlockLog];
    } else {
        return [[JDISVColorGateFormRequestService sharedService] request:requestType path:urlPath function:functionId version:version paramterType:paramterType parameters:parameters complete:completeBlockLog];
    }
}

/**
 @brief
 @see requestType requestType GET/POST
 @see requestSerializerType 序列化类型
 @param urlPath 请求路径 e.g. http://beta-api.m.jd.com/api/product/v1/product/page 路径为@"api/product/v1/product/page"
 @param functionId functionId 方法ID
 @param version version 方法版本
 @param parameters parameters 业务参数
 @param progressRawData 解析返回内容(传nil使用通用错误处理)
 @param success 成功回调
 @param failure 失败回调
 /@return 网络请求NSURLSessionDataTask
 */
- (NSURLSessionDataTask *_Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                    requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                                     path:(NSString *)urlPath
                                 function:(NSString *_Nonnull)functionId
                                 vsersion:(NSString *_Nonnull)version
                               parameters:(NSDictionary *_Nullable)parameters
                          progressRawData:(nullable NSError * (^)(id _Nullable responseObject))progressRawData
                                  success:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject))success
                                  failure:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, NSError *_Nonnull error))failure {
    NSString* urlLogStr = [NSString stringWithFormat:@"%@%@%@",urlPath?:@"",functionId?:@"",version?:@""];
    ISVLog(@"用户请求:%@",urlLogStr);
    void (^successBlock)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject) = ^(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject){
        NSString* detailLog = [self detailRequest:urlTask response:responseObject err:nil];
        ISVLogD(detailLog,@"请求成功:%@",urlLogStr);
        success(urlTask,responseObject);
    };
    void (^failBlock)(JDCDURLTask * _Nonnull urlTask, NSError *_Nonnull error) = ^(JDCDURLTask * _Nonnull urlTask, NSError *_Nonnull error){
        NSString* detailLog = [self detailRequest:urlTask response:nil err:error];
        ISVLogD(detailLog,@"请求失败:%@",urlLogStr);
        failure(urlTask,error);
    };
    
    if (requestSerializerType == JDCDURLRequestSerializerTypeJson) {
        return [[JDISVColorGateJsonRequestService sharedService] request:requestType path:urlPath function:functionId vsersion:version parameters:parameters progressRawData:progressRawData success:successBlock failure:failBlock];
    } else {
        return [[JDISVColorGateFormRequestService sharedService] request:requestType path:urlPath function:functionId vsersion:version parameters:parameters progressRawData:progressRawData success:successBlock failure:failBlock];
    }
}

/// 上传图片方法
/// @param data NSData 图片数据
/// @param imageName fileName
/// @param requestSerializerType 序列化类型
/// @param success 成功回调
/// @param failure 失败回调
- (NSURLSessionDataTask *)uploadImageWithData:(NSData *)data
                                    imageName:(NSString *)imageName
                        requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                                      success:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject))success
                                      failure:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, NSError *_Nonnull error))failure {
    ISVLog(@"上传图片:%@",imageName);
    
    void (^successBlock)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject) = ^(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject){
        NSString* detailLog = [NSString stringWithFormat:@"traceid:%@Request:%@ \n\n\nresponse:%@",urlTask.traceId,urlTask.rawUrlSessionDataTask.currentRequest,responseObject];
        ISVLog(detailLog,@"上传成功--url:%@",urlTask.requestUrl);
        success(urlTask,responseObject);
    };
    void (^failBlock)(JDCDURLTask * _Nonnull urlTask, NSError *_Nonnull error) = ^(JDCDURLTask * _Nonnull urlTask, NSError *_Nonnull error){
        NSString* errLog = [NSString stringWithFormat:@"traceid:%@Request:%@ \n\n\nError:%@",urlTask.traceId,urlTask.rawUrlSessionDataTask.currentRequest,error];
        ISVLogD(errLog,@"上传失败--url:%@",urlTask.requestUrl);
        failure(urlTask,error);
    };
    if (requestSerializerType == JDCDURLRequestSerializerTypeJson) {
        return [[JDISVColorGateJsonRequestService sharedService] uploadImageWithData:data imageName:imageName success:successBlock failure:failBlock];
    } else {
        return [[JDISVColorGateFormRequestService sharedService] uploadImageWithData:data imageName:imageName success:successBlock failure:failBlock];
    }
}

/// 上传图片方法
/// JNOS网关
/// @param UIImage  图片数据
/// @param requestSerializerType 序列化类型
/// @param path
/// @param apiCode
/// @param success 成功回调
/// @param failure 失败回调
- (NSURLSessionDataTask *)jnosUploadImage:(UIImage *)image
                    requestSerializerType:(JDCDHTTPSessionRequestType)requestType
                                     path:(NSString *)urlPath
                                  apiCode:(nonnull NSString *)apiCode
                                   params:(NSMutableDictionary *)params
                                 complete:(nullable void (^)(JDCDURLTask *urlTask, id responseObject, NSError *error))completeBlock {
    
    if(!completeBlock)
        return nil;
    
    if(urlPath == nil){
        urlPath = @"";
    }
    
    NSURLSessionConfiguration* cfg = [NSURLSessionConfiguration defaultSessionConfiguration];
    NSString* cookie = [JDISVColorGateFormRequestService sharedService].cookie;
    if(cookie.length){
        cfg.HTTPAdditionalHeaders = @{@"Content-Type":@"application/json",
                                      @"Cookie":cookie};
    }else{
        cfg.HTTPAdditionalHeaders = @{@"Content-Type":@"application/json"};
    }
    cfg.timeoutIntervalForRequest = 10;
    cfg.requestCachePolicy = NSURLRequestReloadIgnoringCacheData;
    
    NSURLSession* sesstion =  [NSURLSession sessionWithConfiguration:cfg];
    NSString *host = [self jnosHost];
    
    NSMutableString *url;
    if([urlPath hasPrefix:@"/"]){
        urlPath = [urlPath substringFromIndex:1];
    }
    if(urlPath.length == 0){
        url = [host mutableCopy];
    }
    else if([host hasSuffix:@"/"]){
        url = [[NSString stringWithFormat:@"%@%@",host,urlPath] mutableCopy];
    }else{
        url = [[NSString stringWithFormat:@"%@/%@",host,urlPath] mutableCopy];
    }
    if(apiCode.length){
        [url appendString:@"?apiCode="];
        [url appendString:apiCode];
    }
    ISVLog(@"用户请求:%@",url);
    
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    if(JDCDHTTPSessionRequestTypePost == requestType){
        request.HTTPMethod = @"POST";
    }
    // 假设这些是要上传的图片
    NSData *imageData = UIImageJPEGRepresentation(image, 0.5);
    // 设置请求 URL
    NSMutableURLRequest *mutableRequest = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:url]];
    [mutableRequest setHTTPMethod:@"POST"];

    // 设置 Content-Type 为 multipart/form-data
    NSString *contentType = [NSString stringWithFormat:@"multipart/form-data;"];
    [mutableRequest addValue:contentType forHTTPHeaderField:@"Content-Type"];
    // 构建请求体
    NSMutableData *body = [NSMutableData data];
    // 添加第一张图片数据
    // 设置 Content-Type 为 multipart/form-data
    NSString *boundary = @"Boundary+XXXXXXXXXX";
    [body appendData:[[NSString stringWithFormat:@"--%@\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
    [body appendData:[[NSString stringWithFormat:@"Content-Disposition: form-data; name=\"imageFieldName1\"; filename=\"uploadedImage1.jpg\"\r\n"] dataUsingEncoding:NSUTF8StringEncoding]];
    [body appendData:[@"Content-Type: image/jpeg\r\n\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
    [body appendData:imageData];
    [body appendData:[[NSString stringWithFormat:@"\r\n"] dataUsingEncoding:NSUTF8StringEncoding]];

    // 结束标记
    [body appendData:[[NSString stringWithFormat:@"--%@--\r\n", boundary] dataUsingEncoding:NSUTF8StringEncoding]];
    [mutableRequest setHTTPBody:body];

    // 发送请求（这里只是示例，实际中需要使用合适的网络请求框架来发送请求）
//    NSURLResponse *response = nil;
//    NSError *error = nil;
//    NSData *returnData = [NSURLConnection sendSynchronousRequest:mutableRequest returningResponse:&response error:&error];
//    if (error) {
//        NSLog(@"Error uploading: %@", error);
//    } else {
//        NSString *responseString = [[NSString alloc] initWithData:returnData encoding:NSUTF8StringEncoding];
//        NSLog(@"Response: %@", responseString);
//    }
//    
//    NSData *bodyData = [params dataUsingEncoding:NSUTF8StringEncoding];
//    NSString *bodyStr = [[NSString alloc] initWithData:bodyData encoding:NSUTF8StringEncoding];
//    if (nil == bodyStr || bodyStr.length == 0) {
//        bodyStr = @"";
//    }
    
//    request.HTTPBody = bodyData;
    NSURLSessionDataTask* task = [sesstion dataTaskWithRequest:request 
                                             completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError *error) {
        if(error){
            ISVLogD(error.localizedDescription,@"请求失败:%@",url);
            dispatch_async(dispatch_get_main_queue(),^{
                completeBlock(nil,nil,error);
            });
            
        }else{
            NSError* jsonErr;
            NSDictionary* result = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:&jsonErr];
            if(jsonErr){
                ISVLogD(error.localizedDescription,@"请求失败:%@",url);
                dispatch_async(dispatch_get_main_queue(),^{
                    completeBlock(nil,nil,error);
                });
                
            }else{
                @try {
                    NSHTTPURLResponse* urlRes = (NSHTTPURLResponse*)response;
                    
                    NSString* header = [NSString stringWithFormat:@"%@",urlRes.allHeaderFields];
                    
                    NSString* resposeStr = [NSString stringWithFormat:@"respose:%@",response];
                    NSString* traceId = result[@"traceId"];
                    NSString* resultStr = [NSString stringWithFormat:@"%@\n\n\n%@\n%@\n\n\n%@\n\n\n%@",url,traceId,header,resposeStr];
                    ISVLogD(resultStr,@"请求成功:%@",url);
                } @catch (NSException *exception) {
                    
                }
                dispatch_async(dispatch_get_main_queue(),^{
                    completeBlock(nil,result,nil);
                });
            }
        }
        NSLog(@"data %@ err:%@",data,error);
    }];
    [task resume];
    return task;
}

#pragma mark - Private 网络

/// 配置Color网络参数
- (void)configColorNetworkingParam {
    
    // 获取UUID
    NSString *uuid = [self getUUID];
    if (uuid==nil) {
        uuid = @"";
    }
    [self setNetWorkParamType:JDISVColorGateParamterformTypeOverseas];
    [[JDISVColorGateFormRequestService sharedService] configColorGateServiceUUID:uuid];
    [[JDISVColorGateJsonRequestService sharedService] configColorGateServiceUUID:uuid];
    // 获取host配置
    
    _networkEnviParamDic = [NSMutableDictionary dictionaryWithDictionary: @{
        // 0:正式环境 1:预发or测试环境 2:自定义mock环境
        @"0": @{ // 正式环境
                @"GateServerHost":@"https://bqcolor.i.bjbaicmotor.com",
                @"UploadImageColorServerHost":@"https://my.i.bjbaicmotor.com",
                @"ColorAppId":@"bq_market",
                @"ColorSecret":@"7e3ddb8b79ae4e5facca4d81e78fb74d"
        }
    }];
    
    // 配置网络
    [[JDISVColorGateFormRequestService sharedService] configColorGateServiceHostInfo:[NSDictionary dictionaryWithDictionary:_networkEnviParamDic]];
    [[JDISVColorGateJsonRequestService sharedService] configColorGateServiceHostInfo:[NSDictionary dictionaryWithDictionary:_networkEnviParamDic]];
}

- (void)WebGenTokenJump:(NSString*)url
                webView:(WKWebView*)webView
              resultUrl:(void(^)(NSString* rUrl,NSError* err))resultBlock{
    if(!resultBlock){
        [NSException raise:@"no block" format:@"no block"];
    }
    if(url.length){
        resultBlock(url,nil);
    }
    return;
}

-(void)loginCookie:(WKWebView*)webView{
    NSString* pin = [PlatformService getUserPin];
    NSString* key = [PlatformService getUserA2];
    if(pin.length && key.length) {
        NSArray* domains = [PlatformService webViewCookieDomain];
        for(NSString* domain in domains){
            [self insertCookieInJavescripeWithCookieName:@"PASSPORT_KA_KEY" cookieValue:key domain:domain webView:webView];
            [self insertCookieInJavescripeWithCookieName:@"PASSPORT_USER_PIN_KEY" cookieValue:pin domain:domain webView:webView];
            [self insertCookieInJavescripeWithCookieName:@"yb_key" cookieValue:key domain:domain webView:webView];
            [self insertCookieInJavescripeWithCookieName:@"yb_pin" cookieValue:pin domain:domain webView:webView];
            [self insertCookieInJavescripeWithCookieName:@"pwdt_id" cookieValue:pin domain:domain webView:webView];
            
            [self oldSetCookieName:@"PASSPORT_KA_KEY" value:key domain:domain];
            [self oldSetCookieName:@"PASSPORT_USER_PIN_KEY" value:pin domain:domain];
            [self oldSetCookieName:@"yb_key" value:key domain:domain];
            [self oldSetCookieName:@"yb_pin" value:pin domain:domain];
            [self oldSetCookieName:@"pwdt_id" value:pin domain:domain];
        }
        
        if (@available(iOS 11, *)) {
            NSArray *cookies = [[NSHTTPCookieStorage sharedHTTPCookieStorage] cookies];
            WKHTTPCookieStore *cookieStore = webView.configuration.websiteDataStore.httpCookieStore;
            for (NSHTTPCookie *cookie in cookies) {
                [cookieStore setCookie:cookie completionHandler:nil];
            }
        }
    }else{
        WKWebsiteDataStore *dateStore = [WKWebsiteDataStore defaultDataStore];
        [dateStore fetchDataRecordsOfTypes:[WKWebsiteDataStore allWebsiteDataTypes]
                         completionHandler:^(NSArray<WKWebsiteDataRecord *> * __nonnull records) {
            for (WKWebsiteDataRecord *record in records)
            {              {
                [[WKWebsiteDataStore defaultDataStore] removeDataOfTypes:record.dataTypes
                                                          forDataRecords:@[record]
                                                       completionHandler:^{
                    
                }];
            }
            }
        }];
    }
}

-(void)oldSetCookieName:(NSString*)name
                  value:(NSString*)value
                 domain:(NSString*)domain{
    NSMutableDictionary *cookieProperties = [NSMutableDictionary dictionary];
    [cookieProperties setObject:name forKey:NSHTTPCookieName];
    [cookieProperties setObject:value forKey:NSHTTPCookieValue];
    [cookieProperties setObject:domain forKey:NSHTTPCookieDomain];
    [cookieProperties setObject:domain forKey:NSHTTPCookieOriginURL];
    [cookieProperties setObject:@"/" forKey:NSHTTPCookiePath];
    [cookieProperties setObject:@"0" forKey:NSHTTPCookieVersion];
    /// 设置失效时间，不要设置NSHTTPCookieDiscard（设置sessionOnly，会话操作使用）
    [cookieProperties setObject:[[NSDate date] dateByAddingTimeInterval:3600] forKey:NSHTTPCookieExpires];
    NSHTTPCookie *cookie = [NSHTTPCookie cookieWithProperties:cookieProperties];
    [[NSHTTPCookieStorage sharedHTTPCookieStorage] setCookie:cookie];
}

- (void)insertCookieInJavescripeWithCookieName:(NSString *)cookieName
                                   cookieValue:(NSString *)cookieValue
                                        domain:(NSString *)domain
                                        webView:(WKWebView*)webView{
    NSString *cookieSource = [NSString stringWithFormat:@"document.cookie = '%@=%@;domain=%@;path=/;max-age=3600;';",cookieName, cookieValue,domain];

    WKUserScript *cookieScript = [[WKUserScript alloc] initWithSource:cookieSource injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:NO];
    [webView.configuration.userContentController addUserScript:cookieScript];
}

- (void)WebGenTokenJump:(NSString*)url resultUrl:(void(^)(NSString* rUrl, NSError* err))resultBlock{
    if(!resultBlock){
        [NSException raise:@"no block" format:@"no block"];
    }
    if(url.length){
        resultBlock(url,nil);
    }
    return;
}

#pragma mark - Theme主题配置相关
/// 设置主题配置文件
- (void)configThemeList {
    [[JDISVThemeManager sharedManager] fileWithBundle:[self themeListBundle] themeFileName:[self themeListFileName]];
}

//当前是否RTL
-(BOOL)isRTL{
    return self.RTL;
}

//设置当前模式为RTL
-(void)setRTL:(BOOL)rtl{
    if(_RTL != rtl){
        _RTL  = rtl;
        [self ReverseTabbarChilds];
        [KANavigationBarManager sharedManager].isRTL = _RTL;
    }
}

-(void)ReverseTabbarChilds{
    KATabbarController* tabController = (KATabbarController*)[JDISVAppDelegate sharedAppDelegate].tabBarController;
    [tabController.cTabbar  layoutIfNeeded];
    [tabController.cTabbar layoutSubviews];
}


-(NSString*)getRuleURL:(JDISVUrlRule)rule{
    [[NSException exceptionWithName:@"subClase need implement" reason:@"Not implement" userInfo:nil] raise];
    return nil;
}

//获取微信ID

#pragma mark - Tab切换
-(void)showPage:(ShowPageType)pageType{
    ISVLog(@"用户主TAB切换:%@",@(pageType));
    @try {
        UITabBarController* tabController = (UITabBarController*) [JDISVAppDelegate sharedAppDelegate].window.rootViewController;
        if([tabController isKindOfClass:UITabBarController.class]){
            NSInteger index;
            if(self.RTL){
                index = (pageType == ISVShowMinePage?0:tabController.viewControllers.count-1);
            }else{
                index = (pageType == ISVShowHomePage?0:tabController.viewControllers.count-1);
            }
            tabController.selectedIndex = index;
            UINavigationController* nav = tabController.viewControllers[index];
            if([nav isKindOfClass:UINavigationController.class]){
                [nav popToRootViewControllerAnimated:NO];
            }
        }
    } @catch (NSException *exception) {
        NSLog(@"%@",exception);
    }
}

-(void)configLogDebugInfo:(LogDebugInfo)logInfo{
    NSLog(@"loginfo:%@",@(logInfo));
    self.logInfoType = logInfo;
}

;

//不带module名称的日志
-(void)Log:(char*)file
      Fun:(char*)fun
      Info:(NSString*)format, ...{
    va_list ap;
    va_start(ap,format);
    NSString *print = [[NSString alloc] initWithFormat: format arguments: ap];
    va_end(ap);
    [self Log:file Fun:fun Module:@"" Info:print];
}
-(void)Log:(char*)fileName
      Fun:(char*)functionName
    Module:(NSString*)module
      Info:(NSString*)format, ...{
    va_list ap;
    va_start(ap,format);
    NSString *print = [[NSString alloc] initWithFormat: format arguments: ap];
    va_end(ap);
    
    [self Log:fileName Fun:functionName Module:module Detail:@"" Info:print];
}
-(void)Log:(char*)fileName
      Fun:(char*)functionName
    Module:(NSString*)module
    Title:(NSString*)title
      Info:(NSString*)format, ...{
    va_list ap;
    va_start(ap,format);
    NSString *print = [[NSString alloc] initWithFormat: format arguments: ap];
    va_end(ap);
        @autoreleasepool
        {
            static NSDateFormatter* dateFormate;
            if(!dateFormate){
                dateFormate = [[NSDateFormatter alloc] init];
                dateFormate.dateFormat = @"YYYY-MM-dd HH:mm:ss";
            }
            NSDate* date = [NSDate date];
            NSString* logTimeStr = [dateFormate stringFromDate:date];
            NSString *file, *function;
            file = [[NSString alloc] initWithBytes:fileName length: strlen(fileName) encoding: NSUTF8StringEncoding].lastPathComponent;
            function = [NSString stringWithCString:functionName encoding:NSUTF8StringEncoding];
            
            NSMutableString* printDebug = [[NSMutableString alloc] init];
            if(self.logInfoType & logTime){
                [printDebug appendString:logTimeStr?:@""];
                [printDebug appendString:@":"];
            }
            if(self.logInfoType & logModule){
                [printDebug appendString:module?:@""];
                [printDebug appendString:@";"];
            }
            if(self.logInfoType & logFile){
                [printDebug appendString:file?:@""];
                [printDebug appendString:@";"];
            }
            if(self.logInfoType & logFun){
                [printDebug appendString:function?:@""];
                [printDebug appendString:@";"];
            }
            if(self.logInfoType & logInfo){
                [printDebug appendString:print?:@""];
            }
            
            if(printDebug.length){
                NSLog(@"%@",printDebug?:@"");
            }
            
            NSDictionary* param = @{@"date":logTimeStr,
                                    @"info":title?:@"",
                                    @"func":function?:@"",
                                    @"file":file?:@"",
                                    @"module":module?:@"",
                                    @"detail":print?:@""
            };
            NSString* moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:JDISVModuleTypeLog];
            NSString* router = [NSString stringWithFormat:@"router://%@/saveLog",moduleName];
            [JDRouter openURL:router arg:param error:nil completion:nil];
        }
}

-(NSString*)getWXID{
    [NSException raise:@"subClass need Implement" format:@"Not Implement"];
    return nil;
}
//带module名称的日志
-(void)Log:(char*)fileName
      Fun:(char*)functionName
    Module:(NSString*)module
    Detail:(NSString*)detail
    Info:(NSString*)format, ...{
    va_list ap;
    va_start(ap,format);
    NSString *print = [[NSString alloc] initWithFormat: format arguments: ap];
    va_end(ap);
        @autoreleasepool
        {
            static NSDateFormatter* dateFormate;
            if(!dateFormate){
                dateFormate = [[NSDateFormatter alloc] init];
                dateFormate.dateFormat = @"YYYY-MM-dd HH:mm:ss";
            }
            NSDate* date = [NSDate date];
            NSString* logTimeStr = [dateFormate stringFromDate:date];
            NSString *file, *function;
            file = [[NSString alloc] initWithBytes:fileName length: strlen(fileName) encoding: NSUTF8StringEncoding].lastPathComponent;
            function = [NSString stringWithCString:functionName encoding:NSUTF8StringEncoding];
            
            NSMutableString* printDebug = [[NSMutableString alloc] init];
            if(self.logInfoType & logTime){
                [printDebug appendString:logTimeStr?:@""];
                [printDebug appendString:@":"];
            }
            if(self.logInfoType & logModule){
                [printDebug appendString:module?:@""];
                [printDebug appendString:@";"];
            }
            if(self.logInfoType & logFile){
                [printDebug appendString:file?:@""];
                [printDebug appendString:@";"];
            }
            if(self.logInfoType & logFun){
                [printDebug appendString:function?:@""];
                [printDebug appendString:@";"];
            }
            if(self.logInfoType & logInfo){
                [printDebug appendString:print?:@""];
            }
            
            if(printDebug.length){
                NSLog(@"%@",printDebug?:@"");
            }
            
            NSDictionary* param = @{@"date":logTimeStr,
                                    @"info":print?:@"",
                                    @"func":function?:@"",
                                    @"file":file?:@"",
                                    @"module":module?:@"",
                                    @"detail":detail?:@""
            };
            [self doSendLog:param];
        }
}

-(void)doSendLog:(NSDictionary*)param{
    NSString* moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:JDISVModuleTypeLog];
    NSString* router = [NSString stringWithFormat:@"router://%@/saveLog",moduleName];
    [JDRouter openURL:router arg:param error:nil completion:nil];
}

+ (NSString *)generateBoundary {
    CFUUIDRef uuid = CFUUIDCreate(NULL);
    CFStringRef uuidStr = CFUUIDCreateString(NULL, uuid);
    NSString *boundaryPrefix = @"Boundary+";
    NSString *boundary = [boundaryPrefix stringByAppendingString:(__bridge NSString *)uuidStr];
    CFRelease(uuid);
    CFRelease(uuidStr);
    return boundary;
}


@end
