//
//  JDCDPermissionSetting.m
//  BQPermissionsModule
//
//  Created by 张润峰 on 2020/9/18.
//

#import "JDCDPermissionSetting.h"

#import "LBXPermissionSetting.h"

@implementation JDCDPermissionSetting

/// 跳转本 app 对应的设置
+ (void)displayAppPrivacySettings {
    [LBXPermissionSetting displayAppPrivacySettings];
}

/// 跳转 系统设置-隐私-定位 界面
+ (void)displayAppPrivacyLocationSettings {
    NSURL *url = [NSURL URLWithString:@"App-Prefs:root=Privacy&path=LOCATION"];

    if ([[UIApplication sharedApplication] canOpenURL:url]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
    }
}

+ (void)showAlertToDislayPrivacySettingWithTitle:(NSString *)title msg:(NSString *)message cancel:(NSString *)cancel setting:(NSString *)setting completion:(void (^)(void))completion {
    
    [LBXPermissionSetting showAlertToDislayPrivacySettingWithTitle:title msg:message cancel:cancel setting:setting completion:^{
        if (completion) {
            completion();
        }
        [self displayAppPrivacySettings];
    }];
}

+ (void)showAlertToDislayLocationSettingWithTitle:(NSString *)title msg:(NSString *)message cancel:(NSString *)cancel setting:(NSString *)setting completion:(void (^)(void))completion {
    
    [LBXPermissionSetting showAlertToDislayPrivacySettingWithTitle:title msg:message cancel:cancel setting:setting completion:^{
        if (completion) {
            completion();
        }
        [self displayAppPrivacyLocationSettings];
    }];
}

@end

