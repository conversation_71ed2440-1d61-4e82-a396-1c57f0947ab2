//
//  JDTCouponItemModel.h
//  JDTCommonToolModule
//
//  Created by lvchenzhu.1 on 2025/6/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDTCouponItemModel : NSObject

@property (nonatomic, strong) NSNumber *voucherBatchId;

@property (nonatomic, strong) NSNumber *voucherId;

@property (nonatomic, assign) NSInteger scopeType;

@property (nonatomic, copy) NSString *scopeTypeText;

@property (nonatomic, assign) NSInteger obtainStatus;

@property (nonatomic, copy) NSString *voucherTitle;

@property (nonatomic, strong) NSNumber *discount;

@property (nonatomic, assign) NSInteger discountType;

@property (nonatomic, copy) NSString *startTime;

@property (nonatomic, copy) NSString *endTime;

@property (nonatomic, copy) NSString *skuLimitNote;

@property (nonatomic, copy) NSString *areaLimitNote;

@property (nonatomic, copy) NSString *voucherNote;

@property (nonatomic, copy) NSString *usedTimeRoleNote;

@property (nonatomic, strong) NSNumber *num;

@property (nonatomic, strong) NSNumber *discountThreshold;

@property (nonatomic, copy) NSString *unavailableReason;

@property (nonatomic, assign) NSInteger skuLimitType;

@property (nonatomic, assign) BOOL captchaVerify;

@end

NS_ASSUME_NONNULL_END
