//
//  PDProductSpecModel.h
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/20.
//

//  商品规格：比如衣服颜色、尺寸等

#import <Foundation/Foundation.h>
#import "JDTGloablDefines.h"

NS_ASSUME_NONNULL_BEGIN

@interface PDProductSpecItemDetailModel : NSObject

@property (nonatomic, copy) NSArray <NSString *> *skuList;

@property (nonatomic, assign) StockState stockState;

@property (nonatomic, copy) NSString *imgUrl;

@property (nonatomic, strong) NSNumber *serialNumber;

@property (nonatomic, copy) NSString *text;

@end

@interface PDProductSpecItemModel : NSObject

@property (nonatomic, strong) NSNumber *dimNumber;

@property (nonatomic, copy) NSString *dimTitle;

@property (nonatomic, copy) NSArray <PDProductSpecItemDetailModel *> *selectedDimBtnPartVoList;

@end

@interface PDProductSpecModel : NSObject

@property (nonatomic, copy) NSArray <PDProductSpecItemModel *> *saleAttrList;

@end

NS_ASSUME_NONNULL_END
