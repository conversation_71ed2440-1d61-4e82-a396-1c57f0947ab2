//
//  JDTAddressModel.h
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDTAddressItemModel : NSObject

@property (nonatomic, assign) NSInteger addressId;
/// 收货人名字
@property (nonatomic, copy) NSString *name;
 /// 收货人手机号
@property (nonatomic, copy) NSString *mobile;
/// 国家手机号代码，如：+86
@property (nonatomic, copy) NSString *mobileCountry;
/// 国家手机号代码，如：+86（同上，不同接口返回的）
@property (nonatomic, copy) NSString *areaCode;
/// 是否是默认地址
@property (nonatomic, assign) BOOL defAddress;
/// 是否是默认地址（同上，不同接口返回的）
@property (nonatomic, assign) BOOL defaultAddress;

@property (nonatomic, copy) NSString *fullAddress;

@property (nonatomic, copy) NSString *shippingAddress;

@property (nonatomic, copy) NSString *addressDetail;

@property (nonatomic, strong) NSNumber *postCode;

@property (nonatomic, copy) NSString *provinceId;

@property (nonatomic, copy) NSString *provinceName;

@property (nonatomic, copy) NSString *cityId;

@property (nonatomic, copy) NSString *cityName;

@property (nonatomic, copy) NSString *districtId;

@property (nonatomic, copy) NSString *districtName;

@property (nonatomic, copy) NSString *townId;

@property (nonatomic, copy) NSString *townName;
 /// 纬度
@property (nonatomic, strong) NSNumber *latitude;
/// 经度
@property (nonatomic, strong) NSNumber *longitude;
/// 地址 tag 来源（1 为内置，2 为自定义）
@property (nonatomic, strong) NSNumber *tagSource;
/// 地址 tag，如：家、公司等
@property (nonatomic, copy) NSString *tag;

@property (nonatomic, copy) NSString *email;

@property (nonatomic, copy) NSString *lastName;

@property (nonatomic, copy) NSString *firstName;

+ (instancetype)defaultAddress;

@end

@interface JDTAddressModel : NSObject

@property (nonatomic, assign) NSInteger maxNum;

@property (nonatomic, copy) NSArray <JDTAddressItemModel *> *addressList;

@end

NS_ASSUME_NONNULL_END
