//
//  JDTGloablDefines.h
//  JDTCommonToolModule
//
//  Created by lvchenzhu.1 on 2025/4/28.
//

#ifndef JDTGloablDefines_h
#define JDTGloablDefines_h

#define JDT_BLOCK_IF_EXISTS(block, ...) if (block) { block(__VA_ARGS__); }

typedef NSString * PaymentType NS_STRING_ENUM;     // 支付方式
static PaymentType const PaymentTypeOnline = @"1"; // 在线支付
static PaymentType const PaymentTypeCOD = @"2";    // 货到付款

typedef NS_ENUM(NSUInteger, StockState) {
    StockStateHasStock = 33,  // 有库存
    StockStateNoStock = 34,   // 无库存
};

typedef NS_ENUM(NSUInteger, SkuStatus) {
    SkuStatusOnShelves = 110,     // 上架中
    SkuStatusOutOfShelves = 105,  // 下架中
    SkuStatusInvalid = 115,       // 无效
};

typedef NS_ENUM(NSUInteger, ProductType) {
    ProductTypeGoods = 1, // 普通商品
    ProductTypeLoc = 2    // 本地生活商品
};

#endif /* JDTGloablDefines_h */
