//
//  PDSpecOptionCell.h
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/22.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, PDSpecOptionCellState) {
    PDSpecOptionCellStateNormal,          // 正常未选中
    PDSpecOptionCellStateSelected,        // 选中
    PDSpecOptionCellStateUnavailable,     // 不可选
    PDSpecOptionCellStateNoStockNormal,   // 无货未选中
    PDSpecOptionCellStateNoStockSelected, // 无货选中
};

@interface PDSpecSectionHeaderView : UICollectionReusableView

@property (nonatomic, strong) UILabel *sectionLabel;

@end

@interface PDSpecOptionCell : UICollectionViewCell

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, assign) PDSpecOptionCellState state;

@end

NS_ASSUME_NONNULL_END
