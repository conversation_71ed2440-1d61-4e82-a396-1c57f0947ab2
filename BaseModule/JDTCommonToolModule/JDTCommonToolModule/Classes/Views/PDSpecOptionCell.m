//
//  PDSpecOptionCell.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/22.
//

#import "PDSpecOptionCell.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>

@interface PDSpecSectionHeaderView ()

@end

@implementation PDSpecSectionHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self addSubview:self.sectionLabel];
    [self.sectionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

#pragma mark - Getter and Setter
- (UILabel *)sectionLabel {
    if (!_sectionLabel) {
        _sectionLabel = [[UILabel alloc] init];
        _sectionLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _sectionLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    }
    return _sectionLabel;
}

@end

@interface PDSpecOptionCell ()

@property (nonatomic, strong) UILabel *noStockLabel;

@property (nonatomic, strong) CAShapeLayer *dashBorderLayer;

@end

@implementation PDSpecOptionCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    self.layer.cornerRadius = 15;
    self.layer.masksToBounds = YES;
    
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.contentView addSubview:self.noStockLabel];
    [self.noStockLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView);
        make.top.equalTo(self.contentView.mas_top).offset(-8);
        make.size.mas_equalTo(CGSizeMake(26, 15));
    }];
    
    [self.contentView.layer addSublayer:self.dashBorderLayer];
    self.dashBorderLayer.hidden = YES;
    
    self.state = PDSpecOptionCellStateNormal;
}

- (void)setState:(PDSpecOptionCellState)state {
    _state = state;
    switch (self.state) {
        case PDSpecOptionCellStateNormal: {
            self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
            self.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"].CGColor;
            self.layer.borderWidth = 1;
            self.titleLabel.textColor = [UIColor blackColor];
            self.noStockLabel.hidden = YES;
            self.dashBorderLayer.hidden = YES;
            break;
        }
        case PDSpecOptionCellStateSelected: {
            self.backgroundColor = [UIColor whiteColor];
            self.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"].CGColor;
            self.layer.borderWidth = 1;
            self.titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
            self.noStockLabel.hidden = YES;
            self.dashBorderLayer.hidden = YES;
            break;
        }
        case PDSpecOptionCellStateUnavailable: {
            self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
            self.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"].CGColor;
            self.layer.borderWidth = 1;
            self.titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"];;
            self.noStockLabel.hidden = YES;
            self.dashBorderLayer.hidden = YES;
            break;
        }
        case PDSpecOptionCellStateNoStockNormal: {
            self.backgroundColor = [UIColor whiteColor];
            self.layer.borderColor = [UIColor whiteColor].CGColor;
            self.layer.borderWidth = 1;
            self.titleLabel.textColor = [UIColor redColor];
            self.noStockLabel.hidden = NO;
            self.dashBorderLayer.hidden = YES;
            break;
        }
        case PDSpecOptionCellStateNoStockSelected: {
            self.backgroundColor = [UIColor redColor];
            self.layer.borderWidth = 0;
            self.titleLabel.textColor = [UIColor redColor];
            self.noStockLabel.hidden = NO;
            self.dashBorderLayer.hidden = NO;
            break;
        }
        default:
            break;
    }
}

#pragma mark - Getter and Setter
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _titleLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)noStockLabel {
    if (!_noStockLabel) {
        _noStockLabel = [[UILabel alloc] init];
        _noStockLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _noStockLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
        _noStockLabel.textAlignment = NSTextAlignmentCenter;
        _noStockLabel.text = @"无货";
    }
    return _noStockLabel;
}

- (CAShapeLayer *)dashBorderLayer {
    if (!_dashBorderLayer) {
        _dashBorderLayer = [CAShapeLayer layer];
        _dashBorderLayer.strokeColor = [UIColor redColor].CGColor;
        _dashBorderLayer.lineWidth = 1;
        _dashBorderLayer.lineDashPattern = @[@4, @2];
    }
    return _dashBorderLayer;
}

@end
