//
//  AppDelegate.m
//  JDTECommerce
//
//  Created by lvchenzhu.1 on 2025/5/26.
//

#import "AppDelegate.h"
@import JDTInfrastructureModule;
@import QMapKit;

@interface AppDelegate ()

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
    NSString *token = [[NSUserDefaults standardUserDefaults] objectForKey:@"kUDTestToken"];
    if (token.length == 0) {
        token = @"";
    }
    // 初始化基本网络管理器
    [OOPNetworkManager sharedManager].baseURL = @"http://jnos-api.jdx-ka-v2-dev.building2-dev.jdt.com.cn";  // 测试环境
//    [OOPNetworkManager sharedManager].baseURL = @"https://jnos-api.bashiyunhui.com";  // 正式环境
    [OOPNetworkManager sharedManager].requestTimeout = 15.0;
    [[OOPNetworkManager sharedManager] setCommonHeaders:@{
        @"Content-Type": @"application/json",
        @"Accept": @"application/json",
        // 下面是 JNOS 网关需要的参数
        @"x-jnos-app-code": @"b2c.cbff", // 登录相关的传这个jnos.basic，其他传b2c.cbff
        @"x-jnos-tenant-code": @"1", // 租户编码，固定为 1
        @"x-jnos-lang": @"zh_CN", // 当前语言
        @"x-jnos-client-type": @"ios", // pc、h5、ios、android、wxapp
        @"x-jnos-token-c": token
//        @"x-jnos-token-c": @"aOUP3SoVVK3N.Y5YBVWzQicT.qwSfieUz7aKu09y5h1QY" // 登录接口返回的 token，未登录可传空 // 测试环境
//        @"x-jnos-token-c": @"X8kkWqXcbVez.unLSSLajjcG.T1oFM5ZlIxp8xXupNPw3" // 登录接口返回的 token，未登录可传空 // 正式环境
    }];
    [OOPAdvancedNetworkManager sharedAdvancedManager].baseURL = @"http://jnos-api.jdx-ka-v2-dev.building2-dev.jdt.com.cn";
//    [OOPAdvancedNetworkManager sharedAdvancedManager].baseURL = @"https://jnos-api.bashiyunhui.com";
    [OOPAdvancedNetworkManager sharedAdvancedManager].requestTimeout = 15.0;
    [[OOPAdvancedNetworkManager sharedAdvancedManager] setCommonHeaders:@{
        @"Content-Type": @"application/json",
        @"Accept": @"application/json",
        // 下面是 JNOS 网关需要的参数
        @"x-jnos-app-code": @"b2c.cbff", // 登录相关的传这个jnos.basic，其他传b2c.cbff
        @"x-jnos-tenant-code": @"1", // 租户编码，固定为 1
        @"x-jnos-lang": @"zh_CN", // 当前语言
        @"x-jnos-client-type": @"ios", // pc、h5、ios、android、wxapp
        @"x-jnos-token-c": token
//        @"x-jnos-token-c": @"aOUP3SoVVK3N.Y5YBVWzQicT.qwSfieUz7aKu09y5h1QY" // 登录接口返回的 token，未登录可传空 // 测试环境
//        @"x-jnos-token-c": @"X8kkWqXcbVez.unLSSLajjcG.T1oFM5ZlIxp8xXupNPw3" // 登录接口返回的 token，未登录可传空 // 正式环境
    }];
    [OOPAdvancedNetworkManager sharedAdvancedManager].enableResponseCache = YES;
    
    [QMapServices sharedServices].APIKey = @"BQZBZ-H63C3-6I23P-O7N6G-BRZ2S-3LBTG";
    [[QMapServices sharedServices] setPrivacyAgreement:YES];
    [QMSSearchServices sharedServices].apiKey = @"BQZBZ-H63C3-6I23P-O7N6G-BRZ2S-3LBTG";
    
    return YES;
}


#pragma mark - UISceneSession lifecycle


- (UISceneConfiguration *)application:(UIApplication *)application configurationForConnectingSceneSession:(UISceneSession *)connectingSceneSession options:(UISceneConnectionOptions *)options {
    // Called when a new scene session is being created.
    // Use this method to select a configuration to create the new scene with.
    return [[UISceneConfiguration alloc] initWithName:@"Default Configuration" sessionRole:connectingSceneSession.role];
}


- (void)application:(UIApplication *)application didDiscardSceneSessions:(NSSet<UISceneSession *> *)sceneSessions {
    // Called when the user discards a scene session.
    // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
    // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
}


@end
