<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="JDISVPDPromotionCouponFloorCell">
            <rect key="frame" x="0.0" y="0.0" width="375" height="42"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="375" height="42"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k8j-Xm-FO4">
                        <rect key="frame" x="18" y="18" width="20" height="18"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aqC-rn-MyW">
                                <rect key="frame" x="-5.5" y="1.5" width="31" height="15"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="2ll-xQ-uCR">
                                <rect key="frame" x="0.0" y="-1" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="4uc-Xk-Djp"/>
                                    <constraint firstAttribute="width" constant="20" id="GeW-n3-hjL"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="6Ac-lv-Ps7"/>
                            <constraint firstItem="aqC-rn-MyW" firstAttribute="centerY" secondItem="k8j-Xm-FO4" secondAttribute="centerY" id="6aD-12-srl"/>
                            <constraint firstItem="2ll-xQ-uCR" firstAttribute="centerY" secondItem="k8j-Xm-FO4" secondAttribute="centerY" id="GPp-iz-GHj"/>
                            <constraint firstItem="2ll-xQ-uCR" firstAttribute="leading" secondItem="k8j-Xm-FO4" secondAttribute="leading" id="PYZ-dd-KTd"/>
                            <constraint firstItem="aqC-rn-MyW" firstAttribute="centerX" secondItem="k8j-Xm-FO4" secondAttribute="centerX" id="WPF-Z0-OpA"/>
                            <constraint firstAttribute="height" constant="18" id="rCd-mC-QTA"/>
                        </constraints>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="6SZ-X8-qoi">
                        <rect key="frame" x="343" y="17" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="4Cc-80-gBq"/>
                            <constraint firstAttribute="height" constant="20" id="Pya-Qf-vSl"/>
                        </constraints>
                    </imageView>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="fG6-v1-UgM">
                        <rect key="frame" x="39" y="18" width="296" height="18"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="rcW-uD-Bvh"/>
                        </constraints>
                        <collectionViewLayout key="collectionViewLayout" id="lna-o8-tp8" customClass="JDISVPDPromotionCouponCollectionFlowLayout"/>
                    </collectionView>
                </subviews>
                <constraints>
                    <constraint firstItem="k8j-Xm-FO4" firstAttribute="bottom" secondItem="H2p-sc-9uM" secondAttribute="bottom" constant="-6" id="5Td-3N-XVS"/>
                    <constraint firstItem="fG6-v1-UgM" firstAttribute="leading" secondItem="k8j-Xm-FO4" secondAttribute="trailing" constant="1" id="B9w-ZP-o4v"/>
                    <constraint firstItem="k8j-Xm-FO4" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="BXB-bm-Vd6"/>
                    <constraint firstAttribute="trailing" secondItem="fG6-v1-UgM" secondAttribute="trailing" constant="40" id="H2T-vg-hZj"/>
                    <constraint firstAttribute="trailing" secondItem="6SZ-X8-qoi" secondAttribute="trailing" constant="12" id="ZkD-Fn-0fc"/>
                    <constraint firstItem="6SZ-X8-qoi" firstAttribute="centerY" secondItem="k8j-Xm-FO4" secondAttribute="centerY" id="kCm-0y-lAM"/>
                    <constraint firstItem="fG6-v1-UgM" firstAttribute="centerY" secondItem="k8j-Xm-FO4" secondAttribute="centerY" id="mT1-Nv-7Re"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="collectionView" destination="fG6-v1-UgM" id="jWp-Fe-Zap"/>
                <outlet property="iconImageView" destination="2ll-xQ-uCR" id="bTr-4m-np2"/>
                <outlet property="itemTitle" destination="aqC-rn-MyW" id="z5s-8b-LwT"/>
                <outlet property="rightArrow" destination="6SZ-X8-qoi" id="kqY-4E-aN0"/>
                <outlet property="titleViewWidth" destination="6Ac-lv-Ps7" id="tox-c5-t1G"/>
            </connections>
            <point key="canvasLocation" x="9.4202898550724647" y="81.696428571428569"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
