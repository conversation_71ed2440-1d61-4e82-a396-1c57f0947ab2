//
//  JDISVPDBottomBarView.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 吴滔 on 2022/2/16.
//

#import "JDISVBottomBarFloor.h"
#import "JDISVBottomBarMoule.h"
#import "JDISVPDDVerticalButton.h"
#import "JDISVPDAttributeCommonModel.h"

#import <JDISVTMUtilsModule/TMUtils.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVProductDetailTracker.h"
#import "JDISVPDPresaleDataModel.h"
#import "JDISVPDShareBuyDataModel.h"
#import "JDISVPDAttributeFloorMacro.h"

JDCDISVActionType const kJDISVPDDBottomBarShop = @"JDISVPDDBottomBarShop";
JDCDISVActionType const kJDISVPDDBottomBarChat = @"JDISVPDDBottomBarChat";
JDCDISVActionType const kJDISVPDDBottomBarCart = @"JDISVPDDBottomBarCart";

JDCDISVActionType const JDISVPDBottomBarLeftButtonAloneBuyAction = @"JDISVPDBottomBarLeftButtonAloneBuyAction"; // 拼团单独购买
JDCDISVActionType const JDISVPDBottomBarRightButtonCreateGroupAction = @"JDISVPDBottomBarRightButtonCreateGroupAction"; // 拼团发起拼团
JDCDISVActionType const JDISVPDBottomBarLeftButtonAction = @"JDISVPDBottomBarLeftButtonAction";
JDCDISVActionType const JDISVPDBottomBarRightButtonAction = @"JDISVPDBottomBarRightButtonAction";

JDCDISVActionType const JDISVPDBottomBarRightButtonPayDeposit = @"JDISVPDBottomBarRightButtonActionPayDeposit";//预售右按钮支付定金
JDCDISVActionType const kJDISVPDDBottomBarSlashProductBuyNow = @"JDISVPDDBottomBarSlashProductBuyNow";
JDCDISVActionType const kJDISVPDDBottomBarSlashProductDetailPage = @"JDISVPDDBottomBarSlashProductDetailPage";

JDCDISVActionType const kJDISVPDDBottomBarSlashProductBuyNowOpenPropertyVC = @"JDISVPDDBottomBarSlashProductBuyNowOpenPropertyVC";
JDCDISVActionType const kJDISVPDDBottomBarSlashProductDetailPageOpenPropertyVC = @"JDISVPDDBottomBarSlashProductDetailPageOpenPropertyVC";

@interface JDISVBottomBarFloor()

@property (nonatomic, assign) CGFloat rightStackSpacing;

@property (nonatomic, assign) BOOL dontAction;

@end

NSMutableDictionary* kaBottomGBlock;
#define KABottomModuleInitBlock @"KABottomModuleInitBlock"
#define KABottomViewWillShowBlock @"KABottomViewWillShowBlock"


@implementation JDISVBottomBarFloor
- (instancetype)init {
    self = [super init];
    if (self) {
        self.rightStackSpacing = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W18"];
        [self setup];
        self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    }
    return self;
}

- (void)dealloc {
    
    if ([NSThread isMainThread]) {
        @try {
            [[NSNotificationCenter defaultCenter] removeObserver:self];
        } @catch (NSException *exception) {
            NSLog(@"Failed to remove observer: %@", exception);
        }
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            @try {
                [[NSNotificationCenter defaultCenter] removeObserver:self];
            } @catch (NSException *exception) {
                NSLog(@"Failed to remove observer: %@", exception);
            }
        });
    }
}

- (void)setup {
    JDWeakSelf
//    self.backgroundColor = [UIColor whiteColor];
    
    // left stack view
    UIStackView *leftStackView = [[UIStackView alloc] init];
    leftStackView.axis = UILayoutConstraintAxisHorizontal;
    leftStackView.distribution = UIStackViewDistributionEqualSpacing;
    leftStackView.spacing = 0;
    leftStackView.alignment = UIStackViewAlignmentTop;
    [self addSubview:leftStackView];
    [leftStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(12);
        make.width.mas_equalTo(138);
        make.height.mas_equalTo(50);
        make.top.equalTo(self);
    }];
    self.leftStackView = leftStackView;
    
    // shop
    JDISVPDDVerticalButton *shop = [JDISVPDDVerticalButton buttonWithType:(UIButtonTypeCustom)];
    [[shop rac_signalForControlEvents:(UIControlEventTouchUpInside)] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf

        JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVPDDBottomBarShop];
        [self isv_sendAction:action];
    }];
    UIImage *shopImg = [UIImage ka_iconWithName:JDIF_ICON_SHOP_LINE imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    [shop setImage:shopImg forState:(UIControlStateNormal)];
    [shop setTitle:ProductDetailSDKL(@"base_pd_btn_shop") forState:(UIControlStateNormal)];
    [shop setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:(UIControlStateNormal)];
    shop.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
    shop.padding = 4;
    shop.imageEdgeInsetTop = 6.5;
    [shop mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.width.mas_equalTo(46);
    }];
    [leftStackView addArrangedSubview:shop];
    self.shopButton = shop;
    
    // chat
    JDISVPDDVerticalButton *chat = [JDISVPDDVerticalButton buttonWithType:(UIButtonTypeCustom)];
    [[chat rac_signalForControlEvents:(UIControlEventTouchUpInside)] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf
        JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVPDDBottomBarChat];
        [self isv_sendAction:action];
    }];
    UIImage *img = [UIImage ka_iconWithName:JDIF_ICON_CUSTOMERSERVICE_LINE imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
        img = [img JDCDRTL];
    }
    [chat setImage:img forState:(UIControlStateNormal)];
    [chat setTitle:ProductDetailSDKL(@"base_pd_btn_chat") forState:(UIControlStateNormal)];
    [chat setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:(UIControlStateNormal)];
    chat.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
    chat.padding = 4;
    chat.imageEdgeInsetTop = 6.5;
    [chat mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.width.mas_equalTo(46);
    }];
    [leftStackView addArrangedSubview:chat];
    self.chatButton = chat;
    
    // cart
    JDISVPDDVerticalButton *cart = [JDISVPDDVerticalButton buttonWithType:(UIButtonTypeCustom)];
    cart.productDetailBadge = YES;
    cart.badgeBackgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    [[cart rac_signalForControlEvents:(UIControlEventTouchUpInside)] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf
        JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVPDDBottomBarCart];
        [self isv_sendAction:action];
    }];
    UIImage *img2 = [UIImage ka_iconWithName:JDIF_ICON_CART_LINE imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
        img2 = [img2 JDCDRTL];
    }
    [cart setImage:img2 forState:(UIControlStateNormal)];
    [cart setTitle:ProductDetailSDKL(@"base_pd_btn_cart") forState:(UIControlStateNormal)];
    [cart setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"] forState:(UIControlStateNormal)];
    cart.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
    cart.padding = 4;
    cart.imageEdgeInsetTop = 6.5;
    [cart mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(50);
        make.width.mas_equalTo(46);
    }];
    [leftStackView addArrangedSubview:cart];
    self.cartButton = cart;
    
    // space
    UIView *spaceView = [UIView new];
    spaceView.backgroundColor = [UIColor clearColor];
    [leftStackView addArrangedSubview:spaceView];
    
    // line
    UIView *line = [UIView new];
    line.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"000100" alpha:0.08];
    [self addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(1.0 / [UIScreen mainScreen].scale);
        make.leading.trailing.top.equalTo(self);
    }];
    
    //添加购物车数量改变通知
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShoppingCart];
    NSString *router = [NSString stringWithFormat:@"router://%@/getCartCountChangeNotificationName",moduleName];
    NSString *cartCountChangeNotificationName = [JDRouter openURL:router arg:nil error:nil completion:nil];
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(cartCountChange:) name:cartCountChangeNotificationName object:nil];
    __weak typeof(self) weakSelf = self;
    [[NSNotificationCenter defaultCenter] addObserverForName:cartCountChangeNotificationName
                                                      object:nil
                                                       queue:[NSOperationQueue mainQueue]
                                                  usingBlock:^(NSNotification * _Nonnull notification) {
        typeof(self) strongSelf = weakSelf;
        if (!strongSelf) return;
        // 处理购物车数量改变通知
        NSDictionary *dict = notification.userInfo;
        NSNumber *count = dict[@"count"];
        [strongSelf.cartButton setBadge:[count unsignedIntegerValue]];
    }];

    
    [self refreshCartCount];
}

- (void)configRightBtnByPresale:(UIStackView *)rightStackView {
    
    JDWeakSelf
    UIImage *disableBg = [UIImage jdcd_imageWithColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    
    // 预售： “支付定金” 或 “等待抢购” 按钮
    UIButton *rightButton = [[JDISVPlatformService sharedService] getProductDetailRedButton];
    [[rightButton rac_signalForControlEvents:(UIControlEventTouchUpInside)] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf
        
        JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVPDBottomBarRightButtonPayDeposit broadcastAction:YES];
        [self isv_sendAction:action];
    }];
    
    // 活动开始，支付定金
    NSString *yuShouDeposit = @"";
    if (self.module.presale.presale.presaleCount.integerValue > 100) {
        //X超过100件，再展示”X件 已定“
        NSString *presaleCountStr = [self handleNumberFormatter:self.module.presale.presale.presaleCount];
        yuShouDeposit = [NSString stringWithFormat:ProductDetailSDKL(@"product_detail_attr_pay_deposit_and_count"),presaleCountStr];
        rightButton.titleLabel.numberOfLines = 2;
    }else{
        yuShouDeposit = ProductDetailSDKL(@"product_detail_pay_deposit");
    }
    rightButton.titleLabel.textAlignment = NSTextAlignmentCenter;

    [rightButton setTitle:yuShouDeposit forState:(UIControlStateNormal)];
    [rightButton setTitleColor:[UIColor whiteColor] forState:(UIControlStateNormal)];
    [rightButton setBackgroundImage:[self redGradient] forState:(UIControlStateNormal)];
    [rightButton setBackgroundImage:[self highlightImageWithOriginImage:[self redGradient]] forState:(UIControlStateHighlighted)];
    [rightButton setBackgroundImage:disableBg forState:(UIControlStateDisabled)];
    rightButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightMedium)];
    rightButton.layer.masksToBounds = YES;
    rightButton.layer.cornerRadius = 20.f;
    [rightStackView addArrangedSubview:rightButton];
    
    if (self.module.presale.presale.presaleStatus.integerValue == 0) {
        // 预售未开始，置灰按钮，显示等待抢购
        [rightButton setTitle:ProductDetailSDKL(@"base_pd_btn_wait_sale") forState:(UIControlStateNormal)];
        rightButton.enabled = NO;
    }
    if (self.module.isStockout) {
        [rightButton setTitle:ProductDetailSDKL(@"base_pd_btn_out_of_stock") forState:(UIControlStateNormal)];
        rightButton.enabled = NO;
    }
    self.rightButton = rightButton;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.module = floorModel;
    [self config:self.module];
    
    void(^blockView)(JDISVBottomBarFloor*) =  kaBottomGBlock[KABottomViewWillShowBlock];
    if(blockView){
        blockView(self);
    }
}

- (void)config:(__kindof JDISVPDBottomBarModule *)floorModel {
    
    // right stack view
    UIView *v = [self viewWithTag:100001];
    if (v) {
        [v removeFromSuperview];
    }
    UIStackView *rightStackView = [[UIStackView alloc] init];
    rightStackView.tag = 100001;
    rightStackView.spacing = self.rightStackSpacing;
    rightStackView.axis = UILayoutConstraintAxisHorizontal;
    rightStackView.distribution = UIStackViewDistributionFillEqually;
    [self addSubview:rightStackView];
    [rightStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.leftStackView.mas_trailing).offset(12);
        make.trailing.mas_equalTo(-12);
        make.height.mas_equalTo(40);
        make.centerY.equalTo(self.leftStackView);
    }];
    
    if (self.module.common.promoType == JDISVProductPromoTypePresale) {
        // 预售模式
        [self configRightBtnByPresale:rightStackView];
        
    } else {
        // 正常模式，拼团模式等
        JDWeakSelf
        UIImage *disableBg = [UIImage jdcd_imageWithColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"]];
        // left button
        UIButton *leftButton = [[JDISVPlatformService sharedService] getDefaultButtonWithTitle:ProductDetailSDKL(@"pd_base_floor_dialog_product_attr_btn_add_to_cart") frame:CGRectZero buttonType:JDISVButtonOrangeGridentType];
        [[leftButton rac_signalForControlEvents:(UIControlEventTouchUpInside)] subscribeNext:^(__kindof UIControl * _Nullable x) {
            JDStrongSelf
            
            if (self.module.common.promoType == JDISVProductPromoTypeGroupBuy) {
                // 拼团左侧按钮，单独购买
                JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVPDBottomBarLeftButtonAloneBuyAction broadcastAction:YES];
                [self isv_sendAction:action];
                
            } else if (self.module.common.promoType == JDISVProductPromoTypeShareSlash) {
                // 砍价左侧按钮，发起砍价 或 继续砍价
                JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVPDDBottomBarSlashProductDetailPageOpenPropertyVC broadcastAction:YES];
                [self isv_sendAction:action];
                
            } else {
                // 正常模式
                JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVPDBottomBarLeftButtonAction broadcastAction:YES];
                [self isv_sendAction:action];
            }
            
            // 点击埋点
            NSDictionary* param = @{@"page_id":@"Productdetail_MainPage",
                                    @"skuId": self.module.common.skuId ?: @"",
                                    @"sku_id": self.module.common.skuId ?: @""
            };
            if (KSAAPP) {
                param = @{@"page_id":@"Productdetail_MainPage",
                          @"skuId": self.module.common.skuId ?: @""};
            }
            
            [JDISVProductDetailTracker Event:@"Productdetail_Addtocart"
                             param:param];
        }];
        [leftButton setTitle:ProductDetailSDKL(@"pd_base_floor_dialog_product_attr_btn_add_to_cart") forState:(UIControlStateNormal)];
        [leftButton setTitleColor:[UIColor whiteColor] forState:(UIControlStateNormal)];
//        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
//        [leftButton setBackgroundImage:[self orangeGradient] forState:(UIControlStateNormal)];
//        [leftButton setBackgroundImage:[self highlightImageWithOriginImage:[self orangeGradient]] forState:(UIControlStateHighlighted)];
//        [leftButton setBackgroundImage:disableBg forState:(UIControlStateDisabled)];
        [leftButton renderB2];
        leftButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightMedium)];
        leftButton.titleLabel.textAlignment = NSTextAlignmentCenter;
        leftButton.layer.masksToBounds = YES;
        [rightStackView addArrangedSubview:leftButton];
        self.leftButton = leftButton;
        
        // right button
//        UIButton *rightButton = [[JDISVPlatformService sharedService] getProductDetailRedButton];
        UIButton *rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [[rightButton rac_signalForControlEvents:(UIControlEventTouchUpInside)] subscribeNext:^(__kindof UIControl * _Nullable x) {
            JDStrongSelf
            if (self.module.common.promoType == JDISVProductPromoTypeShareSlash) {
                // 砍价立即购买
                JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVPDDBottomBarSlashProductBuyNowOpenPropertyVC broadcastAction:YES];
                [self isv_sendAction:action];

            } else if (self.module.common.promoType == JDISVProductPromoTypeGroupBuy) {
                // 拼团右侧按钮，发起拼团
                JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVPDBottomBarRightButtonCreateGroupAction broadcastAction:YES];
                [self isv_sendAction:action];
                
            }  else {
                // 普通立即购买
                JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVPDBottomBarRightButtonAction broadcastAction:YES];
                [self isv_sendAction:action];
                
            }
            
            // 点击埋点
            NSDictionary* param = @{@"page_id":@"Productdetail_MainPage",
                                    @"skuId": self.module.common.skuId ?: @"",
                                    @"sku_id": self.module.common.skuId ?: @""
            };
            if (KSAAPP) {
                param = @{@"page_id":@"Productdetail_MainPage",
                          @"skuId": self.module.common.skuId ?: @""};
            }
            [JDISVProductDetailTracker Event:@"Productdetail_GotoBuyNow"
                             param:param];
        }];
        [rightButton setTitle:ProductDetailSDKL(@"pd_base_floor_dialog_product_attr_btn_buy_now") forState:(UIControlStateNormal)];
        [rightButton setTitleColor:[UIColor whiteColor] forState:(UIControlStateNormal)];
//        [rightButton setBackgroundImage:[self redGradient] forState:(UIControlStateNormal)];
//        [rightButton setBackgroundImage:[self highlightImageWithOriginImage:[self redGradient]] forState:(UIControlStateHighlighted)];
//        [rightButton setBackgroundImage:disableBg forState:(UIControlStateDisabled)];
        [rightButton setBackgroundColor:[UIColor jdcd_colorWithHexColorString:@"#24334A"]];
        rightButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightMedium)];
        rightButton.titleLabel.textAlignment = NSTextAlignmentCenter;
        rightButton.layer.cornerRadius = 20;
        rightButton.layer.masksToBounds = YES;
        [rightStackView addArrangedSubview:rightButton];
        self.rightButton = rightButton;
        
    }
    
    // 左侧按钮配置
    
    if (self.module.hideChat) {
        self.chatButton.hidden = YES;
    } else {
        self.chatButton.hidden = NO;
    }
    
    if (self.module.shopFeature && self.module.showShop) {
        self.shopButton.hidden = NO;
    } else {
        self.shopButton.hidden = YES;
    }
    
    self.leftButton.titleLabel.numberOfLines = 2;
    
    if (self.module.leftAttriTitleStr){
        [self.leftButton setAttributedTitle:self.module.leftAttriTitleStr forState:(UIControlStateNormal)];
        [self.leftButton setAttributedTitle:self.module.leftAttriTitleStr forState:(UIControlStateHighlighted)];
    }else{
        [self.leftButton setTitle:self.module.leftTitle forState:(UIControlStateNormal)];
        [self.leftButton setTitle:self.module.leftTitle forState:(UIControlStateHighlighted)];
    }
    
    self.rightButton.titleLabel.numberOfLines = 2;
    if (self.module.rightAttriTitleStr) {
        [self.rightButton setAttributedTitle:self.module.rightAttriTitleStr forState:(UIControlStateNormal)];
        [self.rightButton setAttributedTitle:self.module.rightAttriTitleStr forState:(UIControlStateHighlighted)];
        [self.rightButton setAttributedTitle:self.module.rightAttriTitleStr forState:(UIControlStateDisabled)];
    }else{
        [self.rightButton setTitle:self.module.rightTitle forState:(UIControlStateNormal)];
        [self.rightButton setTitle:self.module.rightTitle forState:(UIControlStateHighlighted)];
        [self.rightButton setTitle:self.module.rightTitle forState:(UIControlStateDisabled)];
    }
    
    CGFloat rightStackWidth = [UIScreen mainScreen].bounds.size.width - 12 * 3 - 138;
    if (self.module.hideLeftButton) {
        self.leftButton.hidden = YES;
        
        CGFloat r50 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R50"];
        [self jdisv_CutView:self.rightButton leftCorner:r50 rightCorner:r50 rect:CGRectMake(0, 0, rightStackWidth, 40)];
    } else {
        self.leftButton.hidden = NO;
        CGFloat buttonWidth = (rightStackWidth - _rightStackSpacing)/2;

        CGFloat r53 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R53"];
        CGFloat r54 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R54"];
        [self jdisv_CutView:self.leftButton leftCorner:r53 rightCorner:r54 rect:CGRectMake(0, 0, buttonWidth, 40)];

        CGFloat r55 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R55"];
        CGFloat r56 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R56"];
        [self jdisv_CutView:self.rightButton leftCorner:r55 rightCorner:r56 rect:CGRectMake(0, 0, buttonWidth, 40)];
    }
    
    if (self.module.rightButtonDisable) {
        self.rightButton.enabled = NO;
    } else {
        self.rightButton.enabled = YES;
    }
    
    BQPDPBottomBarButtonColorType type = self.module.rightButtonColorType;
    UIButton *rightButton = self.rightButton;
    if (type == BQPDPBottomBarButtonColorTypeRed) {
        rightButton.enabled = YES;
    } else if (type == BQPDPBottomBarButtonColorTypeGray) {
        rightButton.enabled = NO;
    }
}

-(void)setModule:(JDISVPDBottomBarModule *)module{
    void(^blockModule)(JDISVPDBottomBarModule*) =  kaBottomGBlock[KABottomModuleInitBlock];
    if(blockModule){
        blockModule(module);
    }
    _module = module;
}

- (void)broadcastAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVPDAddCartSuccessAction"]) {
        [self refreshCartCount];
    } else if ([action.actionType isEqualToString:@"JDISVPDShopOpenIMAction"]) {
        [self openImController:controller action:action];
    } else if ([action.actionType isEqualToString:@"JDISVPDShareSlashAction"]) {
        [self handleSlashProductAction:action];
    } else if ([action.actionType isEqualToString:JDISVPDBottomBarLeftButtonAction]) {
        if (self.module.common.promoType == JDISVProductPromoTypeShareSlash && self.module.common.slashStage == JDISVShareSlashStageContinue) {
            // 砍价
            [self slashProductDetailPage:NO];
        }
    }
}

//接收购物车数量改变通知
//- (void)cartCountChange:(NSNotification *)notification {
//    NSDictionary *dict = notification.userInfo;
//    NSNumber *count = dict[@"count"];
//    [self.cartButton setBadge:[count unsignedIntegerValue]];
//}

- (void)refreshCartCount {
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)];
    NSString *router = [NSString stringWithFormat:@"router://%@/getCartCount", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(NSDictionary *  _Nullable object) {
        NSString *code = [object objectForKey:@"code"];
        if ([code isEqualToString:@"0"]) {
            NSDictionary *data = [object objectForKey:@"data"];
            NSNumber *count = [data objectForKey:@"count"];
            [self.cartButton setBadge:[count unsignedIntegerValue]];
        }
    }];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if(self.dontAction)
        return NO;
    
    if ([action.actionType isEqualToString:kJDISVPDDBottomBarShop]) {
        [self  pushShopController:controller action:action];
    } else if ([action.actionType isEqualToString:kJDISVPDDBottomBarChat]) {
        [self openImController:controller action:action];
    }else if ([action.actionType isEqualToString:kJDISVPDDBottomBarCart]){
        [self pushShoppingCartController:controller action:action];
    }else if([action.actionType isEqualToString:kJDISVPDDBottomBarSlashProductBuyNow]){
        [self pushSettlementController:controller action:action];
    }else if([action.actionType isEqualToString:kJDISVPDDBottomBarSlashProductDetailPage]){
        [self pushSlashProductDetailController:controller action:action];
    }
    return YES;
}

#pragma mark - event

- (void)handleSlashProductAction:(JDCDISVAction *)action {
    switch (self.module.common.slashStage) {
        case JDISVShareSlashStageLaunch:
            // 发起砍价
            [self slashProductLaunchTask];
            break;
        case JDISVShareSlashStageContinue:
            // 继续砍价
            [self slashProductDetailPage:NO];
            break;
        case JDISVShareSlashStageBuy:
            // 完成砍价
            [self slashProductBuyNow:action];
            break;
            
        default:
            break;
    }
}

- (void)slashProductLaunchTask {
    @weakify(self)
    [[self.module launchShareSlash] subscribeNext:^(id  _Nullable x) {
        RACTupleUnpack(NSNumber *rt, NSString *msg, NSDictionary *data) = x;
        if ([rt boolValue]) {
            @strongify(self)
            [self slashProductDetailPage:YES data:data];
        } else {
            [KAAlert alert]
            .config
            .renderW3(msg)
            .ka_addAction(ProductDetailSDKL(@"base_pd_user_group_buy_limit_ok"), ^(UIButton * _Nonnull button) {
                [button renderB3];
            }, ^{
                
            })
            .jdcd_show();
        }
    }];
}

- (void)slashProductDetailPage:(BOOL)firstblood {
    [self slashProductDetailPage:firstblood data:nil];
}

- (void)slashProductDetailPage:(BOOL)firstblood data:(NSDictionary *)data {
    NSString *url = self.module.slashUrl;
    if (firstblood) {
        NSNumber *assistValue = data[@"assistValue"];
        if (assistValue) {
            url = [NSString stringWithFormat:@"%@&firstblood=%@",url,assistValue];
        }
    }
    JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVPDDBottomBarSlashProductDetailPage];
    action.value = url;
    [self isv_sendAction:action];
}

- (void)slashProductBuyNow:(JDCDISVAction *)action{
    JDCDISVAction *buyAction = [JDCDISVAction actionWithType:kJDISVPDDBottomBarSlashProductBuyNow];
    buyAction.extendParams = action.extendParams.copy;
    [self isv_sendAction:buyAction];
}

- (void)pushShopController:(UIViewController*)controller action:(JDCDISVAction *)action{
    
    NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShop];
    
    NSString *shopId = self.module.venderId;
    NSString *shopName = self.module.common.shopModule[@"C-M#viewShopItem&basic"][@"shopName"];
    NSString *shopUrl = self.module.common.shopModule[@"C-M#viewShopItem&basic"][@"shopUrl"];
    NSNumber *followState = self.module.common.shopModule[@"C-M#viewShopItem&followInfo"][@"followState"];
    
    NSDictionary *arg = @{@"pageCode": @"1",
                          @"shopId": shopId ?: @"100021",
                          @"shopName": shopName ?: @"",
                          @"shopUrl": shopUrl ?: @"",
                          @"followState": followState ?: @"",
    };
    
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/shopController", module] arg:arg error:nil completion:^(UIViewController *vc) {
        [controller.navigationController pushViewController:vc animated:YES];
    }];
}

- (void)pushShoppingCartController:(UIViewController*)controller action:(JDCDISVAction *)action{
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)];
    NSString *router = [NSString stringWithFormat:@"router://%@/shoppingCartController", moduleName];
    UIViewController *vc =  [JDRouter openURL:router arg:nil error:nil completion:nil];
    [controller.navigationController pushViewController:vc animated:YES];
};

- (void)openImController:(UIViewController*)controller action:(JDCDISVAction *)action{
    NSDictionary* param = @{
            @"controller" : controller,
            @"pid" : self.module.common.skuId ?: @"",
            @"price" : [NSString stringWithFormat:@"%@%@",PRICETAG,self.module.common.priceModule[@"C-M#priceViewItem&priceInfo"][@"mainPrice"] ?: @""],
            @"title" : self.module.titleName ?: @"",
            @"venderId" : self.module.common.venderId ?: @"",
            @"imgUrl": [PlatformService getCompleteImageUrl:self.module.imgUrl ?: @"" moduleType:JDISVModuleTypeProductDetail]
        };
    
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeIM)];
    NSString *router = [NSString stringWithFormat:@"router://%@/openProductIMController", moduleName];
    [JDRouter openURL:router arg:param error:nil completion:nil];
};

- (void)pushSettlementController:(UIViewController*)controller action:(JDCDISVAction *)action{
    JDWeakSelf
    
    if (self.module.jhGroupFeature) {
        // 先判断商品是否限购，再看能否跳结算页
        [[self jhGroupCanBuy] subscribeNext:^(NSNumber * x) {
            if(x.boolValue == YES) {
                JDStrongSelf
                [self buyNowRouter:controller action:action];
            }
            
        } error:^(NSError * _Nullable error)  {
            [KAAlert alert].config
            .renderW4(ProductDetailSDKL(@"base_pd_user_group_buy_limit_title"), error.localizedDescription)
            .addLineAction(ProductDetailSDKL(@"base_pd_user_group_buy_limit_ok"), ^{
               
            })
            .alertShow();
        }];
        
    } else {
        [self buyNowRouter:controller action:action];
    }
    
    
};


-(RACSignal*)jhGroupCanBuy{
//    if(!self.featureJHGroup){
//        return [[RACSignal return:@(YES)] deliverOnMainThread];
//    }

    return [[RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        NSDictionary* param = @{@"reqId":[NSUUID UUID].UUIDString,
                                @"skuIds":@[self.module.skuId ?:@""],
                                @"token":[PlatformService getUserA2]?:@""
        };
        
        [PlatformService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"query_order_check" version:@"1.0" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            BOOL suc = [responseObject[@"success"] boolValue];
            if(!suc){
                [subscriber sendError:[NSError errorWithDomain:@"JNOS" code:-1 userInfo:@{NSLocalizedDescriptionKey:responseObject[@"message"]?:ProductDetailSDKL(@"base_pd_buy_now_add_to_cart_message_failed")}]];
            }else{
                NSNumber* offValue = responseObject[@"data"][@"off"];
                if(offValue.boolValue){
                    [subscriber sendNext:@(YES)];
                    [subscriber sendCompleted];
                }else{
                    [subscriber sendError:[NSError errorWithDomain:@"JNOS" code:-1 userInfo:@{NSLocalizedDescriptionKey:responseObject[@"data"][@"message"]?:ProductDetailSDKL(@"base_pd_buy_now_add_to_cart_message_failed")}]];
                }
            }
        }];
        return nil;
    }] deliverOnMainThread];
}

- (void)buyNowRouter:(UIViewController *)controller action:(JDCDISVAction *)action {
    
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(NSNumber* success) {
        
        if(success.boolValue){
            
            NSString *skuCount = @"";
            NSString *skuId = @"";
            if ([self.module.skuId jdcd_validateString]) {
                skuId = self.module.skuId;
            }
            skuCount = [action.extendParams jdcd_getStringElementForKey:@"selectedCount"];
            NSDictionary *arg = @{@"sourceType":@(1), @"skuId": skuId, @"skuCount": skuCount};
            [PlatformService showLoadingInView:controller.view];
            
            if (self.module.common.promoType == JDISVProductPromoTypePresale) {
                [arg setValue:@"2" forKey:@"payTypeForPresale"];
                [arg setValue:@(YES) forKey:@"isPresale"];
            } else if (self.module.common.promoType == JDISVProductPromoTypeGroupBuy) {
                NSDictionary *joinBuyModule = [self.module.common.allfloors tm_safeObjectForKey:@"joinBuyModule"];
                JDISVPDShareBuyDataModel *shareBuy = [JDISVPDShareBuyDataModel yy_modelWithDictionary:joinBuyModule];
                NSString *taskId = [NSString stringWithFormat:@"%@p%@",shareBuy.activityId, @"0"];
                [arg setValue:taskId forKey:@"taskId"];
                [arg setValue:@(YES) forKey:@"ptFlag"];
            }
            
            if (self.module.common.commonBasic.isLoc) {
                [arg setValue:@(YES) forKey:@"isLoc"];
            }
            
            NSString *settlementRouter = [NSString stringWithFormat:@"router://%@/settlementController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeSettlement)]];
            
            [JDRouter openURL:settlementRouter arg:arg error:nil completion:^(id  _Nullable object) {
               
                [PlatformService dismissInView:controller.view];
                NSNumber *code = [object objectForKey:@"code"];
                if ([code integerValue] == 0) {
                    // 成功
                    UIViewController *vc = [object objectForKey:@"result"];
                    if (vc) {
                        [controller.navigationController pushViewController:vc animated:YES];
                    }
                } else {
                    // 失败
                    NSString *message = [object objectForKey:@"result"];
                    if ([message jdcd_validateString]) {
                        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:message];
                    } else {
                        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:ProductDetailSDKL(@"pd_base_system_error")];
                    }
                }
            }];
        }
    }];
}

- (void)pushSlashProductDetailController:(UIViewController*)controller action:(JDCDISVAction *)action{
    
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(NSNumber* success) {
        
        if(success.boolValue){
            //  do action after loinged;
            NSString *url = action.value;
            if (url) {
                NSDictionary* param = @{@"text_url":url};
                NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeWebView)];
                NSString *router = [NSString stringWithFormat:@"router://%@/getWebViewController", moduleName];
                [JDRouter openURL:router arg:param error:nil completion:^(UIViewController*  _Nullable webView) {
                    [controller.navigationController pushViewController:webView animated:YES];
                }];
            }
        }
    
    }];
}


#pragma mark - color

- (UIImage *)orangeGradient {
    UIColor *s = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-b"];
    UIColor *e = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-c"];
    if (self.module.common.promoType == JDISVProductPromoTypeShareSlash || self.module.common.promoType == JDISVProductPromoTypeGroupBuy || self.module.common.promoType == JDISVProductPromoTypeSecKill || self.module.common.promoType == JDISVProductPromoTypePresale) {
        s = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C31"];
        e = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C34"];
    }
    UIImage *img = [UIImage jdcd_getGradientImageFromColors:@[s, e] gradientType:GradientFromLeftToRight imgSize:CGSizeMake(2, 2)];
    return img;
}

- (UIImage *)redGradient {
    UIColor *s = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9-1"];
    UIColor *e = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9-2"];
    if (self.module.common.promoType == JDISVProductPromoTypeShareSlash || self.module.common.promoType == JDISVProductPromoTypeGroupBuy || self.module.common.promoType == JDISVProductPromoTypeSecKill || self.module.common.promoType == JDISVProductPromoTypePresale) {
        s = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C35"];
        e = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C36"];
    }
    UIImage *img = [UIImage jdcd_getGradientImageFromColors:@[s, e] gradientType:GradientFromLeftToRight imgSize:CGSizeMake(2, 2)];
    return img;
}

// 4*4
- (UIImage *)highlightImageWithOriginImage:(UIImage *)image {
    UIImage *image1 = image;
    UIImage *image2 = [UIImage jdcd_imageWithColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.1]];
    UIGraphicsBeginImageContext(image1.size);
    UIGraphicsBeginImageContextWithOptions(image.size, NO, [UIScreen mainScreen].scale);
    [image1 drawInRect:CGRectMake(0, 0, 2, 2)];
    [image2 drawInRect:CGRectMake(0, 0, 2, 2)];
    UIImage *resultingImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resultingImage;
}


- (void)jdisv_CutView:(UIView*)view
           leftCorner:(CGFloat)leftCorner
          rightCorner:(CGFloat)rightCorner
                 rect:(CGRect)rect {
    BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    CGFloat width = rect.size.width / 2.0;
    CGFloat height = rect.size.height;
    UIBezierPath *bezier = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(0, 0, width, height) byRoundingCorners:(UIRectCornerTopLeft | UIRectCornerBottomLeft) cornerRadii:CGSizeMake(isRTL? rightCorner : leftCorner, isRTL? rightCorner : leftCorner)];
    [bezier appendPath:[UIBezierPath bezierPathWithRoundedRect:CGRectMake(width, 0, width, height) byRoundingCorners:(UIRectCornerTopRight | UIRectCornerBottomRight) cornerRadii:CGSizeMake(isRTL ? leftCorner : rightCorner, isRTL ? leftCorner : rightCorner)]];
    CAShapeLayer *shapeLayer = [CAShapeLayer new];
    shapeLayer.frame = rect;
    shapeLayer.path = bezier.CGPath;
    view.layer.mask = shapeLayer;
}

+(void)moduleFinshBlock:(void(^)(JDISVPDBottomBarModule*))moduleBlock{
    if(!kaBottomGBlock){
        kaBottomGBlock = [NSMutableDictionary dictionaryWithCapacity:2];
    }
    if(moduleBlock){
        kaBottomGBlock[KABottomModuleInitBlock] = moduleBlock;
    }
}
+(void)viewWillShowBlock:(void(^)(JDISVBottomBarFloor*))viewBlock{
    if(!kaBottomGBlock){
        kaBottomGBlock = [NSMutableDictionary dictionaryWithCapacity:2];
    }
    if(viewBlock){
        kaBottomGBlock[KABottomViewWillShowBlock] = viewBlock;
    }
}

//数值三位逗号隔开
- (NSString *)handleNumberFormatter:(NSNumber *)count{
    NSString *countStr = [NSString stringWithFormat:@"%@",count];
    //最多展示9位
    if (countStr.length > 9) {
        countStr = [countStr substringToIndex:9];//截取掉下标之前的字符串
    }
    NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
    formatter.positiveFormat = @",###";
    NSNumber *number = [NSNumber numberWithDouble:countStr.doubleValue];
    NSString *result = [formatter stringFromNumber:number];
    return result ? : @"";
}
@end

