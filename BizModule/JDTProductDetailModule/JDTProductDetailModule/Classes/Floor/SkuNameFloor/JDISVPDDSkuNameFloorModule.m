//
//  JDISVPDDSkuNameFloorModule.m
//  JDISVProductDetailSDKModule
//
//  Created by 张令浩 on 2022/2/17.
//

#import "JDISVPDDSkuNameFloorModule.h"

#import "JDISVPDAttributeFloorMacro.h"

#import "JDISVPDAttributeCommonModel.h"

#import "JDISVPDDSkuNameFloorCell.h"

#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import "PDTagModel.h"

@import JDTCommonToolModule;

JDISVRegisterFloorModule(KaProductSkuNameFloor, JDISVPDDSkuNameFloorModule)

@interface JDISVPDDSkuNameFloorModule ()

@end

@implementation JDISVPDDSkuNameFloorModule
- (UINib *)tableViewFloorNib {
    return [UINib nibWithNibName:@"JDISVPDDSkuNameFloorCell" bundle:[NSBundle isv_pd_currentBundle]];
}

- (CGFloat)floorHeight {
    return _floorHeight;
}

-(BOOL)ignoreCorner{
    return KSAAPP ? YES : NO;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    PDProductBaseModel *baseModel = [PDProductBaseModel yy_modelWithDictionary:data[@"data"][@"productBaseModule"]];
    
//    self.skuNameLabelAttrStr = [[NSAttributedString alloc] initWithString:model.skuName attributes:@{NSForegroundColorAttributeName: [UIColor blackColor], NSFontAttributeName: [UIFont systemFontOfSize:16]}];
    
    PDTagModel *tagModel = [PDTagModel yy_modelWithDictionary:data[@"data"][@"tagModule"]];
    NSMutableArray *tagsMArr = [NSMutableArray array];
    for (PDTagItemModel *tagItem in tagModel.productTagPartList) {
        [tagsMArr addObject:tagItem.text];
    }
    self.tags = [tagsMArr copy];
    
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle defaultParagraphStyle] mutableCopy];
//            style.lineSpacing = 6 - [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"].lineHeight + [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"].pointSize;
    style.maximumLineHeight = 22.5;
    style.minimumLineHeight = 22.5;
    
    NSMutableAttributedString *title = [[NSMutableAttributedString alloc] init];
    
    // 本地生活 到店打标
    CGFloat tagW = 0;
    for (int i=0;i<self.tags.count;i++) {
        NSString *tag = self.tags[i];
        CGFloat h = 16.f;
        CGFloat w = [tag jdcd_getStringSize:[UIFont systemFontOfSize:12.f] constraintsSize:CGSizeMake(CGFLOAT_MAX, 16)].width + 3 + 3;
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, w, h)];
        label.text = tag;
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:12.f];
        label.textColor = [UIColor whiteColor];
        label.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d"];
        label.layer.cornerRadius = 4.f;
        label.layer.masksToBounds = YES;
        attachment.image = [self getImageFromView:label];
        
        attachment.bounds = CGRectMake(0, -2, w, h);
        tagW += w;
        NSAttributedString *tagImg = [NSAttributedString attributedStringWithAttachment:attachment];
//                [tagImg addAttribute:NSBaselineOffsetAttributeName value:@(-1) range:NSMakeRange(0, tagImg.length)];
        [title appendAttributedString:tagImg];
        
        NSAttributedString *space = [[NSAttributedString alloc] initWithString:@" " attributes:@{NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)], NSParagraphStyleAttributeName: style}];
        [title appendAttributedString:space];
        
        CGFloat w1 = [@" " jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)] constraintsSize:CGSizeMake(CGFLOAT_MAX, 16)].width;
        tagW += ceil(w1);
        
        if (self.tags.count > 2 && (i != self.tags.count-1 && i%2 == 1)) {
            //每行做多展示两个标签,超出就换行
            [title appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
        }
    }
    
    NSAttributedString *attr = [[NSAttributedString alloc] initWithString:baseModel.skuName attributes:@{NSForegroundColorAttributeName: [UIColor blackColor], NSFontAttributeName: [UIFont systemFontOfSize:16]}];
    
    [title appendAttributedString:attr];
    
    self.skuNameLabelAttrStr = [title copy];
    
    self.floorHeight = 60;
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    _floorHeight = 0;
    NSDictionary *floorDic = data[@"data"];
        if ([floorDic isKindOfClass:NSDictionary.class]) {
            
            NSDictionary *module = [data tm_dictionaryForKey:@"data"];
            NSDictionary *commonModule = [module tm_dictionaryForKey:@"commonModule"];
            JDISVPDAttributeCommonModel *common = [JDISVPDAttributeCommonModel yy_modelWithDictionary:commonModule];
            NSDictionary *priceModule = [module tm_dictionaryForKey:@"priceModule"];
            common.priceModule = priceModule;
            common.allfloors = module;
            [common configurePromoType];
            self.showFavor = (common.promoType == JDISVProductPromoTypeSecKill) || (common.promoType == JDISVProductPromoTypePresale) || (common.promoType == JDISVProductPromoTypeGroupBuy);
            if (KSAAPP) {
                self.showFavor = NO;
            }
            
            // 标题
            NSString *titleString = [self skuNameFrom:floorDic];
            if (![titleString jdcd_validateString]) {
                //无商品名称不展示楼层
                self.skuNameLabelAttrStr = nil;
                _floorHeight = 6 + 18;
                return;
            }
            
            // tag标
//            商品名称楼层：
//            自提/Loc判断：tagType == 1 自提，下发tagType == 2 Loc
            NSString *storeServiceText = nil;
//            if (common.promoType != JDISVProductPromoTypePresale && common.promoType != JDISVProductPromoTypeGroupBuy) {
//            if (common.promoType != JDISVProductPromoTypeGroupBuy) {
                NSDictionary *productTitleModuleDic = floorDic[@"productTitleModule"] ? : @{};
                if ([productTitleModuleDic isKindOfClass:NSDictionary.class]) {
                    if (productTitleModuleDic.allKeys.count == 0) return;
                    NSArray *productTagPartList = [productTitleModuleDic objectForKey:@"productTagPartList"];
                    if ([productTagPartList isKindOfClass:NSArray.class] && [productTagPartList count] > 0) {
                        NSMutableArray <NSString *>*tags = [NSMutableArray new];
                        for (NSDictionary *tag in productTagPartList) {
                            if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                                [tags insertObject:tag[@"info"][@"C-P#productTag&productTag"][@"tagText"] atIndex:0];
                            }else{
                                [tags addObject:tag[@"info"][@"C-P#productTag&productTag"][@"tagText"]];
                            }
                            NSNumber *tagType = tag[@"info"][@"C-P#productTag&productTag"][@"tagType"];
                            if (tagType.integerValue == 1) {
                                storeServiceText = ProductDetailSDKL(@"pd_base_floor_local_store_hint_self_take");
                            } if (tagType.integerValue == 2) {
                                storeServiceText = ProductDetailSDKL(@"pd_base_floor_local_store_hint");
                            }
                        }
                        if ([tags count]) {
                            self.tags = [tags copy];
                        }
                    }
                }
//            }
            self.storeServiceText = storeServiceText;
                    
            
            NSMutableParagraphStyle *style = [[NSMutableParagraphStyle defaultParagraphStyle] mutableCopy];
//            style.lineSpacing = 6 - [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"].lineHeight + [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"].pointSize;
            style.maximumLineHeight = 22.5;
            style.minimumLineHeight = 22.5;
            
            NSMutableAttributedString *title = [[NSMutableAttributedString alloc] init];
            
            // 本地生活 到店打标
            CGFloat tagW = 0;
            for (int i=0;i<self.tags.count;i++) {
                NSString *tag = self.tags[i];
                CGFloat h = 16.f;
                CGFloat w = [tag jdcd_getStringSize:[UIFont systemFontOfSize:12.f] constraintsSize:CGSizeMake(CGFLOAT_MAX, 16)].width + 3 + 3;
                NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
                UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, w, h)];
                label.text = tag;
                label.textAlignment = NSTextAlignmentCenter;
                label.font = [UIFont systemFontOfSize:12.f];
                label.textColor = [UIColor whiteColor];
                label.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d"];
                label.layer.cornerRadius = 4.f;
                label.layer.masksToBounds = YES;
                attachment.image = [self getImageFromView:label];
                
                attachment.bounds = CGRectMake(0, -2, w, h);
                tagW += w;
                NSMutableAttributedString *tagImg = [NSMutableAttributedString attributedStringWithAttachment:attachment];
//                [tagImg addAttribute:NSBaselineOffsetAttributeName value:@(-1) range:NSMakeRange(0, tagImg.length)];
                [title appendAttributedString:tagImg];
                
                NSAttributedString *space = [[NSAttributedString alloc] initWithString:@" " attributes:@{NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)], NSParagraphStyleAttributeName: style}];
                [title appendAttributedString:space];
                
                CGFloat w1 = [@" " jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)] constraintsSize:CGSizeMake(CGFLOAT_MAX, 16)].width;
                tagW += ceil(w1);
                
                if (self.tags.count > 2 && (i != self.tags.count-1 && i%2 == 1)) {
                    //每行做多展示两个标签,超出就换行
                    [title appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];
                }
            }
//            self.skuTagAttrStr = title;
            
            // 商品名
            NSAttributedString *attr = [[NSAttributedString alloc] initWithString:titleString attributes:@{NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)], NSParagraphStyleAttributeName: style}];
            if(PlatformService.isRTL){
                NSMutableAttributedString* tmpStr = [attr mutableCopy];
                NSAttributedString *spaceStr = [[NSAttributedString alloc] initWithString:@" " attributes:@{NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)], NSParagraphStyleAttributeName: style}];
                [tmpStr appendAttributedString:spaceStr];
                [tmpStr appendAttributedString:title];
                title = tmpStr;
            }else{
                [title appendAttributedString:attr];
            }
            
            self.skuNameLabelAttrStr = [title copy];
            
            CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
            CGFloat maxH = ceil(22.5*5);//五行高度，最多展示五行
            _nameHeight = ceil([self.skuNameLabelAttrStr jdcd_getSizeWithContainer:CGSizeMake([UIWindow isv_pd_screenWidth] - 18 - (self.showFavor ? 60 : 18) - w3*2, maxH)].height);
            CGFloat height = 6 + self.nameHeight;
            _floorHeight += (height);

            //收藏
            if (self.showFavor) {
                _floorHeight += 12;
            }
            
            // 商品销量
//            前端按照销售单位销量展示。
//            销量为0不展示
//            若销量大于0小于等于1w则展示实际销量
//            若销量大于1w小于等于10w，则展示1w+
//            若销量大于10w小于等于100w，则展示10w+
//            若销量大于100w，则展示100w+
            NSString *saleAmountStr = nil;
            NSString *saleAmount = common.commonCore.saleAmount;
//            saleAmount = @"1001";
            if (saleAmount.length > 0) {
                if (saleAmount.integerValue > 0) {
                    NSString *countStr = @"";
                    if (saleAmount.integerValue <= 10000) {
                        countStr = saleAmount;
                        saleAmountStr = [NSString stringWithFormat:ProductDetailSDKL(@"product_detail_scales_unit1"), countStr];
                    } else if (saleAmount.integerValue > 10000 && saleAmount.integerValue <= 100000) {
                        saleAmountStr = [NSString stringWithFormat:ProductDetailSDKL(@"product_detail_scales_unit2"), @"1"];
                    } else if (saleAmount.integerValue > 100000 && saleAmount.integerValue <= 1000000) {
                        saleAmountStr = [NSString stringWithFormat:ProductDetailSDKL(@"product_detail_scales_unit3"), @"10"];
                    } else if (saleAmount.integerValue > 1000000) {
                        saleAmountStr = [NSString stringWithFormat:ProductDetailSDKL(@"product_detail_scales_unit4"), @"1"];
                    }
                }
            }
            self.saleAmountStr = saleAmountStr;
            _floorHeight += 6;
            if (self.saleAmountStr.length > 0) {
                CGFloat saleAmountStrtheight = ceil([self.saleAmountStr jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T6"] constraintsSize:CGSizeMake([UIWindow isv_pd_screenWidth] - 18 - 18 - w3*2, CGFLOAT_MAX)].height);
                _floorHeight += saleAmountStrtheight;
            }
            
            if (self.storeServiceText.length > 0) {
                NSAttributedString *Serviceattri = [[NSAttributedString alloc] initWithString:self.storeServiceText attributes:@{NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
                CGFloat serviceTextheight = ceil([Serviceattri jdcd_getSizeWithContainer:CGSizeMake([UIWindow isv_pd_screenWidth] - 18 - 18 - w3*2, CGFLOAT_MAX)].height)+1;
                _floorHeight += 6 + serviceTextheight;
            }
            
            //最后加上底部边距
            _floorHeight += 12;
    }
}

// SkuNameFloorModule解析数据
- (NSString *)skuNameFrom:(NSDictionary *)floorDic {
    NSDictionary *productTitleModuleDic = floorDic[@"productTitleModule"] ? : @{};
    if ([productTitleModuleDic isKindOfClass:NSDictionary.class]) {
        if (productTitleModuleDic.allKeys.count == 0 ) return @"";
        NSDictionary *data = [productTitleModuleDic objectForKey:@"C-M#ViewTitleItem&core"];
        NSString *title = [data objectForKey:@"title"];
        return [title jdcd_validateString] ? title : @"";
    } else {
        return @"";
    }
}


- (UIImage *)getImageFromView:(UIView *)view {
    
    if (view.bounds.size.width<=0 || view.bounds.size.height<=0) {
        return [[UIImage alloc] init];
    }
    
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

@end
