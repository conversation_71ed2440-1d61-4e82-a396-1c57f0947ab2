//
//  JDISVPDDSkuNameFloorCell.m
//  JDISVProductDetailSDKModule
//
//  Created by 张令浩 on 2022/2/17.
//

#import "JDISVPDDSkuNameFloorCell.h"

#import "JDISVPDAttributeFloorMacro.h"

#import "JDISVPDDSkuNameFloorModule.h"

@interface JDISVPDDSkuNameFloorCell ()

@property (weak, nonatomic) IBOutlet UILabel *skuNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *saleAmountLabel;
@property (weak, nonatomic) IBOutlet UIButton *favorBtn;
@property (weak, nonatomic) IBOutlet UILabel *favorLabel;
@property (weak, nonatomic) IBOutlet UILabel *storeTipLabel;

@property (nonatomic, strong) JDISVPDDSkuNameFloorModule *viewModel;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *skuNameLabelTopLayout;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *skuNameLabelTrailingLayout;
@property (weak, nonatomic) IBOutlet UILabel *labelTag;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tagLabelWidth;


@end

@implementation JDISVPDDSkuNameFloorCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.skuNameLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.skuNameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
    self.skuNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    self.skuNameLabel.numberOfLines = 5;
    
    self.saleAmountLabel.text = @"";
    self.saleAmountLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightLight)];
    self.saleAmountLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    
    self.storeTipLabel.text = @"";
    self.storeTipLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)];
    self.storeTipLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
    
    self.favorLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.favorLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:(UIFontWeightLight)];
    self.favorLabel.hidden = YES;
    
    UIImage *img = [UIImage ka_iconWithName:JDIF_ICON_FAVORITE_LINE_BIG imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    [self.favorBtn setImage:img forState:(UIControlStateNormal)];
    UIImage *img1 = [UIImage ka_iconWithName:JDIF_ICON_FAVORITE_FILL_BIG imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    [self.favorBtn setImage:img1 forState:(UIControlStateSelected)];
    self.favorBtn.hidden = YES;
    
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {

    self.viewModel = floorModel;
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"kFetchFavorStatus" broadcastAction:YES];
    JDWeakSelf
    action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
        JDStrongSelf
        BOOL favor = [obj boolValue];
        self.favorBtn.selected = favor;
        self.favorLabel.text = favor ? ProductDetailSDKL(@"pd_base_floor_btn_like_selected") : ProductDetailSDKL(@"pd_base_floor_btn_like_normal");
    };
    [self isv_sendAction:action];
    
    self.skuNameLabelTopLayout.constant = self.viewModel.showFavor ? 12 : 6;
    self.skuNameLabelTrailingLayout.constant = self.viewModel.showFavor ? 60 : 18;
    self.favorBtn.hidden = !self.viewModel.showFavor;
    self.favorLabel.hidden = !self.viewModel.showFavor;
    
    if (self.viewModel.skuNameLabelAttrStr) {
        self.skuNameLabel.hidden = NO;
        self.skuNameLabel.attributedText = self.viewModel.skuNameLabelAttrStr;
//        self.labelTag.attributedText = _module.skuTagAttrStr;
        self.tagLabelWidth.constant = 0;
        self.labelTag.hidden = YES;
    } else {
        self.labelTag.hidden = YES;
        self.skuNameLabel.hidden = YES;
        self.tagLabelWidth.constant = 0;
    }
    
    [[[self.favorBtn rac_signalForControlEvents:(UIControlEventTouchUpInside)] takeUntil:self.rac_prepareForReuseSignal] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"kFavoriteRequestResult" broadcastAction:YES];
        action.sender = self;
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            JDStrongSelf
            BOOL favor = [obj boolValue];
            self.favorBtn.selected = favor;
            self.favorLabel.text = favor ? ProductDetailSDKL(@"pd_base_floor_btn_like_selected") : ProductDetailSDKL(@"pd_base_floor_btn_like_normal");
        };
        [self isv_sendAction:action];
    }];
    
    if (self.viewModel.storeServiceText.length > 0) {
        self.storeTipLabel.hidden = NO;
        self.storeTipLabel.text = self.viewModel.storeServiceText;
    } else {
        self.storeTipLabel.hidden = YES;
    }
    
    if (self.viewModel.saleAmountStr.length > 0) {
        self.saleAmountLabel.hidden = NO;
        self.saleAmountLabel.text = self.viewModel.saleAmountStr;
    } else {
        self.saleAmountLabel.hidden = YES;
    }
}

@end
