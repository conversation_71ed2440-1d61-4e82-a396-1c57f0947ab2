//
//  JDISVProductStoreListViewController.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/12/27.
//

#import "JDISVProductStoreListViewController.h"

#import <JDISVKAUIKitModule/KANavigationBar.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVDZNEmptyDataSetModule/JDISVDZNEmptyDataSetModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDBRouterModule/JDRouter.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVCategoryModule/NSObject+JDCDExtend.h>

#import <JDISVPlatformModule/JDISVPlatformService.h>
#import "JDISVProductStoreListTableViewCell.h"
#import "NSBundle+JDISVPDBundle.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import "JDISVProductStoreMainViewModel.h"
#import "JDISVProductStoreListTableViewCell.h"
#import "JDISVProductStoreHeaderView.h"
#import "JDISVProductStoreListAction.h"
#import "JDISVProductStoreListModel.h"
#import "JDISVProductStoreListStandCell.h"

@interface JDISVProductStoreListViewController ()<UITableViewDelegate,UITableViewDataSource, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>

@property (nonatomic,strong)UITableView *tableView;

@property (nonatomic,strong)JDISVProductStoreHeaderView *tableViewHeaderView;

@property (nonatomic,strong)JDISVProductStoreMainViewModel *viewModel;

@end

@implementation JDISVProductStoreListViewController

+ (instancetype)initWithSkuId:(NSString *)skuId 
                     venderId:(NSString *)venderID
                      storeId:(NSString*)storeId{
    JDISVProductStoreListViewController *vc = [[JDISVProductStoreListViewController alloc] init];
    vc.viewModel.venderID = venderID;
    vc.viewModel.skuID = skuId;
    vc.viewModel.storeID = storeId;
    return vc;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self.navigationController.navigationBar setHidden:YES];
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    JDWeakSelf
    navigationBar
    .decorator
    .backgroundColor()
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDStrongSelf
        if (self.callBack) {
            self.callBack(@(0));
        }
        [self.navigationController popViewControllerAnimated:YES];
    })
    .title(ProductDetailSDKL(@"base_pd_title_store_select"), NSTextAlignmentCenter) //标题
    .render();
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    
    [self configTableView];
    
    [self loadStoreListData];
}

- (void)configTableView{
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).offset([UIWindow ka_uikit_navigationHeight]);
        make.leading.bottom.trailing.mas_equalTo(self.view);
    }];
    
    [_tableView registerNib:[UINib nibWithNibName:@"JDISVProductStoreListTableViewCell" bundle:[NSBundle isv_pd_currentBundle]] forCellReuseIdentifier:@"JDISVProductStoreListTableViewCell"];
    [_tableView registerNib:[UINib nibWithNibName:@"JDISVProductStoreListTableViewCell" bundle:[NSBundle isv_pd_currentBundle]] forHeaderFooterViewReuseIdentifier:@"JDISVProductStoreHeaderView"];
    
    [_tableView registerClass:JDISVProductStoreListStandCell.class forCellReuseIdentifier:@"JDISVProductStoreListStandCell"];
}

- (void)eventWithAction:(JDISVProductStoreListAction *)action{
    if (action.actionType == JDISVProductStoreListActionWithClickAddress) {
        JDTAddressItemModel *addr = [PlatformService getDefaultAddress];
        NSMutableDictionary *arg = [NSMutableDictionary dictionary];
        if (addr) {
            [arg setObject:addr forKey:@"address"];
        }
        
        NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeAddress];
        NSString* routerStr = [NSString stringWithFormat:@"router://%@/cascaderAddressViewController",module];
        UIViewController *cascaderController  = [JDRouter openURL:routerStr arg:[arg copy] error:nil completion:^(JDTAddressItemModel *object) {
            [PlatformService setDefaultAddress:object];
            // 选择完成
            [self loadStoreListData];
        }];
        KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
        delegate.contentHeight = UIScreen.mainScreen.bounds.size.height-90;
        delegate.defultHeader.titleLabel.text = ProductDetailSDKL(@"pd_base_floor_address");
        cascaderController.transitioningDelegate = delegate;
        if (cascaderController) {
            [self presentViewController:cascaderController animated:YES completion:nil];
        }
    }else if (action.actionType == JDISVProductStoreListActionWithClickPhone){
        NSString *tellString = [NSString stringWithFormat:@"tel://%@",action.value];
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:tellString] options:@{} completionHandler:nil];
    }
}

#pragma mark - Data
- (void)loadStoreListData {
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel loadStoreListData] subscribeCompleted:^{
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [self.tableViewHeaderView reloadAddress];
        [self.tableView reloadData];
    }];
}

#pragma mark - Action
- (void)clickEmptyViewButton {
    [self loadStoreListData];
}

#pragma mark - TableView
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.dataArray.count;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    JDWeakSelf

    [self.tableViewHeaderView.delegate subscribeNext:^(JDISVProductStoreListAction *action) {
        JDStrongSelf
        [self eventWithAction:action];
    }];
    return self.tableViewHeaderView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 44;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVProductStoreListItemModel* selectModel = self.viewModel.dataArray[indexPath.row];
    selectModel.isSelected = YES;
    
    if (self.callBack) {
        self.callBack(selectModel.storeId);
        [self.navigationController popViewControllerAnimated:YES];
        return;
    }
    
    for(JDISVProductStoreListItemModel* itemModel in self.viewModel.dataArray){
        itemModel.isSelected = NO;
    }
    [self.tableView reloadData];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVProductStoreListItemModel* itemModel = self.viewModel.dataArray[indexPath.row];
    JDISVProductStoreListTableViewCell *cell;
    if(itemModel.isStandardDelivery){
        cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVProductStoreListStandCell"];
    }else{
        cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVProductStoreListTableViewCell" forIndexPath:indexPath];
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell config:itemModel];
    JDWeakSelf
    [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(JDISVProductStoreListAction *action) {
        JDStrongSelf
        [self eventWithAction:action];
    }];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [self.viewModel getCurrentCellHeightWithIndex:indexPath.row];
}

- (UITableView *)tableView{
    if(!_tableView){
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero];
        _tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
        _tableView.estimatedRowHeight = 0;
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        _tableView.emptyDataSetSource = self;
        _tableView.emptyDataSetDelegate = self;
    }
    return _tableView;
}

- (JDISVProductStoreHeaderView *)tableViewHeaderView{
    if (!_tableViewHeaderView) {
        _tableViewHeaderView = [[JDISVProductStoreHeaderView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, 44)];
        _tableViewHeaderView.isLoc = self.isLoc;
    }
    return _tableViewHeaderView;
}

- (JDISVProductStoreMainViewModel *)viewModel{
    if (!_viewModel) {
        _viewModel = [[JDISVProductStoreMainViewModel alloc] init];
    }
    return _viewModel;
}

#pragma mark - DZNEmptyDataSet
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView {
    return (self.viewModel.emptyViewType != JDISVProductStoreMainViewEmptyViewTypeHide);
}

- (UIView *)customViewForEmptyDataSet:(UIScrollView *)scrollView {
    KAEmptyViewType emptyViewType;
    if ([_viewModel.emptyButtonTitle jdcd_validateString]) {
        emptyViewType = KAEmptyViewTypeNormal;
    } else {
        emptyViewType = KAEmptyViewTypeNotAction;
    }
    KAEmptyView *emptyView = [[KAEmptyView alloc] initWithFrame:self.tableView.frame type:emptyViewType];
    
    NSLayoutConstraint *heightConstraint = [NSLayoutConstraint constraintWithItem:emptyView attribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:nil attribute:NSLayoutAttributeNotAnAttribute multiplier:1 constant:self.tableView.frame.size.height];
    [emptyView addConstraint:heightConstraint];
    emptyView.verticalAlignment = KAEmptyViewVerticalAlignmentWindowCenter;
    
    emptyView.decrible = self.viewModel.emptyContent;
    emptyView.coverImage = [self.viewModel emptyViewCoverImage];
    
    emptyView.actionTitle = self.viewModel.emptyButtonTitle;
    @weakify(self)
    emptyView.action = ^(UIButton * _Nonnull sender) {
        @strongify(self)
        [self clickEmptyViewButton];
    };
    
    return emptyView;
}
@end
