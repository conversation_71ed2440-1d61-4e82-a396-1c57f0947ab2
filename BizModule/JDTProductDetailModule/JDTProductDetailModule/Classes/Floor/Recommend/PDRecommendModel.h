//
//  PDRecommendModel.h
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NSString * PDPriceType NS_STRING_ENUM;

extern PDPriceType const PDPriceTypeOrigin;     // 原价
extern PDPriceType const PDPriceTypePromotion;  // 促销
extern PDPriceType const PDPriceTypeSecKill;    // 秒杀
extern PDPriceType const PDPriceTypeCollage;    // 拼团
extern PDPriceType const PDPriceTypePresale;    // 预售
extern PDPriceType const PDPriceTypeSlash;      // 砍价

@interface PDRecommendItemModel : NSObject

@property (nonatomic, strong) NSNumber *skuId;

@property (nonatomic, copy) NSString *skuName;

@property (nonatomic, strong) NSNumber *shopId;

@property (nonatomic, strong) NSNumber *venderId;

@property (nonatomic, copy) NSString *imageUrl;
///  价格类型：ORIGIN_PRICE：原价 PROMOTION：促销 SEC_KILL：秒杀 COLLAGE：拼团 PRESALE：预售 SLASH：砍价
@property (nonatomic, copy) PDPriceType priceType;
///  现价
@property (nonatomic, copy) NSString *salePrice;
///  原价
@property (nonatomic, copy) NSString *originalPrice;

@property (nonatomic, strong) NSNumber *reductionRate;

@property (nonatomic, strong) NSNumber *discountRate;

@property (nonatomic, strong) NSNumber *categoryId;

@end

@interface PDRecommendModel : NSObject

@property (nonatomic, copy) NSArray <PDRecommendItemModel *> *recommendSkuVoList;

@end

NS_ASSUME_NONNULL_END
