//
//  PDProductImageFloorCell.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/21.
//

#import "PDProductImageFloorCell.h"
#import "JDISVPDAttributeFloorMacro.h"
#import "PDProductImageFloorViewModel.h"
#import "PDProductImageCollectionCell.h"

@interface PDProductImageFloorCell () <UICollectionViewDelegateFlowLayout, UICollectionViewDataSource>

@property (nonatomic, strong) UICollectionView *mainImgCollectionView;

@property (nonatomic, strong) UILabel *indexLabel;

@property (nonatomic, strong) UIView *subImgContainerView;

@property (nonatomic, strong) UICollectionView *subImgCollectionView;

@property (nonatomic, strong) UIButton *totalBtn;

@property (nonatomic, strong) PDProductImageFloorViewModel *viewModel;

@end

@implementation PDProductImageFloorCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self.contentView addSubview:self.mainImgCollectionView];
    [self.mainImgCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.contentView);
        make.height.mas_equalTo(420);
    }];
    [self.contentView addSubview:self.indexLabel];
    [self.indexLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.mainImgCollectionView.mas_right).offset(-20);
        make.bottom.equalTo(self.mainImgCollectionView.mas_bottom).offset(-20);
        make.height.mas_equalTo(40);
    }];
    
    // 添加滚动监听
    [self.mainImgCollectionView addObserver:self forKeyPath:@"contentOffset" options:NSKeyValueObservingOptionNew context:nil];
}

#pragma mark - <JDISVFloorViewProtocol>
- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
    // 初始化时显示第一张图片的索引
    if (self.viewModel.mainImgArr.count > 0) {
        self.indexLabel.text = [NSString stringWithFormat:@" 1/%ld ", self.viewModel.mainImgArr.count];
    }
    
    if (self.viewModel.subImgArr.count) {
        [self.contentView addSubview:self.subImgContainerView];
        [self.subImgContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mainImgCollectionView.mas_bottom);
            make.left.right.equalTo(self.contentView);
            make.height.mas_equalTo(78);
        }];
        
        [self.subImgContainerView addSubview:self.totalBtn];
        [self.totalBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.subImgContainerView.mas_top).offset(12);
            make.bottom.equalTo(self.subImgContainerView.mas_bottom).offset(-12);
            make.right.equalTo(self.subImgContainerView.mas_right).offset(-18);
        }];
        
        [self.subImgContainerView addSubview:self.subImgCollectionView];
        [self.subImgCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.subImgContainerView.mas_top).offset(12);
            make.bottom.equalTo(self.subImgContainerView.mas_bottom).offset(-12);
            make.left.equalTo(self.subImgContainerView.mas_left).offset(18);
            make.right.equalTo(self.totalBtn.mas_left).offset(-8);
        }];
        
        [self.totalBtn setTitle:[NSString stringWithFormat:@"%zd 款可选", self.viewModel.subImgArr.count] forState:UIControlStateNormal];
        [self.totalBtn addTarget:self action:@selector(onClickTotalBtn) forControlEvents:UIControlEventTouchUpInside];
    }
}

#pragma mark - <UICollectionViewDataSource>
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (collectionView == self.mainImgCollectionView) {
        return self.viewModel.mainImgArr.count;
    } else {
        return self.viewModel.subImgArr.count;
    }
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (collectionView == self.mainImgCollectionView) {
        PDProductImageCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PDProductImageCollectionCell" forIndexPath:indexPath];
        [cell.imgView jdcd_setImage:self.viewModel.mainImgArr[indexPath.item] placeHolderType:JDCDWebImagePlaceHolderTypeLarge completion:nil];
        return cell;
    } else {
        PDProductSubImageCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PDProductSubImageCollectionCell" forIndexPath:indexPath];
        [cell.imgView jdcd_setImage:self.viewModel.subImgArr[indexPath.item] placeHolderType:JDCDWebImagePlaceHolderTypeLarge completion:nil];
        return cell;
    }
}

#pragma mark - <UICollectionViewDelegateFlowLayout>
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (collectionView == self.mainImgCollectionView) {
        return CGSizeMake(collectionView.bounds.size.width, 420);
    } else {
        return CGSizeMake(56, 56);
    }
}

#pragma mark - <UICollectionViewDelegate>

#pragma mark - KVO
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    if ([keyPath isEqualToString:@"contentOffset"] && object == self.mainImgCollectionView) {
        [self updateIndexLabel];
    }
}

- (void)updateIndexLabel {
    if (self.viewModel.mainImgArr.count == 0) return;
    
    CGFloat offsetX = self.mainImgCollectionView.contentOffset.x;
    CGFloat width = self.mainImgCollectionView.bounds.size.width;
    NSInteger currentIndex = (NSInteger)(offsetX / width) + 1;
    
    // 确保索引在有效范围内
    currentIndex = MAX(1, MIN(currentIndex, self.viewModel.mainImgArr.count));
    
    self.indexLabel.text = [NSString stringWithFormat:@" %ld/%ld ", currentIndex, self.viewModel.mainImgArr.count];
}

#pragma mark - Action Method
- (void)onClickTotalBtn {
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVProductImageFloorSelectSpec"];
    [self isv_sendAction:action];
}

#pragma mark - <JDCDISVActionTransferProtocol>
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVProductImageFloorSelectSpec"]) {
        NSDictionary *arg = @{
            @"saleAttrList": self.viewModel.saleAttrList,
            @"baseModel": self.viewModel.baseModel,
            @"imageModel": self.viewModel.imageModel,
            @"priceModel": self.viewModel.priceModel,
            @"limitModel": self.viewModel.limitModel,
            @"skuId": self.viewModel.baseModel.skuId
        };
        UIViewController *vc = [JDRouter openURL:@"router://JDISVProductDetailSDKModule/selectSpecController" arg:arg error:nil completion:nil];
        KAFloatLayerPresentationController *floatVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:vc presentingViewController:controller];
        floatVC.contentHeight = 0.9 * [[UIScreen mainScreen] bounds].size.height;
        vc.transitioningDelegate = floatVC;
        [controller presentViewController:vc animated:YES completion:nil];
    }
    return NO;
}

- (void)dealloc {
    [self.mainImgCollectionView removeObserver:self forKeyPath:@"contentOffset"];
}

#pragma mark - Getter and Setter
- (UICollectionView *)mainImgCollectionView {
    if (!_mainImgCollectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumLineSpacing = 0;
        layout.minimumInteritemSpacing = 0;
        
        _mainImgCollectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _mainImgCollectionView.backgroundColor = [UIColor whiteColor];
        _mainImgCollectionView.delegate = self;
        _mainImgCollectionView.dataSource = self;
        _mainImgCollectionView.pagingEnabled = YES;
        _mainImgCollectionView.showsHorizontalScrollIndicator = NO;
        [_mainImgCollectionView registerClass:[PDProductImageCollectionCell class] forCellWithReuseIdentifier:@"PDProductImageCollectionCell"];
    }
    return _mainImgCollectionView;
}

- (UICollectionView *)subImgCollectionView {
    if (!_subImgCollectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumLineSpacing = 10;
        layout.minimumInteritemSpacing = 0;
        
        _subImgCollectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _subImgCollectionView.backgroundColor = [UIColor whiteColor];
        _subImgCollectionView.delegate = self;
        _subImgCollectionView.dataSource = self;
        _subImgCollectionView.pagingEnabled = YES;
        _subImgCollectionView.showsHorizontalScrollIndicator = NO;
        [_subImgCollectionView registerClass:[PDProductSubImageCollectionCell class] forCellWithReuseIdentifier:@"PDProductSubImageCollectionCell"];
    }
    return _subImgCollectionView;
}

- (UIView *)subImgContainerView {
    if (!_subImgContainerView) {
        _subImgContainerView = [[UIView alloc] init];
        _subImgContainerView.backgroundColor = [UIColor whiteColor];
    }
    return _subImgContainerView;
}

- (UILabel *)indexLabel {
    if (!_indexLabel) {
        _indexLabel = [[UILabel alloc] init];
        _indexLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        _indexLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C1");
        _indexLabel.jdisv_backgroundColorPicker = JDISVColorPickerWithKeyAndAlpha(@"#C7", 0.25);
        _indexLabel.layer.cornerRadius = 7;
        _indexLabel.layer.masksToBounds = YES;
    }
    return _indexLabel;
}

- (UIButton *)totalBtn {
    if (!_totalBtn) {
        _totalBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _totalBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        [_totalBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    }
    return _totalBtn;
}

@end
