//
//  PDProductImageFloorViewModel.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/21.
//

#import "PDProductImageFloorViewModel.h"
#import "JDISVPDAttributeFloorMacro.h"
#import "PDProductImageFloorCell.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

@import JDTCommonToolModule;

JDISVRegisterFloorModule(KaProductMainImageFloor, PDProductImageFloorViewModel)

@implementation PDProductImageFloorViewModel

#pragma mark - <JDISVFloorModuleProtocol>
- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (CGFloat)floorHeight {
    // 只有一个商品时420
    if (self.subImgArr.count) {
        return 420 + 78;
    }
    return 420;
}

- (Class)tableViewFloorClass {
    return [PDProductImageFloorCell class];
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.imageModel = [PDProductImageModel yy_modelWithDictionary:data[@"data"][@"productImgModule"]];
    self.mainImgArr = self.imageModel.imageList;
    NSMutableArray *subImgMArr = [NSMutableArray array];
    for (PDProdcutSubImageModel *subImg in self.imageModel.subImgList) {
        [subImgMArr addObject:subImg.mainImg];
    }
    self.subImgArr = subImgMArr;
    
    self.baseModel = [PDProductBaseModel yy_modelWithDictionary:data[@"data"][@"productBaseModule"]];
    self.priceModel = [PDPriceModel yy_modelWithDictionary:data[@"data"][@"priceModule"]];
    self.limitModel = [PDLimitModel yy_modelWithDictionary:data[@"data"][@"limitModule"]];
    PDProductSpecModel *specModel = [PDProductSpecModel yy_modelWithDictionary:data[@"data"][@"selectedModule"]];
    self.saleAttrList = specModel.saleAttrList;
}

@end
