//
//  PDProductImageFloorViewModel.h
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/21.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@interface PDProductImageFloorViewModel : NSObject <JDISVFloorModuleProtocol>

@property (nonatomic, copy) NSArray <NSString *> *mainImgArr;

@property (nonatomic, copy) NSArray <NSString *> *subImgArr;

@property (nonatomic, strong) PDProductBaseModel *baseModel;

@property (nonatomic, strong) PDProductImageModel *imageModel;

@property (nonatomic, strong) PDPriceModel *priceModel;

@property (nonatomic, strong) PDLimitModel *limitModel;

@property (nonatomic, copy) NSArray <PDProductSpecItemModel *> *saleAttrList;

@end

NS_ASSUME_NONNULL_END
