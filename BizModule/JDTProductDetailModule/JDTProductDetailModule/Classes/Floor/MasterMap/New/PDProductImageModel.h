//
//  PDProductImageModel.h
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/13.
//

#import <Foundation/Foundation.h>

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@interface PDProdcutSubImageModel : NSObject

@property (nonatomic, copy) NSString *skuId;

@property (nonatomic, copy) NSString *mainImg;

@property (nonatomic, assign) StockState stockState;

@property (nonatomic, assign) SkuStatus skuStatus;

@end

@interface PDProductImageModel : NSObject

///  主商品图
@property (nonatomic, copy) NSArray <NSString *> *imageList;

///  其他规格的商品，每一个元素是一个 SKU
@property (nonatomic, copy) NSArray <PDProdcutSubImageModel *> *subImgList;

@end

NS_ASSUME_NONNULL_END
