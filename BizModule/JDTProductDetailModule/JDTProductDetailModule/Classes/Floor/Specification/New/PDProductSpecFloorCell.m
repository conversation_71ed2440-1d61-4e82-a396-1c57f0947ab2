//
//  PDProductSpecFloorCell.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/20.
//

#import "PDProductSpecFloorCell.h"
#import "JDISVPDAttributeFloorMacro.h"
#import "PDProductSpecFloorViewModel.h"

@interface PDProductSpecFloorCell () <JDCDISVActionTransferProtocol>

@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UILabel *leftLabel;

@property (nonatomic, strong) UILabel *infoLabel;

@property (nonatomic, strong) UIImageView *rightImgView;

@property (nonatomic, strong) PDProductSpecFloorViewModel *viewModel;

@end

@implementation PDProductSpecFloorCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self.contentView addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(18);
        make.right.equalTo(self.contentView).offset(-18);
        make.top.bottom.equalTo(self.contentView);
    }];
    self.stackView.axis = UILayoutConstraintAxisHorizontal;
    self.stackView.alignment = UIStackViewAlignmentCenter;
    self.stackView.distribution = UIStackViewDistributionFill;
    
    [self.stackView addArrangedSubview:self.leftLabel];
    [self.stackView addArrangedSubview:self.infoLabel];
    [self.stackView addArrangedSubview:self.rightImgView];
    
    [self.stackView setCustomSpacing:12 afterView:self.leftLabel];
    [self.stackView setCustomSpacing:8 afterView:self.infoLabel];
    
    [self.leftLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.infoLabel setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.leftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_greaterThanOrEqualTo(70);
    }];
    [self.rightImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(12, 12));
    }];
}

#pragma mark - <JDISVFloorViewProtocol>
- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.leftLabel.text = self.viewModel.leftTitle;
    self.infoLabel.text = self.viewModel.infoTitle;
}

#pragma mark - <JDCDISVActionTransferProtocol>
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]) {
        NSDictionary *arg = @{
            @"saleAttrList": self.viewModel.saleAttrList,
            @"baseModel": self.viewModel.baseModel,
            @"imageModel": self.viewModel.imageModel,
            @"priceModel": self.viewModel.priceModel,
            @"limitModel": self.viewModel.limitModel,
            @"skuId": self.viewModel.baseModel.skuId
        };
        UIViewController *vc = [JDRouter openURL:@"router://JDISVProductDetailSDKModule/selectSpecController" arg:arg error:nil completion:nil];
        KAFloatLayerPresentationController *floatVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:vc presentingViewController:controller];
        floatVC.contentHeight = 0.9 * [[UIScreen mainScreen] bounds].size.height;
        vc.transitioningDelegate = floatVC;
        [controller presentViewController:vc animated:YES completion:nil];
    }
    return NO;
}

#pragma mark - Getter and Setter
- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] init];
    }
    return _stackView;
}

- (UILabel *)leftLabel {
    if (!_leftLabel) {
        _leftLabel = [[UILabel alloc] init];
        _leftLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _leftLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    }
    return _leftLabel;
}

- (UILabel *)infoLabel {
    if (!_infoLabel) {
        _infoLabel = [[UILabel alloc] init];
        _infoLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _infoLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    }
    return _infoLabel;
}

- (UIImageView *)rightImgView {
    if (!_rightImgView) {
        _rightImgView = [[UIImageView alloc] init];
        _rightImgView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    }
    return _rightImgView;
}

@end
