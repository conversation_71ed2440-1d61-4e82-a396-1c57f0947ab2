//
//  PDProductSpecFloorViewModel.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/20.
//

#import "PDProductSpecFloorViewModel.h"
#import "PDProductSpecFloorCell.h"
#import "JDISVPDAttributeFloorMacro.h"

@import JDISVFloorRenderModule;
@import JDTCommonToolModule;

JDISVRegisterFloorModule(KaProductSpecificationFloor, PDProductSpecFloorViewModel)

@interface PDProductSpecFloorViewModel ()

@end

@implementation PDProductSpecFloorViewModel

- (CGFloat)floorHeight {
    return 58;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (Class)tableViewFloorClass {
    return [PDProductSpecFloorCell class];
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.baseModel = [PDProductBaseModel yy_modelWithDictionary:data[@"data"][@"productBaseModule"]];
    self.imageModel = [PDProductImageModel yy_modelWithDictionary:data[@"data"][@"productImgModule"]];
    self.priceModel = [PDPriceModel yy_modelWithDictionary:data[@"data"][@"priceModule"]];
    self.limitModel = [PDLimitModel yy_modelWithDictionary:data[@"data"][@"limitModule"]];
    
    PDProductSpecModel *specModel = [PDProductSpecModel yy_modelWithDictionary:data[@"data"][@"selectedModule"]];
    self.leftTitle = @"已选";
    NSMutableString *info = [NSMutableString string];
    for (PDProductSpecItemModel *item in specModel.saleAttrList) {
        for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
            for (NSString *skuId in detail.skuList) {
                if ([self.baseModel.skuId isEqualToString:skuId]) {
                    [info appendFormat:@"%@，", detail.text];
                    break;
                }
            }
        }
    }
    if (info.length > 1) {
        [info deleteCharactersInRange:NSMakeRange(info.length - 1, 1)];
    }
    self.infoTitle = info;
    self.saleAttrList = specModel.saleAttrList;
}

@end
