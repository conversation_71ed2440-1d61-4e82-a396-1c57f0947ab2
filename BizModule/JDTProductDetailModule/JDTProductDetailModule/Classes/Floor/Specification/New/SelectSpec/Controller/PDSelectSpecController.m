//
//  PDSelectSpecController.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/22.
//

#import "PDSelectSpecController.h"
#import "JDISVPDAttributeFloorMacro.h"

@import JDTCommonToolModule.PDSpecOptionCell;

@interface PDSelectSpecController () <UICollectionViewDelegateFlowLayout, UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) UIImageView *imgView;

@property (nonatomic, strong) UILabel *nameLabel;

@property (nonatomic, strong) UILabel *priceLabel;

// TODO:Juice 数量选择
/// 限购提示
//@property (nonatomic, strong) UILabel *limitLabel;

@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UIStackView *bottomStackView;

@property (nonatomic, strong) UIButton *addCartBtn;

@property (nonatomic, strong) UIButton *buyBtn;

@property (nonatomic, strong) NSMutableDictionary <NSNumber *, NSString *> *selectedSpecs;

@property (nonatomic, strong) NSMutableDictionary <NSNumber *, NSIndexPath *> *selectedIndexpath;

@property (nonatomic, copy) NSString *selectedSkuId;

@end

@implementation PDSelectSpecController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupHeaderView];
    [self setupFooterView];
    [self setupSpecCollectionView];
}

- (void)setupHeaderView {
    [self.view addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(108, 108));
    }];
    [self.view addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.equalTo(self.view);
        make.left.equalTo(self.imgView.mas_right).offset(12);
        make.height.mas_equalTo(42);
    }];
    [self.view addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.right.equalTo(self.view);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(21);
    }];
    //    [self.view addSubview:self.limitLabel];
    //    [self.limitLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    //
    //    }];
}

- (void)setupSpecCollectionView {
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        // TODO:Juice  这里布局改下
        make.top.equalTo(self.imgView.mas_bottom).offset(24);
        make.bottom.equalTo(self.addCartBtn.mas_top).offset(28);
    }];
}

- (void)setupFooterView {
    [self.view addSubview:self.bottomStackView];
    [self.bottomStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_left).offset(18);
        make.right.equalTo(self.view.mas_right).offset(-18);
        make.height.mas_equalTo(50);
        make.bottom.equalTo(self.view.mas_bottom).offset(-[UIWindow ka_uikit_safeAreaInsets].bottom);
    }];
    
    self.bottomStackView.axis = UILayoutConstraintAxisHorizontal;
    self.bottomStackView.alignment = UIStackViewAlignmentCenter;
    self.bottomStackView.distribution = UIStackViewDistributionFill;
    
    [self.bottomStackView addArrangedSubview:self.addCartBtn];
    [self.bottomStackView addArrangedSubview:self.buyBtn];
    
    [self.bottomStackView setCustomSpacing:6 afterView:self.addCartBtn];
}

#pragma mark - <UICollectionViewDataSource>
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.saleAttrList.count;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.saleAttrList[section].selectedDimBtnPartVoList.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    PDProductSpecItemModel *model = self.saleAttrList[indexPath.section];
    PDProductSpecItemDetailModel *detailModel = model.selectedDimBtnPartVoList[indexPath.item];
    
    PDSpecOptionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PDSpecOptionCell" forIndexPath:indexPath];
    cell.titleLabel.text = detailModel.text;
    
    BOOL isNoStock = (detailModel.stockState == StockStateNoStock);
    BOOL isUnavailable = ![self isOptionAvailable:detailModel forDimNumber:model.dimNumber];
    NSIndexPath *selectedIndexPath = self.selectedIndexpath[model.dimNumber];
    BOOL isSelected = (selectedIndexPath && selectedIndexPath.item == indexPath.item);
    
    if (isNoStock) {
        cell.state = isSelected ? PDSpecOptionCellStateNoStockSelected : PDSpecOptionCellStateNoStockNormal;
    } else if (isUnavailable) {
        cell.state = PDSpecOptionCellStateUnavailable;
    } else if (isSelected) {
        cell.state = PDSpecOptionCellStateSelected;
    } else {
        cell.state = PDSpecOptionCellStateNormal;
    }
    
    return cell;
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    if (kind == UICollectionElementKindSectionHeader) {
        PDSpecSectionHeaderView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"PDSpecSectionHeaderView" forIndexPath:indexPath];
        headerView.sectionLabel.text = self.saleAttrList[indexPath.section].dimTitle;
        return headerView;
    }
    return nil;
}

#pragma mark - <UICollectionViewDelegateFlowLayout>
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *text = self.saleAttrList[indexPath.section].selectedDimBtnPartVoList[indexPath.item].text;
    CGSize size = [text sizeWithAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12]}];
    return CGSizeMake(size.width + 30, 30);
}

#pragma mark - <UICollectionViewDelegate>
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    PDProductSpecItemModel *model = self.saleAttrList[indexPath.section];
    PDProductSpecItemDetailModel *detailModel = model.selectedDimBtnPartVoList[indexPath.item];
    
    PDSpecOptionCell *cell = (PDSpecOptionCell *)[collectionView cellForItemAtIndexPath:indexPath];
    
    switch (cell.state) {
        case PDSpecOptionCellStateUnavailable: {
            // 如果是不可选或无货状态，则不响应选择
            [collectionView deselectItemAtIndexPath:indexPath animated:NO];
            return;
        }
        case PDSpecOptionCellStateSelected:
        case PDSpecOptionCellStateNoStockSelected: {
            [self.selectedSpecs removeObjectForKey:model.dimNumber];
            [self.selectedIndexpath removeObjectForKey:model.dimNumber];
            break;
        }
        case PDSpecOptionCellStateNormal:
        case PDSpecOptionCellStateNoStockNormal: {
            self.selectedSpecs[model.dimNumber] = detailModel.text;
            self.selectedIndexpath[model.dimNumber] = indexPath;
            break;
        }
        default:
            break;
    }
    
    [self updateAvailableOptions];
    [self determineSelectedSku];
}

- (void)updateAvailableOptions {
    [self.collectionView reloadData];
}

- (BOOL)isOptionAvailable:(PDProductSpecItemDetailModel *)model forDimNumber:(NSNumber *)number {
    // 如果没有选择任何规格，所有选项都可用
    if (self.selectedSpecs.count == 0) {
        return YES;
    }
    // 检查是否与已选规格兼容
    for (NSString *sku in model.skuList) {
        BOOL isCompatible = YES;
        
        // 检查该SKU是否与所有已选规格兼容
        for (NSNumber *selectedDimNumber in self.selectedSpecs.allKeys) {
            // 跳过当前正在检查的维度
            if ([selectedDimNumber isEqualToNumber:number]) {
                continue;
            }
            
            NSString *selectedValue = self.selectedSpecs[selectedDimNumber];
            BOOL foundMatch = NO;
            
            // 在所有规格中查找匹配的维度和值
            for (PDProductSpecItemModel *item in self.saleAttrList) {
                if ([item.dimNumber isEqualToNumber:selectedDimNumber]) {
                    for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                        if ([detail.text isEqualToString:selectedValue]) {
                            // 检查SKU是否在该选项的SKU列表中
                            if ([detail.skuList containsObject:sku]) {
                                foundMatch = YES;
                                break;
                            }
                        }
                    }
                    break;
                }
            }
            
            if (!foundMatch) {
                isCompatible = NO;
                break;
            }
        }
        
        if (isCompatible) {
            return YES;
        }
    }
    return NO;
}

- (void)determineSelectedSku {
    // 如果所有规格都已选择，确定最终SKU
    if (self.selectedSpecs.count == self.saleAttrList.count) {
        NSMutableSet *possibleSkus = nil;
        
        // 遍历所有已选规格，找出符合所有规格的SKU
        for (NSNumber *dimNumber in self.selectedSpecs.allKeys) {
            NSString *selectedValue = self.selectedSpecs[dimNumber];
            
            for (PDProductSpecItemModel *item in self.saleAttrList) {
                if ([item.dimNumber isEqualToNumber:dimNumber]) {
                    for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                        if ([detail.text isEqualToString:selectedValue]) {
                            NSArray *skuList = detail.skuList;
                            NSMutableSet *skuSet = [NSMutableSet setWithArray:skuList];
                            
                            if (possibleSkus == nil) {
                                possibleSkus = skuSet;
                            } else {
                                [possibleSkus intersectSet:skuSet];
                            }
                            
                            break;
                        }
                    }
                }
            }
        }
        
        // 如果找到唯一的SKU，则设置为选中的SKU
        if (possibleSkus.count == 1) {
            self.selectedSkuId = [possibleSkus anyObject];
            NSLog(@"已选中SKU: %@", self.selectedSkuId);
        } else {
            self.selectedSkuId = nil;
        }
    } else {
        self.selectedSkuId = nil;
    }
}

#pragma mark - Getter and Setter
- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [[UIImageView alloc] init];
    }
    return _imgView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
    }
    return _nameLabel;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] init];
    }
    return _priceLabel;
}

//- (UILabel *)limitLabel {
//    if (!_limitLabel) {
//        _limitLabel = [[UILabel alloc] init];
//    }
//    return _limitLabel;
//}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.minimumLineSpacing = 10;
        layout.minimumInteritemSpacing = 10;
        layout.sectionInset = UIEdgeInsetsMake(10, 0, 20, 0);
        layout.headerReferenceSize = CGSizeMake(self.view.bounds.size.width, 40);
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsVerticalScrollIndicator = NO;
        
        [_collectionView registerClass:[PDSpecOptionCell class] forCellWithReuseIdentifier:@"PDSpecOptionCell"];
        [_collectionView registerClass:[PDSpecSectionHeaderView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"PDSpecSectionHeaderView"];
    }
    return _collectionView;
}

- (UIStackView *)bottomStackView {
    if (!_bottomStackView) {
        _bottomStackView = [[UIStackView alloc] init];
    }
    return _bottomStackView;
}

- (UIButton *)addCartBtn {
    if (!_addCartBtn) {
        _addCartBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    }
    return _addCartBtn;
}

- (UIButton *)buyBtn {
    if (!_buyBtn) {
        _buyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    }
    return _buyBtn;
}

- (NSMutableDictionary<NSNumber *,NSString *> *)selectedSpecs {
    if (!_selectedSpecs) {
        _selectedSpecs = [NSMutableDictionary dictionary];
    }
    return _selectedSpecs;
}

- (NSMutableDictionary<NSNumber *,NSIndexPath *> *)selectedIndexpath {
    if (!_selectedIndexpath) {
        _selectedIndexpath = [NSMutableDictionary dictionary];
    }
    return _selectedIndexpath;
}

@end
