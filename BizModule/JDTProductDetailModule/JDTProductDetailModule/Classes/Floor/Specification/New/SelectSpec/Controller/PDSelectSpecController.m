//
//  PDSelectSpecController.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/22.
//

#import "PDSelectSpecController.h"
#import "JDISVPDAttributeFloorMacro.h"

@import JDTCommonToolModule.PDSpecOptionCell;

@interface PDSelectSpecController () <UICollectionViewDelegateFlowLayout, UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) UIImageView *imgView;

@property (nonatomic, strong) UILabel *nameLabel;

@property (nonatomic, strong) KAPriceLabel *priceLabel;
/// 限购提示
@property (nonatomic, strong) UILabel *limitBuyLabel;

@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UIStackView *bottomStackView;

@property (nonatomic, strong) UIButton *addCartBtn;

@property (nonatomic, strong) UIButton *buyBtn;
/// 已选择的规格 key: dimNumber, value: 选中的文本
@property (nonatomic, strong) NSMutableDictionary <NSNumber *, NSString *> *selectedSpecs;
/// 已选择的索引路径 key: dimNumber, value: indexPath
@property (nonatomic, strong) NSMutableDictionary <NSNumber *, NSIndexPath *> *selectedIndexpath;
/// 选中的SKU ID
@property (nonatomic, copy) NSString *selectedSkuId;

@end

@implementation PDSelectSpecController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupHeaderView];
    [self setupFooterView];
    [self setupSpecCollectionView];
    
    [self loadData];
}

- (void)setupHeaderView {
    [self.view addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(108, 108));
    }];
    [self.view addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.equalTo(self.view);
        make.left.equalTo(self.imgView.mas_right).offset(12);
        make.height.mas_equalTo(42);
    }];
    [self.view addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.right.equalTo(self.view);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(21);
    }];
    [self.view addSubview:self.limitBuyLabel];
    [self.limitBuyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.right.equalTo(self.view).offset(-18);
        make.top.equalTo(self.priceLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(16);
    }];
}

- (void)setupSpecCollectionView {
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(self.imgView.mas_bottom).offset(24);
        make.bottom.equalTo(self.bottomStackView.mas_top);
    }];
}

- (void)setupFooterView {
    [self.view addSubview:self.bottomStackView];
    [self.bottomStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        make.height.mas_equalTo(50);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
    }];
    
    self.bottomStackView.axis = UILayoutConstraintAxisHorizontal;
    self.bottomStackView.alignment = UIStackViewAlignmentFill;
    self.bottomStackView.distribution = UIStackViewDistributionFillEqually;
    
    // 设置 stackView 的内边距
    self.bottomStackView.layoutMargins = UIEdgeInsetsMake(5, 0, 5, 0);
    self.bottomStackView.layoutMarginsRelativeArrangement = YES;
    
    [self.bottomStackView addArrangedSubview:self.addCartBtn];
    [self.bottomStackView addArrangedSubview:self.buyBtn];
    
    [self.bottomStackView setCustomSpacing:6 afterView:self.addCartBtn];
}

- (void)loadData {
    NSString *imageUrl = self.imageModel.imageList.firstObject;
    [self.imgView jdcd_setImage:imageUrl placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    self.nameLabel.text = self.baseModel.skuName;
    [self.priceLabel configTextWithPrice:self.priceModel.sellPrice.floatValue middleLinePrice:self.priceModel.originalPrice.floatValue];
    NSUInteger limitMinNum = self.limitModel.limitMinNum;
    NSUInteger limitMaxNum = self.limitModel.limitMaxNum;
    if (limitMinNum > 0) {
        if (limitMaxNum > 0) {
            self.limitBuyLabel.text = [NSString stringWithFormat:@"每笔订单%zd件起购，最多可买%zd件", limitMinNum, limitMaxNum];
        } else {
            self.limitBuyLabel.text = [NSString stringWithFormat:@"每笔订单%zd件起购", limitMinNum];
        }
    } else {
        if (limitMaxNum > 0) {
            self.limitBuyLabel.text = [NSString stringWithFormat:@"每笔订单最多购买%zd件商品", limitMaxNum];
        } else {
            self.limitBuyLabel.text = @"";
        }
    }

    // 如果有预设的SKU ID，自动选中对应的规格
    if (self.skuId.length > 0) {
        [self autoSelectSpecsForSkuId:self.skuId];
    }

    [self.collectionView reloadData];
    [self updateButtonStates];
}

#pragma mark - <UICollectionViewDataSource>
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.saleAttrList.count;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.saleAttrList[section].selectedDimBtnPartVoList.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    PDProductSpecItemModel *model = self.saleAttrList[indexPath.section];
    PDProductSpecItemDetailModel *detailModel = model.selectedDimBtnPartVoList[indexPath.item];
    
    PDSpecOptionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PDSpecOptionCell" forIndexPath:indexPath];
    cell.titleLabel.text = detailModel.text;
    
    BOOL isNoStock = (detailModel.stockState == StockStateNoStock);
    BOOL isUnavailable = ![self isOptionAvailable:detailModel forDimNumber:model.dimNumber];
    NSIndexPath *selectedIndexPath = self.selectedIndexpath[model.dimNumber];
    BOOL isSelected = (selectedIndexPath && selectedIndexPath.item == indexPath.item);
    
    if (isNoStock) {
        cell.state = isSelected ? PDSpecOptionCellStateNoStockSelected : PDSpecOptionCellStateNoStockNormal;
    } else if (isUnavailable) {
        cell.state = PDSpecOptionCellStateUnavailable;
    } else if (isSelected) {
        cell.state = PDSpecOptionCellStateSelected;
    } else {
        cell.state = PDSpecOptionCellStateNormal;
    }
    
    return cell;
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    if (kind == UICollectionElementKindSectionHeader) {
        PDSpecSectionHeaderView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"PDSpecSectionHeaderView" forIndexPath:indexPath];
        headerView.sectionLabel.text = self.saleAttrList[indexPath.section].dimTitle;
        return headerView;
    }
    return nil;
}

#pragma mark - <UICollectionViewDelegateFlowLayout>
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *text = self.saleAttrList[indexPath.section].selectedDimBtnPartVoList[indexPath.item].text;
    CGSize size = [text sizeWithAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12]}];
    return CGSizeMake(size.width + 30, 30);
}

#pragma mark - <UICollectionViewDelegate>
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    PDProductSpecItemModel *model = self.saleAttrList[indexPath.section];
    PDProductSpecItemDetailModel *detailModel = model.selectedDimBtnPartVoList[indexPath.item];
    
    PDSpecOptionCell *cell = (PDSpecOptionCell *)[collectionView cellForItemAtIndexPath:indexPath];
    
    switch (cell.state) {
        case PDSpecOptionCellStateUnavailable: {
            // 如果是不可选或无货状态，则不响应选择
            [collectionView deselectItemAtIndexPath:indexPath animated:NO];
            return;
        }
        case PDSpecOptionCellStateSelected:
        case PDSpecOptionCellStateNoStockSelected: {
            [self.selectedSpecs removeObjectForKey:model.dimNumber];
            [self.selectedIndexpath removeObjectForKey:model.dimNumber];
            break;
        }
        case PDSpecOptionCellStateNormal:
        case PDSpecOptionCellStateNoStockNormal: {
            self.selectedSpecs[model.dimNumber] = detailModel.text;
            self.selectedIndexpath[model.dimNumber] = indexPath;
            break;
        }
        default:
            break;
    }
    
    [self updateAvailableOptions];
    [self determineSelectedSku];
    [self updateButtonStates];
}

- (void)updateAvailableOptions {
    [self.collectionView reloadData];
}

- (BOOL)isOptionAvailable:(PDProductSpecItemDetailModel *)model forDimNumber:(NSNumber *)number {
    // 如果没有选择任何规格，所有选项都可用
    if (self.selectedSpecs.count == 0) {
        return YES;
    }
    // 检查是否与已选规格兼容
    for (NSString *sku in model.skuList) {
        BOOL isCompatible = YES;
        
        // 检查该SKU是否与所有已选规格兼容
        for (NSNumber *selectedDimNumber in self.selectedSpecs.allKeys) {
            // 跳过当前正在检查的维度
            if ([selectedDimNumber isEqualToNumber:number]) {
                continue;
            }
            
            NSString *selectedValue = self.selectedSpecs[selectedDimNumber];
            BOOL foundMatch = NO;
            
            // 在所有规格中查找匹配的维度和值
            for (PDProductSpecItemModel *item in self.saleAttrList) {
                if ([item.dimNumber isEqualToNumber:selectedDimNumber]) {
                    for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                        if ([detail.text isEqualToString:selectedValue]) {
                            // 检查SKU是否在该选项的SKU列表中
                            if ([detail.skuList containsObject:sku]) {
                                foundMatch = YES;
                                break;
                            }
                        }
                    }
                    break;
                }
            }
            
            if (!foundMatch) {
                isCompatible = NO;
                break;
            }
        }
        
        if (isCompatible) {
            return YES;
        }
    }
    return NO;
}

- (void)determineSelectedSku {
    // 如果所有规格都已选择，确定最终SKU
    if (self.selectedSpecs.count == self.saleAttrList.count) {
        NSMutableSet *possibleSkus = nil;
        
        // 遍历所有已选规格，找出符合所有规格的SKU
        for (NSNumber *dimNumber in self.selectedSpecs.allKeys) {
            NSString *selectedValue = self.selectedSpecs[dimNumber];
            
            for (PDProductSpecItemModel *item in self.saleAttrList) {
                if ([item.dimNumber isEqualToNumber:dimNumber]) {
                    for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                        if ([detail.text isEqualToString:selectedValue]) {
                            NSArray *skuList = detail.skuList;
                            NSMutableSet *skuSet = [NSMutableSet setWithArray:skuList];
                            
                            if (possibleSkus == nil) {
                                possibleSkus = skuSet;
                            } else {
                                [possibleSkus intersectSet:skuSet];
                            }
                            
                            break;
                        }
                    }
                }
            }
        }
        
        // 如果找到唯一的SKU，则设置为选中的SKU
        if (possibleSkus.count == 1) {
            self.selectedSkuId = [possibleSkus anyObject];
            NSLog(@"已选中SKU: %@", self.selectedSkuId);
        } else {
            self.selectedSkuId = nil;
        }
    } else {
        self.selectedSkuId = nil;
    }
}

#pragma mark - Auto Selection Methods

- (void)autoSelectSpecsForSkuId:(NSString *)skuId {
    if (!skuId.length || !self.saleAttrList.count) {
        return;
    }

    // 清空之前的选择
    [self.selectedSpecs removeAllObjects];
    [self.selectedIndexpath removeAllObjects];

    // 遍历所有规格维度，找到包含目标SKU的选项
    for (NSInteger section = 0; section < self.saleAttrList.count; section++) {
        PDProductSpecItemModel *specItem = self.saleAttrList[section];

        for (NSInteger item = 0; item < specItem.selectedDimBtnPartVoList.count; item++) {
            PDProductSpecItemDetailModel *detailModel = specItem.selectedDimBtnPartVoList[item];

            // 检查该选项的SKU列表是否包含目标SKU
            if ([detailModel.skuList containsObject:skuId]) {
                // 找到匹配的选项，记录选择
                self.selectedSpecs[specItem.dimNumber] = detailModel.text;
                self.selectedIndexpath[specItem.dimNumber] = [NSIndexPath indexPathForItem:item inSection:section];
                break; // 找到后跳出内层循环
            }
        }
    }

    // 验证选择的规格组合是否能唯一确定目标SKU
    [self validateAutoSelection:skuId];

    // 设置选中的SKU
    self.selectedSkuId = skuId;

    NSLog(@"自动选中规格完成，SKU: %@, 选中规格: %@", skuId, self.selectedSpecs);
}

- (void)validateAutoSelection:(NSString *)targetSkuId {
    // 验证当前选择的规格组合是否能唯一确定目标SKU
    NSMutableSet *possibleSkus = nil;

    for (NSNumber *dimNumber in self.selectedSpecs.allKeys) {
        NSString *selectedValue = self.selectedSpecs[dimNumber];

        for (PDProductSpecItemModel *item in self.saleAttrList) {
            if ([item.dimNumber isEqualToNumber:dimNumber]) {
                for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                    if ([detail.text isEqualToString:selectedValue]) {
                        NSArray *skuList = detail.skuList;
                        NSMutableSet *skuSet = [NSMutableSet setWithArray:skuList];

                        if (possibleSkus == nil) {
                            possibleSkus = skuSet;
                        } else {
                            [possibleSkus intersectSet:skuSet];
                        }
                        break;
                    }
                }
                break;
            }
        }
    }

    // 检查是否能唯一确定目标SKU
    if (![possibleSkus containsObject:targetSkuId]) {
        NSLog(@"警告：自动选择的规格组合无法确定目标SKU: %@", targetSkuId);
        // 清空选择
        [self.selectedSpecs removeAllObjects];
        [self.selectedIndexpath removeAllObjects];
    } else if (possibleSkus.count > 1) {
        NSLog(@"警告：自动选择的规格组合对应多个SKU: %@，目标SKU: %@", possibleSkus, targetSkuId);
    }
}

#pragma mark - Button Actions

- (void)updateButtonStates {
    // 检查是否所有规格都已选择
    BOOL allSpecsSelected = (self.selectedSpecs.count == self.saleAttrList.count);

    // 更新按钮状态
    self.addCartBtn.enabled = allSpecsSelected;
    self.buyBtn.enabled = allSpecsSelected;

    // 更新按钮透明度
    self.addCartBtn.alpha = allSpecsSelected ? 1.0 : 0.6;
    self.buyBtn.alpha = allSpecsSelected ? 1.0 : 0.6;
}

- (void)addToCartButtonTapped {
    if (self.selectedSkuId.length > 0) {
        if (self.addToCartCallback) {
            self.addToCartCallback(self.selectedSkuId);
        }
        NSLog(@"加入购物车，SKU: %@", self.selectedSkuId);
    } else {
        NSLog(@"请先选择完整的商品规格");
    }
}

- (void)buyNowButtonTapped {
    if (self.selectedSkuId.length > 0) {
        if (self.buyNowCallback) {
            self.buyNowCallback(self.selectedSkuId);
        }
        NSLog(@"立即购买，SKU: %@", self.selectedSkuId);
    } else {
        NSLog(@"请先选择完整的商品规格");
    }
}

#pragma mark - Getter and Setter
- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [[UIImageView alloc] init];
    }
    return _imgView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
    }
    return _nameLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
    }
    return _priceLabel;
}

- (UILabel *)limitBuyLabel {
    if (!_limitBuyLabel) {
        _limitBuyLabel = [[UILabel alloc] init];
    }
    return _limitBuyLabel;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.minimumLineSpacing = 10;
        layout.minimumInteritemSpacing = 10;
        layout.sectionInset = UIEdgeInsetsMake(10, 0, 20, 0);
        layout.headerReferenceSize = CGSizeMake(self.view.bounds.size.width, 40);
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsVerticalScrollIndicator = NO;
        
        [_collectionView registerClass:[PDSpecOptionCell class] forCellWithReuseIdentifier:@"PDSpecOptionCell"];
        [_collectionView registerClass:[PDSpecSectionHeaderView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"PDSpecSectionHeaderView"];
    }
    return _collectionView;
}

- (UIStackView *)bottomStackView {
    if (!_bottomStackView) {
        _bottomStackView = [[UIStackView alloc] init];
    }
    return _bottomStackView;
}

- (UIButton *)addCartBtn {
    if (!_addCartBtn) {
        _addCartBtn = [UIButton buttonWithType:UIButtonTypeCustom];

        // 设置标题
        [_addCartBtn setTitle:@"加入购物车" forState:UIControlStateNormal];

        // 设置字体
        _addCartBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium];

        // 设置颜色 - 橙色边框按钮
        [_addCartBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"] forState:UIControlStateNormal];
        [_addCartBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"] forState:UIControlStateDisabled];
        [_addCartBtn setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];

        // 设置边框
        _addCartBtn.layer.borderWidth = 1.0;
        _addCartBtn.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"].CGColor;

        // 设置圆角
        _addCartBtn.layer.cornerRadius = 20.0;
        _addCartBtn.layer.masksToBounds = YES;

        // 添加点击事件
        [_addCartBtn addTarget:self action:@selector(addToCartButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    }
    return _addCartBtn;
}

- (UIButton *)buyBtn {
    if (!_buyBtn) {
        _buyBtn = [UIButton buttonWithType:UIButtonTypeCustom];

        // 设置标题
        [_buyBtn setTitle:@"立即购买" forState:UIControlStateNormal];

        // 设置字体
        _buyBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium];

        // 设置颜色 - 红色填充按钮
        [_buyBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateNormal];
        [_buyBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"] forState:UIControlStateDisabled];
        [_buyBtn setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"]];

        // 设置圆角
        _buyBtn.layer.cornerRadius = 20.0;
        _buyBtn.layer.masksToBounds = YES;

        // 添加点击事件
        [_buyBtn addTarget:self action:@selector(buyNowButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    }
    return _buyBtn;
}

- (NSMutableDictionary<NSNumber *,NSString *> *)selectedSpecs {
    if (!_selectedSpecs) {
        _selectedSpecs = [NSMutableDictionary dictionary];
    }
    return _selectedSpecs;
}

- (NSMutableDictionary<NSNumber *,NSIndexPath *> *)selectedIndexpath {
    if (!_selectedIndexpath) {
        _selectedIndexpath = [NSMutableDictionary dictionary];
    }
    return _selectedIndexpath;
}

@end
