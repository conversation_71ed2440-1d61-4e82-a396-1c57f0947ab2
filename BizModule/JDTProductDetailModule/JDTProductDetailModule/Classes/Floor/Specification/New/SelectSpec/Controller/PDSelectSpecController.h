//
//  PDSelectSpecController.h
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/22.
//

//  商品规格选择（尺寸、颜色等）

#import <UIKit/UIKit.h>

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@interface PDSelectSpecController : UIViewController

@property (nonatomic, strong) PDProductBaseModel *baseModel;

@property (nonatomic, strong) PDProductImageModel *imageModel;

@property (nonatomic, strong) PDPriceModel *priceModel;

@property (nonatomic, strong) PDLimitModel *limitModel;

@property (nonatomic, copy) NSArray <PDProductSpecItemModel *> *saleAttrList;

@property (nonatomic, copy) NSString *skuId;

/// 加入购物车回调
@property (nonatomic, copy) void (^addToCartCallback)(NSString *skuId);

/// 立即购买回调
@property (nonatomic, copy) void (^buyNowCallback)(NSString *skuId);

@end

NS_ASSUME_NONNULL_END
