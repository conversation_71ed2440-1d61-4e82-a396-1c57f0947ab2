//
//  JDISVPDAddressFloorModule.m
//  JDISVProductDetailSDKModule
//
//  Created by 张令浩 on 2022/2/21.
//

#import "JDISVPDAddressFloorModule.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVPDAttributeCommonModel.h"
#import "NSBundle+JDISVPDBundle.h"

@import JDTCommonToolModule;

JDISVRegisterFloorModule(KaProductAddressFloor, JDISVPDAddressFloorModule)

@interface JDISVPDAddressFloorModule ()
//@property (nonatomic, copy) NSString *loc; /**< 支持的LOC类型 1 ：到店服务、2：在线服务、不是loc 就不返回改字段*/
@property (nonatomic, assign) BOOL isLoc;
@property (nonatomic, assign) BOOL showStoreService;
@property (nonatomic, assign) CGFloat height;
@end

@implementation JDISVPDAddressFloorModule
- (UINib *)tableViewFloorNib {
    return [UINib nibWithNibName:@"JDISVPDAddressFloorCell" bundle:[NSBundle isv_pd_currentBundle]];
}

- (CGFloat)floorHeight {
    if (self.isLoc) {
        return CGFLOAT_MIN;
    }
    return self.height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.height = 56;
    
    NSDictionary *floorData = data[@"data"] ? : @{};
    
    NSDictionary *commonModule = [floorData objectForKey:@"commonModule"];
    JDISVPDAttributeCommonModel *common = [JDISVPDAttributeCommonModel yy_modelWithDictionary:commonModule];
    self.skuId = common.skuId;
    
//    self.loc = [self locValueFrom:(floorData[@"commonModule"] ? : @{})];
    self.isLoc = common.commonBasic.isLoc;

    if ([data isKindOfClass:NSDictionary.class]) {
        NSDictionary *addressModule = floorData[@"addressModule"] ? : @{};
        JDTAddressItemModel *model = [JDTAddressItemModel yy_modelWithDictionary:addressModule];
        
        if (model.shippingAddress.length) {
            self.addressText = model.shippingAddress;
        } else {
            // 地址兜底
            self.addressText = [[JDISVPlatformService sharedService] getDefaultAddress].fullAddress;
        }
        
//        self.promiseTimeText = model.addressCore.promise;
    }
    
    // 本地生活 到店服务标
    NSDictionary *floorDic = data[@"data"] ? : @{};
    NSDictionary *productTitleModuleDic = floorDic[@"productTitleModule"] ? : @{};
    if ([productTitleModuleDic isKindOfClass:NSDictionary.class]) {
        if (productTitleModuleDic.allKeys.count == 0) return;
        NSArray *productTagPartList = [productTitleModuleDic objectForKey:@"productTagPartList"];
        if ([productTagPartList isKindOfClass:NSArray.class] && [productTagPartList count] > 0) {
            NSMutableArray <NSString *>*tags = [NSMutableArray new];
            for (NSDictionary *tag in productTagPartList) {
                [tags addObject:tag[@"info"][@"C-P#productTag&productTag"][@"tagText"]];
            }
            if ([tags count]) {
                self.showStoreService = YES;
            }
        }
    }
    
    //配送时间
    if (self.promiseTimeText) {
        self.height += 2 + 17;
    }
}

@end
