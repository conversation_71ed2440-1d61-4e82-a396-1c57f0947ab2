//
//  JDISVPDAddressFloorCell.m
//  JDISVProductDetailSDKModule
//
//  Created by 张令浩 on 2022/2/21.
//

#import "JDISVPDAddressFloorCell.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>

#import "JDISVPDAddressFloorModule.h"
#import "JDISVPDAttributeFloorMacro.h"

#import "UIImage+JDISVPDImage.h"

@interface JDISVPDAddressFloorCell ()
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *titleLabelWidth;
@property (weak, nonatomic) IBOutlet UIImageView *addressIcon;
@property (weak, nonatomic) IBOutlet UILabel *addressLabel;
@property (weak, nonatomic) IBOutlet UIImageView *moreImageView;
@property (weak, nonatomic) IBOutlet UILabel *promiseTimeLabel;

@property (nonatomic, strong) UIView *separator;

@property (nonatomic, strong) JDISVPDAddressFloorModule *viewModel;
@end

@implementation JDISVPDAddressFloorCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.titleLabel.text = ProductDetailSDKL(@"pd_base_floor_address");
    self.titleLabelWidth.constant = FloorTitleFixWidth;
    self.titleLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    self.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    self.addressIcon.image = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:(CGSizeMake(12, 12)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    self.moreImageView.contentMode = UIViewContentModeCenter;
    self.moreImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.addressLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.addressLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.promiseTimeLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.promiseTimeLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
    
    UIView *separator = [UIView new];
    separator.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self.contentView addSubview:separator];
    [separator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(62.5);
        make.top.trailing.equalTo(self.contentView);
        make.height.mas_equalTo(1.0/[UIScreen mainScreen].scale);
    }];
    self.separator = separator;
    
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
    self.addressLabel.text = self.viewModel.addressText;
    self.promiseTimeLabel.text = self.viewModel.promiseTimeText;
}

#pragma mark event
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]) {
        
        NSMutableDictionary *arg = [NSMutableDictionary dictionary];

        if ([PlatformService getUserIsLogin]) {
            JDTAddressItemModel *addr = [PlatformService getDefaultAddress];
            if (addr) {
                [arg setObject:addr forKey:@"address"];
            }
        } else {
            arg = nil;
        }
        NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeAddress];
        NSString* routerStr = [NSString stringWithFormat:@"router://%@/cascaderAddressViewController",module];
        UIViewController *cascaderController  = [JDRouter openURL:routerStr arg:arg error:nil completion:^(JDTAddressItemModel *object) {
            // 选择完成
            [PlatformService setDefaultAddress:object];
            self.addressLabel.text = object.fullAddress;
            
            NSMutableDictionary *tempParams = [NSMutableDictionary dictionary];
            [tempParams setObject:self.viewModel.skuId ? : @"" forKey:@"skuId"];
            JDCDISVAction *action = [JDCDISVAction actionWithType:kJDCDISVFloorReloadDataAction];
            action.value = [tempParams copy];
            [self isv_sendAction:action];
        }];
        KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:controller];
        delegate.contentHeight = UIScreen.mainScreen.bounds.size.height-90;
        delegate.defultHeader.titleLabel.text = ProductDetailSDKL(@"pd_base_floor_address");
        cascaderController.transitioningDelegate = delegate;
        if (cascaderController) {
            [controller presentViewController:cascaderController animated:YES completion:nil];
        }
        return YES;
    }
    return NO;
}


@end
