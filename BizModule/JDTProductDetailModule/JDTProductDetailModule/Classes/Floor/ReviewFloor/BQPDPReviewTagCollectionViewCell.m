//
//  BQPDPReviewTagCollectionViewCell.m
//  BQProductDetailModule
//
//  Created by 龚阳 on 2020/10/8.
//  Copyright © 2020 JD. All rights reserved.
//

#import "BQPDPReviewTagCollectionViewCell.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>

@implementation BQPDPReviewTagCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.contentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    UILabel *title = [UILabel new];
    title.numberOfLines = 0;
    title.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    title.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    [self.contentView addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(0, 12, 0, 12));
    }];
    self.itemTitle = title;
}

@end
