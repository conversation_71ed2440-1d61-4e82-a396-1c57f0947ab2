//
//  JDISVPDPromotionCouponFloorCell.m
//  BQProductDetailModule
//
//  Created by 龚阳 on 2020/10/1.
//  Copyright © 2020 JD. All rights reserved.
//

#import "JDISVPDPromotionCouponFloorCell.h"

#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVPDPromotionDefaultMacro.h"
#import "JDISVPDPromotionCouponCollectionFlowLayout.h"
#import "JDISVPDPromotionCouponCellViewModel.h"
#import "JDISVPDPromotionCouponItemSubItemCell.h"
#import "UIImage+JDISVPDImage.h"
#import "JDISVPDPromotionCouponFloorModule.h"
#import "JDISVPDPromotionPopupController.h"
#import "JDISVPDPromotionCommonService.h"
#import "JDISVPDAttributeCommonModel.h"

@interface JDISVPDPromotionCouponFloorCell () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (weak, nonatomic) IBOutlet UIImageView *iconImageView;
@property (weak, nonatomic) IBOutlet UILabel *itemTitle;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *titleViewWidth;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrow;

@property (nonatomic, strong) JDISVPDPromotionCouponFloorModule *viewModule;
@property (nonatomic, strong) JDISVPDPromotionCouponCellViewModel *viewModel;

@property (nonatomic, weak) UIViewController *detailController;
@property (nonatomic, copy) NSString *reopenPopup;
@end

@implementation JDISVPDPromotionCouponFloorCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d"];
    self.itemTitle.textColor = color;
    self.itemTitle.superview.backgroundColor = [color colorWithAlphaComponent:0.07];
    self.itemTitle.superview.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    self.rightArrow.contentMode = UIViewContentModeCenter;
    self.rightArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    JDISVPDPromotionCouponCollectionFlowLayout *layout = self.collectionView.collectionViewLayout;
    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    layout.minimumLineSpacing = 10;
    layout.minimumInteritemSpacing = 6;
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    [self.collectionView registerClass:[JDISVPDPromotionCouponItemSubItemCell class] forCellWithReuseIdentifier:@"JDISVPDPromotionCouponItemSubItemCell"];
    self.collectionView.userInteractionEnabled = NO;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    JDISVPDPromotionCouponFloorModule *module = floorModel;
    self.viewModule = module;
    if (module.datasource.count > 0) {
        self.viewModel = module.datasource.firstObject;
        self.itemTitle.text = self.viewModel.title;
        CGFloat itemWidth = [self.itemTitle.text jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18) textFont:[UIFont systemFontOfSize:12] maxNumberOfLines:1].width;
        self.titleViewWidth.constant = itemWidth;
        [self.collectionView reloadData];
        
//        if (KSAAPP) {
//            self.itemTitle.hidden = YES;
//            self.iconImageView.hidden = YES;
//            self.iconImageView.image = self.viewModel.iconImage;
//            self.iconImageView.contentMode = UIViewContentModeCenter;
//            self.iconImageView.superview.backgroundColor = [UIColor clearColor];
//            self.titleViewWidth.constant = 0;
//        }
    }
}

//- (void)viewDidAppear {
//    if ([self.reopenPopup isEqualToString:@"promo"]) {
//        dispatch_async(dispatch_get_main_queue(), ^{
//            [self showPromoPopup];
//        });
//    }
//    self.reopenPopup = nil;
//}

#pragma mark - delegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.viewModel.coupons.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVPDPromotionCouponItemSubItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JDISVPDPromotionCouponItemSubItemCell" forIndexPath:indexPath];
    NSString *title = self.viewModel.coupons[indexPath.item];
    cell.itemTitle.text = title;
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = [self.viewModel.couponTitleWidthArray[indexPath.item] doubleValue];
    return CGSizeMake(width, 18);
}

#pragma mark event
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]) {
//        self.detailController = controller;
//        [self showPromoPopup];
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVPDPromotionCouponFloorClick" broadcastAction:YES];
        [self isv_sendAction:action];
    }
    return YES;
}

@end
