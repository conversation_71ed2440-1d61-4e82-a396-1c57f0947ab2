//
//  NewProductDetailController.m
//  JDISVProductDetailSDKModule
//
//  Created by lvchenzhu.1 on 2025/5/8.
//

#import "NewProductDetailController.h"
#import "JDISVProductDetailViewModel.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>

@import JDBRouterModule;
@import JDISVPlatformModule;
@import JDISVFloorRenderModule;
@import JDISVKAUIKitModule;
@import JDISVThemeModule;

@interface NewProductDetailController () <JDCDISVActionDelegate>

@property (nonatomic, strong) KANavigationBar *navigationBar;

@property (nonatomic, strong) KANavigationBarSearchView *navigationBarSearchView;

@property (nonatomic, strong) JDISVFloorListView *floorListView;

@property (nonatomic, strong) JDISVProductDetailViewModel *viewModel;

@end

@implementation NewProductDetailController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self loadData];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];
    
//    self.navigationBar.decorator.backgroundColor()
//    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
//        [self.navigationController popViewControllerAnimated:YES];
//    })
//    .searchTitle(@"搜索", self)
//    .render();
//    [self.view addSubview:self.navigationBar];
//    CGFloat navHeight = [UIWindow ka_uikit_navigationHeight];
//    [self.navigationBar mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.leading.trailing.equalTo(self.view);
//        make.height.mas_equalTo(navHeight);
//    }];
    
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.floorListView.listInsets = UIEdgeInsetsMake(0, w3, 0, w3);
    self.floorListView.scrollView.showsVerticalScrollIndicator = NO;
    self.floorListView.actionDelegate = self;
    
    [self.view addSubview:self.floorListView];
    [self.floorListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.leading.trailing.equalTo(self.view);
        make.top.equalTo(self.view);
//        make.top.equalTo(self.navigationBar.mas_bottom);
    }];
    
    NSArray *preloadingData = [self.viewModel preloadingFloorData];
    [self.floorListView configData:preloadingData allFloorOriginData:nil];
    
    [self.floorListView reloadData];
    
//    self.view.isv_actionDelegate = self.listView;
    
//    [self.view bringSubviewToFront:self.navigationBar];
//    self.navigationBarSearchView = _navigationBar.navigationItem.titleView;
//    self.navigationBarSearchView.textField.delegate = self;
//    self.navigationBarSearchView.textField.textColor =  [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
//    self.navigationBarSearchView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.02];
//    self.navigationBarSearchView.textField.clearButtonMode = UITextFieldViewModeNever;
//    self.navigationBarSearchView.clearButtonMode = UITextFieldViewModeNever;
}

- (void)loadData {
    @weakify(self)
    [[self.viewModel newRequestDataWithSkuId:self.sku params:@{}] subscribeCompleted:^{
        @strongify(self)
        [self.floorListView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        
        [self.floorListView reloadData];
        
        [[self.floorListView requestAsyncData] subscribeCompleted:^{
            @strongify(self)
            [self.floorListView reloadData];
        }];
        
    }];
}

- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    NSString* module =  [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeSearch];
    NSString* router = [NSString stringWithFormat:@"router://%@/searchInputController",module];
    UIViewController *viewController = [JDRouter openURL:router arg:@{@"keyword":ProductDetailSDKL(@"base_pd_lookingfor")} error:nil completion:nil];
    [self.navigationController pushViewController:viewController animated:YES];
    return NO;
}

#pragma mark - Getter and Setter
- (KANavigationBar *)navigationBar {
    if (!_navigationBar) {
        _navigationBar = [[KANavigationBar alloc] init];
    }
    return _navigationBar;
}

- (JDISVFloorListView *)floorListView {
    if (!_floorListView) {
        _floorListView = [[JDISVFloorListView alloc] initWithFrame:CGRectZero ceiling:NO formPageId:KaProductDetailPageId];
        _floorListView.controller = self;
        _floorListView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _floorListView;
}

- (JDISVProductDetailViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[JDISVProductDetailViewModel alloc] init];
    }
    return _viewModel;
}

@end
