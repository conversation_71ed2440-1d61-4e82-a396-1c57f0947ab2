//
//  JDISVProductDetailSDKModule.m
//
//
// 组件输出类, 可引入JDRouter组件, 进行组件间通信

#import <Foundation/Foundation.h>
#import "JDISVProductDetailSDKModule.h"

#import "JDISVProductDetailController.h"
#import "JDISVPDAddtionalPurchaseViewController.h"
#import "JDISVProductStoreListViewController.h"
#import "BQPDPMediaPopupViewController.h"
#import "JDISVPDDMasterMapCollectionViewCellModel.h"
#import "NewProductDetailController.h"
#import "PDSelectSpecController.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

@import JDISVKAUIKitModule;

@implementation JDISVProductDetailSDKModule

JDROUTER_EXTERN_METHOD(JDISVProductDetailSDKModule, productDetailController, arg, callback) {
    // TEST:Juice 测试用代码！禁止commit!记得删除！
    NSString *skuId = arg[@"skuId"];
//    if ([skuId isEqualToString:@"167571"]) {
        // 显示新的商详页面
        NewProductDetailController *vc = [[NewProductDetailController alloc] init];
        // 小程序的商品(24680315)
        vc.sku = @"41041997";
        vc.hidesBottomBarWhenPushed = YES;
        return vc;
//    } else {
//        JDISVProductDetailController *productDetail = [[JDISVProductDetailController alloc] init];
//        productDetail.sku = arg[@"skuId"];
//
//        productDetail.hidesBottomBarWhenPushed = YES;
//
//        return productDetail;
//    }
    
    // 原代码
//    JDISVProductDetailController *productDetail = [[JDISVProductDetailController alloc] init];
//    productDetail.sku = arg[@"skuId"];
//    
//    productDetail.hidesBottomBarWhenPushed = YES;
//    
//    return productDetail;
}

JDROUTER_EXTERN_METHOD(JDISVProductDetailSDKModule, addtionalPurchaseViewController, arg, callback) {
    
    JDISVPDAddtionalPurchaseViewController *productDetail = [[JDISVPDAddtionalPurchaseViewController alloc] init];
    
    productDetail.hidesBottomBarWhenPushed = YES;
    
    return productDetail;
}

// 获取门店列表页面(必要入参如下)
// NSString *skuId = arg[@"skuId"];
// NSString *venderId = arg[@"venderId"];

JDROUTER_EXTERN_METHOD(JDISVProductDetailSDKModule, storeListViewController, arg, callback) {
    
    NSString *skuId = arg[@"skuId"];
    NSString *venderId = arg[@"venderId"];
    NSNumber *isLoc = arg[@"isLoc"] ?: @(NO);
    
    JDISVProductStoreListViewController *storeListViewController = 
    [JDISVProductStoreListViewController initWithSkuId:skuId
                                              venderId:venderId
                                               storeId:@"0"];
    storeListViewController.isLoc = isLoc.boolValue;
    storeListViewController.callBack = ^(NSString* storeId){
        if (callback) {
            callback(nil);
        }
    };
    return storeListViewController;
}

JDROUTER_EXTERN_METHOD(JDISVProductDetailSDKModule, bQPDPMediaPopupViewController, arg, callback) {
    
    NSArray *photos = arg[@"photos"];
    NSInteger index = [arg[@"index"] integerValue];
    UIViewController *topViewController = arg[@"topViewController"];
    
    NSMutableArray *imageUrls = [NSMutableArray arrayWithCapacity:0];
    for (NSString *imagePath in photos) {
        JDISVPDDMasterMapCollectionViewCellModel *model = [[JDISVPDDMasterMapCollectionViewCellModel alloc] init];
        model.imgUrl = imagePath;
        model.showVideo = NO;
        [imageUrls addObject:model];
    }
    
    BQPDPMediaPopupViewController *browsePhotoVC = [[BQPDPMediaPopupViewController alloc] init];
    browsePhotoVC.imageUrls = imageUrls;
    browsePhotoVC.index = index;
    [topViewController presentViewController:browsePhotoVC animated:NO completion:nil];
    return browsePhotoVC;
}

JDROUTER_EXTERN_METHOD(JDISVProductDetailSDKModule, selectSpecController, arg, callback) {
    PDSelectSpecController *vc = [[PDSelectSpecController alloc] init];
    vc.saleAttrList = arg[@"saleAttrList"];
    return vc;
}

@end
