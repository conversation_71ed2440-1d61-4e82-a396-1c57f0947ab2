//
//  JDISVAddressModule.m
//
//
// 组件输出类, 可引入JDRouter组件, 进行组件间通信

#import <Foundation/Foundation.h>
#import "JDISVAddressModule.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import "JDISVAddAddressViewController.h"
#import "JDISVAddressCascaderViewController.h"
#import "JDISVAddressMapViewController.h"
#import "JDISVStoreCascaderViewController.h"

#import "JDISVAddressModulUtil.h"
#import "JDISVAddressService.h"

#import "JDISVSwitchAddress.h"

#import "JDISVAddressListFloorController.h"
#import "JDISVQQLocService.h"

@implementation JDISVAddressModule

JDROUTER_EXTERN_METHOD(JDISVAddressModule, GetLocalInfo, arg, callback) {
    UIViewController* vc = arg[@"vc"];

    [JDRouter openURL:@"router://KSAAddressModule/GetLocalInfo" arg:arg error:nil completion:callback];
    
    if (KSAAPP) {
        return nil;
    }
    
    [[JDISVQQLocService inst] getLoc:vc callBack:^(id  _Nullable object) {
        if(callback){
            callback(object);
        }
    }];
    return nil;
};

// 获取地址列表视图控制器
JDROUTER_EXTERN_METHOD(JDISVAddressModule, addressListViewController, arg, callback) {
#if DEBUG
    
    JDISVAddressListFloorController* controller = [[JDISVAddressListFloorController alloc] init];
    controller.viewdidloadRequest = YES;
    controller.selectedAddressId = [arg objectForKey:@"addressId"];
    controller.canSelect = [[arg objectForKey:@"canSelect"] boolValue];
    NSNumber* from = arg[@"fromMySetting"];
    controller.fromMySetting = from.boolValue;
    controller.backVC = arg[@"backVC"];
    controller.addrCallback = ^(JDTAddressItemModel *addr) {
        if (callback && addr) {
            callback(@{@"info": addr});
        }
    };
    if (callback) {
        callback(@{@"viewController": controller});
    }
    return nil;
#endif
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(id  _Nullable object) {
        JDISVAddressListFloorController* controller = [[JDISVAddressListFloorController alloc] init];
        controller.viewdidloadRequest = YES;
        controller.selectedAddressId = [arg objectForKey:@"addressId"];
        controller.canSelect = [[arg objectForKey:@"canSelect"] boolValue];
        NSNumber* from = arg[@"fromMySetting"];
        controller.fromMySetting = from.boolValue;
        controller.addrCallback = ^(JDTAddressItemModel *addr) {
            if (callback && addr) {
                callback(@{@"info": addr});
            }
        };
        if (callback) {
            callback(@{@"viewController": controller});
        }
    }];
    return nil;
}

// 获取新增地址视图控制器
JDROUTER_EXTERN_METHOD(JDISVAddressModule, addAddressViewController, arg, callback) {
    [JDRouter openURL:@"router://KSAAddressModule/addAddressViewController" arg:arg error:nil completion:callback];
    
    if (KSAAPP) {
        return nil;
    }
    
    
#if DEBUG
    JDISVAddAddressViewController *controller = [[JDISVAddAddressViewController alloc] init];
    controller.addressAddSuccessCallback = ^(NSNumber * _Nonnull addrId) {
        if (callback) {
            callback(@{@"info": addrId});
        }
    };
    if (callback) {
        callback(@{@"viewController": controller});
    }
    return nil;
#endif
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(id  _Nullable object) {
        JDISVAddAddressViewController *controller = [[JDISVAddAddressViewController alloc] init];
        controller.addressAddSuccessCallback = ^(NSNumber * _Nonnull addrId) {
            if (callback) {
                callback(@{@"info": addrId});
            }
        };
        if (callback) {
            callback(@{@"viewController": controller});
        }
    }];
    
    
    return nil;
}

// 获取地址浮层视图控制器
JDROUTER_EXTERN_METHOD(JDISVAddressModule, cascaderAddressViewController, arg, callback) {
    if (KSAAPP) {
        return [JDRouter openURL:@"router://KSAAddressModule/cascaderAddressViewController" arg:arg error:nil completion:callback];
    }
    
    
    NSNumber *show = [arg objectForKey:@"showUserAddress"];
    NSNumber* limit = arg[@"limitLevel"];
    
    JDISVAddressCascaderViewController *cascaderController = [[JDISVAddressCascaderViewController alloc] initWithAddress:[arg objectForKey:@"address"] limit:limit.intValue];
    cascaderController.showUserAddress = show ? [show boolValue] : YES;
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel * _Nullable x) {
        if (callback) {
            callback(x);
        }
    }];
    return cascaderController;
}

// 获取定位视图控制器
JDROUTER_EXTERN_METHOD(JDISVAddressModule, addressLocationViewController, arg, callback) {
    if (KSAAPP) {
        return [JDRouter openURL:@"router://KSAAddressModule/addressLocationViewController" arg:arg error:nil completion:callback];;
    }
    
    JDISVAddressMapViewController *location  = [[JDISVAddressMapViewController alloc] init];
    location.addressLocCallback = ^(NSString * streetDAddressName) {
        if (callback) {
            callback(streetDAddressName);
        }
    };
    return location;
}

// 获取地址列表数据
JDROUTER_EXTERN_METHOD(JDISVAddressModule, addressList, arg, callback) {
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
        NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
        [JDRouter openURL:router arg:nil error:nil completion:^(id  _Nullable object) {
            [[[JDISVAddressService shareInstance] addressList] subscribeNext:^(NSArray<JDTAddressItemModel *> *  _Nullable x) {
//                NSArray *arr = [x jdcd_map:^id _Nonnull(JDTAddressItemModel *  _Nonnull obj) {
//                    return [JDISVAddressModulUtil addressFromAddressModel:obj];
//                }];
                if (callback) {
                    callback(@{@"addressList": x ?: @[]});
                }
            } error:^(NSError * _Nullable error) {
                if (callback) {
                    if (error) {
                        callback(@{@"error": error });
                    } else {
                        callback(nil);
                    }
                }
            }];
        }];
    return nil;
}

// 获取门店地址浮层视图控制器
JDROUTER_EXTERN_METHOD(JDISVAddressModule, storeAddressViewController, arg, callback) {
    JDISVStoreCascaderViewController *cascaderController  = [[JDISVStoreCascaderViewController alloc] initWithVenderId:[arg objectForKey:@"venderId"]];
    [cascaderController.delegate subscribeNext:^(id  _Nullable x) {
        if (callback) {
            callback(x);
        }
    }];
    return cascaderController;
}

// 浮层切换地址
JDROUTER_EXTERN_METHOD(JDISVAddressModule, switchAddress, arg, callback) {
    
#if DEBUG
    JDTAddressItemModel *addr = [arg objectForKey:@"address"];
    [[JDISVSwitchAddress shareInstance] switchAddressWithSelected:addr addCallback:^{
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        [dict setObject:@"create" forKey:@"type"];
        if (callback) {
            callback([dict copy]);
        }
    } selectCallback:^(JDTAddressItemModel * _Nonnull addr) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        [dict setObject:@"select" forKey:@"type"];
        [dict tm_safeSetObject:addr forKey:@"address"];
        if (callback) {
            callback([dict copy]);
        }
    } errorCallback:^(NSError * _Nonnull error) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        [dict setObject:@"error" forKey:@"type"];
        [dict tm_safeSetObject:error forKey:@"errorInfo"];
        if (callback) {
            callback([dict copy]);
        }
    }];
#endif
    
    NSString *moduleName = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeLogin)];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
    [JDRouter openURL:router arg:nil error:nil completion:^(id  _Nullable object) {
        JDTAddressItemModel *addr = [arg objectForKey:@"address"];
        [[JDISVSwitchAddress shareInstance] switchAddressWithSelected:addr addCallback:^{
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict setObject:@"create" forKey:@"type"];
            if (callback) {
                callback([dict copy]);
            }
        } selectCallback:^(JDTAddressItemModel * _Nonnull addr) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict setObject:@"select" forKey:@"type"];
            [dict tm_safeSetObject:addr forKey:@"address"];
            if (callback) {
                callback([dict copy]);
            }
        } errorCallback:^(NSError * _Nonnull error) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            [dict setObject:@"error" forKey:@"type"];
            [dict tm_safeSetObject:error forKey:@"errorInfo"];
            if (callback) {
                callback([dict copy]);
            }
        }];
    }];
    return nil;
}
    
@end
