//
//  JDISVSwitchAddressCell.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/30.
//

#import "JDISVSwitchAddressCell.h"

#import "JDISVAddressModulUtil.h"

#import <JDISVYYTextModule/JDISVYYTextModule-umbrella.h>

@interface JDISVSwitchAddressCell ()

@property (weak, nonatomic) IBOutlet UIButton *selectedButton;
@property (weak, nonatomic) IBOutlet YYLabel *addressName;

@end

@implementation JDISVSwitchAddressCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    [self.selectedButton setImage:[UIImage ka_iconWithName:JDIF_ICON_RADIO imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"]] forState:(UIControlStateNormal)];
    [self.selectedButton setImage:[UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] forState:(UIControlStateSelected)];
    
    self.addressName.numberOfLines = 2;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)configureAddress:(JDTAddressItemModel *)addr select:(BOOL)select {
    BOOL isDef = addr.defAddress;
    NSString *tag = nil;
    if ([addr.tagSource isEqual:@2]) {
        tag = addr.tag;
    } else if ([addr.tagSource isEqual:@1]) {
        // TODO:Juice  接口可能需要新增一个字段来表示
//        if ([addr.tagRet isEqual:@1]) {
//            tag = AddressL(@"address_home_tag");
//        } else if ([addr.tagRet isEqual:@2]) {
//            tag = AddressL(@"address_school_tag");
//        } else if ([addr.tagRet isEqual:@3]) {
//            tag = AddressL(@"address_company_tag");
//        }
        tag = addr.tag;
    }
    UIView *heading = nil;
    CGFloat x = 0;
    CGFloat r60 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    if (isDef || tag.length > 0) {
        heading = [UIView new];
        if (isDef) {
            
            CGFloat cusW = [AddressL(@"address_default_label") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake([JDISVAddressModulUtil screenWidth], 20)].width + 6;
            cusW = ceil(cusW);
            cusW = MAX(32, cusW);
            
            KALabel *defLabel = [KALabel new];
            defLabel.frame = CGRectMake(0, 0, cusW, 18);
            [defLabel renderWithType:(KALabelTypeL4) title:AddressL(@"address_default_label") cornerRadius:r60];
            [heading addSubview:defLabel];
            x += cusW;
            x += 4;
        }
        if (tag.length) {
            CGFloat cusW = [tag jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake([JDISVAddressModulUtil screenWidth], 20)].width + 6;
            cusW = ceil(cusW);
            cusW = MAX(32, cusW);
            
            KALabel *tagLabel = [KALabel new];
            tagLabel.frame = CGRectMake(x, 0, cusW, 18);
            [tagLabel renderWithType:(KALabelTypeL4) title:tag cornerRadius:r60];
            [tagLabel setBackgroundImage:[UIImage jdcd_imageWithColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C14"]] forState:(UIControlStateNormal)];
            [heading addSubview:tagLabel];
            x += cusW;
            x += 4;
        }
    }
    
    NSMutableAttributedString *attr = [NSMutableAttributedString yy_attachmentStringWithContent:heading contentMode:(UIViewContentModeTopLeft) attachmentSize:(CGSizeMake(x, 16)) alignToFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] alignment:(YYTextVerticalAlignmentTop)];
    NSMutableArray *temp = [NSMutableArray array];
    [temp tm_safeAddObject:addr.provinceName];
    [temp tm_safeAddObject:addr.cityName];
    [temp tm_safeAddObject:addr.districtName];
    [temp tm_safeAddObject:addr.townName];
    [temp tm_safeAddObject:addr.addressDetail];
    NSString *fullAddress = [temp componentsJoinedByString:@""];
    NSMutableParagraphStyle *style = [[NSParagraphStyle defaultParagraphStyle] mutableCopy];
    style.lineSpacing = 8 - [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"].lineHeight + [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"].pointSize;
    [attr appendAttributedString:[[NSAttributedString alloc] initWithString:fullAddress attributes:@{ NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"], NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}]];
    [attr addAttributes:@{NSParagraphStyleAttributeName: style} range:NSMakeRange(0, attr.string.length)];
    if (attr) {
        self.addressName.attributedText = [attr copy];
    } else {
        self.addressName.text = nil;
    }
    self.selectedButton.selected = select;
}

@end
