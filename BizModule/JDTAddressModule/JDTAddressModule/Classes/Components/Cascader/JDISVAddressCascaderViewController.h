//
//  JDISVAddressCascaderViewController.h
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/15.
//

#import <UIKit/UIKit.h>

#import "JDISVAddressModulUtil.h"
#import "JDISVAddressCascaderModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVAddressCascaderViewController : UIViewController

@property (nonatomic, strong) RACSubject *delegate;

@property (nonatomic, assign) BOOL showUserAddress;
@property (nonatomic, assign) BOOL convert; /**< 转换经纬度,默认yes */

- (instancetype)initWithAddress:(JDTAddressItemModel *)address
                          limit:(NSInteger)limitLevel;

@end

NS_ASSUME_NONNULL_END
