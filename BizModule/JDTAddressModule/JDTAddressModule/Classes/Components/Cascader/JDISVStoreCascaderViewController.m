//
//  JDISVStoreCascaderViewController.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/29.
//

#import "JDISVStoreCascaderViewController.h"

#import "JDISVStoreCascaderViewModel.h"

#import "JDISVAddressCascaderView.h"

@interface JDISVStoreCascaderViewController () <JXCategoryViewDelegate, JXPagerViewDelegate>

@property (nonatomic, strong) JXCategoryTitleView *category;
@property (nonatomic, strong) JXPagerView *pagerView;
@property (nonatomic, strong) JDISVStoreCascaderViewModel *viewModel;

@property (nonatomic, strong) NSArray *titles;
@property (nonatomic, strong) NSArray *containers;

@property (nonatomic, copy) NSArray<JDISVAddressCascaderItemModel *> *address;
@property (nonatomic, strong) NSNumber *venderId;
@property (nonatomic, strong) UIButton *submitButton;

@end

@implementation JDISVStoreCascaderViewController

- (instancetype)initWithVenderId:(NSNumber *)venderId
{
    self = [super init];
    if (self) {
        _delegate = [RACSubject subject];
        _viewModel = [JDISVStoreCascaderViewModel new];
        self.venderId = venderId;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self.view addSubview:self.category];
    [self.category mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.equalTo(self.view);
        make.height.mas_equalTo(39);
    }];
    
    UIView *bottomView = [UIView new];
    [self.view addSubview:bottomView];
    [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
        make.height.mas_equalTo(50 + [UIWindow ka_uikit_safeAreaInsets].bottom);
    }];
    
    [bottomView addSubview:self.submitButton];
    [self.submitButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(5);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.height.mas_equalTo(40);
    }];
    
    [self.view addSubview:self.pagerView];
    [self.pagerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.category.mas_bottom);
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(bottomView.mas_top);
    }];
    
    [self setupContainersWithModel:nil];
}

- (void)setupContainersWithModel:(JDISVAddressCascaderItemModel *)model {
    if (model == nil) {
        NSMutableArray *parentIds = [NSMutableArray array];
        for (JDISVAddressCascaderItemModel *addr in self.address) {
            NSNumber *addrId = addr.addrId;
            if ([addrId isKindOfClass:NSNumber.class]) {
                [parentIds addObject:addrId];
            } else {
                break;
            }
        }
        [parentIds insertObject:@(0) atIndex:0];
        NSMutableArray *signals = [NSMutableArray array];
        NSMutableArray *containers = [NSMutableArray array];
        @weakify(self);
        for (NSNumber *addrId in parentIds) {
            NSUInteger idx = [parentIds indexOfObject:addrId];
            [containers addObject:[NSNull null]];
            RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
                @strongify(self);
                [self.viewModel requestWithVenderId:self.venderId block:^(NSArray<JDISVAddressCascaderItemModel *> * _Nonnull groupArray, NSError * _Nonnull error) {
                    if (groupArray.count) {
                        JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
                        [item.delegate subscribeNext:^(id  _Nullable x) {
                            @strongify(self);
                            if (x) {
                                [self setupContainersWithModel:x];
                            }
                        }];
                        [item updateWithData:groupArray];
                        [containers tm_safeReplaceObjectAtIndex:idx withObject:item];
                    }
                    [subscriber sendCompleted];
                }];
                return nil;
            }];
            [signals addObject:signal];
        }
        [[RACSignal combineLatest:[signals copy]] subscribeCompleted:^{
            @strongify(self);
            NSMutableArray *temp = [NSMutableArray array];
            NSMutableArray *titles = [NSMutableArray array];
            for (JDISVAddressCascaderView *item in containers) {
                if (![item isKindOfClass:JDISVAddressCascaderView.class]) {
                    break;
                }
                NSUInteger idx = [containers indexOfObject:item];
                JDISVAddressCascaderItemModel *desc = [self.address tm_safeObjectAtIndex:idx];
                BOOL contain = NO;
                for (JDISVAddressCascaderItemModel *addrItem in item.datasource) {
                    if ([addrItem.addrId isKindOfClass:NSString.class] && [addrItem.addrId isEqual:desc.addrId]) {
                        [temp addObject:item];
                        [titles addObject:addrItem.name ?: @"-"];
                        addrItem.selected = YES;
                        item.selected = addrItem;
                        NSIndexPath *ip = [NSIndexPath indexPathForRow:[item.datasource indexOfObject:addrItem] inSection:0];
                        item.selectedIndexPath = ip;
                        contain = YES;
                    }
                }
                if (!contain) {
                    break;
                }
            }
            if (temp.count == 0 && [containers.firstObject isKindOfClass:JDISVAddressCascaderView.class]) {
                [temp addObject:containers.firstObject];
                [titles addObject:AddressL(@"address_popup_select")];
            }
            self.titles = [titles copy];
            // 渲染
            self.containers = [temp copy];
            self.category.titles = self.titles;
            [self.category reloadData];
            self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
            [self.pagerView reloadData];
            [self.category selectItemAtIndex:self.titles.count-1];
            [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
            dispatch_async(dispatch_get_main_queue(), ^{
                for (JDISVAddressCascaderView *item in self.containers) {
                    [item scrollToSelected];
                }
            });
        }];
    } else {
        NSMutableArray *tempTitles = [[self.titles subarrayWithRange:NSMakeRange(0, self.category.selectedIndex + 1)] mutableCopy];
        if (model.name && model.addrId) {
            [tempTitles insertObject:model.name atIndex:tempTitles.count - 1];
            if (![[tempTitles lastObject] isEqual:AddressL(@"address_popup_select")]) {
                [tempTitles replaceObjectAtIndex:tempTitles.count-1 withObject:AddressL(@"address_popup_select")];
            }
            self.titles = [tempTitles copy];
            JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
            NSUInteger idx = self.category.selectedIndex + 1;
            
            {
                NSArray *groupArray = [self.viewModel storeAddressWithNodes:model.childNodes];
                if ([model.name isEqual:AddressL(@"address_select_all")]) {
                    //
                    NSArray *titles = [self.titles subarrayWithRange:(NSMakeRange(0, idx))];
                    self.category.titles = titles;
                    [self.category reloadData];
                } else if (groupArray.count) {
                    [item updateWithData:groupArray];
                    NSMutableArray *tempContainers = [[self.containers subarrayWithRange:NSMakeRange(0, idx)] mutableCopy];
                    [tempContainers addObject:item];
                    self.containers = [tempContainers copy];
                    self.category.titles = self.titles;
                    [self.category reloadData];
                    self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
                    [self.pagerView reloadData];
                    [self.category selectItemAtIndex:self.titles.count-1];
                    [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
                } else {
                    [self finishSelect];
                }
            }
            
            @weakify(self);
            [item.delegate subscribeNext:^(id  _Nullable x) {
                @strongify(self);
                if (x) {
                    [self setupContainersWithModel:x];
                }
            }];
        }
    }
}

- (void)finishSelect {
    NSMutableArray *addressArray = [NSMutableArray array];
    for (JDISVAddressCascaderView *view in self.containers) {
        JDISVAddressCascaderItemModel *item = view.selected;
        if (item) {
            [addressArray addObject:item];
        } else {
            break;
        }
    }
    JDTAddressItemModel *addrItem = [[JDTAddressItemModel alloc] init];
    NSUInteger count = 0;
    for (JDISVAddressCascaderItemModel *item in addressArray) {
        addrItem.addressId = item.addrId.integerValue;
        addrItem.name = item.name;
//        addrItem.parentId = item.parentId;
        if (count == 0) {
            addrItem.provinceId = item.addrId.stringValue;
            addrItem.provinceName = item.name;
        } else if (count == 1) {
            addrItem.cityId = item.addrId.stringValue;
            addrItem.cityName = item.name;
        } else if (count == 2) {
            addrItem.districtId = item.addrId.stringValue;
            addrItem.districtName = item.name;
        } else if (count == 3) {
            addrItem.townId = item.addrId.stringValue;
            addrItem.townName = item.name;
        }
        count ++;
    }
    [self.delegate sendNext:addrItem];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)submitButtonOnClicked {
    NSMutableArray *addressArray = [NSMutableArray array];
    for (JDISVAddressCascaderView *view in self.containers) {
        JDISVAddressCascaderItemModel *item = view.selected;
        if (item) {
            [addressArray addObject:item];
        } else {
            break;
        }
    }
    if (addressArray.count == 0) {
        return;
    }
    JDTAddressItemModel *addrItem = [[JDTAddressItemModel alloc] init];
    NSUInteger count = 0;
    for (JDISVAddressCascaderItemModel *item in addressArray) {
        if ([item.name isEqual:AddressL(@"address_select_all")]) {
            continue;
        }
        addrItem.addressId = item.addrId.integerValue;
        addrItem.name = item.name;
//        addrItem.parentId = item.parentId;
        if (count == 0) {
            addrItem.provinceId = item.addrId.stringValue;
            addrItem.provinceName = item.name;
        } else if (count == 1) {
            addrItem.cityId = item.addrId.stringValue;
            addrItem.cityName = item.name;
        } else if (count == 2) {
            addrItem.districtId = item.addrId.stringValue;
            addrItem.districtName = item.name;
        } else if (count == 3) {
            addrItem.townId = item.addrId.stringValue;
            addrItem.townName = item.name;
        }
        count ++;
    }
    [self.delegate sendNext:addrItem];
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didClickSelectedItemAtIndex:(NSInteger)index {
    
}

#pragma mark - JXPagerViewDelegate

- (NSUInteger)tableHeaderViewHeightInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (UIView *)tableHeaderViewInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSUInteger)heightForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (UIView *)viewForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSInteger)numberOfListsInPagerView:(JXPagerView *)pagerView {
    return self.containers.count;
}

- (id<JXPagerViewListViewDelegate>)pagerView:(JXPagerView *)pagerView initListAtIndex:(NSInteger)index {
    return self.containers[index];
}

#pragma mark - getter

- (JXCategoryTitleView *)category {
    if (!_category) {
        _category = [[JXCategoryNumberView alloc] initWithFrame:CGRectMake(0, 0, [JDISVAddressModulUtil screenWidth], 50)];
        _category.defaultSelectedIndex = 0;
        _category.delegate = self;
        _category.titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        _category.titleSelectedFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        _category.averageCellSpacingEnabled = NO;
        _category.cellSpacing = 25;
        _category.contentEdgeInsetLeft = 0;
        _category.contentEdgeInsetRight = 0;
        _category.titleSelectedColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        _category.titleColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _category;
}

- (JXPagerView *)pagerView {
    if (!_pagerView) {
        _pagerView = [[JXPagerListRefreshView alloc] initWithDelegate:self listContainerType:(JXPagerListContainerType_ScrollView)];
        _pagerView.defaultSelectedIndex = 0;
        _pagerView.listContainerView.categoryNestPagingEnabled = YES;
        _pagerView.isListHorizontalScrollEnabled = NO;
    }
    return _pagerView;
}

- (UIButton *)submitButton {
    if (!_submitButton) {
        _submitButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
        _submitButton.bounds = CGRectMake(0, 0, self.view.bounds.size.width - 36, 40);
        [_submitButton renderB1];
        [_submitButton configKAButtonTitle:AddressL(@"address_comfir_button_title")];
        _submitButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        [_submitButton addTarget:self action:@selector(submitButtonOnClicked) forControlEvents:(UIControlEventTouchUpInside)];
    }
    return _submitButton;
}

@end
