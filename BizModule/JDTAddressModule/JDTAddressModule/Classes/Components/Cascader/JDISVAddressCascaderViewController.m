//
//  JDISVAddressCascaderViewController.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/15.
//

#import "JDISVAddressCascaderViewController.h"

#import "JDISVAddressCascaderViewModel.h"

#import "JDISVAddressCascaderView.h"
#import "JDISVUserAddressView.h"
#import "JDISVAddressService.h"

@interface JDISVAddressCascaderViewController () <JXCategoryViewDelegate, JXPagerViewDelegate>

@property (nonatomic, strong) JXCategoryTitleView *category;
@property (nonatomic, strong) JXPagerView *pagerView;
@property (nonatomic, strong) JDISVUserAddressView *userAddr;
@property (nonatomic, strong) JDISVAddressCascaderViewModel *viewModel;

@property (nonatomic, strong) NSArray *titles;
@property (nonatomic, strong) NSArray *containers;

@property (nonatomic, strong) JDTAddressItemModel *addressItem;

@property (nonatomic, copy) NSArray <NSString *> *addressIds;

@property (nonatomic, assign) NSInteger limitSelectLevel;
@end

@implementation JDISVAddressCascaderViewController

- (instancetype)initWithAddress:(JDTAddressItemModel *)address
                          limit:(NSInteger)limitLevel {
    self = [super init];
    if (self) {
        _convert = YES;
        _addressItem = address;
        NSMutableArray *arr = [NSMutableArray array];
        [arr tm_safeAddObject:address.provinceId];
        [arr tm_safeAddObject:address.cityId];
        [arr tm_safeAddObject:address.districtId];
        [arr tm_safeAddObject:address.townId];
        self.addressIds = [arr copy];
        _delegate = [RACSubject subject];
        _viewModel = [JDISVAddressCascaderViewModel new];
        self.limitSelectLevel = limitLevel;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    if ([PlatformService getUserIsLogin] && self.showUserAddress) {
        [self showUserAddr];
    } else {
        [self showCascader];
    }
}

- (void)showUserAddr {
    @weakify(self)
    JDISVUserAddressView *userAddr = [[JDISVUserAddressView alloc] initWithAddress:self.addressItem];
    userAddr.otherCallback = ^{
        @strongify(self)
        [self.userAddr removeFromSuperview];
        [self showCascader];
    };
    userAddr.addressCallback = ^(JDTAddressItemModel * _Nonnull addr) {
        @strongify(self)
        [self.delegate sendNext:addr];
        [self dismissViewControllerAnimated:YES completion:nil];
    };
    [self.view addSubview:userAddr];
    [userAddr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    self.userAddr = userAddr;
}

- (void)showCascader {
    [self.view addSubview:self.category];
    [self.category mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.equalTo(self.view);
        make.height.mas_equalTo(39);
    }];
    
    [self.view addSubview:self.pagerView];
    [self.pagerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.category.mas_bottom);
        make.leading.trailing.bottom.equalTo(self.view);
    }];
    
    [self setupRootContainersWithModel];
}
-(void)setupRootContainersWithModel{
    NSMutableArray *parentIds = [self.addressIds mutableCopy];
//    [parentIds insertObject:@(100000) atIndex:0];
    // TODO:Juice 这里CN 根据接口address/c/queryEnabledCountryList字段返回来
    [parentIds insertObject:@"CN" atIndex:0];
    NSMutableArray *signals = [NSMutableArray array];
    NSMutableArray *containers = [NSMutableArray array];
    @weakify(self);
    for (NSString *addrId in parentIds) {
        NSUInteger idx = [parentIds indexOfObject:addrId];
        [containers addObject:[NSNull null]];
        RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
            @strongify(self);
            [self.viewModel requestWithParentId:addrId block:^(NSArray<JDISVAddressCascaderItemModel *> * _Nonnull groupArray, NSError * _Nonnull error) {
                if (groupArray.count) {
                    JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
                    if(self.limitSelectLevel == 0 ){
                        item.canSelect = YES;
                    }else{
                        item.canSelect = idx+1 > self.limitSelectLevel;
                    }
                    [item.delegate subscribeNext:^(id  _Nullable x) {
                        @strongify(self);
                        if (x) {
                            [self setupContainersWithModel:x];
                        }
                    }];
                    
                    [item updateWithData:groupArray];
                    [containers tm_safeReplaceObjectAtIndex:idx withObject:item];
                }
                [subscriber sendCompleted];
            }];
            return nil;
        }];
        [signals addObject:signal];
    }
    [[RACSignal combineLatest:[signals copy]] subscribeCompleted:^{
        @strongify(self);
        NSMutableArray *temp = [NSMutableArray array];
        NSMutableArray *titles = [NSMutableArray array];
        for (JDISVAddressCascaderView *item in containers) {
            if (![item isKindOfClass:JDISVAddressCascaderView.class]) {
                break;
            }
            NSUInteger idx = [containers indexOfObject:item];
            NSNumber *addrId = [self.addressIds tm_safeObjectAtIndex:idx];
            BOOL contain = NO;
            for (JDISVAddressCascaderItemModel *addrItem in item.datasource) {
                if ([addrItem.addrId isKindOfClass:NSNumber.class] && [addrItem.addrId isEqual:addrId]) {
                    [temp addObject:item];
                    [titles addObject:addrItem.name ?: @"-"];
                    addrItem.selected = YES;
                    item.selected = addrItem;
                    NSIndexPath *ip = [NSIndexPath indexPathForRow:[item.datasource indexOfObject:addrItem] inSection:0];
                    item.selectedIndexPath = ip;
                    contain = YES;
                }
            }
            if (!contain) {
                break;
            }
        }
        if (temp.count == 0 && [containers.firstObject isKindOfClass:JDISVAddressCascaderView.class]) {
            [temp addObject:containers.firstObject];
            [titles addObject:AddressL(@"address_popup_select")];
        }
        self.titles = [titles copy];
        // 渲染
        self.containers = [temp copy];
        
        self.category.titles = self.titles;
        [self.category reloadData];
        self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
        [self.pagerView reloadData];
        [self.category selectItemAtIndex:self.titles.count-1];
        [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
        dispatch_async(dispatch_get_main_queue(), ^{
            for (JDISVAddressCascaderView *item in self.containers) {
                [item scrollToSelected];
            }
        });
    }];
}

- (void)setupContainersWithModel:(JDISVAddressCascaderItemModel *)model {
    
    NSMutableArray *tempTitles = [[self.titles subarrayWithRange:NSMakeRange(0, self.category.selectedIndex + 1)] mutableCopy];
    if (model.name && model.addrId) {
        [tempTitles insertObject:model.name atIndex:tempTitles.count - 1];
        if (![[tempTitles lastObject] isEqual:AddressL(@"address_popup_select")]) {
            [tempTitles replaceObjectAtIndex:tempTitles.count-1 withObject:AddressL(@"address_popup_select")];
        }
        self.titles = [tempTitles copy];
        JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
        if(self.limitSelectLevel == 0){
            item.canSelect = YES;
        }else{
            NSUInteger idx = self.category.selectedIndex + 1;
            item.canSelect = idx > self.limitSelectLevel;
        }
        JDISVAddressCascaderView *currentCascaderView = self.containers.lastObject;
        [currentCascaderView showLoading:YES];
        NSUInteger idx = self.category.selectedIndex + 1;
        
        @weakify(self);
        [self.viewModel requestWithParentId:model.addrId.stringValue block:^(NSArray<JDISVAddressCascaderItemModel *> * _Nonnull groupArray, NSError * _Nonnull error) {
            @strongify(self);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [currentCascaderView showLoading:NO];
            });
            if (error) {
                
            } else if (groupArray.count) {
                [item updateWithData:groupArray];
                NSMutableArray *tempContainers = [[self.containers subarrayWithRange:NSMakeRange(0, idx)] mutableCopy];
                [tempContainers addObject:item];
                self.containers = [tempContainers copy];
                self.category.titles = self.titles;
                [self.category reloadData];
                self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
                [self.pagerView reloadData];
                [self.category selectItemAtIndex:self.titles.count-1];
                [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
            } else {
                [self finishSelect];
            }
        }];
        
        [item.delegate subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            if (x) {
                [self setupContainersWithModel:x];
            }
        }];
    }
}

- (void)finishSelect {
    NSMutableArray *addressArray = [NSMutableArray array];
    for (JDISVAddressCascaderView *view in self.containers) {
        JDISVAddressCascaderItemModel *item = view.selected;
        if (item) {
            [addressArray addObject:item];
        } else {
            break;
        }
    }
    JDTAddressItemModel *addrItem = [[JDTAddressItemModel alloc] init];
    NSUInteger count = 0;
    for (JDISVAddressCascaderItemModel *item in addressArray) {
        addrItem.addressId = item.addrId.integerValue;
        addrItem.name = item.name;
//        addrItem.parentId = item.parentId;
        if (count == 0) {
            addrItem.provinceId = item.addrId.stringValue;
            addrItem.provinceName = item.name;
        } else if (count == 1) {
            addrItem.cityId = item.addrId.stringValue;
            addrItem.cityName = item.name;
        } else if (count == 2) {
            addrItem.districtId = item.addrId.stringValue;
            addrItem.districtName = item.name;
        } else if (count == 3) {
            addrItem.townId = item.addrId.stringValue;
            addrItem.townName = item.name;
        }
        count ++;
    }
    
//    if (self.convert) {
//        @weakify(self)
//        [PlatformService showLoadingInView:self.view];
//        [[[JDISVAddressService shareInstance] locationWithFullAddress:addr.fullAddress] subscribeNext:^(NSDictionary*  _Nullable loction) {
//            @strongify(self)
//            [PlatformService dismissInView:self.view];
//            NSNumber *lat = [loction objectForKey:@"latitude"];
//            NSNumber *lng = [loction objectForKey:@"longitude"];
//            addr.latitude = lat;
//            addr.longitude = lng;
//            [self.delegate sendNext:addr];
//            [self dismissViewControllerAnimated:YES completion:nil];
//        } error:^(NSError * _Nullable error) {
//            @strongify(self)
//            [PlatformService dismissInView:self.view];
//            [self.delegate sendNext:addr];
//            [self dismissViewControllerAnimated:YES completion:nil];
//        }];
//    } else {
        [self.delegate sendNext:addrItem];
        [self dismissViewControllerAnimated:YES completion:nil];
//    }
}

#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didClickSelectedItemAtIndex:(NSInteger)index {
    
}

#pragma mark - JXPagerViewDelegate

- (NSUInteger)tableHeaderViewHeightInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (UIView *)tableHeaderViewInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSUInteger)heightForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (UIView *)viewForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSInteger)numberOfListsInPagerView:(JXPagerView *)pagerView {
    return self.containers.count;
}

- (id<JXPagerViewListViewDelegate>)pagerView:(JXPagerView *)pagerView initListAtIndex:(NSInteger)index {
    return self.containers[index];
}

#pragma mark - getter

- (JXCategoryTitleView *)category {
    if (!_category) {
        _category = [[JXCategoryNumberView alloc] initWithFrame:CGRectMake(0, 0, [JDISVAddressModulUtil screenWidth], 50)];
        _category.defaultSelectedIndex = 0;
        _category.delegate = self;
        _category.titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        _category.titleSelectedFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        _category.averageCellSpacingEnabled = NO;
        _category.cellSpacing = 25;
        _category.contentEdgeInsetLeft = 0;
        _category.contentEdgeInsetRight = 0;
        _category.titleSelectedColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        _category.titleColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _category;
}

- (JXPagerView *)pagerView {
    if (!_pagerView) {
        _pagerView = [[JXPagerListRefreshView alloc] initWithDelegate:self listContainerType:(JXPagerListContainerType_ScrollView)];
        _pagerView.defaultSelectedIndex = 0;
        _pagerView.listContainerView.categoryNestPagingEnabled = YES;
        _pagerView.isListHorizontalScrollEnabled = NO;
        if (@available(iOS 15.0, *)) {
            _pagerView.mainTableView.sectionHeaderTopPadding = 0;
        }
        _pagerView.mainTableView.backgroundColor = [UIColor clearColor];
    }
    return _pagerView;
}

@end
