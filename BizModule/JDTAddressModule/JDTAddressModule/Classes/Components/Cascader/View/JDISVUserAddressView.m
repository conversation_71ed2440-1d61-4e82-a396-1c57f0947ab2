//
//  JDISVUserAddressView.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/12/1.
//

#import "JDISVUserAddressView.h"
#import "JDISVAddressService.h"
#import "JDISVUserAddressTableViewCell.h"

@interface JDISVUserAddressView () <UITableViewDelegate, UITableViewDataSource, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *bottomView;

@property (nonatomic, strong) JDTAddressItemModel *selectedAddress;
@property (nonatomic, copy) NSArray <JDTAddressItemModel *> *items;

@property (nonatomic, assign) BOOL isLoading;
@property (nonatomic, assign) BOOL isLoaded;
@property (nonatomic, assign) BOOL isError;

@end

@implementation JDISVUserAddressView

- (instancetype)initWithAddress:(JDTAddressItemModel *)address {
    if (self = [super init]) {
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.selectedAddress = address;
        
        // bottom
        UIView *bottomView = [UIView new];
        [self addSubview:bottomView];
        [bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.equalTo(self);
            make.height.mas_equalTo(50+[UIWindow ka_uikit_safeAreaInsets].bottom);
        }];
        self.bottomView = bottomView;
        bottomView.hidden = YES;
        //
        UIButton *other = [UIButton buttonWithType:(UIButtonTypeCustom)];
        other.bounds = CGRectMake(0, 0, [JDISVAddressModulUtil screenWidth], 40);
        [other addTarget:self action:@selector(otherAddrBtnClicked) forControlEvents:(UIControlEventTouchUpInside)];
        [other renderB1];
        [other configKAButtonTitle:AddressL(@"address_popup_choose_another_address")];
        [bottomView addSubview:other];
        [other mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(6);
            make.trailing.mas_equalTo(-6);
            make.height.mas_equalTo(40);
            make.top.mas_equalTo(5);
        }];
        
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.emptyDataSetSource = self;
        _tableView.emptyDataSetDelegate = self;
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVUserAddressTableViewCell" bundle:[NSBundle isv_address_bundle]] forCellReuseIdentifier:@"cell"];
        [self insertSubview:_tableView atIndex:0];
        [_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.top.trailing.equalTo(self);
            make.bottom.equalTo(bottomView.mas_top);
        }];
        
        [self loadData];
    }
    return self;
}

- (void)loadData {
    @weakify(self)
    self.isLoading = YES;
    [self.tableView reloadEmptyDataSet];
    [PlatformService showLoadingInView:self];
    [[[JDISVAddressService shareInstance] addressList] subscribeNext:^(NSArray<JDTAddressItemModel *> *  _Nullable x) {
        @strongify(self)
        [PlatformService dismissInView:self];
        self.bottomView.hidden = NO;
        self.isLoading = NO;
        self.isLoaded = YES;
        self.items = x;
        [self.tableView reloadData];
        
        if (self.items.count == 0) {
            [self otherAddrBtnClicked];
        }
    } error:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self];
        self.bottomView.hidden = YES;
        self.isLoading = NO;
        self.isError = YES;
        self.items = nil;
        [self.tableView reloadData];
    }];
}

- (void)otherAddrBtnClicked {
    if (self.otherCallback) {
        self.otherCallback();
    }
}

#pragma mark - <UITableViewDelegate, UITableViewDataSource>

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.items.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDTAddressItemModel *model = self.items[indexPath.row];
    JDISVUserAddressTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];
    [cell configureWithModel:model select:(model.addressId == self.selectedAddress.addressId)];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    JDTAddressItemModel *model = self.items[indexPath.row];
    self.selectedAddress = model;
    [tableView reloadData];
    if (self.addressCallback) {
        self.addressCallback(model);
    }
}

#pragma mark - DZNEmptyDataSetSource, DZNEmptyDataSetDelegate

- (UIView *)customViewForEmptyDataSet:(UIScrollView *)scrollView {
    {
        if (!self.isLoaded && !self.isError) {
            return nil;
        }
        if (self.isLoading) {
            return nil;
        }
        for (UIView *subView in scrollView.subviews) {
            if ([NSStringFromClass([subView class]) isEqualToString:@"DZNEmptyDataSetView"]) {
                
                CGRect frame = subView.frame;
                frame.origin.y = 0;
                subView.frame = frame;
                
                UIView *view = [subView valueForKey:@"contentView"];
                if (view) {
                    [subView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|[contentView]|" options:0 metrics:nil views:@{@"contentView":view}]];
                }
            }
        }
    }
    UIView *container = [UIView new];
    UIView *errorView;
    @weakify(self)
    if (self.isError) {
        KAEmptyView *empty = [[KAEmptyView alloc] initWithFrame:(CGRectZero) type:(KAEmptyViewTypeNormal)];
        empty.verticalAlignment = KAEmptyViewVerticalAlignmentViewCenter;
        empty.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:(JDISVImageTypeFailToLoad)];
        empty.decrible = AddressL(@"address_load_failed");
        empty.actionTitle = AddressL(@"address_retry");
        empty.action = ^(UIButton * _Nonnull sender) {
            @strongify(self)
            [self loadData];
        };
        errorView = empty;
    } else {
        KAEmptyView *empty = [[KAEmptyView alloc] initWithFrame:(CGRectZero) type:(KAEmptyViewTypeNotAction)];
        empty.verticalAlignment = KAEmptyViewVerticalAlignmentViewCenter;
        empty.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:(JDISVImageTypeNoDataAddress)];
        empty.decrible = AddressL(@"address_empty_address_tips");
        errorView = empty;
    }
    
    [container addSubview:errorView];
    [errorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(container);
    }];
    return container;
}

@end
