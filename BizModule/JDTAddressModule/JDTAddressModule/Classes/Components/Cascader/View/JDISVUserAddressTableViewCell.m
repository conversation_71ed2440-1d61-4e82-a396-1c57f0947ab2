//
//  JDISVUserAddressTableViewCell.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/12/1.
//

#import "JDISVUserAddressTableViewCell.h"

#import "JDISVAddressModulUtil.h"

@interface JDISVUserAddressTableViewCell ()

@property (weak, nonatomic) IBOutlet UIImageView *icon;
@property (weak, nonatomic) IBOutlet UILabel *name;
@property (weak, nonatomic) IBOutlet UILabel *detail;

@end

@implementation JDISVUserAddressTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.name.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
    self.name.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.detail.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)];
    self.detail.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)configureWithModel:(JDTAddressItemModel *)model select:(BOOL)select {
    if (select) {
        self.icon.image = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    } else {
        self.icon.image = [UIImage ka_iconWithName:JDIF_ICON_RADIO imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"]];
    }
    
    self.name.text = model.addressDetail;
    
    NSMutableArray *temp = [NSMutableArray array];
    [temp tm_safeAddObject:model.provinceName];
    [temp tm_safeAddObject:model.cityName];
    [temp tm_safeAddObject:model.districtName];
    [temp tm_safeAddObject:model.townName];
    self.detail.text = [temp componentsJoinedByString:@""];
}

@end
