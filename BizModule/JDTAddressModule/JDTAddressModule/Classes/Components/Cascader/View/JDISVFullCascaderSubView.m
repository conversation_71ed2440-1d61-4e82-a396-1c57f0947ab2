//
//  JDISVFullCascaderSubView.m
//  JDISVAddressModule
//
//  Created by cdwutao3 on 2022/12/17.
//

#import "JDISVFullCascaderSubView.h"
#import "JDISVAddressCascaderViewModel.h"
#import "JDISVAddressCascaderView.h"
#import "JDISVUserAddressView.h"
#import "JDISVAddressService.h"


@interface JDISVFullCascaderSubView()<JXCategoryViewDelegate, JXPagerViewDelegate>

@property (nonatomic, strong) JXCategoryTitleView *category;
@property (nonatomic, strong) JXPagerView *pagerView;
@property (nonatomic, strong) JDISVUserAddressView *userAddr;
@property (nonatomic, strong) JDISVAddressCascaderViewModel *viewModel;

@property (nonatomic, strong) NSArray *titles;
@property (nonatomic, strong) NSArray *containers;

@property (nonatomic, copy) NSArray *addressIds;

@end

@implementation JDISVFullCascaderSubView
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        
        NSMutableArray *arr = [NSMutableArray array];
        self.addressIds = [arr copy];
        _delegate = [RACSubject subject];
        _viewModel = [JDISVAddressCascaderViewModel new];
        [self addSubview:self.category];
        [self.category mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.top.trailing.equalTo(self);
            make.height.mas_equalTo(47);
        }];
    
        [self addSubview:self.pagerView];
        [self.pagerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.category.mas_bottom);
            make.leading.trailing.bottom.equalTo(self);
        }];
    }
    return self;
}

-(void)requestRoot:(NSArray*)rootParent{
    @weakify(self)
    NSMutableArray* tmp = [NSMutableArray array];
    NSMutableArray *containers = [NSMutableArray array];
    NSString* letter = @" ";
    for(NSDictionary* dic in rootParent){
        
        JDISVAddressCascaderItemModel* model = [[JDISVAddressCascaderItemModel alloc] init];
        model.name = dic[@"areaName"];
        model.addrId = dic[@"areaId"];
        model.parentId = @(10000);
        NSString* itemLetter = dic[@"firstLetter"];
        if([letter isEqualToString:itemLetter]){
            [tmp addObject:model];
        }else{
            JDISVAddressCascaderItemModel* letterItem = [[JDISVAddressCascaderItemModel alloc] init];
            letterItem.isLetter = YES;
            letterItem.name = itemLetter;
            letter = itemLetter;
            [tmp addObject:letterItem];
            [tmp addObject:model];
        }
    }
    NSArray<JDISVAddressCascaderItemModel *> * groupArray = [tmp copy];
    if (groupArray.count) {
        JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
        item.canSelect = YES;
        [item.delegate subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            if (x) {
                [self setupContainersWithModel:x];
            }
        }];
        
        [item updateWithData:groupArray];
        [containers  addObject:item];
    }
    NSMutableArray *temp = [NSMutableArray array];
    NSMutableArray *titles = [NSMutableArray array];
    for (JDISVAddressCascaderView *item in containers) {
        if (![item isKindOfClass:JDISVAddressCascaderView.class]) {
            break;
        }
        NSUInteger idx = [containers indexOfObject:item];
        NSNumber *addrId = [self.addressIds tm_safeObjectAtIndex:idx];
        BOOL contain = NO;
        for (JDISVAddressCascaderItemModel *addrItem in item.datasource) {
            if ([addrItem.addrId isKindOfClass:NSNumber.class] && [addrItem.addrId isEqual:addrId]) {
                [temp addObject:item];
                [titles addObject:addrItem.name ?: @"-"];
                addrItem.selected = YES;
                item.selected = addrItem;
                NSIndexPath *ip = [NSIndexPath indexPathForRow:[item.datasource indexOfObject:addrItem] inSection:0];
                item.selectedIndexPath = ip;
                contain = YES;
            }
        }
        if (!contain) {
            break;
        }
    }
    if (temp.count == 0 && [containers.firstObject isKindOfClass:JDISVAddressCascaderView.class]) {
        [temp addObject:containers.firstObject];
        [titles addObject:AddressL(@"address_popup_select")];
    }
    self.titles = [titles copy];
    // 渲染
    self.containers = [temp copy];
    
    self.category.titles = self.titles;
    [self.category reloadData];
    self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
    [self.pagerView reloadData];
    [self.category selectItemAtIndex:self.titles.count-1];
    [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
    dispatch_async(dispatch_get_main_queue(), ^{
        for (JDISVAddressCascaderView *item in self.containers) {
            [item scrollToSelected];
        }
    });
}

-(void)requestThreeCascader:(NSArray *)first
                        sec:(NSArray*)sec
                      third:(NSArray*)third
                      forth:(NSArray*)forth
                 addressIds:(NSArray*)addressIds{
    self.addressIds = addressIds;
    NSMutableArray* tmp = [NSMutableArray array];
    NSMutableArray *containers = [NSMutableArray array];
    for(NSDictionary* dic in first){
        JDISVAddressCascaderItemModel* model = [[JDISVAddressCascaderItemModel alloc] init];
        model.name = dic[@"areaName"];
        model.addrId = dic[@"areaId"];
        model.parentId = @(10000);
        [tmp addObject:model];
    }
    @weakify(self)
    //L1
    NSArray<JDISVAddressCascaderItemModel *> * groupArray = [tmp copy];
    if (groupArray.count) {
        JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
        item.canSelect = YES;
        [item.delegate subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            if (x) {
                [self setupContainersWithModel:x];
            }
        }];
        
        [item updateWithData:groupArray];
        [containers  addObject:item];
    }
    NSArray* level = @[sec,third,forth];
    for(NSArray* l in level){
        if (l.count) {
            JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
            item.canSelect = YES;
            [item.delegate subscribeNext:^(id  _Nullable x) {
                @strongify(self);
                if (x) {
                    [self setupContainersWithModel:x];
                }
            }];
            NSMutableArray* tmpArr = [NSMutableArray array];
            for(JDISVAddressResRegionItem* item in l){
                JDISVAddressCascaderItemModel* model = [[JDISVAddressCascaderItemModel alloc] init];
                model.name = item.areaName;
                model.addrId = item.areaId;
                model.parentId = item.parentId;
                model.isLetter = item.isLetter;
                [tmpArr addObject:model];
            }
            [item updateWithData:[tmpArr copy]];
            [containers  addObject:item];
        }
    }
    
    
    NSMutableArray *titles = [NSMutableArray array];
    for (JDISVAddressCascaderView *item in containers) {
        if (![item isKindOfClass:JDISVAddressCascaderView.class]) {
            break;
        }
        NSUInteger idx = [containers indexOfObject:item];
        NSNumber *addrId = [self.addressIds tm_safeObjectAtIndex:idx];
        BOOL contain = NO;
        NSLog(@"idx = %@ addrid = %@",@(idx),addrId);
        for (JDISVAddressCascaderItemModel *addrItem in item.datasource) {
            NSLog(@"addrItem.addrid:%@ name:%@",addrItem.addrId,addrItem.name);
            if ([addrItem.addrId isKindOfClass:NSNumber.class] && [addrItem.addrId isEqual:addrId]) {
                
                [titles addObject:addrItem.name ?: @"-"];
                addrItem.selected = YES;
                item.selected = addrItem;
                NSUInteger index =  [item.datasource indexOfObject:addrItem];
                NSIndexPath *ip = [NSIndexPath indexPathForRow:index inSection:0];
                item.selectedIndexPath = ip;
                contain = YES;
            }
        }
        if (!contain) {
            break;
        }
    }
//    if (temp.count == 0 && [containers.firstObject isKindOfClass:JDISVAddressCascaderView.class]) {
//        [temp addObject:containers.firstObject];
        [titles addObject:AddressL(@"address_popup_select")];
    
//    }
    self.titles = [titles copy];
    // 渲染
    self.containers = [containers copy];
    
    self.category.titles = self.titles;
    [self.category reloadData];
    self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
    [self.pagerView reloadData];
    [self.category selectItemAtIndex:self.titles.count-1];
    [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
    dispatch_async(dispatch_get_main_queue(), ^{
        for (JDISVAddressCascaderView *item in self.containers) {
            [item scrollToSelected];
        }
    });
}


- (void)setupContainersWithModel:(JDISVAddressCascaderItemModel *)model {
    if(model.addrId.intValue == -10000){
        [self finishSelect];
        return;
    }
    NSMutableArray *tempTitles = [[self.titles subarrayWithRange:NSMakeRange(0, self.category.selectedIndex + 1)] mutableCopy];
    if (model.name && model.addrId) {
        [tempTitles insertObject:model.name atIndex:tempTitles.count - 1];
        if (![[tempTitles lastObject] isEqual:AddressL(@"address_popup_select")]) {
            [tempTitles replaceObjectAtIndex:tempTitles.count-1 withObject:AddressL(@"address_popup_select")];
        }
        self.titles = [tempTitles copy];
        JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
        item.canSelect = YES;
        JDISVAddressCascaderView *currentCascaderView = self.containers.lastObject;
        [currentCascaderView showLoading:YES];
        NSUInteger idx = self.category.selectedIndex + 1;
        @weakify(self);
        [self.viewModel requestWithParentId:model.addrId block:^(NSArray<JDISVAddressCascaderItemModel *> * _Nonnull groupArray, NSError * _Nonnull error) {
            @strongify(self);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [currentCascaderView showLoading:NO];
            });
            if (error) {
                
            } else if (groupArray.count) {
                if(self.titles .count == 4 && self.noSelect){
                    NSMutableArray* tmpNodes = [groupArray mutableCopy];
                    JDISVAddressCascaderItemModel* noSelectItem = [[JDISVAddressCascaderItemModel alloc] init];
                    JDISVAddressCascaderItemModel* first = model.childNodes.firstObject;
                    noSelectItem.name = AddressL(@"address_select_none");
                    noSelectItem.parentId = first.parentId;
                    noSelectItem.addrId = @(-10000);
                    [tmpNodes addObject:noSelectItem];
                    groupArray = [tmpNodes copy];
                }
                [item updateWithData:groupArray];
                NSMutableArray *tempContainers = [[self.containers subarrayWithRange:NSMakeRange(0, idx)] mutableCopy];
                [tempContainers addObject:item];
                self.containers = [tempContainers copy];
                self.category.titles = self.titles;
                [self.category reloadData];
                self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
                [self.pagerView reloadData];
                [self.category selectItemAtIndex:self.titles.count-1];
                [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
            } else {
                [self finishSelect];
            }
        }];
        
        [item.delegate subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            if (x) {
                [self setupContainersWithModel:x];
            }
        }];
    }
}


-(void)setupRootContainersWithModel{
    NSMutableArray *parentIds = [self.addressIds mutableCopy];
    [parentIds insertObject:@(100000) atIndex:0];
    NSMutableArray *signals = [NSMutableArray array];
    NSMutableArray *containers = [NSMutableArray array];
    @weakify(self);
    for (NSNumber *addrId in parentIds) {
        NSUInteger idx = [parentIds indexOfObject:addrId];
        [containers addObject:[NSNull null]];
        RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
            @strongify(self);
            [self.viewModel requestWithParentId:addrId block:^(NSArray<JDISVAddressCascaderItemModel *> * _Nonnull groupArray, NSError * _Nonnull error) {
                if (groupArray.count) {
                    JDISVAddressCascaderView *item = [[JDISVAddressCascaderView alloc] init];
                        item.canSelect = YES;
                    [item.delegate subscribeNext:^(id  _Nullable x) {
                        @strongify(self);
                        if (x) {
                            [self setupContainersWithModel:x];
                        }
                    }];
                    
                    [item updateWithData:groupArray];
                    [containers tm_safeReplaceObjectAtIndex:idx withObject:item];
                }
                [subscriber sendCompleted];
            }];
            return nil;
        }];
        [signals addObject:signal];
    }
    [[RACSignal combineLatest:[signals copy]] subscribeCompleted:^{
        @strongify(self);
        NSMutableArray *temp = [NSMutableArray array];
        NSMutableArray *titles = [NSMutableArray array];
        for (JDISVAddressCascaderView *item in containers) {
            if (![item isKindOfClass:JDISVAddressCascaderView.class]) {
                break;
            }
            NSUInteger idx = [containers indexOfObject:item];
            NSNumber *addrId = [self.addressIds tm_safeObjectAtIndex:idx];
            BOOL contain = NO;
            for (JDISVAddressCascaderItemModel *addrItem in item.datasource) {
                if ([addrItem.addrId isKindOfClass:NSNumber.class] && [addrItem.addrId isEqual:addrId]) {
                    [temp addObject:item];
                    [titles addObject:addrItem.name ?: @"-"];
                    addrItem.selected = YES;
                    item.selected = addrItem;
                    NSIndexPath *ip = [NSIndexPath indexPathForRow:[item.datasource indexOfObject:addrItem] inSection:0];
                    item.selectedIndexPath = ip;
                    contain = YES;
                }
            }
            if (!contain) {
                break;
            }
        }
        if (temp.count == 0 && [containers.firstObject isKindOfClass:JDISVAddressCascaderView.class]) {
            [temp addObject:containers.firstObject];
            [titles addObject:AddressL(@"address_popup_select")];
        }
        self.titles = [titles copy];
        // 渲染
        self.containers = [temp copy];
        
        self.category.titles = self.titles;
        [self.category reloadData];
        self.category.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
        [self.pagerView reloadData];
        [self.category selectItemAtIndex:self.titles.count-1];
        [self.category.listContainer didClickSelectedItemAtIndex:self.titles.count-1];
        dispatch_async(dispatch_get_main_queue(), ^{
            for (JDISVAddressCascaderView *item in self.containers) {
                [item scrollToSelected];
            }
        });
    }];
}


- (void)finishSelect {
    NSMutableArray *addressArray = [NSMutableArray array];
    for (JDISVAddressCascaderView *view in self.containers) {
        JDISVAddressCascaderItemModel *item = view.selected;
        if (item) {
            [addressArray addObject:item];
        } else {
            break;
        }
    }
    JDTAddressItemModel *addr = [[JDTAddressItemModel alloc] init];
    NSUInteger count = 0;
    for (JDISVAddressCascaderItemModel *item in addressArray) {
        if (count == 0) {
            addr.provinceId = item.addrId.stringValue;
            addr.provinceName = item.name;
        } else if (count == 1) {
            addr.cityId = item.addrId.stringValue;
            addr.cityName = item.name;
        } else if (count == 2) {
            addr.districtId = item.addrId.stringValue;
            addr.districtName = item.name;
        } else if (count == 3) {
            addr.townId = item.addrId.stringValue;
            addr.townName = item.name;
        }
        count ++;
    }
    [self.delegate sendNext:addr];
}

#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didClickSelectedItemAtIndex:(NSInteger)index {

}

#pragma mark - JXPagerViewDelegate

- (NSUInteger)tableHeaderViewHeightInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (UIView *)tableHeaderViewInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSUInteger)heightForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (UIView *)viewForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSInteger)numberOfListsInPagerView:(JXPagerView *)pagerView {
    return self.containers.count;
}

- (id<JXPagerViewListViewDelegate>)pagerView:(JXPagerView *)pagerView initListAtIndex:(NSInteger)index {
    return self.containers[index];
}

#pragma mark - getter

- (JXCategoryTitleView *)category {
    if (!_category) {
        _category = [[JXCategoryNumberView alloc] initWithFrame:CGRectMake(0, 0, [JDISVAddressModulUtil screenWidth], 47)];
        _category.titleLabelVerticalOffset = 8.f;
        _category.defaultSelectedIndex = 0;
        _category.delegate = self;
        _category.titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        _category.titleSelectedFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightMedium)];
        _category.averageCellSpacingEnabled = NO;
        _category.cellSpacing = 25;
        _category.contentEdgeInsetLeft = 0;
        _category.contentEdgeInsetRight = 0;
        _category.titleSelectedColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        _category.titleColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _category;
}

- (JXPagerView *)pagerView {
    if (!_pagerView) {
        _pagerView = [[JXPagerListRefreshView alloc] initWithDelegate:self listContainerType:(JXPagerListContainerType_ScrollView)];
        _pagerView.defaultSelectedIndex = 0;
        _pagerView.listContainerView.categoryNestPagingEnabled = YES;
        _pagerView.isListHorizontalScrollEnabled = NO;
        _pagerView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _pagerView.mainTableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _pagerView.listContainerView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        if (@available(iOS 15.0, *)) {
            _pagerView.mainTableView.sectionHeaderTopPadding = 0;
        }
    }
    return _pagerView;
}
@end
