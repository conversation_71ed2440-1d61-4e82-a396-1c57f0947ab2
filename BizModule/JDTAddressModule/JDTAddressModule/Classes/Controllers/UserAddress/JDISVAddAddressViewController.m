//
//  JDISVAddAddressViewController.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/12.
//


#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <CoreLocation/CLLocation.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>

#import "JDISVQQLocService.h"
#import "JDISVLocationManager.h"
#import "JDISVAddressService.h"
#import "JDISVAddAddressViewController.h"
#import "JDISVAddressModulUtil.h"
#import "JDISVAddressTagView.h"
#import "JDISVAdressInfoView.h"
#import "JDISVAddressCascaderViewController.h"
#import "JDISVAddressFullCascaderViewController.h"
#import "JDISVAddressMapViewController.h"
#import "JDISVAddAddressViewModel.h"
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVPermissionsModule/JDISVPermissionsModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
@interface JDISVAddAddressViewController ()<JDISVAdressInfoViewDelegate>

@property (strong, nonatomic)  UIScrollView *scrollView;
@property (weak, nonatomic) IBOutlet UIButton *sureButton;

@property (nonatomic, strong,nullable) UILabel *topTipLabel;

@property (nonatomic, strong,nullable) UIView *topLocationView;
@property (nonatomic, strong,nullable) UILabel* proviceLabel;
@property (nonatomic, strong,nullable) UILabel* detailLabel;
@property (nonatomic, strong,nullable) JDISVAdressInfoView *infoView;

@property (nonatomic, strong,nullable) JDISVAddressTagView *tagView;

@property (nonatomic, strong) JDISVAddAddressViewModel *viewModel;

@property (nonatomic, assign) CLLocationCoordinate2D QQlocation;
@property (nonatomic, strong) JDISVAddressResReverseCodeItem * reverseItem;
@property (nonatomic, assign) BOOL hideTopTip;
@property (nonatomic, assign) BOOL hideTopLocation;

@property (nonatomic, assign) BOOL keyboardEnable;
@property (nonatomic, assign) BOOL keyboardEnableAutoToolbar;
@property (nonatomic, assign) BOOL keyboardShouldResignOnTouchOutside;

@end

@implementation JDISVAddAddressViewController

- (instancetype)init {
    self = [super initWithNibName:@"JDISVAddAddressViewController" bundle:[NSBundle isv_address_bundle]];
    if (self) {
        _viewModel = [[JDISVAddAddressViewModel alloc] init];
        _location = kCLLocationCoordinate2DInvalid;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.viewModel.location = self.location;
    
    [self setupViews];
    // Do any additional setup after loading the view from its nib.
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    navigationBar
    .decorator
    .backgroundColor() //背景色
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
    }) //返回按钮
    .title(AddressL(@"address_list_add"), NSTextAlignmentCenter) //标题
    .render();
    
    self.hideTopTip = YES;
    self.hideTopLocation = YES;
    @weakify(self)
    [JDCDPermission authorizeWithType:JDCDPermissionType_Location completion:^(BOOL granted, BOOL firstTime) {
        @strongify(self)
        self.hideTopLocation = !granted;
        [[JDISVQQLocService inst] getLoc:self callBack:^(NSDictionary*  _Nullable object) {
            if([object isKindOfClass:NSDictionary.class]){
                NSValue *value = object[@"coor"];
//                self.QQlocation = [value coordinateValue];
                [[[JDISVAddressService shareInstance] reverseGeoCodeWithLocation:self.QQlocation] subscribeNext:^(JDISVAddressResReverseCodeItem * _Nullable x) {
                    @strongify(self)
                    self.reverseItem = x;
                    self.detailLabel.text = x.addressDetail;
                    NSString* province = x.provinceName;
                    NSString* city = x.cityName;
                    NSString* dis = x.districtName;
                    NSString* street = x.townName;
                    NSString* addr = [NSString stringWithFormat:@"%@%@%@%@",province?:@"",city?:@""
                                      ,dis?:@""
                                      ,street?:@""];
                    
                    self.proviceLabel.text = addr;
                } error:^(NSError * _Nullable error) {
                    
                }];
            }
        }];
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:YES];
    
    [self storeIQKeyboardSetting];
    [self setIQKeyboardSetting];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self restoreIQKeyboardSetting];
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
    
    CGFloat r1 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    CGFloat r2 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R2"];
    [self.infoView jdisv_addCorners:JDISVCornersMake(r1, r1, r2, r2) rect:self.infoView.bounds];
    [self.tagView jdisv_addCorners:JDISVCornersMake(r1, r1, r2, r2) rect:self.tagView.bounds];
}

- (void)setupViews {
    @weakify(self);
    self.title = AddressL(@"address_detail_title_add");
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo([UIWindow ka_uikit_navigationHeight]);
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.sureButton.mas_top).offset(-5);
    }];
    self.scrollView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;

    UILabel *topTipLabel = [[UILabel alloc] init];
    self.topTipLabel = topTipLabel;
    topTipLabel.text = AddressL(@"address_recognize_hint");
    topTipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
    topTipLabel.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07];
    topTipLabel.textAlignment = NSTextAlignmentLeft;
    topTipLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    [self.scrollView addSubview:topTipLabel];

    
    [self.scrollView addSubview:self.topLocationView];
        
    [self.scrollView addSubview:self.infoView];
    
    self.infoView.detailAddrEditBegin = ^{
        @strongify(self)
        [self.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(240);
        }];
    };

    [self.scrollView addSubview:self.tagView];
   
    [self.sureButton renderB1];
    self.scrollView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.sureButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
    [self.sureButton setTitle:AddressL(@"address_comfir_button_title") forState:UIControlStateNormal];
    RACSignal* merge = [RACSignal merge:@[RACObserve(self, hideTopTip),
                                          RACObserve(self, hideTopLocation),
                                          RACObserve(self.infoView, currentHeight)]];
    
    [merge subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        [self rebuidMas];
    }];
}

- (IBAction)sureButtonClick:(UIButton *)sender {
    self.viewModel.name = self.infoView.name;
    self.viewModel.phone = self.infoView.phone;
    
//    if (![self.viewModel.detail isEqualToString:self.infoView.detail]) {
//        self.viewModel.needUpdateLocation = YES;
//    }
    self.viewModel.detail = self.infoView.detail;
    
    if (self.tagView.isCustom) {
        self.viewModel.tagSource = @2;
    } else {
        self.viewModel.tagSource = @1;
    }
    self.viewModel.tag = self.tagView.tagText;
    self.viewModel.isDefault = self.tagView.isDefault;
    
    
    if (self.viewModel.name.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_name_hint")];
        return;
    }
    
    NSMutableCharacterSet *characterSet = [NSMutableCharacterSet alphanumericCharacterSet];
    [characterSet addCharactersInRange:NSMakeRange(19968, (40869-19968))];
    NSRange nameRange = [self.viewModel.name rangeOfCharacterFromSet:[characterSet invertedSet]];
    BOOL invalidateName = nameRange.length > 0;
    if (self.viewModel.name.length > 20 || invalidateName) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_name_illegal")];
        return;
    }
    
    if (self.viewModel.phone.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_phone_empty")];
        return;
    }
    
    NSRange phoneRange = [self.viewModel.phone rangeOfCharacterFromSet:[[NSCharacterSet characterSetWithCharactersInString:@"0123456789"] invertedSet]];
    BOOL invalidatePhone = phoneRange.length > 0;
    if (self.viewModel.phone.length != 11 || invalidatePhone) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_phone_illegal")];
        [self.infoView markPhoneRed];
        return;
    }
    
    if (self.infoView.area.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_title_select_area")];
        return;
    }
    
    if (self.infoView.detail.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_input_more")];
        return;
    }
    
    if (self.infoView.detail.length < 6 || self.infoView.detail.length > 120) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_specific_illegal")];
        return;
    }
    
//    @weakify(self)
//    if(self.viewModel.addr.town.addressID.intValue == -10000){
//        [PlatformService showLoadingInView:self.view];
//        [[[JDISVAddressService shareInstance]  translateToDetail:self.viewModel.addr
//                                      info:self.infoView.detail] subscribeNext:^(JDISVAddress*  addr) {
//            @strongify(self)
//            [PlatformService dismissInView:self.view];
//            [self userUseTraslateAddr:addr];
//        } error:^(NSError * _Nullable error) {
//            @strongify(self)
//            [PlatformService dismissInView:self.view];
//            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_resolve_4th_region_failed")];
//        }];
//        return;
//    }
    [self userSubmitAddress];
}

-(void)userSubmitAddress{
    @weakify(self);
    [PlatformService showLoadingInView:self.view];
    [self.viewModel addressAddWithBlock:^(NSNumber *addrId, NSString *msg) {
        @strongify(self)
    
        if (addrId) {
            if (self.addressAddSuccessCallback) {
                self.addressAddSuccessCallback(addrId);
            }
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:AddressL(@"address_add_address_success")];
            [self.navigationController popViewControllerAnimated:YES];
        } else {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:msg ?: AddressL(@"address_add_address_error")];
        }
        [PlatformService dismissInView:self.view];
    }];
}

//-(void)userUseTraslateAddr:(JDISVAddress*)addr{
//    @weakify(self);
//    
//    NSString* tip = [NSString stringWithFormat:AddressL(@"address_title_complete_dialog"),addr.town.addressName];
//    [KAAlert alert].config
//    .renderW3(tip)
//    .ka_addAction(AddressL(@"address_cancel"), ^(UIButton * _Nonnull button) {
//        //按钮样式有客户端自己确定
//        [button renderB5];
//    }, ^{
//        @strongify(self)
//        NSLog(@"");
//        [self userNotUserTraslateAddr];
//    })
//    .ka_addAction(AddressL(@"address_comfir_button_title"), ^(UIButton * _Nonnull button) {
//        //按钮样式有客户端自己确定
//        [button renderB3];
//    }, ^{
//        @strongify(self)
//        self.viewModel.addr = addr;
//        [self userSubmitAddress];
//    })
//    .alertShow();
//}

-(void)userNotUserTraslateAddr{
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_complete_dialog_hint")];
    @weakify(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self)
        [self openFullCascader:NO];
    });
}

/// MARK: - JDISVAdressInfoViewDelegate
- (void)locationButtonClick:(UIButton *)sender {
    [self.view endEditing:YES];
    @weakify(self)
    JDISVLocationManager *manager = [JDISVLocationManager shareInstance];
    __block BOOL isShow = NO;
    [manager GetLocationPermissionVerifactionWithController:self completion:^(JDISVLocationPermissionStatus permissionStatus) {
        if (permissionStatus == JDISVLocationPermissionStatusAuthorizedAlways || permissionStatus == JDISVLocationPermissionStatusAuthorizedWhenInUse ) {
            @strongify(self)
            if(isShow)
                return;
            isShow = YES;
            JDISVAddressMapViewController *mapController = [[JDISVAddressMapViewController alloc] init];
            mapController.addressSelectedCallback = ^(CLLocationCoordinate2D location) {
                @strongify(self)
                [self.viewModel reverseGeoCodeWithLocation:location block:^{
                    @strongify(self)
//                    self.viewModel.needUpdateLocation = NO;
                    self.viewModel.location = location;
                    self.viewModel.address = nil;
                    [self.infoView setArea:self.viewModel.area];
                    [self.infoView setDetail:self.viewModel.detail];
                    if (self.viewModel.detail.length < 6) {
                        [self.infoView showErrorTip];
                        [self.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(260);
                        }];
                    }
                }];
            };
            [self.navigationController pushViewController:mapController animated:YES];
        }else {
            [KAAlert alert].config
            .renderW4(AddressL(@"address_map_permssion_alert_title"), AddressL(@"address_map_permssion_alert_content"))
            .ka_addAction(AddressL(@"address_cancel"), ^(UIButton * _Nonnull button) {
                [button renderB5];
            }, ^{})
            .ka_addAction(AddressL(@"address_confirm"), ^(UIButton * _Nonnull button) {
                [button renderB3];
            }, ^{
                NSURL *url = [[NSURL alloc] initWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }).jdcd_show();
        }
    }];
}

- (void)areaButtonClick:(nonnull UIButton *)sender {
    [self.view endEditing:YES];
    if(self.fromMySetting){
        [self openFullCascader:YES];
    }else{
        [self openCascader];
    }
}

-(void)openFullCascader:(BOOL)noSelect{
    
    JDISVAddressFullCascaderViewController *cascaderController;
    if(noSelect){
        cascaderController = [[JDISVAddressFullCascaderViewController alloc]
                              initWithAddress:nil
                              noSelect:noSelect];
    }else{
        cascaderController =
        [[JDISVAddressFullCascaderViewController alloc]
         initWithAddress:self.viewModel.address
         noSelect:noSelect];
    }
    cascaderController.convert = NO;

    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    delegate.defultHeader.titleLabel.text = AddressL(@"address_complete_dialog_hint");
    cascaderController.transitioningDelegate = delegate;
    @weakify(self)
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel*  _Nullable x) {
        @strongify(self)
        self.viewModel.address = x;
        self.viewModel.reverseCodeItem = nil;
//        self.viewModel.needUpdateLocation = YES;
        NSString* tip;
//        if(x.town.addressID.intValue ==  -10000){
//            tip = [NSString stringWithFormat:@"%@%@%@", x.province.addressName ?: @"",x.city.addressName ?: @"",x.district.addressName ?: @""];
//        }else{
//            tip = [NSString stringWithFormat:@"%@%@%@%@", x.province.addressName ?: @"",x.city.addressName ?: @"",x.district.addressName ?: @"",x.town.addressName ?: @""];
//        }
        if ([x.townId isEqualToString:@"-10000"]) {
            tip = [NSString stringWithFormat:@"%@%@%@", x.provinceName ?: @"",x.cityName ?: @"",x.districtName ?: @""];
        } else {
            tip = [NSString stringWithFormat:@"%@%@%@%@", x.provinceName ?: @"",x.cityName ?: @"",x.districtName ?: @"",x.townName ?: @""];
        }
        [self.infoView setArea:tip];
    }];
    [self presentViewController:cascaderController animated:YES completion:nil];
}

-(void)openCascader{
    JDISVAddressCascaderViewController *cascaderController  = [[JDISVAddressCascaderViewController alloc] initWithAddress:nil limit:0];
    cascaderController.convert = NO;
    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    delegate.defultHeader.titleLabel.text = AddressL(@"address_label_title");
    cascaderController.transitioningDelegate = delegate;
    @weakify(self)
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel * _Nullable x) {
        @strongify(self)
        self.viewModel.address = x;
        self.viewModel.reverseCodeItem = nil;
//        self.viewModel.needUpdateLocation = YES;
        [self.infoView setArea:[NSString stringWithFormat:@"%@%@%@%@", self.viewModel.address.provinceName ?: @"",self.viewModel.address.cityName ?: @"",self.viewModel.address.districtName ?: @"",self.viewModel.address.townName ?: @""]];
    }];
    [self presentViewController:cascaderController animated:YES completion:nil];
}

-(void)rebuidMas{
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    CGFloat top = w2;
    self.topTipLabel.hidden = self.hideTopTip;
    [self.topTipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(30);
        make.top.equalTo(self.scrollView).mas_offset(top);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
    }];
    if(!self.hideTopTip){
        top += 30+w2;
    }
    
    self.topLocationView.hidden = self.hideTopLocation;
    [self.topLocationView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(80);
        make.top.equalTo(self.scrollView).offset(top);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
    }];
    self.topLocationView.layer.masksToBounds  = YES;
    CGFloat r1 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.topLocationView.layer.cornerRadius = r1;
    if(!self.hideTopLocation){
        top += 80+w2;
    }
    
    [self.infoView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(self.infoView.currentHeight);
        make.top.equalTo(self.scrollView).offset(top);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
    }];
    
    top+=self.infoView.currentHeight+w2;
    [self.tagView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(193);
        make.top.equalTo(self.scrollView).offset(top);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
        make.width.equalTo(self.view).offset(-2*w3);
    }];
    _scrollView.contentSize = CGSizeMake([UIScreen mainScreen].bounds.size.width, top+193+w2);
    [self.view layoutSubviews];
    [self.view layoutIfNeeded];
    [self.infoView setArea:self.infoView.area];
    
}

/// MARK: - Accessor

- (JDISVAddressTagView *)tagView {
    if(!_tagView){
        _tagView = (JDISVAddressTagView *)[JDISVAddressTagView isv_address_viewFromNibNamed:@"JDISVAddressTagView"];
        _tagView.clipsToBounds = YES;
    }
    return _tagView;
    
}

- (JDISVAdressInfoView *)infoView {
    if(!_infoView){
        _infoView = (JDISVAdressInfoView *)[JDISVAdressInfoView isv_address_viewFromNibNamed:@"JDISVAdressInfoView"];
        _infoView.clipsToBounds = YES;
        _infoView.delegate = self;
        _infoView.canLoc = [self infoViewCanLoc];
        _infoView.forAddAddress = YES;
    }
    return _infoView;
}

-(BOOL)infoViewCanLoc{
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:@"KaAddressAddPageId"];
    NSNumber* feature = config[@"features"][@"floors"][@"KaAddressFormFloor"][@"gisFeature"];
    return feature.boolValue;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
    }
    return _scrollView;
}

- (UIView*)topLocationView{
    if(!_topLocationView){
        _topLocationView = [[UIView alloc] init];
         
        _topLocationView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        UIImageView *imgView = [[UIImageView alloc]init];
        [imgView setImage:[UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(16, 16) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]]];
        [_topLocationView addSubview:imgView];
        [imgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.width.mas_equalTo(20);
            make.top.mas_equalTo(23);
            make.leading.mas_equalTo(20);
        }];
        
        self.detailLabel = [[UILabel alloc] init];
        self.detailLabel.text = AddressL(@"address_select_city_locate_loading");
        self.detailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.detailLabel.textAlignment = NSTextAlignmentLeft;
        self.detailLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium];
        [_topLocationView addSubview:self.detailLabel];
        [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_topLocationView).offset(23);
            make.leading.equalTo(imgView.mas_trailing).offset(5);
            make.trailing.mas_equalTo(-100);
            make.height.mas_equalTo(20);
        }];
        
        self.proviceLabel = [[UILabel alloc] init];
    //    self.topTipLabel = topTipLabel;
        self.proviceLabel.text = @"";
        self.proviceLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.proviceLabel.textAlignment = NSTextAlignmentLeft;
        self.proviceLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        [_topLocationView addSubview:self.proviceLabel];
        [self.proviceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.detailLabel.mas_bottom).offset(6);
            make.leading.equalTo(imgView);
            make.trailing.mas_equalTo(-100);
            make.height.mas_equalTo(18);
        }];
        
        UIButton *loctionBtn = [[UIButton alloc] init];
        [loctionBtn renderB5];
        [loctionBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] forState:UIControlStateNormal];
        [loctionBtn setTitle:AddressL(@"address_use_location") forState:UIControlStateNormal];
        [_topLocationView addSubview:loctionBtn];
        [loctionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.topLocationView);
            make.trailing.mas_equalTo(-18);
            make.height.mas_equalTo(30);
            make.width.mas_equalTo(72);
        }];
        [loctionBtn addTarget:self action:@selector(tapUseLoc) forControlEvents:UIControlEventTouchUpInside];
    }
    return _topLocationView;
}

//- (void)useTapPasteSubmit:(NSString*)str{
//    [PlatformService showLoadingInView:self.view];
//    @weakify(self)
//    [[self.viewModel processPasteStr:str] subscribeNext:^(NSDictionary*  _Nullable dic) {
//        NSLog(@"x");
//        @strongify(self);
//        [PlatformService dismissInView:self.view];
//        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_resolve_4th_region_succeed")];
//        JDISVAddress* addr  = [JDISVAddress defaultAddress];
//        if([dic isKindOfClass:NSDictionary.class]){
//            [self.infoView setDetail:dic[@"addressDetail"]];
//            addr.province.addressName = dic[@"provinceName"];
//            addr.province.addressID = dic[@"provinceId"];
//            addr.city.addressName = dic[@"cityName"];
//            addr.city.addressID = dic[@"cityId"];
//            addr.district.addressName = dic[@"districtName"];
//            addr.district.addressID = dic[@"districtId"];
//            addr.town.addressName = dic[@"townName"];
//            addr.town.addressID = dic[@"townId"];
//            
//            NSString* fullStr = [NSString stringWithFormat:@"%@%@%@%@", addr.province.addressName ?: @"",addr.city.addressName ?: @"",addr.district.addressName ?: @"",addr.town.addressName ?: @""];
//            [self.infoView setArea:fullStr];
//            self.topTipLabel.hidden = NO;
//            NSString* mobile = [dic jdcd_getStringElementForKey:@"mobile"];
//            if(mobile.length){
//                [self.infoView setPhone:mobile];
//            }
//            NSString* name = [dic jdcd_getStringElementForKey:@"name"];
//            if(name.length){
//                [self.infoView setName:name];
//            }
//            self.viewModel.addr= addr;
//            self.viewModel.reverseCodeItem = nil;
//            self.viewModel.needUpdateLocation = YES;
//            [self.infoView resigKey];
//            self.hideTopTip = NO;
//        }
//    } error:^(NSError * _Nullable error) {
//        @strongify(self);
//        [PlatformService dismissInView:self.view];
//        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_resolve_failed_manual_input")];
//    }];
//}

-(void)tapUseLoc{
    [self.viewModel processReverseCodeItem:self.reverseItem];
    
    [self.infoView setArea:self.proviceLabel.text];
    [self.infoView setDetail:self.detailLabel.text];
//    self.viewModel.needUpdateLocation = NO;
    self.viewModel.location = self.QQlocation;
}

#pragma mark - IQKeyboard
- (void)setIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = YES;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = NO;
}

- (void)storeIQKeyboardSetting {
    self.keyboardEnable = [IQKeyboardManager sharedManager].enable;
    self.keyboardEnableAutoToolbar = [IQKeyboardManager sharedManager].enableAutoToolbar;
    self.keyboardShouldResignOnTouchOutside = [IQKeyboardManager sharedManager].shouldResignOnTouchOutside;
}

- (void)restoreIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = self.keyboardEnable;
    [IQKeyboardManager sharedManager].enableAutoToolbar = self.keyboardEnableAutoToolbar;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = self.keyboardShouldResignOnTouchOutside;
}

@end
