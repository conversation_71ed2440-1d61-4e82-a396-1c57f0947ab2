//
//  JDISVEditAddressViewController.h
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/12.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVEditAddressViewController : UIViewController

@property(nonatomic,assign) BOOL fromMySetting;

@property (nonatomic, assign) BOOL saveAndUse;

@property (nonatomic, assign) BOOL disableDelete;

@property (nonatomic, copy) void(^addressEditSuccessCallback)(JDTAddressItemModel *model);

@property (nonatomic, copy) void(^addressDeleteSuccessCallback)(void);

- (instancetype)initWithAddressModel:(JDTAddressItemModel *)model;

@end

NS_ASSUME_NONNULL_END
