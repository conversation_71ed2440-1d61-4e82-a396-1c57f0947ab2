//
//  JDISVAddressLIstFloorController.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/2/25.
//

#import "JDISVAddressListFloorController.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVFloorRenderModule/UIView+JDCDISVFloorRenderCorner.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVAddrListFloorModel.h"
#import "JDISVAddressTracker.h"

@interface JDISVAddressListFloorController()

@property(strong,nonatomic) JDISVAddrListFloorModel *viewModel;

@end

@implementation JDISVAddressListFloorController

//-(void)viewWillDisappear:(BOOL)animated {
//    [super viewWillDisappear:animated];
//    
//    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
//    [defaults removeObjectForKey:@"deliveryAddressId"];
//    [defaults removeObjectForKey:@"billingAdrressId"];
//    [defaults synchronize];
//}

- (void)viewDidLoad {
    self.pageId = @"KaAddressPageId";
    [super viewDidLoad];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.viewModel = [[JDISVAddrListFloorModel alloc] init];
    self.viewModel.canSelect = self.canSelect;
    self.viewModel.selectedAddressId = self.selectedAddressId;
    self.floorDelegate = self.viewModel;
    self.viewModel.pageId = self.pageId;
    self.viewModel.fromMySetting = self.fromMySetting;
    self.listView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

//    self.listView.stretchBackground = YES;
    self.listView.actionDelegate = self;
    if(KSAAPP){
        [JDISVAddressTracker PV:@"Address_List" param:@{@"page_id":@"Address_List",
                                                      }];

    }else{
        [JDISVAddressTracker PV:@"Address_List" param:@{@"page_id":@"Address_List",
                                                        @"page_type":@"address"
                                                      }];
    }
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    CGFloat r30 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R30"];
    CGRect bounds = CGRectMake(0, 0, CGRectGetWidth(self.listView.listContentView.frame), CGRectGetHeight(self.listView.listContentView.frame));
    [self.listView.contentView isv_floorRender_addRoundedCorners:UIRectCornerTopRight | UIRectCornerTopLeft withRadii:CGSizeMake(r30, r30) viewRect:bounds];
}

- (BOOL)action:(JDCDISVAction * __nullable)action{
    if([super action:action])
        return YES;
    
    if([action.actionType isEqualToString:@"JDISVAddrListSelectNewID"]){
        if (self.canSelect) {
            self.selectedAddressId = action.value;
            self.viewModel.selectedAddressId = self.selectedAddressId;
            [self requestData:YES];
        }
        return YES;
    }
    
    if([action.actionType isEqualToString:@"JDISVAddrListSelected"]){
        if (self.canSelect) {
            JDTAddressItemModel *addr = action.value;
            if (self.addrCallback) {
                self.addrCallback(addr);
            } else {
                [PlatformService setDefaultAddress:addr];
            }
//            if (self.backVC) {
//                [self.navigationController popToViewController:self.backVC animated:YES];
//            } else {
                [self.navigationController popViewControllerAnimated:YES];
//            }
        }
        return YES;
    }
    
    if([action.actionType isEqualToString:@"JDISVAddBillingAddress"]){
        NSDictionary *billingAddrDic = action.value;
        NSString *billingAddrId = [billingAddrDic objectForKey:@"billingAddrId"];
        self.selectedBillingAddressId = billingAddrId;
        self.viewModel.selectedBillingAddressId = billingAddrId;
        if (self.billingAddrCallback) {
            self.billingAddrCallback(billingAddrId);
        }
        return YES;
    }
    
    return NO;
}

-(void)showLoading{
    [PlatformService showLoadingInView:self.view];
}

-(void)hideLoading{
    [PlatformService dismissInView:self.view];
}

@end
