//
//  JDISVAddrAddNewFloor.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/7.
//

#import "JDISVAddrAddNewFloor.h"
#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVAddressModulUtil.h"
#import "JDISVAddAddressViewController.h"
#import "JDISVAddrAddNewModule.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

@interface JDISVAddrAddNewFloor ()

@property (strong,nonatomic) UIButton* addButton;

@property (strong,nonatomic) JDISVAddrAddNewModule* model;

@end

@implementation JDISVAddrAddNewFloor

-(instancetype)initWithFrame:(CGRect)frame{
   self = [super initWithFrame:frame];
    if(self){
        [self setupUI];
    }
    return self;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    self.model = floorModel;
}

-(void)setupUI{
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.addButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self addSubview:self.addButton];
    [self.addButton renderB1];
    [self.addButton setImage:[UIImage isv_address_imageNamed:@"jdisv_address_add_button"] forState:UIControlStateNormal];
    [self.addButton setTitle:AddressL(@"address_detail_title_add") forState:UIControlStateNormal];
    [self.addButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    if (@available(iOS 8.2, *)) {
        self.addButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
    }
    [self.addButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(6);
        make.bottom.mas_equalTo(-6);
        make.leading.mas_equalTo(12);
        make.trailing.mas_equalTo(-12);
    }];
    [self.addButton addTarget:self action:@selector(tapAdd) forControlEvents:UIControlEventTouchUpInside];
}
-(void)tapAdd{
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVAddrListAddNew"];
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVAddrListAddNew"]) {
        
//        [JDRouter openURL:@"router://KSAAddressModule/addAddressViewController" arg:@{@"fromMySetting":@(self.model.fromMySetting)}
//                    error:nil
//               completion:^(id  _Nullable object) {
//            UIViewController *vc = [object objectForKey:@"viewController"];
//            if (vc) {
//                vc.hidesBottomBarWhenPushed = YES;
//                [controller.navigationController pushViewController:vc animated:YES];
//            }
//            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
//            NSNumber *addrId = [object objectForKey:@"info"];
//            if (addrId) {
//                [defaults setObject:addrId forKey:@"deliveryAddressId"];
//                JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
//                [self isv_sendAction:action];
//            }
//            
//            NSNumber *billingAddrId = [object objectForKey:@"billingAddrId"];
//            if (billingAddrId) {
//                [defaults setObject:billingAddrId forKey:@"billingAdrressId"];
//                if (controller) {
//                    NSArray *vcArr = [controller.navigationController childViewControllers];
//                    
//                    BOOL flag = NO;
//                    id targetVC = nil;
//                    for (NSInteger i=0; i < vcArr.count; i++) {
//                        id vcObj = [vcArr objectAtIndex:i];
//                        NSString *typeString = NSStringFromClass(object_getClass(vcObj));
//                        if ([typeString isEqualToString:@"JDISVSettlementController"]) {
//                            flag = YES;
//                            targetVC = vcObj;
//                            break;
//                        }
//                    }
//                    
//                    if (flag == YES && targetVC) {
////                        [controller.navigationController popViewControllerAnimated:NO];
////                        [controller.navigationController popToViewController:targetVC animated:YES];
//                    }
//                }
//                
////                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVAddBillingAddress"];
////                action.value = @{@"billingAddrId":billingAddrId};
////                [self isv_sendAction:action];
//            }
//            [defaults synchronize];
//            
//        }];
        
//        [KAAlert alert].config
//            .renderW3a(AddressL(@"billing_address_add_question_add_delivery_address"), NSTextAlignmentCenter)
//            .ka_addAction(AddressL(@"next_time"), ^(UIButton * _Nonnull button) {
//                [button renderB5];
//            }, ^{
//                // Next time add delivery address
//                [self openAddBillingAddressVC:controller addBillingAddress:NO];
//            })
//            .ka_addAction(AddressL(@"yes"), ^(UIButton * _Nonnull button) {
//                [button renderB3];
//            }, ^{
//                // also set a delivery address
//                [self openAddBillingAddressVC:controller addBillingAddress:YES];
//                
//            }).jdcd_show();
        
//        if (KSAAPP) return YES;
        
        
        JDISVAddAddressViewController *addnewVC = [[JDISVAddAddressViewController alloc] init];
        addnewVC.fromMySetting = self.model.fromMySetting;
        @weakify(self)
        addnewVC.addressAddSuccessCallback = ^(NSNumber * _Nonnull addrId) {
            @strongify(self)
            JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
            [self isv_sendAction:action];
        };
        [controller.navigationController pushViewController:addnewVC animated:YES];
        return YES;
    }
    return NO;
}

//- (void)openAddBillingAddressVC:(UIViewController *)controller
//              addBillingAddress:(BOOL)addBillingAddress {
//    
//    [JDRouter openURL:@"router://KSAAddressModule/addAddressViewController"     arg:@{@"fromMySetting":@(self.model.fromMySetting),@"addBillingAddress":@(addBillingAddress)}
//                error:nil
//           completion:^(id  _Nullable object) {
//        UIViewController *vc = [object objectForKey:@"viewController"];
//        if (vc) {
//            [controller.navigationController pushViewController:vc animated:YES];
//        }
//        NSNumber *addrId = [object objectForKey:@"info"];
//        if (addrId) {
//            JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
//            [self isv_sendAction:action];
//        }
//    }];
//}

@end
