//
//  JDISVAddrListFloor.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/3.
//

#import "JDISVAddrListFloor.h"
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>
#import <JDISVKAUIKitModule/KALabel.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import "JSISVAddressListItemViewModel.h"
#import "JDISVAddressModulUtil.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import "JDISVAddrListModule.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVEditAddressViewController.h"
#import <JDISVFloorRenderModule/JDISVBaseFloorViewController.h>
#import "NSString+AddressExtension.h"

@import JDTInfrastructureModule;

@interface JDISVAddrListFloor ()

@property (weak, nonatomic) IBOutlet UILabel *detailLabel;//改为电话
@property (weak, nonatomic) IBOutlet UILabel *userInfoLabel;//改为详细地址
@property (weak, nonatomic) IBOutlet UIButton *editButton;
@property (weak, nonatomic) IBOutlet UILabel *regionLabel;//改为姓名
@property (weak, nonatomic) IBOutlet UIView *lineView;

@property (strong, nonatomic)  UILabel *defaultTag;
@property (strong, nonatomic)  UILabel *customTag;


@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentLeadingLayout;
//@property (weak, nonatomic) IBOutlet UIButton *selectedButton;

@property (weak,nonatomic) JDISVAddrListModule* floorModel;

@end

@implementation JDISVAddrListFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    [self.editButton setImage:[UIImage ka_iconWithName:JDIF_ICON_EDIT imageSize:CGSizeMake(20, 20) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]] forState:UIControlStateNormal];
    
    [self.editButton addTarget:self action:@selector(tapEdit) forControlEvents:UIControlEventTouchUpInside];
    
    self.lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.regionLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightSemibold];
//    [self.regionLabel  setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.regionLabel  setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    self.detailLabel.jdisv_fontPicker = JDISVJDFontPickerWithKey(@"#T9");
    self.userInfoLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightRegular];

    self.regionLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.detailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.userInfoLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];

//    [self.selectedButton setTitle:@"" forState:(UIControlStateNormal)];
//    [self.selectedButton setImage:[UIImage ka_iconWithName:JDIF_ICON_RADIO imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"]] forState:(UIControlStateNormal)];
//    [self.selectedButton setImage:[UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] forState:(UIControlStateSelected)];
//    self.selectedButton.hidden = YES;
//    self.selectedButton.userInteractionEnabled = NO;
    self.contentLeadingLayout.constant = 18;
}


- (void)floorDidLoad:(JDISVAddrListModule*)floorModel{
    self.floorModel = floorModel;
  
//    if (floorModel.canSelect) {
//        self.selectedButton.hidden = NO;
//        self.contentLeadingLayout.constant = 56;
//        self.selectedButton.selected = (self.floorModel.data.addModel.addressId == floorModel.selectedAddressID.integerValue);
//    } else {
//        self.selectedButton.hidden = YES;
        self.contentLeadingLayout.constant = 18;
//        self.selectedButton.selected = NO;
//    }
    JSISVAddressListItemViewModel* viewModel = floorModel.data;
    if(viewModel.areaCode.length>0){
        NSString *areaCode = [viewModel.areaCode stringByReplacingOccurrencesOfString:@"0966" withString:@"966"];
        self.detailLabel.text = [NSString stringWithFormat:@"+%@ %@",areaCode,viewModel.mobile];
    }else{
        self.detailLabel.text = viewModel.mobile;
    }
    NSString* lang = [NSString getKAUseLang];
    if([lang isEqualToString:@"en"]){
        if(viewModel.detailAddress.length){
            self.userInfoLabel.text = [NSString stringWithFormat:@"%@, %@",viewModel.detailAddress,viewModel.region];
        }else{
            self.userInfoLabel.text = viewModel.region;
        }
    }else{
        if(viewModel.detailAddress.length){
            self.userInfoLabel.text = [NSString stringWithFormat:@"%@, %@",viewModel.region,viewModel.detailAddress];
        }else{
            self.userInfoLabel.text = viewModel.region;
        }
    }
    self.userInfoLabel.numberOfLines = 2;
    
    NSString *firstName = [NSString isEmpty:viewModel.firstName] ? viewModel.name : viewModel.firstName;
    if(![NSString isEmpty:viewModel.lastName] ){
        self.regionLabel.text = [NSString stringWithFormat:@"%@ %@", firstName, viewModel.lastName];
    } else {
        self.regionLabel.text = firstName;
    }

    self.lineView.hidden = viewModel.isHiddenLine;
    
    self.backgroundColor = [UIColor clearColor];
    //customtag
    MASViewAttribute* defaultTrailing = self.regionLabel.mas_trailing;
    [self.customTag removeFromSuperview];
    self.customTag = nil;
    if(viewModel.tag.length){
        [self.contentView addSubview:self.customTag];
        self.customTag.text = viewModel.tag;
        CGSize size = [viewModel.tag jdcd_getStringSize:self.customTag.font constraintsSize:CGSizeMake(200, 20)];
        [self.customTag mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.regionLabel.mas_trailing).mas_offset(6);
            make.centerY.mas_equalTo(self.regionLabel);
            make.width.mas_equalTo(ceil(size.width+8));
            make.height.mas_equalTo(16);
            if(!viewModel.isDefault){
                make.trailing.mas_equalTo(self.contentView).mas_lessThanOrEqualTo(-12);
            }
        }];
        [self.customTag setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        [self.customTag setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        defaultTrailing = self.customTag.mas_trailing;
    }
    [self.defaultTag removeFromSuperview];
    self.defaultTag = nil;
    if(viewModel.isDefault){
        
        [self.contentView addSubview:self.defaultTag];
        NSString* defaultStr =  AddressL(@"address_default_label");
        self.defaultTag.text= defaultStr;
        CGSize size = [defaultStr jdcd_getStringSize:self.customTag.font constraintsSize:CGSizeMake(200, 20)];
        [self.defaultTag mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(defaultTrailing).mas_offset(6);
            make.centerY.mas_equalTo(self.regionLabel);
            make.width.mas_equalTo(ceil(size.width+8));
            make.height.mas_equalTo(16);
            make.trailing.mas_equalTo(self.contentView).mas_lessThanOrEqualTo(-12);
        }];
        [self.defaultTag setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        [self.defaultTag setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    }
}


- (CGFloat)screenWidth {
    return [UIScreen mainScreen].bounds.size.width;
}

- (CGFloat)screenHeight {
    return [UIScreen mainScreen].bounds.size.height;
}

- (void)tapEdit {
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVAddrListEdit"];
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVAddrListEdit"]){
        [self editAddress:controller];
        return YES;
    }
    
    if([action.actionType isEqualToString:@"JDISVAddrListSetDefault"]){
        [self setDefautAddress:controller];
        return YES;
    }
    if([action.actionType isEqualToString:@"JDISVAddrListDelete"]){
        // 删除地址
        [KAAlert alert].config
        .renderW3a(AddressL(@"address_list_delete_address_title"), NSTextAlignmentCenter)
        .ka_addAction(AddressL(@"address_cancel"), ^(UIButton * _Nonnull button) {
            [button renderB5];
        }, ^{
            // 取消
        })
        .ka_addAction(AddressL(@"address_confirm"), ^(UIButton * _Nonnull button) {
            [button renderB3];
        }, ^{
            // 删除
            [self deleteAddress:controller];
            
        }).jdcd_show();
        return YES;
    }
    if([action.actionType isEqualToString:kJDCDISVFloorDidSelected]){
        if (self.floorModel.canSelect){
            [self clickFloor];
        }
        return YES;
    }
    return NO;
}

-(void)clickFloor{
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVAddrListSelected"];
    JDTAddressItemModel *addr = self.floorModel.data.addModel;
    action.value = addr;
    [self isv_sendAction:action];
}
-(void)deleteAddress:(UIViewController*)controller{
    JDTAddressItemModel *currentModel = self.floorModel.data.addModel;
    @weakify(self)
    [PlatformService showLoadingInView:controller.view];
    [[OOPNetworkManager sharedManager] POST:@"address/c/deleteUserAddress?apiCode=b2c.cbff.address.c.deleteUserAddress" parameters:@{@"addressId": @(currentModel.addressId)} headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:controller.view];
        if (error) {
            NSString *message = responseObject[@"msg"];
            [KAToast alert].config.renderW1(message?:AddressL(@"address_common_network_error")).jdcd_show();
        } else {
            BOOL success = [responseObject[@"success"] boolValue];
            if (success) {
                JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
                [self isv_sendAction:action];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:AddressL(@"address_delete_address_success")];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:AddressL(@"address_delete_address_error")];
            }
        }
    }];
}

-(void)setDefautAddress:(UIViewController*)controller{
    JDTAddressItemModel *currentModel = self.floorModel.data.addModel;
    NSMutableDictionary *params = [[currentModel yy_modelToJSONObject] mutableCopy];
    params[@"defAddress"] = @(YES);
    @weakify(self)
    [PlatformService showLoadingInView:controller.view];
    [[OOPNetworkManager sharedManager] POST:@"address/c/updateUserAddress?apiCode=b2c.cbff.address.c.updateUserAddress" parameters:params headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self);
        [PlatformService dismissInView:controller.view];
        if (error) {
            NSString *message = responseObject[@"msg"];
            [KAToast alert].config.renderW1(message?:AddressL(@"address_common_network_error")).jdcd_show();
        } else {
            BOOL success = [responseObject[@"success"] boolValue];
            if (success) {
                JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
                [self isv_sendAction:action];
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:AddressL(@"address_set_default_address_success")];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:AddressL(@"address_set_default_address_error")];
            }
        }
    }];
}

-(void)editAddress:(UIViewController *)controller{
    JDTAddressItemModel *model = self.floorModel.data.addModel;

    NSMutableDictionary * dic = [NSMutableDictionary dictionary];
    [dic setObject:@(self.floorModel.canSelect) forKey:@"saveAndUse"];
    [dic setObject:@((self.floorModel.selectedAddressID.intValue == model.addressId)) forKey:@"disableDelete"];
    [dic setObject:@(self.floorModel.fromMySetting) forKey:@"fromMySetting"];
    [dic setObject:[model yy_modelToJSONString] forKey:@"KSAAddressModel"];
    
//    UIViewController *KSAeditVC = [JDRouter openURL:@"router://KSAAddressModule/editAddressViewController" arg:dic error:nil completion:^(NSDictionary * object) {
//        NSString * EditSuccess = [object jdcd_getStringElementForKey:@"EditSuccess"];
//        
//        if ([EditSuccess isEqualToString:@"1"]) {
//            JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
//            [self isv_sendAction:action];
//        }else{
//            if (self.floorModel.canSelect && self.floorModel.data.addModel.addressId == self.floorModel.selectedAddressID.integerValue) {
//                [self sendNewSelectAddr];
//            } else {
//                JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorNextShowRequestAction];
//                [self isv_sendAction:action];
//            }
//        }
//    }];
//    if(KSAeditVC){
//        KSAeditVC.hidesBottomBarWhenPushed = YES;
//        [controller.navigationController pushViewController:KSAeditVC animated:YES];
//    }
//    
//    if (KSAAPP) return;
    
    JDISVEditAddressViewController *editVC = [[JDISVEditAddressViewController alloc] initWithAddressModel:model];
    editVC.saveAndUse = self.floorModel.canSelect;
    editVC.disableDelete = (self.floorModel.selectedAddressID.intValue == model.addressId);
    @weakify(self)
    editVC.addressEditSuccessCallback = ^(JDTAddressItemModel * _Nonnull model) {
        @strongify(self)
        JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
        [self isv_sendAction:action];
    };
    editVC.addressDeleteSuccessCallback = ^{
        @strongify(self)
        if (self.floorModel.canSelect && self.floorModel.data.addModel.addressId == self.floorModel.selectedAddressID.integerValue) {
            [self sendNewSelectAddr];
        }else{
            JDCDISVAction* action = [JDCDISVAction actionWithType:kJDCDISVFloorNextShowRequestAction];
            [self isv_sendAction:action];
        }
        //TODO
        //        [self selectNextAddressWithIndexPath:indexPath];
    };
    editVC.fromMySetting = self.floorModel.fromMySetting;
    [controller.navigationController pushViewController:editVC animated:YES];
    
}

-(void)sendNewSelectAddr{
    
    for (JSISVAddressListItemViewModel* vm in self.floorModel.allData){
        if (vm.addModel.addressId != self.floorModel.selectedAddressID.integerValue) {
            JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVAddrListSelectNewID"];
            action.value = @(vm.addModel.addressId);
            [self isv_sendAction:action];
            return;
        }
    }
}

-(UILabel*)defaultTag{
    if(!_defaultTag){
        UIColor* c13 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"];
        CGFloat r60 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        UIFont* font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
        _defaultTag = [[UILabel alloc] initWithFrame:CGRectZero];
        _defaultTag.textColor = c13;
        _defaultTag.layer.borderColor = c13.CGColor;
        _defaultTag.layer.borderWidth = 1;
        _defaultTag.layer.cornerRadius = r60;
        _defaultTag.backgroundColor = UIColor.clearColor;
        _defaultTag.font = font;
        _defaultTag.textAlignment = NSTextAlignmentCenter;
    }
    return _defaultTag;
}

-(UILabel*)customTag{
    if(!_customTag){
        UIColor* c13 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
        CGFloat r60 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        UIFont* font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"];
        _customTag = [[UILabel alloc] initWithFrame:CGRectZero];
        _customTag.textColor = c13;
        _customTag.layer.borderColor = c13.CGColor;
        _customTag.layer.borderWidth = 1;
        _customTag.layer.cornerRadius = r60;
        _customTag.backgroundColor = UIColor.clearColor;
        _customTag.font = font;
        _customTag.textAlignment = NSTextAlignmentCenter;
    }
    return _customTag;
}
@end


//
//@implementation JDISVAddrListContainerFloor{
//    JDISVAddrListFloor* __addrListFloor;
//}
//
//-(instancetype)init{
//    self = [super init];
//    if(self){
//        [self setupUI];
//    }
//    return self;
//}
////-(instancetype)initWithFrame:(CGRect)frame{
////    self = [super initWithFrame:frame];
////    if(self){
////        [self setupUI];
////    }
////    return self;
////}
//-(void)setupUI{
//    NSBundle* bdl = [NSBundle jdcd_moduleWithBundleName:@"JDISVAddressModule" class: NSClassFromString(@"JDISVAddressModulUtil")];
//    __addrListFloor = [[bdl loadNibNamed:@"JDISVAddrListFloor" owner:self options:nil] objectAtIndex:0];
//    [self addSubview:__addrListFloor];
//    [__addrListFloor mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.equalTo(self);
//    }];
//    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
//    self.backgroundColor = [UIColor redColor];
//
//}
//- (void)floorDidLoad:(JDISVAddrListModule*)floorModel{
//    [__addrListFloor floorDidLoad:floorModel];
//}
//-(void)layoutSubviews{
//    [super layoutSubviews];
//    __addrListFloor.frame = self.bounds;
//}
//@end
