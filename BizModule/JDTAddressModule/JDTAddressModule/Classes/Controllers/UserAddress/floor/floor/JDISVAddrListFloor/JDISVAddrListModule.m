//
//  JDISVAddrListModule.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/3.
//

#import "JDISVAddrListModule.h"
#import "JDISVAddrListFloor.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

JDISVRegisterFloorModule(KaAddrListDataFloor, JDISVAddrListModule)

@interface JDISVAddrListModule ()

@end

@implementation JDISVAddrListModule

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.data = data[@"data"];
    NSNumber* canselect = data[@"ext"][@"canSelect"];
    self.canSelect = canselect.boolValue;

    NSDictionary* ext = data[@"ext"];
    NSNumber* from = ext[@"fromMySetting"];
    self.fromMySetting = from.boolValue;
    
    self.selectedAddressID = data[@"ext"][@"selectAddrID"];
    self.allData = commonModel.allFloorOriginData[@"data"];
    
    NSString * addressStr = [NSString stringWithFormat:@"%@%@",self.data.region,self.data.detailAddress];
    CGFloat addressWidth = self.canSelect?[UIScreen mainScreen].bounds.size.width-56-50:[UIScreen mainScreen].bounds.size.width-18-50;
    
    CGSize loctionBtnSize = [addressStr jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake(addressWidth, 500)];
    CGFloat loctionBtnHeight = loctionBtnSize.height>30?30:loctionBtnSize.height;
    
    self.floorHeight = 119-34 + loctionBtnHeight;
};

//- (CGFloat)floorHeight{
//    NSString * addressStr = [NSString stringWithFormat:@"%@%@",self.data.region,self.data.detailAddress];
//    CGFloat addressWidth = self.canSelect?[UIScreen mainScreen].bounds.size.width-56-50:[UIScreen mainScreen].bounds.size.width-18-50;
//
//    CGSize loctionBtnSize = [addressStr jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake(addressWidth, 500)];
//
//    return 119-34 + loctionBtnSize.height;
//
//    //    return 119;
//}


- (JDISVFloorType)floorType{
    return JDISVFloorTypeScrollFloor;
}

//- (UIView *)floorView{
//    return [[JDISVAddrListContainerFloor alloc] init];
//}
- (Class)tableViewFloorClass{
    return NSClassFromString(@"JDISVAddrListFloor");
}

- (UINib *)tableViewFloorNib{
    NSBundle* bdl = [NSBundle jdcd_moduleWithBundleName:@"JDTAddressModule" class: NSClassFromString(@"JDISVAddressModulUtil")];
    return  [UINib nibWithNibName:@"JDISVAddrListFloor" bundle:bdl];
}


- (BOOL)sideSlipEnable{
    return YES;
}

- (NSArray<JDCDISVAction *>*)sideSlipActions{
    JDCDISVAction * actDefault = [JDCDISVAction actionWithType:@"JDISVAddrListSetDefault"];

    actDefault.value = @{
        @"title":AddressL(@"address_list_menu_set_default"),
        @"color":[[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"]
    };
    JDCDISVAction* actDelete = [JDCDISVAction actionWithType:@"JDISVAddrListDelete"];
    actDelete.value = @{
        @"title":AddressL(@"address_list_menu_delete"),
        @"color":[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]
    };
    NSMutableArray* actions = [NSMutableArray array];
    if (self.selectedAddressID.intValue != self.data.addModel.addressId) {
        [actions addObject:actDelete];
    }
    
    if (!self.data.isDefault) {
        [actions addObject:actDefault];
    }
    return actions;
}

@end

