//
//  JDISVAddrListPageFloor.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/15.
//

#import "JDISVAddrListPageFloor.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

JDISVRegisterVirtualFloorModule(KaAddressListFloor, JDISVAddrListPageFloor)

@interface JDISVAddrListPageFloor()

@end

@implementation JDISVAddrListPageFloor

- (NSArray <NSDictionary *>*)realFloorsWithData:(NSArray *)datas ext:(NSDictionary *)ext commond:(JDISVFloorCommonModel *)commonModel{
    NSMutableArray* listData = [NSMutableArray array];
    NSMutableDictionary* extm = [ext mutableCopy];
    if(!extm){
        extm[@"fromMySetting"] = @(YES);
    }
    
    BOOL noNeedNaviBar = [[extm objectForKey:@"noNeedNaviBar"] boolValue];
    if (!noNeedNaviBar) {
        NSDictionary* navData =
            @{
                @"floorId":@"KaAddrListNavigationFloor",
                @"data":@{},
                @"ext":ext?:@{}
            };
        [listData addObject:navData];
    }
    int floorIndex = 0;
    for(NSObject* obj in datas){
        NSDictionary* listDic =
        @{ @"floorId":@"KaAddrListDataFloor",
           @"data":obj,
           @"ext":ext?:@{}
        };
        floorIndex++;
        [listData addObject:listDic];
    }
//
    NSDictionary* addNewData =@{
            @"floorId":@"KaAddrListAddNewFloor",
            @"data":@{},
            @"ext":ext?:@{}
        };
    if(datas.count < 25){
        [listData addObject:addNewData];
    }
    return listData;
}

@end
