//
//  JDISVAddrAddNewModule.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/7.
//

#import "JDISVAddrAddNewModule.h"
#import "JDISVAddrAddNewFloor.h"
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
JDISVRegisterFloorModule(KaAddrListAddNewFloor, JDISVAddrAddNewModule)

@implementation JDISVAddrAddNewModule

- (UIView *)floorView {
    UIView* v = [[JDISVAddrAddNewFloor alloc] init];
    return v;
}

- (CGFloat)floorHeight {
    return 52 + [[UIApplication sharedApplication] jdt_currentKeyWindow].safeAreaInsets.bottom;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeBottomFixFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary* ext = data[@"ext"];
    NSNumber* from = ext[@"fromMySetting"];
    self.fromMySetting = from.boolValue;
}

@end
