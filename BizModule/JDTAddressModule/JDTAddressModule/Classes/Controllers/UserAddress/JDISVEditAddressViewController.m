//
//  JDISVEditAddressViewController.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/12.
//


#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>

#import "JDISVEditAddressViewController.h"
#import "JDISVAddressModulUtil.h"
#import "JDISVAddressCascaderViewController.h"
#import "JDISVAdressInfoView.h"
#import "JDISVAddressTagView.h"
#import "JDISVAddressMapViewController.h"
#import "JDISVEditAddressViewModel.h"
#import "JDISVLocationManager.h"
#import "JDISVAddressFullCascaderViewController.h"
#import "JDISVAddressService.h"
#import "ADRTencentMapController.h"

@interface JDISVEditAddressViewController () <JDISVAdressInfoViewDelegate>

@property (weak, nonatomic) IBOutlet UIButton *suerButton;

@property (weak, nonatomic) IBOutlet UIButton *deleteButton;

@property (strong, nonatomic)  UIScrollView *scrollView;

@property (nonatomic, strong,nullable) JDISVAdressInfoView *infoView;

@property (nonatomic, strong,nullable) JDISVAddressTagView *tagView;

@property (nonatomic, strong) JDISVEditAddressViewModel *viewModel;

@property (nonatomic, assign) BOOL keyboardEnable;
@property (nonatomic, assign) BOOL keyboardEnableAutoToolbar;
@property (nonatomic, assign) BOOL keyboardShouldResignOnTouchOutside;

@end


@implementation JDISVEditAddressViewController


- (instancetype)initWithAddressModel:(JDTAddressItemModel *)model {
    self = [super initWithNibName:@"JDISVEditAddressViewController" bundle:[NSBundle isv_address_bundle]];
    if (self) {
        _viewModel = [[JDISVEditAddressViewModel alloc] initWithAddressModel:model];
        if (model.townId.intValue != -10000) {
            _viewModel.area = [NSString stringWithFormat:@"%@%@%@%@", model.provinceName ?: @"", model.cityName ?: @"", model.districtName ?: @"", model.townName ?: @""];
        } else {
            _viewModel.area = [NSString stringWithFormat:@"%@%@%@", model.provinceName ?: @"", model.cityName ?: @"", model.districtName ?: @""];
        }
        _viewModel.detail = model.addressDetail;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self setupViews];
    [self configUI];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES];
    [self storeIQKeyboardSetting];
    [self setIQKeyboardSetting];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self restoreIQKeyboardSetting];
}

- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
    CGFloat r1 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    CGFloat r2 = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R2"];
    [self.infoView jdisv_addCorners:JDISVCornersMake(r1, r1, r2, r2) rect:self.infoView.bounds];
    [self.tagView jdisv_addCorners:JDISVCornersMake(r1, r1, r2, r2) rect:self.tagView.bounds];
}

- (void)setupViews {
    
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    navigationBar
    .decorator
    .backgroundColor() //背景色
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
    }) //返回按钮
    .title(AddressL(@"address_detail_title_edit"), NSTextAlignmentCenter) //标题
//    .textActionButton(AddressL(@"address_list_menu_delete"), ^(KANavigationBarButtonStandardItem *item) {
//        __strong typeof(weakSelf) strongSelf = weakSelf;
//        [strongSelf deleteButtonClick:nil];
//    })
    .render();
    
    if (self.disableDelete) {
        navigationBar.navigationItem.rightBarButtonItems = @[];
    }
    
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo([UIWindow ka_uikit_navigationHeight]);
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.suerButton.mas_top).offset(-5);
    }];
    [self.scrollView addSubview:self.infoView];
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    @weakify(self)
    self.infoView.detailAddrEditBegin = ^{
        @strongify(self)
        [self.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(240);
        }];
    };
    [self.infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(240);
        make.top.equalTo(self.scrollView);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
        make.width.equalTo(self.view).offset(-2*w3);
    }];
    
    [self.scrollView addSubview:self.tagView];
    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(193);
        make.top.equalTo(self.infoView.mas_bottom).offset(w2);
        make.leading.mas_equalTo(w3);
        make.trailing.mas_equalTo(-w3);
        make.width.equalTo(self.view).offset(-2*w3);
    }];
    [self.suerButton renderB1];
    [self.deleteButton renderB4];
    self.scrollView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
   
    self.deleteButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:(UIFontWeightSemibold)];
    self.deleteButton.layer.borderWidth = 0.5;
    self.deleteButton.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"].CGColor;
    [self.deleteButton setTitle:AddressL(@"address_list_menu_delete") forState:UIControlStateNormal];
    [self.suerButton setTitle:AddressL(@"address_comfir_button_title") forState:UIControlStateNormal];

}

- (void)configUI {
    [self.infoView setDetail:self.viewModel.addModel.addressDetail];
    [self.infoView setArea:self.viewModel.area];
    [self.infoView setName:self.viewModel.addModel.name];
    [self.infoView setPhone:self.viewModel.addModel.mobile];
    [self.tagView setIsDefault:self.viewModel.addModel.defAddress];
    [self.tagView setTagText:self.viewModel.addModel.tag];
    if (self.viewModel.addModel.tagSource.integerValue == 1) {
        [self.tagView selectInnerTag:self.viewModel.addModel.tag];
    } else {
        [self.tagView selectCustomTag];
    }
//    if ([self.viewModel.addModel.tagSource isEqual:@1]) {
//        [self.tagView setTagId:self.viewModel.addModel.tag];
//    } else if (self.viewModel.addModel.tag.length > 0) {
//        [self.tagView selectCustomTag];
//    }
    
}

- (IBAction)sureButtonClick:(UIButton *)sender {
    self.viewModel.addModel.name = self.infoView.name;
    self.viewModel.addModel.mobile = self.infoView.phone;
    
//    if (![self.viewModel.addModel.addressDetail isEqualToString:self.infoView.detail]) {
//        self.viewModel.needUpdateLocation = YES;
//        self.viewModel.addModel.addressDetail = self.infoView.detail;
//    }
    self.viewModel.addModel.addressDetail = self.infoView.detail;
    
    if (self.tagView.isCustom) {
        self.viewModel.addModel.tagSource = @2;
    } else {
        self.viewModel.addModel.tagSource = @1;
    }
//    self.viewModel.addModel.userDefinedTag = self.tagView.tagText;
    self.viewModel.addModel.defAddress = self.tagView.isDefault;
    
    
    
    if (self.viewModel.addModel.name.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_name_hint")];
        return;
    }
    
    NSMutableCharacterSet *characterSet = [NSMutableCharacterSet alphanumericCharacterSet];
    [characterSet addCharactersInRange:NSMakeRange(19968, (40869-19968))];
    NSRange nameRange = [self.viewModel.addModel.name rangeOfCharacterFromSet:[characterSet invertedSet]];
    BOOL invalidateName = nameRange.length > 0;
    if (self.viewModel.addModel.name.length > 20 || invalidateName) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_name_illegal")];
        return;
    }
    
    
    NSRange phoneRange = [self.viewModel.addModel.mobile rangeOfCharacterFromSet:[[NSCharacterSet characterSetWithCharactersInString:@"0123456789*"] invertedSet]];
    BOOL invalidatePhone = phoneRange.length > 0;
    if (self.viewModel.addModel.mobile.length != 11 || invalidatePhone) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_phone_illegal")];
        [self.infoView markPhoneRed];
        return;
    }
    
    if (self.infoView.area.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_title_select_area")];
        return;
    }
    
    if (self.infoView.detail.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_input_more")];
        return;
    }
    
    if (self.infoView.detail.length < 6 || self.infoView.detail.length > 120) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_detail_specific_illegal")];
        return;
    }
    
//    @weakify(self)
//    if(self.viewModel.model.townId.intValue == -10000){
//        [PlatformService showLoadingInView:self.view];
//        [[[JDISVAddressService shareInstance] translateToDetail:self.viewModel.addr
//                                      info:self.infoView.detail] subscribeNext:^(JDISVAddress*  x) {
//            @strongify(self)
//            [PlatformService dismissInView:self.view];
//            [self userUseTraslateAddr:x];
//            
//        } error:^(NSError * _Nullable error) {
//            @strongify(self)
//            [PlatformService dismissInView:self.view];
//            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_resolve_4th_region_failed")];
//        }];
//        return;
//    }
    [self submitAddress];
}

//-(void)userUseTraslateAddr:(JDISVAddress*)x{
//    @weakify(self);
//    
//    NSString* tip = [NSString stringWithFormat:AddressL(@"address_title_complete_dialog"),x.town.addressName];
//    [KAAlert alert].config
//    .renderW3(tip)
//    .ka_addAction(AddressL(@"address_cancel"), ^(UIButton * _Nonnull button) {
//        //按钮样式有客户端自己确定
//        [button renderB5];
//    }, ^{
//        @strongify(self)
//        NSLog(@"");
//        [self userNotUserTraslateAddr];
//    })
//    .ka_addAction(AddressL(@"address_comfir_button_title"), ^(UIButton * _Nonnull button) {
//        //按钮样式有客户端自己确定
//        [button renderB3];
//    }, ^{
//        @strongify(self)
//        self.viewModel.addr = x;
//        self.viewModel.needUpdateLocation = YES;
//        
//        self.viewModel.model.provinceId = x.province.addressID;
//        self.viewModel.model.provinceName = x.province.addressName;
//        self.viewModel.model.cityId = x.city.addressID;
//        self.viewModel.model.cityName = x.city.addressName;
//        self.viewModel.model.districtId = x.district.addressID;
//        self.viewModel.model.districtName = x.district.addressName;
//        self.viewModel.model.townId = x.town.addressID;
//        self.viewModel.model.townName = x.town.addressName;
//        [self.infoView setArea:self.viewModel.area];
//        [self submitAddress];
//    })
//    .alertShow();
//}


-(void)userNotUserTraslateAddr{
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_complete_dialog_hint")];
    @weakify(self);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        @strongify(self)
        [self openFullCascader:NO];
    });
}
-(void)submitAddress{
    
    [KAToast alert].config
        .renderLoading()
        .jdcd_show();
    @weakify(self)
    [self.viewModel.updateRequestSignal subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        [KAToast closeWithCompletionBlock:nil];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:AddressL(@"address_update_address_success")];
        if (self.addressEditSuccessCallback) {
            self.addressEditSuccessCallback(self.viewModel.addModel);
        }
        [self.navigationController popViewControllerAnimated:YES];
    } error:^(NSError * _Nullable error) {
        [KAToast closeWithCompletionBlock:nil];
            [KAToast alert].config.renderW1(AddressL(@"address_update_address_error")).jdcd_show();
    }];
}

/// MARK: - JDISVAdressInfoViewDelegate

- (void)locationButtonClick:(UIButton *)sender {
    [self.view endEditing:YES];
    @weakify(self)
    __block BOOL isShow = NO;
    JDISVLocationManager *manager = [JDISVLocationManager shareInstance];
    [manager GetLocationPermissionVerifactionWithController:self completion:^(JDISVLocationPermissionStatus permissionStatus) {
        if (permissionStatus == JDISVLocationPermissionStatusAuthorizedAlways || permissionStatus == JDISVLocationPermissionStatusAuthorizedWhenInUse ) {
            @strongify(self)
            if(isShow)
                return;
            isShow = YES;
//            JDISVAddressMapViewController *mapController = [[JDISVAddressMapViewController alloc] init];
//            mapController.addressSelectedCallback = ^(CLLocationCoordinate2D location) {
//                @strongify(self)
//                [self.viewModel reverseGeoCodeWithLocation:location block:^{
//                    @strongify(self)
////                    self.viewModel.needUpdateLocation = NO;
//                    self.viewModel.addModel.latitude = @(location.latitude);
//                    self.viewModel.addModel.longitude = @(location.longitude);
//                    [self.infoView setArea:self.viewModel.area];
//                    [self.infoView setDetail:self.viewModel.addModel.addressDetail];
//                    if (self.viewModel.addModel.addressDetail.length < 6) {
//                        [self.infoView showErrorTip];
//                        [self.infoView mas_updateConstraints:^(MASConstraintMaker *make) {
//                            make.height.mas_equalTo(260);
//                        }];
//                    }
//                }];
//            };
            ADRTencentMapController *mapController = [[ADRTencentMapController alloc] init];
            mapController.addressSelectionCallback = ^(QMSReGeoCodePoi * _Nonnull selectedAddress, QMSReGeoCodeFamousArea * _Nonnull town) {
                self.viewModel.area = [NSString stringWithFormat:@"%@%@%@%@", selectedAddress.ad_info.province, selectedAddress.ad_info.city, selectedAddress.ad_info.district, town.title];
                self.viewModel.detail = selectedAddress.title;
                [self.infoView setArea:self.viewModel.area];
                [self.infoView setDetail:self.viewModel.detail];
            };
            [self.navigationController pushViewController:mapController animated:YES];
        }else {
            [KAAlert alert].config
            .renderW4(AddressL(@"address_map_permssion_alert_title"), AddressL(@"address_map_permssion_alert_content"))
            .ka_addAction(AddressL(@"address_cancel"), ^(UIButton * _Nonnull button) {
                [button renderB5];
            }, ^{})
            .ka_addAction(AddressL(@"address_confirm"), ^(UIButton * _Nonnull button) {
                [button renderB3];
            }, ^{
                NSURL *url = [[NSURL alloc] initWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }).jdcd_show();
        }
    }];
}

- (void)areaButtonClick:(nonnull UIButton *)sender {
    [self.view endEditing:YES];
    
    if(self.fromMySetting){
        [self openFullCascader:YES];
    }else{
        [self openCascader];
    }
}
-(void)openCascader{
    JDISVAddressCascaderViewController *cascaderController  = [[JDISVAddressCascaderViewController alloc] initWithAddress:nil limit:0];
    cascaderController.convert = NO;
    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    delegate.defultHeader.titleLabel.text = AddressL(@"address_label_title");
    cascaderController.transitioningDelegate = delegate;
    @weakify(self)
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel * _Nullable x) {
        @strongify(self)
//        self.viewModel.addr = x;
//        self.viewModel.needUpdateLocation = YES;
        
        self.viewModel.addModel.provinceId = x.provinceId;
        self.viewModel.addModel.provinceName = x.provinceName;
        self.viewModel.addModel.cityId = x.cityId;
        self.viewModel.addModel.cityName = x.cityName;
        self.viewModel.addModel.districtId = x.districtId;
        self.viewModel.addModel.districtName = x.districtName;
        self.viewModel.addModel.townId = x.townId;
        self.viewModel.addModel.townName = x.townName;
        [self.infoView setArea:self.viewModel.area];
    }];
    [self presentViewController:cascaderController animated:YES completion:nil];
}

-(void)openFullCascader:(BOOL)noSelect{
    
       JDISVAddressFullCascaderViewController *cascaderController;
    if(noSelect){
        cascaderController = [[JDISVAddressFullCascaderViewController alloc]
                              initWithAddress:nil
                              noSelect:noSelect];
    }else{
        cascaderController =
        [[JDISVAddressFullCascaderViewController alloc]
         initWithAddress:self.viewModel.addModel
         noSelect:noSelect];
    }
    cascaderController.convert = NO;
    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    delegate.defultHeader.titleLabel.text = AddressL(@"address_complete_dialog_hint");
    cascaderController.transitioningDelegate = delegate;
    @weakify(self)
    [cascaderController.delegate subscribeNext:^(JDTAddressItemModel * _Nullable x) {
        @strongify(self)
//        self.viewModel.addr = x;
//        self.viewModel.needUpdateLocation = YES;
        
        self.viewModel.addModel.provinceId = x.provinceId;
        self.viewModel.addModel.provinceName = x.provinceName;
        self.viewModel.addModel.cityId = x.cityId;
        self.viewModel.addModel.cityName = x.cityName;
        self.viewModel.addModel.districtId = x.districtId;
        self.viewModel.addModel.districtName = x.districtName;
        self.viewModel.addModel.townId = x.townId;
        self.viewModel.addModel.townName = x.townName;
        [self.infoView setArea:self.viewModel.area];
    }];
    [self presentViewController:cascaderController animated:YES completion:nil];
}

/// MARK: - Accessor

- (JDISVAddressTagView *)tagView {
    if(!_tagView){
        _tagView = (JDISVAddressTagView *)[JDISVAddressTagView isv_address_viewFromNibNamed:@"JDISVAddressTagView"];
//        _tagView.layer.cornerRadius = 12;
        _tagView.clipsToBounds = YES;
    }
    return _tagView;
    
}

- (JDISVAdressInfoView *)infoView {
    if(!_infoView){
        _infoView = (JDISVAdressInfoView *)[JDISVAdressInfoView isv_address_viewFromNibNamed:@"JDISVAdressInfoView"];
//        _infoView.layer.cornerRadius = 12;
        _infoView.clipsToBounds = YES;
        _infoView.delegate = self;
        _infoView.canLoc = [self infoViewCanLoc];
    }
    return _infoView;
}

-(BOOL)infoViewCanLoc{
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:@"KaAddressEditPageId"];
    NSNumber* feature = config[@"features"][@"floors"][@"KaAddressFormFloor"][@"gisFeature"];
    return feature.boolValue;
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.contentSize = CGSizeMake([UIScreen mainScreen].bounds.size.width, 465);
    }
    return _scrollView;
}

#pragma mark - IQKeyboard
- (void)setIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = YES;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = NO;
}

- (void)storeIQKeyboardSetting {
    self.keyboardEnable = [IQKeyboardManager sharedManager].enable;
    self.keyboardEnableAutoToolbar = [IQKeyboardManager sharedManager].enableAutoToolbar;
    self.keyboardShouldResignOnTouchOutside = [IQKeyboardManager sharedManager].shouldResignOnTouchOutside;
}

- (void)restoreIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = self.keyboardEnable;
    [IQKeyboardManager sharedManager].enableAutoToolbar = self.keyboardEnableAutoToolbar;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = self.keyboardShouldResignOnTouchOutside;
}

@end
