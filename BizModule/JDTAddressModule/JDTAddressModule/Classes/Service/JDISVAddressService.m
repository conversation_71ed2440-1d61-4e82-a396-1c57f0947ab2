//
//  JDISVAddressService.m
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/24.
//

#import "JDISVAddressService.h"
@import JDTInfrastructureModule;
@import JDTCommonToolModule;

@implementation JDISVAddressService

+ (instancetype)shareInstance {
    static JDISVAddressService *ser = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ser = [JDISVAddressService new];
    });
    return ser;
}

- (RACSignal *)addressList {
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[OOPNetworkManager sharedManager] POST:@"address/c/userAddressList?apiCode=b2c.cbff.address.c.userAddressList" parameters:@{} headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    JDTAddressModel *model = [JDTAddressModel yy_modelWithDictionary:responseObject[@"data"]];
                    [subscriber sendNext:model.addressList];
                } else {
                    NSInteger code = [responseObject[@"code"] integerValue];
                    NSString *message = responseObject[@"msg"];
                    NSMutableDictionary *info = [NSMutableDictionary dictionary];
                    [info tm_safeSetObject:message forKey:NSLocalizedFailureReasonErrorKey];
                    [info tm_safeSetObject:responseObject forKey:@"response"];
                    NSError *buinessError = [NSError errorWithDomain:NSNetServicesErrorDomain code:code userInfo:[info copy]];
                    [subscriber sendError:buinessError];
                }
            }
        }];
        return nil;
    }];
}

//- (RACSignal *)reverseGeoCodeWithLocation:(CLLocationCoordinate2D)location {
//    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        NSMutableDictionary *param = [NSMutableDictionary dictionary];
//        [param setObject:@(location.latitude) forKey:@"latitude"];
//        [param setObject:@(location.longitude) forKey:@"longitude"];
////        NSURLSessionDataTask *task = [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_coor_to_addr" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
////            if (error) {
////                [subscriber sendError:error];
////            } else {
////                JDISVAddressResReverseCode *resp = [JDISVAddressResReverseCode yy_modelWithDictionary:responseObject];
////                if (resp.success) {
////                    [subscriber sendNext:resp.data];
////                } else {
////                    [subscriber sendError:nil];
////                }
////            }
////        }];
////        
////        return [RACDisposable disposableWithBlock:^{
////            [task cancel];
////        }];
//        // TODO:Juice 这里 param 需要传districtId和townId
//        [[OOPNetworkManager sharedManager] POST:@"address/c/queryParentListByAreaId?apiCode=b2c.cbff.address.c.queryParentListByAreaId" parameters:param headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
//            if (error) {
//                [subscriber sendError:error];
//            } else {
//                if ([responseObject[@"code"] isEqualToString:@"0"]) {
//                    [subscriber sendNext:responseObject[@"data"]];
//                } else {
//                    [subscriber sendError:nil];
//                }
//            }
//        }];
//        return nil;
//    }];
//}

- (RACSignal *)cascaderItemsWithParentId:(NSString *)parentId {
//    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        //se_address_regional
//        //se_address_regional_sort_group
//        NSMutableDictionary *param = [NSMutableDictionary dictionary];
//        [param tm_safeSetObject:parentId forKey:@"parentId"];
//        NSURLSessionDataTask *task = [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_regional_sort_group" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            if (error) {
//                [subscriber sendError:error];
//            } else {
//                JDISVAddressResRegion *resp = [JDISVAddressResRegion yy_modelWithDictionary:responseObject];
//                if (resp.success) {
//                    [subscriber sendNext:resp.data];
//                } else {
//                    [subscriber sendError:nil];
//                }
//            }
//        }];
//        
//        return [RACDisposable disposableWithBlock:^{
//            [task cancel];
//        }];
//    }];
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSDictionary *params = @{
            @"parentId": parentId
        };
        [[OOPNetworkManager sharedManager] POST:@"address/c/addressList?apiCode=b2c.cbff.address.c.addressList" parameters:params headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    if (responseObject[@"data"] != nil && [responseObject[@"data"] isKindOfClass:[NSDictionary class]]) {
                        NSArray *regionalArr = [NSArray yy_modelArrayWithClass:[JDTRegionalDetailModel class] json:responseObject[@"data"][@"regional"]];
                        [subscriber sendNext:regionalArr];
                    } else {
                        [subscriber sendNext:@[]];
                    }
                } else {
                    [subscriber sendError:nil];
                }
            }
        }];
        
        return nil;
    }];
}

- (RACSignal *)addressAddWithModel:(JDTAddressItemModel *)model {
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableDictionary *param = [[model yy_modelToJSONObject] mutableCopy];
        if (model.defAddress) {
            [param setObject:@"true" forKey:@"defAddress"];
        } else {
            [param setObject:@"false" forKey:@"defAddress"];
        }
//        [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_add" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            if (error) {
//                [subscriber sendError:error];
//            } else {
//                JDISVAddressRes *resp = [JDISVAddressRes yy_modelWithDictionary:responseObject];
//                if (resp.success) {
//                    [subscriber sendNext:responseObject[@"data"]];
//                } else {
//                    [subscriber sendError:nil];
//                }
//            }
//        }];
        [[OOPNetworkManager sharedManager] POST:@"address/c/addUserAddress?apiCode=b2c.cbff.address.c.addUserAddress" parameters:param headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                if ([responseObject[@"data"] isKindOfClass:[NSNumber class]]) {
                    [subscriber sendNext:responseObject[@"data"]];
                } else {
                    [subscriber sendError:nil];
                }
            }
        }];
        
        return nil;
    }];
}

- (RACSignal *)storeCascderListWithVenderId:(NSNumber *)venderId {
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        [param tm_safeSetObject:venderId forKey:@"venderId"];
        NSURLSessionDataTask *task = [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_store_regional" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                JDISVAddressResStoreRegion *resp = [JDISVAddressResStoreRegion yy_modelWithDictionary:responseObject];
                if (resp.success) {
                    [subscriber sendNext:resp.data];
                } else {
                    [subscriber sendError:nil];
                }
            }
        }];
        
        return [RACDisposable disposableWithBlock:^{
            [task cancel];
        }];
    }];
}

//- (RACSignal *)locationWithFullAddress:(NSString *)fullAddress {
//    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        NSMutableDictionary *param = [NSMutableDictionary dictionary];
//        [param setObject:fullAddress forKey:@"fullAddress"];
//        NSURLSessionDataTask *task = [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_addr_to_coor" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            if (error) {
//                [subscriber sendError:error];
//            } else {
//                JDISVAddressRes *resp = [JDISVAddressRes yy_modelWithDictionary:responseObject];
//                if (resp.success) {
//                    NSDictionary *location = [responseObject objectForKey:@"data"];
//                    [subscriber sendNext:location];
//                } else {
//                    NSError *err = [NSError errorWithDomain:kJDISVAddressService code:-1 userInfo:@{NSLocalizedFailureReasonErrorKey:AddressL(@"address_request_error")}];
//                    [subscriber sendError:err];
//                }
//            }
//        }];
//        
//        return [RACDisposable disposableWithBlock:^{
//            [task cancel];
//        }];
//    }];
//}

- (RACSignal*)processPasteStr:(NSString*)str{
    NSDictionary* param = @{
        @"parsingType":@(1),
        @"text":str?:@""
    };
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_parse_customer_info" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if(error){
                [subscriber sendError:error];
            }else{
                NSNumber* code = responseObject[@"code"];
                if(code.intValue == 200){
                   
                    [subscriber sendNext:responseObject[@"data"]];
                    [subscriber sendCompleted];
                }else{
                    [subscriber sendError:nil];
                }
            }
        }];
        return nil;
    }];
}

//-(RACSignal*)translateToDetail:(JDISVAddress*)addr
//                          info:(NSString*)detailInfo{
//    NSDictionary* param = @{@"parsingType":@(2),
//                            @"detailAddress":detailInfo?:@"",
//                            @"provinceName":addr.province.addressName?:@"",
//                            @"cityName":addr.city.addressName?:@"",
//                            @"districtName":addr.district.addressName?:@""};
//    
//    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_parse_district" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            if(error){
//                [subscriber sendError:error];
//            }else{
//                NSNumber* code = responseObject[@"code"];
//                if(code.intValue == 200){
//                    NSDictionary* data = responseObject[@"data"];
//                    JDISVAddress* addr = [JDISVAddress  defaultAddress];
//                    addr.province.addressID = data[@"provinceId"];
//                    addr.province.addressName = data[@"provinceName"];
//                    addr.city.addressID = data[@"cityId"];
//                    addr.city.addressName = data[@"cityName"];
//                    addr.district.addressID = data[@"districtId"];
//                    addr.district.addressName = data[@"districtName"];
//                    addr.town.addressID = data[@"townId"];
//                    addr.town.addressName = data[@"townName"];
//                    [subscriber sendNext:addr];
//                    [subscriber sendCompleted];
//                }else{
//                    [subscriber sendError:nil];
//                }
//            }
//        }];
//        return nil;
//    }];
//}
@end
