//
//  JDISVAddressService.h
//  JDISVAddressModule
//
//  Created by gongyang2 on 2021/11/24.
//

#import <Foundation/Foundation.h>

#import "JDISVAddressModulUtil.h"
#import "JDISVAddressRes.h"

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@interface JDISVAddressService : NSObject

+ (instancetype)shareInstance;

// 地址列表
- (RACSignal *)addressList;

// 坐标转详细地址
//- (RACSignal *)reverseGeoCodeWithLocation:(CLLocationCoordinate2D)location;

// 获取级联地址子列表
- (RACSignal *)cascaderItemsWithParentId:(NSString *)parentId;

// 新增地址
- (RACSignal *)addressAddWithModel:(JDTAddressItemModel *)model;

// 门店级联地址
- (RACSignal *)storeCascderListWithVenderId:(NSNumber *)venderId;

// 获取经纬度
//- (RACSignal *)locationWithFullAddress:(NSString *)fullAddress;


//-(RACSignal*)translateToDetail:(JDISVAddress*)addr
//                          info:(NSString*)detailInfo;

- (RACSignal*)processPasteStr:(NSString*)str;

@end

NS_ASSUME_NONNULL_END
