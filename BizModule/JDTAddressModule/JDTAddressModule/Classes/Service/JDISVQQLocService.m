//
//  JDISVQQLocService.m
//  JDISVAddressModule
//
//  Created by cdwutao3 on 2022/12/16.
//

#import "JDISVQQLocService.h"
#import "JDISVAddressModulUtil.h"
//#import <QMapKit/QMapKit-umbrella.h>
#import <JDISVPermissionsModule/JDISVPermissionsModule-umbrella.h>
@interface JDISVQQLocService()
//@property (nonatomic, strong) QMapView *mapView;
//@property (nonatomic, strong) QMSSearcher *searcher;
@property (nonatomic, assign) CLLocationCoordinate2D coor;
@property (nonatomic, strong)  JDRouterCompletion callBack;
@end
@implementation JDISVQQLocService
+(instancetype)inst{
    static JDISVQQLocService * _s;
    
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _s = [JDISVQQLocService new];
    });
    return _s;
}
-(instancetype)init{
    self = [super init];
    if(self){
//        self.searcher = [Q<PERSON><PERSON><PERSON><PERSON> new];
//        self.searcher.delegate = self;
    }
    return self;
}


-(void)getLoc:(UIViewController*)vc
     callBack:(JDRouterCompletion)callBack{
    if(callBack == nil){
        return;
    }
//    [JDCDPermission authorizeWithType:JDCDPermissionType_Location
//                           completion:^(BOOL granted, BOOL firstTime) {
//        if(!granted){
//            NSError* err = [NSError errorWithDomain:@"Location" code:-1 userInfo:@{NSLocalizedDescriptionKey:AddressL(@"address_select_city_no_location_title")}];
//            callBack(err);
//        }else{
//            self.callBack = callBack;
//            if(self.mapView){
//                [self.mapView removeFromSuperview];
//            }
//            self.mapView = [[QMapView alloc] initWithFrame:CGRectMake(0, 0, 1, 1)];
//            [self.mapView setLogoMargin:(CGPointMake(6, 3+18)) anchor:(QMapLogoAnchorLeftBottom)];
//            self.mapView.showsScale = NO;
//            self.mapView.userTrackingMode = QUserTrackingModeFollow;
//            
//            [vc.view insertSubview:self.mapView atIndex:0];
//            self.mapView.delegate = self;
//            [self.mapView setShowsUserLocation:YES];
//        }
//    }];
   
}
//
//- (void)mapViewDidFailLoadingMap:(QMapView *)mapView withError:(NSError *)error{
//    self.callBack(error);
//}
//
//- (void)mapViewInitComplete:(QMapView *)mapView {
//    self.coor = mapView.centerCoordinate;
//    [self refreshAddressWithLocation:mapView.centerCoordinate];
//}

//
//- (void)refreshAddressWithLocation:(CLLocationCoordinate2D)location {
//    QMSReverseGeoCodeSearchOption *option = [QMSReverseGeoCodeSearchOption new];
//    [option setLocationWithCenterCoordinate:location];
//    option.poi_options = @"address_format=short;radius=5000;page_size=20;page_index=1;policy=5";
//    option.get_poi = YES;
//    
//    [self.searcher searchWithReverseGeoCodeSearchOption:option];
//}
//
//
//- (void)searchWithSearchOption:(QMSSearchOption *)searchOption didFailWithError:(NSError *)error {
//    self.callBack(error);
//    self.callBack = nil;
//}
//
//
//- (void)searchWithReverseGeoCodeSearchOption:(QMSReverseGeoCodeSearchOption *)reverseGeoCodeSearchOption didReceiveResult:(QMSReverseGeoCodeSearchResult *)reverseGeoCodeSearchResult {
//    QMSAddressComponent *component = reverseGeoCodeSearchResult.address_component;
//
//    NSDictionary* dic = @{@"nation":component.nation?:@"",
//                          @"province":component.province?:@"",
//                          @"city":component.city?:@"",
//                          @"district":component.district?:@"",
//                          @"street":component.street?:@"",
//                          @"street_number":component.street_number?:@"",
//                          @"coor":[NSValue valueWithCoordinate:self.coor]
//    };
//    self.callBack(dic);
//}
@end
