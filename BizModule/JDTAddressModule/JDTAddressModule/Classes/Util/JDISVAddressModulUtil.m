//
//  JDISVAddressModulUtil.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/13.
//

#import "JDISVAddressModulUtil.h"

#import <JDISVCategoryModule/NSBundle+JDCDExtend.h>

@implementation JDISVAddressModulUtil

+ (CGFloat)screenWidth {
    return [UIScreen mainScreen].bounds.size.width;
}

+ (CGFloat)screenHeight {
    return [UIScreen mainScreen].bounds.size.height;
}

@end


@implementation NSBundle (JDISVAddress)

+(NSBundle *)isv_address_bundle {
     return [NSBundle jdcd_moduleWithBundleName:@"JDTAddressModule" class: NSClassFromString(@"JDISVAddressModulUtil")];;
}

@end


@implementation UIView (JDISVAddress)

+(UIView *)isv_address_viewFromNibNamed:(NSString *)name {
    NSArray *views = [[NSBundle isv_address_bundle] loadNibNamed:name owner:self options:nil];
    return views.lastObject;
}

@end

@implementation UIImage (JDISVAddress)

+(nullable UIImage *)isv_address_imageNamed:(NSString *)name {
    NSBundle *bundle = [NSBundle isv_address_bundle];
    UIImage *image = [UIImage imageNamed:name inBundle:bundle compatibleWithTraitCollection:nil];
    return image;//[UIImage imageNamed:name inBundle:SLCRMUserCenterBundle() compatibleWithTraitCollection:nil];
}

@end


@implementation KSAAddressShowInfo
+(NSString*)getShowString:(NSString*)provinceName
                 cityName:(NSString*)cityName
             districtName:(NSString*)districtName
                 townName:(NSString*)townName{
    NSMutableString* tmpRegion = [[NSMutableString alloc] init];
    NSString* lang = [NSString getKAUseLang];
    if([lang isEqualToString:@"en"]){
        if([townName isKindOfClass:NSString.class] && townName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@"%@, ",townName]];
        }
        if([districtName isKindOfClass:NSString.class] && districtName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@"%@, ",districtName]];
        }
        if([cityName isKindOfClass:NSString.class] && cityName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@"%@, ",cityName]];
        }
        if([provinceName isKindOfClass:NSString.class] && provinceName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@"%@",provinceName]];
        }
    }else{
        if([provinceName isKindOfClass:NSString.class] && provinceName.length){
            [tmpRegion appendString:provinceName];
        }
        if([cityName isKindOfClass:NSString.class] && cityName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@", %@",cityName]];
        }
        if([districtName isKindOfClass:NSString.class] && districtName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@", %@",districtName]];
        }
        
        if([townName isKindOfClass:NSString.class] && townName.length){
            [tmpRegion appendString:[NSString stringWithFormat:@", %@",townName]];
        }
    }
    
    return [tmpRegion copy];
}
@end
