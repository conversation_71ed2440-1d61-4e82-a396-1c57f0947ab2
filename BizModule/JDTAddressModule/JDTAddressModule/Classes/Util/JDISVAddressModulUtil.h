//
//  JDISVAddressModulUtil.h
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/13.
//

#import <Foundation/Foundation.h>

#import <JDISVKAUIKitModule/KAFloatLayerPresentationController.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVTMUtilsModule/JDISVTMUtilsModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVPagerViewModule/JDISVPagerViewModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVDZNEmptyDataSetModule/UIScrollView+EmptyDataSet.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVNetworkModule/JDISVNetworkModule-umbrella.h>
#import <JDISVAFNetworkingModule/JDISVAFNetworkingModule-umbrella.h>

#define kJDISVAddressService @"JDISVAddressService"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVAddressModulUtil : NSObject

+ (CGFloat)screenWidth;
+ (CGFloat)screenHeight;

@end

@interface NSBundle (JDISVAddress)

+(NSBundle *)isv_address_bundle;

@end


@interface UIView (JDISVAddress)

+(UIView *)isv_address_viewFromNibNamed:(NSString *)name;

@end

@interface UIImage (JDISVAddress)

+(nullable UIImage *)isv_address_imageNamed:(NSString *)name;

@end


@interface KSAAddressShowInfo:NSObject
+(NSString*)getShowString:(NSString*)provinceName
                 cityName:(NSString*)cityName
             districtName:(NSString*)districtName
                 townName:(NSString*)townName;
@end
NS_ASSUME_NONNULL_END
