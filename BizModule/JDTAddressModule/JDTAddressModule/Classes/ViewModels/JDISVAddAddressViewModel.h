//
//  JDISVAddAddressViewModel.h
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/28.
//

#import <Foundation/Foundation.h>

#import "JDISVAddressModulUtil.h"
#import "JDISVAddressRes.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@interface JDISVAddAddressViewModel : NSObject

@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *phone;
@property (nonatomic, copy) NSString *detail;
@property (nonatomic, copy) NSString *area;
@property (nonatomic, copy) NSString *tag;
@property (nonatomic, assign) BOOL isDefault;

@property (nonatomic, strong) NSNumber *tagSource;  /**< tag来源，1为内置tag，2为自定义tag（待确认） */
@property (nonatomic, strong) NSNumber *tagRet; /**< 内置tag id，1、家 2、学校 3、公司 */

@property (nonatomic, strong) JDISVAddressResReverseCodeItem *reverseCodeItem;

@property (nonatomic, strong) JDTAddressItemModel *address;
//@property (nonatomic, assign) BOOL needUpdateLocation;

@property (nonatomic) CLLocationCoordinate2D location;

- (void)reverseGeoCodeWithLocation:(CLLocationCoordinate2D)location block:(void(^)(void))block;

- (void)addressAddWithBlock:(void(^)(NSNumber *addrId, NSString *msg))block;

- (void)processReverseCodeItem:(JDISVAddressResReverseCodeItem *)item;

- (RACSignal*)processPasteStr:(NSString*)str;

@end
