//
//  JSISVAddressListItemViewModel.h
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/13.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JSISVAddressListItemViewModel : NSObject
/// 是否是默认地址
@property (nonatomic, assign,readonly) BOOL isDefault;
/// 地区
@property (nonatomic, copy,readonly) NSString *region;
/// 详细地址
@property (nonatomic, copy,readonly) NSString *detailAddress;
/// 用户信息
@property (nonatomic, copy,readonly) NSString *userInfo;
@property (nonatomic, copy,readonly) NSString *name;
@property (nonatomic, copy,readonly) NSString *firstName;
@property (nonatomic, copy,readonly) NSString *lastName;
@property (nonatomic, copy,readonly) NSString *mobile;
@property (nonatomic, copy,readonly) NSString *areaCode;

/// 是否隐藏分割线
@property (nonatomic, assign,readonly) BOOL isHiddenLine;

@property (nonatomic, strong, readonly) JDTAddressItemModel *addModel;

@property (nonatomic, copy,readonly) NSString *tag;

- (instancetype)initWithItemModel:(JDTAddressItemModel *)model;

-(void)setIsDefault:(BOOL)isDefault;

-(void)setIsHiddenLine:(BOOL)isHiddenLine;
@end




NS_ASSUME_NONNULL_END
