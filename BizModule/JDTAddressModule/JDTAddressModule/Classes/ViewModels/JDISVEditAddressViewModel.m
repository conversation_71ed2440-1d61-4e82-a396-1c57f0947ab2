//
//  JDISVEditAddressViewModel.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/29.
//

#import "JDISVEditAddressViewModel.h"
#import "JDISVAddressService.h"
#import "JDISVAddressModulUtil.h"

#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVYYModelModule/YYModel.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

@import JDTInfrastructureModule;

@interface JDISVEditAddressViewModel ()

@end

@implementation JDISVEditAddressViewModel


- (instancetype)initWithAddressModel:(JDTAddressItemModel *)model {
    self = [super init];
    if (self) {
        self.addModel = model;
    }
    return self;
}


/// MARK: Public

//- (RACSignal *)deleteRequestSignal {
//    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
//    [parameters tm_safeSetObject:@(self.addModel.addressId) forKey:@"addressId"];
//    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        // TODO:Juice 删除地址
//        [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_delete" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            if (error) {
//                [subscriber sendError:error];
//            }else {
//                BOOL success = [responseObject[@"success"] boolValue];
//                if (success) {
//                    [subscriber sendNext:nil];
//                }else {
//                    NSInteger code = [responseObject[@"code"] integerValue];
//                    NSString *message = responseObject[@"message"];
//                    NSError *buinessError = [NSError errorWithDomain:NSNetServicesErrorDomain code:code userInfo:@{
//                        NSLocalizedFailureReasonErrorKey:message?:AddressL(@"address_common_network_error")
//                    }];
//                    [subscriber sendError:buinessError];
//                }
//            }
//        }];
//        return nil;
//    }];
//}

- (RACSignal *)updateRequestSignal {
    @weakify(self)
//    RACSignal *updateLocation = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//        @strongify(self)
//        NSMutableArray *temp = [NSMutableArray array];
//        [temp tm_safeAddObject:self.area];
//        [temp tm_safeAddObject:self.addModel.addressDetail];
//        NSString *fullname = [temp componentsJoinedByString:@""];
//        [[[JDISVAddressService shareInstance] locationWithFullAddress:fullname] subscribeNext:^(NSDictionary *loction) {
//            [subscriber sendNext:loction];
//        } error:^(NSError * _Nullable error) {
//            [subscriber sendError:error];
//        }];
//        return nil;
//    }];
    
    RACSignal *updateAddress = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
        parameters = [self.addModel yy_modelToJSONObject];
        parameters[@"defAddress"] = self.addModel.defAddress ? @(YES) : @(NO);
//        [parameters tm_safeSetObject:@(self.addModel.addressId) forKey:@"addressId"];
//        [parameters tm_safeSetObject:self.addModel.provinceId forKey:@"provinceId"];
//        [parameters tm_safeSetObject:self.addModel.cityId forKey:@"cityId"];
//        [parameters tm_safeSetObject:self.addModel.districtId forKey:@"districtId"];
//        [parameters tm_safeSetObject:self.addModel.townId forKey:@"townId"];
//        [parameters tm_safeSetObject:self.addModel.name forKey:@"name"];
//        [parameters tm_safeSetObject:self.addModel.mobile forKey:@"mobile"];
//        if (self.addModel.defAddress) {
//            [parameters setValue:@"true" forKey:@"defAddress"];
//        } else {
//            [parameters setValue:@"false" forKey:@"defAddress"];
//        }
//        [parameters tm_safeSetObject:self.addModel.addressDetail forKey:@"addressDetail"];
//        [parameters tm_safeSetObject:self.addModel.tagSource forKey:@"tagSource"];
//        [parameters tm_safeSetObject:self.addModel.tag forKey:@"tagRet"];
//        [parameters setValue:self.addModel.tag forKey:@"userDefinedTag"];
//        [parameters tm_safeSetObject:self.addModel.latitude forKey:@"latitude"];
//        [parameters tm_safeSetObject:self.addModel.longitude forKey:@"longitude"];
//        [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeOverseas) path:@"" function:@"se_address_update" version:@"" parameters:[parameters copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            if (error) {
//                [subscriber sendError:error];
//            }else {
//                BOOL success = [responseObject[@"success"] boolValue];
//                if (success) {
//                    [subscriber sendNext:nil];
//                }else {
//                    NSInteger code = [responseObject[@"code"] integerValue];
//                    NSString *message = responseObject[@"message"];
//                    NSError *buinessError = [NSError errorWithDomain:NSNetServicesErrorDomain code:code userInfo:@{
//                        NSLocalizedFailureReasonErrorKey:message?:AddressL(@"address_common_network_error")
//                    }];
//                    [subscriber sendError:buinessError];
//                }
//            }
//        }];
        [[OOPNetworkManager sharedManager] POST:@"address/c/updateUserAddress?apiCode=b2c.cbff.address.c.updateUserAddress" parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                BOOL success = [responseObject[@"success"] boolValue];
                if (success) {
                    [subscriber sendNext:nil];
                } else {
                    NSInteger code = [responseObject[@"code"] integerValue];
                    NSString *message = responseObject[@"msg"];
                    NSMutableDictionary *info = [NSMutableDictionary dictionary];
                    [info tm_safeSetObject:message forKey:NSLocalizedFailureReasonErrorKey];
                    [info tm_safeSetObject:responseObject forKey:@"response"];
                    NSError *buinessError = [NSError errorWithDomain:NSNetServicesErrorDomain code:code userInfo:[info copy]];
                    [subscriber sendError:buinessError];
                }
            }
        }];
        
        return nil;
    }];
    
//    if (self.needUpdateLocation) {
//        return [updateLocation bind:^RACSignalBindBlock {
//            return ^RACSignal *(NSDictionary *loction, BOOL *stop) {
//                @strongify(self)
//                NSNumber *lat = [loction objectForKey:@"latitude"];
//                NSNumber *lng = [loction objectForKey:@"longitude"];
//                self.addModel.latitude = lat;
//                self.addModel.longitude = lng;
//                return updateAddress;
//            };
//        }];
//    } else {
        return updateAddress;
//    }
}


//- (NSString *)area {
//    if (self.addModel.townId.intValue != -10000) {
//        return [NSString stringWithFormat:@"%@%@%@%@",self.addModel.provinceName ?: @"",self.addModel.cityName ?: @"",self.addModel.districtName ?: @"",self.addModel.townName ?: @""];
//    } else {
//        return [NSString stringWithFormat:@"%@%@%@",self.addModel.provinceName ?: @"",self.addModel.cityName ?: @"",self.addModel.districtName ?: @""];
//    }
//}

//- (void)reverseGeoCodeWithLocation:(CLLocationCoordinate2D)location block:(void (^)(void))block {
//    if (CLLocationCoordinate2DIsValid(location)) {
//        @weakify(self)
//        [[[JDISVAddressService shareInstance] reverseGeoCodeWithLocation:location] subscribeNext:^(JDISVAddressResReverseCodeItem * _Nullable x) {
//            @strongify(self)
//            [self processReverseCodeItem:x];
//            if (block) {
//                block();
//            }
//        } error:^(NSError * _Nullable error) {
//            if (block) {
//                block();
//            }
//        }];
//    }
//}

//- (void)processReverseCodeItem:(JDISVAddressResReverseCodeItem *)item {
//    self.reverseCodeItem = item;
//    self.addModel.provinceId = item.provinceId.stringValue;
//    self.addModel.provinceName = item.provinceName;
//    self.addModel.cityId = item.cityId.stringValue;
//    self.addModel.cityName = item.cityName;
//    self.addModel.districtId = item.districtId.stringValue;
//    self.addModel.districtName = item.districtName;
//    self.addModel.townId = item.townId.stringValue;
//    self.addModel.townName = item.townName;
//    self.addModel.addressDetail = item.addressDetail;
//}

@end
