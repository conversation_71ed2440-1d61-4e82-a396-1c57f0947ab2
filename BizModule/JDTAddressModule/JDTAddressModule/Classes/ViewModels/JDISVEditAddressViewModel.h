//
//  JDISVEditAddressViewModel.h
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/29.
//

#import <Foundation/Foundation.h>
#import "JDISVAddressModulUtil.h"
#import "JDISVAddressRes.h"

@class RACSignal;

NS_ASSUME_NONNULL_BEGIN

@interface JDISVEditAddressViewModel : NSObject

@property (nonatomic, strong) JDTAddressItemModel *addModel;

@property (nonatomic, strong) RACSignal *updateRequestSignal;

//@property (nonatomic, strong) RACSignal *deleteRequestSignal;

- (instancetype)initWithAddressModel:(JDTAddressItemModel *)model;

//- (void)reverseGeoCodeWithLocation:(CLLocationCoordinate2D)location block:(void (^)(void))block;

@end

NS_ASSUME_NONNULL_END
