//
//  JSISVAddressListItemViewModel.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/13.
//

#import "JSISVAddressListItemViewModel.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDLang.h>

@interface JSISVAddressListItemViewModel()

/// 是否是默认地址
@property (nonatomic, assign) BOOL isDefault;
/// 地区
@property (nonatomic, copy) NSString *region;
/// 详细地址
@property (nonatomic, copy) NSString *detailAddress;
/// 用户信息
@property (nonatomic, copy) NSString *userInfo;

@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *firstName;
@property (nonatomic, copy) NSString *lastName;
@property (nonatomic, copy) NSString *mobile;
@property (nonatomic, copy) NSString *areaCode;

/// 是否隐藏分割线
@property (nonatomic, assign,readwrite) BOOL HiddenLine;

@property (nonatomic, copy) NSString *tag;

@property (nonatomic, strong) JDTAddressItemModel *addModel;

@end

@implementation JSISVAddressListItemViewModel

- (instancetype)initWithItemModel:(JDTAddressItemModel *)model {
    if (self = [super init]) {
        _addModel = model;
        _detailAddress = model.addressDetail;
        NSString* lang = [NSString getKAUseLang];
        NSMutableString* tmpRegion = [[NSMutableString alloc] init];
        if([lang isEqualToString:@"en"]){
            if([model.townName isKindOfClass:NSString.class] && model.townName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@"%@, ",model.townName]];
            }
            if([model.districtName isKindOfClass:NSString.class] && model.districtName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@"%@, ",model.districtName]];
            }
            if([model.cityName isKindOfClass:NSString.class] && model.cityName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@"%@, ",model.cityName]];
            }
            if([model.provinceName isKindOfClass:NSString.class] && model.provinceName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@"%@",model.provinceName]];
            }
        }else{
            if([model.provinceName isKindOfClass:NSString.class] && model.provinceName.length){
                [tmpRegion appendString:model.provinceName];
            }
            if([model.cityName isKindOfClass:NSString.class] && model.cityName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@", %@",model.cityName]];
            }
            if([model.districtName isKindOfClass:NSString.class] && model.districtName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@", %@",model.districtName]];
            }
            if([model.townName isKindOfClass:NSString.class] && model.townName.length){
                [tmpRegion appendString:[NSString stringWithFormat:@", %@",model.townName]];
            }
        }
        
        _region = [tmpRegion copy];
        _userInfo = [NSString stringWithFormat:@"%@    %@",model.name,model.mobile];
        _name = model.name;
        _mobile = model.mobile;
        _areaCode = model.areaCode;
        
//        tag来源，1为内置tag，2为自定义tag
        if ([model.tagSource isEqual:@1]) {
            _tag = model.tag;
//            switch (model.tagRet.integerValue) {
//                case 1:{
//                    _tag = AddressL(@"address_home_tag");
//                }
//                    break;
//                case 2:{
//                    _tag = AddressL(@"address_school_tag");
//                }
//                    break;
//                case 3:{
//                    _tag = AddressL(@"address_company_tag");
//                }
//                    break;
//                    
//                default:
//                    break;
//            }
        }else{
//            _tag = model.userDefinedTag;
            _tag = model.tag;
        }
    }
    return self;
}

- (void)setIsDefault:(BOOL)isDefault {
//    _model.defAddress = isDefault;
    _addModel.defAddress = isDefault;
}

- (BOOL)isDefault{
//    return _model.defAddress;
    return _addModel.defAddress;
}

-(void)setIsHiddenLine:(BOOL)isHiddenLine{
    _HiddenLine = isHiddenLine;
}

- (BOOL)isHiddenLine{
    return _HiddenLine;
}
@end
