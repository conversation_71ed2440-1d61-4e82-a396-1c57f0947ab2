//
//  JDISVAddressViewModel.m
//  JDISVAddressModule
//
//  Created by 秦红江 on 2021/10/28.
//

#import "JDISVAddAddressViewModel.h"

#import "JDISVAddressService.h"

@interface JDISVAddAddressViewModel ()

@end

@implementation JDISVAddAddressViewModel

- (void)reverseGeoCodeWithLocation:(CLLocationCoordinate2D)location block:(void (^)(void))block {
    if (CLLocationCoordinate2DIsValid(location)) {
        @weakify(self)
        [[[JDISVAddressService shareInstance] reverseGeoCodeWithLocation:location] subscribeNext:^(JDISVAddressResReverseCodeItem * _Nullable x) {
            @strongify(self)
            [self processReverseCodeItem:x];
            if (block) {
                block();
            }
        } error:^(NSError * _Nullable error) {
            if (block) {
                block();
            }
        }];
    }
}

- (RACSignal*)processPasteStr:(NSString*)str{
    return [[JDISVAddressService shareInstance] processPasteStr:str];
}

- (void)processReverseCodeItem:(JDISVAddressResReverseCodeItem *)item {
    self.reverseCodeItem = item;
    self.area = [NSString stringWithFormat:@"%@%@%@%@", item.provinceName ?: @"", item.cityName ?: @"", item.districtName ?: @"", item.townName ?: @""];
    self.detail = item.addressDetail;
}

- (void)addressAddWithBlock:(void (^)(NSNumber *, NSString *))block {
    JDISVAddressModel *model = [JDISVAddressModel new];
    model.name = self.name;
    model.mobile = self.phone;
    if (self.reverseCodeItem) {
        model.provinceId = self.reverseCodeItem.provinceId;
        model.cityId = self.reverseCodeItem.cityId;
        model.districtId = self.reverseCodeItem.districtId;
        model.townId = self.reverseCodeItem.townId;
    } else {
//        model.provinceId = self.addr.province.addressID;
//        model.cityId = self.addr.city.addressID;
//        model.districtId = self.addr.district.addressID;
//        model.townId = self.addr.town.addressID;
        
        model.provinceId = self.address.provinceId.numberValue;
        model.cityId = self.address.cityId.numberValue;
        model.districtId = self.address.districtId.numberValue;
        model.townId = self.address.townId.numberValue;
    }
    model.addressDetail = self.detail;
    model.tagSource = self.tagSource;
    model.tagRet = self.tagRet;
    model.userDefinedTag = self.tag;
    model.defAddress = self.isDefault;
    
    if (self.needUpdateLocation) {
        NSMutableArray *temp = [NSMutableArray array];
        if (self.reverseCodeItem) {
            [temp tm_safeAddObject:self.reverseCodeItem.provinceName];
            [temp tm_safeAddObject:self.reverseCodeItem.cityName];
            [temp tm_safeAddObject:self.reverseCodeItem.districtName];
            [temp tm_safeAddObject:self.reverseCodeItem.townName];
        } else {
//            [temp tm_safeAddObject:self.addr.province.addressName];
//            [temp tm_safeAddObject:self.addr.city.addressName];
//            [temp tm_safeAddObject:self.addr.district.addressName];
//            [temp tm_safeAddObject:self.addr.town.addressName];
            [temp tm_safeAddObject:self.address.provinceName];
            [temp tm_safeAddObject:self.address.cityName];
            [temp tm_safeAddObject:self.address.districtName];
            [temp tm_safeAddObject:self.address.townName];
        }
        [temp tm_safeAddObject:self.detail];
        NSString *fullname = [temp componentsJoinedByString:@""];
        [[[JDISVAddressService shareInstance] locationWithFullAddress:fullname] subscribeNext:^(NSDictionary *loction) {
            NSNumber *lat = [loction objectForKey:@"latitude"];
            NSNumber *lng = [loction objectForKey:@"longitude"];
            model.latitude = lat;
            model.longitude = lng;
            [[[JDISVAddressService shareInstance] addressAddWithModel:model] subscribeNext:^(id  _Nullable x) {
                if (block) {
                    block(x, nil);
                }
            } error:^(NSError * _Nullable error) {
                if (block) {
                    block(nil, nil);
                }
            }];
        } error:^(NSError * _Nullable error) {
            model.latitude = @(39.908823);
            model.longitude = @(116.39747);
            [[[JDISVAddressService shareInstance] addressAddWithModel:model] subscribeNext:^(id  _Nullable x) {
                if (block) {
                    block(x, nil);
                }
            } error:^(NSError * _Nullable error) {
                if (block) {
                    block(nil, nil);
                }
            }];
        }];
    } else {
        model.latitude = @(self.location.latitude);
        model.longitude = @(self.location.longitude);
        [[[JDISVAddressService shareInstance] addressAddWithModel:model] subscribeNext:^(id  _Nullable x) {
            if (block) {
                block(x, nil);
            }
        } error:^(NSError * _Nullable error) {
            if (block) {
                block(nil, nil);
            }
        }];
    }
}

@end
