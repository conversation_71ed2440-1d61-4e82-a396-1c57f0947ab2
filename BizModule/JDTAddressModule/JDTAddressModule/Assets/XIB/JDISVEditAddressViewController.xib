<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="JDISVEditAddressViewController">
            <connections>
                <outlet property="deleteButton" destination="cCC-wD-DjO" id="OFI-2C-F2u"/>
                <outlet property="suerButton" destination="5rW-TI-ZkK" id="ZMP-D8-aCO"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5rW-TI-ZkK">
                    <rect key="frame" x="18" y="783" width="378" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="NQQ-zc-q14"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <state key="normal" title="确定">
                        <color key="titleColor" systemColor="labelColor"/>
                    </state>
                    <connections>
                        <action selector="sureButtonClick:" destination="-1" eventType="touchUpInside" id="n1J-v7-v2k"/>
                    </connections>
                </button>
                <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cCC-wD-DjO">
                    <rect key="frame" x="18" y="783" width="378" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="IsE-ot-iiH"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <state key="normal" title="删除">
                        <color key="titleColor" systemColor="labelColor"/>
                    </state>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="5rW-TI-ZkK" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" constant="18" id="BCU-vv-zCs"/>
                <constraint firstItem="5rW-TI-ZkK" firstAttribute="bottom" secondItem="Q5M-cg-NOt" secondAttribute="bottom" constant="-5" id="O0Y-uI-Nby"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="bottom" secondItem="cCC-wD-DjO" secondAttribute="bottom" constant="5" id="QDe-O7-Fsq"/>
                <constraint firstItem="cCC-wD-DjO" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" constant="18" id="Une-gB-Cy3"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="5rW-TI-ZkK" secondAttribute="trailing" constant="18" id="ncf-1w-5ZI"/>
                <constraint firstItem="5rW-TI-ZkK" firstAttribute="width" secondItem="cCC-wD-DjO" secondAttribute="width" id="wXl-6J-k8p"/>
            </constraints>
            <simulatedNavigationBarMetrics key="simulatedTopBarMetrics" prompted="NO"/>
            <point key="canvasLocation" x="24.637681159420293" y="108.48214285714285"/>
        </view>
    </objects>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
