<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="140" id="KGk-i7-Jjw" customClass="JDISVAddrListFloor">
            <rect key="frame" x="0.0" y="0.0" width="320" height="140"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="140"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yiK-Aj-ctU">
                        <rect key="frame" x="264" y="42" width="56" height="56"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="56" id="AFr-c6-LOe"/>
                            <constraint firstAttribute="width" constant="56" id="oLe-3o-2Vc"/>
                        </constraints>
                    </button>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MWu-cs-E6m">
                        <rect key="frame" x="18" y="18" width="246" height="18"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="249" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8x5-Ds-08R">
                                <rect key="frame" x="0.0" y="0.0" width="41.5" height="18"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="6kQ-VW-LlW"/>
                            <constraint firstItem="8x5-Ds-08R" firstAttribute="top" secondItem="MWu-cs-E6m" secondAttribute="top" id="XXI-vB-q8p"/>
                            <constraint firstAttribute="bottom" secondItem="8x5-Ds-08R" secondAttribute="bottom" id="fWe-0t-4tt"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TYx-VD-PcC">
                        <rect key="frame" x="18" y="40" width="246" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="Up3-VS-bgX"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jgg-Gp-7Mm">
                        <rect key="frame" x="18" y="64" width="246" height="20.5"/>
                        <constraints>
                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="wBr-Cm-3hA"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pGp-EA-muj">
                        <rect key="frame" x="18" y="139.5" width="302" height="0.5"/>
                        <color key="backgroundColor" red="0.97647058823529409" green="0.97647058823529409" blue="0.97647058823529409" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="0.5" id="blq-CC-AP2"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="TYx-VD-PcC" firstAttribute="top" secondItem="MWu-cs-E6m" secondAttribute="bottom" constant="4" id="7cD-6H-mlV"/>
                    <constraint firstAttribute="trailing" secondItem="yiK-Aj-ctU" secondAttribute="trailing" id="9OG-W5-1a7"/>
                    <constraint firstItem="MWu-cs-E6m" firstAttribute="leading" secondItem="TYx-VD-PcC" secondAttribute="leading" id="9gt-uW-qgr"/>
                    <constraint firstAttribute="trailing" secondItem="pGp-EA-muj" secondAttribute="trailing" id="EgZ-Ke-79J"/>
                    <constraint firstItem="yiK-Aj-ctU" firstAttribute="leading" secondItem="TYx-VD-PcC" secondAttribute="trailing" id="OQp-9J-1FA"/>
                    <constraint firstItem="Jgg-Gp-7Mm" firstAttribute="leading" secondItem="TYx-VD-PcC" secondAttribute="leading" id="QCM-18-U0n"/>
                    <constraint firstItem="yiK-Aj-ctU" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="RQd-fv-c2N"/>
                    <constraint firstItem="8x5-Ds-08R" firstAttribute="leading" secondItem="TYx-VD-PcC" secondAttribute="leading" id="Snv-vL-PId"/>
                    <constraint firstItem="yiK-Aj-ctU" firstAttribute="leading" secondItem="MWu-cs-E6m" secondAttribute="trailing" id="YZX-Sc-PBd"/>
                    <constraint firstItem="pGp-EA-muj" firstAttribute="leading" secondItem="TYx-VD-PcC" secondAttribute="leading" id="YdO-Al-nJv"/>
                    <constraint firstItem="TYx-VD-PcC" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="bGO-qT-DjR"/>
                    <constraint firstItem="MWu-cs-E6m" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="18" id="nDn-Wv-SsJ"/>
                    <constraint firstItem="Jgg-Gp-7Mm" firstAttribute="top" secondItem="TYx-VD-PcC" secondAttribute="bottom" constant="4" id="pAo-Mj-9JW"/>
                    <constraint firstItem="yiK-Aj-ctU" firstAttribute="leading" secondItem="Jgg-Gp-7Mm" secondAttribute="trailing" id="tXp-bU-g6k"/>
                    <constraint firstAttribute="bottom" secondItem="pGp-EA-muj" secondAttribute="bottom" id="tfH-KD-Zg7"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="contentLeadingLayout" destination="bGO-qT-DjR" id="45z-Rv-KVy"/>
                <outlet property="detailLabel" destination="TYx-VD-PcC" id="Hsg-aH-eYg"/>
                <outlet property="editButton" destination="yiK-Aj-ctU" id="JBm-gU-Tjo"/>
                <outlet property="lineView" destination="pGp-EA-muj" id="IBM-hc-LXn"/>
                <outlet property="regionLabel" destination="8x5-Ds-08R" id="gLD-w7-7Ly"/>
                <outlet property="userInfoLabel" destination="Jgg-Gp-7Mm" id="s9A-9n-VDr"/>
            </connections>
            <point key="canvasLocation" x="128.98550724637681" y="39.508928571428569"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
