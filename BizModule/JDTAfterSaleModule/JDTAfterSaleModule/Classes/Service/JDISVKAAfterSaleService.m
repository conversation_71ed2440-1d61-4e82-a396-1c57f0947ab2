//
//  JDISVKAAfterSaleService.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/11/9.
//

#import "JDISVKAAfterSaleService.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVKAAfterSaleModule/JDISVKAAfterSaleModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import "JDISVKAAfterSaleCustomModel.h"

@implementation JDISVKAAfterSaleService

+ (instancetype)shareService {
    static  JDISVKAAfterSaleService *service = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        service = [[JDISVKAAfterSaleService alloc] init];
    });
    
    return service;
}


- (NSURLSessionDataTask *)requestAfterSaleApplyListWithParameters:(NSDictionary *)parameters complete:(void (^)(NSDictionary * _Nonnull))completeBlock{
    
    //WT_TODO_fortest
//    {
#ifdef DEBUG
//        NSString* path = [NSBundle.mainBundle pathForResource:@"order_list" ofType:@"json"];
//        NSData* data = [NSData dataWithContentsOfFile:path];
//        id obj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:nil];
//        completeBlock(obj);
#endif
//    }
//    return nil;
    NSURLSessionDataTask *task = [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_order_list" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        
        if (completeBlock) {
            completeBlock(responseObject);
        }
    }];
    return task;
}

- (NSURLSessionDataTask *)requestAfterSaleRecordListWithParameters:(NSDictionary *)parameters complete:(void (^)(NSDictionary * _Nonnull))completeBlock{
    //WT_TODO_fortest
//    {
#ifdef DEBUG
//    NSString* path = [NSBundle.mainBundle pathForResource:@"record_list" ofType:@"json"];
//    NSData* data = [NSData dataWithContentsOfFile:path];
//    id obj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:nil];
//    completeBlock(obj);
#endif
//    }
    NSURLSessionDataTask *task = [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_record_list" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (completeBlock) {
            completeBlock(responseObject);
        }
    }];
    return task;
}

/**
 @brief JNOS网关请求(使用functionId)-不解析返回内容通用方法
 @see JDCDHTTPSessionRequestType
 @see JDCDURLRequestSerializerType
 @param urlPath 请求路径 e.g. http://beta-api.m.jd.com/api/product/v1/product/page 路径为@"api/product/v1/product/page"
 @param parameters parameters 业务参数
 @param completeBlock 完成回调(Block中是原始数据)
 @return 网络请求NSURLSessionDataTask
 */
+ (NSURLSessionDataTask * _Nonnull)jnosRequest:(JDCDHTTPSessionRequestType)requestType
                                          path:(NSString *)urlPath
                                       apiCode:(NSString*)apiCode
                                        params:(NSDictionary *)params
                                      complete:(nullable void (^)(JDCDURLTask *urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock {
//    [JDISVKAAfterSaleService shareService]
//    [[JDISVPlatformService sharedService] jnosHost];
    
    [[JDISVPlatformService sharedService] jnosRequest:@"" 
                                                 path:@""
                                              apiCode:@""
                                              bodyStr:@""
                                             complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        NSLog(@"1111");
    }];
//    [[JDISVPlatformService sharedService];
//    [KSARequest POST:urlPath
//             apiCode:apiCode
//          paramaters:params
//          completion:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
//        completeBlock(nil, responseObject, error);
//    }];
    return nil;
    
}

@end
