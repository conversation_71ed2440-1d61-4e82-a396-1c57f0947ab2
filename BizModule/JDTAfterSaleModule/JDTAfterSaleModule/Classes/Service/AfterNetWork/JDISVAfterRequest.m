//
//  JDISVAfterRequest.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.zhangchen41 on 2024/9/2.
//

#import "JDISVAfterRequest.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "NSString+JDISVAfterExtension.h"
#import "NSDictionary+JDISVAfterExtension.h"
//#import "KSANetworkConfig.h"
//#import "NSString+KSAEncrypt.h"
//#import "KSALoginManager.h"
//#import <JDBRouterModule/JDBRouterModule-umbrella.h>
//#import "NSDictionary_KSALogin.h"
//#import "KSALoginManager.h"
//#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
//#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
//#import <CommonCrypto/CommonCrypto.h>
//#import <JDISVYYModelModule/YYModel.h>
//#import "KSALoginManager.h"

@implementation JDISVAfterRequest

+ (NSURLSessionDataTask *)JnosPOST:(NSString *__nullable)path
                           apiCode:(NSString *)apiCode
                            cookie:(NSString*)cookieStr
                        paramaters:( NSDictionary *__nullable)paramaters
                        completion:(nullable void(^)(id responseObject, NSError *_error))completion{
    NSString *urlSting = [self urlStringWithPath:path apiCode:apiCode];
    
    return [self jnosDataTaskWithHTTPMethod:@"POST"
                                  URLString:urlSting
                                    apiCode:apiCode
                                     cookie:cookieStr
                                 paramaters:paramaters
                                 completion:completion];
    
}

+(NSURLSessionDataTask *)jnosDataTaskWithHTTPMethod:(NSString *)method
                                          URLString:(NSString *)URLString
                                            apiCode:(NSString *)apiCode
                                             cookie:(NSString*)cookieStr
                                         paramaters:(id)paramaters
                                         completion:(nullable void(^)(id responseObject, NSError *_error))completion  {
    
    NSMutableURLRequest *request = [self jnosRequestWithMethod:method 
                                                     URLString:URLString
                                                       apiCode:apiCode
                                                        cookie:cookieStr
                                                    paramaters:paramaters 
                                                         error:nil];
    request.timeoutInterval = 30;
    NSURLSession *session = [NSURLSession sharedSession];
    
    NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSDictionary *responseObject = nil;
        if (data) {
            responseObject = [NSJSONSerialization JSONObjectWithData:data
                                                             options:NSJSONReadingAllowFragments
                                                               error:&error];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error == nil) {
                NSNumber *code = [responseObject numKey:@"code"];
                if(code.intValue == 504 ||
                   code.intValue == 500){//token失效
//                    [[KSALoginManager shareManager].delegate logined:@"" token:@""];
                    NSError* resultErr = [NSError errorWithDomain:@"KSA.err" code:-12 userInfo:@{NSLocalizedDescriptionKey:responseObject[@"msg"]?:@"KSA_login_request_error"}];
                    completion(nil,resultErr);
                }else{
                    completion(responseObject,error);
                }
            }
            else {
                NSString* currentLang = [NSString getKAUseLang];
                NSString* errorMsg;
                if([currentLang isEqualToString: @"ar"]){
                    errorMsg = @"فشل تحميل الشبكة، يرجى المحاولة مرة أخرى";
                }else if([currentLang isEqualToString:@"en"]){
                    errorMsg = @"Network loading failed, please try again";
                }else{
                    errorMsg = @"网络加载失败，请重试";
                }
                NSError* resultErr = [NSError errorWithDomain:@"KSA.err" code:-12 userInfo:@{NSLocalizedDescriptionKey:errorMsg}];
                completion(nil,resultErr);
            }
        });
    }];
    [dataTask resume];
    return  dataTask;
}

+ (NSMutableURLRequest *)jnosRequestWithMethod:(NSString *)method
                                     URLString:(NSString *)URLString
                                       apiCode:(NSString *)apiCode
                                        cookie:(NSString*)cookieStr
                                    paramaters:(id)paramaters
                                         error:(NSError *__autoreleasing *)error {
    NSParameterAssert(method);
    NSParameterAssert(URLString);
    
    NSURL *url = [NSURL URLWithString:URLString];
//    NSString *payload = @"";
//    NSString *sign = @"";
//    if ([method isEqual:@"GET"]) {
//        NSMutableArray *queryArray = [NSMutableArray array];
//        [paramaters enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
//            [queryArray addObject:[NSString stringWithFormat:@"%@=%@",key,obj]];
//        }];
//        NSString *query = [queryArray componentsJoinedByString:@"&"];
//        query = [query stringByAddingPercentEncodingWithAllowedCharacters:[[NSCharacterSet characterSetWithCharactersInString:@"?!@#$^%*+,:;'\"`<>()[]{}/\\| "] invertedSet]];
//        //        query = [query stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
//        url = [NSURL URLWithString:[NSString stringWithFormat:@"%@?%@",URLString,query]];
//        payload = [[NSString stringWithFormat:@"%@?%@",apiCode,url.query] after_md5];
//    }
//    NSParameterAssert(url);
//    NSString *timestamp = @(floor([[NSDate date] timeIntervalSince1970] * 1000)).stringValue;
//    NSString *nonce = [timestamp after_md5];
//    NSString *signAlgorithm = @"HMACSHA1";
//    NSString *accessKey = [KSANetworkConfig shareConfig].appKey;
//    NSString *appUniqueCode = [KSANetworkConfig shareConfig].appUniqueCode;
//    NSString *deviceId = [KSANetworkConfig shareConfig].deviceId;
//    NSString *appCode = [KSANetworkConfig shareConfig].appKey;
//    
    NSMutableURLRequest *mutableRequest = [[NSMutableURLRequest alloc] initWithURL:url];
//    mutableRequest.timeoutInterval = 10;
//    mutableRequest.HTTPMethod = method;
//    if ([method isEqual:@"POST"]) {
//        if (paramaters) {
//            NSData *body =  [NSJSONSerialization dataWithJSONObject:paramaters options:NSJSONWritingFragmentsAllowed error:nil];
//            payload = [[[NSString alloc] initWithData:body encoding:NSUTF8StringEncoding] after_md5];
//            mutableRequest.HTTPBody = body;
//            NSString *buId = [paramaters objectForKey:@"buId"];
//            NSString *tenantId = [paramaters objectForKey:@"tenantId"];
//            [mutableRequest setValue:buId forHTTPHeaderField:@"buId"];
//            [mutableRequest setValue:tenantId forHTTPHeaderField:@"tenantId"];
//        }
//    }
//    [mutableRequest setValue: @"application/json; encoding=utf-8" forHTTPHeaderField:@"Content-Type"];
//    [mutableRequest setValue: @"application/json" forHTTPHeaderField:@"Accept"];
//    [mutableRequest setValue:timestamp forHTTPHeaderField:@"Timestamp"];
//    [mutableRequest setValue:accessKey forHTTPHeaderField:@"Access-Key"];
//    [mutableRequest setValue:nonce forHTTPHeaderField:@"Nonce"];
//    [mutableRequest setValue:signAlgorithm forHTTPHeaderField:@"Sign-Algorithm"];
//    [mutableRequest setValue:payload forHTTPHeaderField:@"Payload-Hash"];
//    [mutableRequest setValue:@"ios" forHTTPHeaderField:@"req-source"];
//    [mutableRequest setValue:appUniqueCode forHTTPHeaderField:@"app-unique-code"];
//    [mutableRequest setValue:deviceId forHTTPHeaderField:@"req-deviceId"];
//    [mutableRequest setValue:appCode forHTTPHeaderField:@"app-code"];
//    [mutableRequest setValue:appCode forHTTPHeaderField:@"req-app-code"];
//    [mutableRequest setValue:@"b2c" forHTTPHeaderField:@"jnos-product-code"];
//    [mutableRequest setValue:appCode forHTTPHeaderField:@"Jnos-App-Code"];
//    [mutableRequest setValue:@"C" forHTTPHeaderField:@"Jnos-App-Category"];
//    [mutableRequest setValue:@"root" forHTTPHeaderField:@"Jnos-Tenant-Code"];
//    
//    NSString* currentLang = [NSString getKAUseLang];
//    NSString* langStr;
//    if([currentLang isEqualToString: @"ar"]){
//        langStr = @"ar_SA";
//    }else if([currentLang isEqualToString:@"en"]){
//        langStr = @"en_US";
//    }else{
//        langStr = @"zh_CN";
//        
//    }
//    NSString* uuid = PlatformService.getUUID;
//    NSString* cookie;
//    if(cookieStr.length){
//        cookie = [NSString stringWithFormat:@"Jnos-App-Code=b2c.sdk.app;req-app-code=%@;jnos-login-token=%@;content-type=application/json;KSA-App-Category=%@;b2c_c_lang=%@;b2c_c=%@;VISITOR_UUID=%@;%@",appCode,[KSALoginManager shareManager].loginToken?:@"",appCode?:@"",langStr,langStr,uuid,cookieStr];
//    }else{
//        cookie = [NSString stringWithFormat:@"Jnos-App-Code=b2c.sdk.app;req-app-code=%@;jnos-login-token=%@;content-type=application/json;KSA-App-Category=%@;b2c_c_lang=%@;b2c_c=%@;VISITOR_UUID=%@",appCode,[KSALoginManager shareManager].loginToken?:@"",appCode?:@"",langStr,langStr,uuid];
//    }
//    cookie = [NSString stringWithFormat:@"%@;pin=%@;b2c_c_phone=%@;b2c_c_email=%@",cookie,[PlatformService getUserPin],[KSALoginManager shareManager].phone?:@"",[KSALoginManager shareManager].email?:@""];
//
//    
//    [mutableRequest setValue:cookie forHTTPHeaderField:@"Cookie"];
//
//    NSString* phoneVersion = [[UIDevice currentDevice] systemVersion];
//    [mutableRequest setValue:phoneVersion forHTTPHeaderField:@"req-device-os-version"];
//
//    NSString* systemName = [[UIDevice currentDevice] systemName];
//    [mutableRequest setValue:systemName forHTTPHeaderField:@"req-device-os-name"];
//
//    NSString* phoneModel = [[UIDevice currentDevice] model];
//    [mutableRequest setValue:phoneModel forHTTPHeaderField:@"req-device-version"];
//    if([KSANetworkConfig shareConfig].fp){
//        [mutableRequest setValue:[KSANetworkConfig shareConfig].fp forHTTPHeaderField:@"fp"];
//    }else{
//        [mutableRequest setValue:@"NONE" forHTTPHeaderField:@"fp"];
//    }
//    if([KSANetworkConfig shareConfig].deviceId){
//        [mutableRequest setValue:[KSANetworkConfig shareConfig].deviceId forHTTPHeaderField:@"deviceId"];
//    }
//    
//    if ([KSALoginManager shareManager].loginToken) {
//        [mutableRequest setValue:[KSALoginManager shareManager].loginToken forHTTPHeaderField:@"jnos-login-token"];
//    }
//    sign = [[NSString stringWithFormat:@"%@,%@,%@,%@",signAlgorithm,payload,timestamp,nonce] ksa_HMACSHA1];
//    
//    [mutableRequest setValue:sign forHTTPHeaderField:@"sign"];
    return mutableRequest;
}

#pragma mark - private
+ (NSString *)urlStringWithPath:(NSString *)path
                        apiCode:(NSString *)apiCode{
    NSString *jnosHost = [[JDISVPlatformService sharedService] jnosHost];
    if (path) {
        return [jnosHost stringByAppendingString:[NSString stringWithFormat:@"/%@",apiCode]];
    }else{
        return [jnosHost stringByAppendingString:[NSString stringWithFormat:@"/%@",apiCode]];
    }
    return jnosHost;
}

@end
