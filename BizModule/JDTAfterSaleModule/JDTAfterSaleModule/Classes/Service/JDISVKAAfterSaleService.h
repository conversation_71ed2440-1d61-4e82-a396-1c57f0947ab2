//
//  JDISVKAAfterSaleService.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/11/9.
//

#import <Foundation/Foundation.h>


NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSaleService : NSObject

+ (instancetype)shareService;

/// 售后申请列表
/// @param parameters 入参
/// @param completeBlock 成功回调
- (NSURLSessionDataTask *)requestAfterSaleApplyListWithParameters:(NSDictionary *)parameters complete:(nullable void (^)(NSDictionary *response))completeBlock;

/// 售后记录列表
/// @param parameters 入参
/// @param completeBlock 成功回调
- (NSURLSessionDataTask *)requestAfterSaleRecordListWithParameters:(NSDictionary *)parameters complete:(nullable void (^)(NSDictionary *response))completeBlock;

@end

NS_ASSUME_NONNULL_END
