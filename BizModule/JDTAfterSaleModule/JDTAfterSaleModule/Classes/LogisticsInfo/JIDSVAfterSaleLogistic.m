//
//  JIDSVAfterSaleLogistic.m
//  abseil
//
//  Created by cdwutao3 on 2024/1/20.
//

#import "JIDSVAfterSaleLogistic.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVLogisticData.h"
#import "JDISVLogisticCell.h"

@interface JIDSVAfterSaleLogistic ()<UITableViewDataSource,UITableViewDelegate>

@property(strong,nonatomic) KANavigationBar *navigationBar;
@property(strong,nonatomic) UITableView* tableView;
@property (strong,nonatomic) NSArray<JDISVLogisticData*>* datas;
@end

@implementation JIDSVAfterSaleLogistic
+(instancetype)controllerWithLogisticDatas:(NSArray*)logisticDatas{
    JIDSVAfterSaleLogistic* vc = [[JIDSVAfterSaleLogistic alloc] init];
    vc.datas = logisticDatas;
    return vc;
    
}
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    // Do any additional setup after loading the view.
    self.navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:self.navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    NSString* info = Lang(@"aftersale_logistics_track");
    self.navigationBar
        .decorator
        .backgroundColor() //背景色
        .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
            __strong typeof(weakSelf) strongSelf = weakSelf;
            [strongSelf.navigationController popViewControllerAnimated:YES];
        }) //返回按钮
        .title(info, NSTextAlignmentCenter) //标题
        .render();
    [self.view addSubview:self.navigationBar];
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero];
    [self.view addSubview:self.tableView];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    UIEdgeInsets inset =  [UIWindow ka_uikit_safeAreaInsets];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(self.view);
        make.top.mas_equalTo(self.navigationBar.mas_bottom);
        make.bottom.equalTo(self.view.mas_bottom).mas_offset(-inset.bottom);
    }];
    [self.tableView registerClass:JDISVLogisticCell.class forCellReuseIdentifier:@"JDISVLogisticCell"];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.datas.count;
}

// Row display. Implementers should *always* try to reuse cells by setting each cell's reuseIdentifier and querying for available reusable cells with dequeueReusableCellWithIdentifier:
// Cell gets various attributes set automatically based on table (separators) and data source (accessory views, editing controls)

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVLogisticCell* cell = [self.tableView dequeueReusableCellWithIdentifier:@"JDISVLogisticCell"];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    JDISVLogisticData* data = self.datas[indexPath.row];
    if(self.datas.count == 1){
        data.type = JDISVLogisticBeginEnd;
    }else{
        if(indexPath.row == 0){
            data.type = JDISVLogisticBegin;
        }else if(indexPath.row == self.datas.count -1){
            data.type = JDISVLogisticEnd;
        }else{
            data.type = JDISVLogisticMiddle;
        }
    }
    [cell configWithData:data];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVLogisticData* data = self.datas[indexPath.row];
    return data.cellHeight;
}
@end
