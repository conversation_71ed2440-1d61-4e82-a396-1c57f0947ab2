//
//  JDISVLogisticData.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2024/1/20.
//

#import "JDISVLogisticData.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
@implementation JDISVLogisticData

-(CGFloat)cellHeight{
    CGFloat titleWidth = UIScreen.mainScreen.bounds.size.width - 50-18;
    UIFont * font = [UIFont systemFontOfSize:14];
    CGSize size = [self.title jdcd_getStringSize:font constraintsSize:CGSizeMake(titleWidth, 300)];
    CGFloat height = size.height;
    if(size.height <10){
        height = 16;
    }
    return ceil(height)+25+6+14+2;
}
@end
