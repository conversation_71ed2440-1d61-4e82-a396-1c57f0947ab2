//
//  JDISVLogisticData.h
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2024/1/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSUInteger, JDISVLogisticType) {
    JDISVLogisticBegin,
    JDISVLogisticBeginEnd,
    JDISVLogisticMiddle,
    JDISVLogisticEnd,
} ;

@interface JDISVLogisticData : NSObject
@property(assign,nonatomic) JDISVLogisticType type;
@property(copy,nonatomic) NSString* title;
@property(copy,nonatomic)NSString* creationTime;
@property(strong,nonatomic) NSNumber* stateChange;
-(CGFloat)cellHeight;
@end

NS_ASSUME_NONNULL_END
