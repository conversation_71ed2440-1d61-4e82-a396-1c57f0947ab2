//
//  JDISVLogisticCell.m
//  abseil
//
//  Created by cdwutao3 on 2024/1/20.
//

#import "JDISVLogisticCell.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVKAAfterSaleUtils.h"
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
@interface JDISVLogisticCell(){
    
}
@property (strong,nonatomic) JDISVLogisticData* data;
@property (strong,nonatomic) UILabel* title;
@property (strong,nonatomic) UILabel* time;
@property (strong,nonatomic) UIImageView* ShowImg;
@property (strong,nonatomic) UIView* line;
@end

@implementation JDISVLogisticCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}
-(void)resetUI{
    [self.title removeFromSuperview];
    [self.time removeFromSuperview];
    [self.ShowImg removeFromSuperview];
    [self.line removeFromSuperview];
    self.title = [[UILabel alloc] initWithFrame:CGRectZero];
    self.time = [[UILabel alloc] initWithFrame:CGRectZero];
    self.ShowImg = [[UIImageView alloc] initWithFrame:CGRectZero];
    self.line = [[UIView alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.line];
    [self.contentView addSubview:self.title];
    [self.contentView addSubview:self.time];
    [self.contentView addSubview:self.ShowImg];
    
    self.title.numberOfLines = 0;
    self.title.font = [UIFont systemFontOfSize:14];
    self.time.font = [UIFont systemFontOfSize:12];
    self.time.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1];
    self.line.backgroundColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1];
}

-(void)configWithData:(JDISVLogisticData*)data{
    self.data = data;
    [self resetUI];
    if(data.type == JDISVLogisticBeginEnd){
        [self configBeginEnd];
    }
    else if(data.type == JDISVLogisticBegin){
        [self configBegin];
    }else if(data.type == JDISVLogisticMiddle){
        [self configMiddle];
    }else if(data.type == JDISVLogisticEnd){
        [self configEnd];
    }
    self.time.font = [UIFont systemFontOfSize:14];
    
    [self.title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(25);
        make.leading.mas_offset(50);
        make.trailing.mas_offset(-18);
        make.height.mas_greaterThanOrEqualTo(16);
    }];
    [self.time mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.title.mas_bottom).mas_offset(6);
        make.leading.mas_offset(50);
        make.trailing.mas_offset(-18);
    }];
    self.time.text = data.creationTime;
    self.title.text = data.title;
}

-(void)configBeginEnd{
    UIImage* img = JDISVKAAfterSaleModuleImageNamed(@"AfterSale_tackerend");
    self.ShowImg.image = img;
    self.ShowImg.backgroundColor = UIColor.clearColor;
    [self.ShowImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(25);
        make.leading.mas_offset(18);
        make.width.height.mas_offset(20);
    }];
    self.line.hidden = YES;
    self.title.textColor = [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1];
}
-(void)configBegin{
    UIImage* img = JDISVKAAfterSaleModuleImageNamed(@"AfterSale_tackerend");
    self.ShowImg.image = img;
    self.ShowImg.backgroundColor = UIColor.clearColor;
    [self.ShowImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_offset(25);
        make.leading.mas_offset(18);
        make.width.height.mas_offset(20);
    }];
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_offset(28);
        make.top.mas_offset(35);
        make.width.mas_equalTo(1);
        make.bottom.mas_equalTo(self.contentView.mas_bottom);
    }];
    self.line.hidden = NO;
    self.title.textColor = [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1];
}
-(void)configEnd{
    self.ShowImg.backgroundColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1];
    self.ShowImg.image = nil;
    [self.ShowImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(12);
        make.top.mas_equalTo(self.contentView.mas_top).mas_offset(31);
        make.leading.mas_equalTo(self.contentView.mas_leading).mas_offset(23);
    }];
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_offset(28);
        make.top.mas_offset(0);
        make.width.mas_equalTo(1);
        make.bottom.mas_equalTo(self.contentView.mas_top).mas_offset(33);
    }];
    self.ShowImg.layer.cornerRadius = 6;
    self.ShowImg.layer.masksToBounds = YES;
    self.line.hidden = NO;
    self.title.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1];
    
}

-(void)configMiddle{
    [self.line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_offset(28);
        make.top.mas_offset(0);
        make.width.mas_equalTo(1);
        make.bottom.mas_equalTo(self.contentView.mas_bottom);
    }];
    self.ShowImg.backgroundColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1];
    self.ShowImg.image = nil;
    [self.ShowImg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(12);
        make.top.mas_equalTo(self.contentView.mas_top).mas_offset(31);
        make.leading.mas_equalTo(self.contentView.mas_leading).mas_offset(23);
    }];
    self.ShowImg.layer.cornerRadius = 6;
    self.ShowImg.layer.masksToBounds = YES;
    self.line.hidden = NO;
    self.title.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:0.6];
}


@end
