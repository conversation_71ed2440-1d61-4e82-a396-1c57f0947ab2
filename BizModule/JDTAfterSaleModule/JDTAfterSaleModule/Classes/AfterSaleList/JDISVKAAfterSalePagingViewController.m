//
//  JDISVKAAfterSalePagingViewController.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/21.
//

#import "JDISVKAAfterSalePagingViewController.h"
#import <JDISVPagerViewModule/JXPagerView.h>
#import <JDISVPagerViewModule/JXCategoryView.h>
#import "JDISVKAAfterSaleApplyViewController.h"
#import "JDISVKAAfterSaleRecordViewController.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSObject+JDCDExtend.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVCategoryModule/UIView+JDCDRTL.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>//路由
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@interface JDISVKAAfterSalePagingViewController()<JXCategoryViewDelegate>

@property (nonatomic, strong) JXCategoryTitleView *categoryView;
@property (nonatomic, strong) NSArray <NSString *> *titles;
@property (nonatomic, strong) JDISVKAAfterSaleApplyViewController *applyViewController;
@property (nonatomic, strong) JDISVKAAfterSaleRecordViewController *recordViewController;

@end

@implementation JDISVKAAfterSalePagingViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor whiteColor];
    [self.navigationController.navigationBar setHidden:YES];
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    navigationBar.hidden = self.shouldHiddenNavigationBar;
    [self.view addSubview:navigationBar];
    JDWeakSelf
    navigationBar
    .decorator
    .backgroundColor()
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDStrongSelf
        [self.navigationController popViewControllerAnimated:YES];
    })
    .title(Lang(@"after_sale_title"), NSTextAlignmentCenter) //标题
    .render();
    
    self.edgesForExtendedLayout = UIRectEdgeNone;
    _titles = @[Lang(@"after_sale_apply"), Lang(@"after_sale_record")];
    
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/KSAGlobalSearchView",@"KSAGlobalSearchModule"] arg:nil error:nil completion:^(UIView * view) {
        if(view){
            [self.view addSubview:view];
            [view mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.view);
                make.trailing.mas_equalTo(self.view);
                make.top.mas_equalTo([UIWindow ka_uikit_navigationHeight]);
                make.height.mas_equalTo(50.);
            }];
        }
    }];
    
    _categoryView = [[JXCategoryTitleView alloc] initWithFrame:CGRectMake(0, 50, [UIScreen mainScreen].bounds.size.width, 44)];
    self.categoryView.titles = self.titles;
    self.categoryView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.categoryView.delegate = self;
    self.categoryView.titleColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.categoryView.titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
    self.categoryView.titleSelectedColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.categoryView.titleSelectedFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium];
    self.categoryView.contentEdgeInsetLeft = 24;
//    self.categoryView.contentEdgeInsetRight = 200;
    self.categoryView.cellSpacing = 24;
    self.categoryView.averageCellSpacingEnabled = false;
    

    JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
    lineView.indicatorColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    lineView.indicatorWidth = 18;
    lineView.indicatorHeight = 3;
    lineView.verticalMargin = 8;
    self.categoryView.indicators = @[lineView];

    _pagerView = [self preferredPagingView];
    self.pagerView.mainTableView.gestureDelegate = self;
    [self.view addSubview:self.pagerView];

    self.categoryView.listContainer = (id<JXCategoryViewListContainer>)self.pagerView.listContainerView;
    [self.categoryView setDefaultSelectedIndex:self.categoryIndex];
    //导航栏隐藏的情况，处理扣边返回，下面的代码要加上
//    [self.pagerView.listContainerView.scrollView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
//    [self.pagerView.mainTableView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    self.navigationController.interactivePopGestureRecognizer.enabled = (self.categoryView.selectedIndex == 0);
    
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];

    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
}

- (JXPagerView *)preferredPagingView {
    return [[JXPagerView alloc] initWithDelegate:self];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    self.pagerView.frame = CGRectMake(0, (self.shouldHiddenNavigationBar ? 50: ([UIWindow ka_uikit_navigationHeight] + 50)), self.view.bounds.size.width, self.view.bounds.size.height - (self.shouldHiddenNavigationBar ? 50: ([UIWindow ka_uikit_navigationHeight] + 50)));
}

#pragma mark - JXPagerViewDelegate

- (UIView *)tableHeaderViewInPagerView:(JXPagerView *)pagerView {
    return [UIView new];
}

- (NSUInteger)tableHeaderViewHeightInPagerView:(JXPagerView *)pagerView {
    return 0;
}

- (NSUInteger)heightForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return 44;
}

- (UIView *)viewForPinSectionHeaderInPagerView:(JXPagerView *)pagerView {
    return self.categoryView;
}

- (NSInteger)numberOfListsInPagerView:(JXPagerView *)pagerView {
    //和categoryView的item数量一致
    return self.categoryView.titles.count;
}

- (id<JXPagerViewListViewDelegate>)pagerView:(JXPagerView *)pagerView initListAtIndex:(NSInteger)index {
    if (index == 0) {
        self.applyViewController = [[JDISVKAAfterSaleApplyViewController alloc] init];
        self.applyViewController.title = Lang(@"after_sale_apply");
        
        if (self.categoryView.defaultSelectedIndex == 0) {
            [self.applyViewController requestData];
        }
        JDWeakSelf
        self.applyViewController.submitApplySuccess = ^{
            JDStrongSelf
            [self.categoryView selectItemAtIndex:1];
        };
        return self.applyViewController;
    }else {
        self.recordViewController = [[JDISVKAAfterSaleRecordViewController alloc] init];
        self.recordViewController.title = Lang(@"after_sale_record");
        
        if (self.categoryView.defaultSelectedIndex == 1) {
            [self.recordViewController requestData];
        }
        return self.recordViewController;
    }
}



#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    self.navigationController.interactivePopGestureRecognizer.enabled = (index == 0);
    if (index == 0) {
        [self.applyViewController requestData];
    }else{
        [self.recordViewController requestData];
    }
}


@end
