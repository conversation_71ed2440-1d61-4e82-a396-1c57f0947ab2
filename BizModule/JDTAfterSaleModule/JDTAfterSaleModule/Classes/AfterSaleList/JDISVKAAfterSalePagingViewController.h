//
//  JDISVKAAfterSalePagingViewController.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/21.
//

#import <UIKit/UIKit.h>
#import <JDISVPagerViewModule/JXPagerView.h>
//#import <JDISVPagerViewModule/JXCategoryTitleView.h>

@class JXCategoryTitleView;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSalePagingViewController : UIViewController<JXPagerViewDelegate, JXPagerMainTableViewGestureDelegate>

@property (nonatomic, assign) BOOL shouldHiddenNavigationBar;//隐藏后页面整体上移到顶部，默认NO
@property (nonatomic, strong) JXPagerView *pagerView;
@property (nonatomic, strong, readonly) JXCategoryTitleView *categoryView;

@property (nonatomic, assign)NSInteger categoryIndex;
- (JXPagerView *)preferredPagingView;

@end

NS_ASSUME_NONNULL_END
