//
//  JDISVKAAfterSaleApplyModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSaleApplyModel : NSObject

@property (nonatomic,copy)NSString *skuNumber;

@property (nonatomic,copy)NSString *skuName;

@property (nonatomic,copy)NSString *skuImage;

@property (nonatomic,copy)NSString *skuCount;

@property (nonatomic,copy)NSString *isOverTime;

@property (nonatomic,copy)NSString *isSubmited;

@property (nonatomic,assign)CGFloat *cellHeight;

@property (nonatomic,assign)BOOL replaceNewEnabled;

@property (nonatomic,assign)BOOL returnEnabled;

@end

@interface afterSaleApplyListServiceType : NSObject

@property (nonatomic,copy)NSString *serviceText;

@property (nonatomic,assign)NSInteger serviceType;

@property (nonatomic,assign)NSInteger valid;


@property (nonatomic,strong)NSString *tips;

@end

@interface afterSaleApplyListOrderDetail : NSObject


@property (nonatomic,assign)NSInteger skuType; //商品类型（"0":一般商品 "1":延保商品 "2":赠品结构（可能是赠品或者附件））

@property (nonatomic,copy)NSString *skuName;

@property (nonatomic,assign)NSInteger skuCount;

@property (nonatomic,copy)NSString *afsServiceId;

@property (nonatomic,copy)NSString * orderId;

@property (nonatomic,strong)NSNumber * skuId;

@property (copy,nonatomic) NSString * skuUUid;  //skuUUid;

@property (nonatomic,strong)NSNumber *appliedCount;

@property (nonatomic,copy)NSString *skuImg; //

/*
 400-not support
 401-time out
 402-number err
 */
@property (nonatomic,strong)NSNumber *reasonType; //

/*
 可以申请售后则为空
 402-已无可申请售后数量：您的售后申请已提交。
 401-订单时间已过期：您的商品已超过售后申请时间。
 400-订单状态不支持：您的订单未完成，不可申请售后。
 */
@property (nonatomic,copy)NSString *reasonText; //不能申请售后的原因文案

@property (nonatomic,assign)BOOL canApply;  //是否可申请售后

@property (nonatomic,strong)NSArray <afterSaleApplyListServiceType *> *serviceTypeList; 

@end

@interface afterSaleApplyListOrder : NSObject

@property (nonatomic,copy)NSString *orderId; //订单号

@property (nonatomic,assign)NSInteger status; //订单状态

@property (nonatomic,assign)NSInteger orderType;  //订单类型

@property (nonatomic,assign)NSInteger dateSubmit;  //订单提交日期

@property (nonatomic,assign)NSInteger venderId;

@property (nonatomic,copy)NSString *venderName;

@property (nonatomic,strong)NSArray<afterSaleApplyListOrderDetail *> *skuList;

@end

@interface AfterSaleApplyListPage : NSObject

@property (nonatomic,assign)NSInteger currentPage; //当前页

@property (nonatomic,assign)NSInteger totalCount; //数据总数

@property (nonatomic,assign)NSInteger pageSize;  //请求每页数量

@property (nonatomic,copy)NSString *unit; //单位

@property (nonatomic,strong) NSArray<afterSaleApplyListOrder *> *result;

@end

NS_ASSUME_NONNULL_END
