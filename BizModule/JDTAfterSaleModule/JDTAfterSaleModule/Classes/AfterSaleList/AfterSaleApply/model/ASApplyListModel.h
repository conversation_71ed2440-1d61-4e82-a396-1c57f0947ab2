//
//  ASApplyListModel.h
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/7.
//

#import <Foundation/Foundation.h>

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

// 商品不能申请售后原因
typedef NS_ENUM(NSUInteger, AFSkuNoApplyReasonType) {
    AFSkuNoApplyReasonTypeNotSupport = 1, // 该商品不支持售后
    AFSkuNoApplyReasonTypeNoCount = 2,    // 无可申请数量
    AFSkuNoApplyReasonTypeExpired = 3,    // 已超过售后期
    AFSkuNoApplyReasonTypeApplied = 4     // 已提交过申请
};

// 售后类型
typedef NS_ENUM(NSUInteger, AFServiceType) {
    AFServiceTypeRefundOnly = 3,          // 仅退款
    AFServiceTypeReturnAndRefund = 1,     // 退货退款
    AFServiceTypeExchange = 2             // 换货
};

typedef NS_ENUM(NSUInteger, AFServiceStatus) {
    AFServiceStatusCreated,                // 初始化
    AFServiceStatusWaitAudit,              // 等待审核
    AFServiceStatusWaitCustomerShipping,   // 等待客户发货
    AFServiceStatusVenderPickUpIng,        // 商家取货中
    AFServiceStatusWaitVenderReceiving,    // 等待商家收货
    AFServiceStatusWaitVenderHandle,       // 等待商家处理
    AFServiceStatusVenderExchangeing,      // 商家换货中
    AFServiceStatusWaitRefund,             // 等待退款
    AFServiceStatusRefunding,              // 退款中
    AFServiceStatusRefunded,               // 已退款
    AFServiceStatusReject,                 // 被驳回
    AFServiceStatusCancel,                 // 取消
    AFServiceStatusClose,                  // 关闭
    AFServiceStatusFinished                // 完成
};

@interface ASApplyListItemAddressInfoModel : NSObject

@property (nonatomic, assign) NSInteger addressId;

@property (nonatomic, assign) NSInteger provinceId;

@property (nonatomic, assign) NSInteger cityId;
/// 县
@property (nonatomic, assign) NSInteger countyId;
/// 乡
@property (nonatomic, assign) NSInteger villageId;

@property (nonatomic, copy) NSString *provinceName;

@property (nonatomic, copy) NSString *cityName;

@property (nonatomic, copy) NSString *countyName;

@property (nonatomic, copy) NSString *villageName;
/// 详细地址
@property (nonatomic, copy) NSString *address;
/// 售后详细地址（只有详情没有拼接）
@property (nonatomic, copy) NSString *addressDetail;

@property (nonatomic, copy) NSString *latitude;

@property (nonatomic, copy) NSString *longitude;

@end

@interface ASApplyListItemExpressInfoModel : NSObject
/// 快递单号
@property (nonatomic, copy) NSString *expressNo;
/// 商品数量
@property (nonatomic, assign) NSInteger nums;

@end

@interface ASApplyListItemReturnCustomerInfoModel : NSObject

@property (nonatomic, copy) NSString *contactName;

@property (nonatomic, copy) NSString *contactTel;
/// 快递单号（老）
@property (nonatomic, copy) NSString *expressNo;
/// 快递单号
@property (nonatomic, copy) NSArray <ASApplyListItemExpressInfoModel *> *expressInfos;
/// 地址信息
@property (nonatomic, strong) ASApplyListItemAddressInfoModel *addressInfo;
/// 快递公司
@property (nonatomic, copy) NSString *expressCompany;

@end

@interface ASApplyListItemSkuItemLocCodeModel : NSObject
/// 核销码
@property (nonatomic, copy) NSString *locCode;
/// 核销状态
@property (nonatomic, assign) NSInteger status;

@end

@interface ASApplyListItemSkuItemGeneralTypeModel : NSObject
/// 类型枚举值
@property (nonatomic, assign) NSInteger type;
/// 类型文案
@property (nonatomic, copy) NSString *text;

@end

@interface ASApplyListItemSkuItemServiceTypeModel : NSObject
/// 服务类型文案(退货退款、换货等)
@property (nonatomic, copy) NSString *serviceText;
/// 服务类型枚举值
@property (nonatomic, assign) AFServiceType serviceType;
/// 服务按钮是否支持，不支持为0，支持为1
@property (nonatomic, assign) BOOL valid;
/// 服务按钮不支持时下发的提示文案
@property (nonatomic, copy) NSString *tips;

@end

@interface ASApplyListItemSkuItemModel : NSObject

@property (nonatomic, copy) NSString *skuImg;

@property (nonatomic, copy) NSString *skuId;

@property (nonatomic, copy) NSString *skuUUid;

@property (nonatomic, copy) NSString *skuName;

@property (nonatomic, assign) NSInteger skuCount;
/// 商品类型（"0":一般商品 "1":延保商品 "2":赠品结构（可能是赠品或者附件））
@property (nonatomic, assign) NSInteger skuType;
/// 商品价格
@property (nonatomic, strong) NSNumber *skuPrice;
/// 商品实际应付价格
@property (nonatomic, strong) NSNumber *skuSingleShouldPrice;
/// 是否可申请
@property (nonatomic, assign) BOOL canApply;
/// 支持的申请服务类型
@property (nonatomic, copy) NSArray <NSNumber *> *canApplyType;
/// 可申请售后数量
@property (nonatomic, strong) NSNumber *canApplyCount;
/// 退款金额
@property (nonatomic, strong) NSNumber *preReturnAmount;
/// 积分退款金额
@property (nonatomic, strong) NSNumber *returnPointAmount;
/// 优惠券退款金额
@property (nonatomic, strong) NSNumber *returnVoucherAmount;
/// 已申请数量
@property (nonatomic, assign) NSInteger appliedCount;
/// 不能申请售后的原因代码
@property (nonatomic, assign) AFSkuNoApplyReasonType reasonType;
/// 不能申请售后的原因文案
@property (nonatomic, copy) NSString *reasonText;
/// 已申请的售后服务单，如果多个取最新的即可
@property (nonatomic, strong) NSNumber *afsServiceId;
/// 已申请的最新的售后服务单的状态
@property (nonatomic, assign) AFServiceStatus afsServiceStatus;
/// 服务类型
@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemServiceTypeModel *> *serviceTypeList;
/// 退款方式
@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemGeneralTypeModel *> *refundTypeList;
/// 退货方式
@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemGeneralTypeModel *> *returnModelList;
/// 核销码列表
@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemLocCodeModel *> *locCodeList;

@end

@interface ASApplyListItemVenderModel : NSObject

@property (nonatomic, assign) NSInteger venderId;

@property (nonatomic, copy) NSString *venderName;

@property (nonatomic, copy) NSString *logo;
/// 是否展示“联系客服”
@property (nonatomic, assign) BOOL displayChatBtn;

@property (nonatomic, assign) NSInteger shopId;

@property (nonatomic, copy) NSString *shopName;

@end

@interface ASApplyListItemModel : NSObject

@property (nonatomic, copy) NSString *orderId;

@property (nonatomic, assign) ProductType orderType;
/// 是否自营
@property (nonatomic, assign) BOOL orderSelf;
/// 是否有赠品
@property (nonatomic, assign) BOOL orderHasGift;
/// 实际支付金额
@property (nonatomic, copy) NSString *actualPayAmount;

@property (nonatomic, assign) NSInteger orderStatus;
/// 下单时间
@property (nonatomic, copy) NSString *bookTime;

@property (nonatomic, copy) NSString *completeTime;
/// 店铺信息
@property (nonatomic, strong) ASApplyListItemVenderModel *venderInfo;
/// 商品（现在的订单拆分原则是按照 sku 维度的，有几个 sku 就会拆分成几个订单，因此这里skuList只会有 1 个元素）
@property (nonatomic, copy) NSArray <ASApplyListItemSkuItemModel *> *skuList;
/// 用户收货地址信息
@property (nonatomic, strong) ASApplyListItemReturnCustomerInfoModel *returnCustomerInfo;

@property (nonatomic, copy) NSDictionary <NSString *, NSString *> *sendpayMap;

@property (nonatomic, assign) NSInteger orderSource;

@end

@interface ASApplyListModel : NSObject
/// 总条数（总页数 = total / pageSize）
@property (nonatomic, assign) NSInteger total;

@property (nonatomic, copy) NSArray <ASApplyListItemModel *> *data;

@property (nonatomic, assign) NSInteger currentPage;

@property (nonatomic, assign) NSInteger pageSize;
/// 滚动ID（用于分页查询）
@property (nonatomic, copy) NSString *scrollId;

@end

NS_ASSUME_NONNULL_END
