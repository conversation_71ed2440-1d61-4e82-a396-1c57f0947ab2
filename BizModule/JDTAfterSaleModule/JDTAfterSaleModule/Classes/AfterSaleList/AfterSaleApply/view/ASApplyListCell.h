//
//  ASApplyListCell.h
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/8.
//

#import <UIKit/UIKit.h>
#import "ASApplyListModel.h"

@class RACSubject;

typedef NS_ENUM(NSUInteger, ASApplyListCellStyleType) {
    ASApplyListCellStyleTypeApplyBtnFirst,             // 第一次申请
    ASApplyListCellStyleTypeApplyBtnDisabled,          // 按钮显示但禁用(超过售后期时)
    ASApplyListCellStyleTypeApplyBtnAgain,             // 再次申请（购买多件商品，剩余商品继续申请）
    ASApplyListCellStyleTypeNoApplyBtnNotSupport,      // 无申请按钮-不支持申请
    ASApplyListCellStyleTypeNoApplyBtnApplying         // 无申请按钮-申请中（显示一条进度信息）
};

NS_ASSUME_NONNULL_BEGIN

@interface ASApplyListCell : UITableViewCell

@property (nonatomic, strong) RACSubject *delegate;

/// 配置数据
/// @param model 数据模型
- (void)configWithModel:(ASApplyListItemModel *)model;

/// 渲染界面
/// @param index 索引
- (void)render:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END
