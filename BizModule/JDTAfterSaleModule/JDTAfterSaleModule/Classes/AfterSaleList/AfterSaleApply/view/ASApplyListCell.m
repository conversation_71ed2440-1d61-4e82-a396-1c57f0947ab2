//
//  ASApplyListCell.m
//  JDTAfterSaleModule
//
//  Created by lvchenzhu.1 on 2025/7/8.
//

#import "ASApplyListCell.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAAfterSaleAction.h"

@import JDISVKAIconFontModule;

@interface ASApplyListCell ()

// 主容器
@property (nonatomic, strong) UIView *containerView;

// 店铺信息
@property (nonatomic, strong) UIImageView *shopImgView;
@property (nonatomic, strong) UILabel *shopNameLabel;
@property (nonatomic, strong) UIImageView *arrowImgView;

// 商品信息
@property (nonatomic, strong) UIImageView *productImageView;
@property (nonatomic, strong) UILabel *productNameLabel;
@property (nonatomic, strong) UILabel *quantityLabel;

// 状态提示
@property (nonatomic, strong) UILabel *statusTipLabel;

// 申请按钮
@property (nonatomic, strong) UIButton *applyButton;

// 数据模型
@property (nonatomic, strong) ASApplyListItemModel *model;
@property (nonatomic, strong) ASApplyListItemSkuItemModel *skuItem;

@end

@implementation ASApplyListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
        [self setupConstraints];
    }
    return self;
}

- (void)configUI {
    self.backgroundColor = [UIColor clearColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    // 主容器
    self.containerView = [[UIView alloc] init];
    self.containerView.backgroundColor = [UIColor whiteColor];
    self.containerView.layer.cornerRadius = 4;
    self.containerView.layer.masksToBounds = YES;
    [self.contentView addSubview:self.containerView];

    // 店铺名称
    self.shopImgView = [[UIImageView alloc] init];
    self.shopImgView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C7", JDIF_ICON_SHOP_LINE, CGSizeMake(20, 20));
    [self.containerView addSubview:self.shopImgView];
    
    self.shopNameLabel = [[UILabel alloc] init];
    self.shopNameLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
    self.shopNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    [self.containerView addSubview:self.shopNameLabel];
    
    self.arrowImgView = [[UIImageView alloc] init];
    self.arrowImgView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C7", JDIF_ICON_ARROW_RIGHT_SMALL, CGSizeMake(12, 12));
    [self.containerView addSubview:self.arrowImgView];

    // 商品图片
    self.productImageView = [[UIImageView alloc] init];
    self.productImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.productImageView.layer.cornerRadius = 6;
    self.productImageView.layer.masksToBounds = YES;
    self.productImageView.backgroundColor = [UIColor whiteColor];
    [self.containerView addSubview:self.productImageView];

    // 商品名称
    self.productNameLabel = [[UILabel alloc] init];
    self.productNameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.productNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.productNameLabel.numberOfLines = 0;
    [self.containerView addSubview:self.productNameLabel];

    // 数量标签
    self.quantityLabel = [[UILabel alloc] init];
    self.quantityLabel.font = [UIFont systemFontOfSize:12];
    self.quantityLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    [self.containerView addSubview:self.quantityLabel];

    // 状态提示
    self.statusTipLabel = [[UILabel alloc] init];
    self.statusTipLabel.font = [UIFont systemFontOfSize:12];
    self.statusTipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
    self.statusTipLabel.numberOfLines = 0;
    [self.containerView addSubview:self.statusTipLabel];

    // 申请按钮
    self.applyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.applyButton.titleLabel.font = [UIFont systemFontOfSize:14];
    [self.applyButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.applyButton setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];
    self.applyButton.layer.cornerRadius = 4;
    self.applyButton.layer.masksToBounds = YES;
    [self.applyButton addTarget:self action:@selector(applyButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.containerView addSubview:self.applyButton];
}

- (void)setupConstraints {
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).insets(UIEdgeInsetsMake(8, 8, 8, 8));
    }];

    [self.shopImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.containerView).offset(18);
        make.top.mas_equalTo(self.containerView).offset(18);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];
    
    [self.shopNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.shopImgView);
        make.leading.mas_equalTo(self.shopImgView.mas_trailing).offset(2);
        make.trailing.mas_equalTo(self.arrowImgView.mas_leading).offset(-2);
        make.height.mas_equalTo(20);
    }];
    
    [self.arrowImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.shopNameLabel.mas_trailing).offset(2);
        make.trailing.mas_lessThanOrEqualTo(self.containerView).offset(-18);
        make.centerY.mas_equalTo(self.shopNameLabel);
        make.size.mas_equalTo(CGSizeMake(12, 12));
    }];

    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.shopImgView);
        make.top.mas_equalTo(self.shopNameLabel.mas_bottom).offset(22);
        make.width.height.mas_equalTo(80);
    }];

    [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productImageView);
        make.leading.mas_equalTo(self.productImageView.mas_trailing).offset(12);
        make.trailing.mas_lessThanOrEqualTo(self.containerView).offset(-18);
        make.height.mas_lessThanOrEqualTo(42);
    }];

    [self.quantityLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.shopImgView.mas_bottom).offset(-5);
        make.leading.mas_equalTo(self.productNameLabel);
        make.trailing.mas_equalTo(self.productNameLabel);
        make.height.mas_equalTo(20);
    }];

    [self.statusTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
        make.left.mas_equalTo(self.containerView).offset(12);
        make.right.mas_equalTo(self.containerView).offset(-12);
        make.height.mas_greaterThanOrEqualTo(16);
    }];

    [self.applyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.statusTipLabel.mas_bottom).offset(12);
        make.right.mas_equalTo(self.containerView).offset(-12);
        make.width.mas_equalTo(80);
        make.height.mas_equalTo(32);
        make.bottom.mas_equalTo(self.containerView).offset(-12);
    }];
}

#pragma mark - Public Methods

- (void)configWithModel:(ASApplyListItemModel *)model {
    self.model = model;
    self.skuItem = model.skuList.firstObject;
}

- (void)render:(NSInteger)index {
    if (!self.model || !self.skuItem) {
        return;
    }

    // 店铺名称
    self.shopNameLabel.text = self.model.venderInfo.shopName ?: @"";

    // 商品信息
    [self.productImageView jdcd_setImage:[PlatformService getCompleteImageUrl:self.skuItem.skuImg]
                             placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault]
                             contentMode:UIViewContentModeScaleAspectFit];

    self.productNameLabel.text = self.skuItem.skuName ?: @"";
    self.quantityLabel.text = [NSString stringWithFormat:@"数量 %ld", (long)self.skuItem.skuCount];

    // 状态和按钮配置
    [self configStatusAndButton];
}

- (void)configStatusAndButton {
    if (self.skuItem.canApply) {
        // 可以申请售后
        self.statusTipLabel.text = @"";
        self.statusTipLabel.hidden = YES;

        [self.applyButton setTitle:@"申请售后" forState:UIControlStateNormal];
        [self.applyButton setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];
        self.applyButton.enabled = YES;
    } else {
        // 不能申请售后，显示原因
        self.statusTipLabel.text = self.skuItem.reasonText ?: @"该商品不支持售后";
        self.statusTipLabel.hidden = NO;

        [self.applyButton setTitle:@"申请售后" forState:UIControlStateNormal];
        [self.applyButton setBackgroundColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
        self.applyButton.enabled = NO;
    }
}

#pragma mark - Actions

- (void)applyButtonTapped {
    if (!self.skuItem.canApply) {
        return;
    }

    // 发送申请售后事件
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithClickApplyButton];
    action.sender = self.model;
    [self.delegate sendNext:action];
}

#pragma mark - Lazy Loading

- (RACSubject *)delegate {
    if (!_delegate) {
        _delegate = [RACSubject subject];
    }
    return _delegate;
}

@end
