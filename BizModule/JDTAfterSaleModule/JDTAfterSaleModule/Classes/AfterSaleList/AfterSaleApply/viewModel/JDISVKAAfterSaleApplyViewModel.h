//
//  JDISVKAAfterSaleApplyViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import <Foundation/Foundation.h>

@class RACSubject;
@class RACSignal;
@class afterSaleApplyListOrderDetail;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSaleApplyViewModel : NSObject

@property (nonatomic,copy)NSArray<afterSaleApplyListOrderDetail *> *dataArray;

@property (nonatomic,assign)NSInteger currentPage;

@property (nonatomic,assign)bool isHiddenFooter;

@property (nonatomic,assign)NSInteger errorTypeValue;  //0:空页面 1:加载失败

@property (nonatomic,assign)NSInteger pageCount;

- (RACSignal *)requestInfo;

- (CGFloat)getCurrentCellHeight:(NSInteger)index;

@end

NS_ASSUME_NONNULL_END
