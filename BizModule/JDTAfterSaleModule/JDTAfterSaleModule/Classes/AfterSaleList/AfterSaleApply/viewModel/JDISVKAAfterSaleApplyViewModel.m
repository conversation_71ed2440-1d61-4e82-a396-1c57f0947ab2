//
//  JDISVKAAfterSaleApplyViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleApplyViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import "JDISVKAAfterSaleApplyModel.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAAfterSaleCustomModel.h"

#import "JDISVKAAfterSaleService.h"
@interface JDISVKAAfterSaleApplyViewModel()

@property (nonatomic,copy)NSArray *tempArray;

//是否隐藏到底提示
@property (nonatomic,assign)BOOL isHiddenFullTips;

@end

@implementation JDISVKAAfterSaleApplyViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.currentPage = 1;
        self.isHiddenFooter = true;
    }
    return self;
}


- (CGFloat)getCurrentCellHeight:(NSInteger)index{
    //cell固定高度
    CGFloat fixedHeight = 166 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    afterSaleApplyListOrderDetail *orderDetail = nil;
    if (index >= 0 && index < self.dataArray.count) {
        orderDetail = self.dataArray[index];
        if (orderDetail.canApply == NO) {
            if (orderDetail.reasonText) {
                CGSize reasonSize = [orderDetail.reasonText jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width - 88 - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
                fixedHeight = fixedHeight + 24 + reasonSize.height ;
            }
        } else {
            fixedHeight = fixedHeight + 30;
        }
    } else {
        fixedHeight = fixedHeight + 30;
    }
    return fixedHeight;
}

- (void)configDataArray{
    if (![self.tempArray yy_modelIsEqual:[NSNull null]]) {
        for (NSDictionary *order in self.tempArray) {
            afterSaleApplyListOrder *orderModel = [afterSaleApplyListOrder yy_modelWithDictionary:order];
            for (NSDictionary *detailOrder in orderModel.skuList) {
                NSMutableArray *mutableArray = [NSMutableArray arrayWithArray:self.dataArray];
                afterSaleApplyListOrderDetail *tempOrder = [afterSaleApplyListOrderDetail yy_modelWithDictionary:detailOrder];
                tempOrder.orderId = orderModel.orderId;
                [mutableArray addObject:tempOrder];
                self.dataArray = [mutableArray copy];
            }
        }
    }
    self.isHiddenFooter = self.dataArray.count >= 4 ? NO : YES;
    
}

- (RACSignal *)requestInfo{
    NSDictionary *parameters = @{@"pageSize":@10,@"page":@(self.currentPage)};
    JDWeakSelf
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[JDISVKAAfterSaleService shareService] requestAfterSaleApplyListWithParameters:parameters complete:^(NSDictionary * _Nonnull response) {
            JDStrongSelf
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:response];
            
            if (customModel.success) {
                NSDictionary *data = customModel.data;
                if (!([data[@"result"] isEqual:[NSNull null]] || data[@"result"] == nil)) {
                    self.tempArray = data[@"result"];
                    self.isHiddenFooter = self.tempArray.count >= 4 ? NO : YES;
                    
                    if (!([data[@"pageCount"] isEqual:[NSNull null]] || data[@"pageCount"] == nil)) {
                        self.pageCount = [data[@"pageCount"] intValue];
                    }else{
                        self.pageCount = 1;
                    }
                    [self configDataArray];
                    [subscriber sendCompleted];
                }else{
                    self.errorTypeValue = 0;
                    [subscriber sendError:nil];
                }
            }else{
                self.errorTypeValue = 1;
                [subscriber sendError:nil];
            }
            
        }];
        return nil;
    }];
    return signal;

}


@end
