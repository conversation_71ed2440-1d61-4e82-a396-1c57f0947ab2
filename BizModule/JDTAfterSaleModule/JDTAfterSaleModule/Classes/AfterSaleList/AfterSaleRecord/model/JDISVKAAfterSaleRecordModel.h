//
//  JDISVKAAfterSaleRecordModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfterSaleRecordModel : NSObject

@property (nonatomic,assign)CGFloat cellHeight;

@property (nonatomic,copy)NSString *skuNumber;

@property (nonatomic,copy)NSString *skuName;

@property (nonatomic,copy)NSString *skuImage;

@property (nonatomic,copy)NSString *skuCount;

@property (nonatomic,copy)NSString *afterSalesType;

@property (nonatomic,copy)NSString *applyStatus;

@property (nonatomic,copy)NSString *applyDetailReason;

@property (nonatomic,assign)CGFloat applyDetailHeight;

@end

@interface AfsRecordSkuDetailVo : NSObject

@property (nonatomic,assign)NSInteger skuId;

@property (nonatomic,copy)NSString *skuName;

@property (nonatomic,copy)NSString *skuImg;

@property (nonatomic,strong)NSNumber *skuPrice;

@property (nonatomic,copy)NSArray *serviceTypeList;

@property (nonatomic,copy)NSArray *refundTypeList;

@property (nonatomic,copy)NSArray *returnModelList;


/*
 自定义新增
 */

@property (nonatomic,copy)NSString *afsServiceId; //服务单号

@property (nonatomic,copy)NSString *afsServiceStatusText; //服务单状态文案

@property (nonatomic,copy)NSString *afsServiceStateDesc; //操作内容

@property (nonatomic,assign)NSInteger skuCount;  //已申请服务单数量

@property (nonatomic,assign)NSInteger appliedCount;

@property (nonatomic,copy)NSString *customerExpectStr; //

@property (nonatomic,assign)NSInteger customerExpect;

@property (nonatomic,copy)NSString *afsServiceStepName;  //服务节点名称

@end


@interface AfterSaleRecordModel : NSObject

@property (nonatomic,copy)NSString *afsServiceId; //服务单号

@property (nonatomic,assign)NSInteger orderId; //订单号

@property (nonatomic,assign)NSInteger skuId;  //商品编号

@property (nonatomic,copy)NSString *skuName; //商品名称

@property (nonatomic,assign)NSInteger returnType;  //返件方式 1=客户发货，2=上门取件

@property (nonatomic,copy)NSString *refundType; //退款方式名称

@property (nonatomic,strong)NSDate *applyDate;  //申请时间

@property (nonatomic,strong)NSDate *currentDate;  //当前时间

@property (nonatomic,strong)NSDate *updateTime;  //状态更新时间

@property (nonatomic,assign)NSInteger customerExpect; //

@property (nonatomic,copy)NSString *customerExpectStr; //

/*
 服务单状态
 1=申请，10=待审核，15=待客户返件，
 20=待拆包，30=待处理，40=待维修，50=待退款，60=待换新，70=待发货，
 200=已完成，210=已拒绝，220=已取消
 -1=未知状态
 */
@property (nonatomic,assign)NSInteger afsServiceStatus;

@property (nonatomic,copy)NSString *afsServiceStatusText; //服务单状态文案

@property (nonatomic,strong)NSDictionary *afsServiceOperateMap; //服务单操作类型

@property (nonatomic,assign)NSInteger skuCount;  //已申请服务单数量

@property (nonatomic,copy)NSString *imgPath; //图片地址

@property (nonatomic,copy)NSString *afsServiceStateDesc; //操作内容

@property (nonatomic,copy)NSString *afsServiceStepName;  //服务节点名称

@property (nonatomic,copy)NSString *afsServiceStatusAppText; //app显示状态

/*
 审核结果
 101=客户发货,102=取消审核,103=审核不通过,
 104=上门取件,108=退款不退货,112=缺件补发,
 114=闪电退款
 */
@property (nonatomic,copy)NSString *auditResult;
/*
 处理结果
 301=退货，302=换货，303=维修，304=原返，
 305=待反馈，306=换货审核，307=强制关单
 */
@property (nonatomic,assign)NSInteger processResult;

@property (nonatomic,strong)NSDate *auditTime;  //审核时间

@property (nonatomic,copy) NSArray<AfsRecordSkuDetailVo *> *skuList;

@end


@interface AfterSaleRecordListPageVo : NSObject

@property (nonatomic,assign)NSInteger totalCount; //当前页

@property (nonatomic,assign)NSInteger currentPage; //总记录条数

@property (nonatomic,assign)NSInteger pageSize;  //总页数

@property (nonatomic,copy)NSString *unit;  //单位 条

@property (nonatomic,copy) NSArray<AfterSaleRecordModel *> *data;


@end

NS_ASSUME_NONNULL_END
