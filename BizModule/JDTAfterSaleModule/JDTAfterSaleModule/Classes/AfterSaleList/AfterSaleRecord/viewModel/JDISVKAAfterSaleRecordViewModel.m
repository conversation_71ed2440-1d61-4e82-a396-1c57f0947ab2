//
//  JDISVKAAfterSaleRecordViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleRecordViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAAfterSaleRecordModel.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAAfterSaleCustomModel.h"

#import "JDISVKAAfterSaleService.h"
@interface JDISVKAAfterSaleRecordViewModel()

@property (nonatomic,copy)NSArray *tempArray;

@end

@implementation JDISVKAAfterSaleRecordViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.currentPage = 1;
        self.isHiddenFooter = true;
    }
    return self;
}

- (CGFloat)getCurrentCellHeight:(NSInteger)index{
    AfsRecordSkuDetailVo *recordDetail = self.dataArray[index];
    CGFloat fixedHeight = 166 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    if (recordDetail.afsServiceStateDesc) {
        if (recordDetail.afsServiceStateDesc.length > 0) {
            CGSize afsServiceStateDescSize = [recordDetail.afsServiceStateDesc jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width - 100 - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
            fixedHeight = fixedHeight + 48 + afsServiceStateDescSize.height;
        }
    }
    return fixedHeight;
}

- (void)configDataArray{
    if (![self.tempArray yy_modelIsEqual:[NSNull null]]) {
        for (NSDictionary *recordDic in self.tempArray) {
            AfterSaleRecordModel *record = [AfterSaleRecordModel yy_modelWithDictionary:recordDic];
            for (NSDictionary *detailRecord in record.skuList) {
                NSMutableArray *mutableArray = [NSMutableArray arrayWithArray:self.dataArray];
                AfsRecordSkuDetailVo *tempDetailRecord = [AfsRecordSkuDetailVo yy_modelWithDictionary:detailRecord];
                tempDetailRecord.afsServiceId = record.afsServiceId;
                tempDetailRecord.afsServiceStatusText = record.afsServiceStatusText;
                tempDetailRecord.afsServiceStateDesc = record.afsServiceStateDesc;
                tempDetailRecord.customerExpectStr = record.customerExpectStr;
                tempDetailRecord.customerExpect = record.customerExpect;
                tempDetailRecord.afsServiceStepName = record.afsServiceStepName;
                [mutableArray addObject:tempDetailRecord];
                self.dataArray = [mutableArray copy];
            }
        }
    }
    
}

- (RACSignal *)requestInfo{
    NSDictionary *parameters = @{@"pageSize":@10,@"page":@(self.currentPage)};
    JDWeakSelf
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[JDISVKAAfterSaleService shareService] requestAfterSaleRecordListWithParameters:parameters complete:^(NSDictionary * _Nonnull response) {
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:response];
            JDStrongSelf
            if (customModel.success) {
                NSDictionary *data = customModel.data;
                if (!([data[@"result"] isEqual:[NSNull null]] || data[@"result"] == nil)) {
                    self.tempArray = data[@"result"];
                    self.isHiddenFooter = self.tempArray.count >= 4 ? NO : YES;
                    if (!([data[@"pageCount"] isEqual:[NSNull null]] || data[@"pageCount"] == nil)) {
                        self.pageCount = [data[@"pageCount"] intValue];
                    }else{
                        self.pageCount = 1;
                    }
                    [self configDataArray];
                    [subscriber sendCompleted];
                }else{
                    self.errorTypeValue = 0;
                    [subscriber sendError:nil];
                }
            }else{
                self.errorTypeValue = 1;
                [subscriber sendError:nil];
            }
        }];
        return nil;
    }];
    return signal;
}

- (NSArray *)dataArray{
    if (!_dataArray) {
        _dataArray = [[NSArray alloc] init];
    }
    return _dataArray;
}


@end
