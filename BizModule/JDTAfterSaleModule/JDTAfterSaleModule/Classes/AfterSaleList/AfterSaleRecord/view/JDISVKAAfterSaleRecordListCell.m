//
//  JDISVKAAfterSaleRecordListCell.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleRecordListCell.h"
#import "JDISVKAAfterSaleUtils.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAAfterSaleRecordModel.h"
#import "JDISVKAAfterSaleAction.h"
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
@interface JDISVKAAfterSaleRecordListCell()

@property (weak, nonatomic) IBOutlet UIView *mainView;

@property (weak, nonatomic) IBOutlet UILabel *serverLabel;

@property (weak, nonatomic) IBOutlet UILabel *serverValue;

@property (weak, nonatomic) IBOutlet UILabel *returnTypeLabel;

@property (weak, nonatomic) IBOutlet UIImageView *returnTypeImageView;

@property (weak, nonatomic) IBOutlet UIImageView *commodityImageView;

@property (weak, nonatomic) IBOutlet UILabel *commodityName;

@property (weak, nonatomic) IBOutlet UILabel *commodityCountLabel;

@property (weak, nonatomic) IBOutlet UILabel *commodityCountValue;

@property (weak, nonatomic) IBOutlet UIView *applyDetailView;

@property (weak, nonatomic) IBOutlet UILabel *applyDetailLabel;
@property (weak, nonatomic) IBOutlet UILabel *applyDetailValue;//(暂时移除)
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImageView;

@property (nonatomic,strong)AfsRecordSkuDetailVo *model;
#pragma mark 约束

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;
@end

@implementation JDISVKAAfterSaleRecordListCell

- (void)awakeFromNib{
    [super awakeFromNib];
    [self setCell];
}

- (void)setCell{
    [self configCellColorAndFont];
}


- (void)config:(AfsRecordSkuDetailVo *)model{
    self.model = model;
}

- (void)render:(NSInteger)index{
    [self.applyDetailView jd_addTapAction:@selector(clickApplyDetail) withTarget:self];
    
    self.serverValue.text = self.model.afsServiceId;
    self.commodityName.text = self.model.skuName;
    self.commodityCountValue.text = [NSString stringWithFormat:@"%ld",self.model.appliedCount];
    [self.commodityImageView jdcd_setImage:[PlatformService getCompleteImageUrl:self.model.skuImg] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    if (self.model.customerExpect == 10) {
//        self.returnTypeLabel.text = Lang(@"after_sale_return");
        self.returnTypeLabel.text = self.model.customerExpectStr;
        self.returnTypeImageView.image = [UIImage ka_iconWithName:JDIF_ICON_REFUND imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    }else{
//        self.returnTypeLabel.text = Lang(@"after_sale_exchange");
        self.returnTypeLabel.text = self.model.customerExpectStr;
        self.returnTypeImageView.image = [UIImage ka_iconWithName:JDIF_ICON_EXCHANGE imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    }
    self.returnTypeImageView.hidden = YES;
    if (self.model.afsServiceStatusText && self.model.afsServiceStatusText.length > 0) {
        [self.applyDetailView setHidden:NO];
        self.applyDetailLabel.text = self.model.afsServiceStatusText;
        self.applyDetailValue.text = self.model.afsServiceStatusText;
    }else{
        [self.applyDetailView setHidden:YES];
    }
}

- (void)configCellColorAndFont{
    self.backgroundColor = [UIColor clearColor];
    self.serverLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightSemibold);
    self.serverLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.serverLabel.text = Lang(@"after_sale_detail_service_id");
    self.serverValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.serverValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.returnTypeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightMedium);
    self.returnTypeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.returnTypeLabel.text = Lang(@"after_sale_return_title");
    self.commodityName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityCountLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityCountLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.commodityCountValue.jdisv_fontPicker = JDISVJDFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.commodityCountValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.commodityCountLabel.text = Lang(@"after_sale_count");
    
    self.applyDetailLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyDetailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.applyDetailValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyDetailValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.applyDetailView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.rightArrowImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
//    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
//        self.rightArrowImageView.image = [[UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(10, 10) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]] JDCDRTL];
//    }
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.applyDetailView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.commodityImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.commodityImageView.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"] colorWithAlphaComponent:0.02];
}

- (void)clickApplyDetail{
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithClickRecordApplyDetailView];
    action.sender = self.model.afsServiceId;
    [self.delegate sendNext:action];
}


- (RACSubject *)delegate{
    if (!_delegate) {
        _delegate = [[RACSubject alloc] init];
    }
    return _delegate;
}

@end
