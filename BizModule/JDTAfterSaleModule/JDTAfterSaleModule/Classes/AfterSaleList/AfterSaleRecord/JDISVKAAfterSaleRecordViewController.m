//
//  JDISVKAAfterSaleRecordViewController.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleRecordViewController.h"
#import "UIWindow+afterSaleSafeArea.h"
#import "JDISVKAAfterSaleRecordListCell.h"
#import "JDISVKAAfterSaleUtils.h"
#import <JDISVThemeModule/JDISVThemeColor.h>

#import <JDISVMJRefreshModule/JDISVMJRefreshModule-umbrella.h>
#import <JDISVCategoryModule/NSObject+JDCDExtend.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAAfterSaleAction.h"
#import "JDISVKAAfterSaleDetailViewController.h"

#import "JDISVKAAfterSaleRecordViewModel.h"

#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
@interface JDISVKAAfterSaleRecordViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic,strong)JDISVKAAfterSaleRecordViewModel *viewModel;

//@property (nonatomic,strong)UIImageView *skeletonImageView;

@property (nonatomic,strong)KAEmptyView *errorView;

@end

@implementation JDISVKAAfterSaleRecordViewController

- (void)viewDidLoad {
    
    [super viewDidLoad];

    [self configTableView];

}

- (void)requestData{
    
//    if (self.viewModel.dataArray.count <= 0) {
//        self.skeletonImageView = [[UIImageView alloc] initWithImage:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypeSkeletonAfterSalesApplication]];
//        [self.tableView addSubview:self.skeletonImageView];
//        [self.skeletonImageView mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.leading.top.mas_equalTo(self.view);
//            make.height.mas_equalTo(self.view);
//            make.width.mas_equalTo(self.view.frame.size.width);
//        }];
//    }
    [PlatformService dismissInView:self.view];
    [PlatformService showLoadingInView:self.view];
    
    self.viewModel.currentPage = 1;
    self.viewModel.dataArray = @[];
    [self.tableView reloadData];
    [self.tableView.mj_header setHidden:YES];
    [self.tableView.mj_footer setHidden:YES];
    JDWeakSelf
    [[self.viewModel requestInfo] subscribeError:^(NSError * _Nullable error) {
        JDStrongSelf
        [PlatformService dismissInView:self.view];
//        [self.skeletonImageView removeFromSuperview];
        [self.tableView.mj_footer setHidden:self.viewModel.isHiddenFooter];
        
        if (self.viewModel.errorTypeValue == 0) {
            [self configEmptyView];
        }else{
            [self configErroView];
        }
        
    } completed:^{
        JDStrongSelf
        if (self.errorView) {
            [self.errorView removeFromSuperview];
        }
        [PlatformService dismissInView:self.view];
//        [self.skeletonImageView removeFromSuperview];
        [self.tableView.mj_header setHidden:NO];
        [self.tableView.mj_footer setHidden:self.viewModel.isHiddenFooter];
        [self.tableView reloadData];
        
        if (self.viewModel.currentPage >= self.viewModel.pageCount) {
            self.tableView.mj_footer.state = MJRefreshStateNoMoreData;
        }else{
            self.tableView.mj_footer.state = MJRefreshStateIdle;
        }
        
        if (self.viewModel.dataArray.count <= 0) {
            [self configEmptyView];
        }
    }];
}

- (void)configEmptyView{
    if (self.errorView) {
        [self.errorView removeFromSuperview];
    }
    [self.tableView.mj_footer setHidden:YES];
    self.errorView = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNotAction];
    self.errorView.offSetY = - [UIWindow ka_uikit_navigationHeight];
    self.errorView.decrible = Lang(@"after_sale_list_record_empty");
    self.errorView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    self.errorView.coverImage = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypeNoDataAfterSalesRecord];
    [self.view addSubview:self.errorView];
    [self.errorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.mas_equalTo(self.view);
        make.height.mas_equalTo(self.tableView);
    }];
}

- (void)configErroView{
    if (self.errorView) {
        [self.errorView removeFromSuperview];
    }
    [self.tableView.mj_footer setHidden:YES];
    [self.tableView reloadData];
    self.errorView = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNormal];
    self.errorView.offSetY = - [UIWindow ka_uikit_navigationHeight];
    self.errorView.decrible = Lang(@"after_sale_load_error");
    self.errorView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    self.errorView.coverImage = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypeFailToLoad];
    JDWeakSelf
    [self.errorView addActionButtonWithTitle:Lang(@"after_sale_retry") render:^(UIButton * _Nonnull sender) {
        [sender renderB3];
    } action:^(UIButton * _Nonnull sender) {
        JDStrongSelf
        [self requestData];
    }];
    [self.view addSubview:self.errorView];
    [self.errorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.mas_equalTo(self.view);
        make.height.mas_equalTo(self.tableView);
    }];
}

- (void)eventWithActionType:(JDISVKAAfterSaleAction *)action{
    if (action.actionType == JDISVKAAfterSaleActionTypeWithClickRecordApplyDetailView) {
        JDISVKAAfterSaleDetailViewController *vc = [[JDISVKAAfterSaleDetailViewController alloc] init];
        vc.afsServiceId = action.sender;
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
    }
}

- (void)configTableView{
    [self.view addSubview:self.tableView];
    JDWeakSelf
    self.tableView.mj_header = [PlatformService getMJRefreshHeader:^{
        JDStrongSelf
        self.viewModel.currentPage = 1;
        self.viewModel.dataArray = @[];
        [[self.viewModel requestInfo] subscribeError:^(NSError * _Nullable error) {
            [self.tableView.mj_header endRefreshing];
            if (self.viewModel.errorTypeValue == 0) {
                [self configEmptyView];
            }else{
                [self configErroView];
            }
            [self.tableView reloadData];
        } completed:^{
            if (self.errorView) {
                [self.errorView removeFromSuperview];
            }
            [self.tableView.mj_header endRefreshing];
            [self.tableView reloadData];
            if (self.viewModel.currentPage >= self.viewModel.pageCount) {
                self.tableView.mj_footer.state = MJRefreshStateNoMoreData;
            }else{
                self.tableView.mj_footer.state = MJRefreshStateIdle;
            }
            
            if (self.viewModel.dataArray.count <= 0) {
                [self configEmptyView];
            }
        }];
    }];
    
    self.tableView.mj_footer = [PlatformService getMJRefreshAutoFooter:^{
        self.viewModel.currentPage += 1;
        [[self.viewModel requestInfo] subscribeError:^(NSError * _Nullable error) {
        self.viewModel.currentPage -= 1;
        [PlatformService setMJRefreshFooterFailureStatue:self.tableView.mj_footer];
        } completed:^{
            [PlatformService resetMJRefreshFooterFailureState:self.tableView.mj_footer];
            [self.tableView reloadData];
            if (self.viewModel.currentPage >= self.viewModel.pageCount) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }else{
                [self.tableView.mj_footer endRefreshing];
            }
        }];
    }];
    [self.tableView.mj_footer setHidden:YES];
    [self.tableView.mj_header setHidden:YES];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    self.tableView.frame = self.view.bounds;
}


#pragma mark - UITableViewDataSource, UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVKAAfterSaleRecordListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVKAAfterSaleRecordListCell" forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell config:self.viewModel.dataArray[indexPath.row]];
    JDWeakSelf
    [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(JDISVKAAfterSaleAction *action) {
        JDStrongSelf
        [self eventWithActionType:action];
    }];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [self.viewModel getCurrentCellHeight:indexPath.row];
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVKAAfterSaleRecordListCell *listCell = (JDISVKAAfterSaleRecordListCell *)cell;
    [listCell render:indexPath.row];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

- (void)dealloc {
    NSLog(@"JDISVKAAfterSaleApplyViewController dealloced");
}

#pragma mark - JXPagingViewListViewDelegate

- (UIView *)listView {
    return self.view;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *))callback {
    self.scrollCallback = callback;
}

- (UITableView *)tableView{
    if (!_tableView) {
        _tableView = [[UITableView alloc] init];
        _tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
        _tableView.tableFooterView = [UIView new];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.estimatedRowHeight = 0;
        _tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVKAAfterSaleRecordListCell" bundle:JDISVKAAfterSaleModuleBunle()] forCellReuseIdentifier:@"JDISVKAAfterSaleRecordListCell"];
        //列表的contentInsetAdjustmentBehavior失效，需要自己设置底部inset
        _tableView.contentInset = UIEdgeInsetsMake(0, 0, UIApplication.sharedApplication.keyWindow.afterSale_layoutInsets.bottom, 0);
    }
    return _tableView;
}

- (JDISVKAAfterSaleRecordViewModel *)viewModel{
    if (!_viewModel) {
        _viewModel = [[JDISVKAAfterSaleRecordViewModel alloc] init];
    }
    return _viewModel;
}

@end

