//
//  JDISVKAAfterSaleListRefreshViewController.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleListRefreshViewController.h"
#import <JDISVPagerViewModule/JXCategoryView.h>
#import <JDISVPagerViewModule/JXPagerListRefreshView.h>
#import "JDISVAfterTracker.h"

@interface JDISVKAAfterSaleListRefreshViewController ()

@end

@implementation JDISVKAAfterSaleListRefreshViewController

-(void)viewDidLoad{
    [super viewDidLoad];

    [JDISVAfterTracker PV:@"Repair_Service"
                    param:@{@"page_id":@"Repair_Service"}];
    
}
- (JXPagerView *)preferredPagingView {
    return [[JXPagerListRefreshView alloc] initWithDelegate:self];
}

//#pragma mark - JXCategoryViewDelegate
//
//- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
//    self.navigationController.interactivePopGestureRecognizer.enabled = (index == 0);
//}

@end
