//
//  JDISVKAAfterSaleModule.m
//
//
// 组件输出类, 可引入JDRouter组件, 进行组件间通信

#import <Foundation/Foundation.h>
#import "JDISVKAAfterSaleModule.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import "JDISVKAAfterSaleListRefreshViewController.h"

#import "JDISVKAAfterSaleDetailViewController.h"
#import <JDISVPagerViewModule/JXCategoryTitleView.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
@implementation JDISVKAAfterSaleModule

/*
例:
输出一个JDISVKAAfterSaleModule组件exportInterface接口, 查看JDRouter.h头文件方法说明
JDROUTER_EXTERN_METHOD(JDISVKAAfterSaleModule, exportInterface, arg, callback) {
        
    return nil;
}
*/
JDROUTER_EXTERN_METHOD(JDISVKAAfterSaleModule, getAfterSaleApplyListViewController, arg, callback) {
#if DEBUG
    JDISVKAAfterSaleListRefreshViewController *applyListViewController = [[JDISVKAAfterSaleListRefreshViewController alloc] init];
    applyListViewController.hidesBottomBarWhenPushed = true;
    applyListViewController.shouldHiddenNavigationBar = [arg[@"shouldHiddenNavigationBar"] boolValue];
    if (callback) {
        callback(applyListViewController);
    }
#else
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeLogin];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin",moduleName];
    [JDRouter openURL:router
    arg:nil
    error:nil
    completion:^(NSNumber* success){
        if(success.boolValue){
            JDISVKAAfterSaleListRefreshViewController *applyListViewController = [[JDISVKAAfterSaleListRefreshViewController alloc] init];
            applyListViewController.hidesBottomBarWhenPushed = true;
            applyListViewController.shouldHiddenNavigationBar = [arg[@"shouldHiddenNavigationBar"] boolValue];
            if (callback) {
                callback(applyListViewController);
            }
        }else {
            if (callback) {
                callback(nil);
            }
        }
    }];
#endif
    
    return nil;
}
    
JDROUTER_EXTERN_METHOD(JDISVKAAfterSaleModule, getAfterSaleRecordListViewController, arg, callback) {
#if DEBUG
    JDISVKAAfterSaleListRefreshViewController *applyRecordViewController = [[JDISVKAAfterSaleListRefreshViewController alloc] init];
    applyRecordViewController.categoryIndex = 1;
    applyRecordViewController.categoryView.defaultSelectedIndex = 1;
    applyRecordViewController.hidesBottomBarWhenPushed = true;
    if (callback) {
        callback(applyRecordViewController);
    }
#else
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeLogin];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin",moduleName];
    [JDRouter openURL:router
    arg:nil
    error:nil
    completion:^(NSNumber* success){
        if(success.boolValue){
            JDISVKAAfterSaleListRefreshViewController *applyRecordViewController = [[JDISVKAAfterSaleListRefreshViewController alloc] init];
            applyRecordViewController.categoryIndex = 1;
            applyRecordViewController.categoryView.defaultSelectedIndex = 1;
            applyRecordViewController.hidesBottomBarWhenPushed = true;
            if (callback) {
                callback(applyRecordViewController);
            }
        }else {
            if (callback) {
                callback(nil);
            }
        }
    }];
#endif
   
    return nil;
}

JDROUTER_EXTERN_METHOD(JDISVKAAfterSaleModule, getAfterSalesDetailViewController, arg, callback) {
#if DEBUG
    if (arg[@"afsServiceId"]) {
        JDISVKAAfterSaleDetailViewController *vc = [[JDISVKAAfterSaleDetailViewController alloc] init];
        vc.afsServiceId = arg[@"afsServiceId"];
        vc.hidesBottomBarWhenPushed = YES;
        if (callback) {
            callback(vc);
        }
    }else{
        if (callback) {
            callback(nil);
        }
    }
#else
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeLogin];
    NSString *router = [NSString stringWithFormat:@"router://%@/excuteAfterLogin",moduleName];
    [JDRouter openURL:router
    arg:nil
    error:nil
    completion:^(NSNumber* success){
        if(success.boolValue){
            if (arg[@"afsServiceId"]) {
                JDISVKAAfterSaleDetailViewController *vc = [[JDISVKAAfterSaleDetailViewController alloc] init];
                vc.afsServiceId = arg[@"afsServiceId"];
                vc.hidesBottomBarWhenPushed = YES;
                if (callback) {
                    callback(vc);
                }
            }else{
                if (callback) {
                    callback(nil);
                }
            }
        }else {
            if (callback) {
                callback(nil);
            }
        }
    }];
#endif
    
    return nil;
    
}
    
@end
