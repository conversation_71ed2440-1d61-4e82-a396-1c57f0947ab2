//
//  JDISVKADetailOrderInfoModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/21.
//

#import <Foundation/Foundation.h>
#import "JDISVKAApplyDetailSubmitModel.h"
#import "JDISVKAApplyDetailMainModel.h"
#import "JDISVLogisticData.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAAfsDetailVenderInfo : NSObject

@property(nonatomic,assign)NSInteger venderId;
@property(nonatomic,assign)NSInteger shopId;

@property(nonatomic,copy)NSString *venderName;

@property(nonatomic,assign)bool displayChatBtn;

@end

@interface JDISVKAAfsStoreInfo : NSObject

@property(nonatomic,assign)NSInteger storeId;

@property(nonatomic,copy)NSString *storeName;

@property(nonatomic,copy)NSString *phone;

@property(nonatomic,copy)NSString *storeAddress;

@property(nonatomic,copy)NSString *businessTime;

@end

@interface JDISVKAAfsAddressInfo : NSObject

@property(nonatomic,assign)NSInteger addressId;

@property(nonatomic,assign)NSInteger provinceId;

@property(nonatomic,copy)NSString *address;

@end

@interface JDISVKAAfsContactInfoVo : NSObject

@property(nonatomic,copy)NSString *contactName;

@property(nonatomic,copy)NSString *contactTel;

@property(nonatomic,copy)NSString *expressNo;
@property(nonatomic,copy)NSArray<applyDetailAfsExpressInfoVo*> *expressInfos;//快递单号数组<applyDetailAfsExpressInfoVo>

@property(nonatomic,copy)NSString *expressCompany;

@property(nonatomic,strong)JDISVKAAfsAddressInfo *addressInfo;

@end

@interface JDISVKAAfsGeneralTypeVo : NSObject

@property(nonatomic,assign)NSInteger type;

@property(nonatomic,copy)NSString *text;

@end

@interface JDISVKAAfsSkuDetailVo : NSObject

@property(nonatomic,strong)NSNumber *skuSingleShouldPrice;

@property(nonatomic,assign)NSInteger skuCount;

@property(nonatomic,assign)NSInteger appliedCount;

@property(nonatomic,assign)NSInteger canApplyCount;

@property(nonatomic,copy)NSString *skuImg;

@property(nonatomic,assign) NSInteger skuId;

@property(nonatomic,copy)NSString *skuName;

@property(nonatomic,copy)NSArray<JDISVKAAfsGeneralTypeVo*> *refundTypeList;

@property(nonatomic,copy)NSArray<JDISVKAAfsGeneralTypeVo*> *returnModelList;

@end

@interface JDISVKAAfsServiceDetailVo : NSObject

@property(nonatomic,assign)NSInteger orderId;

@property(nonatomic,assign)NSInteger afsServiceId;

@property(nonatomic,assign)BOOL orderSelf;//是否自营订单
@property(nonatomic,assign)NSInteger orderType;

@property(nonatomic,copy)NSArray<JDISVKAAfsSkuDetailVo*> *skuList;

@property (nonatomic,copy)NSString *customerExpectStr;

@property (nonatomic,assign)NSInteger customerExpect; //20 换新  10 退货
/**>
 * Customer provided pictures of defective products
 * 123.jpg,456.jpg
 */
@property (nonatomic,copy)NSString *questionPic;

/*
 申请阶段 10
 APPLY(10)
 客服审核
 AUDIT_JD(21)
 商家审核 22
 AUDIT_POP(22)
 审核不通过 20
 AUDIT_FAIL(20)
 京东收货 31
 RECEIVE_JD(31)
 京东处理 33
 PROCESS_JD(33)
 商家处理 34
 PROCESS_POP(34)
 商家收货 状态废弃 32
 RECEIVE_POP(32)
 待用户确认 40
 CONFIRM(40)
 完成 50
 COMPLETE(50)
 取消 60
 CANCEL(60)
 /OP催收环节 35
 RECEIVABLE_POP(35)
 */
@property(nonatomic,assign)NSInteger afsServiceStep;

@property (nonatomic,copy)NSString *afsServiceStepName;

@property(nonatomic,assign)NSInteger afsServiceStatus;

@property (nonatomic,copy)NSString *afsServiceStatusText;

@property(nonatomic,assign)NSInteger refundType;//20, "direct_return", 30,"not_20"

@property (nonatomic,copy)NSString *refundTypeStr;

@property (nonatomic,copy)NSString *bankName;
@property (nonatomic,copy)NSString *bankCode;
@property (nonatomic,copy)NSString *bankAccountHolder;
@property (nonatomic,copy)NSString *bankTel;

@property (nonatomic,strong)NSNumber *refundAmount;

@property (nonatomic,copy)NSString *refundAmountStr;

@property (nonatomic,strong)NSNumber *applyDate;

@property(nonatomic,assign)NSInteger returnModel;   //40:客户发货 72:上门取件 70：门店自提

@property (nonatomic,copy)NSString *returnModelStr;

@property (nonatomic,strong)JDISVApplyExpandInfoReq *afsApplyExpandInfoVo;

@property (nonatomic,strong)applyDetailAfsContactInfoVo *pickCustomerInfo;

@property(nonatomic,assign)NSInteger pickStatus;   //取件状态：1待取件、 2已取件

@property (nonatomic,copy)NSString *pickStatusStr;//取件状态展示：待取件、已取件。支持国际化


@property(nonatomic,assign)bool allowExpressNoInput;

@property(copy,nonatomic) NSString* reasonId;

@property(copy,nonatomic) NSString* reasonName;

@property(nonatomic,strong)JDISVKAAfsDetailVenderInfo *venderInfo;

@property(nonatomic,strong)JDISVKAAfsStoreInfo *storeInfo;

//客户推后给商家时，商家联系方式
@property(nonatomic,strong)JDISVKAAfsContactInfoVo *returnVenderInfo;

//商家发货给客户时，客户联系方式
@property(nonatomic,strong)JDISVKAAfsContactInfoVo *returnCustomerInfo;

@property (nonatomic,copy)NSString *questionDesc;

@property(nonatomic,assign)bool allowCancel;

@end

@interface JDISVKAAAfsServiceProcessNodeStatusVo : NSObject

@property (nonatomic,assign)NSInteger order;

@property (nonatomic,assign)NSInteger type;

@property (nonatomic,copy)NSString *text;  //服务单状态节点名词

@end

@interface JDISVKAAfsServiceProcessNodeVo : NSObject

@property (nonatomic,assign)NSInteger afsServiceId; //服务单号

@property (nonatomic,assign)NSInteger currentIndex; //当前节点索引

@property (nonatomic,assign)NSInteger nodeCode; //当前服务单节点

@property (nonatomic,copy)NSString *nodeCodeI18nKey;  //服务单状态节点名词

@property (nonatomic,copy)NSString *nodeContentI18nKey;  //服务单状态节点文案

@property (nonatomic,strong)NSArray <JDISVKAAAfsServiceProcessNodeStatusVo *> *nodeStatusList; //进度条时间节点

@end

@interface JDISVKADetailOrderInfoModel : NSObject

@property(nonatomic,strong)JDISVKAAfsServiceProcessNodeVo *afsServiceProcessNodeVo;

@property(nonatomic,strong)JDISVKAAfsServiceDetailVo *afsServiceDetailVo;

@end

NS_ASSUME_NONNULL_END
