//
//  JDISVKAAfterSaleDeliverView.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2022/11/14.
//

#import "JDISVKAAfterSaleDeliverView.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVYYModelModule/YYModel.h>
#define kJDISVSaveCarrierInfo @"kJDISVSaveCarrierInfo"

@interface JDISVKAAfterSaleDeliverView()<UITextFieldDelegate,UITableViewDelegate,UITableViewDataSource>{
    
}
@property (nonatomic, strong) UITextField* deliveryCampany;
@property (nonatomic, strong) UITextField* deliveryId;

@property (nonatomic, strong) UIView* tableShadowView;
@property (nonatomic, strong) UITableView* tableView;

@property (nonatomic, strong) NSArray* datas;
@property (nonatomic, strong) NSArray* filterDatas;

@property (nonatomic, strong) UIView * noDataView;
@property (nonatomic, strong) UILabel* noDataLabel;
@property (nonatomic, assign) CGRect originalRect;

//id,name,type
//承运商类别 (1, "快递承运商"),(2, "大件承运商"),(3, "家装服务商"),(4, "冷链承运商"),(5, "国际承运商"),(6, "商家专属"),(7, "落地配"),(100, "其他")
@property(nonatomic, strong) NSDictionary* selectCarrier;
@end

@implementation JDISVKAAfterSaleDeliverView
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        [self setupUI];
    }
    return self;
}

-(void)setupUI{
    self.deliveryCampany = [[UITextField alloc] init];
    self.deliveryCampany.delegate = self;
    [self addSubview:self.deliveryCampany];
    UIView *paddingView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 16, 20)];
    self.deliveryCampany.leftView = paddingView;
    self.deliveryCampany.leftViewMode = UITextFieldViewModeAlways;
    UIView *paddingView1 = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 16, 20)];
    self.deliveryCampany.rightView = paddingView1;
    self.deliveryCampany.rightViewMode = UITextFieldViewModeAlways;
    
    
    self.deliveryCampany.layer.cornerRadius = 6;
    self.deliveryCampany.layer.masksToBounds = YES;
    [self.deliveryCampany mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self);
        make.leading.mas_equalTo(self).mas_offset(0);
        make.trailing.mas_equalTo(self).mas_offset(0);
        make.height.mas_equalTo(32);
    }];
    @weakify(self)
    [[self.deliveryCampany rac_textSignal] subscribeNext:^(NSString * _Nullable x) {
        @strongify(self)
        if(x.length == 0){
            self.filterDatas = self.datas;
        }
        else{
            self.filterDatas = [[[self.datas.rac_sequence signalWithScheduler:RACScheduler.immediateScheduler] filter:^BOOL(NSDictionary*  _Nullable value) {
                NSString* name = value[@"name"];
                return [name containsString:x];
            }] toArray];
        }
        [self.tableView reloadData];
    }];
    
    self.deliveryId = [[UITextField alloc] init];
    UIView *paddingView3 = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 16, 20)];
    self.deliveryId.leftView = paddingView3;
    self.deliveryId.leftViewMode = UITextFieldViewModeAlways;
    UIView *paddingView4 = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 16, 20)];
    self.deliveryCampany.rightView = paddingView4;
    self.deliveryCampany.rightViewMode = UITextFieldViewModeAlways;
    self.deliveryId.delegate = self;
    [self addSubview:self.deliveryId];
    [self.deliveryId mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.deliveryCampany.mas_bottom).mas_offset(6);
            make.leading.mas_equalTo(self).mas_offset(0);
            make.trailing.mas_equalTo(self).mas_offset(0);
            make.height.mas_equalTo(32);
    }];
    self.deliveryId.layer.cornerRadius = 6;
    self.deliveryId.layer.masksToBounds = YES;
    
    UIColor* C5 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    UIColor* C2 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    UIColor* C7 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.deliveryCampany.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_company_dialog_hint") attributes:@{NSForegroundColorAttributeName:C5}];
    self.deliveryId.attributedPlaceholder = [[NSAttributedString alloc]initWithString:Lang(@"after_sale_no_track") attributes:@{NSForegroundColorAttributeName:C5}];
    self.deliveryCampany.backgroundColor = C2;
    self.deliveryId.backgroundColor = C2;
    self.deliveryCampany.font = [UIFont systemFontOfSize:14];
    self.deliveryId.font = [UIFont systemFontOfSize:14];
    self.deliveryCampany.textColor = C7;
    self.deliveryId.textColor = C7;
    [[self.deliveryId rac_textSignal] subscribeNext:^(NSString * _Nullable x) {
        @strongify(self)
        self.expressId = x;
    }];
    [[self request] subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        self.datas = x;
        self.filterDatas = self.datas;
    } error:^(NSError * _Nullable error) {
        
    }];
}

- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    if([textField isEqual:self.deliveryCampany]){
        [self layoutIfNeeded];
        @weakify(self)
        CGPoint r =
        [self.tableShadowView convertPoint:CGPointMake(0, 0)
                                               toView:self.superview.superview];
        NSLog(@"!!!!!!%@",[NSValue valueWithCGPoint:r]);
        self.tableView.frame = CGRectMake(r.x, r.y, self.tableShadowView.frame.size.width, self.tableShadowView.frame.size.height);
        self.noDataView.frame = CGRectMake(0, 0, self.tableView.bounds.size.width, self.tableView.bounds.size.height);
        self.noDataLabel.frame = CGRectMake(0, 33, self.tableView.bounds.size.width, 40);
        
            self.tableShadowView.hidden = NO;
            self.tableView.hidden = NO;
            self.filterDatas = self.datas;
            [self.tableView reloadData];
        
        
    }
    return YES;
}

-(UITableView*)tableView{
    if(!_tableView){
        self.tableShadowView = [[UIView alloc] initWithFrame:CGRectZero];
        self.tableShadowView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        [self addSubview:_tableShadowView];
        _tableShadowView.backgroundColor = [UIColor redColor];
        [_tableShadowView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self).mas_offset(32);
            make.leading.mas_equalTo(self);
            make.trailing.mas_equalTo(self);
            make.height.mas_equalTo(6+32+32+15+32);
        }];
        _tableShadowView.hidden = YES;
        
        _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, 60, 32*3+21)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [self.superview.superview addSubview:_tableView];
        self.superview.superview.backgroundColor = [UIColor redColor];
        _tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        [_tableView registerClass:UITableViewCell.class forCellReuseIdentifier:@"caCell"];
        [_tableView setSeparatorInset:UIEdgeInsetsMake(0, 16, 0, 16)];
        self.clipsToBounds = NO;
//        self.superview.clipsToBounds = NO;
        
        _noDataView = [[UIView alloc] initWithFrame:CGRectZero];
        _noDataView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _tableView.showsVerticalScrollIndicator = NO;
        _tableView.showsHorizontalScrollIndicator = NO;
        [_tableView addSubview:_noDataView];
        _noDataLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _noDataLabel.font = [UIFont systemFontOfSize:14];
        _noDataLabel.textAlignment = NSTextAlignmentCenter;
        _noDataLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"];

        _noDataLabel.text = Lang(@"after_sale_company_dialog_no_search");
        [_noDataView addSubview:_noDataLabel];
        
        _tableView.hidden = YES;
    }
    if(!_tableView.superview){
        [self.superview.superview addSubview:_tableView];
    }
    return _tableView;
}

-(void)layoutSubviews{
    [super layoutSubviews];
    CGPoint r =
    [self.tableShadowView convertPoint:CGPointMake(0, 0)
                                           toView:self.superview.superview];
    NSLog(@"!!!!!!%@",[NSValue valueWithCGPoint:r]);
    self.tableView.frame = CGRectMake(r.x, r.y, self.tableShadowView.frame.size.width, self.tableShadowView.frame.size.height);
    self.noDataView.frame = CGRectMake(0, 0, self.tableView.bounds.size.width, self.tableView.bounds.size.height);
    self.noDataLabel.frame = CGRectMake(0, 33, self.tableView.bounds.size.width, 40);
}

-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if(self.filterDatas.count == 0){
        self.noDataView.hidden = NO;
    }else{
        self.noDataView.hidden = YES;
    }
    NSLog(@"tabel count:%@",@(self.filterDatas.count));
    return self.filterDatas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    UITableViewCell* cell = [tableView dequeueReusableCellWithIdentifier:@"caCell"];
    NSDictionary* dic = self.filterDatas[indexPath.row];
    cell.textLabel.text = dic[@"name"];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    cell.textLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    cell.textLabel.font = [UIFont systemFontOfSize:14];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    NSDictionary* dic = self.filterDatas[indexPath.row];
    self.selectCarrier = dic;
    self.tableView.hidden = YES;
    self.tableShadowView.hidden = YES;
    self.deliveryCampany.text = dic[@"name"];
    self.expressCampanyId = dic[@"id"];
    self.expressCampanyName = dic[@"name"];
    [self.deliveryCampany resignFirstResponder];
}

-(RACSignal*)request{
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSString* str = [[NSUserDefaults standardUserDefaults] objectForKey:kJDISVSaveCarrierInfo];
        NSArray* datas = [NSArray yy_modelWithJSON:str];
        if(datas.count){
            [subscriber sendNext:datas];
        }
        [[self realRequest] subscribeNext:^(id  _Nullable x) {
            NSString* str = [x yy_modelToJSONString];
            [[NSUserDefaults standardUserDefaults] setObject:str forKey:kJDISVSaveCarrierInfo];
            [subscriber sendNext:x];
        }];
        return nil;
    }];
}

-(RACSignal*)realRequest{
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_get_recommend_carrier_1.0" version:@"" parameters:nil complete:^(JDCDURLTask * _Nonnull urlTask, NSDictionary*  _Nullable responseObject, NSError * _Nullable error) {
            if(error){
                [subscriber sendError:error];
                return;
            }
            if(![responseObject isKindOfClass:NSDictionary.class]){
                [subscriber sendError:nil];
                return;
            }
            NSNumber* suc = responseObject[@"success"];
            if(suc.boolValue == YES){
                NSArray* arr = responseObject[@"data"];
                if([arr isKindOfClass:NSArray.class] && arr.count>1){
                    [subscriber sendNext:responseObject[@"data"]];
                    [subscriber sendCompleted];
                }else{
                    [subscriber sendError:nil];
                }
            }
            else{
                [subscriber sendError:nil];
            }
        }];
        return nil;
    }];
}
@end
