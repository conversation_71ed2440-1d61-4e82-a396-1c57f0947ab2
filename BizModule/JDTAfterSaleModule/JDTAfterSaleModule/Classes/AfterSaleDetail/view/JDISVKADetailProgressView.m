//
//  JDISVKADetailProgressView.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/21.
//

#import "JDISVKADetailProgressView.h"
#import "JDISVKADetailHeaderViewModel.h"
#import "JDISVKAAfterSaleUtils.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVKADetailOrderInfoModel.h"
#import <JDISVYYModelModule/YYModel.h>
@interface JDISVKADetailProgressView()

@property (weak, nonatomic) IBOutlet UIView *mainView;


@property (weak, nonatomic) IBOutlet UIImageView *currentAuditStatusImageView;

@property (weak, nonatomic) IBOutlet UILabel *progressTipLabel;

/*
 申请
 */
@property (weak, nonatomic) IBOutlet UIImageView *applyImageView;
@property (weak, nonatomic) IBOutlet UILabel *applyLabel;
@property (weak, nonatomic) IBOutlet UIView *underLineApplyLeft;
@property (weak, nonatomic) IBOutlet UIView *underLineApplyRight;
/*
 审核
 */

@property (weak, nonatomic) IBOutlet UIImageView *auditImageView;
@property (weak, nonatomic) IBOutlet UILabel *auditLabel;
@property (weak, nonatomic) IBOutlet UIView *underLineAuditLeft;
@property (weak, nonatomic) IBOutlet UIView *underLineAuditRight;


/*
 检查
 */
@property (weak, nonatomic) IBOutlet UIImageView *checkImageView;
@property (weak, nonatomic) IBOutlet UILabel *checkLabel;
@property (weak, nonatomic) IBOutlet UIView *underLineCheckLeft;
@property (weak, nonatomic) IBOutlet UIView *underLineCheckRight;

/*
 处理
 */
@property (weak, nonatomic) IBOutlet UIImageView *dealImageView;
@property (weak, nonatomic) IBOutlet UILabel *dealLabel;
@property (weak, nonatomic) IBOutlet UIView *underLineDealLeft;
@property (weak, nonatomic) IBOutlet UIView *underLineDealRight;

/*
 完成
 */
@property (weak, nonatomic) IBOutlet UIImageView *doneImageView;
@property (weak, nonatomic) IBOutlet UILabel *doneLabel;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *progressWidth;


@end

@implementation JDISVKADetailProgressView

- (instancetype)init
{
    self = [super init];
    if (self) {
        self = [[JDISVKAAfterSaleModuleBunle() loadNibNamed:@"JDISVKADetailProgressView" owner:self options:nil] lastObject];
        self.applyLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11",UIFontWeightRegular);
        self.applyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.auditLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11",UIFontWeightRegular);
        self.auditLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.checkLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11",UIFontWeightRegular);
        self.checkLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.dealLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11",UIFontWeightRegular);
        self.dealLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.doneLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11",UIFontWeightRegular);
        self.doneLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        
        

        self.backgroundColor = [UIColor clearColor];
        
       
    }
    return self;
}

- (void)configProgressView:(JDISVKADetailHeaderViewModel *)viewModel{
    
    self.backgroundColor = [UIColor colorWithPatternImage:[UIImage jdcd_getGradientImageFromColors:@[[[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-e"],[[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-f"]] gradientType:GradientFromLeftToRight imgSize:CGSizeMake(self.frame.size.width, self.frame.size.height)]];
    self.progressTipLabel.text = viewModel.afsServiceStepName;
    
    switch (viewModel.afsServiceStep) {
        case 50:{
            self.currentAuditStatusImageView.image = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_CIRCLE_SMALL imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];
        }
            break;
        case 60:{
            self.currentAuditStatusImageView.image = [UIImage ka_iconWithName:JDIF_ICON_TIPS imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];
        }
            break;
        case 20:{
            self.currentAuditStatusImageView.image = [UIImage ka_iconWithName:JDIF_ICON_TIPS imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];
        }
            break;
            
        default:{
            self.currentAuditStatusImageView.image = [UIImage ka_iconWithName:JDIF_ICON_CLOCK_LINE_SMALL imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"]];
        }
            break;
    }
    
    
    self.progressWidth.constant = ([UIScreen mainScreen].bounds.size.width - 36 - 18*5)*0.2*0.5;
    
    self.progressTipLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T5",UIFontWeightSemibold);
    self.progressTipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    
    NSMutableArray *labelArray = [[NSMutableArray alloc] init];
    NSMutableArray *imageArray = [[NSMutableArray alloc] init];
    NSMutableArray *underLineArray = [[NSMutableArray alloc] init];

    [labelArray addObject:self.applyLabel];
    [labelArray addObject:self.auditLabel];
    [labelArray addObject:self.checkLabel];
    [labelArray addObject:self.dealLabel];
    [labelArray addObject:self.doneLabel];
    [imageArray addObject:self.applyImageView];
    [imageArray addObject:self.auditImageView];
    [imageArray addObject:self.checkImageView];
    [imageArray addObject:self.dealImageView];
    [imageArray addObject:self.doneImageView];
    [underLineArray addObject:self.underLineApplyLeft];
    [underLineArray addObject:self.underLineApplyRight];
    [underLineArray addObject:self.underLineAuditLeft];
    [underLineArray addObject:self.underLineAuditRight];
    [underLineArray addObject:self.underLineCheckLeft];
    [underLineArray addObject:self.underLineCheckRight];
    [underLineArray addObject:self.underLineDealLeft];
    [underLineArray addObject:self.underLineDealRight];
    
    UIColor* lineColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    for (UIImageView *imageView in imageArray) {
        UIImage* img = JDISVKAAfterSaleModuleImageNamed(@"afterSale_detail_unDone");
        UIImage* nImg = [self imgWithColor:lineColor img:img];
        [imageView setImage:nImg];
    }
    
    for (UIView *currentUnderline in underLineArray) {
        currentUnderline.backgroundColor = lineColor;
    }
    NSInteger index = viewModel.currentIndex - 1;
    for (NSDictionary *dic in viewModel.progressStatusTextArray) {
        JDISVKAAAfsServiceProcessNodeStatusVo *node = [JDISVKAAAfsServiceProcessNodeStatusVo yy_modelWithDictionary:dic];
        NSString *nodeText = node.text;
        if (node.order > 5 || node.order < 1) {
            return;
        }
        UILabel *currentLabel =  labelArray[node.order-1];
        currentLabel.text = nodeText;
        
        //配置节点
        UIImageView *currentImageView = imageArray[node.order-1];
        [currentLabel setHidden:NO];
        [currentImageView setHidden:NO];
        
        if (node.order < viewModel.progressStatusTextArray.count) {
            NSInteger currentUnderLineCount = ((node.order-1) * 2 + 1);
            UIView *currentLeftView = underLineArray[currentUnderLineCount-1];
            UIView *currentRightView = underLineArray[currentUnderLineCount];
            [currentLeftView setHidden:NO];
            [currentRightView setHidden:NO];
        }
    }
    
    if (index > 5 || index < 0) {
        return;
    }
    
    
    UILabel *label = labelArray[index];
    label.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T11",UIFontWeightMedium);
    
    
    for (int i = 0 ; i <= index; i++) {
        UIImage* img = JDISVKAAfterSaleModuleImageNamed(@"afterSale_detail_done");
        
        if(i == index){
            img = JDISVKAAfterSaleModuleImageNamed(@"afterSale_detail_current");
        }
        
        if(i == viewModel.progressStatusTextArray.count-1){
            img = JDISVKAAfterSaleModuleImageNamed(@"afterSale_detail_done");
        }
        
        UIImageView *setImageView = imageArray[i];
        UIImage* nImg = [self imgWithColor:lineColor img:img];
        setImageView.image = nImg;
    }
    NSInteger underLineCount = (index * 2 + 1);
    for (NSInteger i = underLineCount ; i < underLineArray.count; i++) {
        UIView *line = underLineArray[i];
        line.backgroundColor =[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1" alpha:0.5];
    }



}
-(UIImage*)imgWithColor:(UIColor*)color
                    img:(UIImage*)img
{
    UIGraphicsBeginImageContextWithOptions(img.size, NO, img.scale);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextTranslateCTM(context, 0, img.size.height);
    CGContextScaleCTM(context, 1.0, -1.0);
    CGContextSetBlendMode(context, kCGBlendModeNormal);
    CGRect rect = CGRectMake(0, 0, img.size.width, img.size.height);
    CGContextClipToMask(context, rect, img.CGImage);
    [color setFill];
    CGContextFillRect(context, rect);
    UIImage*newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImage;
}
@end
