//
//  JDISVKADetailOrderInfoCell.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailOrderInfoCell.h"
#import "JDISVKADetailOrderInfoViewModel.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVReactiveObjCModule/RACSubject.h>
#import "JDISVKAAfterSaleAction.h"
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JIDSVAfterSaleLogistic.h"
#import <JDISVKAAfterSaleModule/JDISVKAAfterSaleModule-umbrella.h>

#import "NSString+JDISVAfterExtension.h"
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import "JDISVKAApplyDetailReturntipView.h"

@interface JDISVKADetailOrderInfoCell()

@property (weak, nonatomic) IBOutlet UIView *mainView;
@property (nonatomic, weak) UIView *stackView;

@property (nonatomic,strong)JDISVKADetailOrderInfoViewModel *viewModel;

//服务单号
@property (weak, nonatomic) IBOutlet UIView *serverIDView;
@property (weak, nonatomic) IBOutlet UILabel *seriverIDLabel;
@property (weak, nonatomic) IBOutlet UILabel *serverIDValue;

//申请时间
@property (weak, nonatomic) IBOutlet UIView *applyTimeView;
@property (weak, nonatomic) IBOutlet UILabel *applyTimeLabel;
@property (weak, nonatomic) IBOutlet UILabel *applyTimeValue;

//售后类型
@property (weak, nonatomic) IBOutlet UIView *afterSaleTypeView;
@property (weak, nonatomic) IBOutlet UILabel *afterSaleTypeLabel;
@property (weak, nonatomic) IBOutlet UILabel *afterSaleTypeValue;

//申请原因
@property (weak, nonatomic) IBOutlet UIView *applyReasionView;
@property (weak, nonatomic) IBOutlet UILabel *applyReasionLabel;
@property (weak, nonatomic) IBOutlet UILabel *applyReasionValue;

//问题描述
@property (weak, nonatomic) IBOutlet UIView *applyQuestionDescView;
@property (weak, nonatomic) IBOutlet UILabel *applyQuestionDescLabel;
@property (weak, nonatomic) IBOutlet UILabel *applyQuestionDescValue;

// Damage pictures
@property (nonatomic, weak)UIView *pictureBGView;

//退款方式
@property (weak, nonatomic) IBOutlet UIView *refundTypeView;
@property (weak, nonatomic) IBOutlet UILabel *refundTypeLabel;
@property (weak, nonatomic) IBOutlet UILabel *refundTypeValue;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *refundTypeViewHeight;

//银行卡名称
@property (weak, nonatomic) IBOutlet UIView *bankNameView;
@property (weak, nonatomic) IBOutlet UILabel *bankNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *bankNameValue;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bankNameViewHeight;

//银行卡号
@property (weak, nonatomic) IBOutlet UIView *bankCardNumView;
@property (weak, nonatomic) IBOutlet UILabel *bankCardNumLabel;
@property (weak, nonatomic) IBOutlet UILabel *bankCardNumValue;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bankCardNumViewHeight;

//银行开户姓名
@property (weak, nonatomic) IBOutlet UIView *bankAccountNameView;
@property (weak, nonatomic) IBOutlet UILabel *bankAccountNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *bankAccountNameValue;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bankAccountNameViewHeight;

//银行-手机号码
@property (weak, nonatomic) IBOutlet UIView *phoneNumberView;
@property (weak, nonatomic) IBOutlet UILabel *phoneNumberLabel;
@property (weak, nonatomic) IBOutlet UILabel *phoneNumberValue;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *phoneNumberViewHeight;


//退货方式
@property (weak, nonatomic) IBOutlet UIView *returnTypeView;
@property (weak, nonatomic) IBOutlet UILabel *returnTypeLabel;
@property (weak, nonatomic) IBOutlet UILabel *returnTypeValue;

//上门取件状态提示
@property (weak, nonatomic) IBOutlet UIView *pickUpStatusTipView;
@property (weak, nonatomic) IBOutlet UILabel *pickUpStatusTipLabel;
@property (weak, nonatomic) IBOutlet UIImageView *pickUpStatusTipIcon;

//上门取件状态
@property (weak, nonatomic) IBOutlet UIView *pickUpStatusView;
@property (weak, nonatomic) IBOutlet UILabel *pickUpStatusLabel;
@property (weak, nonatomic) IBOutlet UILabel *pickUpStatusValue;

//上门取件信息
@property (weak, nonatomic) IBOutlet UIView *pickUpInfoView;
@property (weak, nonatomic) IBOutlet UILabel *pickUpInfoLabel;
@property (weak, nonatomic) IBOutlet UILabel *pickUpName;
@property (weak, nonatomic) IBOutlet UILabel *pickUpPhone;
@property (weak, nonatomic) IBOutlet UILabel *pickUpAddress;
//@property (nonatomic, weak) UILabel *returnModelTipsLB;
@property (nonatomic, strong) JDISVKAApplyDetailReturntipView *returnTipView;


//承运商
@property (weak, nonatomic) IBOutlet UIView *pickUpCarrierView;
@property (weak, nonatomic) IBOutlet UILabel *pickUpCarriereLabel;
@property (weak, nonatomic) IBOutlet UILabel *pickUpCarrierValue;

//取件单号
@property (weak, nonatomic) IBOutlet UIView *pickUpTrackingNumView;
@property (weak, nonatomic) IBOutlet UILabel *pickUpTrackingNumLabel;
@property (weak, nonatomic) IBOutlet UILabel *pickUpTrackingNumValue;

//上门取件时间
@property (weak, nonatomic) IBOutlet UIView *pickUpTimeView;
@property (weak, nonatomic) IBOutlet UILabel *pickUpTimeLabel;
@property (weak, nonatomic) IBOutlet UILabel *pickUpTimeValue;

//快递信息
@property (weak, nonatomic) IBOutlet UIView *deliveryInfoView;
@property (weak, nonatomic) IBOutlet UILabel *deliveryInfoLabel;
@property (weak, nonatomic) IBOutlet UILabel *deliveryInfoValue;

//收货信息

@property (weak, nonatomic) IBOutlet UIView *divideLineView;

@property (weak, nonatomic) IBOutlet UIView *receiverInfoView;
@property (weak, nonatomic) IBOutlet UILabel *receiverInfoLabel;
@property (weak, nonatomic) IBOutlet UILabel *receiverName;
@property (weak, nonatomic) IBOutlet UILabel *receiverPhone;
@property (weak, nonatomic) IBOutlet UILabel *receiverAddress;
//发货承运商
@property (weak, nonatomic) IBOutlet UIView *returnCarrierView;
@property (weak, nonatomic) IBOutlet UILabel *returnCarriereLabel;
@property (weak, nonatomic) IBOutlet UILabel *returnCarriereValue;

//发货单号
@property (weak, nonatomic) IBOutlet UIView *invoiceInfoView;
@property (weak, nonatomic) IBOutlet UILabel *invoiceLabel;

@property (weak, nonatomic) IBOutlet UILabel *invoiceValue;

@property (weak, nonatomic) IBOutlet UIButton *invoiceCopyButton;

#pragma mark 约束

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *serverIDViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *applyTimeViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *reasonViewConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *questionDescViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *afterSaleTypeViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *returnTypeViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pickUpStatusTipViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pickUpStatusViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pickUpInfoViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pickUpCarrierViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pickUpTrackingNumViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pickUpTimeViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *deliveryInfoViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *receiverInfoViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *invoiceInfoViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *returnCarrierViewHeight;


@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;

@end

@implementation JDISVKADetailOrderInfoCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self setupCell];
}

- (void)setupCell{
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];;
    self.mainView.layer.masksToBounds = YES;
    
    self.serverIDView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.applyTimeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.applyReasionView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.applyQuestionDescView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

    self.afterSaleTypeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.refundTypeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.bankNameView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.bankCardNumView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.bankAccountNameView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.phoneNumberView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

    self.returnTypeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    UIColor *tipCloor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C41"];
    self.pickUpStatusTipView.backgroundColor = [tipCloor colorWithAlphaComponent:0.1];
    self.pickUpStatusTipLabel.textColor = tipCloor;
    self.pickUpStatusTipLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
    UIImage* arrow =  [UIImage ka_iconWithName:JDIF_ICON_INFO imageSize:CGSizeMake(16, 16) color:tipCloor];
    self.pickUpStatusTipIcon.image = arrow;
    
    self.pickUpStatusView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.pickUpInfoView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.pickUpCarrierView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.pickUpTrackingNumView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

    self.pickUpTimeView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.deliveryInfoView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.divideLineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.invoiceInfoView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.returnCarrierView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.receiverInfoView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
}
    

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
    
    [self displayDamagedPhotos];
    [self displayRetrunTips];
}

- (void)render{
    [self.serverIDView setHidden:!self.viewModel.serverOrderNumber];
    [self.applyTimeView setHidden:!self.viewModel.applyTime];
    [self.applyReasionView setHidden:!self.viewModel.reasonName];
    [self.applyQuestionDescView setHidden:!self.viewModel.questionDesc];
    [self.afterSaleTypeView setHidden:!self.viewModel.serverType];
    
    //TODOH
    [self.refundTypeView setHidden:!self.viewModel.refundType];
    [self.bankNameView setHidden:!self.viewModel.bankName];
    [self.bankCardNumView setHidden:!self.viewModel.bankCode];
    [self.bankAccountNameView setHidden:!self.viewModel.bankAccountHolder];
    [self.phoneNumberView setHidden:!self.viewModel.bankTel];
    
    
    [self.returnTypeView setHidden:!self.viewModel.returnModelStr];
    [self.pickUpStatusView setHidden:!self.viewModel.pickStatusStr];
    [self.pickUpInfoView setHidden:!self.viewModel.pickCustomerInfo.contactName];
    [self.pickUpCarrierView setHidden:!self.viewModel.pickCustomerInfo.expressCompany];
    [self.pickUpTrackingNumView setHidden:!self.viewModel.pickCustomerInfo.expressNo];
    
    [self.pickUpTimeView setHidden:YES];
//    [self.pickUpTimeView setHidden:!self.viewModel.afsApplyExpandInfoVo.reserveDateBegin];
    
    [self.deliveryInfoView setHidden:!self.viewModel.deliveryInfo];
    [self.receiverInfoView setHidden:!self.viewModel.isShowReceivingArea];
//    [self.invoiceInfoView setHidden:!self.viewModel.invoiceNumber];
    [self.invoiceInfoView setHidden:self.viewModel.expressInfos.count <= 0];
    [self.returnCarrierView setHidden:!self.viewModel.returnExpressCompany];
    [self configCellColorAndFont];
    
    self.serverIDViewHeight.constant = self.viewModel.serverIDViewHeight;
    self.applyTimeViewHeight.constant = self.viewModel.applyTimeViewHeight;
    self.reasonViewConstraint.constant = self.viewModel.reasonInfoViewHeight;
    self.questionDescViewHeight.constant = self.viewModel.questionDescViewHeight;
    self.afterSaleTypeViewHeight.constant = self.viewModel.afterSaleTypeViewHeight;
    
    
    self.refundTypeViewHeight.constant = self.viewModel.refundTypeViewHeight;
    self.bankNameViewHeight.constant = self.viewModel.bankNameViewHeight;
    self.bankCardNumViewHeight.constant = self.viewModel.bankCardNumViewHeight;
    self.bankAccountNameViewHeight.constant = self.viewModel.bankAccountNameViewHeight;
    self.phoneNumberViewHeight.constant = self.viewModel.phoneNumberViewHeight;
    
    self.returnTypeViewHeight.constant = self.viewModel.returnTypeViewHeight;
    self.pickUpStatusTipViewHeight.constant = self.viewModel.pickUpStatusTipViewHeight;
    self.pickUpStatusViewHeight.constant = self.viewModel.pickUpStatusViewHeight;
    self.pickUpInfoViewHeight.constant = self.viewModel.pickUpInfoViewHeight;
    self.pickUpCarrierViewHeight.constant = self.viewModel.pickUpCarrierViewHeight;
    self.pickUpTrackingNumViewHeight.constant = self.viewModel.pickUpTrackingNumViewHeight;
    self.pickUpTimeViewHeight.constant = self.viewModel.pickUpTimeViewHeight;

    self.deliveryInfoViewHeight.constant = self.viewModel.deliveryInfoViewHeight;
    self.receiverInfoViewHeight.constant = self.viewModel.receiverInfoViewHeight;
    self.invoiceInfoViewHeight.constant = self.viewModel.invoiceInfoViewHeight;
    self.returnCarrierViewHeight.constant = self.viewModel.returnCarrierViewHeight;
    NSLog(@"%@",@(self.viewModel.reasonInfoViewHeight)) ;
    
    //赋值
    self.serverIDValue.text = self.viewModel.serverOrderNumber;
    self.applyTimeValue.text = self.viewModel.applyTime;
    self.afterSaleTypeValue.text = self.viewModel.serverType;
    
    self.returnTypeValue.text = self.viewModel.returnModelStr;
    self.pickUpStatusTipLabel.text = self.viewModel.pickStatusTipStr;
    self.pickUpStatusTipView.hidden = self.viewModel.pickStatusTipStr ? NO : YES;
    self.pickUpStatusValue.text = self.viewModel.pickStatusStr;
    self.pickUpName.text = self.viewModel.pickCustomerInfo.contactName;
    self.pickUpPhone.text = [NSString stringWithFormat:@"+966 %@",self.viewModel.pickCustomerInfo.contactTel];

    self.pickUpAddress.text = self.viewModel.pickCustomerInfo.addressInfo.address;
    self.pickUpCarrierValue.text = self.viewModel.pickCustomerInfo.expressCompany;
    //    self.pickUpTrackingNumValue.text = self.viewModel.pickCustomerInfo.expressNo;
    self.pickUpTrackingNumValue.hidden = YES;
    self.pickUpTrackingNumView.hidden = self.viewModel.pickCustomerInfo.expressInfos.count > 0 ? NO : YES;
    for(int i=0;i<self.viewModel.pickCustomerInfo.expressInfos.count;i++){
        applyDetailAfsExpressInfoVo *expressInfoModel = self.viewModel.pickCustomerInfo.expressInfos[i];
        
        UILabel *label = [[UILabel alloc] init];
        label.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        [self.pickUpTrackingNumView addSubview:label];
        label.text = expressInfoModel.expressNo;
        
        UILabel *label2 = [[UILabel alloc] init];
        label2.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        label2.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        [self.pickUpTrackingNumView addSubview:label2];
        label2.text = [NSString stringWithFormat:Lang(@"after_sale_apply_pickUp_logistics_count"),expressInfoModel.nums];
        if (self.viewModel.pickCustomerInfo.expressInfos.count <= 1){
            label2.hidden = YES;
        }
        
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button renderB4];
        button.tag = i;
        button.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R52"];
        [button.titleLabel setFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]];
        [button setTitle:Lang(@"after_sale_copy") forState:UIControlStateNormal];
        [button addTarget:self action:@selector(copyPickupTrackNum:) forControlEvents:UIControlEventTouchUpInside];
        [self.pickUpTrackingNumView addSubview:button];
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.pickUpTrackingNumView).offset(-18);
            make.height.mas_equalTo(24);
            make.width.mas_equalTo(48);
            make.centerY.equalTo(label);
        }];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.pickUpTrackingNumLabel.mas_trailing).offset(12);
            make.height.mas_equalTo(20);
            make.top.equalTo(self.pickUpTrackingNumView).offset(i*(20+8)+6);
        }];
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(label.mas_trailing).offset(6);
            make.centerY.equalTo(label);
        }];
        
        //logictis
        UIColor* C9 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        if(expressInfoModel.umsList.count){
            UIButton* logictsBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            [self.pickUpTrackingNumView addSubview:logictsBtn];
            NSString* btnStr = Lang(@"aftersale_logistics_track");
            UIFont* font = [UIFont systemFontOfSize:14];
            logictsBtn.titleLabel.font = font;
            CGSize size = [btnStr jdcd_getStringSize:font constraintsSize:CGSizeMake(200, 16)];
            [logictsBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(label.mas_bottom).mas_offset(5);
                make.leading.mas_equalTo(label.mas_leading);
                make.height.mas_equalTo(25);
                make.width.mas_equalTo(size.width+25);
            }];
            [logictsBtn setTitle:btnStr forState:UIControlStateNormal];
            [logictsBtn setTitleColor:C9 forState:UIControlStateNormal];
            logictsBtn.layer.borderColor = C9.CGColor;
            logictsBtn.layer.borderWidth = 1;
            logictsBtn.layer.cornerRadius = 12.5;
            logictsBtn.layer.masksToBounds = YES;
            NSArray<JDISVLogisticData*>* logisticsData = expressInfoModel.umsList;
            [logictsBtn jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
                UIViewController* vc = [JIDSVAfterSaleLogistic controllerWithLogisticDatas:logisticsData];
                [[PlatformService getTopViewController].navigationController pushViewController:vc animated:YES];
            }];
        }
    }
    
    self.refundTypeValue.text = self.viewModel.refundTypeStr;
    self.bankNameValue.text = self.viewModel.bankName;
    self.bankCardNumValue.text = self.viewModel.bankCode;
    self.bankAccountNameValue.text = self.viewModel.bankAccountHolder;
    self.phoneNumberValue.text = [NSString stringWithFormat:@"+966 %@",self.viewModel.bankTel];
    
    self.applyReasionValue.text = self.viewModel.reasonName;
    self.applyQuestionDescValue.text = self.viewModel.questionDesc;

    
    if (self.viewModel.deliveryInfo.length > 0) {
        self.deliveryInfoValue.text = self.viewModel.deliveryInfo;
        self.deliveryInfoValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        self.deliveryInfoValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }else{
        self.deliveryInfoValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        self.deliveryInfoValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"];
        self.deliveryInfoValue.text = Lang(@"after_sale_track_et_hint");
    }
//    self.invoiceValue.text = self.viewModel.invoiceNumber;
    self.invoiceValue.hidden = YES;
    for(int i=0;i<self.viewModel.expressInfos.count;i++){
        applyDetailAfsExpressInfoVo *expressInfoModel = self.viewModel.expressInfos[i];
        
        UILabel *label = [[UILabel alloc] init];
        label.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        [self.invoiceInfoView addSubview:label];
        label.text = expressInfoModel.expressNo;
        
        UILabel *label2 = [[UILabel alloc] init];
        label2.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        label2.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        [self.invoiceInfoView addSubview:label2];
        label2.text = [NSString stringWithFormat:Lang(@"after_sale_apply_pickUp_logistics_count"),expressInfoModel.nums];
        if (self.viewModel.expressInfos.count <= 1){
            label2.hidden = YES;
        }
        
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button renderB4];
        button.tag = i;
        button.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R52"];
        [button.titleLabel setFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]];
        [button setTitle:Lang(@"after_sale_copy") forState:UIControlStateNormal];
        [button addTarget:self action:@selector(copyTrackNum:) forControlEvents:UIControlEventTouchUpInside];
        [self.invoiceInfoView addSubview:button];
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.invoiceInfoView).offset(-18);
            make.height.mas_equalTo(24);
            make.width.mas_equalTo(48);
            make.centerY.equalTo(label);
        }];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.invoiceLabel.mas_trailing).offset(12);
//            make.trailing.equalTo(button);
            make.height.mas_equalTo(20);
            make.top.equalTo(self.invoiceInfoView).offset(i*(20+8)+6);
        }];
        [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(label.mas_trailing).offset(6);
//            make.height.mas_equalTo(24);
//            make.width.mas_equalTo(48);
            make.centerY.equalTo(label);
        }];
        //logictis
        UIColor* C9 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        if(expressInfoModel.umsList.count){
            UIButton* logictsBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            [self.invoiceInfoView addSubview:logictsBtn];
            NSString* btnStr = Lang(@"aftersale_logistics_track");
            UIFont* font = [UIFont systemFontOfSize:14];
            logictsBtn.titleLabel.font = font;
            CGSize size = [btnStr jdcd_getStringSize:font constraintsSize:CGSizeMake(200, 16)];
            [logictsBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(label.mas_bottom).mas_offset(5);
                make.leading.mas_equalTo(label.mas_leading);
                make.height.mas_equalTo(25);
                make.width.mas_equalTo(size.width+25);
            }];
            [logictsBtn setTitle:btnStr forState:UIControlStateNormal];
            [logictsBtn setTitleColor:C9 forState:UIControlStateNormal];
            logictsBtn.layer.borderColor = C9.CGColor;
            logictsBtn.layer.borderWidth = 1;
            logictsBtn.layer.cornerRadius = 12.5;
            logictsBtn.layer.masksToBounds = YES;
            NSArray<JDISVLogisticData*>* logisticsData = expressInfoModel.umsList;
            [logictsBtn jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
                UIViewController* vc = [JIDSVAfterSaleLogistic controllerWithLogisticDatas:logisticsData];
                [[PlatformService getTopViewController].navigationController pushViewController:vc animated:YES];
            }];
        }
    }
    
    NSString *receiverName = [NSString isEmpty:self.viewModel.firstName] ? self.viewModel.receiverName : self.viewModel.firstName;
    receiverName = [NSString isEmpty:receiverName] ? @"" : receiverName;
    
    if (![NSString isEmpty:self.viewModel.lastName]) {
        receiverName = [NSString stringWithFormat:@"%@ %@",receiverName, self.viewModel.lastName];
    }
    receiverName = [NSString isEmpty:receiverName] ? @"-" : receiverName;
   
    self.receiverName.text = receiverName;
    
    self.receiverPhone.text = [NSString stringWithFormat:@"+966 %@",self.viewModel.receiverPhone];
    self.returnCarriereValue.text = self.viewModel.returnExpressCompany;
    self.receiverAddress.text = self.viewModel.receiverAddress;
    
    [self.applyQuestionDescValue mas_updateConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.applyQuestionDescLabel.mas_trailing).offset(12);
        make.trailing.mas_equalTo(self.applyQuestionDescView.mas_trailing).offset(-18);
    }];
}

- (void)configCellColorAndFont{
    self.seriverIDLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.seriverIDLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.serverIDValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.serverIDValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.seriverIDLabel.text = Lang(@"after_sale_detail_service_id");
    
    self.applyTimeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyTimeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.applyTimeValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyTimeValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.applyTimeLabel.text = Lang(@"after_sale_detail_time");
    
    self.applyReasionLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyReasionLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.applyReasionValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyReasionValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.applyReasionLabel.text = Lang(@"after_sale_detail_reason");
    
    self.applyQuestionDescLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyQuestionDescLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.applyQuestionDescValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.applyQuestionDescValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.applyQuestionDescLabel.text = Lang(@"after_sale_desc_reason");
    
    self.afterSaleTypeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.afterSaleTypeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.afterSaleTypeValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.afterSaleTypeValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.afterSaleTypeLabel.text = Lang(@"after_sale_detail_type");
    
    //退款信息
    self.refundTypeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.refundTypeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.refundTypeValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.refundTypeValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.refundTypeLabel.text = Lang(@"after_sale_refund_title");
    
    self.bankNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.bankNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.bankNameValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.bankNameValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.bankNameLabel.text = Lang(@"after_sale_apply_bankName");
    
    self.bankCardNumLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.bankCardNumLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.bankCardNumValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.bankCardNumValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.bankCardNumLabel.text = Lang(@"after_sale_apply_bankCard");
    
    self.bankAccountNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.bankAccountNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.bankAccountNameValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.bankAccountNameValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.bankAccountNameLabel.text = Lang(@"after_sale_apply_bankAccountName");
    
    self.phoneNumberLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.phoneNumberLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.phoneNumberValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.phoneNumberValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.phoneNumberLabel.text = Lang(@"after_sale_apply_phoneNumber");
    
    
    self.returnTypeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.returnTypeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.returnTypeValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.returnTypeValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.returnTypeLabel.text = Lang(@"after_sale_return_title");
    
    self.pickUpStatusLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpStatusLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.pickUpStatusValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpStatusValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.pickUpStatusLabel.text = Lang(@"after_sale_apply_pickUp_status");
    
    self.pickUpInfoLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpInfoLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.pickUpInfoLabel.text = Lang(@"after_sale_apply_pickUp_info");
    self.pickUpName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.pickUpPhone.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpPhone.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.pickUpAddress.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpAddress.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];

    self.pickUpCarriereLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpCarriereLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.pickUpCarriereLabel.text = Lang(@"after_sale_apply_pickUp_logistics_name");
    self.pickUpCarrierValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpCarrierValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];

    self.pickUpTrackingNumLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpTrackingNumLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.pickUpTrackingNumLabel.text = Lang(@"after_sale_apply_pickUp_logistics_number");
    self.pickUpTrackingNumValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpTrackingNumValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.pickUpTimeLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpTimeLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.pickUpTimeLabel.text = Lang(@"after_sale_apply_pickUp_time");
    self.pickUpTimeValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.pickUpTimeValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];

    self.deliveryInfoLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.deliveryInfoLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.deliveryInfoLabel.text = Lang(@"after_sale_detail_transport");
    
    self.receiverInfoLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.receiverInfoLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.receiverInfoLabel.text = Lang(@"after_sale_detail_transport");

    self.receiverName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.receiverName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.receiverPhone.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.receiverPhone.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.receiverAddress.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.receiverAddress.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.invoiceLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.invoiceLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.invoiceValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.invoiceValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.invoiceLabel.text = Lang(@"after_sale_detail_send_id");
    
    self.returnCarriereLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.returnCarriereLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.returnCarriereLabel.text = Lang(@"after_sale_apply_return_logistics_name");
    self.returnCarriereValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.returnCarriereValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    
    self.divideLineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self.invoiceCopyButton renderB4];
    self.invoiceCopyButton.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R52"];
    [self.invoiceCopyButton.titleLabel setFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]];
    [self.invoiceCopyButton setTitle:Lang(@"after_sale_copy") forState:UIControlStateNormal];
    [self.invoiceCopyButton addTarget:self action:@selector(copyInvoice) forControlEvents:UIControlEventTouchUpInside];
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
}

- (void)copyInvoice{
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithDetailCopyOrderInfoSuccess];
    action.value = self.invoiceValue.text;
    [self.delegate sendNext:action];
}

- (void)copyPickupTrackNum:(UIButton *)sender{
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    applyDetailAfsExpressInfoVo *expressInfoModel = self.viewModel.pickCustomerInfo.expressInfos[sender.tag];
    [pasteboard setString:expressInfoModel.expressNo ? : @""];
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:Lang(@"after_sale_copy_success")];
}

- (void)copyTrackNum:(UIButton *)sender{
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    applyDetailAfsExpressInfoVo *expressInfoModel = self.viewModel.expressInfos[sender.tag];
    [pasteboard setString:expressInfoModel.expressNo ? : @""];
    [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:Lang(@"after_sale_copy_success")];
}

- (void)displayDamagedPhotos {
    if ([NSString isEmpty:self.viewModel.questionPic]) { return; }
    
    NSArray *photos = [self.viewModel.questionPic componentsSeparatedByString:@","];
    if (nil == photos || 0 == photos.count) { return; }
    
    if (nil != self.pictureBGView) { return; }
    
    UIStackView *stackView = (UIStackView *)[self.applyQuestionDescView superview];
    if (nil == stackView) { return; }
    
    if (nil == self.tableView) { return; }
    
    [self.viewModel updataViewModel];
    
    UILabel *titleLB = [[UILabel alloc] init];
    titleLB.textAlignment = NSTextAlignmentLeft;
    titleLB.numberOfLines = 0;
    titleLB.backgroundColor = [UIColor clearColor];
    titleLB.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightRegular);
    titleLB.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    titleLB.text = Lang(@"after_sale_desc_problem_photos");
    [self.applyQuestionDescView addSubview:titleLB];
    
    CGFloat photosViewH = self.viewModel.photosViewHeight;
    UIView *pictureBGView = [[UIView alloc] init];
    [self.applyQuestionDescView addSubview:pictureBGView];
    self.pictureBGView = pictureBGView;
    
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(pictureBGView.mas_top);
        make.bottom.mas_lessThanOrEqualTo(self.pictureBGView.mas_bottom);
        make.leading.mas_equalTo(self.applyQuestionDescLabel.mas_leading);
        make.trailing.mas_equalTo(self.applyQuestionDescLabel.mas_trailing);
    }];
    
    [pictureBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_greaterThanOrEqualTo(self.applyQuestionDescValue.mas_bottom).offset(10);
        make.bottom.mas_equalTo(self.applyQuestionDescView.mas_bottom);
        make.leading.mas_equalTo(titleLB.mas_trailing).offset(0);
        make.trailing.mas_equalTo(stackView.mas_trailing);
        make.height.mas_equalTo(photosViewH);
    }];
    
//    NSArray *existingSubviews = stackView.arrangedSubviews;
//    NSUInteger preIndex = [existingSubviews indexOfObject:self.applyQuestionDescView];
//    // 将新视图插入到指定位置 / Insert the new view into the specified location
//    [stackView insertArrangedSubview:pictureBGView atIndex:preIndex+1];
    // refresh and recount cell Height
    [self refreshPhotoUI];
    
    [self.tableView reloadData];
    
}

// refresh phots UI
- (void)refreshPhotoUI {
    
    for (UIView *subView in self.pictureBGView.subviews) {
        [subView removeFromSuperview];
    }
    
    CGFloat itemDis = 10.0;
    CGFloat photoRowHeight = 60.0;
    NSInteger rowItemCount = 3;
    NSArray *photos = [self.viewModel.questionPic componentsSeparatedByString:@","];
    UIView *preView = nil;
    for (NSInteger index = 0; index < photos.count; index++) {
        NSInteger row = index < 2 ? 1 : 2;
        NSString *photoPath = [photos objectAtIndex:index];
        
        UIView *itemView = [[UIView alloc] init];
        itemView.clipsToBounds = YES;
        [self.pictureBGView addSubview:itemView];
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            if (nil == preView || (index % rowItemCount) == 0) {
                make.leading.mas_equalTo(self.pictureBGView.mas_leading).offset(18);
            } else {
                make.leading.mas_equalTo(preView.mas_trailing).offset(15);
            }
            if (1 == row) {
                make.top.mas_equalTo(self.pictureBGView.mas_top);
            } else {
                if ((index % rowItemCount) == 0) {
                    make.top.mas_equalTo(preView.mas_bottom).offset(itemDis);
                } else {
                    make.top.mas_equalTo(preView.mas_top);
                }
            }
            make.width.mas_equalTo(photoRowHeight);
            make.height.mas_equalTo(photoRowHeight);
            if (index == photos.count - 1) {
                make.bottom.mas_lessThanOrEqualTo(self.pictureBGView.mas_bottom);
            }
        }];
        preView = itemView;
        
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView.layer.cornerRadius = 8;
        imageView.layer.masksToBounds = YES;
        imageView.clipsToBounds = YES;
        imageView.userInteractionEnabled = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        @weakify(self)
        [imageView jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
            @strongify(self)
            [self viewBigPictureAtIndex:index];
        }];
        [itemView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.top.bottom.mas_equalTo(itemView);
        }];
        NSString *photoUrl = [PlatformService getCompleteImageUrl:photoPath];
        [imageView jdcd_setImage:photoUrl contentMode:UIViewContentModeScaleAspectFill];
    }
}

- (void)viewBigPictureAtIndex:(NSInteger)index {
    NSArray *photos = [self.viewModel.questionPic componentsSeparatedByString:@","];
    if(nil == photos || 0 == photos.count) return;
    
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithDictionary:0];
    [params setObject:photos forKey:@"photos"];
    [params setObject:@(index) forKey:@"index"];
    [params setObject:self.viewController forKey:@"topViewController"];
    
    [JDRouter openURL:@"router://JDISVProductDetailSDKModule/bQPDPMediaPopupViewController"
                  arg:params
                error:nil
           completion:^(UIViewController*  _Nullable controller) {
    }];
}

- (void)displayRetrunTips {
    
    if (72 != self.viewModel.returnModel) { return; }
    if (10001 != self.viewModel.afsServiceStatus) { return; }
    if (nil != _returnTipView) { return; }
    
    [self.pickUpInfoView addSubview:self.returnTipView];
    CGFloat tipViewHeight = [self.returnTipView getHeight];
    self.viewModel.returnTipsHeight = tipViewHeight;
    [self.returnTipView mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(self.pickUpInfoView).offset(18);
        make.trailing.mas_equalTo(self.pickUpInfoView).offset(-18);
        make.height.mas_equalTo(tipViewHeight);
        make.top.mas_equalTo(self.pickUpAddress.mas_bottom).offset(8);
        make.bottom.mas_equalTo(self.pickUpInfoView).offset(0);
    }];
    self.returnTypeView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    
    [self.viewModel updataViewModel];
    
    [self.tableView reloadData];
}

-(JDISVKAApplyDetailReturntipView*) returnTipView{
    if(!_returnTipView){
        _returnTipView = [[JDISVKAApplyDetailReturntipView alloc] initWithFrame:CGRectZero];
    }
    return _returnTipView;
}

@end
