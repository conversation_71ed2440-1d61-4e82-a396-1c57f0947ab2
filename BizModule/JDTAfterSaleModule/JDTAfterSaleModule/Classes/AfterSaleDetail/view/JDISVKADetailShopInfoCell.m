//
//  JDISVKADetailShopInfoCell.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailShopInfoCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKADetailShopInfoViewModel.h"
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

#import "JDISVKAAfterSaleAction.h"
#import <JDISVReactiveObjCModule/RACSubject.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
@interface JDISVKADetailShopInfoCell()

@property (weak, nonatomic) IBOutlet UIView *mainView;

@property (weak, nonatomic) IBOutlet UILabel *merchantsName;

@property (weak, nonatomic) IBOutlet UIButton *merchantsTag;

@property (weak, nonatomic) IBOutlet UIImageView *type1ImageVIew;

@property (weak, nonatomic) IBOutlet UILabel *type1Label;

@property (weak, nonatomic) IBOutlet UIImageView *type2ImageView;

@property (weak, nonatomic) IBOutlet UILabel *type2Label;


@property (weak, nonatomic) IBOutlet UIImageView *contactImageView;

@property (weak, nonatomic) IBOutlet UIView *divideLineView;

#pragma mark 约束

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *merchantsTagWidthConstraint;

@property (nonatomic,strong)JDISVKADetailShopInfoViewModel *viewModel;

@end

@implementation JDISVKADetailShopInfoCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self configCell];
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.mainView.layer.masksToBounds = YES;
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
}

- (void)render{
    self.type1Label.text = self.viewModel.type1Value;
    self.type2Label.text = self.viewModel.type2Value;
    self.merchantsName.text = self.viewModel.cellMainName;
    
    NSString * merchantsTagText;
    if (self.viewModel.returnType == 1) {
        self.type1ImageVIew.image = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"]];
        self.type2ImageView.image = [UIImage ka_iconWithName:JDIF_ICON_PHONE_BIG imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"]];
        
        [self.merchantsTag setTitle:Lang(@"after_sale_return_shop") forState:UIControlStateDisabled];
        merchantsTagText = Lang(@"after_sale_return_shop");
        [self.divideLineView setHidden:YES];
        [self.contactImageView setHidden:YES];
    }else{
        self.type1ImageVIew.image = [UIImage ka_iconWithName:JDIF_ICON_CLOCK_LINE_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"]];
        self.type2ImageView.image = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"]];
        
        self.divideLineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        self.contactImageView.image = [UIImage ka_iconWithName:JDIF_ICON_PHONE_BIG imageSize:CGSizeMake(24, 24) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
        
        
        [self.merchantsTag setTitle:Lang(@"after_sale_return_store") forState:UIControlStateDisabled];
        merchantsTagText = Lang(@"after_sale_return_store");
        if ([self.viewModel.phone jdcd_validPhone]) {
            [self.divideLineView setHidden:NO];
            [self.contactImageView setHidden:NO];
            [self.contentView jd_addTapAction:@selector(clickPhone) withTarget:self];
        }else{
            [self.divideLineView setHidden:YES];
            [self.contactImageView setHidden:YES];
        }
    }
    CGSize merchantsTagSize = [merchantsTagText jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake(300, 100)];
    self.merchantsTagWidthConstraint.constant = merchantsTagSize.width+10;
}

- (void)clickPhone{
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithDetailClickPhone];
    [self.delegate sendNext:action];
}

- (void)configCell{
    self.backgroundColor = [UIColor clearColor];
    self.mainView.backgroundColor = [UIColor clearColor];
    self.mainView.layer.cornerRadius = 12;
    self.merchantsName.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T6",UIFontWeightSemibold);
    self.merchantsName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.type1Label.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.type1Label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"];
    self.type2Label.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.type2Label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"];
    
    //L3 label，自定义圆角大小
    [self.merchantsTag setEnabled:NO];
    [self.merchantsTag renderL3WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R52"]];
    [self.merchantsTag.titleLabel setFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]];
    [self.merchantsTag jdisv_setBackgroundColorPicker:JDISVColorPickerWithKeyAndAlpha(@"#C9", 0.10) forState:UIControlStateDisabled];
    [self.merchantsTag jdisv_setTitleColorPicker:JDISVColorPickerWithKey(@"#C9") forState:UIControlStateDisabled];
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
}


@end
