//
//  JDISVKADetailHeaderView.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailHeaderView.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKADetailProgressView.h"
#import "JDISVKADetailHeaderViewModel.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
@interface JDISVKADetailHeaderView()

@property (nonatomic,strong)JDISVKADetailProgressView *progressMainView;

@end

@implementation JDISVKADetailHeaderView

- (void)config{
    [self addSubview:self.progressMainView];
    self.backgroundColor = [UIColor clearColor];
    [self.progressMainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"]);
        make.bottom.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]);
        make.leading.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
        make.width.mas_equalTo(self.frame.size.width - 2 * [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
        make.height.mas_equalTo(110);
    }];
    [self.progressMainView.progressView setHidden:YES];
}

- (void)render:(JDISVKADetailHeaderViewModel *)viewModel{
    if (viewModel.isShowProgressView) {
        [self.progressMainView.progressView setHidden:NO];
    }else{
        [self.progressMainView.progressView setHidden:YES];
    }
    [self.progressMainView configProgressView:viewModel];
}


- (JDISVKADetailProgressView *)progressMainView{
    if (!_progressMainView) {
        _progressMainView = [[JDISVKADetailProgressView alloc] init];
    }
    return _progressMainView;
}

@end
