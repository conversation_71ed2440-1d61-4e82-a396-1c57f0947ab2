//
//  JDISVKADetailReturnMoneyCell.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailReturnMoneyCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKADetailReturnMoneyViewModel.h"
#import <JDISVKAUIKitModule/KAPriceLabel.h>
#import <JDISVMasonryModule/Masonry.h>
@interface JDISVKADetailReturnMoneyCell()

@property (weak, nonatomic) IBOutlet UIView *mainView;

@property (weak, nonatomic) IBOutlet UILabel *returnMoneyLabel;

//@property (weak, nonatomic) IBOutlet UILabel *returnMoneyValue;

@property (nonatomic,strong)KAPriceLabel *returnMoneyValue;

@property (nonatomic,strong)JDISVKADetailReturnMoneyViewModel *viewModel;

#pragma mark 约束

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewLeftConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewTopConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewBottomConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mainViewRightConstraint;

@end


@implementation JDISVKADetailReturnMoneyCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self setupCell];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
}

- (void)render{
    [self.returnMoneyValue configTextWithPrice:[self.viewModel.skuPrice floatValue] type:KAPriceTypeP3 colorType:@"#C9"];
}


- (void)setupCell{
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.returnMoneyLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
    self.returnMoneyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.returnMoneyLabel.text = Lang(@"after_sale_detail_refund_money");
    
    self.mainViewLeftConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.mainViewTopConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    self.mainViewBottomConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.mainViewRightConstraint.constant = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    
    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.mainView.layer.masksToBounds = YES;
    self.returnMoneyValue = [[KAPriceLabel alloc] init];
    [self.contentView addSubview:self.returnMoneyValue];
    [self.returnMoneyValue mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.returnMoneyLabel.mas_trailing).offset(36);
        make.centerY.mas_equalTo(self.returnMoneyLabel);
    }];
}

@end
