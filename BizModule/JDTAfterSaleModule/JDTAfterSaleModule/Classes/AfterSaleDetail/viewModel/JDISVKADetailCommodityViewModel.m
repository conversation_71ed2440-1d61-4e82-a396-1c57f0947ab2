//
//  JDISVKADetailCommodityViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailCommodityViewModel.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
@implementation JDISVKADetailCommodityViewModel

- (NSString *)identifier{
    return @"JDISVKADetailCommodityCell";
}

- (CGFloat)cellHeight{
    CGFloat commodityCellHeight = (self.isShowContactView ? 200 : 156) + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    return commodityCellHeight;
}

- (void)updataViewModel:(id)model{
}

@end
