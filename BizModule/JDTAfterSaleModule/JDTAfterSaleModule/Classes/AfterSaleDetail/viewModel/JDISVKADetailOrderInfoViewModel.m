//
//  JDISVKADetailOrderInfoViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailOrderInfoViewModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import "NSString+JDISVAfterExtension.h"
@interface JDISVKADetailOrderInfoViewModel()

@property(nonatomic,assign)CGFloat orderInfoCellHeight;

@end

@implementation JDISVKADetailOrderInfoViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
    }
    return self;
}

- (void)updataViewModel{
    self.orderInfoCellHeight = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]+12*2;
    //WT_TODO
//    self.orderType = 75;
    //本地生活屏蔽 快递信息和退货方式
    if(self.orderType == 75){
        self.returnModelStr = nil;
        self.allowExpressNoInput = NO;
    }
    
    if (self.serverOrderNumber) {
        [self getCellHeightWithKey:@"serverOrderNumber" withLabel:Lang(@"after_sale_detail_service_id")];

    }
    if (self.applyTime) {
        [self getCellHeightWithKey:@"applyTime" withLabel:Lang(@"after_sale_detail_time")];
    }
    if (self.serverType) {
        [self getCellHeightWithKey:@"serverType" withLabel:Lang(@"after_sale_detail_reason")];
    }
    if (self.reasonName){
        [self getCellHeightWithKey:@"reasonName" withLabel:Lang(@"after_sale_detail_type")];
    }
    if (self.questionDesc){
        [self getCellHeightWithKey:@"questionDesc" withLabel:Lang(@"after_sale_desc_reason")];
    }
//    if (self.questionPic){
//        [self getCellHeightWithKey:@"questionPic" withLabel:@""];
//    }
    if (self.returnModelStr) {
        [self getCellHeightWithKey:@"returnType" withLabel:Lang(@"after_sale_return_title")];
    }
    //上门取件
    if(self.pickStatusStr){
        [self getCellHeightWithKey:@"pickUpStatus" withLabel:Lang(@"after_sale_apply_pickUp_status")];
    }
    if(self.pickCustomerInfo.contactName && self.pickCustomerInfo.contactTel){
        [self getCellHeightWithKey:@"pickUpInfo" withLabel:Lang(@"after_sale_apply_pickUp_info")];
    }
    
    if(self.pickCustomerInfo.expressCompany){
        [self getCellHeightWithKey:@"pickUpCarrier" withLabel:Lang(@"after_sale_apply_pickUp_logistics_name")];
    }
    
//    if(self.pickCustomerInfo.expressNo){
//        [self getCellHeightWithKey:@"pickUpTrackingNum" withLabel:Lang(@"after_sale_apply_pickUp_logistics_number")];
//    }
    if(self.pickCustomerInfo.expressInfos.count > 0){
        [self getCellHeightWithKey:@"pickUpTrackingNum" withLabel:Lang(@"after_sale_apply_pickUp_logistics_number")];
    }
    
//    if(self.afsApplyExpandInfoVo.reserveDateBegin){
//        [self getCellHeightWithKey:@"pickUpTime" withLabel:Lang(@"after_sale_apply_pickUp_time")];
//    }
    
    //银行卡信息
    if(self.refundType){
        [self getCellHeightWithKey:@"refundType" withLabel:Lang(@"after_sale_refund_title")];
    }
    if(self.bankName){
        [self getCellHeightWithKey:@"bankName" withLabel:Lang(@"after_sale_apply_bankName")];
    }
    if(self.bankCode){
        [self getCellHeightWithKey:@"bankCardNum" withLabel:Lang(@"after_sale_apply_bankCard")];
    }
    if(self.bankAccountHolder){
        [self getCellHeightWithKey:@"bankAccountName" withLabel:Lang(@"after_sale_apply_bankAccountName")];
    }
    if(self.bankTel){
        [self getCellHeightWithKey:@"phoneNumber" withLabel:Lang(@"after_sale_apply_phoneNumber")];
    }
    
//    if (self.allowExpressNoInput) {
//        [self getCellHeightWithKey:@"deliveryInfo"];
//    }
    if (self.deliveryInfo) {
            [self getCellHeightWithKey:@"deliveryInfo" withLabel:Lang(@"after_sale_detail_transport")];
        }
    if (self.isShowReceivingArea) {
        [self getCellHeightWithKey:@"isShowReceivingArea" withLabel:Lang(@"after_sale_detail_transport")];
    }
//    if (self.invoiceNumber) {
//        [self getCellHeightWithKey:@"invoiceNumber" withLabel:Lang(@"after_sale_detail_send_id")];
//    }
    if(self.expressInfos.count > 0){
        [self getCellHeightWithKey:@"invoiceNumber" withLabel:Lang(@"after_sale_detail_send_id")];
    }
    //待取件 && expressList > 1
    if(self.pickStatus == 1 && self.pickCustomerInfo.expressInfos.count > 1){
        self.pickStatusTipStr = [NSString stringWithFormat:Lang(@"after_sale_apply_pickUp_logistics_tip"),self.expressInfos.count];
        [self getCellHeightWithKey:@"pickUpStatusTip" withLabel:self.pickStatusTipStr];
    }
    if(self.returnExpressCompany){
        [self getCellHeightWithKey:@"returnCarrier" withLabel:Lang(@"after_sale_apply_return_logistics_name")];
    }
}

- (void)getCellHeightWithKey:(NSString *)str withLabel:(NSString *)label{
    CGFloat leftLabelHeight = [self getLabelHeight:label WithKey:str];
    if( [str isEqualToString:@"reasonName"]){
        
        NSString* reasonName = self.reasonName;
        CGFloat margin = 2*[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        CGFloat width = UIScreen.mainScreen.bounds.size.width - 149-margin;
        CGSize size =  [reasonName jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(width, 200)];
        
        CGFloat reasonValueHeight = 32;//申请原因内容的高度
        if(size.height < 32){
            reasonValueHeight = 32;
        }else{
            reasonValueHeight = size.height+8;
        }
        self.reasonInfoViewHeight = reasonValueHeight>leftLabelHeight?reasonValueHeight:leftLabelHeight;
        self.orderInfoCellHeight += self.reasonInfoViewHeight;

    }else if([str isEqualToString:@"questionDesc"]){
        
        NSString* questionDesc = self.questionDesc;
        CGFloat margin = 2*[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
//        CGFloat width = UIScreen.mainScreen.bounds.size.width - 149-margin;
        CGFloat width = UIScreen.mainScreen.bounds.size.width - 130 - 18 - margin;
        CGSize size =  [questionDesc jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(width, 200)];
        
        CGFloat questionDescValueHeight = 32;//申请原因内容的高度
        if(size.height < 32){
            questionDescValueHeight = 32;
        }else{
            questionDescValueHeight = size.height+8;
        }
        self.questionDescViewHeight = questionDescValueHeight > leftLabelHeight ? questionDescValueHeight : leftLabelHeight;
        self.photosViewHeight = [self photosHeight];
        self.questionDescViewHeight += self.photosViewHeight;
        
        self.orderInfoCellHeight += self.questionDescViewHeight;

    }else if ([str isEqualToString:@"isShowReceivingArea"]) {
        CGFloat margin = 2*[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        CGFloat width = UIScreen.mainScreen.bounds.size.width - 148-margin;

        CGSize nameSize = [self.receiverName jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(width, MAXFLOAT))];

        CGSize addressSize = [self.receiverAddress jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(width, MAXFLOAT))];
        self.receiverInfoViewHeight = 13 +nameSize.height+6+17+6+addressSize.height+10;
        self.orderInfoCellHeight += self.receiverInfoViewHeight;
        
    }else if([str isEqualToString:@"bankName"]){
        CGFloat margin = 2*[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        CGFloat width = UIScreen.mainScreen.bounds.size.width - 149-margin;
        //TODOH
        CGSize size =  [self.bankName jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular] constraintsSize:CGSizeMake(width, 500)];
        CGFloat bankNameValueHeight = 32;//银行名称内容高度
        if(size.height < 32){
            bankNameValueHeight = 32;
        }else{
            bankNameValueHeight = size.height+15;
        }
        self.bankNameViewHeight = bankNameValueHeight>leftLabelHeight?bankNameValueHeight:leftLabelHeight;
        self.orderInfoCellHeight += self.bankNameViewHeight;
        
    }else if ([str isEqualToString:@"pickUpInfo"]){
        
        CGFloat margin = 2*[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        CGFloat width = UIScreen.mainScreen.bounds.size.width - 148-margin;

        CGSize nameSize = [self.pickCustomerInfo.contactName jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(width, MAXFLOAT))];
        
        CGSize addressSize = [self.pickCustomerInfo.addressInfo.address jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(width, MAXFLOAT))];
        self.pickUpInfoViewHeight = 13 +nameSize.height+6+17+6+addressSize.height+10;
        
        self.pickUpInfoViewHeight += self.returnTipsHeight;
        self.orderInfoCellHeight += self.pickUpInfoViewHeight;
    }
    else if ([str isEqualToString:@"pickUpStatusTip"]){
        CGFloat height = [label jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightRegular] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-33-12, CGFLOAT_MAX)].height+10+10;
        self.pickUpStatusTipViewHeight = height;
        self.orderInfoCellHeight += self.pickUpStatusTipViewHeight;
    }else if([str isEqualToString:@"pickUpTrackingNum"]){
        if (self.pickCustomerInfo.expressInfos.count > 1){
            leftLabelHeight = 6+(20+8)*self.pickCustomerInfo.expressInfos.count;
        }
        for(applyDetailAfsExpressInfoVo* info in self.pickCustomerInfo.expressInfos){
            if(info.umsList.count>0){
                leftLabelHeight += 35;
            }
        }
        self.pickUpTrackingNumViewHeight = leftLabelHeight;
        
        self.orderInfoCellHeight += self.pickUpTrackingNumViewHeight;
    }else if([str isEqualToString:@"invoiceNumber"]){
        if (self.expressInfos.count > 1){
            leftLabelHeight = 6+(20+8)*self.expressInfos.count;
        }
        for(applyDetailAfsExpressInfoVo* info in self.expressInfos){
            if(info.umsList.count>0){
                leftLabelHeight += 35;
            }
        }
        self.invoiceInfoViewHeight = leftLabelHeight;
        self.orderInfoCellHeight += self.invoiceInfoViewHeight;
    }
    
//    else if([str isEqualToString:@"questionPic"]){
//        if ([NSString isEmpty:self.questionPic]) { return; }
//        
//        NSArray *photos = [self.questionPic componentsSeparatedByString:@","];
//        if (nil == photos || 0 == photos.count) { return; }
//        
//        NSInteger photoCount = photos.count;
//        
//        CGFloat photoTopMargin = 10.0;
//        CGFloat photoBottomMargin = 10.0;
//        CGFloat photoMiddleMargin = 0.0;
//        NSInteger photoRowCount = 1;
//        CGFloat photosRowViewHeight = 60.0;
//        
//        if (0 == self.photosViewHeight){
//            self.photosViewHeight = photosRowViewHeight;
//        }
//        
//        if (nil != photos && 0 < photoCount){
//            photoRowCount = photoCount > 3 ? 2 : 1;
//            photoMiddleMargin = photoRowCount == 1 ? 0.0 : 10.0;
//            self.photosViewHeight = photosRowViewHeight * photoRowCount;
//        }
//        CGFloat photosHeight = photoTopMargin + self.photosViewHeight + photoMiddleMargin + photoBottomMargin;
//        
//        self.photosViewHeight = photosHeight;
//        self.orderInfoCellHeight += self.photosViewHeight;
//    }
    
    else{
        self.orderInfoCellHeight += leftLabelHeight;
    }
}

- (CGFloat )getLabelHeight:(NSString *)label WithKey:(NSString *)str{
    CGSize size =  [label jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular] constraintsSize:CGSizeMake(100, 300)];
    CGFloat height = 32;
    if(size.height>32){
        height = size.height+15;
    }
    if([str isEqualToString:@"serverOrderNumber"]){
        self.serverIDViewHeight = height;
    }else if ([str isEqualToString:@"applyTime"]){
        self.applyTimeViewHeight = height;
    }else if ([str isEqualToString:@"serverType"]){
        self.afterSaleTypeViewHeight = height;
    }else if ([str isEqualToString:@"returnType"]){
        self.returnTypeViewHeight = height;
    }else if ([str isEqualToString:@"pickUpStatus"]){
        self.pickUpStatusViewHeight = height;
    }else if([str isEqualToString:@"pickUpCarrier"]){
        self.pickUpCarrierViewHeight = height;
    }else if([str isEqualToString:@"pickUpTrackingNum"]){
        self.pickUpTrackingNumViewHeight = height;
    }else if ([str isEqualToString:@"pickUpTime"]){
        self.pickUpTimeViewHeight = height;
    }
    else if([str isEqualToString:@"refundType"]){
        self.refundTypeViewHeight = height;
    }
    else if([str isEqualToString:@"bankCardNum"]){
        self.bankCardNumViewHeight = height;
    }else if([str isEqualToString:@"bankAccountName"]){
        self.bankAccountNameViewHeight = height;
    }else if([str isEqualToString:@"phoneNumber"]){
        self.phoneNumberViewHeight = height;
    }else if([str isEqualToString:@"returnCarrier"]){
        self.returnCarrierViewHeight = height;
    }
    else if ([str isEqualToString:@"deliveryInfo"]){
        self.deliveryInfoViewHeight = height;
    }else if ([str isEqualToString:@"invoiceNumber"]){
        self.invoiceInfoViewHeight = height;
    }
    return height;
}

- (CGFloat)photosHeight {
    if ([NSString isEmpty:self.questionPic]) { return 0.0; }
    
    NSArray *photos = [self.questionPic componentsSeparatedByString:@","];
    if (nil == photos || 0 == photos.count) { return 0.0; }
    
    NSInteger photoCount = photos.count;
    
    CGFloat photoTopMargin = 10.0;
    CGFloat photoBottomMargin = 10.0;
    CGFloat photoMiddleMargin = 0.0;
    NSInteger photoRowCount = 1;
    CGFloat photosRowViewHeight = 60.0;
    
    if (0 == self.photosViewHeight){
        self.photosViewHeight = photosRowViewHeight;
    }
    
    if (nil != photos && 0 < photoCount){
        photoRowCount = photoCount > 3 ? 2 : 1;
        photoMiddleMargin = photoRowCount == 1 ? 0.0 : 10.0;
        self.photosViewHeight = photosRowViewHeight * photoRowCount;
    }
    CGFloat photosHeight = photoTopMargin + self.photosViewHeight + photoMiddleMargin + photoBottomMargin;
    self.photosViewHeight = photosHeight;
    return self.photosViewHeight;
}

- (NSString *)identifier{
    return @"JDISVKADetailOrderInfoCell";
}

- (CGFloat)cellHeight{
    return _orderInfoCellHeight;
}


@end
