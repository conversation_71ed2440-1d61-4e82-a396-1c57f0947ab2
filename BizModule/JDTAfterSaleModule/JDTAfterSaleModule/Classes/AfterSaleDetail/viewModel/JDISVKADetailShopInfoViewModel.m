//
//  JDISVKADetailShopInfoViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailShopInfoViewModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@interface JDISVKADetailShopInfoViewModel()

@property(nonatomic,assign)CGFloat shopInfoCellHeight;

@end

@implementation JDISVKADetailShopInfoViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (NSString *)identifier{
    return @"JDISVKADetailShopInfoCell";
}

- (CGFloat)cellHeight{
    return self.shopInfoCellHeight;
}

- (void)updataViewModel{
    self.shopInfoCellHeight = 0;
    CGSize type1Size;
    CGSize type2Size;
    NSString * merchantsTagText;//eg:退货商家/退货门店
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    if (_returnType == 1) {
        type1Size = [self.type1Value jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width-59-w3*2, MAXFLOAT))];
        type2Size = [self.type2Value jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width-59-w3*2, MAXFLOAT))];
        merchantsTagText =  Lang(@"after_sale_return_shop");

    }else{
        type1Size = [self.type1Value jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width-59-w3*2, MAXFLOAT))];
        type2Size = [self.type2Value jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width-100-w3*2, MAXFLOAT))];
        merchantsTagText =  Lang(@"after_sale_return_store");
    }
    
    CGSize merchantsTagSize = [merchantsTagText jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake(300, 100)];
    CGFloat merchantsTagTextWidth = merchantsTagSize.width+10;

    CGFloat nameWidth = UIScreen.mainScreen.bounds.size.width-w3*2-18-12-merchantsTagTextWidth-12;
    CGFloat nameHeight =  [self.cellMainName jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightSemibold] constraintsSize:(CGSizeMake(nameWidth, MAXFLOAT))].height;
    nameHeight = ceil(nameHeight);
    CGFloat w1 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"];
    w1 = 18;
    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    w2 = 20;
    self.shopInfoCellHeight = w1+ceil(nameHeight)+8+ceil(type1Size.height)+8+ceil(type2Size.height)+w2;
}

@end
