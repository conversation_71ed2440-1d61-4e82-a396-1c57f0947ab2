//
//  JDISVKADetailCommodityViewModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKAApplyDetailBaseViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKADetailCommodityViewModel : JDISVKAApplyDetailBaseViewModel

@property (nonatomic,assign)BOOL isShowContactView;    //是否展示联系客户

@property (nonatomic,copy)NSString *shopName;

@property (nonatomic,copy)NSString *skuName;

@property (nonatomic,copy)NSString *skuImage;

@property (nonatomic,assign)NSInteger skuCount;
@property(nonatomic,assign)NSInteger appliedCount;

@property (nonatomic,copy)NSString *skuSingleShouldPrice;

@property (nonatomic,strong) NSArray* skus;
@property (assign,nonatomic) NSInteger venderId;
@property (assign,nonatomic) NSInteger shopId;

@end

NS_ASSUME_NONNULL_END
