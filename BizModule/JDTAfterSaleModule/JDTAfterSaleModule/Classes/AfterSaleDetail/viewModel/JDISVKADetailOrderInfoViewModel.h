//
//  JDISVKADetailOrderInfoViewModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKAApplyDetailBaseViewModel.h"
#import "JDISVKADetailOrderInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKADetailOrderInfoViewModel : JDISVKAApplyDetailBaseViewModel

@property (nonatomic,copy)NSString *__nullable serverOrderNumber;

@property (nonatomic,copy)NSString *__nullable applyTime;

@property (nonatomic,copy)NSString *serverType;
@property(nonatomic,assign)NSInteger customerExpect;//20 换新  10 退货

@property(nonatomic,assign)NSInteger returnModel;   //40:客户发货 72：上门取件
@property(nonatomic,assign)NSInteger afsServiceStatus;   // 10001 待审核

@property (nonatomic,copy)NSString *returnModelStr;

@property (nonatomic,strong)JDISVApplyExpandInfoReq *afsApplyExpandInfoVo;

@property (nonatomic,strong)applyDetailAfsContactInfoVo *pickCustomerInfo;

@property(nonatomic,assign)NSInteger pickStatus;   //取件状态：1待取件、 2已取件
@property (nonatomic,copy)NSString *pickStatusTipStr;
@property (nonatomic,copy)NSString *pickStatusStr;//取件状态展示：待取件、已取件。支持国际化

@property(nonatomic,assign)NSInteger refundType;
@property (nonatomic,copy)NSString *refundTypeStr;
@property (nonatomic,copy)NSString *bankName;
@property (nonatomic,copy)NSString *bankCode;
@property (nonatomic,copy)NSString *bankAccountHolder;
@property (nonatomic,copy)NSString *bankTel;



@property (nonatomic,copy)NSString *reasonName;

@property (nonatomic,copy)NSString *questionDesc;
/**>
 * Customer provided pictures of defective products
 * 123.jpg,456.jpg
 */
@property (nonatomic,copy)NSString *questionPic;

@property (nonatomic,assign) NSInteger orderType;
@property (nonatomic,assign) BOOL orderSelf;//是否自营订单

@property (nonatomic,copy)NSString *__nullable deliveryInfo;

@property (nonatomic,assign)bool isShowReceivingArea;

@property (nonatomic,copy)NSString *receiverAddress;

@property (nonatomic,copy)NSString *receiverName;
@property (nonatomic,copy)NSString *firstName;
@property (nonatomic,copy)NSString *lastName;

@property (nonatomic,copy)NSString *receiverPhone;

@property (nonatomic,copy)NSArray *expressInfos;
@property (nonatomic,copy)NSString *returnExpressCompany;

@property (nonatomic,copy)NSString *__nullable invoiceNumber;

@property (nonatomic,assign)CGFloat serverIDViewHeight;
@property (nonatomic,assign)CGFloat applyTimeViewHeight;
@property (nonatomic,assign)CGFloat reasonInfoViewHeight;
@property (nonatomic,assign)CGFloat questionDescViewHeight;
@property (nonatomic,assign)CGFloat afterSaleTypeViewHeight;

//TODOH
@property (nonatomic,assign)CGFloat refundTypeViewHeight;
@property (nonatomic,assign)CGFloat bankNameViewHeight;
@property (nonatomic,assign)CGFloat bankCardNumViewHeight;
@property (nonatomic,assign)CGFloat bankAccountNameViewHeight;
@property (nonatomic,assign)CGFloat phoneNumberViewHeight;

@property (nonatomic,assign)CGFloat returnTypeViewHeight;
@property (nonatomic,assign)CGFloat pickUpStatusTipViewHeight;
@property (nonatomic,assign)CGFloat pickUpStatusViewHeight;
@property (nonatomic,assign)CGFloat pickUpInfoViewHeight;
@property (nonatomic,assign)CGFloat pickUpCarrierViewHeight;
@property (nonatomic,assign)CGFloat pickUpTrackingNumViewHeight;
@property (nonatomic,assign)CGFloat pickUpTimeViewHeight;

@property (nonatomic,assign)CGFloat deliveryInfoViewHeight;
@property (nonatomic,assign)CGFloat receiverInfoViewHeight;
@property (nonatomic,assign)CGFloat invoiceInfoViewHeight;
@property (nonatomic,assign)CGFloat returnCarrierViewHeight;
@property (nonatomic,assign)BOOL allowExpressNoInput;  //是否展示客户返件物流单号输入框标记
@property (nonatomic,assign)CGFloat photosViewHeight;
@property (nonatomic,assign)CGFloat returnTipsHeight; 

@end

NS_ASSUME_NONNULL_END
