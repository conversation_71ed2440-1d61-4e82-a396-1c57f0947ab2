//
//  JDISVKADetailHeaderViewModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKADetailHeaderViewModel : NSObject

@property (nonatomic,assign)bool isShowProgressView;

@property (nonatomic,copy)NSString *currentProgressStatusText;

@property (nonatomic,copy)NSArray *progressStatusTextArray;

@property (nonatomic,assign)NSInteger currentIndex;

@property(nonatomic,assign)NSInteger afsServiceStep;

@property (nonatomic,copy)NSString *afsServiceStepName;

- (NSString*)identifier;

- (CGFloat)cellHeight;

- (void)uploadViewModel;

@end

NS_ASSUME_NONNULL_END

