//
//  JDISVKADetailHeaderViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/20.
//

#import "JDISVKADetailHeaderViewModel.h"
#import "JDISVKADetailOrderInfoModel.h"
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVYYModelModule/YYModel.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@implementation JDISVKADetailHeaderViewModel

- (NSString *)identifier{
    return @"JDISVKADetailHeaderView";
}

- (CGFloat)cellHeight{
    
    CGFloat maxNodeHeight = 0;
    
    for (NSDictionary *dic in self.progressStatusTextArray) {
        
        JDISVKAAAfsServiceProcessNodeStatusVo *node = [JDISVKAAAfsServiceProcessNodeStatusVo yy_modelWithDictionary:dic];
        
        NSString *nodeText = node.text;
        
        CGSize nodeLabSize = [nodeText jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11"] constraintsSize:CGSizeMake(55, 500)];
        
        if(nodeLabSize.height>maxNodeHeight){
            maxNodeHeight = nodeLabSize.height;
        }
    }

    return 110-15+maxNodeHeight + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    
}


- (CGFloat)safeTop {
    CGFloat safeTop = 20;
    if (@available(iOS 11.0, *)) {
        CGFloat tempSafeTop = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
        if (tempSafeTop > 0) {
            safeTop = tempSafeTop;
        }
    }
    return safeTop;
}

@end
