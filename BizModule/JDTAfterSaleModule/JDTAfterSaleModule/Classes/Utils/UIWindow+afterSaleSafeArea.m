//
//  UIWindow+afterSaleSafeArea.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "UIWindow+afterSaleSafeArea.h"

@implementation UIWindow (afterSaleSafeArea)

- (UIEdgeInsets)afterSale_layoutInsets {
    if (@available(iOS 11.0, *)) {
        UIEdgeInsets safeAreaInsets = self.safeAreaInsets;
        if (safeAreaInsets.bottom > 0) {
            return safeAreaInsets;
        }
        return UIEdgeInsetsMake(20, 0, 0, 0);
    }
    return UIEdgeInsetsMake(20, 0, 0, 0);
}

- (CGFloat)afterSale_navigationHeight {
    CGFloat statusBarHeight = [self afterSale_layoutInsets].top;
    return statusBarHeight + 44;
}

@end
