//
//  NSString+Extension.h
//  Zode
//
//  Created by ext.zhangchen41 on 2024/8/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSString (Extension)
+ (BOOL)isEmpty:(NSString *)str;
+ (NSString *)trim:(NSString *)str;
+ (BOOL)isValidEmail:(NSString *)email;
+ (BOOL)isNumericString:(NSString *)string;
+ (BOOL)isAlphaNumericString:(NSString *)string;
+ (NSString *)removeSpecialCharcters:(NSString *)string;

@end

NS_ASSUME_NONNULL_END
