//
//  NSString+Extension.m
//  Zode
//
//  Created by ext.zhangchen41 on 2024/8/22.
//

#import "NSString+AfterExtension.h"

@implementation NSString (Extension)

+ (BOOL)isEmpty:(NSString *)str {
    
    if (nil == str) { return YES; }
    
    if ([NSNull class] == [str class]) { return YES; }
    
    NSString *string = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    
    if (string.length == 0) { return YES; }
    if ([@"" isEqualToString:string]) { return YES; }
    
    return NO;
}

+ (NSString *)trim:(NSString *)str {
    
    if ([NSNull class] == [str class]) { return @""; }
    
    NSString *string = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
    
    return string;
}

+ (BOOL)isValidEmail:(NSString *)email {
    NSString *emailRegex = @"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}";
    NSPredicate *emailTest = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", emailRegex];
    return [emailTest evaluateWithObject:email];
}

+ (BOOL)isNumericString:(NSString *)string {
    NSCharacterSet *nonNumericSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
    return ([string rangeOfCharacterFromSet:nonNumericSet].location == NSNotFound);
}

+ (BOOL)isAlphaNumericString:(NSString *)string {
    NSCharacterSet *alphaNumericInvertedSet = [[NSCharacterSet alphanumericCharacterSet] invertedSet];
    return ([string rangeOfCharacterFromSet:alphaNumericInvertedSet].location == NSNotFound);
}

+ (NSString *)removeSpecialCharcters:(NSString *)input {
    NSCharacterSet *allowedCharacters = [[NSCharacterSet alphanumericCharacterSet] invertedSet];
    NSString *output = [[input componentsSeparatedByCharactersInSet:allowedCharacters] componentsJoinedByString:@""];
    NSLog(@"input = %@ === outPut: %@", input, output);
    return output;
}

@end
