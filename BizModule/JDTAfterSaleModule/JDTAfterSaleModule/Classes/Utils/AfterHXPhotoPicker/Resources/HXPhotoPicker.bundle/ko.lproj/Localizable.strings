"下一步" = "다음 단계";
"当前没有可编辑的资源" = "현재 편집 할만 한 자원이 없다";
"当前没有可删除的资源" = "지금은 삭제 할만 한 자원이 없다";
"确定删除这张照片吗?" = "이 사진을 삭제 할까요?";
"确定删除这个视频吗?" = "이 동영상 삭제 할까요?";
"删除" = "삭제";
"下载失败，请重试！" = "실패를 다운로드 할 테니, 다시 시험 해 보십시오!";
"编辑" = "편집";
"正在下载iCloud上的资源" = "iCloud의 자원을 다운로드하고 있다";
"原始值" = "원시적인 가치";
"正方形" = "정방형";
"提示" = "제시";
"是否删除此照片" = "이 사진은 삭제 여부";
"还原" = "환원하다";
"选择" = "선택";
"旋转" = "회전";
"选择照片时不能选择视频" = "사진을 선택 할 때는 동영상을 선택 할 수 없다";
"没有照片可选!" = "사진 컷은 없어요!";
"张照片" = "사진";
"个视频" = "동영상";
"Photos" = "사진";
"Photo" = "사진";
"Videos" = "동영상";
"Video" = "동영상";
"今天" = "오늘";
"昨天" = "어제";
"返回"= "귀환";
"取消" = "취소";
"预览" = "본";
"原图" = "원본";
"设置" = "설치";
"确定" = "확정";
"完成" = "완성";
"关闭" = "폐쇄";
"拍摄" = "촬영";
"拍摄失败" = "촬영에 실패하다";
"裁剪" = "맞 춤";
"抱歉" = "죄 송";
"重拍" = "다시 찍";
"相机" = "카메라";
"照片" = "사진";
"视频" = "동영상";
"相册" = "사진첩";
"加载中" = "적재 중";
"处理中" = "처리 중";
"点击拍照" = "사진을 클릭하다";
"长按录像" = "녹화하다";
"点击拍照，长按录像" = "사진을 클릭하고 촬영하다";
"处理失败,请重试!" = "실패를 처리하 려면, 다시 시험 해 보십시오!";
"无法使用相机!" = "카메라를 사용 할 수 없다!";
"照片正在下载" = "사진이 다운로드 되고 있다";
"已达最大数!" = "이미 최대의 숫자에 달 했다!";
"照片已达最大数!" = "사진이 이미 최대치에 달 했다!";
"视频已达最大数!" = "동영 상이 이미 가장 많다!";
"最多只能选择%ld张图片" = "가장 많은 %ld장의 그림을 선택 할 수 있다";
"最多只能选择%ld个视频" = "최대 %ld개 동영상만 꼽을 수 있다";
"最多只能选择%ld个" = "최대 %ld개만 선택 할 수 있다";
"图片不能和视频同时选择" = "사진은 동영상과 동시에 선택 할 수 없다";
"视频不能和图片同时选择" = "영상은 그림과 함께 선택 할 수 없다";
"视频少于%ld秒，无法选择" = "동영 상은 %ld초 이하 여서 선택 할 수 없다";
"视频大于%ld秒，无法选择" = "%ld초 이상 된 비디오를 선택 할 수 없습니다";
"录制时间少于%.0f秒" = "녹화 시간은 %.0f초보다적다";
"请在设置-隐私-相机中允许访问相机" = "설치하십시오-사생활-카메라에 카메라를 허용하다";
"无法访问照片\n请点击这里前往设置中允许访问照片" = "사진을 찾을 수 없다\n이곳을 클릭 해서 사진을 방문 하도 록 하세요";
"无法使用相机" = "카메라를 사용 할 수 없다";
"无法访问照片，请前往设置中允许\n访问照片" = "사진을 방문 할 수 없 으니, 안내 해 주시기 바 랍니다\n사진을 방문하다";
"无法使用麦克风" = "마이크를 사용 할 수 없어요";
"请在设置-隐私-相机中允许访问麦克风" = "설치하십시오-사생활-카메라에는 마이크를 방문하는 것이 허용 된다";
"视频保存失败!" = "동영상 보존 실패!";
"照片保存失败!" = "사진 보존 실패!";
"保存失败，无法访问照片\n请前往设置中允许访问照片" = "실패를 보존하다，사진을 찾을 수 없다\n여기에 가서 허락 해 주세요\n사진을 방문하다";
"没有可选的照片!" = "선택 할 수 있는 사진이 없습니다!";
"视频已被删除!" = "동영상은 이미 삭제 되었다!";
"尚未从iCloud上下载，请至系统相册下载完毕后选择" = "아직 iclod에서 다운로드 받지 못 했다，시스템 상에서 다운 받아 보시기 바 랍니다";
"今天" = "오늘";
"昨天" = "어제";
"麦克风添加失败，录制视频会没有声音哦!" = "마이크 가 실패 했다,녹화 영상은 소리 가 없 네요!";
"点击录制" = "녹음 제작";
"%.0f秒内的视频无效哦~" = "%.0f초 안에 영상이 무효예요~";
"录制视频失败!" = "비디오 녹화에 실패하다!";
"继续录制" = "계속 녹음하다";
"无法访问相册" = "앨범을 찾을 수 없다";
"请在设置-隐私-相册中允许访问相册" = "설치하십시오-사생활-사진첩에는 사진첩을 방문 할 수 있다";
"下载失败" = "다운을 받다";
"所有照片" = "모든 사진";
"相机胶卷" = "카메라 롤";
"编辑后，GIF将会变为静态图，确定继续吗？" = "GIF는 편집 후 정적으로 변하게 되 는데 계속 진행 하시겠습니까?";
"编辑后，LivePhoto将会变为静态图，确定继续吗？" = "편집 후 라이브 포토토는 정적인 그림으로 바 뀔 것 입니다. 계속 하시겠습니까?";
"图片尺寸过小!" = "사진 사이즈 너무 작아요!";
"获取视频失败!" = "비디오를 가져오는 데 실패 했습니다!";
"确定取消吗?" = "취소 가 확실 합니까?";
"继续" = "계속 하기";
"获取" = "입수";
"处理失败，请重试" = "처리 가 실패 했습니다. 다시 시도하십시오";
"选择照片时不能选择视频" = "사진을 선택 할 때는 동영상을 선택 할 수 없습니다";
"选择视频时不能选择照片" = "동영상을 선택 할 때는 사진을 선택 할 수 없습니다";
"只能选择%ld秒内的视频，需进行编辑" = "%ld초짜리 동영상을 골라 편집 해야 한다";
"从手机相册选择" = "핸드폰 앨범에서 선택";
"正在下载网络图片，请稍等" = "네트워크 그림을 다운로드 중입니다. 잠시만 기다려 주십시오.";
"正在获取照片大小，请稍等" = "사진 크기 가져오는 중, 잠시만 기다려 주십시오.";
"照片大小超过最大限制%@" = "사진의 크기가 최대 제한을 초과하다 %@";
"视频大小超过最大限制%@" = "비디오 크기가 최대 제한 이상입니다 %@";
"照片或视频" = "사진이나 비디오";
"轻触拍照" = "터치 해서 사진 을 찍다.";
"按住摄像" = "눌 러 서 촬영 하 다.";
"轻触拍照，按住摄像" = "터치 해서 사진 찍 고, 눌 러 서 찍 고.";
"拍照" = "사진 을 찍다.";
"摄像" = "촬영 하 다.";
"无法访问所有照片\n请点击这里前往设置中允许访问所有照片" = "모든 사진 에 접근 할 수 없습니다.\n이 곳 을 클릭 해서 설정 중 에 모든 사진 에 접근 할 수 있 도록 해 주세요.";
"无法访问所有照片" = "모든 사진 에 접근 할 수 없습니다.";
"请在设置-隐私-相册中允许访问所有照片" = "설정 하 세 요. - 프라이버시. - 앨범 에서 모든 사진 에 접근 할 수 있 습 니 다.";
"要删除这张照片吗?" = "이 사진 을 삭제 하 시 겠 습 니까?";
"要删除此视频吗?" = "이 영상 을 삭제 하 시 겠 습 니까?";
"自由" = "자유";
"松手即可删除" = "삭제로 이동";
"拖动到此处删除" = "삭제하려면 여기로 드래그하세요.";
"仅可访问部分照片，建议开启「所有照片」" = "일부 사진만 액세스할 수 있습니다. 모든 사진을 여는 것이 좋습니다.";
"去设置" = "세트로 이동";
"更多" = "더";

"HXAlbumCameraRoll" = "모든 사진";
"HXAlbumPanoramas" = "파노라마 사진";
"HXAlbumVideos" = "동영상";
"HXAlbumFavorites" = "개인 소장";
"HXAlbumTimelapses" = "시간 지연 촬영";
"HXAlbumRecents" = "최근 프로젝트";
"HXAlbumRecentlyAdded" = "최근에 추가";
"HXAlbumBursts" = "스냅을 연속으로 찍다";
"HXAlbumSlomoVideos" = "느린 동작";
"HXAlbumSelfPortraits" = "셀 카";
"HXAlbumScreenshots" = "화면 스냅";
"HXAlbumDepthEffect" = "콜 롬";
"HXAlbumLivePhotos" = "실황 사진";
"HXAlbumAnimated" = "발생시키다 투";

