//
//  NSDictionary+JDISVAfterExtension.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.zhangchen41 on 2024/9/1.
//

#import "NSDictionary+JDISVAfterExtension.h"

@implementation NSDictionary (JDISVAfterExtension)

+(id)userDefaultsWithKey:(NSString*)key {
    return [[NSUserDefaults standardUserDefaults] objectForKey:key];
}

+(void)setUserDefaults:(id)obj forKey:(NSString*)key {
    NSUserDefaults *defs = [NSUserDefaults standardUserDefaults];
    [defs setObject:obj forKey:key];
    [defs synchronize];
}

-(NSNumber*)numKey:(NSString*)key{
    id obj = self[key];
    if ([obj isKindOfClass:NSNumber.class]){
        return obj;
    }
    if([obj isKindOfClass:NSString.class]){
        return @([obj intValue]);
    }
    return nil;
}

@end
