//
//  UIImage+JDISVAfterExtension.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.zhang<PERSON><PERSON> on 2024/9/1.
//

#import "UIImage+JDISVAfterExtension.h"
#define ImageCompress .4
#define ScreenWidth           [UIScreen mainScreen].bounds.size.width
#define ScreenHeight          [UIScreen mainScreen].bounds.size.height

@implementation UIImage (JDISVAfterExtension)

+ (UIImage*)imageScal:(UIImage *)image {
    CGSize targetSize;
    if (image.size.width < image.size.height) {
        targetSize = CGSizeMake(image.size.width,image.size.width * 4 / 3.f );
    }else{
        targetSize = CGSizeMake(image.size.height * 4 / 3.f, image.size.height);
    }
    UIImage *sourceImage = image;
    UIImage *newImage = nil;
    CGSize imageSize = sourceImage.size;
    CGFloat width = imageSize.width;
    CGFloat height = imageSize.height;
    CGFloat targetWidth = targetSize.width;
    CGFloat targetHeight = targetSize.height;
    CGFloat scaleFactor = 0.0;
    CGFloat scaledWidth = targetWidth;
    CGFloat scaledHeight = targetHeight;
    CGPoint thumbnailPoint = CGPointMake(0.0,0.0);
    
    if (CGSizeEqualToSize(imageSize, targetSize) == NO)
    {
        CGFloat widthFactor = targetWidth / width;
        CGFloat heightFactor = targetHeight / height;
        
        if (widthFactor > heightFactor)
            scaleFactor = widthFactor; // scale to fit height
        else
            scaleFactor = heightFactor; // scale to fit width
        scaledWidth= width * scaleFactor;
        scaledHeight = height * scaleFactor;
        
        // center the image
        if (widthFactor > heightFactor)
        {
            thumbnailPoint.y = (targetHeight - scaledHeight) * 0.5;
        }
        else if (widthFactor < heightFactor)
        {
            thumbnailPoint.x = (targetWidth - scaledWidth) * 0.5;
        }
    }
    
    UIGraphicsBeginImageContext(targetSize); // this will crop
    
    CGRect thumbnailRect = CGRectZero;
    thumbnailRect.origin = thumbnailPoint;
    thumbnailRect.size.width= scaledWidth;
    thumbnailRect.size.height = scaledHeight;
    
    [sourceImage drawInRect:thumbnailRect];
    
    newImage = UIGraphicsGetImageFromCurrentImageContext();
    if(newImage == nil)
        NSLog(@"could not scale image");
    
    //pop the context to get back to the default
    UIGraphicsEndImageContext();
    return newImage;
}

+ (UIImage *)fixOrientation:(UIImage *)aImage {
    
    // No-op if the orientation is already correct
    if (aImage.imageOrientation == UIImageOrientationUp)
        return aImage;
    
    // We need to calculate the proper transformation to make the image upright.
    // We do it in 2 steps: Rotate if Left/Right/Down, and then flip if Mirrored.
    CGAffineTransform transform = CGAffineTransformIdentity;
    
    switch (aImage.imageOrientation) {
        case UIImageOrientationDown:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, aImage.size.width, aImage.size.height);
            transform = CGAffineTransformRotate(transform, M_PI);
            break;
            
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
            transform = CGAffineTransformTranslate(transform, aImage.size.width, 0);
            transform = CGAffineTransformRotate(transform, M_PI_2);
            break;
            
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, 0, aImage.size.height);
            transform = CGAffineTransformRotate(transform, -M_PI_2);
            break;
        default:
            break;
    }
    
    switch (aImage.imageOrientation) {
        case UIImageOrientationUpMirrored:
        case UIImageOrientationDownMirrored:
            transform = CGAffineTransformTranslate(transform, aImage.size.width, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
            
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRightMirrored:
            transform = CGAffineTransformTranslate(transform, aImage.size.height, 0);
            transform = CGAffineTransformScale(transform, -1, 1);
            break;
        default:
            break;
    }
    
    // Now we draw the underlying CGImage into a new context, applying the transform
    // calculated above.
    CGContextRef ctx = CGBitmapContextCreate(NULL, aImage.size.width, aImage.size.height,
                                             CGImageGetBitsPerComponent(aImage.CGImage), 0,
                                             CGImageGetColorSpace(aImage.CGImage),
                                             CGImageGetBitmapInfo(aImage.CGImage));
    CGContextConcatCTM(ctx, transform);
    switch (aImage.imageOrientation) {
        case UIImageOrientationLeft:
        case UIImageOrientationLeftMirrored:
        case UIImageOrientationRight:
        case UIImageOrientationRightMirrored:
            // Grr...
            CGContextDrawImage(ctx, CGRectMake(0,0,aImage.size.height,aImage.size.width), aImage.CGImage);
            break;
            
        default:
            CGContextDrawImage(ctx, CGRectMake(0,0,aImage.size.width,aImage.size.height), aImage.CGImage);
            break;
    }
    
    // And now we just create a new UIImage from the drawing context
    CGImageRef cgimg = CGBitmapContextCreateImage(ctx);
    UIImage *img = [UIImage imageWithCGImage:cgimg];
    CGContextRelease(ctx);
    CGImageRelease(cgimg);
    return img;
}

+(UIImage *)dateTextImage:(UIImage *)image {
    
    float textHeight = image.size.height * 0.04;
    
    if (image.size.height < image.size.width) {
        textHeight = image.size.width * 0.04;
    }
    float baseHeight = image.size.height * 0.04 * 4;
    if (image.size.height > image.size.width) {
        baseHeight = image.size.height * 0.04 * 3;
    }
    
    CGSize size= CGSizeMake (image.size.width , image.size.height ); // 画布大小
    UIGraphicsBeginImageContextWithOptions (size, NO , 0.0 );
    [image drawAtPoint : CGPointMake ( 0 , 0 )];
    
    // context
    CGContextRef context= UIGraphicsGetCurrentContext ();
    CGContextDrawPath (context, kCGPathStroke );
    
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];

    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    NSTimeZone *localzone = [NSTimeZone localTimeZone];
    [dateFormatter setTimeZone:localzone];
    NSString *dateStr = [dateFormatter stringFromDate:[NSDate date]];
    
    dateFormatter.dateStyle = NSDateFormatterFullStyle;
    
    NSString *currentDateStr = [dateFormatter stringFromDate:[NSDate date]];
    
    NSArray *timeArr = [[dateStr componentsSeparatedByString:@" "].lastObject componentsSeparatedByString:@":"];
    NSString *strTime = [NSString stringWithFormat:@"%@:%@",timeArr.firstObject,[timeArr objectAtIndex:1]];
    NSString *strData = currentDateStr;
    NSString *strWeek = @"";
    
    CGRect rect;
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, 1, textHeight)];
    label.text = strTime;
    label.font = [UIFont systemFontOfSize:(int)textHeight - 1];
    [label sizeToFit];
    
    rect = CGRectMake(image.size.width - 0.025*image.size.width - label.frame.size.width, image.size.height - baseHeight, label.frame.size.width, label.frame.size.height);
    
    // draw
    [strTime drawInRect:rect withAttributes:@{NSFontAttributeName :label.font, NSForegroundColorAttributeName :[UIColor whiteColor]}];
    
    float upHeight = label.frame.size.height;
    textHeight = textHeight * 0.5;
    label.text = strData;
    label.font = [UIFont systemFontOfSize:(int)textHeight - 1];
    [label sizeToFit];
    // draw
    rect = CGRectMake(image.size.width - 0.025*image.size.width - label.frame.size.width, image.size.height - baseHeight + upHeight, label.frame.size.width, label.frame.size.height);

    [strData drawInRect:rect withAttributes:@{NSFontAttributeName :label.font, NSForegroundColorAttributeName :[UIColor whiteColor]}];
    
    upHeight += label.frame.size.height;
    label.text = strWeek;
    label.font = [UIFont systemFontOfSize:(int)textHeight - 1];
    [label sizeToFit];
    // 画
    rect = CGRectMake(image.size.width - 0.025*image.size.width - label.frame.size.width, image.size.height - baseHeight + upHeight + label.frame.size.height * 0.2, label.frame.size.width, label.frame.size.height);
    [strWeek drawInRect:rect withAttributes:@{NSFontAttributeName :label.font, NSForegroundColorAttributeName :[UIColor whiteColor]}];
    
    // new image
    UIImage *newImage= UIGraphicsGetImageFromCurrentImageContext ();
    UIGraphicsEndImageContext ();
    return newImage;
}

+(UIImage *)compressImageWithData:(NSData *)imageData {
    UIImage *retImage = [UIImage imageWithData:imageData];
    if ((imageData.length/1024) > 300) {
        retImage = [UIImage compressImageWith:retImage];
    }
    return retImage;
}

// 等比例压缩图片0.2
+(UIImage *)compressImageWith:(UIImage *)image
{
    if (image.size.width <= ScreenWidth * 2) {
        return image;
    }
    
    float imageWidth = image.size.width;
    float imageHeight = image.size.height;
    float width = ScreenWidth * 2.5;
    float height = image.size.height/(image.size.width/width);
    
    float widthScale = imageWidth /width;
    float heightScale = imageHeight /height;
    
    // 创建一个bitmap的context
    // 并把它设置成为当前正在使用的context
    UIGraphicsBeginImageContext(CGSizeMake(width, height));
    
    if (widthScale > heightScale) {
        [image drawInRect:CGRectMake(0, 0, imageWidth /heightScale , height)];
    }
    else {
        [image drawInRect:CGRectMake(0, 0, width , imageHeight /widthScale)];
    }
    
    // 从当前context中创建一个改变大小后的图片
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    // 使当前的context出堆栈
    UIGraphicsEndImageContext();
    
    //    // 限制大小300k
    NSData *imgData = UIImageJPEGRepresentation(newImage, ImageCompress);
    //    }
    newImage = [UIImage imageWithData:imgData];
    
    return newImage;
}

@end
