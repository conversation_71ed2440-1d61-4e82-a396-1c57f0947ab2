//
//  NSString+JDISVAfterExtension.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.zhangchen41 on 2024/9/1.
//

#import "NSString+JDISVAfterExtension.h"
#import <CommonCrypto/CommonHMAC.h>
#import <CommonCrypto/CommonDigest.h>

@implementation NSString (JDISVAfterExtension)

+ (BOOL)isEmpty:(NSString *)str {
    if(nil == str)
    {
        return YES;
    }
    if([str isKindOfClass:[NSNull class]])
    {
        return YES;
    }
    if(YES == [@"" isEqualToString:[NSString trim:str]])
    {
        return YES;
    }
    if(0 == str.length)
    {
        return YES;
    }
    return NO;
}

+ (NSString*)trim:(NSString *)str {
    if(![str isKindOfClass:[NSString class]])
        return @"";
    return [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
}

+ (NSString *)notNilValue:(NSString *)str {
    if ([NSString isEmpty:str]) {
        return @"";
    }
    return str;
}

+ (CGSize)countTextCGSize:(UIFont *)font
               viewHeight:(CGFloat)viewHeight
                     text:(NSString *)text {
    if (!font) return CGSizeZero;
    
    if ([NSString isEmpty:text]) return CGSizeZero;
    
    CGSize size = CGSizeMake(MAXFLOAT,viewHeight);
    
    CGSize txtSize;
    NSStringDrawingOptions options = NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading;
    CGRect txtRT = [text boundingRectWithSize:size options:options attributes:@{NSFontAttributeName:font} context:nil];
    txtSize = txtRT.size;
    return txtSize;
}

+ (CGSize)countTextCGSize:(UIFont *)font
                viewWidth:(CGFloat)viewWidth
                     text:(NSString *)text {
    if (!font || [NSString isEmpty:text]) return CGSizeZero;
    
    CGSize size = CGSizeMake(viewWidth,MAXFLOAT);
    CGSize txtSize;
    NSStringDrawingOptions options =  NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading;
    CGRect txtRT = [text boundingRectWithSize:size options:options attributes:@{NSFontAttributeName:font} context:nil];
    txtSize = txtRT.size;
    return txtSize;
}

- (NSString *)after_md5 {
    const char *str = [self UTF8String];
    unsigned char md5Buffer[CC_MD5_DIGEST_LENGTH];
    CC_MD5(str, (CC_LONG)strlen(str), md5Buffer);
    NSMutableString *output = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH *2];
    for (int i = 0; i<CC_MD5_DIGEST_LENGTH; i++) {
        [output appendFormat:@"%02x",md5Buffer[i]];
    }
    return output;
}

@end
