//
//  UIButton+Extension.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.zhangchen41 on 2024/8/30.
//

#import "UIButton+Extension.h"

@implementation UIButton (Extension)

/***>
 * 创建按钮图片
 * Create button image
 */
+ (UIButton *)createBarButtonWithImage:(UIImage*)image
                               target:(id)target
                               action:(SEL)action {
    UIButton *btn = [[UIButton alloc]initWithFrame:CGRectMake(0, 0, image.size.width, image.size.height)];
    [btn setImage:image forState:UIControlStateNormal];
    [btn addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
    return btn;
}

/***>
 * 创建按钮图片
 * Create button with background image
 */
+ (UIButton*)creatButtonWithBackground:(UIImage*)background
                                 tilte:(NSString*)title
                            titleColor:(UIColor *)titleColor
                                 frame:(CGRect)frame
                                 taget:(id)taget
                                action:(SEL)action {
    UIButton *NAME = nil;
    NAME = [[UIButton alloc]initWithFrame:frame];
    [NAME addTarget:taget action:action forControlEvents:UIControlEventTouchUpInside];
    [NAME setBackgroundImage:background forState:UIControlStateNormal];
    [NAME setTitle:title forState:UIControlStateNormal];
    [NAME setTitleColor:titleColor forState:UIControlStateNormal];
    NAME.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    return NAME;
}

@end
