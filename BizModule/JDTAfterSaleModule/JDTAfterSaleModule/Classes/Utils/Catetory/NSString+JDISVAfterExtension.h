//
//  NSString+JDISVAfterExtension.h
//  JDISVKAAfterSaleModule
//
//  Created by ext.zhangchen41 on 2024/9/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSString (JDISVAfterExtension)

+ (BOOL)isEmpty:(NSString *)str;

+ (NSString*)trim:(NSString *)str;

+ (NSString *)notNilValue:(NSString *)str;

+ (CGSize)countTextCGSize:(UIFont *)font
               viewHeight:(CGFloat)viewHeight
                     text:(NSString *)text;

+ (CGSize)countTextCGSize:(UIFont *)font
                viewWidth:(CGFloat)viewWidth
                     text:(NSString *)text;
- (NSString *)after_md5;

@end

NS_ASSUME_NONNULL_END
