//
//  ZCTakePictureCol.h
//  DYCustomTakePhtotoDemo
//
//  Created by lock<PERSON><PERSON> on 16/10/1.
//  Copyright © 2016年 __defaultyuan. All rights reserved.
//

#import <UIKit/UIKit.h>

/**>
 * 按功能分
 * Classified by function
 */
typedef NS_ENUM(NSInteger, TakePictureFunctionType) {
    TakePictureDefaultType //Regular photography
};

/** 前置或后置摄像头
 *  Front or rear camera
 */
typedef NS_ENUM(NSInteger, TakePicturePosition)
{
    TakePicturePositionBack = 0,
    TakePicturePositionFront
};

// Classified by function
typedef enum {
    BoardTakePictureNone = 1,// regular photography
    BoardTakePictureOCR = 2,// OCR takes photos
}BoardTakePicture;

/** Photography agency */
@protocol  ZCTakePictureColDelegate<NSObject>
@optional
- (void)didFinishPickingImageArr:(UIImage *)imageArr;
- (void)didFinishPickingImage:(UIImage *)image;
- (void)didCancelPickingImage;
@end

@interface ZCTakePictureCol : UIViewController
@property (nonatomic,assign)BOOL            needCoremotion;
// Whether it is not required to be proportional, default is required
@property (nonatomic,assign)BOOL            noNeedProportion;
/// Determine if the device has a camera
@property (nonatomic, assign, readonly) BOOL isAuthorizedCamera;
/// Determine if the device has a camera
@property (nonatomic, assign, readonly) BOOL isCameraAvailable;
@property (nonatomic,weak)id<ZCTakePictureColDelegate> delegate;
// is it a front or rear camera
@property (nonatomic,assign)TakePicturePosition position;
/// Divided by business functions
@property (nonatomic, assign) TakePictureFunctionType functionType;
@property (nonatomic, assign)BOOL isOnlyOne;// Do we only need a single one
@property (nonatomic, assign)BOOL isNeedDateWater;// Do you need a watermark
@property(nonatomic,copy)void (^backBlock)(void);
@property(nonatomic,copy)void (^replaceBackBlock)(void);
@property(nonatomic,copy)void (^takePicture)(UIImage *image);
@end
