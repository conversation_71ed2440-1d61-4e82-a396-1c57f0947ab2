//
//  ZCTakePictureCol.m
//  ZCCustomTakePicture
//
//  Created by lock<PERSON><PERSON> on 16/10/1.
//  Copyright © 2016年 __defaultyuan. All rights reserved.
//

#import "ZCTakePictureCol.h"
#import <AVFoundation/AVFoundation.h>
#import "UIView+borderLine.h"
#import "UIImage+fixOrientation.h"
#import <CoreMotion/CoreMotion.h>
#import "NSString+JDISVAfterExtension.h"
#import "UIImage+JDISVAfterExtension.h"
#import "NSDictionary+JDISVAfterExtension.h"

#define HightCamera  ScreenWidth*4/3.f //拍照高度
#define HightCameraTop  44.f //拍照底部高度
#define ORIGINAL_MAX_WIDTH 640.0f

#define DDColor(r, g, b) [UIColor colorWithRed:(r)/255.0 green:(g)/255.0 blue:(b)/255.0 alpha:1]
#define ColorText DDColor(173, 124, 28) // Shooting mode text color

#define UserDefOrientation @"UserDefOrientation"//锁定方向
#define TagSetAlert 100001

// block self
#define WEAKSELF typeof(self) __weak weakSelf = self;

#define ScreenHeight [UIScreen mainScreen].bounds.size.height
#define ScreenWidth  [UIScreen mainScreen].bounds.size.width
#define VIEW_W(VIEW) CGRectGetWidth(VIEW.frame)
#define VIEW_H(VIEW) CGRectGetHeight(VIEW.frame)
#define VIEW_CenterX(VIEW) VIEW_W(VIEW)/2.0
#define VIEW_CenterY(VIEW) VIEW_H(VIEW)/2.0
#define VIEW_XMIN(VIEW) CGRectGetMinX(VIEW.frame)
#define VIEW_YMIN(VIEW) CGRectGetMinY(VIEW.frame)
#define VIEW_XMAX(VIEW) CGRectGetMaxX(VIEW.frame)
#define VIEW_YMAX(VIEW) CGRectGetMaxY(VIEW.frame)


@interface ZCTakePictureCol ()
<AVCaptureMetadataOutputObjectsDelegate,UIGestureRecognizerDelegate,UIAlertViewDelegate>
{
    BOOL _staHidden;
}
@property (nonatomic,assign)BOOL isGoOnTake;//是否连续拍摄
//session：由他把输入输出结合在一起，并开始启动捕获设备（摄像头）
@property (nonatomic, strong) AVCaptureSession *session;
//AVCaptureDeviceInput 代表输入设备，他使用AVCaptureDevice 来初始化
@property(nonatomic, strong) AVCaptureDeviceInput *videoInput;
//当启动摄像头开始捕获输入
@property (nonatomic, strong) AVCaptureStillImageOutput *stillImageOutput;
@property (nonatomic, strong) AVCaptureVideoPreviewLayer *previewLayer;
//捕获设备，通常是前置摄像头，后置摄像头，麦克风（音频输入）
@property(nonatomic)AVCaptureDevice *device;
@property (nonatomic,strong)UIView  *focusView;//聚焦视图
@property (nonatomic,strong)UIButton *flashButton;//闪光灯按钮
@property (nonatomic,strong)UIButton *takePicBtn;
@property (nonatomic,strong)UIButton *cancelBtn;
@property (nonatomic,strong)UIButton *restartBtn;// 重拍
@property (nonatomic,strong)UIButton *doneBtn;// 完成拍照
@property (nonatomic,strong)UIView *cameraView;//相机录制视图
@property (nonatomic,strong)UIView *gainView;//拍一张照的闪
//图片相关
@property (nonatomic,strong)UIImageView *preIimageV;//当前摄像头展示的图片

@property (nonatomic,strong)UIView *upBarView;//顶部视图
@property (nonatomic,assign) AVCaptureVideoOrientation deviceOrientation;
@property (nonatomic,strong) CMMotionManager *mgr;//加速器

@property (nonatomic,assign)BOOL isflashOn;//闪光灯是否开
@property (nonatomic,strong)UIButton *sharpBtn;//锐化按钮
@property (nonatomic, strong) UIButton *oneBtn;//单拍

@property (nonatomic, strong)UIPanGestureRecognizer *changTakePan;//滑动改变拍摄模式
@property (nonatomic, strong)UIPinchGestureRecognizer *fousPinch;//调焦手势
@end

@implementation ZCTakePictureCol

-(void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.session) {
        [self.session startRunning];
    }
    if (_mgr) {
        [self CMMotionStart];
    }
}

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    if (self.session) {
        [self focusAtPoint:CGPointMake(ScreenWidth / 2.f, VIEW_CenterY(self.cameraView))];
    }
}

-(void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    if (self.session) {
        [self.session stopRunning];
    }
    [_mgr stopAccelerometerUpdates];
}


- (void)viewDidLoad {
    [super viewDidLoad];
    
    void (^checkCamera)(void) = ^(){
        self.view.backgroundColor = [UIColor whiteColor];
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            //打开相机提示框
            UIAlertView *alert = [[UIAlertView alloc] initWithTitle:Lang(@"aftersale_return_camera_alert_title")
                                                            message:Lang(@"aftersale_return_camera_alert_message")
                                                           delegate:self
                                                  cancelButtonTitle:Lang(@"after_sale_cancel")
                                                  otherButtonTitles:Lang(@"aftersale_return_button_settings"),nil];
            alert.tag = TagSetAlert;
            [alert show];
        }
    };
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        checkCamera();
        return;
    }
    
    _deviceOrientation = AVCaptureVideoOrientationPortrait;
    if (!self.isAuthorizedCamera || !self.isCameraAvailable) {
        return;
    }
    
    [self initViews];
    [self setUpGesture];
    
    BOOL canCa = [self canUserCamear];
    if (canCa) {
        [self initialSession];
    }else{
        return;
    }
    
    if (_needCoremotion) {
        [self creatCMMotion];
    }
}

-(void)creatCMMotion{
    _mgr = [CMMotionManager new];
    
}

-(void)CMMotionStart{
    if (_mgr.gyroAvailable == YES) {
        _mgr.accelerometerUpdateInterval = .2;
        _mgr.gyroUpdateInterval = .2;
        [_mgr startAccelerometerUpdatesToQueue:[NSOperationQueue currentQueue] withHandler:^(CMAccelerometerData  *accelerometerData, NSError *error) {
            if (!error) {
                [self outputAccelertionData:accelerometerData.acceleration];
            }else{
                NSLog(@"%@", error);
            }
        }];
    }
}

- (BOOL)prefersStatusBarHidden
{
    return YES;
}

-(void)creatCameraView{
    // 设置相机捕捉视图界面
    UIView *cameraView = [[UIView alloc] init];
    cameraView.frame = CGRectMake(0, HightCameraTop, ScreenWidth,HightCamera);
    cameraView.backgroundColor = [UIColor blackColor];
    cameraView.clipsToBounds = NO;
    [self.view addSubview:cameraView];
    self.cameraView = cameraView;
    
    //聚焦视图
    _focusView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 80, 80)];
    _focusView.layer.borderWidth = 1.0;
    _focusView.layer.borderColor =[UIColor greenColor].CGColor;
    _focusView.backgroundColor = [UIColor clearColor];
    [self.cameraView addSubview:_focusView];
    _focusView.hidden = YES;
    
    // 预览照片View
    UIImageView *preIimageV = [[UIImageView alloc] init];
    preIimageV.frame = CGRectMake(0, 0, VIEW_W(self.cameraView), VIEW_H(self.cameraView));
    preIimageV.contentMode = UIViewContentModeScaleAspectFit;
    self.cameraView.clipsToBounds = NO;
    [self.cameraView addSubview:preIimageV];
    self.preIimageV = preIimageV;
}

-(void)creatUpView{
    //顶部视图父视图
    _upBarView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, ScreenWidth, 44)];
    _upBarView.backgroundColor = [UIColor blackColor];
    [self.view addSubview:_upBarView];
    
    //闪光灯按钮
    _flashButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _flashButton.frame = CGRectMake(5, 0, 44, VIEW_H(_upBarView));
    [_flashButton setImage:[UIImage imageNamed:@"CameraFlashIconOff"] forState:UIControlStateNormal];
    [_flashButton setImage:[UIImage imageNamed:@"CameraFlashIconOn"] forState:UIControlStateSelected];
    [_flashButton addTarget:self action:@selector(FlashOn) forControlEvents:UIControlEventTouchUpInside];
    [_upBarView addSubview:_flashButton];
    
    //锐化按钮
    _sharpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _sharpBtn.center = CGPointMake(VIEW_CenterX(_upBarView), VIEW_CenterY(_upBarView));
    _sharpBtn.bounds = CGRectMake(0, 0, 44, VIEW_H(_upBarView));
    _sharpBtn.titleLabel.font = [UIFont systemFontOfSize:16];
    [_sharpBtn setImage:[UIImage imageNamed:@"cameraSharpenClose"] forState:UIControlStateNormal];
    [_sharpBtn setImage:[UIImage imageNamed:@"cameraSharpenOpen"] forState:UIControlStateSelected];
    [_sharpBtn addTarget:self action:@selector(sharpBtnClick) forControlEvents:UIControlEventTouchUpInside];
    [_upBarView addSubview:_sharpBtn];
    //摄像头切换按钮
    UIButton *rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
    rightButton.frame = CGRectMake(ScreenWidth - 10 - 44, VIEW_YMIN(rightButton), 44, VIEW_H(_upBarView));
    [rightButton setImage:[UIImage imageNamed:@"cameraSwitch"] forState:UIControlStateNormal];
    rightButton.titleLabel.textAlignment = NSTextAlignmentCenter;
    [rightButton addTarget:self action:@selector(changeCamera) forControlEvents:UIControlEventTouchUpInside];
    [_upBarView addSubview:rightButton];
}

-(void)creatDownView{
    //底部父视图
    UIView *btomView = [[UIView alloc]initWithFrame:CGRectMake(0, VIEW_YMAX(self.cameraView), ScreenWidth, ScreenHeight - HightCameraTop - HightCamera)];
    btomView.backgroundColor = [UIColor blackColor];
    [self.view addSubview:btomView];
    
    //拍照按钮
    UIButton *takePicBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    takePicBtn.frame = CGRectMake((btomView.frame.size.width - 64.) * .5, (btomView.frame.size.height - 64.) * .5, 64, 64);
    takePicBtn.backgroundColor = [UIColor whiteColor];
    takePicBtn.layer.cornerRadius = takePicBtn.frame.size.width * .5;
    takePicBtn.layer.masksToBounds = YES;
    [takePicBtn addTarget:self action:@selector(takePic) forControlEvents:UIControlEventTouchUpInside];
    [btomView addSubview:takePicBtn];
    self.takePicBtn = takePicBtn;
    
    //取消按钮
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    cancelBtn.frame = CGRectMake(15, (btomView.frame.size.height - 44.) * .5, 44, 44);
    [cancelBtn setTitle:Lang(@"after_sale_cancel") forState:UIControlStateNormal];
    [cancelBtn.titleLabel setFont:[UIFont systemFontOfSize:16.f]];
    [cancelBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [cancelBtn addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
    [btomView addSubview:cancelBtn];
    self.cancelBtn = cancelBtn;
    
    //重拍按钮
    float restartBtnWide = [NSString countTextCGSize:[UIFont systemFontOfSize:16.f] viewHeight:100 text:Lang(@"aftersale_return_button_retake")].width;
    UIButton *restartBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    restartBtn.center = CGPointMake(24 + 17, _takePicBtn.center.y);
    restartBtn.bounds = CGRectMake(0, 0, restartBtnWide, 30);
    restartBtn.backgroundColor = [UIColor clearColor];
    [restartBtn setTitle:Lang(@"aftersale_return_button_retake") forState:UIControlStateNormal];
    [restartBtn.titleLabel setFont:[UIFont systemFontOfSize:16.f]];
    [restartBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [restartBtn addTarget:self action:@selector(restartAction) forControlEvents:UIControlEventTouchUpInside];
    [btomView addSubview:restartBtn];
    self.restartBtn = restartBtn;
    
    //完成按钮
    float viewW = [NSString countTextCGSize:[UIFont systemFontOfSize:16.f] viewHeight:44 text:Lang(@"aftersale_return_button_done")].width;
    UIButton *doneBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    doneBtn.center = CGPointMake(ScreenWidth - 24 - 17, takePicBtn.center.y);
    doneBtn.bounds = CGRectMake(0, 0, viewW, 44);
    doneBtn.backgroundColor = [UIColor clearColor];
    [doneBtn setTitle:Lang(@"aftersale_return_button_done") forState:UIControlStateNormal];
    [doneBtn.titleLabel setFont:[UIFont systemFontOfSize:16.f]];
    [doneBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [doneBtn addTarget:self action:@selector(doneAction) forControlEvents:UIControlEventTouchUpInside];
    [btomView addSubview:doneBtn];
    self.doneBtn = doneBtn;
}

- (void)initViews {
    self.view.backgroundColor = [UIColor blackColor];
    //拍照相关视图
    [self creatCameraView];
    //相机顶部视图
    [self creatUpView];
    //相机底部视图
    [self creatDownView];
}

#pragma 创建调焦手势
- (void)setUpGesture{
    _fousPinch = [[UIPinchGestureRecognizer alloc] initWithTarget:self action:@selector(handlePinchGesture:)];
    _fousPinch.delegate = self;
    [self.cameraView addGestureRecognizer:_fousPinch];
    
    //点击对焦手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(focusGesture:)];
    tapGesture.delegate = self;
    [self.cameraView addGestureRecognizer:tapGesture];
    if (!_isOnlyOne) {
        //移动选择拍照类型手势
        _changTakePan = [[UIPanGestureRecognizer alloc]initWithTarget:self action:@selector(changTakePan:)];
        _changTakePan.delegate = self;
        [self.view addGestureRecognizer:_changTakePan];
    }
}

- (void)initialSession {
    // 开启后置摄像头了
    _position = TakePicturePositionBack;
    
    //使用AVMediaTypeVideo 指明self.device代表视频，默认使用后置摄像头进行初始化
    self.device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    
    //这个方法的执行我放在init方法里了
    self.session = [[AVCaptureSession alloc] init];
    self.videoInput = [[AVCaptureDeviceInput alloc] initWithDevice:[self backCamera] error:nil];
    //[self fronCamera]方法会返回一个AVCaptureDevice对象，因为我初始化时是采用前摄像头，所以这么写，具体的实现方法后面会介绍
    [self.session setSessionPreset:AVCaptureSessionPresetPhoto];//需要更加清晰的照片的话可以重新设置新值
    self.stillImageOutput = [[AVCaptureStillImageOutput alloc] init];
    NSDictionary * outputSettings = [[NSDictionary alloc] initWithObjectsAndKeys:AVVideoCodecJPEG,AVVideoCodecKey, nil];
    //这是输出流的设置参数AVVideoCodecJPEG参数表示以JPEG的图片格式输出图片
    [self.stillImageOutput setOutputSettings:outputSettings];
    
    if ([self.session canAddInput:self.videoInput]) {
        [self.session addInput:self.videoInput];
    }
    
    if ([self.session canAddOutput:self.stillImageOutput]) {
        [self.session addOutput:self.stillImageOutput];
    }
    
    if (self.session) {
        [self.session startRunning];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self focusAtPoint:CGPointMake(ScreenWidth / 2.f, VIEW_CenterY(self.cameraView))];
        });
    }
    
    [self setUpCameraLayer];
    [self setDonePicture:NO];
    
    if ([_device lockForConfiguration:nil]) {
        //自动闪光灯
        if ([_device isFlashModeSupported:AVCaptureFlashModeOff]) {
            [_device setFlashMode:AVCaptureFlashModeOff];
        }
        //自动白平衡
        if ([_device isWhiteBalanceModeSupported:AVCaptureWhiteBalanceModeAutoWhiteBalance]) {
            [_device setWhiteBalanceMode:AVCaptureWhiteBalanceModeAutoWhiteBalance];
        }
        //自动对焦
        if ([_device isFocusModeSupported:AVCaptureFocusModeContinuousAutoFocus]) {
            [_device setFocusMode:AVCaptureFocusModeContinuousAutoFocus];
        }
        //自动曝光
        if ([_device isExposureModeSupported:AVCaptureExposureModeContinuousAutoExposure]) {
            [_device setExposureMode:AVCaptureExposureModeContinuousAutoExposure];
        }
        [_device unlockForConfiguration];
    }
}

- (void)setUpCameraLayer
{
    if (self.previewLayer == nil) {
        self.previewLayer = [AVCaptureVideoPreviewLayer layerWithSession:self.session];
        [self.previewLayer setFrame:CGRectMake(0, 0, VIEW_W(_cameraView), VIEW_H(_cameraView))];
        [self.previewLayer setVideoGravity:AVLayerVideoGravityResizeAspectFill];
        [_cameraView.layer insertSublayer:self.previewLayer below:[[_cameraView.layer sublayers] objectAtIndex:0]];
    }
}

- (AVCaptureDevice *)backCamera {
    return [self cameraWithPosition:self.position == TakePicturePositionBack ? AVCaptureDevicePositionBack :AVCaptureDevicePositionFront];
}

#pragma mark 判断是否可以进行拍照
- (BOOL)isAuthorizedCamera
{
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    return !(authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted);
}

-(BOOL)isCameraAvailable
{
    NSArray *mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
    BOOL isAvailable =  [UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera];
    
    BOOL _isCameraAvailable = YES;
    if (!isAvailable || [mediaTypes count] <= 0) {
        _isCameraAvailable = NO;
    }
    return _isCameraAvailable ;
}

/** 拍照 */
- (void)takePic {
    [self gainPicture];
}

/** 取消 */
- (void)cancelAction {
    
    WEAKSELF
    [self dismissViewControllerAnimated:YES completion:^{
        if (weakSelf.replaceBackBlock) {
            weakSelf.replaceBackBlock();
        }
        if ([weakSelf.delegate respondsToSelector:@selector(didCancelPickingImage)]) {
            [weakSelf.delegate didCancelPickingImage];
        }
        if (weakSelf.takePicture) {
            weakSelf.takePicture(nil);
        }
        if (weakSelf.backBlock) {
            weakSelf.backBlock();
        }
    }];
}

-(void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{

    if(alertView.tag == TagSetAlert){
        if(buttonIndex==1) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            [[UIApplication sharedApplication] openURL:url];
        } else {
            WEAKSELF
            [self dismissViewControllerAnimated:YES completion:^{
                if (weakSelf.replaceBackBlock) {
                    weakSelf.replaceBackBlock();
                }
                if ([weakSelf.delegate respondsToSelector:@selector(didCancelPickingImage)]) {
                    [weakSelf.delegate didCancelPickingImage];
                }
                if (weakSelf.takePicture) {
                    weakSelf.takePicture(nil);
                }
                if (weakSelf.backBlock) {
                    weakSelf.backBlock();
                }
            }];
        }
    }
}


/** 重拍 */
- (void)restartAction {
    UIView *cenView = [self.view viewWithTag:1000];
    cenView.hidden = NO;
    _upBarView.hidden = NO;
    for (UIView *view in _upBarView.subviews) {
        view.hidden = NO;
    }
    _changTakePan.enabled = YES;
    _preIimageV.frame = CGRectMake(0, 0, VIEW_W(_cameraView), VIEW_H(_cameraView) + 1);
    [self setDonePicture:NO];
    if (self.session) {
        [self.session startRunning];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self focusAtPoint:CGPointMake(ScreenWidth / 2.f, VIEW_CenterY(self.cameraView))];
        });
    }
}

/** 拍照之后YES  显示的东西 */
- (void)setDonePicture:(BOOL)isTake {
    //拍照完之后
    self.previewLayer.hidden = _takePicBtn.hidden = _cancelBtn.hidden = isTake;
    self.restartBtn.hidden = self.preIimageV.hidden = _doneBtn.hidden = !isTake;
}

/** 完成拍照 */
- (void)doneAction {
    if (_isGoOnTake) {
        [self dismissViewControllerAnimated:YES completion:^{
            if ([self.delegate respondsToSelector:@selector(didFinishPickingImage:)]) {
                [self myProgressTask];
            }
        }];
        
    }else{
        WEAKSELF
        UIImage *photo;
        photo = [weakSelf.preIimageV.image fixOrientation];
        if (self.replaceBackBlock) {
            self.replaceBackBlock();
        }
        [weakSelf dismissViewControllerAnimated:YES completion:^{
            if ([weakSelf.delegate respondsToSelector:@selector(didFinishPickingImage:)]) {
                [weakSelf.delegate didFinishPickingImage:photo];
            }
            if (weakSelf.takePicture) {
                weakSelf.takePicture(photo);
            }
            if (weakSelf.backBlock) {
                weakSelf.backBlock();
            }
        }];
    }
}

#pragma mark 获取照片
- (void)gainPicture
{
    [self gainOnePic];
}

//获取单个摄像头照片
-(void)gainOnePic {
    _gainView.hidden = NO;
    _takePicBtn.enabled = NO;
    WEAKSELF
    [UIView animateWithDuration:0.1 animations:^{
        weakSelf.gainView.alpha = 0;
    } completion:^(BOOL finished) {
        weakSelf.gainView.alpha = 0.5;
        weakSelf.gainView.hidden = YES;
    }];
    
    AVCaptureConnection *videoConnection = nil;
    for (AVCaptureConnection *connection in self.stillImageOutput.connections) {
        for (AVCaptureInputPort *port in [connection inputPorts]) {
            if ([[port mediaType] isEqual:AVMediaTypeVideo] ) {
                videoConnection = connection;
                break;
            }
        }
        if (videoConnection) { break; }
    }
    if (!videoConnection) {
        NSLog(@"获取照片失败!");
        _takePicBtn.enabled = YES;
        return;
    }
    
    [videoConnection setVideoOrientation:_deviceOrientation];
    
    [self.stillImageOutput captureStillImageAsynchronouslyFromConnection:videoConnection completionHandler:^(CMSampleBufferRef imageDataSampleBuffer, NSError *error) {
        WEAKSELF
        if (imageDataSampleBuffer == NULL) {
            weakSelf.takePicBtn.enabled = YES;
            return;
        }
        NSData * imageData = [AVCaptureStillImageOutput jpegStillImageNSDataRepresentation:imageDataSampleBuffer];
        UIImage *gainImage = [UIImage imageWithData:imageData];
        AVCaptureDevicePosition position = AVCaptureDevicePositionUnspecified; //判断是前置摄像头还是后置摄像头
        NSArray *inputs = self.session.inputs;
        for (AVCaptureDeviceInput *input in inputs) {
            AVCaptureDevice *device = input.device;
            if ([device hasMediaType:AVMediaTypeVideo]) {
                position = device.position;
            }
        }
        //截可视范围之内的图
        gainImage = [UIImage imageScal:gainImage];
        gainImage = [UIImage fixOrientation:gainImage];
        UIView *cenView = [self.view viewWithTag:1000];
        
        cenView.hidden = YES;
        weakSelf.changTakePan.enabled = NO;
        if (weakSelf.isGoOnTake) { // 是否连拍
            self.restartBtn.hidden = YES;
            self.cancelBtn.hidden = NO;
        }else{
            for (UIView *view in weakSelf.upBarView.subviews) {
                view.hidden = YES;
            }
            if (weakSelf.isNeedDateWater) {
                gainImage = [UIImage dateTextImage:gainImage];
            }
            weakSelf.preIimageV.image = gainImage;
            [self setDonePicture:YES];
            if (self.session) {
                [self.session stopRunning];
            }
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            weakSelf.takePicBtn.enabled = YES;
        });
        
    }];
}

-(void)changOtherCamera {
    NSUInteger cameraCount = [[AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo] count];
    if (cameraCount > 1) {
        NSError *error;
        AVCaptureDevice *newCamera = nil;
        AVCaptureDeviceInput *newInput = nil;
        AVCaptureDevicePosition position = [[_videoInput device] position];
        if (position == AVCaptureDevicePositionFront){
            newCamera = [self cameraWithPosition:AVCaptureDevicePositionBack];
        }
        else {
            newCamera = [self cameraWithPosition:AVCaptureDevicePositionFront];
        }
        newInput = [AVCaptureDeviceInput deviceInputWithDevice:newCamera error:nil];
        if (newInput != nil) {
            [self.session beginConfiguration];
            [self.session removeInput:_videoInput];
            if ([self.session canAddInput:newInput]) {
                [self.session addInput:newInput];
                self.videoInput = newInput;
                
            } else {
                [self.session addInput:self.videoInput];
            }
            [self.session commitConfiguration];
        } else if (error) {
            NSLog(@"toggle carema failed, error = %@", error);
        }
    }
}

#pragma mark 拍照方向
- (void)outputAccelertionData:(CMAcceleration)acceleration{
    CGFloat xx = acceleration.x;
    CGFloat yy = acceleration.y;
    CGFloat zz = acceleration.z;
    CGFloat device_angle = M_PI / 2.0f - atan2(yy, xx);
    if (device_angle > M_PI){
        device_angle -= 2 * M_PI;
    }
    if ((zz < -.60f) || (zz > .60f)) {
        if ( (device_angle > -M_PI_4) && (device_angle < M_PI_4) ){
            if(_deviceOrientation != AVCaptureVideoOrientationPortraitUpsideDown)
            {
                _deviceOrientation = AVCaptureVideoOrientationPortraitUpsideDown;//手机右放（眼对着屏幕）
            }
        }else if ((device_angle < -M_PI_4) && (device_angle > -3 * M_PI_4)){
            if(_deviceOrientation != AVCaptureVideoOrientationLandscapeRight)
            {
                _deviceOrientation = AVCaptureVideoOrientationLandscapeRight;//手机右放（眼对着屏幕）
            }
        }else if ((device_angle > M_PI_4) && (device_angle < 3 * M_PI_4)){
            if(_deviceOrientation != AVCaptureVideoOrientationLandscapeLeft)
            {
                _deviceOrientation = AVCaptureVideoOrientationLandscapeLeft;//手机右放（眼对着屏幕）
            }
        }else{
            if(_deviceOrientation != AVCaptureVideoOrientationPortrait)
            {
                _deviceOrientation = AVCaptureVideoOrientationPortrait;//手机右放（眼对着屏幕）
            }
        }
    } else {
        if ( (device_angle > -M_PI_4) && (device_angle < M_PI_4) ){
            if(_deviceOrientation != AVCaptureVideoOrientationPortraitUpsideDown)
            {
                _deviceOrientation = AVCaptureVideoOrientationPortraitUpsideDown;//手机右放（眼对着屏幕）
            }
        }else if ((device_angle < -M_PI_4) && (device_angle > -3 * M_PI_4)){
            if(_deviceOrientation != AVCaptureVideoOrientationLandscapeRight)
            {
                _deviceOrientation = AVCaptureVideoOrientationLandscapeRight;//手机右放（眼对着屏幕）
            }
        }else if ((device_angle > M_PI_4) && (device_angle < 3 * M_PI_4)){
            if(_deviceOrientation != AVCaptureVideoOrientationLandscapeLeft)
            {
                _deviceOrientation = AVCaptureVideoOrientationLandscapeLeft;//手机右放（眼对着屏幕）
            }
        }else{
            if(_deviceOrientation != AVCaptureVideoOrientationPortrait)
            {
                _deviceOrientation = AVCaptureVideoOrientationPortrait;//手机右放（眼对着屏幕）
            }
        }
    }
    if (![NSDictionary userDefaultsWithKey:UserDefOrientation]) {
        if (_deviceOrientation != AVCaptureVideoOrientationPortrait) {
            UIAlertView *aler = [[UIAlertView alloc]initWithTitle:nil message:Lang(@"aftersale_return_take_picture_tips") delegate:nil cancelButtonTitle:Lang(@"aftersale_return_arlert_message_tips") otherButtonTitles:nil];
            [aler show];
            [NSDictionary setUserDefaults:@"1" forKey:UserDefOrientation];
        }
    }
}

- (AVCaptureVideoOrientation)avOrientationForDeviceOrientation:(UIDeviceOrientation)deviceOrientation
{
    AVCaptureVideoOrientation result = (AVCaptureVideoOrientation)deviceOrientation;
    if ( deviceOrientation == UIDeviceOrientationLandscapeLeft )
        result = AVCaptureVideoOrientationLandscapeRight;
    else if ( deviceOrientation == UIDeviceOrientationLandscapeRight )
        result = AVCaptureVideoOrientationLandscapeLeft;
    return result;
}

#pragma mark 调焦
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
{
    return YES;
}

/*此方法默认，若两个手势同时出现只执行一个*/
-(BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer
{
    return YES;
}

//缩放手势 用于调整焦距
- (void)handlePinchGesture:(UIPinchGestureRecognizer *)recognizer{
    BOOL allTouchesAreOnThePreviewLayer = YES;
    NSUInteger numTouches = [recognizer numberOfTouches], i;
    for ( i = 0; i < numTouches; ++i ) {
        CGPoint location = [recognizer locationOfTouch:i inView:self.cameraView];
        CGPoint convertedLocation = [self.previewLayer convertPoint:location fromLayer:self.previewLayer.superlayer];
        if ( ! [self.previewLayer containsPoint:convertedLocation] ) {
            allTouchesAreOnThePreviewLayer = NO;
            break;
        }
    }
    
    if (recognizer.state == UIGestureRecognizerStateEnded) {
        [self focusAtPoint:CGPointMake(ScreenWidth / 2.f, VIEW_CenterY(self.cameraView))];
    }
}

//检查相机权限
- (BOOL)canUserCamear{
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    if (authStatus == AVAuthorizationStatusDenied) {
        UIAlertView *alertView = [[UIAlertView alloc]initWithTitle:Lang(@"aftersale_return_tips_message_open_camera_auth")
                                                           message:Lang(@"aftersale_return_tips_message_please_modify_in_settings")
                                                          delegate:self
                                                 cancelButtonTitle:Lang(@"after_sale_reason_confirm")
                                                 otherButtonTitles:Lang(@"after_sale_cancel"), nil];
        alertView.tag = 100;
        [alertView show];
        return NO;
    }
    else{
        return YES;
    }
    return YES;
}

#pragma mark 闪光灯
- (void)FlashOn{
    if ([_device lockForConfiguration:nil]) {
        if (_isflashOn) {
            if ([_device isFlashModeSupported:AVCaptureFlashModeOff]) {
                [_device setFlashMode:AVCaptureFlashModeOff];
                _isflashOn = NO;
                [_flashButton setTitle:Lang(@"aftersale_return_button_open") forState:UIControlStateNormal];
            }
        }else{
            if ([_device isFlashModeSupported:AVCaptureFlashModeOn]) {
                [_device setFlashMode:AVCaptureFlashModeOn];
                _isflashOn = YES;
                [_flashButton setTitle:Lang(@"aftersale_return_button_close") forState:UIControlStateNormal];
            }
        }
        
        [_device unlockForConfiguration];
    }
    _flashButton.selected = !_flashButton.selected;
}

#pragma mark 切换前后摄像头
- (void)changeCamera{
    NSUInteger cameraCount = [[AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo] count];
    
    if (cameraCount > 1) {
        NSError *error;
        CATransition *animation = [CATransition animation];
        animation.duration = .5f;
        animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
        animation.type = @"oglFlip";
        AVCaptureDevice *newCamera = nil;
        AVCaptureDeviceInput *newInput = nil;
        AVCaptureDevicePosition position = [[_videoInput device] position];
        if (position == AVCaptureDevicePositionFront){
            newCamera = [self cameraWithPosition:AVCaptureDevicePositionBack];
            animation.subtype = kCATransitionFromLeft;
            _flashButton.hidden = NO;
        }
        else {
            newCamera = [self cameraWithPosition:AVCaptureDevicePositionFront];
            animation.subtype = kCATransitionFromRight;
            _flashButton.hidden = YES;
        }
        
        newInput = [AVCaptureDeviceInput deviceInputWithDevice:newCamera error:nil];
        [self.previewLayer addAnimation:animation forKey:nil];
        if (newInput != nil) {
            [self.session beginConfiguration];
            [self.session removeInput:_videoInput];
            if ([self.session canAddInput:newInput]) {
                [self.session addInput:newInput];
                self.videoInput = newInput;
                
            } else {
                [self.session addInput:self.videoInput];
            }
            [self.session commitConfiguration];
        } else if (error) {
            NSLog(@"toggle carema failed, error = %@", error);
        }
        WEAKSELF
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf focusAtPoint:CGPointMake(ScreenWidth / 2.f, VIEW_CenterY(self.cameraView))];
        });
    }
}
- (AVCaptureDevice *)cameraWithPosition:(AVCaptureDevicePosition)position{
    NSArray *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    for ( AVCaptureDevice *device in devices )
        if ( device.position == position ){
            _device = device;
            return device;
        }
    
    return nil;
}
- (void)focusGesture:(UITapGestureRecognizer*)gesture{
    CGPoint point = [gesture locationInView:gesture.view];
    [self focusAtPoint:point];
}
- (void)focusAtPoint:(CGPoint)point{
    CGSize size = self.view.bounds.size;
    CGPoint focusPoint = CGPointMake( point.y /size.height ,1-point.x/size.width );
    NSError *error;
    if ([self.device lockForConfiguration:&error]) {
        
        if ([self.device isFocusModeSupported:AVCaptureFocusModeAutoFocus]) {
            [self.device setFocusPointOfInterest:focusPoint];
            [self.device setFocusMode:AVCaptureFocusModeAutoFocus];
        }
        
        if ([self.device isExposureModeSupported:AVCaptureExposureModeAutoExpose ]) {
            [self.device setExposurePointOfInterest:focusPoint];
            [self.device setExposureMode:AVCaptureExposureModeAutoExpose];
        }
        
        [self.device unlockForConfiguration];
        _focusView.center = point;
        _focusView.hidden = NO;
        WEAKSELF
        [UIView animateWithDuration:0.3 animations:^{
            weakSelf.focusView.transform = CGAffineTransformMakeScale(1.25, 1.25);
        }completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5 animations:^{
                weakSelf.focusView.transform = CGAffineTransformIdentity;
            } completion:^(BOOL finished) {
                weakSelf.focusView.hidden = YES;
            }];
        }];
    }
    
}

#pragma mark 锐化相关
-(void)sharpBtnClick{
}

-(void)sharpShowProgress{
    NSString *text;
    if (_sharpBtn.selected) {
        text = Lang(@"aftersale_return_button_sharpening_enabled");
    }else{
        text = Lang(@"aftersale_return_button_sharpening_has_been_turned_off");
    }
    //    UIImageView *imageView = [[UIImageView alloc]initWithFrame:CGRectMake((ScreenWidth - 126) / 2.f, 7, 126, 48)];
    UIImageView *imageView = [[UIImageView alloc]initWithFrame:CGRectMake(ScreenWidth / 2.f, 7, 0, 0)];
    imageView.image = [UIImage imageNamed:@"cameraSharpProgress"];;
    imageView.clipsToBounds = YES;
    [self.cameraView addSubview:imageView];
    
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(12, 16, 126 - 24, 24)];
    label.font = [UIFont systemFontOfSize:17];
    label.text = text;
    label.textAlignment = NSTextAlignmentCenter;
    label.adjustsFontSizeToFitWidth = YES;
    [imageView addSubview:label];
    _sharpBtn.enabled = NO;
    
    [UIView animateWithDuration:0.3 animations:^{
        imageView.frame = CGRectMake((ScreenWidth - 126) / 2.f, 7, 126, 48);
    }completion:^(BOOL finished) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [imageView removeFromSuperview];
            _sharpBtn.enabled = YES;
        });
    }];
}

//拍照模式按钮点击事件
-(void)changTakeType:(UIButton *)btn {
    [self changTakeType];
}

-(void)changTakeType{
    UIView *view = [[UIView alloc]init];
    view.center = CGPointMake(ScreenWidth / 2.f, VIEW_H(self.cameraView) / 2.f);
    view.bounds = CGRectMake(0, 0, 200, 70);
    view.backgroundColor = ColorText;
    view.layer.cornerRadius = 5.f;
    view.clipsToBounds = YES;
    view.alpha = 0;
    [self.cameraView addSubview:view];
    UILabel *label = [[UILabel alloc]initWithFrame:CGRectMake(0, 0, VIEW_W(view), VIEW_H(view))];
    label.textColor = [UIColor whiteColor];
    label.font = [UIFont systemFontOfSize:20.];
    label.textAlignment = NSTextAlignmentCenter;
    [view addSubview:label];
    
    if (_isGoOnTake) {
        label.text = Lang(@"aftersale_return_button_take_single");
        _isGoOnTake = NO;
        float oneWide = [NSString countTextCGSize:[UIFont systemFontOfSize:12] viewHeight:100 text:@"单张"].width;
        WEAKSELF
        [UIView animateWithDuration:0.2 animations:^{
//            weakSelf.takeView.left = (ScreenWidth - oneWide)/ 2.f;
        }];
        
        [UIView animateWithDuration:0.4 animations:^{
            view.alpha = 0.8;
        } completion:^(BOOL finished) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [view removeFromSuperview];
            });
        }];
    }else{
    }
    
    if (_isGoOnTake) {
        _preIimageV.hidden = YES;
    }else{
        _preIimageV.hidden = NO;
        _doneBtn.hidden = YES;
    }
}

-(void)changTakePan:(UIPanGestureRecognizer *)pan {
    if (_fousPinch.state == UIGestureRecognizerStateChanged || _fousPinch.state == UIGestureRecognizerStateEnded) {
        return;
    }
    
    CGPoint point=[pan translationInView:self.view];
    
    if (pan.state==UIGestureRecognizerStateEnded) {
        [self changTakeType];
    }
    float oneWide = [NSString countTextCGSize:[UIFont systemFontOfSize:12] viewHeight:100 text:Lang(@"aftersale_return_button_take_single")].width;
    float contWide = [NSString countTextCGSize:[UIFont systemFontOfSize:12] viewHeight:100 text:@"连拍"].width;
    if(pan.state==UIGestureRecognizerStateChanged) {
        [pan setTranslation:CGPointZero inView:self.view];
    }
}

-(void)dealloc{
}

-(void)myProgressTask{
    self.view.userInteractionEnabled = NO;
}

-(void)backVC{
    if (self.replaceBackBlock) {
        self.replaceBackBlock();
    }
    WEAKSELF
    [self dismissViewControllerAnimated:YES completion:^{
        if (weakSelf.backBlock) {
            weakSelf.backBlock();
        }
    }];
}
@end


