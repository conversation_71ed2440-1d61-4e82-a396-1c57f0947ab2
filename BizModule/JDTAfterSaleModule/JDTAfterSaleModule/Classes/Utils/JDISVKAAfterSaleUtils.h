//
//  JDISVKAAfterSaleUtils.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import <Foundation/Foundation.h>
#import <JDISVCategoryModule/NSBundle+JDCDExtend.h>

NS_ASSUME_NONNULL_BEGIN

static inline NSBundle * _Nonnull JDISVKAAfterSaleModuleBunle() {
    return [NSBundle jdcd_moduleWithBundleName:@"JDISVKAAfterSaleModule" class: NSClassFromString(@"JDISVKAAfterSaleUtils")];
}

static inline UIImage * _Nonnull JDISVKAAfterSaleModuleImageNamed(NSString * _Nonnull name) {
    return [UIImage imageNamed:name inBundle:JDISVKAAfterSaleModuleBunle() compatibleWithTraitCollection:nil];
}

@interface JDISVKAAfterSaleUtils : NSObject

@end

@interface UIImage (KSAAddress)

+ (NSBundle *)jdisv_after_imageBundle;
+ (nullable UIImage *)jdisv_after_imageNamed:(NSString *)name;

@end

NS_ASSUME_NONNULL_END
