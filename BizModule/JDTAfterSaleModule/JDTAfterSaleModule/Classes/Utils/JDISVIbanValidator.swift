//
//  StringExtension.swift
//  JDISVKAAfterSaleModule
//
//  Created by <PERSON><PERSON><PERSON>han on 15/12/2024.
//

import Foundation

@objcMembers
public class JDISVIbanValidator: NSObject {
    //  IBAN using the ISO 13616-1 standard
    public func isValidIBAN(_ iban: String) -> Bool {
        // Remove spaces and make uppercase
        let sanitizedIBAN = iban.replacingOccurrences(of: " ", with: "").uppercased()
        // Check the basic length requirement
        guard sanitizedIBAN.count >= 15 && sanitizedIBAN.count <= 34 else {
            return false
        }
        // Regular expression for basic IBAN format validation
        let ibanRegex = "^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$"
        let ibanPredicate = NSPredicate(format: "SELF MATCHES %@", ibanRegex)
        guard ibanPredicate.evaluate(with: sanitizedIBAN) else {
            return false
        }
        // Rearrange IBAN (move first 4 characters to the end)
        let rearrangedIBAN = sanitizedIBAN.dropFirst(4) + sanitizedIBAN.prefix(4)
        // Convert letters to numbers (A = 10, B = 11, ..., Z = 35)
        let convertedIBAN = rearrangedIBAN.compactMap { character -> String? in
            if let digit = character.wholeNumberValue {
                return String(digit)
            } else if let asciiValue = character.asciiValue, asciiValue >= 65 && asciiValue <= 90 {
                return String(asciiValue - 55) // A=10, B=11, ..., Z=35
            } else {
                return nil
            }
        }.joined()
        // Perform modulo 97 operation
        guard let ibanNumber = BigInt(convertedIBAN) else {
            return false
        }
        return ibanNumber % 97 == 1
    }
}

// A helper BigInt type for large numbers
struct BigInt {
    private let digits: [UInt8]
    
    init?(_ numberString: String) {
        guard numberString.allSatisfy({ $0.isNumber }) else {
            return nil
        }
        self.digits = numberString.compactMap { UInt8(String($0)) }
    }
    
    static func % (lhs: BigInt, rhs: Int) -> Int {
        var remainder = 0
        for digit in lhs.digits {
            remainder = (remainder * 10 + Int(digit)) % rhs
        }
        return remainder
    }
}
