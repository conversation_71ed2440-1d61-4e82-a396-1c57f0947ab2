//
//  JDISVKAAfterSaleUtils.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAAfterSaleUtils.h"
#import "JDISVKAAfterSaleModule.h"

@implementation JDISVKAAfterSaleUtils

@end

@implementation UIImage (KSAAddress)

+ (NSBundle *)jdisv_after_imageBundle {
    
    static NSBundle *afterBundle = nil;
    if (afterBundle == nil) {
#ifdef SWIFT_PACKAGE
        NSBundle *containnerBundle = SWIFTPM_MODULE_BUNDLE;
#else
        NSBundle *containnerBundle = [NSBundle bundleForClass:[JDISVKAAfterSaleModule class]];
#endif
        // 添加调试输出
        NSLog(@"Container bundle path: %@", containnerBundle.bundlePath);
        afterBundle = [NSBundle bundleWithPath:[containnerBundle pathForResource:@"JDISVKAAfterSaleImage" ofType:@"bundle"]];
        if (afterBundle == nil) {
            NSLog(@"Failed to find JDISVKAAfterSaleImage bundle.");
        }
    }
     return afterBundle;
 }

+(nullable UIImage *)jdisv_after_imageNamed:(NSString *)name {
    NSBundle *currentBundle = [UIImage jdisv_after_imageBundle];
    return [UIImage imageNamed:name inBundle:currentBundle compatibleWithTraitCollection:nil];
}

@end
