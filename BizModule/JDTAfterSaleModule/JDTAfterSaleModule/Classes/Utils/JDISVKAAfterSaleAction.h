//
//  JDISVKAAfterSaleAction.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger,JDISVKAAfterSaleActionType) {
    //售后列表
    JDISVKAAfterSaleActionTypeWithReplaceNew,  //点击申请换货
    JDISVKAAfterSaleActionTypeWithReplaceNewClickAddress, //点击申请换货中的地址按钮
    JDISVKAAfterSaleActionTypeWithReturnCommodity,  //点击申请退货
    JDISVKAAfterSaleActionTypeWithClickHeaderApplyButton,  //点击header里的售后申请
    JDISVKAAfterSaleActionTypeWithClickHeaderRecordButton,  //点击header里的售后详情
    JDISVKAAfterSaleActionTypeWithBack,  //点击导航栏的返回
    JDISVKAAfterSaleActionTypeWithClickRecordApplyDetailView,  //点击申请详情里面的审核区域
    JDISVKAAfterSaleActionTypeWithReturnDamaged, // 坏品售后
    
    //售后申请详情
    JDISVKAAfterSaleActionTypeWithStepperReachMax,
    JDISVKAAfterSaleActionTypeWithStepperClicked,
    JDISVKAAfterSaleActionTypeWithApplyCauseEditied,
    JDISVKAAfterSaleActionTypeWithSelectReason,
    JDISVKAAfterSaleActionTypeWithSelectBankName,
    JDISVKAAfterSaleActionTypeWithSelectReturnModel,
    JDISVKAAfterSaleActionTypeWithBankInfoEditied,
    JDISVKAAfterSaleActionTypeWithRefund,


    //售后详情页点击
    JDISVKAAfterSaleActionTypeWithDetailClickPhone,      //点击退货门店联系方式
    JDISVKAAfterSaleActionTypeWithDetailClickCommodity,  //点击商品信息
    JDISVKAAfterSaleActionTypeWithDetailClickContactService, //点击联系客服
    JDISVKAAfterSaleActionTypeWithDetailDeliveryInfoTextFiledBeginEditing, //快递信息输入框开始编辑
    JDISVKAAfterSaleActionTypeWithDetailDeliveryInfoTextFiledEndEditing,
    JDISVKAAfterSaleActionTypeWithDetailDeliveryInfoTextFiledReturn,
    JDISVKAAfterSaleActionTypeWithDetailCopyOrderInfoSuccess,             //复制快递信息成功
};

@interface JDISVKAAfterSaleAction : NSObject

@property (nonatomic, assign,readonly) JDISVKAAfterSaleActionType actionType;
@property (nonatomic, strong,nullable) id sender;
@property (nonatomic, strong,nullable) id value;
@property (nonatomic, assign)NSInteger tag;

- (instancetype)init __attribute__((unavailable("must use - initWithType:")));

- (instancetype)initWithType:(JDISVKAAfterSaleActionType)actionType NS_DESIGNATED_INITIALIZER;

+ (instancetype)actionWithType:(JDISVKAAfterSaleActionType)actionType;

@end

NS_ASSUME_NONNULL_END
