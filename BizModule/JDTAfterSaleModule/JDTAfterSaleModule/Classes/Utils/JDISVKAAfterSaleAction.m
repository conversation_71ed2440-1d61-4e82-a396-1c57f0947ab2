//
//  JDISVKAAfterSaleAction.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/22.
//

#import "JDISVKAAfterSaleAction.h"

@interface JDISVKAAfterSaleAction()

@property (nonatomic, assign,readwrite) JDISVKAAfterSaleActionType actionType;

@end

@implementation JDISVKAAfterSaleAction

- (instancetype)initWithType:(JDISVKAAfterSaleActionType)actionType {
    if (self = [super init]) {
        _actionType = actionType;
    }
    return self;
}

+ (instancetype)actionWithType:(JDISVKAAfterSaleActionType)actionType{
    return [[JDISVKAAfterSaleAction alloc] initWithType:actionType];
}

@end
