//
//  JDISVAfterSaleOpenAppProtocolHandler.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/12/13.
//

#import "JDISVAfterSaleOpenAppProtocolHandler.h"

#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
@JDProtocolHandlerRegister(jump_aftersale,JDISVAfterSaleOpenAppProtocolHandler);

@implementation JDISVAfterSaleOpenAppProtocolHandler

- (BOOL)handleProtocolWithWebView:(id)webView completion:
(JDProtocolHandleFinishedBlock)completion {
    [super handleProtocolWithWebView:webView
                          completion:completion];
    
    
    if (self.model.params[@"arg"]) {
        NSDictionary *args = @{@"afsServiceId":self.model.params[@"arg"]};
        [JDRouter openURL:[NSString stringWithFormat:@"router://%@/getAfterSalesDetailViewController",[[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeAfterSales]] arg:args error:nil completion:^(UIViewController * viewController) {
            if(viewController){
                viewController.hidesBottomBarWhenPushed = true;
                [self.navigationController pushViewController:viewController animated:YES];
            }
        }];
    }else{
        [JDRouter openURL:[NSString stringWithFormat:@"router://%@/getAfterSaleApplyListViewController",[[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeAfterSales]] arg:nil error:nil completion:^(UIViewController * viewController) {
            if(viewController){
                viewController.hidesBottomBarWhenPushed = true;
                [self.navigationController pushViewController:viewController animated:YES];
            }
        }];
    }
    return YES;
}
 
- (UINavigationController *)navigationController {
    
    UINavigationController* resultController = [UIApplication
                                                sharedApplication].delegate.window.rootViewController;
    if([resultController isKindOfClass:UITabBarController.class]){
        UITabBarController* tabController = (UITabBarController*) resultController;

        resultController = tabController.selectedViewController;
    }
    if ([resultController isKindOfClass:UINavigationController.class]){
        return resultController;
    }
    return nil;
}

@end

