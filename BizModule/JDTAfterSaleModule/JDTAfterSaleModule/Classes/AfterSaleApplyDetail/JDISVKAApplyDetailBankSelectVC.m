//
//  JDISVKAApplyDetailBankSelectVC.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/6/2.
//

#import "JDISVKAApplyDetailBankSelectVC.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>

#import "JDISVKAApplyDetailBankSelectCell.h"


@interface JDISVKAApplyDetailBankSelectVC ()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) NSIndexPath *selectedIndexPath;

@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UIButton *sureButton;
@property (nonatomic, strong) UILabel* labelTitle;

@end

@implementation JDISVKAApplyDetailBankSelectVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupData];
    [self setupUI];
   
}

- (void)setupData{
    _delegate = [RACSubject subject];
    
    [self setBankListData];
    
    self.selectedIndexPath = [NSIndexPath indexPathForRow:0 inSection:0];
    if(self.selectedBankName.length){
        for (int i = 0; i<self.datasource.count; i++) {
            JDISVKAApplyDetailBankNameModel * model = self.datasource[i];
            if([model.bankName isEqualToString:self.selectedBankName]){
                model.selected = YES;
                self.sureButton.enabled = YES;
                self.selectedIndexPath = [NSIndexPath indexPathForRow:i inSection:0];
                break;
            }
        }
    }
}

- (void)setupUI{
    self.view.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    
    [self.view addSubview:self.labelTitle];
    [self.labelTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).mas_offset(18);
        make.leading.trailing.mas_equalTo(self.view);
        make.height.mas_equalTo(26);
    }];
    
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.width.height.mas_equalTo(18);
        make.trailing.mas_equalTo(0);
    }];
    
    [self.view addSubview:self.sureButton];
    [self.sureButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.height.mas_equalTo(40);
        if (@available(iOS 11.0, *)) {
            make.bottom.mas_equalTo(self.view.mas_safeAreaLayoutGuideBottom).mas_offset(-5);
        }else {
            make.bottom.mas_equalTo(self.view.mas_bottom).mas_offset(-5);
        }
    }];
    
    [self.tableView registerClass:[JDISVKAApplyDetailBankSelectCell class] forCellReuseIdentifier:@"cell"];
    [self.view addSubview:self.tableView];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.top.mas_equalTo(self.labelTitle).mas_offset(33);
        make.bottom.mas_equalTo(self.sureButton.mas_top).mas_offset(-10);
    }];
    [self scrollToSelected];
}

- (void)updateWithData:(NSArray *)data {
    self.datasource = data;
    [self.tableView reloadData];
}

- (void)scrollToSelected {
//    CGRect rect = [self.tableView rectForRowAtIndexPath:self.selectedIndexPath];
//    [self.tableView scrollRectToVisible:rect animated:NO];
    
    [self.tableView scrollToRowAtIndexPath:self.selectedIndexPath atScrollPosition:UITableViewScrollPositionTop animated:YES];

}

- (void)closeButtonClicked {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)sureButtonClicked {
    
    if(self.sureButtonCallBack){
        JDISVKAApplyDetailBankNameModel* selectedModel;
        for(JDISVKAApplyDetailBankNameModel* model in self.datasource){
            if(model.selected){
                selectedModel = model;
                break;
            }
        }
        if(selectedModel){
            self.sureButtonCallBack(selectedModel.bankName);
        }
    }
    [self closeButtonClicked];
    
}

#pragma mark -  UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.datasource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVKAApplyDetailBankSelectCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];
    JDISVKAApplyDetailBankNameModel *model = self.datasource[indexPath.row];
    [cell configureWithItemModel:model];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    
    JDISVKAApplyDetailBankNameModel* model = self.datasource[indexPath.row];
    CGFloat arrowWidth = 0;
    if(model.selected){
        arrowWidth = 20+4;
    }
    CGFloat width = [UIScreen mainScreen].bounds.size.width - 36-17-18-arrowWidth;
    
    CGSize size = [model.bankName sizeWithFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constrainedToSize:CGSizeMake(width, 1000)];
    CGFloat cellHeight = size.height+18;
    if(cellHeight<38){
        cellHeight = 38;
    }
    return cellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    JDISVKAApplyDetailBankNameModel *model = self.datasource[indexPath.row];
    
    for (JDISVKAApplyDetailBankNameModel *model in self.datasource) {
        model.selected = NO;
    }
    
    model.selected = YES;
    self.sureButton.enabled = YES;
    [self.delegate sendNext:model];
    [tableView reloadData];
}

#pragma mark - getter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.tableFooterView = [UIView new];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = YES;
        _tableView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");

    }
    return _tableView;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_sureButton setTitle:Lang(@"after_sale_reason_confirm") forState:UIControlStateNormal];
        [_sureButton addTarget:self action:@selector(sureButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        
        CGFloat r50 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R50"];
        [_sureButton renderB1WithCornerRadius:r50];
        _sureButton.enabled = NO;
    }
    return _sureButton;
}

- (UILabel*)labelTitle{
    if(!_labelTitle){
        _labelTitle = [[UILabel alloc] init];
        _labelTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _labelTitle.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:(UIFontWeightMedium)];
        _labelTitle.text = Lang(@"after_sale_apply_bankName");
        _labelTitle.textAlignment = UITextAlignmentCenter;
    }
    return _labelTitle;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
        [_closeButton setImage:image forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        _closeButton.layer.cornerRadius = 9;
        _closeButton.layer.masksToBounds = YES;
        _closeButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _closeButton;
}

- (void)setBankListData{
//    NSArray * bankNameArr = @[@"Al Rajhi Bank",
//                              @"Alinma bank",
//                              @"Arab National Bank",
//                              @"Bank AlBilad",
//                              @"Bank AlJazira",
//                              @"Bank Muscat",
//                              @"Banque Saudi Fransi",
//                              @"BNP Paribas",
//                              @"Credit Suisse",
//                              @"Deutsche Bank",
//                              @"Emirates NBD",
//                              @"First Abu Dhabi Bank (FAB)",
//                              @"Gulf International Bank Saudi Arabia (GIB-SA)",
//                              @"Industrial and Commercial Bank of China (ICBC)",
//                              @"J.P. Morgan Chase N.A",
//                              @"MUFG Bank, Ltd.",
//                              @"National Bank of Bahrain (NBB)",
//                              @"National Bank of Kuwait (NBK)",
//                              @"National Bank of Pakistan (NBP)",
//                              @"Qatar National Bank (QNB)",
//                              @"Riyad Bank",
//                              @"Saudi Investment Bank",
//                              @"Saudi National Bank",
//                              @"Standard Chartered Bank",
//                              @"State Bank of India (SBI)",
//                              @"The Saudi British Bank (SABB)",
//                              @"Trade bank of Iraq",
//                              @"Ziraat Bankası",
//    ];
    
//    NSArray * bankNameArr_AR = @[@"مصرف الراجحي",
//                                 @"مصرف الإنماء",
//                                 @"البنك العربي الوطني",
//                                 @"بنك البلاد",
//                                 @"بنك الجزيرة",
//                                 @"Bank Muscat",
//                                 @"البنك السعودي الفرنسي",
//                                 @"BNP Paribas",
//                                 @"Credit Suisse",
//                                 @"Deutsche Bank",
//                                 @"Emirates NBD",
//                                 @"First Abu Dhabi Bank (FAB)",
//                                 @"بنك الخليج الدولي - السعودية",
//                                 @"Industrial and Commercial Bank of China (ICBC)",
//                                 @"J.P. Morgan Chase N.A",
//                                 @"MUFG Bank, Ltd.",
//                                 @"National Bank of Bahrain (NBB)",
//                                 @"National Bank of Kuwait (NBK)",
//                                 @"National Bank of Pakistan (NBP)",
//                                 @"Qatar National Bank (QNB)",
//                                 @"بنك الرياض",
//                                 @"البنك السعودي للاستثمار",
//                                 @"البنك الأهلي السعودي",
//                                 @"Standard Chartered Bank",
//                                 @"State Bank of India (SBI)",
//                                 @"البنك السعودي البريطاني (ساب))",
//                                 @"Trade bank of Iraq",
//                                 @"Ziraat Bankası",
//    ];
    
//    NSArray * bankListArr;
//    if([[NSString getKAUseLang] isEqualToString:@"ar"]){
//        bankListArr = [bankNameArr_AR copy];
//    }else{
//        bankListArr = [bankNameArr copy];
//    }
    
    NSArray * bankListArr = [self.datasource copy];
    NSMutableArray * datasource = [NSMutableArray array];
    for (NSString *bankName in bankListArr) {
        JDISVKAApplyDetailBankNameModel * m = [JDISVKAApplyDetailBankNameModel new];
        m.bankName = bankName;
        m.selected = NO;
        [datasource addObject:m];
    }
    self.datasource = [datasource copy];
}
@end
