//
//  JDISVKAApplyDetailMainModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/11/2.
//

#import <Foundation/Foundation.h>
#import "JDISVWeekCalendarUitilit.h"
#import "JDISVLogisticData.h"
NS_ASSUME_NONNULL_BEGIN

@interface applyDetailVenderInfo : NSObject

@property(nonatomic,assign)NSInteger venderId;

@property(nonatomic,copy)NSString *venderName;

@end

@interface applyDetailAddressInfoVO : NSObject

@property(nonatomic,assign)NSInteger addressId;

@property(nonatomic,copy)NSString *address;

@property(nonatomic,assign)NSInteger provinceId;

@property(nonatomic,assign)NSInteger cityId; //

@property(nonatomic,assign)NSInteger countyId; //

@property(nonatomic,assign)NSInteger villageId; //


@end

@interface applyDetailAfsExpressInfoVo : NSObject
@property(nonatomic,copy)NSString *expressNo;//快递单号
@property(nonatomic,assign) NSInteger nums;//商品数量
@property(nonatomic,strong) NSArray<JDISVLogisticData*>* umsList;
@end

@interface applyDetailAfsContactInfoVo : NSObject

@property(nonatomic,copy)NSString *contactName;
@property(nonatomic,copy)NSString *firstName;
@property(nonatomic,copy)NSString *lastName;

@property(nonatomic,copy)NSString *contactTel;

@property(nonatomic,copy)NSString *expressNo;//快递单号
@property(nonatomic,copy)NSArray <applyDetailAfsExpressInfoVo*>*expressInfos;//快递单号数组<applyDetailAfsExpressInfoVo>

@property(nonatomic,copy)NSString *expressCompany;//承运商

@property(nonatomic,strong)applyDetailAddressInfoVO *addressInfo;

@end


@interface applyDetailGeneralTypeVo : NSObject

@property(nonatomic,assign)NSInteger type;

@property(nonatomic,copy)NSString *text;

@property(nonatomic,strong)JDISVWeekModel *dateModelSelect;

@end

@interface applyDetailAfsSkuDetailVo : NSObject


@property (nonatomic,assign)NSInteger skuType;


@property(nonatomic,strong)NSNumber *skuSingleShouldPrice;

@property(nonatomic,assign)NSInteger skuCount;

@property(nonatomic,assign)NSInteger skuId;

@property(nonatomic,assign)NSInteger canApplyCount;

@property(nonatomic,copy)NSString *skuImg;

@property(nonatomic,copy)NSString *skuName;

@property (copy,nonatomic) NSString* skuUUid;

@property(nonatomic,strong)NSArray<applyDetailGeneralTypeVo*> *refundTypeList;

@property(nonatomic,strong)NSArray<applyDetailGeneralTypeVo*> *returnModelList;

@end

@interface JDISVKAApplyDetailMainModel : NSObject

@property(nonatomic,copy)NSArray<applyDetailAfsSkuDetailVo *> *skuList;

@property(nonatomic,strong)applyDetailVenderInfo *venderInfo;

@property(nonatomic,strong)applyDetailAfsContactInfoVo *returnCustomerInfo;

@property(nonatomic,assign)NSInteger orderId;

@property(nonatomic,assign)BOOL orderHasGift;//订单是否有赠品

@property(nonatomic,assign)NSInteger orderType;
@property(nonatomic,assign)BOOL orderSelf;//是否自营订单

@property(nonatomic,assign)NSInteger orderStatus;

@property(nonatomic,strong)NSDate *bookTime;

@end

@interface JDISVKAGiftModel : NSObject
@property(nonatomic,assign)NSInteger wareId;//商品编号

@property(copy,nonatomic) NSString* skuUuid;

@property(copy,nonatomic) NSString* giftRefUuid;//赠品关联主商品uuid

@property(nonatomic,assign)NSInteger num;//商品在该订单中的数量，该数量为拆分后商品分到当前订单中的数量

@property(nonatomic,strong)NSNumber *warePrice;//商品价格

@property(nonatomic,assign)NSInteger canApplyNum;//可申请数量

@property(nonatomic,assign)NSInteger appliedNum;//已申请数量

@property(copy,nonatomic) NSString* skuName;//商品名称

@property(copy,nonatomic) NSString* imageUrl;//商品图片

@end


NS_ASSUME_NONNULL_END
