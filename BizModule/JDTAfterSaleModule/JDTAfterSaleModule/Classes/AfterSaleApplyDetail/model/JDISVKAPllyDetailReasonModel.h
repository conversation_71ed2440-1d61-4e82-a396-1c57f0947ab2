//
//  JDISVKAPllyDetailReasonModel.h
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2022/11/11.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAPllyDetailReasonModel : NSObject
@property (copy, nonatomic) NSString* reasonId;
@property (copy, nonatomic) NSString* reasonText;
@property (assign, nonatomic) BOOL selected;
@end

@interface JDISVKAApplyDetailBankNameModel : NSObject
@property (copy, nonatomic) NSString* bankName;
@property (assign, nonatomic) BOOL selected;
@end



NS_ASSUME_NONNULL_END
