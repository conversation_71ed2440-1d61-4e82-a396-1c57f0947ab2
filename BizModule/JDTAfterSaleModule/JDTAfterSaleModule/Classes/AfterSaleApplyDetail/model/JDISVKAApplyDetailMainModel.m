//
//  JDISVKAApplyDetailMainModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/11/2.
//

#import "JDISVKAApplyDetailMainModel.h"

@implementation applyDetailVenderInfo

@end

@implementation applyDetailAfsExpressInfoVo
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"umsList":@"JDISVLogisticData",
    };
}
@end

@implementation applyDetailAfsContactInfoVo
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
             @"expressInfos":@"applyDetailAfsExpressInfoVo",
    };
}
@end

@implementation applyDetailAddressInfoVO

@end

@implementation applyDetailGeneralTypeVo

@end

@implementation applyDetailAfsSkuDetailVo

@end

@implementation JDISVKAApplyDetailMainModel

@end

@implementation JDISVKAGiftModel

@end
