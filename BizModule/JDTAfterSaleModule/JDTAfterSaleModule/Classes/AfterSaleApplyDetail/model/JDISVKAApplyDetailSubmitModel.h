//
//  JDISVKAApplyDetailSubmitModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/11/3.
//

#import <Foundation/Foundation.h>
@class JDISVApplyExpandInfoReq;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyDetailSubmitModel : NSObject

@property(nonatomic,assign)NSInteger orderId; //订单id

@property(nonatomic,assign)NSInteger venderId; //商户id

@property(nonatomic,assign)NSInteger skuId; //商品id

@property (nonatomic,assign)NSInteger customerExpect; //服务类型    10退，20换，30修（虚拟）

@property (nonatomic,strong)NSNumber *returnModel; //返件类型    40=客户发货，72=上门取件

//@property(nonatomic,strong)JDISVApplyExpandInfoReq * applyExpandInfo;

//@property (nonatomic,strong)NSNumber * reserveDateBegin;//预约取件开始时间戳，回显主要用这个。本次需求传当天的00:00:00
//@property (nonatomic,strong)NSNumber * reserveDateEnd;//预约取件结束时间戳。本次需求传当天的23:59:59


@property (nonatomic,strong)NSNumber *refundType; //退款类型
@property (nonatomic,copy)NSString *bankName;
@property (nonatomic,copy)NSString *bankCode;
@property (nonatomic,copy)NSString *bankAccountHolder;
@property (nonatomic,copy)NSString *bankTel;

@property (nonatomic,assign)NSInteger applyCount; //申请商品数量

@property (nonatomic,copy)NSString *skuUUid;

// qestion image path
@property (nonatomic,copy)NSString *questionPic;
// 0普通售后/Ordinary after-sales service，1坏品售后/Defective product after-sales service
@property (nonatomic,assign)NSInteger extBusinessType;
@property (nonatomic,copy)NSString *questionDesc; //问题描述

@property (nonatomic,copy)NSString *contactName; //收件人姓名

@property (nonatomic,copy)NSString *contactTel; //收件人电话

@property (nonatomic,copy)NSString *contactTelCountry; //客户电话国际区号

@property (nonatomic,copy)NSString *returnAddr; //客户收件信息，服务类型退货的可以不填

@property(nonatomic,assign)NSInteger returnAddrFirstId; //

@property(nonatomic,assign)NSInteger returnAddrSecondId; //

@property(nonatomic,assign)NSInteger returnAddrThirdId; //

@property(nonatomic,assign)NSInteger returnAddrFourthId; //

@property(nonatomic,assign)NSInteger returnAddrId; //

@property(nonatomic,assign)NSInteger productType;



@property (nonatomic,copy)NSString *pickupContactName;
@property (nonatomic,copy)NSString *pickupContactLastName;
@property (nonatomic,copy)NSString *contactLastName;

@property (nonatomic,copy)NSString *pickupContactTel;

@property (nonatomic,copy)NSString *pickupContactTelCountry; //缺少

@property (nonatomic,copy)NSString *pickwareAddr;

@property(nonatomic,assign)NSInteger pickwareAddrFirstId;

@property(nonatomic,assign)NSInteger pickwareAddrSecondId;

@property(nonatomic,assign)NSInteger pickwareAddrThirdId;

@property(nonatomic,assign)NSInteger pickwareAddrFourthId;

@property(nonatomic,assign)NSInteger pickupAddrId;//缺少

@property(nonatomic,assign)NSInteger serviceType;





/*
 售后中台枚举
 1=Defective
 2=Damaged on arrival
 3=Expired
 4=Missing items
 5=Not as advertised
 6=Change of mind
 7=Wrong item
 8=Others
 */
@property (nonatomic,assign)NSInteger reasonId; //退货原因枚举，产品定一个前端写死传过来即可

@property (nonatomic,copy)NSString *pin;

@end

@interface JDISVApplyExpandInfoReq : NSObject

//@property (nonatomic,strong)NSNumber * reserveDateBegin;//预约取件开始时间戳，回显主要用这个。本次需求传当天的00:00:00
//@property (nonatomic,strong)NSNumber * reserveDateEnd;//预约取件结束时间戳。本次需求传当天的23:59:59
//@property (nonatomic,strong)NSString * reserveDateStr;//预约时间段字符串

@end




NS_ASSUME_NONNULL_END

