//
//  JDISVKAApplDetailReasonCell.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2022/11/10.
//

#import "JDISVKAApplDetailReasonCell.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeColor.h>

#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVCategoryModule/UIView+JDCDCorners.h>

@interface JDISVKAApplDetailReasonCell ()
@property (nonatomic, strong) UIButton* selectImg;
@property (nonatomic, strong) UILabel *labelName;
@end

@implementation JDISVKAApplDetailReasonCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.selectImg = [UIButton buttonWithType:UIButtonTypeCustom];
        self.selectImg.userInteractionEnabled = NO;
        [self.contentView addSubview:self.selectImg];
        [self addSubview:self.selectImg];
        [self.selectImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(0);
            make.centerY.mas_equalTo(self);
            make.width.height.mas_equalTo(17);
        }];
        self.labelName = [[UILabel alloc] init];
        [self addSubview:self.labelName];
        [self.labelName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.centerY.mas_equalTo(self);
            make.leading.mas_equalTo(self.selectImg.mas_trailing).mas_offset(18);
            make.trailing.mas_equalTo(self);
        }];
        self.labelName.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightSemibold];
        self.labelName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        self.labelName.numberOfLines = 0;
       
    }
    return self;
}

-(void)setTitle:(NSString *)title{
    _title = title;
    self.labelName.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightSemibold];
    self.labelName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.labelName.text = title;
}

- (void)setSelectedItem:(BOOL)selectedItem {
    self.selectImg.jdisv_selected_B7 = selectedItem;
}

@end
