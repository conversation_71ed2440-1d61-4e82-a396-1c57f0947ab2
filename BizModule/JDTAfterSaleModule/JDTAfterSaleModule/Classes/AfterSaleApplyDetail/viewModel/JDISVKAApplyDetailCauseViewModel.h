//
//  JDISVKAApplyDetailCauseViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailBaseViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@class RACSignal;
@interface JDISVKAApplyDetailCauseViewModel : JDISVKAApplyDetailBaseViewModel

@property (nonatomic, copy)NSString *userApplyCauseString;
@property(nonatomic,weak)UITableView *tableView;
@property(nonatomic,weak)UIViewController *viewController;
@property (nonatomic, assign)CGFloat photosRwoHeight;
@property (nonatomic, assign)CGFloat photosHeight;
@property (nonatomic,assign)NSInteger maxNum;
@property (nonatomic,strong)NSArray *photos;
@property (nonatomic, copy)NSString *uploadTips;


- (RACSignal *)uploadDamagePhoto:(NSData *)photoData;
- (RACSignal *)uploadDamagePhotoDataArr:(NSArray *)photoDataArray;

@end

NS_ASSUME_NONNULL_END
