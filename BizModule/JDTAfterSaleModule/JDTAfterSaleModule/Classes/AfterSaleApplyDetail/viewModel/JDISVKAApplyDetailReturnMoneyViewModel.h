//
//  JDISVKAApplyDetailReturnMoneyViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/26.
//

#import "JDISVKAApplyDetailBaseViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyDetailReturnMoneyViewModel : JDISVKAApplyDetailBaseViewModel
@property(nonatomic,assign)NSInteger type;//20, "direct_back", 30,no_direct_back(user fill bank info), 40 Point Refund
@property (nonatomic,copy)NSString *returnMoneyValue;
@property (nonatomic,copy)NSString *bankName;
@property (nonatomic,copy)NSString *bankCode;
@property (nonatomic,copy)NSString *bankAccountHolder;
@property (nonatomic,copy)NSString *bankTel;
@property (assign,nonatomic) BOOL returnBank;

@property (assign,nonatomic) BOOL needShowPointRefund;
@property (copy,nonatomic) NSString *refundPointinfo;

@end

NS_ASSUME_NONNULL_END
