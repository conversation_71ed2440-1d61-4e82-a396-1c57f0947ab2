//
//  JDISVKAApplyDetailReturnViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailBaseViewModel.h"
#import "JDISVKAApplyDetailMainModel.h"


NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyDetailReturnViewModel : JDISVKAApplyDetailBaseViewModel

@property (nonatomic,assign)bool isShowAddress;

@property (nonatomic,strong)NSArray <applyDetailGeneralTypeVo *>*returnModelList;      //退货方式


@property (nonatomic,strong)applyDetailGeneralTypeVo *selectedReturnModel;//选中的取件方式


@property (nonatomic,copy)NSString *returnTypeValue;      //退货方式

@property (nonatomic,copy)NSString *receiverAddress;

/***>
 * 收货人姓名
 * Receiver Name
 */
@property (nonatomic,copy)NSString *receiverName;
/***>
 * 收货人姓名 - first Name
 * Receiver Name - first Name
 */
@property (nonatomic,copy)NSString *firstName;
/***>
 * 收货人姓名 - last Name
 * Receiver Name - last Name
 */
@property (nonatomic,copy)NSString *lastName;

@property (nonatomic,copy)NSString *receiverPhone;

@property (nonatomic,copy)NSString *pageType;

@end

NS_ASSUME_NONNULL_END
