//
//  JDISVKAApplyDetailReturnReasonViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2022/11/10.
//

#import "JDISVKAApplyDetailReturnReasonViewModel.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
@implementation JDISVKAApplyDetailReturnReasonViewModel

- (NSString *)identifier{
    return @"JDISVKAApplyDetailReturnReasonCell";
}

- (CGFloat)cellHeight{
    return 97 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
}

- (void)updataViewModel{
    
}

@end
