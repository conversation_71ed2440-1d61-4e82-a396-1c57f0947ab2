//
//  JDISVKAApplyDetailReturnMoneyViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/26.
//

#import "JDISVKAApplyDetailReturnMoneyViewModel.h"
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@implementation JDISVKAApplyDetailReturnMoneyViewModel

- (NSString *)identifier{
    return @"JDISVKAApplyDetailReturnMoneyCell";
}

- (CGFloat)cellHeight{
    if(self.type == 30){
        if(self.needShowPointRefund){
            if(self.returnBank){
                CGSize warningLabelSize = [Lang(@"after_sale_apply_warning") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-93, 500)];
                
                return 392 + warningLabelSize.height+17 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
            }else{
                CGSize warningLabelSize = [Lang(@"after_sale_apply_point_warning") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-93, 500)];
                
                return 58 + warningLabelSize.height+16+18 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
            }
        }else{
            CGSize warningLabelSize = [Lang(@"after_sale_apply_warning") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:(UIFontWeightRegular)] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-93, 500)];
            
            return 392 + warningLabelSize.height+17 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
        }
        
    }
    
    return 56 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    
}

@end
