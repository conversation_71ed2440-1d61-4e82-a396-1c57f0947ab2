//
//  JDISVKAApplyDetailMainViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailMainViewModel.h"
#import "JDISVKAApplyDetailReturnViewModel.h"
#import "JDISVKAApplyDetailReturnReceiveViewModel.h"
#import "JDISVKAApplyDetailCauseViewModel.h"
#import "JDISVKAApplyDetailCommodityViewModel.h"
#import "JDISVKAApplyDetailReturnMoneyViewModel.h"
#import "JDISVKAApplyDetailReturnReasonViewModel.h"
#import "JDISVCategoryModule/NSDictionary+JDCDExtend.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVCategoryModule/NSObject+JDCDExtend.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVYYModelModule/YYModel.h>
#import "JDISVKAApplyDetailMainModel.h"
#import "JDISVKAAfterSaleCustomModel.h"
#import "JDISVKAPllyDetailReasonModel.h"

#import <JDISVPlatformModule/JDISVAddress.h>
#import "JDISVKAApplyDetailSubmitModel.h"
#import <JDISVKAAfterSaleModule/NSString+AfterExtension.h>

@interface JDISVKAApplyDetailMainViewModel()
@property (nonatomic, strong) NSURLSessionDataTask *getGiftTask;

@end

@implementation JDISVKAApplyDetailMainViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.requestStatus = NO;
        NSMutableArray *array = [[NSMutableArray alloc] init];
        
        //商品
        JDISVKAApplyDetailCommodityViewModel *commodityViewModel = [[JDISVKAApplyDetailCommodityViewModel alloc] init];
        
        //原因
        JDISVKAApplyDetailReturnReasonViewModel* resonViewModel = [[JDISVKAApplyDetailReturnReasonViewModel alloc] init];
        
        //描述
        JDISVKAApplyDetailCauseViewModel *causeViewModel = [[JDISVKAApplyDetailCauseViewModel alloc] init];
        
        //退款方式（退回原账户）
        JDISVKAApplyDetailReturnMoneyViewModel *returnMoneyViewModel = [[JDISVKAApplyDetailReturnMoneyViewModel alloc] init];
        
        //退货信息 （退货方式+收货地址）
        JDISVKAApplyDetailReturnViewModel *returnViewModel = [[JDISVKAApplyDetailReturnViewModel alloc] init];
        
        //客户收货地址
        JDISVKAApplyDetailReturnReceiveViewModel *returnReceiveViewModel = [[JDISVKAApplyDetailReturnReceiveViewModel alloc] init];
        
        [array addObject:commodityViewModel];
        [array addObject:resonViewModel]; //原因
        [array addObject:causeViewModel];
        [array addObject:returnMoneyViewModel];
        [array addObject:returnViewModel];
        [array addObject:returnReceiveViewModel];
        
        self.viewModelArray = [array copy];
    }
    return self;
}

-(void)setReason:(NSString*)reasonId reasonText:(NSString*)reasonText{
    for (JDISVKAApplyDetailReturnReasonViewModel* reason in self.viewModelArray){
        if([reason isKindOfClass:JDISVKAApplyDetailReturnReasonViewModel.class]){
            reason.reasonId = reasonId;
            reason.reasonText = reasonText;
            break;
        }
    }
}

-(void)setRefund:(NSString*) refund{
    for (JDISVKAApplyDetailReturnMoneyViewModel* returnMoney in self.viewModelArray){
        if([returnMoney isKindOfClass:JDISVKAApplyDetailReturnMoneyViewModel.class]){
            if([refund isEqualToString:@"40"]){
                returnMoney.returnBank = NO;
            }else{
                returnMoney.returnBank = YES;
            }
            break;
        }
    }
}


-(NSString*)reasonId{
    for (JDISVKAApplyDetailReturnReasonViewModel* reason in self.viewModelArray){
        if([reason isKindOfClass:JDISVKAApplyDetailReturnReasonViewModel.class]){
            return reason.reasonId;
        }
    }
    return nil;
}

-(void)setUnselectReason:(BOOL)unselectReason {
    for (JDISVKAApplyDetailReturnReasonViewModel* reason in self.viewModelArray){
        if([reason isKindOfClass:JDISVKAApplyDetailReturnReasonViewModel.class]){
            reason.unselectReason = unselectReason;
            break;
        }
    }
}

-(void)setBankName:(NSString*)bankName{
    for (JDISVKAApplyDetailReturnMoneyViewModel* moneyModel in self.viewModelArray){
        if([moneyModel isKindOfClass:JDISVKAApplyDetailReturnMoneyViewModel.class]){
            moneyModel.bankName = bankName;
            break;
        }
    }
}
-(NSString*)bankName{
    for (JDISVKAApplyDetailReturnMoneyViewModel* moneyModel in self.viewModelArray){
        if([moneyModel isKindOfClass:JDISVKAApplyDetailReturnMoneyViewModel.class]){
            return moneyModel.bankName;
        }
    }
    return nil;
}

- (void)setSelectedReturnModel:(applyDetailGeneralTypeVo *)selectedReturnModel{
    
    self.submitModel.returnModel = @(selectedReturnModel.type);
    
//    self.submitModel.reserveDateBegin = selectedReturnModel.dateModelSelect.startTime;
//    self.submitModel.reserveDateEnd = selectedReturnModel.dateModelSelect.endTime;

    JDISVKAApplyDetailReturnViewModel *model = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnViewModel.class];
    
    model.selectedReturnModel = [applyDetailGeneralTypeVo new];
    model.selectedReturnModel.type = selectedReturnModel.type;
    model.selectedReturnModel.text = selectedReturnModel.text;
    model.selectedReturnModel.dateModelSelect = selectedReturnModel.dateModelSelect;
    
    if (model.selectedReturnModel.type == 72) {
        if([model.selectedReturnModel.dateModelSelect.dateString jdcd_validateString] && !self.orderSelf){
            model.returnTypeValue = [NSString stringWithFormat:@"%@ %@",model.selectedReturnModel.text,model.selectedReturnModel.dateModelSelect.dateString];
        }else{
            model.returnTypeValue = model.selectedReturnModel.text;
        }
        
        model.isShowAddress = true;
    }else {
        model.returnTypeValue = model.selectedReturnModel.text;
        model.isShowAddress = false;
    }
    [model updataViewModel];
    
}
- (applyDetailGeneralTypeVo *)selectedReturnModel{
    JDISVKAApplyDetailReturnViewModel *model = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnViewModel.class];
    return model.selectedReturnModel;
}


- (RACSignal *)getCurrentServerDateTime{

    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
                
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_get_cur_server_date_time" version:@"" parameters:nil complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if(!error){
                JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
                
                if (customModel.success && [customModel.data isKindOfClass:NSDictionary.class]) {
                    NSDictionary * dataDic = customModel.data;
                    NSNumber * timestamp = [dataDic jdcd_getNumberElementForKey:@"timestamp"];
                    [subscriber sendNext:timestamp];

                }else{
                    NSError* resultErr = [NSError errorWithDomain:@"KSA.err" code:-12 userInfo:@{NSLocalizedDescriptionKey:customModel.message}];
                    [subscriber sendError:resultErr];
                }
            }else{
                [subscriber sendError:error];
            }
            
        }];
        
        return nil;
    }];
    return signal;
}


- (RACSignal *)requestBankInfo{
    
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_search_bank_info" version:@"" parameters:nil complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if(!error){
                JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
                
                if (customModel.success) {
                    NSArray *arr = customModel.data;
                    if ([arr isKindOfClass:[NSArray class]] && arr.count>0) {
                        [subscriber sendNext:arr];
                        [subscriber sendCompleted];
                    }else{
                        
                        [subscriber sendError:[NSError errorWithDomain:@"KSA.err" code:-12 userInfo:@{NSLocalizedDescriptionKey:customModel.message}]];
                    }
                }else{
                    [subscriber sendError:[NSError errorWithDomain:@"KSA.err" code:-12 userInfo:@{NSLocalizedDescriptionKey:customModel.message}]];
                }
            }else{
                [subscriber sendError:error];
            }
        }];
        
        return nil;
    }];
    return signal;
}

- (RACSignal *)requestGiftInfo{
    
    NSDictionary* parameters = @{
        @"orderId":self.requestParameters[@"orderId"],
        @"skuUuid":self.requestParameters[@"skuUUid"],
        @"wareId":self.requestParameters[@"skuId"],
        @"applyingNum":@(self.applyCount),
    };

    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        [self.getGiftTask cancel];
        
        self.getGiftTask = [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_get_gift_by_main_ware_id" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//            {
                //WT_TODOH_fortest
//#ifdef DEBUG
                
//                NSString* path = [NSBundle.mainBundle pathForResource:@"return_giftInfo" ofType:@"json"];
//                NSData* data = [NSData dataWithContentsOfFile:path];
//                id obj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:nil];
//                responseObject = obj;
//#endif
//            }
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
            
            if (customModel.success) {
                NSArray *arr = customModel.data;
                if ([arr isKindOfClass:[NSArray class]] && arr.count>0) {

                    NSMutableArray * giftArrM = [NSMutableArray array];
                    for (NSDictionary * dic in arr) {
                        JDISVKAGiftModel *mainModel = [JDISVKAGiftModel yy_modelWithDictionary:dic];
                        [giftArrM addObject:mainModel];
                    }
                    //配置数据
                    JDISVKAApplyDetailCommodityViewModel *commodityViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailCommodityViewModel.class];
                    commodityViewModel.gitfArr = giftArrM;
                    [commodityViewModel updataViewModel];

                    [subscriber sendCompleted];
                }else{
                    
                    [subscriber sendError:nil];
                }
            }else{
                [subscriber sendError:nil];
            }
            
        }];
        
        return nil;
    }];
    return signal;
}

- (RACSignal *)requestData{
    JDWeakSelf
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        JDStrongSelf
        NSDictionary *parameters = self.requestParameters;
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_record_apply_query_info" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            {
                //WT_TODOH_fortest 售后申请详情
#ifdef DEBUG
//                NSString* path = [NSBundle.mainBundle pathForResource:@"return_apply" ofType:@"json"];
//                NSData* data = [NSData dataWithContentsOfFile:path];
//                id obj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:nil];
//                responseObject = obj;
#endif
            }
            
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
            
            if (customModel.success) {
                [self configViewModelWithData:customModel.data];
                JDISVKAApplyDetailMainModel *mainModel = [JDISVKAApplyDetailMainModel yy_modelWithDictionary:customModel.data];
                
                if(mainModel.orderType == 75) //删除退货楼层 判断是loc商品
                {
                    [self removeReturnModel];
                }
                
                //请求赠品数据 (买赠的主品需要调用)
                if(self.isRequestGiftInfo){
                    [[self requestGiftInfo] subscribeError:^(NSError * _Nullable error) {
                        [subscriber sendCompleted];

                    } completed:^{
                        [subscriber sendCompleted];
                    }];
                }else{
                    [subscriber sendCompleted];
                }
                                
            }else{
                [subscriber sendError:nil];
            }
            
        }];
        
        return nil;
    }];
    return signal;
}
//删除退货楼层
-(void)removeReturnModel{
    NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithArray:self.viewModelArray];
    id removeModel;
    id removeReceiveModel;
    for (id model in mutableArray){
        if([model isKindOfClass:JDISVKAApplyDetailReturnViewModel.class]){
            removeModel = model;
        }
        if([model isKindOfClass:JDISVKAApplyDetailReturnReceiveViewModel.class]){
            removeReceiveModel = model;
        }
    }
    [mutableArray removeObject:removeModel];
    [mutableArray removeObject:removeReceiveModel];
    self.viewModelArray = [mutableArray copy];
}

- (void)configViewModelWithData:(id)data{
    JDISVKAApplyDetailMainModel *mainModel = [JDISVKAApplyDetailMainModel yy_modelWithDictionary:data];
    self.orderSelf = mainModel.orderSelf;

    JDISVKAApplyDetailCommodityViewModel *commodityViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailCommodityViewModel.class];
    
    applyDetailAfsSkuDetailVo *skuDetail;
    if(mainModel.skuList.count){
        NSDictionary *dic = mainModel.skuList.firstObject;
        skuDetail = [applyDetailAfsSkuDetailVo yy_modelWithDictionary:dic];
    }
    commodityViewModel.shopName = mainModel.venderInfo.venderName;
    commodityViewModel.skuName = skuDetail.skuName;
    if(skuDetail.skuSingleShouldPrice){
        commodityViewModel.skuSingleShouldPrice = [NSString stringWithFormat:@"%@",skuDetail.skuSingleShouldPrice];
    }else{
        commodityViewModel.skuSingleShouldPrice = @"--";
    }
    commodityViewModel.skuImage = skuDetail.skuImg;
    commodityViewModel.skuCount = skuDetail.skuCount;
    commodityViewModel.skuApplyCount = skuDetail.canApplyCount;
//    commodityViewModel.skuApplyCount = 1;
    commodityViewModel.userSelectCount = 1;
    if(mainModel.orderHasGift && skuDetail.skuType != 2 && [self.pageType isEqualToString: Lang(@"after_sale_apply_return_title")]){
        self.isRequestGiftInfo = YES;
    }else{
        self.isRequestGiftInfo = NO;
    }

    @weakify(self)
    [RACObserve(commodityViewModel, userSelectCount)subscribeNext:^(NSNumber*  _Nullable x) {
        @strongify(self)
        if(x.intValue < 1){
            self.applyCount = 1;
        }else{
            self.applyCount = x.intValue;
        }
    }];
    [commodityViewModel updataViewModel];
    
    JDISVKAApplyDetailCauseViewModel *causeViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailCauseViewModel.class];
    
    RAC(self,userApplyCauseString) = RACObserve(causeViewModel, userApplyCauseString);
    
    JDISVKAApplyDetailReturnMoneyViewModel *returnMoneyViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnMoneyViewModel.class];
    //退货申请-退款方式
    if ([self.pageType isEqualToString: Lang(@"after_sale_apply_return_title")] || 30 == self.serviceType) {
        if(skuDetail.refundTypeList.count){
            NSDictionary *dic = skuDetail.refundTypeList.firstObject;
            applyDetailGeneralTypeVo *refundType = [applyDetailGeneralTypeVo yy_modelWithDictionary:dic];
            returnMoneyViewModel.type = refundType.type;
            returnMoneyViewModel.returnMoneyValue = refundType.text;
            self.refundType = refundType.type;
            self.submitModel.refundType = @(refundType.type);
            
            for(NSDictionary* item in skuDetail.refundTypeList){
                applyDetailGeneralTypeVo *refund = [applyDetailGeneralTypeVo yy_modelWithDictionary:item];
                if(refund.type == 40){
                    self.refundType = 40;
                    self.submitModel.refundType = @(40);
                    returnMoneyViewModel.needShowPointRefund = YES;
                    returnMoneyViewModel.refundPointinfo = refund.text;
                    break;
                }
            }
        }
        
        if(returnMoneyViewModel.returnMoneyValue.length ==0){//默认原返
            returnMoneyViewModel.type = 20;
            returnMoneyViewModel.returnMoneyValue = Lang(@"after_sale_refund");
            self.refundType = 20;
            self.submitModel.refundType = @(20);
        }
        
    }else{
        //换货申请
        NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithArray:self.viewModelArray];
        [mutableArray removeObject:returnMoneyViewModel];
        self.viewModelArray = [mutableArray copy];
        self.submitModel.refundType = @(30);
    }
//    RAC(self,bankName) = RACObserve(returnMoneyViewModel, bankName);//TODOH
    RAC(self,bankCode) = RACObserve(returnMoneyViewModel, bankCode);
    RAC(self,bankAccountHolder) = RACObserve(returnMoneyViewModel, bankAccountHolder);
    RAC(self,bankTel) = RACObserve(returnMoneyViewModel, bankTel);
    
    
    JDISVKAApplyDetailReturnViewModel *returnViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnViewModel.class];
    _returnViewModel = returnViewModel;
    returnViewModel.pageType = self.pageType;
    
    //获取的退货类型 40客户自寄  72上门取件
    NSMutableArray <applyDetailGeneralTypeVo *>* returnModelListArr = [NSMutableArray array];
    for (NSDictionary *dic in skuDetail.returnModelList) {
        
        applyDetailGeneralTypeVo *returnMode = [applyDetailGeneralTypeVo yy_modelWithDictionary:dic];
        [returnModelListArr addObject:returnMode];
    }
    
    if(returnModelListArr.count>0){
        //默认取第一个
        applyDetailGeneralTypeVo *returnMode = returnModelListArr.firstObject;
        returnViewModel.returnTypeValue = returnMode.text;
        returnViewModel.returnModelList = returnModelListArr;//客户自寄/上门取件

        returnViewModel.receiverAddress = mainModel.returnCustomerInfo.addressInfo.address;
        returnViewModel.receiverName = mainModel.returnCustomerInfo.contactName;
        returnViewModel.receiverPhone = mainModel.returnCustomerInfo.contactTel;
        
        self.selectedReturnModel = returnMode;

    }
    
    [returnViewModel updataViewModel];
    
    //售后类型
    NSInteger customerExpect = 10;
    if (10 == self.serviceType) {
        customerExpect = 10;
    } else if(20 == self.serviceType){
        customerExpect = 20;
    } else if(30 == self.serviceType){
        customerExpect = 30;
    }
//    [self.pageType isEqualToString: Lang(@"after_sale_apply_return_title")] ? 10 : 20;
    
    //换新 客户收货地址
    if (customerExpect == 20) {
        JDISVKAApplyDetailReturnReceiveViewModel *ReceiveViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnReceiveViewModel.class];
        _returnReceiveViewModel = ReceiveViewModel;
        ReceiveViewModel.pageType = self.pageType;
        
        //获取的退货类型 40客户自寄  72上门取件
//        NSMutableArray <applyDetailGeneralTypeVo *>* returnModelListArr = [NSMutableArray array];
//        for (NSDictionary *dic in skuDetail.returnModelList) {
//
//            applyDetailGeneralTypeVo *returnMode = [applyDetailGeneralTypeVo yy_modelWithDictionary:dic];
//            [returnModelListArr addObject:returnMode];
//        }
        
        if(mainModel.returnCustomerInfo){
            //默认取第一个
//            applyDetailGeneralTypeVo *returnMode = returnModelListArr.firstObject;
//            returnViewModel.returnTypeValue = returnMode.text;
//            returnViewModel.returnModelList = returnModelListArr;//客户自寄/上门取件
            
            ReceiveViewModel.receiverAddress = mainModel.returnCustomerInfo.addressInfo.address;
            ReceiveViewModel.receiverName = mainModel.returnCustomerInfo.contactName;
            ReceiveViewModel.receiverPhone = mainModel.returnCustomerInfo.contactTel;
        }
        
        [ReceiveViewModel updataViewModel];
    } else {
        //退货移除收货地址楼层
        NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithArray:self.viewModelArray];
        id removeReceiveModel;
        for (id model in mutableArray){
            if([model isKindOfClass:JDISVKAApplyDetailReturnReceiveViewModel.class]){
                removeReceiveModel = model;
            }
        }
        [mutableArray removeObject:removeReceiveModel];
        self.viewModelArray = [mutableArray copy];
    }
    
    self.submitModel.orderId = mainModel.orderId;
    self.submitModel.venderId = mainModel.venderInfo.venderId;
    self.submitModel.skuId = skuDetail.skuId;
    self.submitModel.customerExpect = customerExpect;

    
    //预留字段默认都为86
    //换货地址
    self.submitModel.contactTelCountry = @"+966";
    self.submitModel.contactName = mainModel.returnCustomerInfo.contactName;
    self.submitModel.contactTel = mainModel.returnCustomerInfo.contactTel;
    self.submitModel.returnAddr = mainModel.returnCustomerInfo.addressInfo.address;
    self.submitModel.returnAddrId = mainModel.returnCustomerInfo.addressInfo.addressId;
    self.submitModel.returnAddrFirstId = mainModel.returnCustomerInfo.addressInfo.provinceId;
    self.submitModel.returnAddrSecondId = mainModel.returnCustomerInfo.addressInfo.cityId;
    self.submitModel.returnAddrThirdId = mainModel.returnCustomerInfo.addressInfo.countyId;
    self.submitModel.returnAddrFourthId = mainModel.returnCustomerInfo.addressInfo.villageId;
    
    //上门取件地址
    self.submitModel.pickupContactTelCountry = @"+966";
    self.submitModel.pickupContactName = mainModel.returnCustomerInfo.contactName;
    self.submitModel.pickupContactTel = mainModel.returnCustomerInfo.contactTel;
    self.submitModel.pickwareAddr = mainModel.returnCustomerInfo.addressInfo.address;
    self.submitModel.pickupAddrId = mainModel.returnCustomerInfo.addressInfo.addressId;
    self.submitModel.pickwareAddrFirstId = mainModel.returnCustomerInfo.addressInfo.provinceId;
    self.submitModel.pickwareAddrSecondId = mainModel.returnCustomerInfo.addressInfo.cityId;
    self.submitModel.pickwareAddrThirdId = mainModel.returnCustomerInfo.addressInfo.countyId;
    self.submitModel.pickwareAddrFourthId = mainModel.returnCustomerInfo.addressInfo.villageId;
    
    
    self.addressId = @(mainModel.returnCustomerInfo.addressInfo.addressId);
    self.submitModel.reasonId = 8;
}

//更新取件地址
- (void)updateAddressCell:(id)addressModel{
    JDISVKAApplyDetailReturnViewModel *returnViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnViewModel.class];
    //跳转地址模块相关
    if ([addressModel isKindOfClass:JDISVAddress.class]) {
        JDISVAddress *address = addressModel;
        self.addressId = address.addressID;
        returnViewModel.receiverAddress = address.fullAddress;
        returnViewModel.receiverPhone = address.mobile;
        returnViewModel.receiverName = address.name;
        [returnViewModel updataViewModel];
    
//        self.submitModel.contactTelCountry = @"+966";//TODOH+966
//        self.submitModel.contactName = address.name;
//        self.submitModel.contactTel = address.mobile;
//        self.submitModel.returnAddr = address.fullAddress;
//        self.submitModel.returnAddrId = [address.addressID intValue];
//        self.submitModel.returnAddrFirstId = [address.province.addressID intValue];
//        self.submitModel.returnAddrSecondId = [address.city.addressID intValue];
//        self.submitModel.returnAddrThirdId = [address.district.addressID intValue];

        self.submitModel.pickupContactTelCountry = @"+966";//TODOH+966
        self.submitModel.pickupContactName = address.name;
        self.submitModel.pickupContactTel = address.mobile;
        self.submitModel.pickwareAddr = address.fullAddress;
        self.submitModel.pickupAddrId = [address.addressID intValue];
        self.submitModel.pickwareAddrFirstId = [address.province.addressID intValue];
        self.submitModel.pickwareAddrSecondId = [address.city.addressID intValue];
        self.submitModel.pickwareAddrThirdId = [address.district.addressID intValue];
        
        if (address.town.addressID) {
            self.submitModel.pickwareAddrFourthId = [address.town.addressID intValue];
        }
    }
    else if ([addressModel isKindOfClass:NSDictionary.class]) {
        NSDictionary *addressDic = addressModel;
        
        NSString *firtName = [addressDic objectForKey:@"firstName"];
        NSString *lastName = [addressDic objectForKey:@"lastName"];
        firtName = [NSString isEmpty:firtName] ? @"" : firtName;
        lastName = [NSString isEmpty:lastName] ? @"" : lastName;
        
        self.addressId = [addressDic objectForKey:@"addressID"];
        returnViewModel.receiverAddress = [addressDic objectForKey:@"fullAddress"];
        returnViewModel.receiverPhone = [addressDic objectForKey:@"mobile"];
        returnViewModel.receiverName = [NSString stringWithFormat:@"%@ %@", firtName, lastName];
        returnViewModel.firstName = [addressDic objectForKey:@"firstName"];
        returnViewModel.lastName = [addressDic objectForKey:@"lastName"];
        [returnViewModel updataViewModel];

        self.submitModel.pickupContactTelCountry = @"+966";//TODOH+966
        
        
        self.submitModel.pickupContactName = firtName;
        self.submitModel.pickupContactLastName = lastName;
//        self.submitModel.contactLastName = lastName;
        self.submitModel.pickupContactTel = [addressDic objectForKey:@"mobile"];
        self.submitModel.pickwareAddr = [addressDic objectForKey:@"fullAddress"];
        self.submitModel.pickupAddrId = [[addressDic objectForKey:@"addressID"] integerValue];
        self.submitModel.pickwareAddrFirstId = [[[addressDic objectForKey:@"province"] objectForKey:@"addressID"] integerValue];
        self.submitModel.pickwareAddrSecondId = [[[addressDic objectForKey:@"city"] objectForKey:@"addressID"] integerValue];
        self.submitModel.pickwareAddrThirdId = [[[addressDic objectForKey:@"district"] objectForKey:@"addressID"] integerValue];
        
        if (self.submitModel.pickwareAddrFirstId == 0) {
            self.submitModel.pickwareAddrFirstId = [[[addressDic objectForKey:@"province"] objectForKey:@"id"] integerValue];
        }
        if (self.submitModel.pickwareAddrSecondId == 0) {
            self.submitModel.pickwareAddrSecondId = [[[addressDic objectForKey:@"city"] objectForKey:@"id"] integerValue];
        }
        if (self.submitModel.pickwareAddrThirdId == 0) {
            self.submitModel.pickwareAddrThirdId = [[[addressDic objectForKey:@"district"] objectForKey:@"id"] integerValue];
        }
        
        NSInteger addressID = [[[addressDic objectForKey:@"town"] objectForKey:@"addressID"] integerValue];
        if (addressID) {
            self.submitModel.pickwareAddrFourthId = addressID;
        }
    }
}


//更新收件地址
- (void)updateReceiveAddressCell:(id)addressModel{
    JDISVKAApplyDetailReturnReceiveViewModel *returnReceiveViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailReturnReceiveViewModel.class];
    //跳转地址模块相关
    if ([addressModel isKindOfClass:JDISVAddress.class]) {
        JDISVAddress *address = addressModel;
        self.addressId = address.addressID;
        returnReceiveViewModel.receiverAddress = [address KSAShowAddressStrUseDetail:YES];
        returnReceiveViewModel.receiverPhone = address.mobile;
        returnReceiveViewModel.receiverName = address.name;
        [returnReceiveViewModel updataViewModel];
    
        self.submitModel.contactTelCountry = @"+966";//TODOH+966
        self.submitModel.contactName = address.name;
        self.submitModel.contactTel = address.mobile;
        self.submitModel.returnAddr = address.fullAddress;
        self.submitModel.returnAddrId = [address.addressID intValue];
        self.submitModel.returnAddrFirstId = [address.province.addressID intValue];
        self.submitModel.returnAddrSecondId = [address.city.addressID intValue];
        self.submitModel.returnAddrThirdId = [address.district.addressID intValue];

//        self.submitModel.pickupContactTelCountry = @"+966";//TODOH+966
//        self.submitModel.pickupContactName = address.name;
//        self.submitModel.pickupContactTel = address.mobile;
//        self.submitModel.pickwareAddr = address.fullAddress;
//        self.submitModel.pickwareAddrId = [address.addressID intValue];
//        self.submitModel.pickwareAddrFirstId = [address.province.addressID intValue];
//        self.submitModel.pickwareAddrSecondId = [address.city.addressID intValue];
//        self.submitModel.pickwareAddrThirdId = [address.district.addressID intValue];
        
        if (address.town.addressID) {
            self.submitModel.returnAddrFourthId = [address.town.addressID intValue];
            
//            self.submitModel.pickwareAddrFourthId = [address.town.addressID intValue];
        }
    
        
    }
}

//https://cf.jd.com/pages/viewpage.action?pageId=974007178
-(RACSignal*)requestReason{
    NSDictionary* param = @{@"reasonApplyType":@(1)};
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [PlatformService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_get_apply_reason_1.0" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, NSDictionary*  _Nullable responseObject, NSError * _Nullable error) {
            if(error){
                [subscriber sendError:error];
                return;
            }
            NSNumber* code = responseObject[@"code"];
            if(code.intValue !=1){
                [subscriber sendError:[NSError errorWithDomain:@"com.isv.afs" code:-1 userInfo:@{NSLocalizedDescriptionKey:responseObject[@"msg"]?:Lang(@"after_sale_request_no_network")}]];
                 return;
            }
            NSArray* reasons = responseObject[@"data"];
            NSMutableArray* tmpArr = [NSMutableArray array];
            if([reasons isKindOfClass:NSArray.class]){
                for(NSDictionary* rd in reasons){
                    JDISVKAPllyDetailReasonModel* model = [[JDISVKAPllyDetailReasonModel alloc] init];
                    model.reasonId = [rd jdcd_getStringElementForKey:@"reasonId"];
                    model.reasonText = [rd jdcd_getStringElementForKey:@"reasonName"];
                    [tmpArr addObject:model];
                }
            }
            [subscriber sendNext:[tmpArr copy]];
            [subscriber sendCompleted];
        }];
        return nil;
    }];
}

- (RACSignal *)submitAfterSaleRequest{
    self.submitModel.applyCount = self.applyCount;
    self.submitModel.questionDesc = [self.userApplyCauseString jdcd_trimWhitespace];
    self.submitModel.reasonId = [[self reasonId] intValue];
    self.submitModel.skuUUid = self.requestParameters[@"skuUUid"];
    
    // If there are after-sales pictures to transmit this parameter
    if (nil != [self causeViewModel].photos && [self causeViewModel].photos.count > 0) {
        self.submitModel.questionPic = [[self causeViewModel].photos componentsJoinedByString:@","];
    }
    
    //同adnroid
    self.submitModel.productType = 10;
    
    self.submitModel.serviceType = self.serviceType;

    if(self.submitModel.refundType.integerValue == 30){
        self.submitModel.bankName = self.bankName;
        self.submitModel.bankCode = self.bankCode;
        self.submitModel.bankAccountHolder = self.bankAccountHolder;
        self.submitModel.bankTel = self.bankTel;
    }
    
    if(self.submitModel.serviceType == 30){
        self.submitModel.extBusinessType = 1; // 1坏品售后 / Defective product after-sales service
    } else {
        self.submitModel.extBusinessType = 0;
    }
    
    JDISVAddress* address = [PlatformService getDefaultAddress];
    NSDictionary *parameters = [self.submitModel yy_modelToJSONObject];
    NSDictionary* pickupAddress = @{@"pickupLongitude":address.longitude?:@"",
                                    @"pickupLatitude":address.latitude?:@""};
    NSMutableDictionary *tmpDic = [parameters mutableCopy];
    [tmpDic addEntriesFromDictionary:pickupAddress];
    parameters = [tmpDic copy];
    
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [PlatformService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_afs_record_apply_create" version:@"" parameters:parameters complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            JDISVKAAfterSaleCustomModel *customModel = [JDISVKAAfterSaleCustomModel yy_modelWithDictionary:responseObject];
            
            if (customModel.success) {
                [subscriber sendCompleted];
            }else{
                NSInteger code = [customModel.code integerValue];
                NSString *message = customModel.message;
                NSError *buinessError = [NSError errorWithDomain:NSNetServicesErrorDomain code:code userInfo:@{
                    NSLocalizedDescriptionKey:message?:Lang(@"after_sale_commit_failed")
                }];
                [subscriber sendError:buinessError];
            }
            
        }];
        
        return nil;
    }];
    return signal;
}

- (__kindof JDISVKAApplyDetailBaseViewModel *)viewModelInDataArrayWithClass:(Class)aClass{
    for (JDISVKAApplyDetailBaseViewModel *viewModel in self.viewModelArray) {
        if ([viewModel isKindOfClass:aClass]) {
            return viewModel;
        }
    }
    return nil;
}

- (CGFloat)safeBottom{
    CGFloat safeBottom = 0;
    if (@available(iOS 11.0, *)) {
        CGFloat tempSafeBottom = [UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom;
        if (tempSafeBottom > 0) {
            safeBottom = tempSafeBottom;
        }
    }
    return safeBottom;
}

- (JDISVKAApplyDetailSubmitModel *)submitModel{
    if (!_submitModel) {
        _submitModel = [[JDISVKAApplyDetailSubmitModel alloc] init];
    }
    return _submitModel;
}

- (JDISVKAApplyDetailCauseViewModel *)causeViewModel {
    JDISVKAApplyDetailCauseViewModel *causeViewModel = [self viewModelInDataArrayWithClass:JDISVKAApplyDetailCauseViewModel.class];
    return causeViewModel;
}

@end
