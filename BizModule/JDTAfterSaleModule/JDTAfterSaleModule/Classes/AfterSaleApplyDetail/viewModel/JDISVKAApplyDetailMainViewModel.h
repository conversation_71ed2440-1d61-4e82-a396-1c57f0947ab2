//
//  JDISVKAApplyDetailMainViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import <Foundation/Foundation.h>
#import "JDISVKAApplyDetailMainModel.h"
#import "JDISVKAApplyDetailReturnViewModel.h"
#import "JDISVKAApplyDetailReturnReceiveViewModel.h"

@class JDISVKAApplyDetailSubmitModel,JDISVKAApplyDetailCauseViewModel;
@class RACSignal;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyDetailMainViewModel : NSObject

@property (nonatomic,copy)NSArray *viewModelArray;

@property (nonatomic,copy)NSString *pageType;
@property (nonatomic,assign)NSInteger serviceType;

@property (nonatomic,assign)NSInteger applyCount;

@property (nonatomic,copy)NSString *userApplyCauseString;

@property(nonatomic,assign)BOOL orderSelf;

@property(nonatomic,assign)NSInteger refundType; //20, "direct_back", 30,no_direct_back
@property (nonatomic,copy)NSString *bankName;
@property (nonatomic,copy)NSString *bankCode;
@property (nonatomic,copy)NSString *bankAccountHolder;
@property (nonatomic,copy)NSString *bankTel;

@property(nonatomic,assign)BOOL isRequestGiftInfo;

@property (nonatomic,copy)NSString *skuUUid;

@property (nonatomic,strong)NSDictionary *requestParameters;

@property (nonatomic,strong)NSNumber *addressId;

@property (nonatomic,assign)bool requestStatus;

@property (nonatomic,strong)JDISVKAApplyDetailReturnViewModel *returnViewModel;
@property (nonatomic,strong)JDISVKAApplyDetailReturnReceiveViewModel *returnReceiveViewModel;

@property (nonatomic,strong)JDISVKAApplyDetailSubmitModel *submitModel;

- (CGFloat)safeBottom;

- (RACSignal *)getCurrentServerDateTime;

- (RACSignal *)requestBankInfo;

- (RACSignal *)requestGiftInfo;

- (RACSignal *)requestData;

- (RACSignal *)submitAfterSaleRequest;

- (void)updateAddressCell:(id)addressModel;
- (void)updateReceiveAddressCell:(id)addressModel;

- (void)setReason:(NSString*)reasonId reasonText:(NSString*)reasonText;
- (void)setUnselectReason:(BOOL)unselectReason;
-(void)setRefund:(NSString*) refund;
- (void)setBankName:(NSString*)bankName;

- (NSString*)reasonId;

- (NSString*)bankName;

- (void)setSelectedReturnModel:(applyDetailGeneralTypeVo *)selectedReturnModel;

- (applyDetailGeneralTypeVo *)selectedReturnModel;

- (RACSignal*)requestReason;
- (JDISVKAApplyDetailCauseViewModel *)causeViewModel;

@end

NS_ASSUME_NONNULL_END
