//
//  JDISVKAApplyDetailReturnReceiveViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailReturnReceiveViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import "JDISVKAApplyDetailMainModel.h"
@interface JDISVKAApplyDetailReturnReceiveViewModel()

@property(nonatomic,assign)CGFloat orderInfoCellHeight;

@end

@implementation JDISVKAApplyDetailReturnReceiveViewModel

- (NSString *)identifier{
    return @"JDISVKAApplyDetailReturnReceiveCell";
}

- (CGFloat)cellHeight{
    return self.orderInfoCellHeight;
}

- (void)updataViewModel{
    
//    if (self.isShowAddress) {
        CGSize addressSize = [self.receiverAddress jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width-80-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
//    self.orderInfoCellHeight = addressSize.height+1 + 160+21 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    self.orderInfoCellHeight = addressSize.height+1 + (170-34)  + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
//    }else {
//
//        self.orderInfoCellHeight = 97 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
//    }
}

@end
