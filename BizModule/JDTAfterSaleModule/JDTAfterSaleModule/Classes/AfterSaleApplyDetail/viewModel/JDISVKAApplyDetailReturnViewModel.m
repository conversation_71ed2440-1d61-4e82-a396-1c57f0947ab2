//
//  JDISVKAApplyDetailReturnViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailReturnViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import "JDISVKAApplyDetailMainModel.h"
#import "JDISVKAApplyDetailReturntipView.h"

@interface JDISVKAApplyDetailReturnViewModel()

@property(nonatomic,assign)CGFloat orderInfoCellHeight;

@end

@implementation JDISVKAApplyDetailReturnViewModel

- (NSString *)identifier{
    return @"JDISVKAApplyDetailReturnCell";
}

- (CGFloat)cellHeight{
    JDISVKAApplyDetailReturntipView* view = [[JDISVKAApplyDetailReturntipView alloc] initWithFrame:CGRectZero];
    return self.orderInfoCellHeight + [view getHeight]+18;
}

- (void)updataViewModel{
    
    if (self.isShowAddress) {
        CGSize addressSize = [self.receiverAddress jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:(CGSizeMake(UIScreen.mainScreen.bounds.size.width-80-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], MAXFLOAT))];
        self.orderInfoCellHeight = addressSize.height+1 + 160+21 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    }else {
        
        self.orderInfoCellHeight = 97 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    }
}

@end
