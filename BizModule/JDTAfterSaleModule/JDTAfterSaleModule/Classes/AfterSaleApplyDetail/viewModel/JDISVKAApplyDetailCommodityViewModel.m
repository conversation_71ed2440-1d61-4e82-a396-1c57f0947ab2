//
//  JDISVKAApplyDetailCommodityViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailCommodityViewModel.h"
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVKAApplyDetailCommodityViewModel()

@property(nonatomic,assign)CGFloat applyDetailCommodityCellHeight;

@end

@implementation JDISVKAApplyDetailCommodityViewModel

- (NSString *)identifier{
    return @"JDISVKAApplyDetailCommodityCell";
}

- (CGFloat)cellHeight{
    return self.applyDetailCommodityCellHeight;
}

-(void)setSkuSingleShouldPrice:(NSString *)skuSingleShouldPrice{
    _skuSingleShouldPrice = skuSingleShouldPrice;
}


- (void)updataViewModel{
    if (self.gitfArr.count>0) {
        
        CGSize tipsLabSize = [Lang(@"after_sale_maizeng_text") jdcd_getStringSize:[UIFont fontWithName:@"PingFangSC-Regular" size:12] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-52, 100)];
        CGFloat tipsLabHeight = 18;
        if (tipsLabSize.height>18) {
            tipsLabHeight = 36;
        }
        self.applyDetailCommodityCellHeight = 196 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]+18+tipsLabHeight+(12+80)*self.gitfArr.count;
        
    }else{
        self.applyDetailCommodityCellHeight = 196 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    }
}

@end
