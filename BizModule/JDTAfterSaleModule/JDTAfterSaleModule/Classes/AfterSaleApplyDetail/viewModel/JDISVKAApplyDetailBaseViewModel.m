//
//  JDISVKAApplyDetailBaseViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailBaseViewModel.h"

@implementation JDISVKAApplyDetailBaseViewModel

- (NSString *)identifier{
    @throw [NSException exceptionWithName:@"ApplyDetail" reason:[NSString stringWithFormat:Lang(@"sub class %@ need implement func: %@"),NSStringFromClass(self.class),NSStringFromSelector(_cmd)] userInfo:nil];
}

- (CGFloat)cellHeight{
    @throw [NSException exceptionWithName:@"ApplyDetail" reason:[NSString stringWithFormat:Lang(@"sub class %@ need implement func: %@"),NSStringFromClass(self.class),NSStringFromSelector(_cmd)] userInfo:nil];
}


- (void)updataViewModel:(id)model{
    
}

- (void)updataViewModel{
    
}

@end
