//
//  JDISVKAApplyDetailCommodityViewModel.h
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailBaseViewModel.h"
#import "JDISVKAApplyDetailMainModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyDetailCommodityViewModel : JDISVKAApplyDetailBaseViewModel

@property (nonatomic,copy)NSString *shopName;

@property (nonatomic,copy)NSString *skuName;

@property (nonatomic,copy)NSString *skuImage;

@property (nonatomic,assign)NSInteger skuCount;

@property (nonatomic,assign)NSInteger skuApplyCount;
@property (nonatomic,assign)NSInteger userSelectCount;

//@property (nonatomic,copy)NSString *skuPrice;
@property (nonatomic,copy)NSString *skuSingleShouldPrice;


@property (nonatomic,strong) NSArray <JDISVKAGiftModel *>*gitfArr;//sku数据

@end

NS_ASSUME_NONNULL_END
