//
//  JDISVKAApplyDetailCauseViewModel.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailCauseViewModel.h"
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import "NSString+JDISVAfterExtension.h"

@implementation JDISVKAApplyDetailCauseViewModel

- (NSString *)identifier{
    return @"JDISVKAApplyDetailCauseCell";
}

- (CGFloat)cellHeight {
//    return 168 + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    
    CGFloat photoTopMargin = 10.0;
    CGFloat photoBottomMargin = 10.0;
    CGFloat photoMiddleMargin = 0.0;
    NSInteger photoRowCount = 1;
    if (0 == _photosRwoHeight){
        _photosRwoHeight = 60;
    }
    if (0 == _photosHeight){
        _photosHeight = _photosRwoHeight;
    }
    
    if (nil != _photos && 0 < _photos.count){
        NSInteger photoCount = _photos.count;
        photoRowCount = photoCount > 2 ? 2 : 1;
        photoMiddleMargin = photoRowCount == 1 ? 0.0 : 10.0;
        _photosHeight = _photosRwoHeight * photoRowCount;
    }
    CGFloat uploadTipsHeight = 0.0;
    CGFloat textW = [UIScreen mainScreen].bounds.size.width - 2 * 18;
    uploadTipsHeight = [NSString countTextCGSize:[UIFont systemFontOfSize:14] viewWidth:textW text:_uploadTips].height;
    
    CGFloat cellHeight = 168 + photoTopMargin + _photosHeight + photoMiddleMargin + photoBottomMargin + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"] + [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"] + uploadTipsHeight;
    return cellHeight;
}

-(NSInteger)maxNum {
    return 5;
}

- (RACSignal *)uploadDamagePhoto:(NSData *)photoData {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    [params setValue:@"b2ccbff.fastbe.system.public.image.upload" forKey:@"apiCode"];
    [params setValue:photoData forKey:@"photoData"];
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        [[JDISVPlatformService sharedService] jnosRequest:JDCDHTTPSessionRequestTypePost
                                                     path:@"fastbe/system/public/image/upload"
                                                  apiCode:@"b2ccbff.fastbe.system.public.image.upload"
                                                  bodyStr:params
                                                 complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
                return;
            }
            
            BOOL success = [responseObject[@"success"] boolValue];
            if (success) {
                [subscriber sendNext:responseObject];
                return;
            }
            
            [subscriber sendError:error];
        }];
        return nil;
    }];
}

- (RACSignal *)uploadDamagePhotoDataArr:(NSArray *)photoDataArray {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    [params setValue:@"b2ccbff.fastbe.system.public.image.upload" forKey:@"apiCode"];
    [params setValue:photoDataArray forKey:@"photoDataArray"];
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        [[JDISVPlatformService sharedService] jnosRequest:JDCDHTTPSessionRequestTypePost
                                                     path:@"fastbe/system/public/image/upload"
                                                  apiCode:@"b2ccbff.fastbe.system.public.image.upload"
                                                  bodyStr:params
                                                 complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
                return;
            }
            
            BOOL success = [responseObject[@"success"] boolValue];
            if (success) {
                [subscriber sendNext:responseObject];
                return;
            }
            
            [subscriber sendError:error];
        }];
        return nil;
    }];
}

@end
