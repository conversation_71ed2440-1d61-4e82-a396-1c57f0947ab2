//
//  JDISVWeekCalendarUitilit.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/6/6.
//

#import "JDISVWeekCalendarUitilit.h"

@interface JDISVWeekCalendarUitilit ()

@end

@implementation JDISVWeekCalendarUitilit
+ (instancetype)shareInstance {
    static JDISVWeekCalendarUitilit *weekCalendarUitility = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        weekCalendarUitility = [[JDISVWeekCalendarUitilit alloc] init];
        //[weekCalendarUitility getDateWeeksDuraingToday];
    });
    return weekCalendarUitility;
}

- (void)setTimestamp:(NSNumber *)timestamp{
    _timestamp = timestamp;
    [self getDateWeeksFromToday];
}

-(NSString *)transWeekName:(NSString *)orrignWeekName
{
    NSString *targetWeekName = @"";
    
    return targetWeekName;
}

-(void)getDateWeeksFromToday{
    
    //serverDate
    NSDate *serverDate = [NSDate dateWithTimeIntervalSince1970:([self.timestamp doubleValue] / 1000)];

    //日历
    NSCalendar *calendar = [NSCalendar currentCalendar];

    NSMutableArray *dateWeeks = [NSMutableArray array];
    for (int i=1; i<8; i++) {
        //这一天
        NSDateComponents *components = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay | NSCalendarUnitWeekday fromDate:[serverDate initWithTimeIntervalSinceNow:60 * 60 * 24 * i]];
        NSDate *date = [calendar dateFromComponents:components];
        
        //年月日
        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
        [dateFormatter setDateFormat:@"MMMM dd"];
        dateFormatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:[NSString getKAUseLang]];

        NSString *dateString = [dateFormatter stringFromDate:date];


        NSString * startTimeString = [NSString stringWithFormat:@"%ld-%ld-%ld 00:00:00",components.year,components.month,components.day];
        
        NSString * endTimeString = [NSString stringWithFormat:@"%ld-%ld-%ld 23:59:59",components.year,components.month,components.day];
        
        NSTimeInterval startTime = [self convertToTimeIntervalWithTimeString:startTimeString];
        NSTimeInterval endTime = [self convertToTimeIntervalWithTimeString:endTimeString];

        JDISVWeekModel * weekModel = [JDISVWeekModel new];
        weekModel.startTime = @(startTime * 1000);
        weekModel.endTime = @(endTime * 1000);
        weekModel.dateString = dateString;
        
        [dateWeeks addObject:weekModel];

    }
    
    self.weeks = dateWeeks;
}

- (NSTimeInterval)convertToTimeIntervalWithTimeString:(NSString *)timeString{
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];//不指定时区就是默认是当前时区
    NSDate *date = [dateFormatter dateFromString:timeString];
    NSTimeInterval timeInterval = [date timeIntervalSince1970];
    return timeInterval;
}
@end



@implementation JDISVWeekModel

@end
