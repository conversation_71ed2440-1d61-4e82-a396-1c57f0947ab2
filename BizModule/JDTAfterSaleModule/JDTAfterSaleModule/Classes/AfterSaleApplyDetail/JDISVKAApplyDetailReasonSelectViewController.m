//
//  JDISVKAApplyDetailReasonSelectViewController.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2022/11/10.
//

#import "JDISVKAApplyDetailReasonSelectViewController.h"

#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVKAUIKitModule/NSMutableAttributedString+KAPirce.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>

#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>

#import <JDISVKAUIKitModule/JDISVPropertyAssociateView.h>
#import <JDISVKAUIKitModule/KAEmptyView.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVKAPllyDetailReasonModel.h"
#import "JDISVKAApplDetailReasonCell.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>



@interface JDISVKAApplyDetailReasonSelectViewController ()<JDISVPropertyAssociateDelegate>
@property (nonatomic, strong) JDISVPropertyAssociateView *associateView;

@property (nonatomic, strong) UIButton *closeButton;
@property (nonatomic, strong) UIButton *sureButton;
@property (nonatomic, strong) UILabel* labelTitle;
@property (nonatomic, strong) UIFont *calFont;
@end

@implementation JDISVKAApplyDetailReasonSelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.calFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightSemibold];
   
    if(self.selectedReasonId.length){
        for(JDISVKAPllyDetailReasonModel* model in self.datas){
            if([model.reasonId isEqualToString:self.selectedReasonId]){
                model.selected = YES;
                self.sureButton.enabled = YES;
            }
        }
    }
    
    [self setUpUI];
    [self.associateView reloadData];
}

- (void)setUpUI {
    self.view.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self.associateView.collectionView registerClass:JDISVKAApplDetailReasonCell.class forCellWithReuseIdentifier:@"JDISVKAApplDetailReasonCell"];
   
    self.associateView.collectionView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
     self.associateView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    
    [self.view addSubview:self.labelTitle];
    [self.labelTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).mas_offset(18);
        make.leading.trailing.mas_equalTo(self.view);
        make.height.mas_equalTo(26);
    }];
   
    
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.width.height.mas_equalTo(18);
        make.trailing.mas_equalTo(0);
    }];
    
    [self.view addSubview:self.sureButton];
    
    [self.sureButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.height.mas_equalTo(40);
        if (@available(iOS 11.0, *)) {
            make.bottom.mas_equalTo(self.view.mas_safeAreaLayoutGuideBottom).mas_offset(-5);
        }else {
            make.bottom.mas_equalTo(self.view.mas_bottom).mas_offset(-5);
        }
    }];
    
    [self.view addSubview:self.associateView];
    [self.associateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.top.mas_equalTo(self.labelTitle.mas_bottom).mas_offset(32);
        make.bottom.mas_equalTo(self.sureButton.mas_top).mas_offset(-10);
    }];

}

#pragma mark - JDISVPropertyAssociateView
- (NSInteger)numberOfSectionsInAssociateView:(JDISVPropertyAssociateView *)associateView {
    return 1;
}

- (NSInteger)associateView:(JDISVPropertyAssociateView *)associateView numberOfItemsInSection:(NSInteger)section {
    return self.datas.count;
}

- (NSArray<NSString *> *)associateView:(JDISVPropertyAssociateView *)associateView enableKeysForItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVKAPllyDetailReasonModel* model = self.datas[indexPath.row];
    return @[model.reasonId];
}

- (CGSize)associateView:(JDISVPropertyAssociateView *)associateView sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVKAPllyDetailReasonModel* model = self.datas[indexPath.row];
    CGFloat width = [UIScreen mainScreen].bounds.size.width - 36-17-18;
    
    CGSize size = [model.reasonText jdcd_getStringSize:self.calFont constraintsSize:(CGSizeMake(width, MAXFLOAT))];
    
    CGSize resultSize = CGSizeMake([UIScreen mainScreen].bounds.size.width - 36, size.height>30?size.height+1:30);
    return resultSize;
}

- (__kindof UICollectionViewCell *)associateView:(JDISVPropertyAssociateView *)associateView cellForItemAtIndexPath:(NSIndexPath *)indexPath itemSelectStatus:(JDISVPropertyAssociateItemSelectStatus)selectStatus associateSection:(BOOL)associateSection{
    
    JDISVKAPllyDetailReasonModel* model = self.datas[indexPath.row];
    
    
    JDISVKAApplDetailReasonCell *cell = [associateView.collectionView dequeueReusableCellWithReuseIdentifier:@"JDISVKAApplDetailReasonCell" forIndexPath:indexPath];
    
    
    cell.selectedItem = model.selected;
    cell.title = model.reasonText;
    
    return cell;
}

- (void)associateView:(JDISVPropertyAssociateView *)associateView didSelectItemAtIndexPath:(NSIndexPath *)indexPath associateSection:(BOOL)associateSection allItemsSelected:(BOOL)allItemsSelected key:(nonnull NSString *)key{
    
    for(JDISVKAPllyDetailReasonModel* model in self.datas){
        if([model.reasonId isEqualToString:key]){
            model.selected = YES;
        }else{
            model.selected = NO;
        }
    }
    self.sureButton.enabled = YES;
    [self.associateView reloadData];
}

- (BOOL)associateEnableOfItemsAtSection:(NSInteger)section {
    return YES;
}


- (UIEdgeInsets)associateView:(JDISVPropertyAssociateView *)associateView insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, 0, 0, 0);
}

- (void)closeButtonClicked {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)sureButtonClicked {
    
    if(self.sureButtonCallBack){
        JDISVKAPllyDetailReasonModel* selectedModel;
        for(JDISVKAPllyDetailReasonModel* model in self.datas){
            if(model.selected){
                selectedModel = model;
                break;;
            }
        }
        if(selectedModel){
            self.sureButtonCallBack(selectedModel.reasonId, selectedModel.reasonText);
        }
    }
    [self closeButtonClicked];
    
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (JDISVPropertyAssociateView *)associateView {
    if (!_associateView) {
        _associateView = [[JDISVPropertyAssociateView alloc] initWithFrame:CGRectZero];
        _associateView.delegate = self;
    }
    return _associateView;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_sureButton setTitle:Lang(@"after_sale_reason_confirm") forState:UIControlStateNormal];
        [_sureButton addTarget:self action:@selector(sureButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        
        CGFloat r50 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R50"];
        [_sureButton renderB1WithCornerRadius:r50];
        _sureButton.enabled = NO;
    }
    return _sureButton;
}

- (UILabel*)labelTitle{
    if(!_labelTitle){
        _labelTitle = [[UILabel alloc] init];
        _labelTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _labelTitle.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T5",UIFontWeightMedium);
        if(_isRefond){
            _labelTitle.text = Lang(@"after_sale_detail_RefundChannel");
        }else{
            _labelTitle.text = Lang(@"after_sale_detail_reason");
        }
        _labelTitle.textAlignment = UITextAlignmentCenter;
    }
    return _labelTitle;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
        [_closeButton setImage:image forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        _closeButton.layer.cornerRadius = 9;
        _closeButton.layer.masksToBounds = YES;
        _closeButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _closeButton;
}
@end
