//
//  JDISVKAApplyReturnModeSelectVC.h
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/6/12.
//

#import <UIKit/UIKit.h>
#import "JDISVKAApplyDetailMainModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyReturnModeSelectVC : UIViewController

@property (nonatomic,strong)NSArray <applyDetailGeneralTypeVo *>*returnModelList;  //退货方式

@property (nonatomic, strong) applyDetailGeneralTypeVo *selectedReturnModel;

@property (nonatomic, copy) void(^sureButtonCallBack)(applyDetailGeneralTypeVo *selectedReturnModel);

@property (nonatomic, strong) NSNumber * timestamp;

@property(nonatomic,assign)BOOL orderSelf;//是否自营订单
@end


@interface JDISVKAApplDetailReturnModelCell : UICollectionViewCell
@property (nonatomic, strong) UILabel *labelTitle;
@property (nonatomic, assign) BOOL selectedItem;
@end

NS_ASSUME_NONNULL_END
