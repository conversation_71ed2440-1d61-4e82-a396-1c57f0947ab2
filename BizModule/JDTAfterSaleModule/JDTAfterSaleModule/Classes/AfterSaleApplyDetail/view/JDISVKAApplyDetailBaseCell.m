//
//  JDISVKAApplyDetailBaseCell.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailBaseCell.h"
#import "JDISVKAApplyDetailBaseViewModel.h"
#import <JDISVReactiveObjCModule/RACSubject.h>
@implementation JDISVKAApplyDetailBaseCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    
}

- (void)setupCell{
    
}

- (void)render{
    
}

- (RACSubject *)delegate {
    if (!_delegate) {
        _delegate = [RACSubject subject];
    }
    return _delegate;
}

@end
