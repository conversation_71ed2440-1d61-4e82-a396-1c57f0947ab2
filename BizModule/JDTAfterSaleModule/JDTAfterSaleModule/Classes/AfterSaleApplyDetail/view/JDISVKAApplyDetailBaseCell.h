//
//  JDISVKAApplyDetailBaseCell.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import <UIKit/UIKit.h>

@class JDISVKAApplyDetailBaseViewModel;
@class RACSubject;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVKAApplyDetailBaseCell : UITableViewCell

@property (nonatomic, weak) UITableView *tableView;
@property (nonatomic, weak) UIViewController *viewController;
@property (nonatomic, strong) RACSubject *delegate;

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel;

- (void)render;

- (void)setupCell;

@end

NS_ASSUME_NONNULL_END
