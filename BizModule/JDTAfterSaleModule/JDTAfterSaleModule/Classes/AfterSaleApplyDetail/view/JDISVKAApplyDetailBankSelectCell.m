//
//  JDISVKAApplyDetailBankSelectCell.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/6/2.
//

#import "JDISVKAApplyDetailBankSelectCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>

@interface JDISVKAApplyDetailBankSelectCell()
@property (nonatomic, strong) UIImageView *icon;
@property (nonatomic, strong) UILabel *name;

@end

@implementation JDISVKAApplyDetailBankSelectCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        _icon = [[UIImageView alloc] initWithImage:[UIImage ka_iconWithName:JDIF_ICON_SUCCESS_BIG imageSize:(CGSizeMake(20, 20)) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]]];
        _icon.hidden = YES;
        [self.contentView addSubview:_icon];
        [_icon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(20);
            make.leading.mas_equalTo(0);
            make.centerY.equalTo(self.contentView);
        }];
        
        _name = [UILabel new];
        _name.numberOfLines = 0;
        [self.contentView addSubview:_name];
        [_name mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(0);
//            make.height.mas_equalTo(20);
            make.centerY.equalTo(self.contentView);
            make.trailing.mas_equalTo(-18);
        }];
    }
    return self;
}


- (void)configureWithItemModel:(JDISVKAApplyDetailBankNameModel *)model{
    if (model.selected) {
        _name.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightMedium)];
        _icon.hidden = NO;
        [_name mas_updateConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(20+4);
        }];
    } else {
        _name.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)];
        _icon.hidden = YES;
        [_name mas_updateConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(0);
        }];
    }
    _name.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    _name.text = model.bankName;
}

@end
