//
//  JDISVKAApplyDetailReturnReasonCell.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2022/11/10.
//

#import "JDISVKAApplyDetailReturnReasonCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import "JDISVKAAfterSaleAction.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import "JDISVKAApplyDetailReturnReasonViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVKAApplyDetailReturnReasonCell()
@property (strong,nonatomic) UILabel* labelReason;
@property (strong,nonatomic) UILabel* labelText;
@property (strong,nonatomic) UIImageView* imgArr;
@property (strong,nonatomic) UIView* lineView;
@property (strong,nonatomic) UIView* reasonView;
@property (strong,nonatomic) UIGestureRecognizer* tapGes;


@end
@implementation JDISVKAApplyDetailReturnReasonCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupCell];
    }
    return self;
}

- (void)render{
  
}

- (void)config:(__kindof JDISVKAApplyDetailReturnReasonViewModel *)viewModel{
    if(viewModel.reasonText.length){
        self.labelText.text = viewModel.reasonText;
        _labelText.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium];
        _labelText.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    
    if ([@"1004" isEqualToString:viewModel.reasonId] && viewModel.unselectReason) {
        [_imgArr setHidden:YES];
        [self.contentView removeGestureRecognizer:self.tapGes];
        self.tapGes = nil;
    } else {
        [_imgArr setHidden:NO];
    }
}

-(void)tap{
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithSelectReason];
    [self.delegate sendNext:action];
}

- (void)setupCell{
    self.tapGes = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tap)];
    [self.contentView addGestureRecognizer:self.tapGes];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    
    [self.contentView addSubview:self.reasonView];
    [self.reasonView addSubview:self.labelReason];
    [self.reasonView addSubview:self.labelText];
    [self.reasonView addSubview:self.imgArr];
    [self.reasonView addSubview:self.lineView];
    
    [self.reasonView mas_makeConstraints:^(MASConstraintMaker *make){
        make.bottom.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]);
        make.top.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"]);
        make.leading.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
        make.trailing.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
    }];
    [self.labelReason mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(18);
        make.leading.mas_equalTo(18);
        make.height.mas_offset(17);
    }];
    
    [self.labelText mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.equalTo(self.labelReason.mas_bottom).offset(12);
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(self.imgArr.mas_leading).mas_offset(-12);
        make.height.mas_offset(20);
    }];
    
    [self.imgArr mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.labelText);
        make.width.height.mas_equalTo(12);
        make.trailing.mas_equalTo(self.contentView.mas_trailing).mas_offset(-18);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
        make.top.equalTo(self.labelText.mas_bottom).offset(12);
        make.width.height.mas_equalTo(0.6);
    }];
    
    self.reasonView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
}

- (UIImageView *)imgArr{
    if (!_imgArr) {
        UIImage* img = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        _imgArr = [[UIImageView alloc] initWithImage:img];
    }
    return _imgArr;
}
- (UIView *)reasonView{
    if (!_reasonView) {
        _reasonView = [[UIView alloc] init];
        _reasonView.backgroundColor =  [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    return _reasonView;
}

- (UILabel *)labelText{
    if (!_labelText) {
        _labelText = [[UILabel alloc] init];
        _labelText.text = Lang(@"after_sale_reason_select");
        _labelText.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        _labelText.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _labelText;
}

- (UILabel *)labelReason{
    if (!_labelReason) {
        _labelReason = [[UILabel alloc] init];
        _labelReason.text = Lang(@"after_sale_detail_reason");
        _labelReason.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _labelReason.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _labelReason;
}

- (UIView *)lineView{
    if(!_lineView){
        _lineView = [[UIView alloc]init];
        _lineView.backgroundColor =  [UIColor jdcd_colorWithHexColorString:@"E0E0E0"];
    }
    return _lineView;
}
@end
