//
//  JDISVKAApplyDetailCauseCell.m
//  JDISVKAAfterSaleModule
//
//  Created by huchengyang3 on 2021/10/19.
//

#import "JDISVKAApplyDetailCauseCell.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVKAApplyDetailCauseViewModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>

#import "JDISVKAAfterSaleUtils.h"

#import "JDISVKAAfterSaleAction.h"
#import <JDISVReactiveObjCModule/RACSubject.h>
#import "UIImage+Luban_AfterSales_Extension.h"

#import "UIButton+Extension.h"
#import "UIImage+JDISVAfterExtension.h"
#import "JSBlocksActionSheet.h"
#import "HXPhotoPicker.h"
#import "ZCTakePictureCol.h"

#define itemViewTag 10000

@interface JDISVKAApplyDetailCauseCell()
<UITextViewDelegate,HXAlbumListViewControllerDelegate,ZCTakePictureColDelegate>
@property (nonatomic,strong)UIView *mainView;
@property (nonatomic,strong)UIView *textBackgroudView;
@property (nonatomic,strong)UITextView *textView;
@property (nonatomic,strong)UILabel *applyCauseLabel;
@property (nonatomic,strong)UILabel *textPlaceHolderLabel;
@property (nonatomic,strong)UILabel *textStringLengthLabel;

@property (nonatomic,weak)UIView *pictureBGView;
@property (nonatomic,weak)UILabel *pictureTitleLB;
@property (nonatomic,weak)UIButton *uploadButton;
@property (nonatomic,weak)UILabel *uploadTipsLB;

@property (nonatomic,strong)JDISVKAApplyDetailCauseViewModel *viewModel;
@property (nonatomic,strong)HXPhotoManager *manager;

@end

@implementation JDISVKAApplyDetailCauseCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self setupCell];
}

- (void)config:(__kindof JDISVKAApplyDetailBaseViewModel *)viewModel{
    self.viewModel = viewModel;
    self.viewModel.uploadTips = Lang(@"after_sale_upload_image_desc_reason");
}

- (void)setupCell{
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    [self.contentView addSubview:self.mainView];
    [self.mainView addSubview:self.applyCauseLabel];
    [self.mainView addSubview:self.textBackgroudView];
    [self.textBackgroudView addSubview:self.textView];
    [self.textBackgroudView addSubview:self.textPlaceHolderLabel];
    [self.textBackgroudView addSubview:self.textStringLengthLabel];
    
    [self.mainView mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W1"]);
        make.bottom.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]);
        make.leading.mas_equalTo(self).offset([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
        make.trailing.mas_equalTo(self).offset(-[[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]);
    }];
    [self.applyCauseLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.mainView).offset(18);
        make.leading.mas_equalTo(self.mainView).offset(18);
        make.height.mas_offset(20);
    }];
    [self.textBackgroudView mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.applyCauseLabel.mas_bottom).offset(6);
        make.leading.mas_equalTo(self.mainView).offset(18);
        make.trailing.mas_equalTo(self.mainView).offset(-18);
    }];
    [self.textView mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(self.textBackgroudView).offset(12);
        make.trailing.mas_equalTo(self.textBackgroudView).offset(-12);
        make.top.mas_equalTo(self.textBackgroudView).offset(12);
        make.height.mas_offset(58);
    }];
    [self.textPlaceHolderLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.leading.mas_equalTo(self.textBackgroudView).offset(12+4);
        make.trailing.mas_equalTo(self.textBackgroudView).offset(-12);
        make.top.mas_equalTo(self.textBackgroudView).offset(14);
        make.height.mas_offset(56);
    }];
    [self.textStringLengthLabel mas_makeConstraints:^(MASConstraintMaker *make){
        make.top.mas_equalTo(self.textView.mas_bottom).offset(6);
        make.trailing.mas_equalTo(self.textView.mas_trailing);
        make.bottom.mas_equalTo(self.textBackgroudView).offset(-10);
    }];

    self.mainView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.mainView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.textBackgroudView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R80"];
    
    [self addPictureBGView];
}

- (void)addPictureBGView {
    UIView *pictureBGView = [[UIView alloc] init];
    [self.mainView addSubview:pictureBGView];
    self.pictureBGView = pictureBGView;
    [pictureBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.textBackgroudView.mas_bottom).offset(15);
        make.leading.mas_equalTo(self.textBackgroudView.mas_leading);
        make.trailing.mas_equalTo(self.textBackgroudView.mas_trailing);
    }];
    UIImage *uploadImg = JDISVKAAfterSaleModuleImageNamed(@"afterSale_detail_upload");
    UIButton *uploadButton = [UIButton creatButtonWithBackground:uploadImg
                                                           tilte:@""
                                                      titleColor:nil
                                                           frame:CGRectZero
                                                           taget:self
                                                          action:@selector(toChoicePicture)];
    uploadButton.contentMode = UIViewContentModeScaleAspectFill;
    [pictureBGView addSubview:uploadButton];
    self.uploadButton = uploadButton;
    [uploadButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(pictureBGView.mas_leading);
        make.width.mas_equalTo(60);
        make.height.mas_equalTo(60);
        make.top.mas_equalTo(pictureBGView.mas_top);
    }];
    
    self.viewModel.uploadTips = Lang(@"after_sale_upload_image_desc_reason");
    UILabel *titleLB = [[UILabel alloc] init];
    titleLB.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    titleLB.textAlignment = NSTextAlignmentLeft;
    titleLB.numberOfLines = 0;
    titleLB.backgroundColor = [UIColor clearColor];
    titleLB.font = [UIFont systemFontOfSize:14.];
    titleLB.text = self.viewModel.uploadTips;
    [self.mainView addSubview:titleLB];
    self.uploadTipsLB = titleLB;
    [titleLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(pictureBGView.mas_bottom).offset(5);
        make.leading.mas_equalTo(pictureBGView.mas_leading);
        make.trailing.mas_equalTo(pictureBGView.mas_trailing);
        make.height.mas_greaterThanOrEqualTo(20);
        make.bottom.mas_equalTo(self.mainView.mas_bottom).offset(-15);
    }];
    
//    titleLB.backgroundColor = [UIColor redColor];
//    pictureBGView.backgroundColor = [UIColor greenColor];
    
}

- (void)textViewDidChange:(UITextView *)textView{
    
    //去空格
    if (!textView.text.length) {
        [self.textPlaceHolderLabel setHidden:NO];
    }else{
        [self.textPlaceHolderLabel setHidden:YES];
    }
    
    if (textView.text.length > 500)
    {
        textView.text = [textView.text substringToIndex: 500];
    }
    
    self.viewModel.userApplyCauseString = textView.text;
//    self.viewModel.pictrueHeight = self.viewMode
    if (!textView.text.length) {
        self.textStringLengthLabel.text = @"0/500";
    }else{
        self.textStringLengthLabel.text = [NSString stringWithFormat:@"%lu/500",(unsigned long)textView.text.length];
    }
    
    if (textView.text.length == 500) {
        self.textStringLengthLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    }else{
        self.textStringLengthLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    
}

- (void)textViewDidEndEditing:(UITextView *)textView{
    JDISVKAAfterSaleAction *action = [JDISVKAAfterSaleAction actionWithType:JDISVKAAfterSaleActionTypeWithApplyCauseEditied];
    [self.delegate sendNext:action];
}

- (UIView *)mainView{
    if (!_mainView) {
        _mainView = [[UIView alloc] init];
    }
    return _mainView;
}

- (UIView *)textBackgroudView{
    if (!_textBackgroudView) {
        _textBackgroudView = [[UIView alloc] init];
        _textBackgroudView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _textBackgroudView;
}

- (UITextView *)textView{
    if (!_textView) {
        _textView = [[UITextView alloc] init];
        _textView.delegate = self;
        _textView.textAlignment = NSTextAlignmentLeft;

//        _textView.textContainerInset = UIEdgeInsetsMake(12, 12, 12, 12);
        _textView.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        _textView.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _textView.backgroundColor = [UIColor clearColor];
    }
    return _textView;
}

- (UILabel *)applyCauseLabel{
    if (!_applyCauseLabel) {
        _applyCauseLabel = [[UILabel alloc] init];
        _applyCauseLabel.text = Lang(@"after_sale_desc_reason");
        _applyCauseLabel.textAlignment = NSTextAlignmentLeft;
        _applyCauseLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        _applyCauseLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _applyCauseLabel;
}

- (UILabel *)textPlaceHolderLabel{
    if (!_textPlaceHolderLabel) {
        _textPlaceHolderLabel = [[UILabel alloc] init];
        _textPlaceHolderLabel.numberOfLines = 0;
        _textPlaceHolderLabel.text = Lang(@"after_sale_desc_reason_hint");
        _textPlaceHolderLabel.textAlignment = NSTextAlignmentLeft;
        _textPlaceHolderLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        _textPlaceHolderLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _textPlaceHolderLabel;
}

- (UILabel *)textStringLengthLabel{
    if (!_textStringLengthLabel) {
        _textStringLengthLabel = [[UILabel alloc] init];
        _textStringLengthLabel.text = @"0/500";
        _textStringLengthLabel.font = [UIFont fontWithName:@"Helvetica"size:14];
        _textStringLengthLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _textStringLengthLabel;
}

- (JDISVKAApplyDetailCauseViewModel *)viewModel{
    if (!_viewModel) {
        _viewModel = [[JDISVKAApplyDetailCauseViewModel alloc] init];
        _viewModel.photos = @[];
        _viewModel.maxNum = 5;
        _viewModel.photosRwoHeight = 60.0;
        _viewModel.photosHeight = 60.0;
    }
    return _viewModel;
}

#pragma mark - Choice Picture
- (void)toChoicePicture {
    @weakify(self);
    NSArray *menus = @[Lang(@"aftersale_return_take_photos"), Lang(@"aftersale_return_choise_photos_from_photo_liary")];
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    JSBlocksActionSheet *as = [[JSBlocksActionSheet alloc]initWithTitleArray:nil dismissedCallback:^(JSBlocksActionSheet *actionSheet, int buttonIndex) {
        @strongify(self)
        // 为空或越界就返回（有可能是点击了取消按钮）
        if (!menus.count || menus.count == buttonIndex) return;
        // 处理点击
        [self doAction:menus[buttonIndex]];
    } cancelButtonTitle:Lang(@"after_sale_cancel") destructiveButtonTitle:nil otherButtonTitles:menus];
    [as showInView:keyWindow];
    
}

/** 执行底部菜单相应操作 */
- (void)doAction:(NSString *)actionTitle {
    
    NSInteger maxNum = self.viewModel.maxNum - self.viewModel.photos.count;
    
    // 拍照 take_photos
    if ([actionTitle isEqualToString:Lang(@"aftersale_return_take_photos")]) {
        [self takePic];
    }
    // 从相册中选择 Choise photos from photo library
    else if ([actionTitle isEqualToString:Lang(@"aftersale_return_choise_photos_from_photo_liary")]) {
        HXPhotoManager *manager = [[HXPhotoManager alloc] initWithType:HXPhotoManagerSelectedTypePhoto];
        manager.configuration.openCamera = NO;
        manager.configuration.photoMaxNum = maxNum;
        manager.original = NO;
        self.manager.configuration.hideOriginalBtn = YES;
        self.manager = manager;
        
        @weakify(self);
        [self.viewModel.viewController hx_presentSelectPhotoControllerWithManager:self.manager
                                                                          didDone:^(NSArray<HXPhotoModel *> *allList, NSArray<HXPhotoModel *> *photoList, NSArray<HXPhotoModel *> *videoList, BOOL isOriginal, UIViewController *viewController, HXPhotoManager *manager) {
            
            @strongify(self);
            [photoList hx_requestImageWithOriginal:NO
                                        completion:^(NSArray<UIImage *> * _Nullable imageArray, NSArray<HXPhotoModel *> * _Nullable errorArray) {
                NSMutableArray *photos = [NSMutableArray arrayWithCapacity:0];
                for (UIImage *photo in imageArray) {
                    NSData *imageData = [UIImage lubanCompressImage:photo];
                    [photos addObject:imageData];
                }
                // 单张
//                NSData *imageData = [photos firstObject];
//                [self didFinishPickingPhotos:imageData];
                // 多张
                [self didFinishPickingPhotoArray:photos];
            }];
        } cancel:^(UIViewController *viewController, HXPhotoManager *manager) {
            NSSLog(@"block - Cancel");
        }];
    }
}

# pragma mark -- 图片选择相关 / Image selection related
- (void)takePic {
    ZCTakePictureCol *p = [[ZCTakePictureCol alloc] init];
    p.needCoremotion = YES;
    p.isOnlyOne = YES;
    /**>
     * Rear camera
     * 后置摄像头
     */
    p.position = TakePicturePositionBack;
    p.delegate = self;
    p.modalPresentationStyle = UIModalPresentationOverFullScreen;
    [self.viewModel.viewController presentViewController:p animated:YES completion:^{}];
}

/** 拍照完成 */
-(void)didFinishPickingImage:(UIImage *)image {
    if (!image) return;
    
    NSData *imageData = [UIImage lubanCompressImage:image];
    if (!imageData) return;
    
    @weakify(self);
    [PlatformService showLoadingInView:self];
    [[self.viewModel uploadDamagePhotoDataArr:@[imageData]] subscribeNext:^(id result) {
        @strongify(self);
        [PlatformService dismissInView:self];

        NSDictionary *data = [result objectForKey:@"data"];
        NSString *imagePath = [data objectForKey:@"path"];
        NSArray *imagePathArr = [imagePath componentsSeparatedByString:@","];
        [self appendPhotoArray:imagePathArr];
        
    } error:^(NSError *_Nullable error) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail
                                              message:[error localizedDescription]];
        [PlatformService dismissInView:self];
    } completed:^{
        [PlatformService dismissInView:self];
    }];
    
}

/***>
 * 拍照完成
 * Photo taken completed
 */
-(void)didFinishPickingPhotos:(NSData *)imageData {
    if (!imageData) return;
    
    @weakify(self);
    [PlatformService showLoadingInView:self];
    [[self.viewModel uploadDamagePhoto:imageData] subscribeNext:^(id result) {
        @strongify(self);
        [PlatformService dismissInView:self];

        NSDictionary *data = [result objectForKey:@"data"];
        NSString *imagePath = [data objectForKey:@"path"];
        [self appendPhotoWithPath:imagePath];
        
    } error:^(NSError *_Nullable error) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail 
                                              message:[error localizedDescription]];
        [PlatformService dismissInView:self];
    } completed:^{
        [PlatformService dismissInView:self];
    }];
}

-(void)didFinishPickingPhotoArray:(NSArray *)imageArr {
    if (!imageArr || 0 == imageArr.count) return;
    
    @weakify(self);
    [PlatformService showLoadingInView:self];
    [[self.viewModel uploadDamagePhotoDataArr:imageArr] subscribeNext:^(id result) {
        @strongify(self);
        [PlatformService dismissInView:self];

        NSDictionary *data = [result objectForKey:@"data"];
        NSString *imagePath = [data objectForKey:@"path"];
        NSArray *imagePathArr = [imagePath componentsSeparatedByString:@","];
        [self appendPhotoArray:imagePathArr];
        
    } error:^(NSError *_Nullable error) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail
                                              message:[error localizedDescription]];
        [PlatformService dismissInView:self];
    } completed:^{
        [PlatformService dismissInView:self];
    }];
}

/***>
* Cancel taking photos
* 取消拍照
*/
- (void)didCancelPickingImage {}

/***>
* append  photo
* 添加图片到UI
*/
- (void)appendPhotoWithPath:(NSString *)imagePath {
    
    NSMutableArray *currentPhotos = [NSMutableArray arrayWithCapacity:0];
    if (nil != self.viewModel.photos && 0 < self.viewModel.photos.count) {
        [currentPhotos addObjectsFromArray:self.viewModel.photos];
    }
    [currentPhotos addObject:imagePath];

    self.viewModel.photos = nil;
    self.viewModel.photos = [currentPhotos copy];

    // refresh phots UI
    [self refreshPhotoUI];
}

/***>
* append  photo
* 添加多张图片到UI
*/
- (void)appendPhotoArray:(NSArray *)imagePathArray {
    
    NSMutableArray *currentPhotos = [NSMutableArray arrayWithCapacity:0];
    if (nil != self.viewModel.photos && 0 < self.viewModel.photos.count) {
        [currentPhotos addObjectsFromArray:self.viewModel.photos];
    }
    [currentPhotos addObjectsFromArray:imagePathArray];

    self.viewModel.photos = nil;
    self.viewModel.photos = [currentPhotos copy];

    // refresh phots UI
    [self refreshPhotoUI];
}

/***>
* Delete  photos
* 删除图片
*/
- (void)deletePhoto:(UIButton *)button {
    if (nil == self.viewModel.photos || 0 == self.viewModel.photos.count) { return; }
    
    NSInteger currentIndex = button.tag;
    NSMutableArray *photos = [NSMutableArray arrayWithCapacity:0];
    [photos addObjectsFromArray:self.viewModel.photos];
    [photos removeObjectAtIndex:currentIndex];
    self.viewModel.photos = [photos copy];
    
    // refresh phots UI
    [self refreshPhotoUI];
}

// refresh phots UI
- (void)refreshPhotoUI {
    
    for (UIView *subView in self.pictureBGView.subviews) {
        if (subView == self.uploadButton) { continue; }
        
        [subView removeFromSuperview];
    }
    
    BOOL isMaxNum = self.viewModel.photos.count == self.viewModel.maxNum;
    UIImage *deleteImg = JDISVKAAfterSaleModuleImageNamed(@"after_sales_close");
    CGFloat itemDis = 10.0;
    CGFloat itemWH = _viewModel.photosRwoHeight;
    UIView *preView = nil;
    for (NSInteger index = 0; index < self.viewModel.photos.count; index++) {
        NSInteger row = index < 2 ? 1 : 2;
        NSString *photoPath = [self.viewModel.photos objectAtIndex:index];
        
        UIView *itemView = [[UIView alloc] init];
        itemView.tag = itemViewTag + index;
        itemView.clipsToBounds = YES;
        [self.pictureBGView addSubview:itemView];
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            if (nil == preView) {
                if (isMaxNum) {
                    make.leading.mas_equalTo(self.pictureBGView.mas_leading);
                } else {
                    make.leading.mas_equalTo(self.uploadButton.mas_trailing).offset(itemDis);
                }
            } else {
                if ((isMaxNum && 3 == index) || (!isMaxNum && 2 == index)) {
                    make.leading.mas_equalTo(self.pictureBGView.mas_leading);
                } else {
                    make.leading.mas_equalTo(preView.mas_trailing).offset(itemDis);
                }
            }
            if (1 == row) {
                make.top.mas_equalTo(self.pictureBGView.mas_top);
            } else {
                if ((isMaxNum && 3 == index) || (!isMaxNum && 2 == index)) {
                    make.top.mas_equalTo(preView.mas_bottom).offset(itemDis);
                } else {
                    make.top.mas_equalTo(preView.mas_top);
                }
            }
            make.width.mas_equalTo(itemWH);
            make.height.mas_equalTo(itemWH);
            if (index == self.viewModel.photos.count - 1) {
                make.bottom.mas_equalTo(self.pictureBGView.mas_bottom);
            }
        }];
        preView = itemView;
        
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView.layer.cornerRadius = 8;
        imageView.layer.masksToBounds = YES;
        imageView.userInteractionEnabled = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        [itemView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.bottom.mas_equalTo(itemView);
        }];
        NSString *photoUrl = [PlatformService getCompleteImageUrl:photoPath];
        [imageView jdcd_setImage:photoUrl contentMode:UIViewContentModeScaleAspectFill];
        
        CGFloat deleteWH = 26.0;
        UIButton *deleteButton = [UIButton createBarButtonWithImage:deleteImg target:self action:@selector(deletePhoto:)];
        deleteButton.tag = index;
        deleteButton.imageEdgeInsets = UIEdgeInsetsMake(0, deleteWH-18, deleteWH-18, 0);
        [itemView addSubview:deleteButton];
        [deleteButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(itemView.mas_top);
            make.trailing.mas_equalTo(itemView.mas_trailing);
            make.height.mas_equalTo(deleteWH);
            make.width.mas_equalTo(deleteWH);
        }];
    }
    // refresh and recount cell Height
    [self.viewModel.tableView reloadData];
}

@end
