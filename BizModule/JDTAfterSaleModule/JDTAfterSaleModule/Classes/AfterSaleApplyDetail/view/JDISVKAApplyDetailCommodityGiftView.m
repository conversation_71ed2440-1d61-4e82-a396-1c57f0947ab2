//
//  JDISVKAApplyDetailCommodityGiftView.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/5/23.
//

#import "JDISVKAApplyDetailCommodityGiftView.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

@class JDISVKAApplyDetailCommodityGiftSubView;

@interface JDISVKAApplyDetailCommodityGiftView ()

@property (nonatomic, strong) UILabel *tipsLab;


@end

@implementation JDISVKAApplyDetailCommodityGiftView

- (instancetype)init
{
    self = [super init];
    if (self) {
        
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        [self addSubview:self.tipsLab];
        
        
        CGSize tipsLabSize = [self.tipsLab.text jdcd_getStringSize:self.tipsLab.font constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width-52, 100)];
        CGFloat tipsLabHeight = 18;
        if (tipsLabSize.height>18) {
            tipsLabHeight = 36;
        }
        [self.tipsLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self).offset(18);
            make.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(tipsLabHeight);
        }];
    }
    return self;
}

- (void)setArr:(NSArray *)arr{
    for (UIView * subView in self.subviews) {
        if([subView isKindOfClass:JDISVKAApplyDetailCommodityGiftSubView.class]){
            [subView removeFromSuperview];
        }
    }
    if(arr.count>0){
        for (int i=0; i<arr.count; i++) {
            JDISVKAApplyDetailCommodityGiftSubView * giftView = [[JDISVKAApplyDetailCommodityGiftSubView alloc]init];
            giftView.giftModel = arr[i];
            [self addSubview:giftView];
            [giftView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.tipsLab.mas_bottom).offset(12+i*(12+80));
                make.leading.trailing.mas_equalTo(0);
                make.height.mas_equalTo(80);
            }];
        }
        self.hidden = NO;
    }else{
        self.hidden = YES;
    }
}

#pragma mark - getter
- (UILabel *)tipsLab {
    if (!_tipsLab) {
        _tipsLab = [[UILabel alloc] initWithFrame:CGRectZero];
        _tipsLab.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _tipsLab.textColor = [UIColor jdcd_colorWithHexColorString:@"FF9B48"];
        _tipsLab.text = Lang(@"after_sale_maizeng_text");
        _tipsLab.numberOfLines = 0;
    }
    return _tipsLab;
}


@end




@interface JDISVKAApplyDetailCommodityGiftSubView ()


@property (nonatomic, strong) UIImageView *commodityImageView;

@property (nonatomic, strong) UILabel *commodityNameLabel;

@property (nonatomic, strong) UILabel *countLabl;//数量

@property (nonatomic, strong) UILabel *countValue;

@property (nonatomic, strong) UIButton *giftBtn;//赠标识


@end

@implementation JDISVKAApplyDetailCommodityGiftSubView

-(void)setGiftModel:(JDISVKAGiftModel *)giftModel{
    [self.commodityImageView jdcd_setImage:giftModel.imageUrl placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    self.commodityNameLabel.text = giftModel.skuName;
    self.countValue.text = [NSString stringWithFormat:@"%ld",giftModel.canApplyNum];

}
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        [self addSubview:self.commodityImageView];
        [self addSubview:self.commodityNameLabel];
        [self addSubview:self.countLabl];
        [self addSubview:self.countValue];
        [self addSubview:self.giftBtn];

        [self.commodityImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
            make.leading.mas_equalTo(0);
            make.width.height.mas_equalTo(80);
        }];
        [self.commodityNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.trailing.mas_equalTo(0);
            make.leading.mas_equalTo(self.commodityImageView.mas_trailing).offset(12);
            make.height.mas_equalTo(18);
        }];
        [self.countLabl mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.commodityNameLabel.mas_bottom).offset(6);
            make.leading.mas_equalTo(self.commodityImageView.mas_trailing).offset(12);
            make.height.mas_equalTo(16);
        }];
        [self.countValue mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.commodityNameLabel.mas_bottom).offset(6);
            make.leading.mas_equalTo(self.countLabl.mas_trailing).offset(3);
            make.height.mas_equalTo(16);
        }];
        
        CGSize giftSize = [self.giftBtn renderASCouponTag:Lang(@"after_sale_maizeng_tag")];
        [self.giftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.countLabl.mas_bottom).offset(12);
            make.leading.mas_equalTo(self.commodityImageView.mas_trailing).offset(12);
            make.height.mas_equalTo(18);
            make.width.mas_equalTo(giftSize.width);
        }];
        
    }
    return self;
}

#pragma mark - getter
- (UIImageView *)commodityImageView {
    if (!_commodityImageView) {
        _commodityImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _commodityImageView.backgroundColor = [UIColor colorWithRed:250.0 green:250.0 blue:250.0 alpha:1.0];
        _commodityImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
        UIImage* placeHolder = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
        _commodityImageView.image = placeHolder;
    }
    return _commodityImageView;
}
- (UILabel *)commodityNameLabel {
    if (!_commodityNameLabel) {
        _commodityNameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _commodityNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        _commodityNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _commodityNameLabel;
}
- (UILabel *)countLabl {
    if (!_countLabl) {
        _countLabl = [[UILabel alloc] initWithFrame:CGRectZero];
        _countLabl.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _countLabl.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        _countLabl.text = Lang(@"after_sale_count_colon");
    }
    return _countLabl;
}
- (UILabel *)countValue {
    if (!_countValue) {
        _countValue = [[UILabel alloc] initWithFrame:CGRectZero];
        _countValue.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightRegular);
        _countValue.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _countValue;
}

- (UIButton *)giftBtn{
    if(!_giftBtn){
        _giftBtn = [[UIButton alloc] initWithFrame:CGRectZero];
        [_giftBtn renderASCouponTag:Lang(@"after_sale_maizeng_tag")];
    }
    return _giftBtn;
}


@end

