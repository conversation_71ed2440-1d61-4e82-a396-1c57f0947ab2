//
//  JDISVKAApplyDetailReturntipView.m
//  JDISVKAAfterSaleModule
//
//  Created by cdwutao3 on 2024/1/15.
//

#import "JDISVKAApplyDetailReturntipView.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVKAApplyDetailReturntipView(){
    
    
}
@property (strong,nonatomic) UIImageView* imgTip;
@property (strong,nonatomic) UILabel* labelTip;
@property (assign,nonatomic) CGFloat viewHeight;
@end

@implementation JDISVKAApplyDetailReturntipView
-(CGFloat)getHeight{
    return self.viewHeight;
}

-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10" alpha:0.1];
        self.layer.cornerRadius = 8;
        
        [self addSubview:self.imgTip];
        [self.imgTip mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self).mas_offset(8);
            make.leading.mas_equalTo(self).mas_offset(8);
            make.height.width.mas_equalTo(16);
        }];
        [self addSubview:self.labelTip];
        [self.labelTip mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self).mas_offset(31);
            make.trailing.mas_equalTo(self).mas_offset(-10);
            make.top.mas_equalTo(self).mas_offset(8);
        }];
    }
    return self;
}

-(UIImageView*)imgTip{
    if(!_imgTip){
        UIImage* img = [UIImage ka_iconWithName:JDIF_ICON_TIPS imageSize:CGSizeMake(16, 16) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"]];
        _imgTip = [[UIImageView alloc] initWithImage:img];
    }
    return _imgTip;
}

-(UILabel*)labelTip{
    if(!_labelTip){
        _labelTip = [[UILabel alloc]initWithFrame:CGRectZero];
        _labelTip.text = Lang(@"aftersale_returnmode_tip");
        _labelTip.font = [UIFont systemFontOfSize:12];
        _labelTip.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
        CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        CGFloat width = UIScreen.mainScreen.bounds.size.width-2*w3-36-41;
        CGSize size = [_labelTip.text jdcd_getStringSize:_labelTip.font constraintsSize:CGSizeMake(width, 100)];
        _labelTip.numberOfLines = 0;
        _viewHeight = 16 + size.height;
    }
    return _labelTip;
}

@end
