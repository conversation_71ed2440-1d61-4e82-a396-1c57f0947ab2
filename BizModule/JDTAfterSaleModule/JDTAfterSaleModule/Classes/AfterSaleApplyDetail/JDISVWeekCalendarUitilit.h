//
//  JDISVWeekCalendarUitilit.h
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/6/6.
//

#import <Foundation/Foundation.h>
@class JDISVWeekModel;


NS_ASSUME_NONNULL_BEGIN

@interface JDISVWeekCalendarUitilit : NSObject
@property (nonatomic, strong) NSNumber * timestamp;

@property (nonatomic, strong) NSArray <JDISVWeekModel *>*weeks;
+ (instancetype)shareInstance;

@end


@interface JDISVWeekModel : NSObject

@property (nonatomic,strong)NSNumber * startTime;//预约取件开始时间戳，回显主要用这个。本次需求传当天的00:00:00
@property (nonatomic,strong)NSNumber * endTime;//预约取件结束时间戳。本次需求传当天的23:59:59
@property (nonatomic,strong)NSString * dateString;//显示的日期字符串

@end

NS_ASSUME_NONNULL_END

