//
//  JDISVKAApplyReturnModeSelectVC.m
//  JDISVKAAfterSaleModule
//
//  Created by ext.huasong1 on 2023/6/12.
//

#import "JDISVKAApplyReturnModeSelectVC.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import "JDISVWeekCalendarUitilit.h"


@class JDISVKAApplDetailReturnModelCell;

@interface JDISVKAApplyReturnModeSelectVC ()<UICollectionViewDelegate,UICollectionViewDataSource>

//Customer return
@property (nonatomic, strong) UIView *customerReturnView;
@property (nonatomic, strong) UIButton *customerReturnSelectBtn;
@property (nonatomic, strong) UILabel *customerReturnLabel;

@property (nonatomic, strong) UIView *pickUpView;
@property (nonatomic, strong) UIButton *pickUpSelectBtn;
@property (nonatomic, strong) UILabel *pickUpLabel;
@property (nonatomic, strong) UILabel *pickUpSubLabel;
@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, copy) NSArray <JDISVWeekModel *>*datasource;//上门取件的时间数据

@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UIButton *sureButton;

@property (nonatomic, strong) UILabel* labelTitle;

@end

@implementation JDISVKAApplyReturnModeSelectVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setDateListData];

    [self setupUI];
   
    if(self.selectedReturnModel.type == 40){
        [self clickCustomerReturnView];
    }else if(self.selectedReturnModel.type == 72){
        [self clickPickUpView];
    }
    
    for (applyDetailGeneralTypeVo * model in self.returnModelList) {
        if(model.type == 40){
            self.customerReturnLabel.text = model.text;
            
        }else if(model.type == 72){
            self.pickUpLabel.text = model.text;
        }
    }
    //returnModelList中 包含上门取件type==72 才能进到选择页面
    if(self.returnModelList.count == 1){
        //隐藏客户自寄选项
        self.customerReturnView.hidden = YES;
        
        [self.pickUpView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.labelTitle.mas_bottom).mas_offset(32);
            make.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(23);
        }];
    }
}

- (void)setupUI{
    self.view.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    
    [self.view addSubview:self.labelTitle];
    [self.labelTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).mas_offset(18);
        make.leading.trailing.mas_equalTo(self.view);
        make.height.mas_equalTo(26);
    }];
    
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.width.height.mas_equalTo(18);
        make.trailing.mas_equalTo(0);
    }];
    
    [self.view addSubview:self.sureButton];
    [self.sureButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.height.mas_equalTo(40);
        if (@available(iOS 11.0, *)) {
            make.bottom.mas_equalTo(self.view.mas_safeAreaLayoutGuideBottom).mas_offset(-5);
        }else {
            make.bottom.mas_equalTo(self.view.mas_bottom).mas_offset(-5);
        }
    }];
    
    [self.view addSubview:self.customerReturnView];
    [self.customerReturnView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.top.mas_equalTo(self.labelTitle.mas_bottom).mas_offset(32);
        make.height.mas_equalTo(23);
    }];
    
    [self.customerReturnView addSubview:self.customerReturnSelectBtn];
    [self.customerReturnSelectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(20);
        make.centerY.mas_equalTo(self.customerReturnView);
        make.width.height.mas_equalTo(17);
    }];
    
    [self.customerReturnView addSubview:self.customerReturnLabel];
    [self.customerReturnLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.customerReturnSelectBtn.mas_trailing).offset(14);
        make.centerY.mas_equalTo(self.customerReturnView);
        make.height.mas_equalTo(23);
    }];


    [self.view addSubview:self.pickUpView];
    [self.pickUpView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.top.mas_equalTo(self.customerReturnView.mas_bottom).mas_offset(30);
        make.height.mas_equalTo(23);
    }];
    
    [self.pickUpView addSubview:self.pickUpSelectBtn];
    [self.pickUpSelectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(20);
        make.centerY.mas_equalTo(self.pickUpView);
        make.width.height.mas_equalTo(17);
    }];
    
    [self.pickUpView addSubview:self.pickUpLabel];
    [self.pickUpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.pickUpSelectBtn.mas_trailing).offset(14);
        make.centerY.mas_equalTo(self.pickUpView);
        make.height.mas_equalTo(23);
    }];
    
    [self.view addSubview:self.pickUpSubLabel];
    [self.pickUpSubLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.pickUpLabel);
        make.top.mas_equalTo(self.pickUpView.mas_bottom).mas_offset(8);
        make.height.mas_equalTo(20);
    }];
       
    [self.view addSubview:self.collectionView];
    [self.collectionView registerClass:[JDISVKAApplDetailReturnModelCell class] forCellWithReuseIdentifier:@"JDISVKAApplDetailReturnModelCell"];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.pickUpSubLabel);
        make.top.mas_equalTo(self.pickUpSubLabel.mas_bottom).mas_offset(18);
        make.height.mas_equalTo(174);
        make.width.mas_equalTo(292);
    }];
    
    
}

- (void)closeButtonClicked {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)sureButtonClicked {
    
    if(self.sureButtonCallBack){
        for(applyDetailGeneralTypeVo* model in self.returnModelList){
            if(self.customerReturnSelectBtn.jdisv_selected_B7 && model.type == 40){
                self.selectedReturnModel.type = model.type;
                self.selectedReturnModel.text = model.text;
                self.selectedReturnModel.dateModelSelect = nil;
                
            }else if(self.pickUpSelectBtn.jdisv_selected_B7 && model.type == 72){
                self.selectedReturnModel.type = model.type;
                self.selectedReturnModel.text = model.text;
            }
        }
        if(self.selectedReturnModel){
            self.sureButtonCallBack(self.selectedReturnModel);
        }
    }
    [self closeButtonClicked];
}


- (void)setDateListData{
    if(!self.selectedReturnModel){
        self.selectedReturnModel = [applyDetailGeneralTypeVo new];
    }
    
    [JDISVWeekCalendarUitilit shareInstance].timestamp = self.timestamp;
    self.datasource = [JDISVWeekCalendarUitilit shareInstance].weeks;
    
//    if(!self.selectedReturnModel.dateModelSelect){
//        if (self.datasource.count > 0){
////            self.selectedReturnModel.dateModelSelect = self.datasource[0];
//        }
//    }
}

#pragma mark -  UICollectionViewDelegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.datasource.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    JDISVKAApplDetailReturnModelCell * cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JDISVKAApplDetailReturnModelCell" forIndexPath:indexPath];
    
    JDISVWeekModel * model = self.datasource[indexPath.row];
    NSString * timeStr = model.dateString;

    cell.labelTitle.text = timeStr;
    
    if([timeStr isEqualToString:self.selectedReturnModel.dateModelSelect.dateString]){
        [cell setSelectedItem:YES];
    }else{
        [cell setSelectedItem:NO];
    }
//    if (self.selectedReturnModel.dateModelSelect.dateString) {
//        if([timeStr isEqualToString:self.selectedReturnModel.dateModelSelect.dateString]){
//            [cell setSelectedItem:YES];
//        }else{
//            [cell setSelectedItem:NO];
//        }
//    } else{//设置默认选择第一个
//        if (indexPath.row == 0){
//            [cell setSelectedItem:YES];
//        } else {
//            [cell setSelectedItem:NO];
//        }
//    }
    return cell;
    
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    self.selectedReturnModel.dateModelSelect = self.datasource[indexPath.row];
    [self.collectionView reloadData];
    self.sureButton.enabled = YES;

}
#pragma mark - TapAction
- (void)clickCustomerReturnView{
    self.customerReturnSelectBtn.jdisv_selected_B7 = YES;
    self.pickUpSelectBtn.jdisv_selected_B7 = NO;
    self.pickUpSubLabel.hidden = YES;
    self.collectionView.hidden = YES;
    self.sureButton.enabled = YES;
    
}

- (void)clickPickUpView{
    self.customerReturnSelectBtn.jdisv_selected_B7 = NO;
    self.pickUpSelectBtn.jdisv_selected_B7 = YES;
    self.pickUpSubLabel.hidden = NO;
    self.collectionView.hidden = NO;
    if(self.selectedReturnModel.dateModelSelect.dateString.length>0){
        self.sureButton.enabled = YES;
    }else{
        self.sureButton.enabled = NO;
    }
    
    if(self.orderSelf){
        //自营订单隐藏上门取件时间
        self.pickUpSubLabel.hidden = YES;
        self.collectionView.hidden = YES;
        self.sureButton.enabled = YES;
    }
}

#pragma mark - getter
- (UIView *)customerReturnView{
    if(!_customerReturnView){
        _customerReturnView = [UIView new];
        _customerReturnView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
        [_customerReturnView jd_addTapAction:@selector(clickCustomerReturnView) withTarget:self];

    }
    return _customerReturnView;
}

- (UIButton *)customerReturnSelectBtn{
    if(!_customerReturnSelectBtn){
        _customerReturnSelectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _customerReturnSelectBtn.jdisv_selected_B7 = NO;
        _customerReturnSelectBtn.userInteractionEnabled = NO;
    }
    return _customerReturnSelectBtn;
}

- (UILabel *)customerReturnLabel{
    if (!_customerReturnLabel) {
        _customerReturnLabel = [[UILabel alloc] init];
//        _customerReturnLabel.text = Lang(@"after_sale_apply_customerReturn");
        _customerReturnLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T6",UIFontWeightRegular);
        _customerReturnLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _customerReturnLabel;
}

- (UIView *)pickUpView{
    if(!_pickUpView){
        _pickUpView = [UIView new];
        _pickUpView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
        [_pickUpView jd_addTapAction:@selector(clickPickUpView) withTarget:self];

    }
    return _pickUpView;
}

- (UIButton *)pickUpSelectBtn{
    if(!_pickUpSelectBtn){
        _pickUpSelectBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _pickUpSelectBtn.jdisv_selected_B7 = NO;
        _pickUpSelectBtn.userInteractionEnabled = NO;
    }
    return _pickUpSelectBtn;
}

- (UILabel *)pickUpLabel{
    if (!_pickUpLabel) {
        _pickUpLabel = [[UILabel alloc] init];
//        _pickUpLabel.text = Lang(@"after_sale_apply_pickUp");
        _pickUpLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T6",UIFontWeightRegular);
        _pickUpLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    }
    return _pickUpLabel;
}

- (UILabel *)pickUpSubLabel{
    if (!_pickUpSubLabel) {
        _pickUpSubLabel = [[UILabel alloc] init];
        _pickUpSubLabel.text = Lang(@"after_sale_apply_pickUp_SelectTime");
        _pickUpSubLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7",UIFontWeightRegular);
        _pickUpSubLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        _pickUpSubLabel.hidden = YES;
    }
    return _pickUpSubLabel;
}

-(UICollectionView *)collectionView{
    
    if (!_collectionView) {
        
        UICollectionViewFlowLayout * layout = [[UICollectionViewFlowLayout alloc]init];
        
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        
        layout.itemSize = CGSizeMake(140, 30);
        
        layout.minimumLineSpacing = 18;
        
        layout.minimumInteritemSpacing = 12;
                        
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectZero collectionViewLayout:layout];
        
        _collectionView.delegate = self;
        
        _collectionView.dataSource = self;
        
        _collectionView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");

        _collectionView.showsVerticalScrollIndicator = NO;
        
        _collectionView.hidden = YES;
    }
    
    return _collectionView;
    
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_sureButton setTitle:Lang(@"after_sale_reason_confirm") forState:UIControlStateNormal];
        [_sureButton addTarget:self action:@selector(sureButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        
        CGFloat r50 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R50"];
        [_sureButton renderB1WithCornerRadius:r50];
        _sureButton.enabled = NO;
    }
    return _sureButton;
}

- (UILabel*)labelTitle{
    if(!_labelTitle){
        _labelTitle = [[UILabel alloc] init];
        _labelTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _labelTitle.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:(UIFontWeightMedium)];
        _labelTitle.text = Lang(@"after_sale_return_title");
        _labelTitle.textAlignment = UITextAlignmentCenter;
    }
    return _labelTitle;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
        [_closeButton setImage:image forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        _closeButton.layer.cornerRadius = 9;
        _closeButton.layer.masksToBounds = YES;
        _closeButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _closeButton;
}


@end





@implementation JDISVKAApplDetailReturnModelCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.labelTitle = [[UILabel alloc] init];
        self.labelTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.labelTitle.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightRegular)];
        self.labelTitle.textAlignment = UITextAlignmentCenter;
        self.labelTitle.userInteractionEnabled = NO;
        self.labelTitle.layer.cornerRadius = 4;
        self.labelTitle.layer.masksToBounds = YES;

        [self.contentView addSubview:self.labelTitle];
        [self.labelTitle mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(self.contentView);
        }];
       
    }
    return self;
}



- (void)setSelectedItem:(BOOL)selectedItem {
    if(selectedItem){
        self.labelTitle.layer.borderWidth = 1;
        self.labelTitle.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"].CGColor;
        self.labelTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        self.labelTitle.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");

    }else{
        self.labelTitle.layer.borderWidth = 0;
        self.labelTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        self.labelTitle.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C2");

    }
}
@end
