"after_sale_reason_confirm" = "确定";
"after_sale_detail_reason" = "申请原因";
"after_sale_commit_return_hint" = "一旦退出页面，所填写的内容将会丢失，请确认是否退出？";
"after_sale_cancel" = "取消";
"after_sale_load_error" = "页面加载失败";
"after_sale_retry" = "重试";
"after_sale_commit" = "提交";
"after_sale_count_warn_message" = "已超过可申请售后数量，请重新选择";
"after_sale_commit_reason_select_error" = "请选择申请原因";
"after_sale_commit_reason_empty_error" = "请输入问题描述";
"after_sale_commit_reason_range_error" = "问题描述至少输入10位字符";
"after_sale_commit_failed" = "售后申请失败，请稍后重试";
"after_sale_commit_success" = "售后申请成功，客服将尽快跟进并处理";
"after_sale_desc_reason" = "问题描述";
"after_sale_desc_reason_hint" = "请在此描述问题，详细地描述可帮助我们尽快处理您的申请。";
"after_sale_apply_exchange_title" = "换新申请";
"after_sale_return_title" = "退货方式";
"after_sale_send_back" = "客户寄回";
"after_sale_detail_address_receive" = "收货地址";
"after_sale_refund_title" = "退款方式";
"after_sale_refund" = "退回原账户";
"after_sale_reason_select" = "请选择   ";
"after_sale_apply_return_title" = "退货申请";
"after_sale_request_no_network" = "网络错误，请稍后再试";
"after_sale_company_dialog_hint" = "请输入快递公司";
"after_sale_no_track" = "请输入快递单号";
"after_sale_company_dialog_no_search" = "暂无搜索结果";
"after_sale_detail_title" = "售后详情";
"after_sale_detail_return_title" = "退货详情";
"after_sale_detail_exchange_title" = "换新详情";
"after_sale_detail_cancel_apply" = "取消申请";
"after_sale_track_dialog_title" = "添加快递信息";
"after_sale_track_dialog_title_sec" = "快递信息填写后无法修改,请谨慎填写";
"after_sale_add_express_delivery_failed" = "添加失败，请稍后尝试";
"after_sale_add_express_delivery_success" = "快递单号添加成功";
"after_sale_detail_cancel_dialog_title" = "是否确认取消服务单？";
"after_sale_canel_order" = "正在为您取消订单，请稍后…";
"after_sale_canel_order_failed" = "取消失败，请稍后尝试";
"after_sale_canel_order_success" = "服务单已取消";
"after_sale_copy_success" = "复制成功";
"after_sale_track_et_hint" = "未添加";
"after_sale_copy" = "复制";
"after_sale_return_shop" = "退货商家";
"after_sale_return_store" = "退货门店";
"after_sale_bt_record_exchange" = "换新";
"after_sale_return" = "退货";
"after_sale_time" = "营业时间 %@";
"after_sale_title" = "我的售后";
"after_sale_apply" = "售后申请";
"after_sale_record" = "售后记录";
"after_sale_list_apply_empty" = "暂无售后申请";
"after_sale_list_record_empty" = "暂无售后记录";
"after_sale_exchange" = "换货";
"after_sale_exchange_num" = "申请数量";
"after_sale_detail_transport" = "快递信息";
"after_sale_detail_send_id" = "换货单号";
"after_sale_price" = "单价";
"after_sale_detail_refund_money" = "退款金额";
"after_sale_detail_contact_service" = "联系客服";
"after_sale_detail_type" = "售后类型";
"after_sale_detail_service_id" = "服务单号";
"after_sale_count" = "数量";
"after_sale_detail_time" = "申请时间";
"after_sale_apply_id" = "订单编号";




"after_sale_maizeng_text" = "赠品需将与主品一起退回，否则商家将会扣除赠品价值退款";
"after_sale_maizeng_specification" = "规格：";
"after_sale_count_colon" = "数量：";
"after_sale_maizeng_tag" = "赠";
"after_sale_apply_bankName" = "银行名称";
"after_sale_apply_input_bankName" = "请选择开户银行";
"after_sale_apply_bankCard" = "银行卡号";
"after_sale_apply_input_bankCard" = "请输入银行卡号";
"after_sale_apply_bankCard_illegal_length" = "银行卡号仅允许输入22位数字";
"after_sale_apply_bankCard_invalid" = "The IBAN number you entered is incorrect.";
"after_sale_apply_bankAccountName" = "银行开户姓名";
"after_sale_apply_input_bankAccountName" = "请输入银行开户姓名";
"after_sale_apply_bankAccountName_illegal" = "银行开户姓名最多允许输入50位字符";
"after_sale_apply_phoneNumber" = "手机号码";
"after_sale_apply_input_phoneNumber" = "请输入手机号码";
"after_sale_apply_phoneNumber_illegal" = "手机号码仅允许输入9位数字";
"after_sale_apply_warning" = "请您提交前仔细核对银行信息，退款提交申请成功后，款项将退回该银行卡内。若信息有误，您的退款将可能失败或转入错误账号，若转入错误账户，退款将无法追回";
"after_sale_list_no_exchange" = "该商品已错过规定的7天换货时效";
"after_sale_list_no_return" = "该商品已错过规定的7天退货时效";
"after_sale_apply_customerReturn" = "客户自寄";
"after_sale_apply_pickUp" = "上门取件";
"after_sale_apply_pickUp_SelectTime" = "请选择上门取件时间";
"after_sale_apply_pickUp_status" = "取件状态";
"after_sale_apply_pickUp_info" = "取件信息";
"after_sale_apply_pickUp_time" = "取件时间";
"after_sale_apply_select_returnMethod" = "请选择退货方式";
"after_sale_apply_pickUp_logistics_name" = "承运商";
"after_sale_apply_pickUp_logistics_number" = "运单号";
"after_sale_apply_pickUp_logistics_count" = "%ld个";
"after_sale_apply_pickUp_logistics_tip" = "快递员分 %ld 次取件，请按指定件数寄回";
"after_sale_detail_return_address_receive" = "收货信息";
"after_sale_apply_return_logistics_name" = "换货承运商";

"aftersale_return_choise_photos_from_photo_liary" = "从照片库中选择照片";
"aftersale_return_take_photos" = "拍照";
"afterre_return_alert_message_maxnum" = "已达到图像的最大数量";
"aftersale_return_alert_title" = "提示";
"aftersale_return_alert_ok" = "知道了";
"after_sale_apply_upload_photo_tips" = "请上传损坏产品的图片";
"after_sale_upload_image_desc_reason" = "最多可以上传5张图片。每张图片最多5M，支持jpg、jpeg、png格式";
"after_sale_desc_proproblem_photos" = "问题图片";
"aftersale_return_button_settings" = "设置";
"aftersale_return_button_album" = "相册";
"aftersale_return_camera_alert_title" = "您尚未授权";
"aftersale_return_camera_alert_message" = "您没有摄像头\n请在设备的“设置-隐私-摄像头”中允许访问摄像头";
"aftersale_return_title_recents" = "最近";
"aftersale_return_title_photos" = "照片";
"aftersale_return_button_done" = "完成";
"aftersale_return_toast_missing_delivery_address" = "缺少收货地址，请添加";
"aftersale_return_tips_only_part_photos" = "仅可访问部分照片，建议开启「所有照片」";
"aftersale_return_tips_to_settings" = "去设置";
"aftersale_return_button_retake" = "重拍";
"aftersale_return_take_picture_tips" = "如果你发现你横拍的照片却竖着显示时，请在控制中心锁定屏幕方向";
"aftersale_return_arlert_message_tips" = "我知道了";

"aftersale_return_tips_message_open_camera_auth" = "请打开相机权限";
"aftersale_return_tips_message_please_modify_in_settings" = "请在手机系统设置中更改";
"aftersale_return_button_open" = "开启";
"aftersale_return_button_close" = "关闭";
"aftersale_return_button_take_single" = "单张拍摄";
"aftersale_return_button_sharpening_enabled" = "锐化已开启";
"aftersale_return_button_sharpening_has_been_turned_off" = "锐化已关闭";
"aftersale_return_button_continuous_take" = "连拍";

"aftersale_return_unable_access_photos" = "无法访问照片\n请点击这里前往设置中允许访问照片";
"aftersale_return_unable_open_photos" = "无法访问照片";
"aftersale_return_more" = "更多";

