"after_sale_exchange_num" = "Applied Quantity";
"after_sale_commit_reason_empty_error" = "Please enter problem description";
"after_sale_list_no_exchange" = "The 7-day exchange period of the product has expired";
"after_sale_detail_transport" = "Express delivery information";
"after_sale_detail_return_title" = "Return details";
"after_sale_detail_send_id" = "Exchange Order #";
"after_sale_price" = "Unit Price";
"after_sale_copy_success" = "Copy succeeded";
"after_sale_request_error" = "The system is busy. Please try again later";
"after_sale_detail_refund_money" = "Refund Amount";
"after_sale_detail_contact_service" = "Contact customer service";
"after_sale_reason_confirm" = "OK";
"after_sale_return_title" = "Return Method";
"after_sale_list_apply_empty" = "No Return application available";
"after_sale_company_dialog_hint" = "Please enter express company";
"after_sale_load_error" = "Page loading failed";
"after_sale_track_dialog_title" = "Add express delivery information";
"after_sale_track_dialog_title_sec" = "Please fill in the express delivery information with care as it cannot be modified";
"after_sale_detail_type" = "Application Type";
"after_sale_detail_cancel_apply" = "Cancel Application";
"after_sale_detail_cancel_dialog_title" = "Are you sure to cancel service order?";
"after_sale_no_track" = "Please enter tracking number";
"after_sale_track_et_hint" = "Not added";
"after_sale_return_store" = "Physical store for return";
"after_sale_refund" = "Back to original account";
"after_sale_time" = "Business hours %@";
"after_sale_detail_address_receive" = "Delivery address";
"after_sale_apply_return_title" = "Return Application";
"after_sale_exchange" = "Replace";
"after_sale_commit_return_hint" = "Once you exit from the page, the edited content will be lost. Are you sure to exit?";
"after_sale_count_warn_message" = "Exceeded the available return quantity, please select it again";
"after_sale_detail_service_id" = "Service Order #";
"after_sale_count" = "Quantity";
"after_sale_detail_title" = "Aftersales details";
"after_sale_apply_exchange_title" = "Old-for-new application";
"after_sale_commit_reason_select_error" = "Please select reasons for application";
"after_sale_copy" = "Copy";
"after_sale_company_dialog_no_search" = "No search results";
"after_sale_request_no_network" = "Network error, please try again later";
"after_sale_commit_failed" = "Aftersales application failed, please try again later";
"after_sale_record" = "Return Records";
"after_sale_cancel" = "Cancel";
"after_sale_detail_time" = "Application Time";
"after_sale_commit_success" = "Aftersales application succeeded. It will be followed up and processed by Customer Service as soon as possible";
"after_sale_detail_reason" = "Application Reason";
"after_sale_detail_RefundChannel" = "Refund Channel";
"after_sale_save_track_error" = "Saving failed, please try again later";
"after_sale_detail_track_success" = "Your tracking number has been submitted";
"after_sale_return_shop" = "Merchant for return";
"after_sale_list_record_empty" = "No return records available";
"after_sale_detail_exchange_title" = "Return Details";
"after_sale_refund_title" = "Refund Channel";
"after_sale_desc_reason" = "Problem Description";
"after_sale_title" = "Return";
"after_sale_commit" = "Submit";
"after_sale_desc_reason_hint" = "Please give a detailed description of the problem here, so that we can process your application as soon as possible.";
"after_sale_apply_id" = "Order #";
"after_sale_return" = "Return";
"after_sale_reason_select" = "Please select";
"after_sale_bt_record_exchange" = "Replace";
"after_sale_commit_reason_range_error" = "At least 10 characters to be entered for problem description";
"after_sale_apply" = "Return Application";
"after_sale_retry" = "Retry ";
"after_sale_add_express_delivery_failed" = "Addition failed, please try again later";
"after_sale_add_express_delivery_success" = "Tracking number added successfully";
"after_sale_canel_order" = "The order is being cancelled for you, please wait…";
"after_sale_canel_order_failed" = "Cancellation failed, please try again later";
"after_sale_canel_order_success" = "The service order has been cancelled";
"after_sale_maizeng_text" = "Gifts should be returned together with the main product, otherwise the seller will deduct the gift value for a refund";
"after_sale_maizeng_specification" = "Specification:";
"after_sale_count_colon" = "Qty:";
"after_sale_maizeng_tag" = "Gift";
"after_sale_apply_bankName" = "Bank Name";
"after_sale_apply_input_bankName" = "Please select the bank of deposit";
"after_sale_apply_bankCard" = "IBAN No.";
"after_sale_apply_input_bankCard" = "Please enter the bank card number";
"after_sale_apply_bankCard_illegal_length" = "Only 22-digit numbers can be entered for a bank card number";
"after_sale_apply_bankCard_invalid" = "The IBAN number you entered is incorrect.";
"after_sale_apply_bankAccountName" = "Account Name";
"after_sale_apply_input_bankAccountName" = "Please enter bank account name";
"after_sale_apply_bankAccountName_illegal" = "A maximum of 50 characters can be entered for a bank account name";
"after_sale_apply_phoneNumber" = "Mobile Phone Number";
"after_sale_apply_input_phoneNumber" = "Please enter your mobile phone number";
"after_sale_apply_phoneNumber_illegal" = "Only 9-digit number can be entered for mobile phone number";
"after_sale_apply_phoneNumber_illegal_10" = "Only 10-digit number can be entered for mobile phone number";
"after_sale_apply_warning" = "Please check your bank information carefully before submitting the application. The money will be refunded to the bank card after the application is successfully submitted. If the information is incorrect, your refund may fail or be transferred to a wrong account. If the refund is transferred to a wrong account, it will not be recoverable";

"after_sale_apply_point_warning" = "Your refund will be converted into points, 1SAR=100 Points";
"after_sale_apply_point_refund" = "Point Refund";
"after_sale_apply_bank_refund" = "Refund to bank card";
"after_sale_list_no_exchange" = "The 7-day exchange period of the product has expired";
"after_sale_list_no_return" = "The 7-day return period of the product has expired";
"after_sale_apply_customerReturn" = "Mailed by customer";
"after_sale_apply_pickUp" = "Door-to-door pickup";
"after_sale_apply_pickUp_SelectTime" = "Please select the time for door-to-door pickup";
"after_sale_apply_pickUp_status" = "Pickup Status";
"after_sale_apply_pickUp_info" = "Pickup Information";
"after_sale_apply_pickUp_time" = "Pickup Time";
"after_sale_apply_select_returnMethod" = "Please select the return method";
"after_sale_apply_pickUp_logistics_name" = "Carrier";
"after_sale_apply_pickUp_logistics_number" = "Tracking #";
"after_sale_apply_pickUp_logistics_count" = "%ld package(s)";
"after_sale_apply_pickUp_logistics_tip" = "The courier will pick up the products %ld times. Please send back the packages as the number specified";
"after_sale_detail_return_address_receive" = "Receiving Information";
"after_sale_apply_return_logistics_name" = "Exchange Carrier";

"aftersale_input_phone" = "Please enter the correct phone number!";
"aftersale_input_phone_9digit" = "Only 9 digits allowed for input!";
"aftersale_input_phone_10digit" = "Only 10 digits allowed for input!";

"aftersale_returnmode_tip" = "After the application is submitted, the Courier will pick up the package at door as soon as possible. Please prepare your products in advance";
"aftersale_logistics_track" = "Logistics tracking";


"aftersale_return_choise_photos_from_photo_liary" = "Choose from photo library";
"aftersale_return_take_photos" = "Take photos";
"aftersale_return_alert_message_maxnum" = "The maximum number of images has been reached";
"aftersale_return_alert_title" = "Tips";
"aftersale_return_alert_okay" = "Got it";
"after_sale_apply_upload_photo_tips" = "Please upload pictures of damaged products";
"after_sale_bt_damage_product_service" = "Return of Damaged Product";
"after_sale_upload_image_desc_reason" = "Up to 5 pictures can be uploaded. Each picture is at most 5M and supports jpg,jpeg,png";
"after_sale_desc_problem_photos" = "Problem Pictures";
"aftersale_return_button_settings" = "Settings";
"aftersale_return_button_album" = "Album";
"aftersale_return_action_title_choise_photo_library" = "Select Image";
"aftersale_return_camera_alert_title" = "You have not authorized";
"aftersale_return_camera_alert_message" = "You don't have a camera \n Please allow access to the camera in the device's \" Settings - Privacy - Camera";
"aftersale_return_title_recents" = "Recents";
"aftersale_return_title_photos" = "Photos";
"aftersale_return_button_done" = "Done";
"aftersale_return_toast_missing_delivery_address" = "Missing shipping address, please add";
"aftersale_return_tips_only_part_photos" = "Only partial photos can be accessed, it is recommended to enable 'All Photos'";
"aftersale_return_tips_to_settings" = "Go set it up";
"aftersale_return_button_retake" = "Retake";
"aftersale_return_take_picture_tips" = "If you find that the photo you took horizontally is displayed vertically, please lock the screen orientation in the control center";
"aftersale_return_arlert_message_tips" = "I got it!";

"aftersale_return_tips_message_open_camera_auth" = "Please open camera permissions";
"aftersale_return_tips_message_please_modify_in_settings" = "Please change in the mobile system settings";
"aftersale_return_button_open" = "open";
"aftersale_return_button_close" = "close";
"aftersale_return_button_take_single" = "single shoot";
"aftersale_return_button_sharpening_enabled" = "Sharpening enabled";
"aftersale_return_button_sharpening_has_been_turned_off" = "Sharpening has been turned off";
"aftersale_return_button_continuous_take" = "continuous take";

"aftersale_return_unable_access_photos" = "Unable to access photos.\nPlease click here to go to settings and allow access to photos.";
"aftersale_return_unable_open_photos" = "Unable to access all photos";
"aftersale_return_more" = "More";
