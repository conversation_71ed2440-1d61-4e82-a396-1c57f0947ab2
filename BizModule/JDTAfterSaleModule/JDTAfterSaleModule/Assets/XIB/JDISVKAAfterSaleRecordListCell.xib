<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="292" id="KGk-i7-Jjw" customClass="JDISVKAAfterSaleRecordListCell">
            <rect key="frame" x="0.0" y="0.0" width="445" height="292"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="445" height="292"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ctc-19-v7n">
                        <rect key="frame" x="0.0" y="0.0" width="445" height="280"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ewT-SA-Z1o">
                                <rect key="frame" x="18" y="148" width="409" height="45"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8hP-Ow-e3T">
                                        <rect key="frame" x="12" y="12" width="351" height="21"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="jfr-89-m8D">
                                        <rect key="frame" x="381" y="17.5" width="10" height="10"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="10" id="8Le-Qx-dzh"/>
                                            <constraint firstAttribute="width" constant="10" id="n3v-Bg-4zY"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemTealColor"/>
                                <constraints>
                                    <constraint firstItem="8hP-Ow-e3T" firstAttribute="top" secondItem="ewT-SA-Z1o" secondAttribute="top" constant="12" id="5ah-i3-ibW"/>
                                    <constraint firstItem="jfr-89-m8D" firstAttribute="centerY" secondItem="ewT-SA-Z1o" secondAttribute="centerY" id="LBk-By-k9l"/>
                                    <constraint firstItem="jfr-89-m8D" firstAttribute="leading" secondItem="8hP-Ow-e3T" secondAttribute="trailing" constant="18" id="Lh5-KO-lIA"/>
                                    <constraint firstAttribute="bottom" secondItem="8hP-Ow-e3T" secondAttribute="bottom" constant="12" id="Oqg-MW-6nO"/>
                                    <constraint firstAttribute="trailing" secondItem="jfr-89-m8D" secondAttribute="trailing" constant="18" id="gWd-aD-UzO"/>
                                    <constraint firstItem="8hP-Ow-e3T" firstAttribute="leading" secondItem="ewT-SA-Z1o" secondAttribute="leading" constant="12" id="upr-IN-3dX"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="服务单号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SSk-aX-84T">
                                <rect key="frame" x="18" y="18" width="69.5" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="3x5-Yv-Lt7"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7yv-2l-MBF">
                                <rect key="frame" x="93.5" y="17.5" width="264" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KKw-4T-LzP">
                                <rect key="frame" x="110" y="56" width="317" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="36y-nL-Azm"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="g2p-r4-USr">
                                <rect key="frame" x="391.5" y="18" width="35.5" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="数量" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FXg-fe-qhY">
                                <rect key="frame" x="110" y="108" width="35" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="pNb-ic-03E">
                                <rect key="frame" x="18" y="56" width="80" height="80"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="80" id="CdE-v6-tCO"/>
                                    <constraint firstAttribute="height" constant="80" id="XGK-l0-h7o"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="g9l-tZ-RG6">
                                <rect key="frame" x="363.5" y="14.5" width="24" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="24" id="38l-Yg-cur"/>
                                    <constraint firstAttribute="height" constant="24" id="aia-4L-r8S"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pqc-FX-ike">
                                <rect key="frame" x="151" y="108" width="42" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Pqc-FX-ike" firstAttribute="leading" secondItem="FXg-fe-qhY" secondAttribute="trailing" constant="6" id="2YM-dg-pbl"/>
                            <constraint firstItem="g2p-r4-USr" firstAttribute="leading" secondItem="g9l-tZ-RG6" secondAttribute="trailing" constant="4" id="6iV-aY-AWM"/>
                            <constraint firstItem="g2p-r4-USr" firstAttribute="top" secondItem="ctc-19-v7n" secondAttribute="top" constant="18" id="A9k-uT-Ksp"/>
                            <constraint firstAttribute="trailing" secondItem="KKw-4T-LzP" secondAttribute="trailing" constant="18" id="BJr-ds-Lvl"/>
                            <constraint firstItem="ewT-SA-Z1o" firstAttribute="leading" secondItem="ctc-19-v7n" secondAttribute="leading" constant="18" id="IDb-mU-yKB"/>
                            <constraint firstItem="KKw-4T-LzP" firstAttribute="top" secondItem="pNb-ic-03E" secondAttribute="top" id="KCx-EQ-5FN"/>
                            <constraint firstItem="pNb-ic-03E" firstAttribute="top" secondItem="SSk-aX-84T" secondAttribute="bottom" constant="18" id="MaW-Pc-RxP"/>
                            <constraint firstItem="Pqc-FX-ike" firstAttribute="centerY" secondItem="FXg-fe-qhY" secondAttribute="centerY" id="Ptm-DU-7eP"/>
                            <constraint firstItem="KKw-4T-LzP" firstAttribute="leading" secondItem="pNb-ic-03E" secondAttribute="trailing" constant="12" id="TrS-g3-fWA"/>
                            <constraint firstAttribute="trailing" secondItem="g2p-r4-USr" secondAttribute="trailing" constant="18" id="ULN-KC-pLy"/>
                            <constraint firstItem="7yv-2l-MBF" firstAttribute="leading" secondItem="SSk-aX-84T" secondAttribute="trailing" constant="6" id="UVK-Uf-s36"/>
                            <constraint firstItem="g9l-tZ-RG6" firstAttribute="leading" secondItem="7yv-2l-MBF" secondAttribute="trailing" constant="6" id="V2U-ln-K1b"/>
                            <constraint firstItem="FXg-fe-qhY" firstAttribute="top" secondItem="KKw-4T-LzP" secondAttribute="bottom" constant="12" id="WKO-dQ-IRy"/>
                            <constraint firstItem="g9l-tZ-RG6" firstAttribute="centerY" secondItem="g2p-r4-USr" secondAttribute="centerY" id="Yt9-R3-e5M"/>
                            <constraint firstItem="pNb-ic-03E" firstAttribute="leading" secondItem="SSk-aX-84T" secondAttribute="leading" id="be9-d4-M9G"/>
                            <constraint firstItem="7yv-2l-MBF" firstAttribute="centerY" secondItem="SSk-aX-84T" secondAttribute="centerY" id="ee3-lG-Ilf"/>
                            <constraint firstItem="SSk-aX-84T" firstAttribute="leading" secondItem="ctc-19-v7n" secondAttribute="leading" constant="18" id="lQ5-Uh-Ejd"/>
                            <constraint firstItem="SSk-aX-84T" firstAttribute="top" secondItem="ctc-19-v7n" secondAttribute="top" constant="18" id="maB-Om-03J"/>
                            <constraint firstItem="ewT-SA-Z1o" firstAttribute="top" secondItem="pNb-ic-03E" secondAttribute="bottom" constant="12" id="nXZ-Ku-xYd"/>
                            <constraint firstItem="FXg-fe-qhY" firstAttribute="leading" secondItem="KKw-4T-LzP" secondAttribute="leading" id="p3f-Pd-S05"/>
                            <constraint firstAttribute="trailing" secondItem="ewT-SA-Z1o" secondAttribute="trailing" constant="18" id="zky-7n-6U1"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="ctc-19-v7n" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="9Vl-kJ-Aqa"/>
                    <constraint firstItem="ctc-19-v7n" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Zcm-UC-gH9"/>
                    <constraint firstAttribute="bottom" secondItem="ctc-19-v7n" secondAttribute="bottom" constant="12" id="sJH-UL-UZk"/>
                    <constraint firstAttribute="trailing" secondItem="ctc-19-v7n" secondAttribute="trailing" id="seG-Mt-cYH"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="applyDetailLabel" destination="8hP-Ow-e3T" id="ypx-ov-cqk"/>
                <outlet property="applyDetailView" destination="ewT-SA-Z1o" id="6uN-hG-4B6"/>
                <outlet property="commodityCountLabel" destination="FXg-fe-qhY" id="4oX-fA-alA"/>
                <outlet property="commodityCountValue" destination="Pqc-FX-ike" id="7NO-qk-gqI"/>
                <outlet property="commodityImageView" destination="pNb-ic-03E" id="Di7-ez-DM9"/>
                <outlet property="commodityName" destination="KKw-4T-LzP" id="Trx-OU-v9u"/>
                <outlet property="mainView" destination="ctc-19-v7n" id="otb-Rl-ks8"/>
                <outlet property="mainViewBottomConstraint" destination="sJH-UL-UZk" id="gig-Ra-NMi"/>
                <outlet property="mainViewLeftConstraint" destination="9Vl-kJ-Aqa" id="Que-Kg-L6a"/>
                <outlet property="mainViewRightConstraint" destination="seG-Mt-cYH" id="4S7-BP-oNq"/>
                <outlet property="mainViewTopConstraint" destination="Zcm-UC-gH9" id="ikO-GU-Xe4"/>
                <outlet property="returnTypeImageView" destination="g9l-tZ-RG6" id="3PU-Aj-jB3"/>
                <outlet property="returnTypeLabel" destination="g2p-r4-USr" id="GTc-uo-WcC"/>
                <outlet property="rightArrowImageView" destination="jfr-89-m8D" id="pLr-6F-yi4"/>
                <outlet property="serverLabel" destination="SSk-aX-84T" id="Twm-WJ-30y"/>
                <outlet property="serverValue" destination="7yv-2l-MBF" id="BG9-cQ-BPG"/>
            </connections>
            <point key="canvasLocation" x="394.92753623188406" y="141.96428571428569"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemTealColor">
            <color red="0.18823529411764706" green="0.69019607843137254" blue="0.7803921568627451" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
