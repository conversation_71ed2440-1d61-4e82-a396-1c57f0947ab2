<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="237" id="KGk-i7-Jjw" customClass="JDISVKAAfterSaleApplyListCell">
            <rect key="frame" x="0.0" y="0.0" width="402" height="237"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="402" height="237"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hAY-fR-Nsq">
                        <rect key="frame" x="0.0" y="0.0" width="402" height="225"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="k9d-ez-uNV" userLabel="RetrunDamageButton">
                                <rect key="frame" x="288" y="177" width="0.0" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="hIY-rV-3kd"/>
                                    <constraint firstAttribute="width" id="vZg-5l-eRx"/>
                                </constraints>
                                <state key="normal" title="退货"/>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="订单编号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Znl-Zm-Cvk">
                                <rect key="frame" x="18" y="18" width="57.5" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="N6J-Pr-MXK"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SfH-la-A99">
                                <rect key="frame" x="81.5" y="17.5" width="302.5" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pxs-pn-Uf4">
                                <rect key="frame" x="106" y="56" width="278" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="Lyy-4q-ta3"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="数量" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mm1-3q-nx5">
                                <rect key="frame" x="106" y="108" width="35" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="a3G-GC-g0y">
                                <rect key="frame" x="18" y="56" width="80" height="80"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="80" id="UyJ-4B-3pP"/>
                                    <constraint firstAttribute="height" constant="80" id="VvA-G8-mxP"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RYw-qU-JNx">
                                <rect key="frame" x="147" y="108" width="42" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4PM-tI-dja">
                                <rect key="frame" x="288" y="177" width="0.0" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="Lat-1J-ZCV"/>
                                </constraints>
                                <state key="normal" title="换新"/>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g26-DK-wB4">
                                <rect key="frame" x="300" y="177" width="84" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="Ht3-fB-ArZ"/>
                                    <constraint firstAttribute="width" constant="84" id="QKg-E5-Alf"/>
                                </constraints>
                                <state key="normal" title="退货"/>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8v2-HX-USf">
                                <rect key="frame" x="18" y="148" width="366" height="65"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="dsfdskofkodskofkodskofdskofkdskofkodskofkodskof" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l9h-fe-GaG">
                                        <rect key="frame" x="12" y="12" width="314" height="41"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="mr2-Vk-IaP">
                                        <rect key="frame" x="338" y="27.5" width="10" height="10"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="10" id="8au-9K-QR0"/>
                                            <constraint firstAttribute="width" constant="10" id="n8B-Ng-wwg"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemTealColor"/>
                                <constraints>
                                    <constraint firstItem="l9h-fe-GaG" firstAttribute="centerY" secondItem="8v2-HX-USf" secondAttribute="centerY" id="1Nu-Wk-JQ9"/>
                                    <constraint firstAttribute="bottom" secondItem="l9h-fe-GaG" secondAttribute="bottom" constant="12" id="38g-65-cPK"/>
                                    <constraint firstItem="l9h-fe-GaG" firstAttribute="leading" secondItem="8v2-HX-USf" secondAttribute="leading" constant="12" id="5Ng-GS-bB8"/>
                                    <constraint firstItem="l9h-fe-GaG" firstAttribute="top" secondItem="8v2-HX-USf" secondAttribute="top" constant="12" id="CId-Zb-zUc"/>
                                    <constraint firstAttribute="trailing" secondItem="mr2-Vk-IaP" secondAttribute="trailing" constant="18" id="CjW-n6-bCC"/>
                                    <constraint firstItem="mr2-Vk-IaP" firstAttribute="leading" secondItem="l9h-fe-GaG" secondAttribute="trailing" constant="12" id="GAy-fg-u4P"/>
                                    <constraint firstItem="mr2-Vk-IaP" firstAttribute="centerY" secondItem="8v2-HX-USf" secondAttribute="centerY" id="ZXo-33-EGR"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="SfH-la-A99" firstAttribute="centerY" secondItem="Znl-Zm-Cvk" secondAttribute="centerY" id="06d-oL-e3E"/>
                            <constraint firstItem="Pxs-pn-Uf4" firstAttribute="top" secondItem="a3G-GC-g0y" secondAttribute="top" id="0oS-5z-E9B"/>
                            <constraint firstItem="mm1-3q-nx5" firstAttribute="top" secondItem="Pxs-pn-Uf4" secondAttribute="bottom" constant="12" id="8z2-pV-Edx"/>
                            <constraint firstItem="g26-DK-wB4" firstAttribute="leading" secondItem="k9d-ez-uNV" secondAttribute="trailing" constant="12" id="D3L-5S-2Ya"/>
                            <constraint firstItem="4PM-tI-dja" firstAttribute="centerY" secondItem="g26-DK-wB4" secondAttribute="centerY" id="Gwm-lZ-o6T"/>
                            <constraint firstItem="k9d-ez-uNV" firstAttribute="leading" secondItem="4PM-tI-dja" secondAttribute="leading" id="Kww-eP-PhS"/>
                            <constraint firstItem="RYw-qU-JNx" firstAttribute="leading" secondItem="mm1-3q-nx5" secondAttribute="trailing" constant="6" id="LfU-bH-2R7"/>
                            <constraint firstItem="g26-DK-wB4" firstAttribute="leading" secondItem="4PM-tI-dja" secondAttribute="trailing" constant="12" id="S7o-9a-QLT"/>
                            <constraint firstItem="RYw-qU-JNx" firstAttribute="centerY" secondItem="mm1-3q-nx5" secondAttribute="centerY" id="Sgf-VW-YaP"/>
                            <constraint firstItem="8v2-HX-USf" firstAttribute="top" secondItem="a3G-GC-g0y" secondAttribute="bottom" constant="12" id="Zmr-iN-JuK"/>
                            <constraint firstItem="8v2-HX-USf" firstAttribute="leading" secondItem="hAY-fR-Nsq" secondAttribute="leading" constant="18" id="cQU-dw-749"/>
                            <constraint firstItem="a3G-GC-g0y" firstAttribute="leading" secondItem="Znl-Zm-Cvk" secondAttribute="leading" id="e0T-EK-e5F"/>
                            <constraint firstItem="Pxs-pn-Uf4" firstAttribute="leading" secondItem="a3G-GC-g0y" secondAttribute="trailing" constant="8" symbolic="YES" id="ea0-LR-UH5"/>
                            <constraint firstAttribute="trailing" secondItem="8v2-HX-USf" secondAttribute="trailing" constant="18" id="fpC-cT-EbK"/>
                            <constraint firstAttribute="trailing" secondItem="SfH-la-A99" secondAttribute="trailing" constant="18" id="fwH-4J-5s8"/>
                            <constraint firstItem="a3G-GC-g0y" firstAttribute="top" secondItem="Znl-Zm-Cvk" secondAttribute="bottom" constant="18" id="ldD-u8-exB"/>
                            <constraint firstAttribute="bottom" secondItem="g26-DK-wB4" secondAttribute="bottom" constant="18" id="mR0-LV-el7"/>
                            <constraint firstItem="Znl-Zm-Cvk" firstAttribute="top" secondItem="hAY-fR-Nsq" secondAttribute="top" constant="18" id="qUs-Vi-aII"/>
                            <constraint firstItem="SfH-la-A99" firstAttribute="leading" secondItem="Znl-Zm-Cvk" secondAttribute="trailing" constant="6" id="rZJ-DI-C8k"/>
                            <constraint firstItem="mm1-3q-nx5" firstAttribute="leading" secondItem="Pxs-pn-Uf4" secondAttribute="leading" id="rpy-8A-IuH"/>
                            <constraint firstAttribute="trailing" secondItem="Pxs-pn-Uf4" secondAttribute="trailing" constant="18" id="v6w-E0-rpH"/>
                            <constraint firstAttribute="trailing" secondItem="g26-DK-wB4" secondAttribute="trailing" constant="18" id="xLU-4A-ldb"/>
                            <constraint firstItem="Znl-Zm-Cvk" firstAttribute="leading" secondItem="hAY-fR-Nsq" secondAttribute="leading" constant="18" id="yQc-Pi-gH3"/>
                            <constraint firstItem="k9d-ez-uNV" firstAttribute="bottom" secondItem="4PM-tI-dja" secondAttribute="bottom" id="yr2-ys-eSr"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="hAY-fR-Nsq" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="OUs-1P-jNJ"/>
                    <constraint firstAttribute="bottom" secondItem="hAY-fR-Nsq" secondAttribute="bottom" constant="12" id="Vzw-rw-FYr"/>
                    <constraint firstItem="hAY-fR-Nsq" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="dFv-u7-7Sa"/>
                    <constraint firstAttribute="trailing" secondItem="hAY-fR-Nsq" secondAttribute="trailing" id="jup-8U-YhW"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="applyIDLabel" destination="Znl-Zm-Cvk" id="136-zs-3Sh"/>
                <outlet property="applyIDValue" destination="SfH-la-A99" id="JAx-SR-Ofg"/>
                <outlet property="applyReasonLabel" destination="l9h-fe-GaG" id="chi-Hq-boZ"/>
                <outlet property="applyReasonView" destination="8v2-HX-USf" id="Qee-uz-k9g"/>
                <outlet property="commodityCountLabel" destination="mm1-3q-nx5" id="fT3-wP-P5G"/>
                <outlet property="commodityCountValue" destination="RYw-qU-JNx" id="Dc1-GE-T2Z"/>
                <outlet property="commodityImageView" destination="a3G-GC-g0y" id="ExD-oI-4ov"/>
                <outlet property="commodityName" destination="Pxs-pn-Uf4" id="CGc-cH-CUa"/>
                <outlet property="mainView" destination="hAY-fR-Nsq" id="8xV-SW-UGA"/>
                <outlet property="mainViewBottomConstraint" destination="Vzw-rw-FYr" id="e9D-FV-UDA"/>
                <outlet property="mainViewLeftConstraint" destination="OUs-1P-jNJ" id="5FO-U0-ayP"/>
                <outlet property="mainViewRightConstraint" destination="jup-8U-YhW" id="Rgv-yS-ExR"/>
                <outlet property="mainViewTopConstraint" destination="dFv-u7-7Sa" id="ltn-Lj-sHR"/>
                <outlet property="replaceNewButton" destination="4PM-tI-dja" id="qTF-XG-tlj"/>
                <outlet property="returnButton" destination="g26-DK-wB4" id="ohX-Wp-hWY"/>
                <outlet property="returnButtonWidth" destination="QKg-E5-Alf" id="A3n-Fc-d1D"/>
                <outlet property="returnDamageButton" destination="k9d-ez-uNV" id="SgL-gn-R2w"/>
                <outlet property="returnDamageButtonWidth" destination="vZg-5l-eRx" id="JPs-eW-qoe"/>
                <outlet property="rightArrowImageView" destination="mr2-Vk-IaP" id="2Fa-if-MD4"/>
            </connections>
            <point key="canvasLocation" x="843" y="229"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemTealColor">
            <color red="0.18823529411764706" green="0.69019607843137254" blue="0.7803921568627451" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
