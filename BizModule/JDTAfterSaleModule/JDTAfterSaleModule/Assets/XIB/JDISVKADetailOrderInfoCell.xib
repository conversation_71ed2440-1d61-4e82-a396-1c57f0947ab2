<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="759" id="KGk-i7-Jjw" customClass="JDISVKADetailOrderInfoCell">
            <rect key="frame" x="0.0" y="0.0" width="516" height="1200"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="516" height="1200"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="reP-cA-HQu">
                        <rect key="frame" x="0.0" y="0.0" width="516" height="1188"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="PoK-zy-eFr">
                                <rect key="frame" x="0.0" y="12" width="516" height="1172"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kuX-II-Dud">
                                        <rect key="frame" x="0.0" y="0.0" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="服务单号22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0CB-wC-0EE">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="NWV-Cm-tGo"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vi4-d1-LrH">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="0CB-wC-0EE" firstAttribute="centerY" secondItem="kuX-II-Dud" secondAttribute="centerY" id="NSs-m1-R5F"/>
                                            <constraint firstItem="vi4-d1-LrH" firstAttribute="leading" secondItem="0CB-wC-0EE" secondAttribute="trailing" constant="12" id="NnR-js-PLl"/>
                                            <constraint firstAttribute="trailing" secondItem="vi4-d1-LrH" secondAttribute="trailing" constant="18" id="T8R-bv-f1u"/>
                                            <constraint firstItem="0CB-wC-0EE" firstAttribute="leading" secondItem="kuX-II-Dud" secondAttribute="leading" constant="18" id="nfh-it-0fG"/>
                                            <constraint firstItem="vi4-d1-LrH" firstAttribute="top" secondItem="0CB-wC-0EE" secondAttribute="top" id="roo-BC-UmG"/>
                                            <constraint firstAttribute="height" constant="52" id="ru9-v3-OHp"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1BI-4h-qyx">
                                        <rect key="frame" x="0.0" y="52" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="申请时间222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jqx-yA-BO8">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="Lnx-Ez-0N3"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6gM-Bx-PRO">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="6gM-Bx-PRO" secondAttribute="trailing" constant="18" id="0cJ-uR-YgW"/>
                                            <constraint firstItem="Jqx-yA-BO8" firstAttribute="leading" secondItem="1BI-4h-qyx" secondAttribute="leading" constant="18" id="WjF-jY-PRc"/>
                                            <constraint firstItem="6gM-Bx-PRO" firstAttribute="leading" secondItem="Jqx-yA-BO8" secondAttribute="trailing" constant="12" id="dPS-z0-550"/>
                                            <constraint firstAttribute="height" constant="52" id="hvr-2e-8gg"/>
                                            <constraint firstItem="Jqx-yA-BO8" firstAttribute="centerY" secondItem="1BI-4h-qyx" secondAttribute="centerY" id="hyr-cQ-fty"/>
                                            <constraint firstItem="6gM-Bx-PRO" firstAttribute="top" secondItem="Jqx-yA-BO8" secondAttribute="top" id="lZ0-Uh-zAj"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="720-IF-dvY">
                                        <rect key="frame" x="0.0" y="104" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="售后类型22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XFv-4b-ece">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="2sf-VN-Opi"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="4" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PR6-bf-l7f">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="XFv-4b-ece" firstAttribute="leading" secondItem="720-IF-dvY" secondAttribute="leading" constant="18" id="4bA-Sp-QQs"/>
                                            <constraint firstItem="PR6-bf-l7f" firstAttribute="top" secondItem="XFv-4b-ece" secondAttribute="top" id="TEJ-b5-ELU"/>
                                            <constraint firstAttribute="trailing" secondItem="PR6-bf-l7f" secondAttribute="trailing" constant="18" id="YlE-Ky-MKr"/>
                                            <constraint firstAttribute="height" constant="52" id="fk6-vZ-awI"/>
                                            <constraint firstItem="XFv-4b-ece" firstAttribute="centerY" secondItem="720-IF-dvY" secondAttribute="centerY" id="uNl-qR-5FK"/>
                                            <constraint firstItem="PR6-bf-l7f" firstAttribute="leading" secondItem="XFv-4b-ece" secondAttribute="trailing" constant="12" id="vMq-B7-2gM"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SoG-Y2-Ny2">
                                        <rect key="frame" x="0.0" y="156" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="申请原因22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hJ1-6m-VkZ">
                                                <rect key="frame" x="18" y="6" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="Mxs-qe-s52"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="3" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="12" translatesAutoresizingMaskIntoConstraints="NO" id="mEJ-cK-f4K">
                                                <rect key="frame" x="130" y="6" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="mEJ-cK-f4K" secondAttribute="trailing" constant="18" id="2rc-VA-lOI"/>
                                            <constraint firstAttribute="height" constant="52" id="8rP-Bz-azd"/>
                                            <constraint firstItem="mEJ-cK-f4K" firstAttribute="leading" secondItem="hJ1-6m-VkZ" secondAttribute="trailing" constant="12" id="9f9-hp-7Yw"/>
                                            <constraint firstItem="hJ1-6m-VkZ" firstAttribute="leading" secondItem="SoG-Y2-Ny2" secondAttribute="leading" constant="18" id="pSO-6R-96K"/>
                                            <constraint firstItem="hJ1-6m-VkZ" firstAttribute="top" secondItem="SoG-Y2-Ny2" secondAttribute="top" constant="6" id="u3W-V8-et0"/>
                                            <constraint firstItem="mEJ-cK-f4K" firstAttribute="top" secondItem="hJ1-6m-VkZ" secondAttribute="top" id="yoN-kl-IMT"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hSz-hl-29Y">
                                        <rect key="frame" x="0.0" y="208" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="问题描述22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RCl-Ei-waF">
                                                <rect key="frame" x="18" y="6" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="ZFq-Ya-8Tn"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="1000" text="3" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="oHA-ce-9Md">
                                                <rect key="frame" x="130" y="6" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="RCl-Ei-waF" firstAttribute="leading" secondItem="hSz-hl-29Y" secondAttribute="leading" constant="18" id="68f-se-GZd"/>
                                            <constraint firstAttribute="height" constant="52" id="gQ5-Cd-zmo"/>
                                            <constraint firstItem="oHA-ce-9Md" firstAttribute="leading" secondItem="RCl-Ei-waF" secondAttribute="trailing" constant="12" id="qDc-A2-nNb"/>
                                            <constraint firstItem="oHA-ce-9Md" firstAttribute="top" secondItem="RCl-Ei-waF" secondAttribute="top" id="v7k-py-i96"/>
                                            <constraint firstItem="RCl-Ei-waF" firstAttribute="top" secondItem="hSz-hl-29Y" secondAttribute="top" constant="6" id="yn7-vM-ilo"/>
                                            <constraint firstAttribute="trailing" secondItem="oHA-ce-9Md" secondAttribute="trailing" constant="18" id="zu1-bW-cHy"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bai-0e-ZQ8">
                                        <rect key="frame" x="0.0" y="260" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="退款方式22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SmE-5z-oeP">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="2PS-2I-PMg"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ASx-WU-7Wm">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="ASx-WU-7Wm" secondAttribute="trailing" constant="18" id="2WO-aq-Ppp"/>
                                            <constraint firstItem="ASx-WU-7Wm" firstAttribute="top" secondItem="SmE-5z-oeP" secondAttribute="top" id="6jF-Y6-hee"/>
                                            <constraint firstAttribute="height" constant="52" id="JVY-Wo-Hii"/>
                                            <constraint firstItem="SmE-5z-oeP" firstAttribute="centerY" secondItem="bai-0e-ZQ8" secondAttribute="centerY" id="NvQ-vj-IAx"/>
                                            <constraint firstItem="SmE-5z-oeP" firstAttribute="leading" secondItem="bai-0e-ZQ8" secondAttribute="leading" constant="18" id="odZ-Ma-hgf"/>
                                            <constraint firstItem="ASx-WU-7Wm" firstAttribute="leading" secondItem="SmE-5z-oeP" secondAttribute="trailing" constant="12" id="w90-1g-Rt1"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0Ws-m8-xME">
                                        <rect key="frame" x="0.0" y="312" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="银行名称22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="K2B-9i-46o">
                                                <rect key="frame" x="18" y="7.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="8KU-Lz-3q2"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fSi-lX-BsF">
                                                <rect key="frame" x="130" y="7.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="fSi-lX-BsF" secondAttribute="trailing" constant="18" id="WZC-Ga-Pvb"/>
                                            <constraint firstItem="K2B-9i-46o" firstAttribute="leading" secondItem="0Ws-m8-xME" secondAttribute="leading" constant="18" id="YKe-nt-qCF"/>
                                            <constraint firstItem="K2B-9i-46o" firstAttribute="top" secondItem="0Ws-m8-xME" secondAttribute="top" constant="7.5" id="h1N-8I-4xt"/>
                                            <constraint firstAttribute="height" constant="52" id="krt-4i-vdV"/>
                                            <constraint firstItem="fSi-lX-BsF" firstAttribute="top" secondItem="K2B-9i-46o" secondAttribute="top" id="nZM-jb-khK"/>
                                            <constraint firstItem="fSi-lX-BsF" firstAttribute="leading" secondItem="K2B-9i-46o" secondAttribute="trailing" constant="12" id="w30-Fd-MId"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hnT-pf-Kqs">
                                        <rect key="frame" x="0.0" y="364" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="银行卡号22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LFe-uf-lSZ">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="aUl-Mf-Rp0"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zOh-Rl-9eh">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="LFe-uf-lSZ" firstAttribute="leading" secondItem="hnT-pf-Kqs" secondAttribute="leading" constant="18" id="KK5-Su-8X5"/>
                                            <constraint firstItem="zOh-Rl-9eh" firstAttribute="leading" secondItem="LFe-uf-lSZ" secondAttribute="trailing" constant="12" id="MDo-gT-VZi"/>
                                            <constraint firstAttribute="trailing" secondItem="zOh-Rl-9eh" secondAttribute="trailing" constant="18" id="Taz-PR-EEz"/>
                                            <constraint firstItem="LFe-uf-lSZ" firstAttribute="centerY" secondItem="hnT-pf-Kqs" secondAttribute="centerY" id="Y9f-oz-56y"/>
                                            <constraint firstAttribute="height" constant="52" id="Yn3-fo-DGY"/>
                                            <constraint firstItem="zOh-Rl-9eh" firstAttribute="top" secondItem="LFe-uf-lSZ" secondAttribute="top" id="uNo-OQ-7G3"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wBk-Yh-pcw">
                                        <rect key="frame" x="0.0" y="416" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="银行开户姓名22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yqV-uP-9pm">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="jIo-xl-zN7"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kkA-Gp-iRn">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="yqV-uP-9pm" firstAttribute="centerY" secondItem="wBk-Yh-pcw" secondAttribute="centerY" id="CcZ-OU-Cqk"/>
                                            <constraint firstItem="kkA-Gp-iRn" firstAttribute="leading" secondItem="yqV-uP-9pm" secondAttribute="trailing" constant="12" id="DMa-ER-lkg"/>
                                            <constraint firstItem="yqV-uP-9pm" firstAttribute="leading" secondItem="wBk-Yh-pcw" secondAttribute="leading" constant="18" id="G66-Uw-ZVw"/>
                                            <constraint firstAttribute="height" constant="52" id="P3U-rc-Qq0"/>
                                            <constraint firstAttribute="trailing" secondItem="kkA-Gp-iRn" secondAttribute="trailing" constant="18" id="Seh-EZ-aQs"/>
                                            <constraint firstItem="kkA-Gp-iRn" firstAttribute="top" secondItem="yqV-uP-9pm" secondAttribute="top" id="i4h-rM-sk2"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8eC-gN-vf4">
                                        <rect key="frame" x="0.0" y="468" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="手机号码22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ev1-QY-nmq">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="fO7-bF-gbs"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vDZ-FC-HA9">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="vDZ-FC-HA9" firstAttribute="leading" secondItem="Ev1-QY-nmq" secondAttribute="trailing" constant="12" id="3ml-i1-pvJ"/>
                                            <constraint firstAttribute="height" constant="52" id="5Uc-h1-aRa"/>
                                            <constraint firstItem="Ev1-QY-nmq" firstAttribute="centerY" secondItem="8eC-gN-vf4" secondAttribute="centerY" id="Dp2-FJ-6be"/>
                                            <constraint firstAttribute="trailing" secondItem="vDZ-FC-HA9" secondAttribute="trailing" constant="18" id="NkC-Ti-fGE"/>
                                            <constraint firstItem="Ev1-QY-nmq" firstAttribute="leading" secondItem="8eC-gN-vf4" secondAttribute="leading" constant="18" id="eNa-EZ-us3"/>
                                            <constraint firstItem="vDZ-FC-HA9" firstAttribute="top" secondItem="Ev1-QY-nmq" secondAttribute="top" id="vwh-Hc-Xs0"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9nv-M6-Chl">
                                        <rect key="frame" x="0.0" y="520" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="退货方式" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fLF-lM-gP6">
                                                <rect key="frame" x="18" y="16" width="100" height="20.5"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="NYp-E4-gAd"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZdT-r5-sTU">
                                                <rect key="frame" x="130" y="16" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="ZdT-r5-sTU" firstAttribute="top" secondItem="fLF-lM-gP6" secondAttribute="top" id="4SU-c4-Ycb"/>
                                            <constraint firstAttribute="height" constant="52" id="R5a-Im-YAu"/>
                                            <constraint firstItem="fLF-lM-gP6" firstAttribute="leading" secondItem="9nv-M6-Chl" secondAttribute="leading" constant="18" id="gSo-2M-XHA"/>
                                            <constraint firstItem="ZdT-r5-sTU" firstAttribute="leading" secondItem="fLF-lM-gP6" secondAttribute="trailing" constant="12" id="iOB-VD-tMS"/>
                                            <constraint firstItem="fLF-lM-gP6" firstAttribute="centerY" secondItem="9nv-M6-Chl" secondAttribute="centerY" id="ouF-Lr-2M2"/>
                                            <constraint firstAttribute="trailing" secondItem="ZdT-r5-sTU" secondAttribute="trailing" constant="18" id="rhw-vA-wjc"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eCa-g0-4rN">
                                        <rect key="frame" x="0.0" y="572" width="516" height="36"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="快递员分 2 次取件，请按指定件数寄回" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TwG-n4-hF1">
                                                <rect key="frame" x="33" y="8" width="471" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="F9F-4c-qeB">
                                                <rect key="frame" x="12" y="10" width="16" height="16"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="16" id="ggq-CN-2mw"/>
                                                    <constraint firstAttribute="height" constant="16" id="oVK-Iw-pAB"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="TwG-n4-hF1" secondAttribute="trailing" constant="12" id="53h-2C-zA6"/>
                                            <constraint firstItem="F9F-4c-qeB" firstAttribute="leading" secondItem="eCa-g0-4rN" secondAttribute="leading" constant="12" id="NBI-tE-K3z"/>
                                            <constraint firstAttribute="height" constant="36" id="VCq-fp-ukT"/>
                                            <constraint firstItem="TwG-n4-hF1" firstAttribute="leading" secondItem="F9F-4c-qeB" secondAttribute="trailing" constant="5" id="doL-IQ-Fwe"/>
                                            <constraint firstItem="F9F-4c-qeB" firstAttribute="top" secondItem="eCa-g0-4rN" secondAttribute="top" constant="10" id="gCf-kr-C1h"/>
                                            <constraint firstItem="TwG-n4-hF1" firstAttribute="centerY" secondItem="eCa-g0-4rN" secondAttribute="centerY" id="vrD-G0-Dms"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HaF-Zq-A4k">
                                        <rect key="frame" x="0.0" y="608" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="取件状态" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rrU-WX-x2Y">
                                                <rect key="frame" x="18" y="16" width="100" height="20.5"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="01f-ff-XjH"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8m6-lL-x3y">
                                                <rect key="frame" x="130" y="16" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="8m6-lL-x3y" secondAttribute="trailing" constant="18" id="1H6-2C-LLh"/>
                                            <constraint firstItem="8m6-lL-x3y" firstAttribute="top" secondItem="rrU-WX-x2Y" secondAttribute="top" id="AMV-op-nd5"/>
                                            <constraint firstAttribute="height" constant="52" id="JGl-Xu-ttH"/>
                                            <constraint firstItem="rrU-WX-x2Y" firstAttribute="centerY" secondItem="HaF-Zq-A4k" secondAttribute="centerY" id="lC5-if-Lru"/>
                                            <constraint firstItem="8m6-lL-x3y" firstAttribute="leading" secondItem="rrU-WX-x2Y" secondAttribute="trailing" constant="12" id="v6o-YH-tTN"/>
                                            <constraint firstItem="rrU-WX-x2Y" firstAttribute="leading" secondItem="HaF-Zq-A4k" secondAttribute="leading" constant="18" id="waz-LH-dff"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RZb-yi-YQW">
                                        <rect key="frame" x="0.0" y="660" width="516" height="100"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="取件信息222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yRD-kI-phU">
                                                <rect key="frame" x="18" y="13" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="jxh-A9-EQS"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="333dslfdsokfokokfkodsokfdsokfokdsokfokdsokfdsokfokdsokfsokdfkodsokfsdkofkosdokf" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oKp-x5-soJ">
                                                <rect key="frame" x="130" y="62.5" width="368" height="29"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="7" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="Fit-eA-sEH">
                                                <rect key="frame" x="130" y="13" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="444" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="m9U-0s-Zgw">
                                                <rect key="frame" x="130" y="39.5" width="368" height="17"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="17" id="VbB-Mc-hPC"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="yRD-kI-phU" firstAttribute="leading" secondItem="RZb-yi-YQW" secondAttribute="leading" constant="18" id="7wZ-vT-YkT"/>
                                            <constraint firstItem="m9U-0s-Zgw" firstAttribute="top" secondItem="Fit-eA-sEH" secondAttribute="bottom" constant="6" id="Evb-eH-gjc"/>
                                            <constraint firstAttribute="trailing" secondItem="Fit-eA-sEH" secondAttribute="trailing" constant="18" id="GMW-51-X5h"/>
                                            <constraint firstItem="Fit-eA-sEH" firstAttribute="top" secondItem="yRD-kI-phU" secondAttribute="top" id="Luc-ne-1pF"/>
                                            <constraint firstAttribute="trailing" secondItem="oKp-x5-soJ" secondAttribute="trailing" constant="18" id="TLz-ci-Tox"/>
                                            <constraint firstItem="oKp-x5-soJ" firstAttribute="top" secondItem="m9U-0s-Zgw" secondAttribute="bottom" constant="6" id="Tff-aG-4NH"/>
                                            <constraint firstItem="yRD-kI-phU" firstAttribute="top" secondItem="RZb-yi-YQW" secondAttribute="top" constant="13" id="Uei-za-ynv"/>
                                            <constraint firstAttribute="height" constant="100" id="YyS-Sq-Jnd"/>
                                            <constraint firstAttribute="trailing" secondItem="m9U-0s-Zgw" secondAttribute="trailing" constant="18" id="ZbM-bf-lR8"/>
                                            <constraint firstItem="Fit-eA-sEH" firstAttribute="leading" secondItem="yRD-kI-phU" secondAttribute="trailing" constant="12" id="jWm-eC-FLR"/>
                                            <constraint firstItem="oKp-x5-soJ" firstAttribute="leading" secondItem="Fit-eA-sEH" secondAttribute="leading" id="lZK-yI-hXV"/>
                                            <constraint firstItem="m9U-0s-Zgw" firstAttribute="leading" secondItem="yRD-kI-phU" secondAttribute="trailing" constant="12" id="zWx-I3-AL0"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J4Q-dl-qFA">
                                        <rect key="frame" x="0.0" y="760" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="承运商2222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ma4-Il-udJ">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="moo-Zn-teC"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4nS-SC-B3E">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="4nS-SC-B3E" firstAttribute="top" secondItem="Ma4-Il-udJ" secondAttribute="top" id="HTS-tn-0Ut"/>
                                            <constraint firstItem="Ma4-Il-udJ" firstAttribute="centerY" secondItem="J4Q-dl-qFA" secondAttribute="centerY" id="PDQ-Xg-Vde"/>
                                            <constraint firstItem="Ma4-Il-udJ" firstAttribute="leading" secondItem="J4Q-dl-qFA" secondAttribute="leading" constant="18" id="Sxm-8Q-SAs"/>
                                            <constraint firstAttribute="height" constant="52" id="qwW-z9-0Ys"/>
                                            <constraint firstItem="4nS-SC-B3E" firstAttribute="leading" secondItem="Ma4-Il-udJ" secondAttribute="trailing" constant="12" id="tju-7w-rU3"/>
                                            <constraint firstAttribute="trailing" secondItem="4nS-SC-B3E" secondAttribute="trailing" constant="18" id="wdq-Mh-R3r"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rmq-4v-EdM">
                                        <rect key="frame" x="0.0" y="812" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="取件单号2222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="08r-Wu-cNz">
                                                <rect key="frame" x="18" y="6" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="w2D-75-rZq"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2Do-rQ-dzs">
                                                <rect key="frame" x="130" y="6" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="08r-Wu-cNz" firstAttribute="leading" secondItem="rmq-4v-EdM" secondAttribute="leading" constant="18" id="04D-Ue-1vr"/>
                                            <constraint firstItem="08r-Wu-cNz" firstAttribute="top" secondItem="rmq-4v-EdM" secondAttribute="top" constant="6" id="0du-by-DZC"/>
                                            <constraint firstItem="2Do-rQ-dzs" firstAttribute="leading" secondItem="08r-Wu-cNz" secondAttribute="trailing" constant="12" id="WVr-zM-equ"/>
                                            <constraint firstItem="2Do-rQ-dzs" firstAttribute="top" secondItem="08r-Wu-cNz" secondAttribute="top" id="aZx-uD-2Zr"/>
                                            <constraint firstAttribute="trailing" secondItem="2Do-rQ-dzs" secondAttribute="trailing" constant="18" id="rMb-ht-lh1"/>
                                            <constraint firstAttribute="height" constant="52" id="zJo-RL-BMu"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NgT-GC-DiB">
                                        <rect key="frame" x="0.0" y="864" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="取件时间2222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xKi-x5-DjF">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="WJh-hW-8Ci"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uaf-mv-Ngm">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="uaf-mv-Ngm" firstAttribute="top" secondItem="xKi-x5-DjF" secondAttribute="top" id="0eg-bz-jj6"/>
                                            <constraint firstItem="uaf-mv-Ngm" firstAttribute="leading" secondItem="xKi-x5-DjF" secondAttribute="trailing" constant="12" id="Oy0-Y3-z5U"/>
                                            <constraint firstItem="xKi-x5-DjF" firstAttribute="leading" secondItem="NgT-GC-DiB" secondAttribute="leading" constant="18" id="VS2-mV-YIQ"/>
                                            <constraint firstAttribute="trailing" secondItem="uaf-mv-Ngm" secondAttribute="trailing" constant="18" id="XLp-hR-HF9"/>
                                            <constraint firstItem="xKi-x5-DjF" firstAttribute="centerY" secondItem="NgT-GC-DiB" secondAttribute="centerY" id="tfx-wk-fyW"/>
                                            <constraint firstAttribute="height" constant="52" id="v3E-SV-7pC"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ISv-z4-QZO">
                                        <rect key="frame" x="0.0" y="916" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="发件人信息22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kxz-7d-hR8">
                                                <rect key="frame" x="18" y="6" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="kFZ-X4-X5n"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="6" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e36-Bt-Pkt">
                                                <rect key="frame" x="130" y="6" width="380" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="52" id="3Tc-WJ-R3V"/>
                                            <constraint firstItem="e36-Bt-Pkt" firstAttribute="leading" secondItem="Kxz-7d-hR8" secondAttribute="trailing" constant="12" id="N1y-n3-pJA"/>
                                            <constraint firstItem="Kxz-7d-hR8" firstAttribute="top" secondItem="ISv-z4-QZO" secondAttribute="top" constant="6" id="Qpp-Pv-SsO"/>
                                            <constraint firstItem="Kxz-7d-hR8" firstAttribute="leading" secondItem="ISv-z4-QZO" secondAttribute="leading" constant="18" id="nON-h3-wnq"/>
                                            <constraint firstItem="e36-Bt-Pkt" firstAttribute="top" secondItem="Kxz-7d-hR8" secondAttribute="top" id="rvm-w6-TQ3"/>
                                            <constraint firstAttribute="trailing" secondItem="e36-Bt-Pkt" secondAttribute="trailing" constant="6" id="vkx-v8-Uy4"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GtZ-wk-fCx">
                                        <rect key="frame" x="0.0" y="968" width="516" height="100"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="收件人信息22222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lzu-lB-KXj">
                                                <rect key="frame" x="18" y="13" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="dWc-qd-Oee"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="333dslfdsokfokokfkodsokfdsokfokdsokfokdsokfdsokfokdsokfsokdfkodsokfsdkofkosdokf" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tuo-dX-gJf">
                                                <rect key="frame" x="130" y="62.5" width="368" height="29"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="7" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="7At-Oa-83K">
                                                <rect key="frame" x="130" y="13" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tnV-WK-nuB">
                                                <rect key="frame" x="18" y="0.0" width="498" height="1"/>
                                                <color key="backgroundColor" systemColor="systemOrangeColor"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="7I1-9a-pPD"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="444" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qpv-ju-Ur2">
                                                <rect key="frame" x="130" y="39.5" width="368" height="17"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="17" id="geM-xA-zoH"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="tnV-WK-nuB" firstAttribute="top" secondItem="GtZ-wk-fCx" secondAttribute="top" id="7YE-Ya-8KS"/>
                                            <constraint firstItem="7At-Oa-83K" firstAttribute="top" secondItem="lzu-lB-KXj" secondAttribute="top" id="AWe-qB-J6S"/>
                                            <constraint firstItem="Tuo-dX-gJf" firstAttribute="top" secondItem="qpv-ju-Ur2" secondAttribute="bottom" constant="6" id="FcD-M5-lMx"/>
                                            <constraint firstAttribute="trailing" secondItem="7At-Oa-83K" secondAttribute="trailing" constant="18" id="HXT-H8-yWB"/>
                                            <constraint firstItem="7At-Oa-83K" firstAttribute="leading" secondItem="lzu-lB-KXj" secondAttribute="trailing" constant="12" id="JBC-Nh-CvO"/>
                                            <constraint firstItem="tnV-WK-nuB" firstAttribute="leading" secondItem="GtZ-wk-fCx" secondAttribute="leading" constant="18" id="K9I-0q-tko"/>
                                            <constraint firstAttribute="trailing" secondItem="qpv-ju-Ur2" secondAttribute="trailing" constant="18" id="L8k-Z3-Q3K"/>
                                            <constraint firstItem="qpv-ju-Ur2" firstAttribute="leading" secondItem="lzu-lB-KXj" secondAttribute="trailing" constant="12" id="O22-do-Fl1"/>
                                            <constraint firstItem="qpv-ju-Ur2" firstAttribute="top" secondItem="7At-Oa-83K" secondAttribute="bottom" constant="6" id="SeG-DB-INy"/>
                                            <constraint firstItem="lzu-lB-KXj" firstAttribute="top" secondItem="tnV-WK-nuB" secondAttribute="bottom" constant="12" id="WGZ-ec-aIH"/>
                                            <constraint firstItem="Tuo-dX-gJf" firstAttribute="leading" secondItem="7At-Oa-83K" secondAttribute="leading" id="c2Z-Gh-SNm"/>
                                            <constraint firstAttribute="height" constant="100" id="gc3-T0-5ga"/>
                                            <constraint firstAttribute="trailing" secondItem="tnV-WK-nuB" secondAttribute="trailing" id="kPD-Vf-pqq"/>
                                            <constraint firstAttribute="trailing" secondItem="Tuo-dX-gJf" secondAttribute="trailing" constant="18" id="mtq-i1-47g"/>
                                            <constraint firstItem="lzu-lB-KXj" firstAttribute="leading" secondItem="GtZ-wk-fCx" secondAttribute="leading" constant="18" id="vE1-Us-Kip"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Z52-ti-8hL">
                                        <rect key="frame" x="0.0" y="1068" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="承运商2222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RkE-7H-20h">
                                                <rect key="frame" x="18" y="5.5" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="P3z-H2-ueP"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jsC-vs-kKs">
                                                <rect key="frame" x="130" y="5.5" width="368" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="jsC-vs-kKs" firstAttribute="top" secondItem="RkE-7H-20h" secondAttribute="top" id="2Ge-F4-z68"/>
                                            <constraint firstItem="jsC-vs-kKs" firstAttribute="leading" secondItem="RkE-7H-20h" secondAttribute="trailing" constant="12" id="2zm-uT-ViC"/>
                                            <constraint firstItem="RkE-7H-20h" firstAttribute="leading" secondItem="Z52-ti-8hL" secondAttribute="leading" constant="18" id="A8x-d6-BpT"/>
                                            <constraint firstAttribute="height" constant="52" id="GFE-xS-6kP"/>
                                            <constraint firstItem="RkE-7H-20h" firstAttribute="centerY" secondItem="Z52-ti-8hL" secondAttribute="centerY" id="OFt-vQ-Kuk"/>
                                            <constraint firstAttribute="trailing" secondItem="jsC-vs-kKs" secondAttribute="trailing" constant="18" id="qBN-F1-c3d"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TyO-ki-aau">
                                        <rect key="frame" x="0.0" y="1120" width="516" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1111" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VFv-vr-k5V">
                                                <rect key="frame" x="130" y="6" width="302" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="发货单号222222" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FMo-wI-pdB">
                                                <rect key="frame" x="18" y="6" width="100" height="41"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="Ilh-46-aa7"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bLb-gc-6wn">
                                                <rect key="frame" x="450" y="4.5" width="48" height="24"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="6RZ-QP-4RV"/>
                                                    <constraint firstAttribute="height" constant="24" id="7hl-Mo-kmI"/>
                                                </constraints>
                                                <state key="normal" title="Button"/>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="bLb-gc-6wn" firstAttribute="leading" secondItem="VFv-vr-k5V" secondAttribute="trailing" constant="18" id="ApD-mg-Lgv"/>
                                            <constraint firstItem="VFv-vr-k5V" firstAttribute="leading" secondItem="FMo-wI-pdB" secondAttribute="trailing" constant="12" id="Dqb-Ft-zCR"/>
                                            <constraint firstItem="FMo-wI-pdB" firstAttribute="top" secondItem="TyO-ki-aau" secondAttribute="top" constant="6" id="MXe-Qv-5bY"/>
                                            <constraint firstItem="VFv-vr-k5V" firstAttribute="top" secondItem="FMo-wI-pdB" secondAttribute="top" id="TBN-9z-d0s"/>
                                            <constraint firstItem="bLb-gc-6wn" firstAttribute="centerY" secondItem="VFv-vr-k5V" secondAttribute="centerY" id="a0a-sh-wb6"/>
                                            <constraint firstItem="FMo-wI-pdB" firstAttribute="leading" secondItem="TyO-ki-aau" secondAttribute="leading" constant="18" id="hTK-FE-s3e"/>
                                            <constraint firstAttribute="height" constant="52" id="xDZ-Bc-fau"/>
                                            <constraint firstAttribute="trailing" secondItem="bLb-gc-6wn" secondAttribute="trailing" constant="18" id="ykK-HD-Wk0"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="kuX-II-Dud" firstAttribute="top" secondItem="PoK-zy-eFr" secondAttribute="top" id="7QJ-PT-cqj"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="PoK-zy-eFr" firstAttribute="top" secondItem="reP-cA-HQu" secondAttribute="top" constant="12" id="Lju-eK-uTv"/>
                            <constraint firstAttribute="trailing" secondItem="PoK-zy-eFr" secondAttribute="trailing" id="MaO-dR-EpU"/>
                            <constraint firstItem="PoK-zy-eFr" firstAttribute="leading" secondItem="reP-cA-HQu" secondAttribute="leading" id="ukW-kR-KCU"/>
                            <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="PoK-zy-eFr" secondAttribute="bottom" constant="4" id="xLC-N4-Y6E"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="reP-cA-HQu" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="83k-uO-m0c"/>
                    <constraint firstItem="reP-cA-HQu" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="GpC-U4-cE6"/>
                    <constraint firstAttribute="trailing" secondItem="reP-cA-HQu" secondAttribute="trailing" id="Onb-uw-GKz"/>
                    <constraint firstAttribute="bottom" secondItem="reP-cA-HQu" secondAttribute="bottom" constant="12" id="o2U-jH-eCK"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="afterSaleTypeLabel" destination="XFv-4b-ece" id="pzR-QL-4v4"/>
                <outlet property="afterSaleTypeValue" destination="PR6-bf-l7f" id="wdY-Kj-TWI"/>
                <outlet property="afterSaleTypeView" destination="720-IF-dvY" id="tzU-ZS-fjQ"/>
                <outlet property="afterSaleTypeViewHeight" destination="fk6-vZ-awI" id="1Mw-Ei-Cdf"/>
                <outlet property="applyQuestionDescLabel" destination="RCl-Ei-waF" id="tK2-ij-VMq"/>
                <outlet property="applyQuestionDescValue" destination="oHA-ce-9Md" id="V2Z-9h-eWI"/>
                <outlet property="applyQuestionDescView" destination="hSz-hl-29Y" id="xHa-XC-VCl"/>
                <outlet property="applyReasionLabel" destination="hJ1-6m-VkZ" id="uKS-Mh-eR8"/>
                <outlet property="applyReasionValue" destination="mEJ-cK-f4K" id="MPS-Ir-PJl"/>
                <outlet property="applyReasionView" destination="SoG-Y2-Ny2" id="vj7-68-bSt"/>
                <outlet property="applyTimeLabel" destination="Jqx-yA-BO8" id="qf4-zc-2Am"/>
                <outlet property="applyTimeValue" destination="6gM-Bx-PRO" id="5lA-J4-bGb"/>
                <outlet property="applyTimeView" destination="1BI-4h-qyx" id="jEe-uA-lDF"/>
                <outlet property="applyTimeViewHeight" destination="hvr-2e-8gg" id="hbx-XS-7nm"/>
                <outlet property="bankAccountNameLabel" destination="yqV-uP-9pm" id="oHA-8d-ikN"/>
                <outlet property="bankAccountNameValue" destination="kkA-Gp-iRn" id="sJi-fG-2xm"/>
                <outlet property="bankAccountNameView" destination="wBk-Yh-pcw" id="W2T-WO-d9t"/>
                <outlet property="bankAccountNameViewHeight" destination="P3U-rc-Qq0" id="BRQ-mG-2Z7"/>
                <outlet property="bankCardNumLabel" destination="LFe-uf-lSZ" id="dnn-Uu-kIw"/>
                <outlet property="bankCardNumValue" destination="zOh-Rl-9eh" id="Xxc-K8-T18"/>
                <outlet property="bankCardNumView" destination="hnT-pf-Kqs" id="xkJ-bF-X8U"/>
                <outlet property="bankCardNumViewHeight" destination="Yn3-fo-DGY" id="JwD-xj-EVg"/>
                <outlet property="bankNameLabel" destination="K2B-9i-46o" id="bTT-GD-vcU"/>
                <outlet property="bankNameValue" destination="fSi-lX-BsF" id="gNM-7y-DEv"/>
                <outlet property="bankNameView" destination="0Ws-m8-xME" id="CLn-4u-cy6"/>
                <outlet property="bankNameViewHeight" destination="krt-4i-vdV" id="lQz-2R-wMD"/>
                <outlet property="deliveryInfoLabel" destination="Kxz-7d-hR8" id="lkd-Rr-aRH"/>
                <outlet property="deliveryInfoValue" destination="e36-Bt-Pkt" id="UQJ-7K-Pdg"/>
                <outlet property="deliveryInfoView" destination="ISv-z4-QZO" id="exp-zk-iOB"/>
                <outlet property="deliveryInfoViewHeight" destination="3Tc-WJ-R3V" id="2wd-uB-nIP"/>
                <outlet property="divideLineView" destination="tnV-WK-nuB" id="Qgm-eu-EkG"/>
                <outlet property="invoiceCopyButton" destination="bLb-gc-6wn" id="wuG-Nb-RRR"/>
                <outlet property="invoiceInfoView" destination="TyO-ki-aau" id="Sin-CV-DIX"/>
                <outlet property="invoiceInfoViewHeight" destination="xDZ-Bc-fau" id="2Vc-hl-QHl"/>
                <outlet property="invoiceLabel" destination="FMo-wI-pdB" id="Kb4-71-dba"/>
                <outlet property="invoiceValue" destination="VFv-vr-k5V" id="wwX-N0-0ZA"/>
                <outlet property="mainView" destination="reP-cA-HQu" id="2IK-gM-RZt"/>
                <outlet property="mainViewBottomConstraint" destination="o2U-jH-eCK" id="KWc-DX-VAw"/>
                <outlet property="mainViewLeftConstraint" destination="83k-uO-m0c" id="0GC-7Z-DNI"/>
                <outlet property="mainViewRightConstraint" destination="Onb-uw-GKz" id="gBv-Ps-SLe"/>
                <outlet property="mainViewTopConstraint" destination="GpC-U4-cE6" id="i4H-FO-RdO"/>
                <outlet property="phoneNumberLabel" destination="Ev1-QY-nmq" id="NWT-ch-9KL"/>
                <outlet property="phoneNumberValue" destination="vDZ-FC-HA9" id="YZU-E3-68K"/>
                <outlet property="phoneNumberView" destination="8eC-gN-vf4" id="UhI-gM-huQ"/>
                <outlet property="phoneNumberViewHeight" destination="5Uc-h1-aRa" id="Ixz-K8-ePy"/>
                <outlet property="pickUpAddress" destination="oKp-x5-soJ" id="QoO-h3-vA1"/>
                <outlet property="pickUpCarrierValue" destination="4nS-SC-B3E" id="5P9-qd-AZK"/>
                <outlet property="pickUpCarrierView" destination="J4Q-dl-qFA" id="Tsd-dx-hSN"/>
                <outlet property="pickUpCarrierViewHeight" destination="qwW-z9-0Ys" id="GPf-Yu-NPc"/>
                <outlet property="pickUpCarriereLabel" destination="Ma4-Il-udJ" id="4iY-0m-Iuu"/>
                <outlet property="pickUpInfoLabel" destination="yRD-kI-phU" id="s72-ca-Iei"/>
                <outlet property="pickUpInfoView" destination="RZb-yi-YQW" id="ahl-0N-Jtn"/>
                <outlet property="pickUpInfoViewHeight" destination="YyS-Sq-Jnd" id="TSp-w1-GRC"/>
                <outlet property="pickUpName" destination="Fit-eA-sEH" id="3k4-YZ-ltk"/>
                <outlet property="pickUpPhone" destination="m9U-0s-Zgw" id="Fc0-BS-J3d"/>
                <outlet property="pickUpStatusLabel" destination="rrU-WX-x2Y" id="hX7-vt-ZzB"/>
                <outlet property="pickUpStatusTipIcon" destination="F9F-4c-qeB" id="u1A-3H-Iee"/>
                <outlet property="pickUpStatusTipLabel" destination="TwG-n4-hF1" id="PEa-6i-FNj"/>
                <outlet property="pickUpStatusTipView" destination="eCa-g0-4rN" id="gMR-LV-ZcC"/>
                <outlet property="pickUpStatusTipViewHeight" destination="VCq-fp-ukT" id="7dt-Fz-M9f"/>
                <outlet property="pickUpStatusValue" destination="8m6-lL-x3y" id="iJe-dl-uo5"/>
                <outlet property="pickUpStatusView" destination="HaF-Zq-A4k" id="ndv-FY-uho"/>
                <outlet property="pickUpStatusViewHeight" destination="JGl-Xu-ttH" id="DPb-ho-e1y"/>
                <outlet property="pickUpTimeLabel" destination="xKi-x5-DjF" id="sa1-No-VWK"/>
                <outlet property="pickUpTimeValue" destination="uaf-mv-Ngm" id="dZt-mA-FY7"/>
                <outlet property="pickUpTimeView" destination="NgT-GC-DiB" id="QDa-eA-NzU"/>
                <outlet property="pickUpTimeViewHeight" destination="v3E-SV-7pC" id="zHt-9r-CY3"/>
                <outlet property="pickUpTrackingNumLabel" destination="08r-Wu-cNz" id="1y3-lQ-qMw"/>
                <outlet property="pickUpTrackingNumValue" destination="2Do-rQ-dzs" id="NBA-yz-qeV"/>
                <outlet property="pickUpTrackingNumView" destination="rmq-4v-EdM" id="k4Z-8A-kll"/>
                <outlet property="pickUpTrackingNumViewHeight" destination="zJo-RL-BMu" id="zQT-kM-dEY"/>
                <outlet property="questionDescViewHeight" destination="gQ5-Cd-zmo" id="hsB-pV-Eba"/>
                <outlet property="reasonViewConstraint" destination="8rP-Bz-azd" id="e8Y-bg-aBF"/>
                <outlet property="receiverAddress" destination="Tuo-dX-gJf" id="duf-b7-8ch"/>
                <outlet property="receiverInfoLabel" destination="lzu-lB-KXj" id="pOP-aw-a4g"/>
                <outlet property="receiverInfoView" destination="GtZ-wk-fCx" id="Hdx-hi-4pF"/>
                <outlet property="receiverInfoViewHeight" destination="gc3-T0-5ga" id="75t-Ra-TxO"/>
                <outlet property="receiverName" destination="7At-Oa-83K" id="Uxc-NL-bM8"/>
                <outlet property="receiverPhone" destination="qpv-ju-Ur2" id="aM6-yc-Yhl"/>
                <outlet property="refundTypeLabel" destination="SmE-5z-oeP" id="XXl-2q-Bbp"/>
                <outlet property="refundTypeValue" destination="ASx-WU-7Wm" id="1KV-8V-ivZ"/>
                <outlet property="refundTypeView" destination="bai-0e-ZQ8" id="Piv-Di-bpk"/>
                <outlet property="refundTypeViewHeight" destination="JVY-Wo-Hii" id="tv6-kU-2jR"/>
                <outlet property="returnCarrierView" destination="Z52-ti-8hL" id="BBp-K3-9Tg"/>
                <outlet property="returnCarrierViewHeight" destination="GFE-xS-6kP" id="l1r-dd-FjW"/>
                <outlet property="returnCarriereLabel" destination="RkE-7H-20h" id="0ZE-XH-kWa"/>
                <outlet property="returnCarriereValue" destination="jsC-vs-kKs" id="95k-NY-6bd"/>
                <outlet property="returnTypeLabel" destination="fLF-lM-gP6" id="8nd-gs-7pL"/>
                <outlet property="returnTypeValue" destination="ZdT-r5-sTU" id="QeD-LR-DFo"/>
                <outlet property="returnTypeView" destination="9nv-M6-Chl" id="0Rc-sF-iQg"/>
                <outlet property="returnTypeViewHeight" destination="R5a-Im-YAu" id="hX7-eI-I46"/>
                <outlet property="seriverIDLabel" destination="0CB-wC-0EE" id="LEJ-e4-TIl"/>
                <outlet property="serverIDValue" destination="vi4-d1-LrH" id="Lxw-zD-fzG"/>
                <outlet property="serverIDView" destination="kuX-II-Dud" id="R9s-kV-m2z"/>
                <outlet property="serverIDViewHeight" destination="ru9-v3-OHp" id="KIZ-ED-9rZ"/>
            </connections>
            <point key="canvasLocation" x="311.59420289855075" y="350.89285714285711"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemOrangeColor">
            <color red="1" green="0.58431372550000005" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
