<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="153" id="KGk-i7-Jjw" customClass="JDISVKADetailShopInfoCell">
            <rect key="frame" x="0.0" y="0.0" width="560" height="153"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="560" height="153"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ugu-CZ-TPk">
                        <rect key="frame" x="0.0" y="0.0" width="560" height="141"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P6I-4g-T7m">
                                <rect key="frame" x="18" y="18" width="41.5" height="20.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="X9g-DF-6dW">
                                <rect key="frame" x="71.5" y="16.5" width="64" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="24" id="Wki-RE-NMy"/>
                                    <constraint firstAttribute="width" constant="64" id="WtJ-vv-Onf"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="imr-mp-7bp">
                                <rect key="frame" x="18" y="49.5" width="12" height="12"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="12" id="HGk-ti-Pzh"/>
                                    <constraint firstAttribute="width" constant="12" id="Y5G-uB-g2J"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="2Dk-pY-zbI">
                                <rect key="frame" x="18" y="78" width="12" height="12"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="12" id="7pI-Zj-oHx"/>
                                    <constraint firstAttribute="height" constant="12" id="8HL-Nw-ADI"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="LYc-Yc-0k5">
                                <rect key="frame" x="518" y="75" width="24" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="24" id="ERE-6H-G8x"/>
                                    <constraint firstAttribute="height" constant="24" id="nFW-5A-3AG"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="阿斯顿撒点开撒开的撒看懂胩送快递空嫂的" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="D4O-rF-zwG">
                                <rect key="frame" x="34" y="46.5" width="501" height="20.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Labelsakdpsokadokaskodkoaskodkosaokdaskodkoaskodakosdko" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="309-pd-hvX">
                                <rect key="frame" x="34" y="75" width="459" height="41"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kQl-fV-DzT">
                                <rect key="frame" x="505" y="77" width="1" height="20"/>
                                <color key="backgroundColor" systemColor="systemIndigoColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="1" id="RJp-ij-vjm"/>
                                    <constraint firstAttribute="height" constant="20" id="qiZ-VG-Rpw"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="kQl-fV-DzT" firstAttribute="centerY" secondItem="LYc-Yc-0k5" secondAttribute="centerY" id="0pb-U8-O3l"/>
                            <constraint firstAttribute="trailing" secondItem="LYc-Yc-0k5" secondAttribute="trailing" constant="18" id="331-06-Q3w"/>
                            <constraint firstItem="309-pd-hvX" firstAttribute="leading" secondItem="2Dk-pY-zbI" secondAttribute="trailing" constant="4" id="3Y3-9F-dxy"/>
                            <constraint firstItem="LYc-Yc-0k5" firstAttribute="leading" secondItem="kQl-fV-DzT" secondAttribute="trailing" constant="12" id="87S-FI-6eu"/>
                            <constraint firstItem="imr-mp-7bp" firstAttribute="top" secondItem="D4O-rF-zwG" secondAttribute="top" constant="3" id="A3A-Tq-rhw"/>
                            <constraint firstItem="imr-mp-7bp" firstAttribute="leading" secondItem="P6I-4g-T7m" secondAttribute="leading" id="EKM-LJ-9CT"/>
                            <constraint firstItem="D4O-rF-zwG" firstAttribute="top" secondItem="P6I-4g-T7m" secondAttribute="bottom" constant="8" id="FIt-YG-xua"/>
                            <constraint firstAttribute="trailing" secondItem="D4O-rF-zwG" secondAttribute="trailing" constant="25" id="QG9-fh-fSS"/>
                            <constraint firstItem="P6I-4g-T7m" firstAttribute="leading" secondItem="Ugu-CZ-TPk" secondAttribute="leading" constant="18" id="UJC-Iv-oeC"/>
                            <constraint firstItem="X9g-DF-6dW" firstAttribute="leading" secondItem="P6I-4g-T7m" secondAttribute="trailing" constant="12" id="UJM-SO-MT1"/>
                            <constraint firstItem="X9g-DF-6dW" firstAttribute="centerY" secondItem="P6I-4g-T7m" secondAttribute="centerY" id="ZkP-u3-hCY"/>
                            <constraint firstItem="2Dk-pY-zbI" firstAttribute="top" secondItem="309-pd-hvX" secondAttribute="top" constant="3" id="iOh-jh-9av"/>
                            <constraint firstItem="kQl-fV-DzT" firstAttribute="leading" secondItem="309-pd-hvX" secondAttribute="trailing" constant="12" id="khr-Qk-Jsx"/>
                            <constraint firstItem="2Dk-pY-zbI" firstAttribute="leading" secondItem="P6I-4g-T7m" secondAttribute="leading" id="p8h-Ym-mV2"/>
                            <constraint firstItem="P6I-4g-T7m" firstAttribute="top" secondItem="Ugu-CZ-TPk" secondAttribute="top" constant="18" id="ppP-3E-CVs"/>
                            <constraint firstItem="LYc-Yc-0k5" firstAttribute="top" secondItem="309-pd-hvX" secondAttribute="top" id="qzC-Hg-F8x"/>
                            <constraint firstItem="D4O-rF-zwG" firstAttribute="leading" secondItem="imr-mp-7bp" secondAttribute="trailing" constant="4" id="ucb-Iu-r9k"/>
                            <constraint firstItem="309-pd-hvX" firstAttribute="top" secondItem="D4O-rF-zwG" secondAttribute="bottom" constant="8" id="vaV-xY-qTN"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="X9g-DF-6dW" secondAttribute="trailing" constant="12" id="zQc-rj-5iT"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="Ugu-CZ-TPk" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="4PZ-MW-weg"/>
                    <constraint firstAttribute="trailing" secondItem="Ugu-CZ-TPk" secondAttribute="trailing" id="XeB-DG-p3u"/>
                    <constraint firstItem="Ugu-CZ-TPk" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Ywe-Ha-7DM"/>
                    <constraint firstAttribute="bottom" secondItem="Ugu-CZ-TPk" secondAttribute="bottom" constant="12" id="aUb-t4-Cgi"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="contactImageView" destination="LYc-Yc-0k5" id="egs-eB-1jb"/>
                <outlet property="divideLineView" destination="kQl-fV-DzT" id="D9G-SO-R4w"/>
                <outlet property="mainView" destination="Ugu-CZ-TPk" id="3KH-8g-IHT"/>
                <outlet property="mainViewBottomConstraint" destination="aUb-t4-Cgi" id="yaV-ma-cuV"/>
                <outlet property="mainViewLeftConstraint" destination="4PZ-MW-weg" id="asJ-dh-E09"/>
                <outlet property="mainViewRightConstraint" destination="XeB-DG-p3u" id="Mjq-R9-hs6"/>
                <outlet property="mainViewTopConstraint" destination="Ywe-Ha-7DM" id="0lk-Xi-1Ma"/>
                <outlet property="merchantsName" destination="P6I-4g-T7m" id="BYn-94-CVw"/>
                <outlet property="merchantsTag" destination="X9g-DF-6dW" id="CQU-FO-v5Z"/>
                <outlet property="merchantsTagWidthConstraint" destination="WtJ-vv-Onf" id="HdK-Z5-cTG"/>
                <outlet property="type1ImageVIew" destination="imr-mp-7bp" id="oYz-Yy-dnl"/>
                <outlet property="type1Label" destination="D4O-rF-zwG" id="fCO-k0-24z"/>
                <outlet property="type2ImageView" destination="2Dk-pY-zbI" id="EGw-k1-xge"/>
                <outlet property="type2Label" destination="309-pd-hvX" id="O1N-tP-oI2"/>
            </connections>
            <point key="canvasLocation" x="286.95652173913044" y="108.14732142857143"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemIndigoColor">
            <color red="0.34509803921568627" green="0.33725490196078434" blue="0.83921568627450982" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
