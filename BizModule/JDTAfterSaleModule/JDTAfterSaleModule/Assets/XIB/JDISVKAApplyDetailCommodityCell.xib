<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="192" id="KGk-i7-Jjw" customClass="JDISVKAApplyDetailCommodityCell">
            <rect key="frame" x="0.0" y="0.0" width="441" height="192"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="441" height="192"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3DK-Dq-4Lj">
                        <rect key="frame" x="0.0" y="0.0" width="441" height="180"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="YMe-Qt-ayY">
                                <rect key="frame" x="18" y="20" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="20" id="UAL-Rd-QaV"/>
                                    <constraint firstAttribute="height" constant="20" id="c5P-1z-Wu4"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="sadsadsa" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sEm-Xo-QAR">
                                <rect key="frame" x="40" y="20" width="383" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="kFW-Fl-kLf"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="IMb-q8-RSv">
                                <rect key="frame" x="18" y="58" width="80" height="80"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="80" id="S3F-QK-V4l"/>
                                    <constraint firstAttribute="width" constant="80" id="S8q-yT-4Ps"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="五粮液股份五a头五粮液股份五a头五粮液股份五a头五粮液股份五a头五粮液股份五a头" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vky-X1-TJN">
                                <rect key="frame" x="110" y="58" width="313" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="NcJ-tY-jS4"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="单价" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="k0r-vC-OSK">
                                <rect key="frame" x="110" y="110" width="29" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="5Ld-q2-UK6"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="t3y-zR-iof">
                                <rect key="frame" x="145" y="111.5" width="35.5" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="数量" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IGM-8t-s4h">
                                <rect key="frame" x="192.5" y="109.5" width="35" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HhE-cU-Wp4">
                                <rect key="frame" x="233.5" y="109.5" width="42" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="申请数量" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HSs-63-eXh">
                                <rect key="frame" x="18" y="156" width="69.5" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="djs-bA-pSn"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="YMe-Qt-ayY" firstAttribute="leading" secondItem="3DK-Dq-4Lj" secondAttribute="leading" constant="18" id="1l2-IN-Qmd"/>
                            <constraint firstItem="IGM-8t-s4h" firstAttribute="leading" secondItem="t3y-zR-iof" secondAttribute="trailing" constant="12" id="8Pf-l7-qGa"/>
                            <constraint firstItem="t3y-zR-iof" firstAttribute="leading" secondItem="k0r-vC-OSK" secondAttribute="trailing" constant="6" id="9gu-7M-n26"/>
                            <constraint firstItem="HhE-cU-Wp4" firstAttribute="centerY" secondItem="IGM-8t-s4h" secondAttribute="centerY" id="Bd9-lt-jIY"/>
                            <constraint firstItem="YMe-Qt-ayY" firstAttribute="top" secondItem="3DK-Dq-4Lj" secondAttribute="top" constant="20" id="EDA-A8-hWu"/>
                            <constraint firstItem="HhE-cU-Wp4" firstAttribute="leading" secondItem="IGM-8t-s4h" secondAttribute="trailing" constant="6" id="FI0-kQ-QWc"/>
                            <constraint firstItem="HSs-63-eXh" firstAttribute="leading" secondItem="3DK-Dq-4Lj" secondAttribute="leading" constant="18" id="Lmm-Tv-O7N"/>
                            <constraint firstItem="sEm-Xo-QAR" firstAttribute="leading" secondItem="YMe-Qt-ayY" secondAttribute="trailing" constant="2" id="PIt-sc-LzP"/>
                            <constraint firstItem="vky-X1-TJN" firstAttribute="leading" secondItem="IMb-q8-RSv" secondAttribute="trailing" constant="12" id="Vcv-8U-CEs"/>
                            <constraint firstItem="IMb-q8-RSv" firstAttribute="top" secondItem="YMe-Qt-ayY" secondAttribute="bottom" constant="18" id="Yg9-0I-sEy"/>
                            <constraint firstItem="HSs-63-eXh" firstAttribute="top" secondItem="IMb-q8-RSv" secondAttribute="bottom" constant="18" id="cmD-64-Un7"/>
                            <constraint firstItem="k0r-vC-OSK" firstAttribute="leading" secondItem="IMb-q8-RSv" secondAttribute="trailing" constant="12" id="fgQ-gc-lB8"/>
                            <constraint firstAttribute="trailing" secondItem="vky-X1-TJN" secondAttribute="trailing" constant="18" id="ley-fv-Sn5"/>
                            <constraint firstItem="IGM-8t-s4h" firstAttribute="centerY" secondItem="k0r-vC-OSK" secondAttribute="centerY" id="mja-8K-XFn"/>
                            <constraint firstItem="sEm-Xo-QAR" firstAttribute="centerY" secondItem="YMe-Qt-ayY" secondAttribute="centerY" id="mni-Xe-jXK"/>
                            <constraint firstItem="k0r-vC-OSK" firstAttribute="top" secondItem="vky-X1-TJN" secondAttribute="bottom" constant="12" id="ouS-0P-x4o"/>
                            <constraint firstItem="vky-X1-TJN" firstAttribute="top" secondItem="IMb-q8-RSv" secondAttribute="top" id="pi4-cb-Wpm"/>
                            <constraint firstAttribute="trailing" secondItem="sEm-Xo-QAR" secondAttribute="trailing" constant="18" id="rCn-h3-uT6"/>
                            <constraint firstItem="t3y-zR-iof" firstAttribute="centerY" secondItem="k0r-vC-OSK" secondAttribute="centerY" id="waa-cE-Umy"/>
                            <constraint firstItem="IMb-q8-RSv" firstAttribute="leading" secondItem="3DK-Dq-4Lj" secondAttribute="leading" constant="18" id="zak-iX-3Fj"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="3DK-Dq-4Lj" secondAttribute="trailing" id="DpS-1u-N55"/>
                    <constraint firstAttribute="bottom" secondItem="3DK-Dq-4Lj" secondAttribute="bottom" constant="12" id="gEW-Bv-7gV"/>
                    <constraint firstItem="3DK-Dq-4Lj" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="tgn-Cb-2rF"/>
                    <constraint firstItem="3DK-Dq-4Lj" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="uDI-dK-Vo5"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="applyCountLabel" destination="HSs-63-eXh" id="yDg-kg-Vmi"/>
                <outlet property="commodityImageView" destination="IMb-q8-RSv" id="Gh0-8T-rZi"/>
                <outlet property="commodityNameLabel" destination="vky-X1-TJN" id="vO7-vz-RVJ"/>
                <outlet property="countLabel" destination="IGM-8t-s4h" id="rWe-fG-Fdq"/>
                <outlet property="countValue" destination="HhE-cU-Wp4" id="24R-8g-QIH"/>
                <outlet property="mainView" destination="3DK-Dq-4Lj" id="vlz-pz-cQX"/>
                <outlet property="mainViewBottomConstraint" destination="gEW-Bv-7gV" id="poV-6N-DV3"/>
                <outlet property="mainViewLeftConstraint" destination="uDI-dK-Vo5" id="wzU-Ju-SeF"/>
                <outlet property="mainViewRightConstraint" destination="DpS-1u-N55" id="hph-Px-6Jw"/>
                <outlet property="mainViewTopConstraint" destination="tgn-Cb-2rF" id="w3M-og-arK"/>
                <outlet property="priceLabel" destination="k0r-vC-OSK" id="eRO-St-z6m"/>
                <outlet property="priceValue" destination="t3y-zR-iof" id="cx0-ML-dya"/>
                <outlet property="shopImageView" destination="YMe-Qt-ayY" id="PoC-4A-AjE"/>
                <outlet property="shopNameLabel" destination="sEm-Xo-QAR" id="O5o-nO-Y2G"/>
            </connections>
            <point key="canvasLocation" x="386.23188405797106" y="152.00892857142856"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
