# JDTAfterSaleModule

[![CI Status](https://img.shields.io/travis/lvchenzhu.1/JDTAfterSaleModule.svg?style=flat)](https://travis-ci.org/lvchenzhu.1/JDTAfterSaleModule)
[![Version](https://img.shields.io/cocoapods/v/JDTAfterSaleModule.svg?style=flat)](https://cocoapods.org/pods/JDTAfterSaleModule)
[![License](https://img.shields.io/cocoapods/l/JDTAfterSaleModule.svg?style=flat)](https://cocoapods.org/pods/JDTAfterSaleModule)
[![Platform](https://img.shields.io/cocoapods/p/JDTAfterSaleModule.svg?style=flat)](https://cocoapods.org/pods/JDTAfterSaleModule)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

JDTAfterSaleModule is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'JDTAfterSaleModule'
```

## Author

lvchenzhu.1, <EMAIL>

## License

JDTAfterSaleModule is available under the MIT license. See the LICENSE file for more info.
