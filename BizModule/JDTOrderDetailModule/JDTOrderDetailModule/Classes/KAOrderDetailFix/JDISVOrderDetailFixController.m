//
//  JDISVOrderDetailFixController.m
//  JDISVOrderDetailSDKModule
//
//  Created by cdzhangrunfeng1 on 2022/12/2.
//

#import "JDISVOrderDetailFixController.h"

#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVMasonryModule/Masonry.h>
#import <JDBRouterModule/JDRouter.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
@interface JDISVOrderDetailFixController ()<UITextFieldDelegate>

@property (nonatomic,strong) UIView* topTipView;
@property (nonatomic,strong) UIImageView* tipIcon;
@property (nonatomic,strong) UILabel* topTipsLabel;
@property (nonatomic,strong) UIView* lsitTitleView;
@property (nonatomic,strong) UILabel* lsitTitleLabel;

@property (weak, nonatomic) IBOutlet UILabel *topTipLabel;

@property (unsafe_unretained, nonatomic) IBOutlet UIView *containerView;
@property (strong, nonatomic) UIButton *confirmButton;

@property (nonatomic, assign) CGFloat navigationBarOffset;

@property (weak, nonatomic) IBOutlet UILabel *customerNameLabel;
// 视图
@property (weak, nonatomic) IBOutlet UITextField *customerNameTF;
@property (weak, nonatomic) IBOutlet UILabel *mobileLabel;
@property (weak, nonatomic) IBOutlet UITextField *mobileTF;
@property (weak, nonatomic) IBOutlet UILabel *areLabel;
@property (weak, nonatomic) IBOutlet UILabel *detailAddressLabel;
@property (weak, nonatomic) IBOutlet UITextField *areaTF;
@property (weak, nonatomic) IBOutlet UITextField *detailAddressTF;
@property (weak, nonatomic) IBOutlet UILabel *bottomTipLabel;

// 预填充数据
@property (nonatomic, strong) NSString * customerName; // 收货人姓名
@property (nonatomic, strong) NSString * mobile; // 收货人手机号
@property (nonatomic, strong) NSString * area; // 地区
@property (nonatomic, strong) NSString * detailAddress; // 详细地址


@property (weak, nonatomic) IBOutlet UILabel *labelShadow;
@property (nonatomic, strong) NSNumber * province;
@property (nonatomic, strong) NSNumber * city;
@property (nonatomic, strong) NSNumber * country;
@property (nonatomic, strong) NSNumber * town;

@property (nonatomic, strong) NSString * provinceName;
@property (nonatomic, strong) NSString * cityName;
@property (nonatomic, strong) NSString * countryName;
@property (nonatomic, strong) NSString * townName;

@property (assign,nonatomic) NSInteger limitAddrs;
@property (weak, nonatomic) IBOutlet UIButton *btnArr;

@property (assign, nonatomic) BOOL isShowKeyboard;
@property (assign,nonatomic) NSInteger keyboardOffset;
@end

@implementation JDISVOrderDetailFixController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyBoardDidShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyBoardDidHide:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardDidShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardDidHideNotification object:nil];
}

- (void)keyBoardDidShow:(NSNotification*)aNotification {
    
    if (self.isShowKeyboard)
        return;
    self.isShowKeyboard = YES;
    
        NSDictionary* info = [aNotification userInfo];
        CGSize kbSize = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue].size;
//        self.keyboardHeight = kbSize.height;
    // 获取键盘动画时间
        CGFloat animationTime  = [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    // 定义好动作
        void (^animation)(void) = ^void(void) {
                self.view.frame = CGRectMake(0, self.view.frame.origin.y-self.keyboardOffset, self.view.frame.size.width,  self.view.frame.size.height);
    };
    
    if (animationTime > 0) {
        [UIView animateWithDuration:animationTime animations:animation];
    } else {
        animation();
    }
}
- (void)keyBoardDidHide:(NSNotification*)aNotification {
    if (self.isShowKeyboard == NO)
        return;
    self.isShowKeyboard = NO;
        NSDictionary* info = (NSDictionary *)[aNotification userInfo];
        CGSize kbSize = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue].size;
//        self.keyboardHeight = kbSize.height;
    // 获取键盘动画时间
        CGFloat animationTime  = [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    // 定义好动作
        void (^animation)(void) = ^void(void) {
                self.view.frame = CGRectMake(0, 0, self.view.frame.size.width,  self.view.frame.size.height);
    };
    
    if (animationTime > 0) {
                [UIView animateWithDuration:animationTime animations:animation];
            } else {
                    animation();
                }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view from its nib.
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];

    _topTipView = [[UIView alloc] init];
    _topTipView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10" alpha:0.1];
    [self.view addSubview:self.topTipView];
    [self.topTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(0);
        make.top.equalTo(self.view).offset([UIWindow ka_uikit_navigationHeight]);
//        make.height.mas_equalTo(300);
    }];
    
    self.tipIcon = [[UIImageView alloc] init];
    self.tipIcon.image = [UIImage ka_iconWithName:JDIF_ICON_INFO imageSize:CGSizeMake(12, 12) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"]];
    [self.topTipView addSubview:self.tipIcon];
    [self.tipIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.topTipView).offset(12);
        make.height.width.mas_equalTo(16);
        make.top.equalTo(self.topTipView).offset(5);
    }];
    
    self.topTipsLabel = [[UILabel alloc] init];
//    self.topTipLabel.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    self.topTipsLabel.numberOfLines = 0;
    self.topTipsLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
    self.topTipsLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.topTipsLabel.textAlignment = NSTextAlignmentLeft;
    self.topTipsLabel.text = [NSString stringWithFormat:@"1.%@\n2.%@",OrderDetailL(@"isv_order_detail_modify_tip"),OrderDetailL(@"isv_order_detail_modify_tip_bottom")];
    [self.topTipView addSubview:self.topTipsLabel];
    [self.topTipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topTipView).mas_offset(5);
        make.leading.equalTo(self.tipIcon.mas_trailing).offset(5);
        make.trailing.equalTo(self.topTipView).offset(-15);
        make.bottom.equalTo(self.topTipView).mas_offset(-5);
    }];
    
    self.lsitTitleView = [[UIView alloc] init];
    self.lsitTitleView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self.view addSubview:self.lsitTitleView];
    [self.lsitTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.topTipView.mas_bottom).mas_offset(8);
        make.leading.equalTo(self.view).offset(0);
        make.trailing.equalTo(self.view).offset(0);
        make.height.mas_equalTo(18+20+18);
    }];
    self.lsitTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    self.lsitTitleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.lsitTitleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium];
    self.lsitTitleLabel.textAlignment = NSTextAlignmentLeft;
    self.lsitTitleLabel.text = OrderDetailL(@"isv_order_order_detail_modify_address_new_title");
    [self.view addSubview:self.lsitTitleLabel];
    [self.lsitTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.lsitTitleView);
        make.leading.equalTo(self.lsitTitleView).offset(18);
        make.trailing.equalTo(self.lsitTitleView).offset(-18);
    }];
    
    self.topTipLabel.text = OrderDetailL(@"isv_order_detail_modify_tip");
    self.topTipLabel.hidden = YES;
    self.customerNameLabel.text = OrderDetailL(@"isv_address_detail_name_label");
    self.customerNameTF.textAlignment = NSTextAlignmentLeft;
    self.mobileLabel.text = OrderDetailL(@"isv_address_detail_phone_label");
    self.mobileTF.textAlignment = NSTextAlignmentLeft;
    self.areLabel.text = OrderDetailL(@"isv_address_detail_location_label");
    self.areaTF.textAlignment = NSTextAlignmentLeft;
    self.detailAddressLabel.text = OrderDetailL(@"isv_address_detail_specific_label");
    self.detailAddressTF.textAlignment = NSTextAlignmentLeft;
    self.bottomTipLabel.text = OrderDetailL(@"isv_order_detail_modify_tip_bottom");
    self.bottomTipLabel.hidden = YES;
    
    self.customerNameTF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.mobileTF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.areaTF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.detailAddressTF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.customerNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.mobileLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.areLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.detailAddressLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.customerNameTF.delegate = self;
    self.mobileTF.delegate = self;
    self.areaTF.delegate = self;
    self.detailAddressTF.delegate = self;
    
    /**RTL 适配需要先配置 textAlignment，再设置attributedPlaceholder的段落 */
    //** 此处设置textAlignment属性原因：xib情况下，光标不会移到正确的位置，需要手动设置一次，光标才会展示到正确的位置*/
    self.customerNameTF.textAlignment = NSTextAlignmentLeft;
    self.mobileTF.textAlignment = NSTextAlignmentLeft;
    self.areaTF.textAlignment = NSTextAlignmentLeft;
    self.detailAddressTF.textAlignment = NSTextAlignmentLeft;
    
//    BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    /*富文本占位符的NSMutableParagraphStyle属性，如果非RTL的情况下为靠左的情况下，可以不用设置其。alignment属性。如果非RTL的情况下为靠右的情况下，需要手动设置alignment属性，且对齐属性需要根据isRTL设置例如：style.aligment = isRTL ？ NSTextAlignmentRight : NSTextAlignmentLeft*/
    NSDictionary *attrs = @{NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:(UIFontWeightMedium)],
                            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
                            NSParagraphStyleAttributeName:[[NSMutableParagraphStyle alloc] init]
    };
    self.customerNameTF.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:OrderDetailL(@"address_detail_name_hint") attributes:attrs];
    self.mobileTF.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:OrderDetailL(@"address_detail_phone_hint") attributes:attrs];
    self.areaTF.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:OrderDetailL(@"address_detail_region_hint") attributes:attrs];
    self.detailAddressTF.attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:OrderDetailL(@"address_detail_specific_hint") attributes:attrs];
    
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    CGFloat w = [UIScreen mainScreen].bounds.size.width - 18.f*2;
    CGFloat h = 40.f;
    self.confirmButton.frame = CGRectMake(18, [UIScreen mainScreen].bounds.size.height-40-[UIWindow ka_uikit_safeAreaInsets].bottom, w, h);
    [self.view addSubview:self.confirmButton];
    [self.confirmButton setTitle:OrderDetailL(@"isv_order_comfirm_btn_text") forState:UIControlStateNormal];
    [self.confirmButton setTitle:OrderDetailL(@"isv_order_comfirm_btn_text") forState:UIControlStateHighlighted];
    [self.confirmButton renderB3WithCornerRadius:20.f];
    [self.confirmButton addTarget:self action:@selector(confirmBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
//    self.containerView.frame = CGRectMake(self.containerView.frame.origin.x
//                                          , self.containerView.frame.origin.y, w, 240.f);
    [self.containerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.lsitTitleView.mas_bottom).mas_offset(0);
        make.leading.equalTo(self.view).offset(0);
        make.trailing.equalTo(self.view).offset(0);
        make.height.mas_offset(294+18);
    }];
//    self.containerView.layer.cornerRadius = 12.f;
    self.containerView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.navigationBarOffset = [UIWindow ka_uikit_navigationHeight];
    
    //自定义导航
    KANavigationBar *navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:navigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    navigationBar
    .decorator
    .backgroundColor() //背景色
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
    }) //返回按钮
    .title(OrderDetailL(@"isv_order_edit_receive"), NSTextAlignmentCenter) //标题
    .render();
    
    @weakify(self)
    [[self fetchFillSign]  subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        [self limitAddress];
    }];
    self.mobileTF.delegate = self;
    
    self.labelShadow.textColor = self.areaTF.textColor;
    self.labelShadow.backgroundColor =self.labelShadow.superview.superview.backgroundColor;
//    self.labelShadow.backgroundColor = [UIColor clearColor];
    self.labelShadow.font = self.areaTF.font;
    
    UIImage* arr = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_BIG
                                  imageSize:CGSizeMake(16, 16)
                                      color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    [self.btnArr setImage:arr forState:UIControlStateNormal];
    self.limitAddrs = 3;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(textFieldDidChanged:) name:UITextFieldTextDidChangeNotification object:nil];
}

// 地址选择限制
-(void)limitAddress{
    if(self.storehouseId && self.venderId && self.province && self.city && self.country &&self.town){
        NSDictionary* param =@{
            @"venderId":@([self.venderId intValue])?:@"",
            @"warehouseId":@([self.storehouseId intValue])?:@"",
            @"province":self.province?:@"",
            @"city":self.city?:@"",
            @"country":self.country?:@"",
            @"town":self.town?:@""
        };
        
        
        
        [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm
                                         paramterType:JDISVColorGateParameterTypeOverseas
                                                 path:@"stock_query_stock_coverage" function:@"" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            NSNumber *success = [responseObject objectForKey:@"success"];
            if (!error && success.boolValue == YES) {
                NSDictionary* dataDic = responseObject[@"data"];
                NSNumber* limit = dataDic[@"canChangeAddressLevel"];
                self.limitAddrs = limit.intValue;
            } else {
                
            }
        }];
    }
}

// 获取填充数据
-(RACSignal*)fetchFillSign{
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSDictionary *param = @{@"orderId":self.orderId ?: @""};
        [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"queryEditableOrderInfo" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            NSNumber *success = [responseObject objectForKey:@"success"];
            if (!error && success.boolValue == YES) {
                NSDictionary *value = responseObject[@"value"];
                if ([value isKindOfClass:NSDictionary.class]) {
                    @strongify(self)
                    self.customerName = value[@"editBaseOrderDTO"][@"userInfo"][@"customerName"];
                    self.mobile = value[@"editBaseOrderDTO"][@"userInfo"][@"mobile"];
                    self.detailAddress = value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"detailAddress"];
                    
                    self.provinceName = value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"privinceName"];
                    self.cityName = value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"cityName"];
                    self.countryName = value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"countyName"];
                    self.townName = value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"townName"];
                    self.area = [NSString stringWithFormat:@"%@ %@ %@ %@", self.provinceName, self.cityName, self.countryName, self.townName];
                    
                    
                    self.province= value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"provinceId"];
                    self.city= value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"cityId"];
                    self.country= value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"countyId"];
                    self.town= value[@"editBaseOrderDTO"][@"cascAddressDTO"][@"townId"];
                    self.customerNameTF.text = self.customerName;
                    self.mobileTF.text = self.mobile;
                    self.areaTF.text = self.area;
                    self.labelShadow.text = self.area;
                    self.detailAddressTF.text = self.detailAddress;
                    [subscriber sendNext:nil];
                }else{
                    [subscriber sendError:nil];
                }
            } else {
                [subscriber sendError:nil];
            }
        }];
        return nil;
    }];
}

- (void)confirmBtnAction {
    @weakify(self)
    if (self.self.customerNameTF.text.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"isv_address_detail_name_hint")];
        return;
    }
    
    NSMutableCharacterSet *characterSet = [NSMutableCharacterSet alphanumericCharacterSet];
    [characterSet addCharactersInRange:NSMakeRange(19968, (40869-19968))];
    NSRange nameRange = [self.self.customerNameTF.text rangeOfCharacterFromSet:[characterSet invertedSet]];
    BOOL invalidateName = nameRange.length > 0;
    if (self.customerNameTF.text.length > 50 || invalidateName) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"isv_address_detail_phone_illegal")];
        return;
    }
    
    if (self.mobileTF.text.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"isv_address_detail_phone_empty")];
        return;
    }
    
    if(![self.mobileTF.text containsString:@"**"]){
        NSRange phoneRange = [self.mobileTF.text rangeOfCharacterFromSet:[[NSCharacterSet characterSetWithCharactersInString:@"0123456789"] invertedSet]];
        BOOL invalidatePhone = phoneRange.length > 0;
        if (self.mobileTF.text.length != 9 || invalidatePhone) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"isv_order_phone_tip")];
            return;
        }
    }
    
    if (self.detailAddressTF.text.length == 0) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"isv_order_address_placeholder")];
        return;
    }
    
    if (self.detailAddressTF.text.length > 300) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"isv_address_detail_specific_illegal")];
        return;
    }
    
    [[self updateOrderInfo] subscribeError:^(NSError * _Nullable error) {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderDetailL(@"ka_order_submit_edit_error")];
    } completed:^{
        @strongify(self)
        if(self.modiySuccess){
            self.modiySuccess();
        }
       
        
        {
            //w3-单按钮
            [KAAlert alert].config
            .renderW3(OrderDetailL(@"ka_order_submit_edit_success"))
            .ka_addAction(OrderDetailL(@"ka_order_dialog_btn_confirm"), ^(UIButton * _Nonnull button) {
                //按钮样式有客户端自己确定
                [button renderB3];
            }, ^{
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.f * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    @strongify(self)
                    [self.navigationController popViewControllerAnimated:YES];
                });
            })
            .alertShow();
        }
        
    }];
}

//失败
//{"code":0,"resultCode":0,"success":true,"traceId":"442.37.16714165158855367","value":{"editCommitStateDTO":{"commitBizMsg":"修改申请提交失败，请重试","commitBizStatus":false}}}
-(RACSignal*)updateOrderInfo{
    NSDictionary* param = @{
        @"editOpType":@"2",
        @"orderId":self.orderId?:@"",
        @"isPrePay":self.orderStatus.intValue == 1?@"1":@"0",
        @"mark":@"1",
        @"businessOrderType":@"1",
        @"businessTag":@"cn_ybxt_b2c",
        @"editOrderOpParam":@{
            @"isRiskInfoEncrypt":@"0",
            @"userInfoDTO":@{
                @"customerName":self.customerNameTF.text?:@"",
                @"mobile":self.mobileTF.text?:@"",
            },
            @"addressInfoDTO":@{
                @"privinceId":self.province?:@"",
                @"cityId":self.city?:@"",
                @"countyId":self.country?:@"",
                @"townId":self.town?:@"",
                @"provinceName":self.provinceName?:@"",
                @"cityName":self.cityName?:@"",
                @"countyName":self.countryName?:@"",
                @"townName":self.townName?:@"",
                @"detailAddress":self.detailAddressTF.text?:@""
            }
        }
    };
    
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"orderEditCommit" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, NSDictionary*  _Nullable responseObject, NSError * _Nullable error) {
            NSNumber *success = [responseObject objectForKey:@"success"];
            if (!error && success.boolValue == YES) {
                NSDictionary* value = [responseObject jdcd_getDicElementForKey:@"value"];
                if(value[@"editCommitStateDTO"]){
                    NSDictionary* editDTO = [value jdcd_getDicElementForKey:@"editCommitStateDTO"];
                    NSNumber* status = [editDTO jdcd_getNumberElementForKey:@"commitBizStatus"];
                    if(status.boolValue){
                        [subscriber sendCompleted];
                    }else{
                        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:editDTO[@"commitBizMsg"]?:OrderDetailL(@"isv_order_comfirm_failed")];
                    }
                }else{
                    [subscriber sendError:nil];
                }
            }else{
                [subscriber sendError:nil];
            }
        }];
        return nil;
    }];
}

// 点击地址
- (IBAction)locBtnAction:(id)sender {
    
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
    arg[@"showUserAddress"] = @0;
    arg[@"limitLevel"] = @(self.limitAddrs);
    JDTAddressItemModel *addr = [JDTAddressItemModel defaultAddress];
    addr.provinceId = self.province.stringValue;
    addr.cityId = self.city.stringValue;
    addr.districtId = self.country.stringValue;
    addr.townId = self.town.stringValue;
    arg[@"address"] = addr;
    UIViewController *cascaderController  = [JDRouter openURL:@"router://JDISVAddressModule/cascaderAddressViewController" arg:arg error:nil completion:^(JDTAddressItemModel *object) {
        // 选择完成
        self.province = object.provinceId.jdcd_toNumber;
        self.city = object.cityId.jdcd_toNumber;
        self.country = object.districtId.jdcd_toNumber;
        self.town  = object.townId.jdcd_toNumber;
        self.provinceName =object.provinceName;
        self.cityName =object.cityName;
        self.countryName =object.districtName;
        self.townName =object.townName;
        NSString *address = [NSString stringWithFormat:@"%@ %@ %@ %@", object.provinceName, object.cityName, object.districtName, object.townName];
        self.areaTF.text = address;
        self.labelShadow.text = address;
    }];
    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.defultHeader.titleLabel.text = OrderDetailL(@"isv_order_address_title");
    cascaderController.transitioningDelegate = delegate;
    if (cascaderController) {
        [self presentViewController:cascaderController animated:YES completion:nil];
    }
}

#pragma mark - UITextField代理
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    if(textField == self.detailAddressTF){
        self.keyboardOffset = 50;
    } else {
        self.keyboardOffset = 0;
    }
    return YES;
}

- (void)textFieldDidChanged:(NSNotification *)notification {
    
    UITextField *textField = (UITextField *)notification.object;
    if(textField == self.customerNameTF ||
       textField == self.mobileTF ||
       textField == self.detailAddressTF){

        // 需要限制的长度
        NSUInteger maxLength = 1000;

        if(textField == self.customerNameTF){
            maxLength = 50;

        }else if(textField == self.mobileTF){
            maxLength = 9;

        }else if(textField == self.detailAddressTF){
            maxLength = 300;
        }
        NSString *contentText = textField.text;
        UITextRange *selectedRange = [textField markedTextRange];

        NSInteger markedTextLength = [textField offsetFromPosition:selectedRange.start toPosition:selectedRange.end];

        if (markedTextLength == 0) {

            if (contentText.length > maxLength) {

                NSRange rangeRange = [contentText rangeOfComposedCharacterSequencesForRange:NSMakeRange(0, maxLength)];
                textField.text = [contentText substringWithRange:rangeRange];
            }
        }
    }
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    
    if (textField == self.mobileTF) {
        if ([string isEqualToString:@""]) {
            return YES;
        }
        
        if (textField.text.length >= 9) {
            return NO;
        }

        NSString *validRegEx =@"^[0-9]$";
        NSPredicate *regExPredicate =[NSPredicate predicateWithFormat:@"SELF MATCHES %@", validRegEx];
        BOOL myStringMatchesRegEx = [regExPredicate evaluateWithObject:string];
        if (myStringMatchesRegEx) {
            return YES;
        }
        return NO;
    } else if (textField == self.customerNameTF) {
        static NSString *regex = @"[^a-zA-Z\u0621-\u064a\\s_*\\\\-]";
        NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
        if ([predicate evaluateWithObject:string]) {
            NSString* tip =  OrderDetailL(@"isv_order_order_detail_address_name_error");
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:tip];
            return NO;
        }
        if((self.customerNameTF.text.length + string.length) > 50){
            NSString* tip =  OrderDetailL(@"isv_order_order_detail_address_name_error");
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:tip];
            return NO;
        }
    }
    
    if([string isContainsEmoji]) return NO;
    
    return YES;
}

-(void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [self.view endEditing:YES];
}
@end
