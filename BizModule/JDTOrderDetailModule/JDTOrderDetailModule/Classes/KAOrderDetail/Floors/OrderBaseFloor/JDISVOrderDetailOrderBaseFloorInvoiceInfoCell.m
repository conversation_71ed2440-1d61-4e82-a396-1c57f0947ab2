//
//  JDISVOrderDetailOrderBaseFloorInvoiceInfoCell.m
//  JDISVOrderDetailSDKModule
//
//  Created by ext.chenhongyu12 on 2023/6/12.
//

#import "JDISVOrderDetailOrderBaseFloorInvoiceInfoCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVOrderDetailOrderBaseFloorBaseInfoCellViewModel.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVOrderDetailOrderBaseFloorBaseInfoCellViewModel.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import "JDISVOrderDetailOrderBaseFloorActionParam.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@interface JDISVOrderDetailOrderBaseFloorInvoiceInfoCell ()

//@property (strong, nonatomic) UILabel *infoTitleLabel;

@property (strong,nonatomic) UILabel *infoTitleLabel;
@property (strong,nonatomic) UIButton *viewInvoice;
@property (strong,nonatomic) UIButton *sendToEmail;
@property (strong,nonatomic) UIButton *OrderSummary;
@property (strong,nonatomic) UILabel * noInvoice;
@property (nonatomic, strong) JDISVOrderDetailOrderBaseFloorBaseInfoCellViewModel *viewModel;

@end

@implementation JDISVOrderDetailOrderBaseFloorInvoiceInfoCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.contentView.backgroundColor = [UIColor clearColor];
        self.backgroundColor = [UIColor clearColor];
        
        
        [self.contentView addSubview:self.infoTitleLabel];
        [self.infoTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView).mas_offset(6);
            make.width.mas_offset(90);
            make.leading.mas_equalTo(self.contentView).mas_offset(18);
        }];
        
//        [self.contentView addSubview:self.OrderSummary];
//        [self.contentView addSubview:self.viewInvoice];
//        [self.contentView addSubview:self.sendToEmail];
        
        
        
        
//        [self.OrderSummary mas_makeConstraints:^(MASConstraintMaker *make) {
//            //            make.top.mas_equalTo(self.infoTitleLabel);
//            make.centerY.mas_equalTo(self.infoTitleLabel);
//            make.leading.mas_equalTo(self.infoTitleLabel.mas_trailing).mas_offset(12);
//        }];
//        
//        [self.viewInvoice mas_makeConstraints:^(MASConstraintMaker *make) {
////            make.top.mas_equalTo(self.infoTitleLabel);
//            make.centerY.mas_equalTo(self.infoTitleLabel);
//            make.leading.mas_equalTo(self.OrderSummary.mas_trailing).mas_offset(12);
//        }];
//        
//        [self.sendToEmail mas_makeConstraints:^(MASConstraintMaker *make) {
////            make.top.mas_equalTo(self.infoTitleLabel);
//            make.centerY.mas_equalTo(self.infoTitleLabel);
//            make.leading.mas_equalTo(self.viewInvoice.mas_trailing).mas_offset(15);
//        }];
    }
    return self;
}

#pragma mark - <getter>

- (UILabel *)infoTitleLabel{
    if (!_infoTitleLabel){
        _infoTitleLabel = [[UILabel alloc] init];
        _infoTitleLabel.numberOfLines = 0;
    }
    return _infoTitleLabel;
}

- (UIButton *)viewInvoice{
    if (!_viewInvoice){
        _viewInvoice = [[UIButton alloc] init];
        [self configBtn:_viewInvoice title:OrderDetailL(@"isv_order_detail_view_invoice")];
        [_viewInvoice addTarget:self action:@selector(showInvoicePDF) forControlEvents:UIControlEventTouchUpInside];
    }
    return _viewInvoice;
}

- (UIButton *)sendToEmail{
    if (!_sendToEmail){
        _sendToEmail = [[UIButton alloc] init];
        [self configBtn:_sendToEmail title:OrderDetailL(@"isv_order_detail_send_to_email")];
        [_sendToEmail addTarget:self action:@selector(showInputEmailAlert) forControlEvents:UIControlEventTouchUpInside];
    }
    return _sendToEmail;
}

- (UIButton *)OrderSummary{
    if (!_OrderSummary){
        _OrderSummary = [[UIButton alloc] init];
        [self configBtn:_OrderSummary title:OrderDetailL(@"isv_order_detail_OrderSummary")];
        [_OrderSummary addTarget:self action:@selector(showSummary) forControlEvents:UIControlEventTouchUpInside];
    }
    return _OrderSummary;
}
-(UILabel*)noInvoice{
    if(!_noInvoice){
        _noInvoice = [[UILabel alloc] initWithFrame:CGRectZero];
        _noInvoice.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _noInvoice.text = OrderDetailL(@"isv_order_detail_no_invoice");
        _noInvoice.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    }
    return _noInvoice;
}


- (void)configBtn:(UIButton *)button title:(NSString *)title {
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitle:title forState:UIControlStateHighlighted];
    [button setTitle:title forState:UIControlStateDisabled];
    button.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [button setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C14"] forState:UIControlStateNormal];
    [button setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C14"] forState:UIControlStateHighlighted];
    [button setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C14"] forState:UIControlStateDisabled];
}


- (void)updateCellWithViewModel:(__kindof JDISVOrderDetailOrderBaseFloorBaseInfoCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVOrderDetailOrderBaseFloorBaseInfoCellViewModel.class]) {
        CGFloat offset = -6;
        self.viewModel = viewModel;
        self.infoTitleLabel.attributedText = _viewModel.titleAttributedText;
        
        // mock
        // _viewModel.statisticsInfoModel.vatQualification = NO;
        // _viewModel.statisticsInfoModel.orderSummarayUrl = @"";
        
        // 1.订单对应商家无VAT资质：（下载发票和发送邮件）弹出提示：卖家暂时无法提供发票！
//        if(!_viewModel.statisticsInfoModel.vatQualification){
//            [self.contentView addSubview:self.viewInvoice];
//            [self.viewInvoice mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.top.mas_equalTo(self.infoTitleLabel).offset(offset);
//                make.leading.mas_equalTo(self.infoTitleLabel.mas_trailing).mas_offset(12);
//            }];
//            return;
//        }
        
        // 订单对应商家有VAT资质，orderSummarayUrl有值
        if(self.viewModel.statisticsInfoModel.orderSummarayUrl.length) {
            
            [self.contentView addSubview:self.viewInvoice];
            [self.contentView addSubview:self.sendToEmail];
            [self.contentView addSubview:self.OrderSummary];
            self.viewInvoice.titleLabel.numberOfLines = 0;
            self.sendToEmail.titleLabel.numberOfLines = 0;
            self.OrderSummary.titleLabel.numberOfLines = 0;
            self.viewInvoice.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
            self.sendToEmail.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
            self.OrderSummary.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
            CGFloat width = (UIScreen.mainScreen.bounds.size.width-36- 36-90)/3;
            [self.OrderSummary mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.infoTitleLabel).offset(offset);
                make.leading.mas_equalTo(self.infoTitleLabel.mas_trailing).mas_offset(12);
                make.width.mas_lessThanOrEqualTo(width);
            }];
            [self.viewInvoice mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.infoTitleLabel).offset(offset);
                make.leading.mas_equalTo(self.OrderSummary.mas_trailing).mas_offset(12);
                make.width.mas_equalTo(width);
            }];
            [self.sendToEmail mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.infoTitleLabel).offset(offset);
                make.leading.mas_equalTo(self.viewInvoice.mas_trailing).mas_offset(12);
                make.width.mas_equalTo(width);
            }];
        } 
        // 订单对应商家有VAT资质，orderSummarayUrl没有值
        else {
            [self.contentView addSubview:self.viewInvoice];
            [self.viewInvoice mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.infoTitleLabel).offset(offset);
                make.leading.mas_equalTo(self.infoTitleLabel.mas_trailing).mas_offset(12);
            }];
            
            [self.contentView addSubview:self.sendToEmail];
            [self.sendToEmail mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.infoTitleLabel).offset(offset);
                make.leading.mas_equalTo(self.viewInvoice.mas_trailing).mas_offset(12);
            }];
        }
    }
}

#pragma mark - <inputEmail>
- (void)showInputEmailAlert{
    
    if (![self checkInvoiceStatus]) { return; }
    
    JDISVOrderDetailOrderBaseFloorInvoiceInfoEmailView *view = [[JDISVOrderDetailOrderBaseFloorInvoiceInfoEmailView alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width - 24.f, 65)];
    JDWeakSelf
    [KAAlert alert].config.jdcd_shouldActionClickClose(^BOOL(NSInteger index) {
        return index == 0 ? YES : [view jdcd_isEmail];
    }).renderW4Costom(OrderDetailL(@"isv_order_detail_email_title"), view).addLineAction(OrderDetailL(@"ka_order_dialog_btn_cancel"), ^{
        NSLog(@"Cancel");
    }).addFillAction(OrderDetailL(@"ka_order_dialog_btn_confirm"), ^{
        JDStrongSelf
        NSString *email = [view inputValue];
        if ([email jdcd_validateString]){
            [self sendInvoicePdfToEmail:email];
        }
    }).alertShow();
}
-(void)showSummary{
    NSString *url = @"";
    if ([self.viewModel.statisticsInfoModel.orderSummarayUrl jdcd_validateString]){
        url = self.viewModel.statisticsInfoModel.orderSummarayUrl;;
    }else{
        return;
    }
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:url] options:@{} completionHandler:nil];
}

- (void)showInvoicePDF{
    
    // 没有发票资质: 卖家暂时无法提供发票！
    if (![self checkInvoiceStatus]) { return; }
    
    // 订单对应商家有VAT资质且完成开票：跳转：跳转到ERP开票链接或发送邮件(原功能)
    NSString *url = self.viewModel.statisticsInfoModel.taxInvoiceUrl;
    //打开外部浏览器
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:url] options:@{} completionHandler:nil];
//    使用内嵌WebView预览
//    [self.delegate sendNext:[JDISVOrderDetailOrderBaseFloorActionParam initWithIdentifier:@"JDISVOrderDetailOrderBaseFloorInvoiceInfoCell" parames:@{@"url":url}]];
    
}

- (void)sendInvoicePdfToEmail:(NSString *)email{
    NSDictionary *param = @{@"orderId":self.viewModel.orderId,@"to":email};
    [PlatformService showLoadingInView:[PlatformService getTopViewController].view];
    [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"user.order.orderEmailSender" function:@"" version:@"" parameters:param complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        [PlatformService dismissInView:[PlatformService getTopViewController].view];
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage showTime:3.0 message:OrderDetailL(@"isv_order_detail_email_failed")];
        } else {
            if ([responseObject jdcd_getNumberElementForKey:@"success"].boolValue == YES){
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage showTime:3.0 message:OrderDetailL(@"isv_order_detail_email_success")];
            }else{
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage showTime:3.0 message:OrderDetailL(@"isv_order_detail_email_failed")];
            }
        }
    }];
}

- (BOOL)checkInvoiceStatus {
    
    // 没有发票资质: 卖家暂时无法提供发票！
    if (!_viewModel.statisticsInfoModel.vatQualification) {
        [KAToast alert].config.renderW1(OrderDetailL(@"isv_order_detail_invoice_no_invoice")).jdcd_show();
        return NO;
    }
    
    // 订单对应商家有VAT资质且未完成开票：（下载发票和发送邮件）弹出提示：卖家正在开发票，请稍后查看！
    if(_viewModel.statisticsInfoModel.vatQualification && ![self.viewModel.statisticsInfoModel.taxInvoiceUrl jdcd_validateString]){
        [KAToast alert].config.renderW1(OrderDetailL(@"isv_order_detail_invoice_check_later")).jdcd_show();
        return NO;
    }
    return YES;
}


@end

#pragma mark -JDISVOrderDetailOrderBaseFloorInvoiceInfoEmailView

@interface JDISVOrderDetailOrderBaseFloorInvoiceInfoEmailView ()<UITextFieldDelegate>
@property (strong,nonatomic) UITextField *emailTextField;
@property (strong,nonatomic) UILabel *errorlabel;
@end

@implementation JDISVOrderDetailOrderBaseFloorInvoiceInfoEmailView

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self){
        [self addSubview:self.emailTextField];
        [self addSubview:self.errorlabel];
        
        
        [self.emailTextField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self);
            make.leading.mas_equalTo(self);
            make.trailing.mas_equalTo(self);
            make.height.mas_offset(44);
        }];
        
        [self.errorlabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.emailTextField.mas_bottom).mas_offset(4);
            make.leading.trailing.mas_equalTo(self);
            make.height.mas_offset(17);
        }];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(textFieldDidChanged:) name:UITextFieldTextDidChangeNotification object:nil];
    }
    return self;
}

- (UITextField *)emailTextField{
    if (!_emailTextField){
        _emailTextField = [[UITextField alloc] init];
        _emailTextField.delegate = self;
        _emailTextField.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        _emailTextField.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
        _emailTextField.clipsToBounds = YES;
        _emailTextField.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _emailTextField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 12, 44)];
        _emailTextField.leftViewMode = UITextFieldViewModeAlways;
        _emailTextField.rightView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 12, 44)];
        _emailTextField.rightViewMode = UITextFieldViewModeAlways;
        _emailTextField.textAlignment = NSTextAlignmentLeft;
        NSMutableAttributedString *attributedPlaceholder = [[NSMutableAttributedString alloc] initWithString:OrderDetailL(@"isv_order_detail_email_placeholder")];
        [attributedPlaceholder addAttributes:@{NSParagraphStyleAttributeName:[NSMutableParagraphStyle new],
                                               NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
                                               NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
                                             } range:NSMakeRange(0, attributedPlaceholder.length)];
        _emailTextField.attributedPlaceholder = attributedPlaceholder;
    }
    return _emailTextField;
}

- (UILabel *)errorlabel{
    if (!_errorlabel){
        _errorlabel = [[UILabel alloc] init];
        _errorlabel.text = OrderDetailL(@"isv_order_detail_email_tip");
        _errorlabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"];
        _errorlabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        _errorlabel.clipsToBounds = YES;
        _errorlabel.hidden = YES;
    }
    return _errorlabel;
}

#pragma mark - <UITextFieldDelegate>

- (void)textFieldDidChanged:(NSNotification *)notification{
    UITextField *textField = (UITextField *)notification.object;
    // 需要限制的长度
    NSUInteger maxLength = 50;
    NSString *contentText = textField.text;
    UITextRange *selectedRange = [textField markedTextRange];
    
    NSInteger markedTextLength = [textField offsetFromPosition:selectedRange.start toPosition:selectedRange.end];
    
    if (markedTextLength == 0) {
        
        if (contentText.length > maxLength) {
            
            NSRange rangeRange = [contentText rangeOfComposedCharacterSequencesForRange:NSMakeRange(0, maxLength)];
            textField.text = [contentText substringWithRange:rangeRange];
        }
    }
    
    self.errorlabel.hidden = [textField.text jdcd_isEmail];
//    CGRect frame = self.frame;
//    if ([textField.text jdcd_isEmail]){
//        frame.size.height = 44;
//    }else{
//        frame.size.height = 65;
//    }
//    self.frame = frame;
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
    if([string isContainsEmoji]) return NO;
    return YES;
}

- (BOOL)jdcd_isEmail{
    return [self.emailTextField.text jdcd_isEmail];
}

- (NSString *)inputValue{
    return self.emailTextField.text ? self.emailTextField.text : @"";
}

- (void)dealloc{
    NSLog(@"JDISVOrderDetailOrderBaseFloorInvoiceInfoEmailView dealloc");
    
}

@end
