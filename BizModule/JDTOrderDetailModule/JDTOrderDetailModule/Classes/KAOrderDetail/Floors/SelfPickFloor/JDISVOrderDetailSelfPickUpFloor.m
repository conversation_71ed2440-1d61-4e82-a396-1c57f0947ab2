//
//  JDISVOrderDetailSelfPickUpFloor.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/1.
//

#import "JDISVOrderDetailSelfPickUpFloor.h"

#import <WebKit/WKWebView.h>
#import <MapKit/MapKit.h>
#import <CoreLocation/CoreLocation.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import <JDISVFloorRenderModule/JDCDISVAction.h>

#import "JDISVOrderDetailSelfPickUpFloorModule.h"

JDCDISVActionType const kJDISVOrderDetailSelfPickUpFloorCallPhoneAction = @"JDISVOrderDetailSelfPickUpFloorCallPhoneAction";
JDCDISVActionType const kJDISVOrderDetailSelfPickUpFloorOpenSiteMapAction = @"JDISVOrderDetailSelfPickUpFloorOpenSiteMapAction";

@interface JDISVOrderDetailSelfPickUpFloor ()<JDCDISVActionTransferProtocol>
@property (weak, nonatomic) IBOutlet UILabel *selfPickupTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *selfPickupTitleLabelWidth;
@property (weak, nonatomic) IBOutlet UILabel *codeLabel;
@property (weak, nonatomic) IBOutlet UILabel *validityLabel;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *sepratorLineTop;
@property (weak, nonatomic) IBOutlet UIImageView *topSepratorLine;
@property (weak, nonatomic) IBOutlet UILabel *selfPickupSiteLabel;
@property (weak, nonatomic) IBOutlet UILabel *businessStatusLabel;//停业、歇业
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeLabel;
@property (weak, nonatomic) IBOutlet UILabel *siteAddressLabel;
@property (weak, nonatomic) IBOutlet UIButton *locationButton;
@property (weak, nonatomic) IBOutlet UIButton *callPhoneButton;
@property (weak, nonatomic) IBOutlet UIImageView *sepratorLine;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *siteAdressLabelHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *selfPickupSiteLabelHeight;

@property (nonatomic, strong) JDISVOrderDetailSelfPickUpFloorModule *viewModel;
@end

@implementation JDISVOrderDetailSelfPickUpFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.sepratorLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.topSepratorLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.businessStatusLabel.textColor = [UIColor whiteColor];
    self.businessStatusLabel.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.businessStatusLabel.layer.cornerRadius = 3;
    self.businessStatusLabel.layer.masksToBounds = YES;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
//    if ([_viewModel.titleAttributedString.string jdcd_validateString]) {
//        self.selfPickupTitleLabel.attributedText = _viewModel.titleAttributedString;
//        self.selfPickupTitleLabel.hidden = NO;
//    } else {
//        self.selfPickupTitleLabel.hidden = YES;
//    }
//
//    self.selfPickupTitleLabelWidth.constant = _viewModel.titleLabelWidth;
//    if ([_viewModel.codeAttributedString.string jdcd_validateString]) {
//        self.codeLabel.attributedText = _viewModel.codeAttributedString;
//        self.codeLabel.hidden = NO;
//    } else {
//        self.codeLabel.hidden = YES;
//    }
//
//    if (_viewModel.validateTimeAttributedString) {
//        _validityLabel.hidden = NO;
//        _validityLabel.attributedText = _viewModel.validateTimeAttributedString;
//    } else {
//        _validityLabel.hidden = YES;
//    }
//    if (_viewModel.floorHeight!=0) {
//        self.sepratorLineTop.constant = _viewModel.sepratorLineTop;
//        self.topSepratorLine.hidden = NO;
//    } else {
//        self.topSepratorLine.hidden = YES;
//    }
    
    //
    if (_viewModel.openStatus) {
        if (_viewModel.openStatus.integerValue == 1) {//营业
            self.businessStatusLabel.hidden = YES;
        } else if (_viewModel.openStatus.integerValue == 2) {//歇业
            self.businessStatusLabel.hidden = NO;
            self.businessStatusLabel.text = OrderDetailL(@"isv_order_stop_doing_business");
        } else if (_viewModel.openStatus.integerValue == 6) {//停业
            self.businessStatusLabel.hidden = NO;
            self.businessStatusLabel.text = OrderDetailL(@"isv_order_stop_outdoing_business");
        } else {
            self.businessStatusLabel.hidden = YES;
        }
    }
    if ([_viewModel.selfPickupSiteNameAttributedString.string jdcd_validateString]) {
        self.selfPickupSiteLabel.attributedText = _viewModel.selfPickupSiteNameAttributedString;
        self.selfPickupSiteLabel.hidden = NO;
        self.selfPickupSiteLabelHeight.constant = _viewModel.siteNameLabelHeight;
    } else {
        self.selfPickupSiteLabel.hidden = YES;
        self.selfPickupSiteLabelHeight.constant = 0;
    }
    if (_viewModel.bussinessTimeTitleAttributedString) {
        self.bussinessTimeTitleLabel.attributedText = _viewModel.bussinessTimeTitleAttributedString;
        self.bussinessTimeTitleLabel.hidden = NO;
    } else {
        self.bussinessTimeTitleLabel.hidden = YES;
    }
    
    if (_viewModel.businessTimeAttributedString) {
        self.bussinessTimeLabel.attributedText = _viewModel.businessTimeAttributedString;
        self.bussinessTimeLabel.hidden = NO;
    } else {
        self.bussinessTimeLabel.hidden = YES;
    }
    if (_viewModel.addressAttributedString) {
        self.siteAddressLabel.numberOfLines = 0;
        self.siteAddressLabel.attributedText = _viewModel.addressAttributedString;
        self.siteAddressLabel.hidden = NO;
        self.siteAdressLabelHeight.constant = _viewModel.addressLabelHeight;
    } else {
        self.siteAddressLabel.hidden = YES;
        self.siteAdressLabelHeight.constant = 0;
    }
    

    [self.locationButton addTarget:self action:@selector(locationButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [self.callPhoneButton addTarget:self action:@selector(callPhoneButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [self.locationButton setBackgroundImage:[UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(24.f, 24.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] forState:UIControlStateNormal];
    [self.callPhoneButton setBackgroundImage:[UIImage ka_iconWithName:JDIF_ICON_PHONE_SMALL imageSize:CGSizeMake(24.f, 24.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] forState:UIControlStateNormal];
    if (_viewModel.floorHeight != 0) {
        self.callPhoneButton.hidden = NO;
        if (_viewModel.storeLocationFeature) {
            self.locationButton.hidden = NO;
            self.sepratorLine.hidden = NO;
        } else {
            self.locationButton.hidden = YES;
            self.sepratorLine.hidden = YES;
        }
    } else {
        self.locationButton.hidden = YES;
        self.callPhoneButton.hidden = YES;
        self.sepratorLine.hidden = YES;
    }
    
}


- (void)locationButtonClick{
    JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVOrderDetailSelfPickUpFloorOpenSiteMapAction];
    [self isv_sendAction:action];
}

- (void)callPhoneButtonClick{
    JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVOrderDetailSelfPickUpFloorCallPhoneAction];
    [self isv_sendAction:action];
}

#pragma mark - JDCDISVActionTransferProtocol
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDISVOrderDetailSelfPickUpFloorCallPhoneAction]) {
        // 拨打电话
        if ([_viewModel.sitePhone jdcd_validateString]) {
            NSMutableString * str=[[NSMutableString alloc] initWithFormat:@"tel://%@", _viewModel.sitePhone];
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
        }
        
        return YES;
    } else if ([action.actionType isEqualToString:kJDISVOrderDetailSelfPickUpFloorOpenSiteMapAction]) {
        // 打开地图
        // 创建MKPlaceMark对象
        if ([_viewModel.longitude jdcd_validateString] && [_viewModel.latitude jdcd_validateString]) {
            MKPlacemark *sitePmk = [[MKPlacemark alloc] initWithCoordinate:CLLocationCoordinate2DMake([_viewModel.latitude doubleValue], [_viewModel.longitude doubleValue]) addressDictionary:nil];
            NSDictionary *mapLaunchOptions = @{
                MKLaunchOptionsMapTypeKey: @(MKMapTypeStandard),
            };
            MKMapItem *siteMapitem = [[MKMapItem alloc] initWithPlacemark:sitePmk];
            [MKMapItem openMapsWithItems:@[siteMapitem] launchOptions:mapLaunchOptions];
        }
        
        return YES;
    }
    
    return NO;
}
@end
