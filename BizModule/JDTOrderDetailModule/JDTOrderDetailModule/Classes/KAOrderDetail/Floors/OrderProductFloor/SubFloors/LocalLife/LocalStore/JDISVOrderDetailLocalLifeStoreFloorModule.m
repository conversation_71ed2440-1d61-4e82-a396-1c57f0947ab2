//
//  JDISVButtonFloorModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.songjian6 on 2022/7/14.
//

#import "JDISVOrderDetailLocalLifeStoreFloorModule.h"
#import "JDISVOrderDetailLocalLifeStoreFloor.h"
#import "JDISVGroupBuyFloorModel.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
#import <JDISVYYModelModule/YYModel.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

JDISVRegisterFloorModule(KaOrderLocalStoreFloor, JDISVOrderDetailLocalLifeStoreFloorModule)
 
@interface JDISVOrderDetailLocalLifeStoreFloorModule()

@property(assign,nonatomic) float height;

@end


@implementation JDISVOrderDetailLocalLifeStoreFloorModule

- (UIView *)floorView {
    UIView* view = [[JDISVOrderDetailLocalLifeStoreFloor alloc] init];
    return view;
}

- (CGFloat)floorHeight {
    return self.height;
}

- (JDISVFloorType)floorType{
    return JDISVFloorTypeScrollFloor;
    
}

-(void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.height = 0;
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    if ([allFloorData[@"orderType"] integerValue] != ProductTypeLoc) {
        // 非 LOC 订单，不展示
        return;
    }
    ORDERListItemStateModel *stateModel = [ORDERListItemStateModel yy_modelWithDictionary:allFloorData[@"orderState"]];
    if (stateModel.state == ORDERListItemStateTypeWaitPay || stateModel.state == ORDERListItemStateTypeCanceled) {
        // 待支付/已取消，不展示
        return;
    }
    NSArray <ORDERListItemLocStoreHouseModel *> *storeArr = [NSArray yy_modelArrayWithClass:[ORDERListItemLocStoreHouseModel class] json:allFloorData[@"storehouseInfos"]];
    if (storeArr.count > 0) {
        ORDERListItemLocStoreHouseModel *firstStoreModel = storeArr.firstObject;
        self.shop = firstStoreModel;
        NSMutableArray *shopMArr = [NSMutableArray array];
        for (ORDERListItemLocStoreHouseModel *store in storeArr) {
            [shopMArr addObject:store];
        }
        self.shopList = [shopMArr copy];
        [self updateHeight];
    }
}

//-(void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
//    NSDictionary *allFloorData = data[@"data"] ? : @{};
//    if (((NSNumber *)allFloorData[@"others"][@"orderType"]).integerValue != 75) {
//        //非LOC订单，不展示
//        self.height = 0;
//        return;
//    }
//    
//    NSString *orderstatus = allFloorData[@"orderStatusDTO"][@"orderStatusId"] ? : @"";
//    if ([orderstatus isEqualToString:@"1"] || [orderstatus isEqualToString:@"6"]) {
//        //待支付/已取消，不展示门店楼层
//        self.height = 0;
//        return;
//    }
//    
//    NSNumber *isThird = allFloorData[@"isThirdProduct"];
//    if (isThird.boolValue) {
//        //三方商品，不展示门店楼层
//        self.height = 0;
//        return;
//    }
//    
//    NSDictionary *carMiInfoDic = [allFloorData jdcd_getDicElementForKey:@"carMiInfoDTO"];
//    NSString *qrCode = [carMiInfoDic jdcd_getStringElementForKey:@"cardPwd"];
//    
//    if (![qrCode jdcd_validateString]) {
//        //无码，不展示门店楼层
//        self.height = 0;
//        return;
//    }
//    
//    NSDictionary* dataDic = [data jdcd_getDicElementForKey:@"data"];
//    NSArray* storeInfoList = [dataDic jdcd_getArrayElementForKey:@"storeInfoDTOList"];
//    NSMutableArray* shopList = [NSMutableArray array];
//    [storeInfoList enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
//        JDISVOrderDetailLocalLifeStoreFloorModel * model = [JDISVOrderDetailLocalLifeStoreFloorModel yy_modelWithDictionary:obj];
//        if(model){
//            [shopList addObject:model];
//        }
//    }];
//    
//    NSDictionary* storeDic = [dataDic jdcd_getDicElementForKey:@"storeInfoDTO"];
//    self.shopList = [shopList copy];
//    self.shop = [JDISVOrderDetailLocalLifeStoreFloorModel  yy_modelWithDictionary:storeDic];
//    [self updateHeight];
//}

//默认 117
-(void)updateHeight{
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    CGFloat width = UIScreen.mainScreen.bounds.size.width - 2*w3-36;
    UIFont* font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    CGSize size = [self.shop.storehouseName jdcd_getStringSize:font constraintsSize:CGSizeMake(width, 400)];
    if(size.width> 16){
        self.height = 117+size.height-11;
    }else{
        self.height = 117;
    }
}
@end
