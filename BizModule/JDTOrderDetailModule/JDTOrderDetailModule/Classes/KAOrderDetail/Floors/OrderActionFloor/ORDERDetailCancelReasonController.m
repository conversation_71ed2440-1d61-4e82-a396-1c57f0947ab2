//
//  ORDERDetailCancelReasonController.m
//  JDTOrderDetailModule
//
//  Created by lvchenzhu.1 on 2025/6/23.
//

#import "ORDERDetailCancelReasonController.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

// 取消原因数据模型
@interface ORDERCancelReasonModel : NSObject
@property (nonatomic, strong) NSString *title;
@property (nonatomic, assign) BOOL selected;
@end

@implementation ORDERCancelReasonModel
@end

// 取消原因Cell
@interface ORDERCancelReasonCell : UITableViewCell
@property (nonatomic, strong) UIView *radioButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) ORDERCancelReasonModel *model;
- (void)configWithModel:(ORDERCancelReasonModel *)model;
@end

@implementation ORDERCancelReasonCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor whiteColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    // 单选按钮
    self.radioButton = [[UIView alloc] init];
    self.radioButton.layer.cornerRadius = 10;
    self.radioButton.layer.borderWidth = 1;
    self.radioButton.layer.borderColor = [[UIColor lightGrayColor] CGColor];
    self.radioButton.backgroundColor = [UIColor whiteColor];
    [self.contentView addSubview:self.radioButton];

    // 标题标签
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.titleLabel.textColor = [UIColor blackColor];
    [self.contentView addSubview:self.titleLabel];

    // 布局
    [self.radioButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(20);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];

    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.radioButton.mas_right).offset(12);
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).offset(-20);
    }];
}

- (void)configWithModel:(ORDERCancelReasonModel *)model {
    self.model = model;
    self.titleLabel.text = model.title;

    if (model.selected) {
        self.radioButton.backgroundColor = [UIColor orangeColor];
        self.radioButton.layer.borderColor = [[UIColor orangeColor] CGColor];

        // 添加内部白色圆点
        [self.radioButton.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        UIView *innerDot = [[UIView alloc] init];
        innerDot.backgroundColor = [UIColor whiteColor];
        innerDot.layer.cornerRadius = 3;
        [self.radioButton addSubview:innerDot];
        [innerDot mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self.radioButton);
            make.size.mas_equalTo(CGSizeMake(6, 6));
        }];
    } else {
        self.radioButton.backgroundColor = [UIColor whiteColor];
        self.radioButton.layer.borderColor = [[UIColor lightGrayColor] CGColor];
        [self.radioButton.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
}

@end

@interface ORDERDetailCancelReasonController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *confirmBtn;
@property (nonatomic, strong) NSArray<ORDERCancelReasonModel *> *reasonList;
@property (nonatomic, assign) NSInteger selectedIndex;

@end

@implementation ORDERDetailCancelReasonController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupData];
    [self setupUI];
}

- (void)setupData {
    // 初始化取消原因数据
    NSArray *reasons = @[
        @"不想要了",
        @"商品选错/多选",
        @"商品无货",
        @"地址信息填写错误",
        @"商品降价",
        @"价格高于其他平台",
        @"没用/少用/错用优惠",
        @"其他原因"
    ];

    NSMutableArray *reasonModels = [NSMutableArray array];
    for (NSString *reason in reasons) {
        ORDERCancelReasonModel *model = [[ORDERCancelReasonModel alloc] init];
        model.title = reason;
        model.selected = NO;
        [reasonModels addObject:model];
    }

    self.reasonList = reasonModels;
    self.selectedIndex = -1;
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];

    // 标题
    [self.view addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(30);
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view).offset(-20);
    }];

    // 表格视图
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(30);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.confirmBtn.mas_top).offset(-20);
    }];

    // 确定按钮
    [self.view addSubview:self.confirmBtn];
    [self.confirmBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view).offset(-20);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-20);
        make.height.mas_equalTo(50);
    }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.reasonList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *identifier = @"ORDERCancelReasonCell";
    ORDERCancelReasonCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[ORDERCancelReasonCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }

    ORDERCancelReasonModel *model = self.reasonList[indexPath.row];
    [cell configWithModel:model];

    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 60;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // 取消之前的选择
    if (self.selectedIndex >= 0 && self.selectedIndex < self.reasonList.count) {
        self.reasonList[self.selectedIndex].selected = NO;
    }

    // 设置新的选择
    self.selectedIndex = indexPath.row;
    self.reasonList[self.selectedIndex].selected = YES;

    // 刷新表格
    [self.tableView reloadData];

    // 更新确定按钮状态
    self.confirmBtn.enabled = YES;
    self.confirmBtn.backgroundColor = [UIColor orangeColor];
}

#pragma mark - Actions

- (void)confirmButtonClicked:(UIButton *)sender {
    if (self.selectedIndex >= 0 && self.selectedIndex < self.reasonList.count) {
        ORDERCancelReasonModel *selectedReason = self.reasonList[self.selectedIndex];
        NSLog(@"选择的取消原因: %@", selectedReason.title);

        // 执行回调
        if (self.reasonSelectedCallback) {
            self.reasonSelectedCallback(selectedReason.title);
        }

        [self.navigationController popViewControllerAnimated:YES];
    }
}

#pragma mark - Getters

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"请选择取消订单原因";
        _titleLabel.font = [UIFont boldSystemFontOfSize:18];
        _titleLabel.textColor = [UIColor blackColor];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _titleLabel;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = NO;
    }
    return _tableView;
}

- (UIButton *)confirmBtn {
    if (!_confirmBtn) {
        _confirmBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_confirmBtn setTitle:@"确定" forState:UIControlStateNormal];
        [_confirmBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _confirmBtn.titleLabel.font = [UIFont boldSystemFontOfSize:16];
        _confirmBtn.backgroundColor = [UIColor lightGrayColor];
        _confirmBtn.layer.cornerRadius = 25;
        _confirmBtn.layer.masksToBounds = YES;
        _confirmBtn.enabled = NO;
        [_confirmBtn addTarget:self action:@selector(confirmButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _confirmBtn;
}

@end
