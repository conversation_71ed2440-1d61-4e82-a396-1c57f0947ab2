//
//  JDISVOrderDetailOrderActionFloorModule.m
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import "JDISVOrderDetailOrderActionFloorModule.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import "JDISVOrderDetailOrderActionFloor.h"
#import "JDISVOrderDetailOrderActionFloorModel.h"
#import "JDISVOrderDetailModuleMacro.h"
#import "NSString+urlencode.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
@import JDTCommonToolModule;

static const CGFloat kJDISVOrderDetailOrderActionFloorModuleBottomHeight = 54.f;

JDISVRegisterFloorModule(KaOrderActionFloor, JDISVOrderDetailOrderActionFloorModule);

@interface JDISVOrderDetailOrderActionFloorModule ()

@property (nonatomic, copy) NSArray <ORDERListItemSkuDetailModel *> *productModels;

@property (nonatomic, copy) NSArray <ORDERListItemShipmentOrderModel *> *shipmentOrder;

@end

@implementation JDISVOrderDetailOrderActionFloorModule
- (UIView *)floorView {
    UIView* v = [[JDISVOrderDetailOrderActionFloor alloc] init];
    return v;
}

- (CGFloat)floorHeight {
    return self.bottomHeight;
}
- (BOOL)isDeliverFloor {
    return NO;
}

- (BOOL)ignoreCorner {
    return YES;
}

- (JDISVFloorType)floorType{
    return JDISVFloorTypeBottomFixFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *allFloorData = data[@"data"] ? : @{};
    ORDERListItemModel *order = [ORDERListItemModel yy_modelWithDictionary:allFloorData];
    self.orderId = order.orderId;
    self.buttonViewModels = [self updateButtonViewModelsWithNew:order.buttons];
    
    NSMutableArray *productModels = [NSMutableArray array];
    for (ORDERListItemSkuDetailModel *skuItem in order.orderItem) {
        [productModels addObject:skuItem];
    }
    self.productModels = [productModels copy];
    
    self.shipmentOrder = order.shipmentOrder;
}

//- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
//    NSDictionary *featureExt = data[@"ext"] ? : @{};
//    self.evaluateButtonFeature = featureExt[@"evaluateButtonFeature"] && [featureExt[@"evaluateButtonFeature"] isEqualToNumber:@1] ? YES:NO;
//    self.delayReceiveButtonFeature = featureExt[@"delayReceiveButtonFeature"] && [featureExt[@"delayReceiveButtonFeature"] isEqualToNumber:@1] ? YES:NO;
//    self.checkInvoiceButtonFeature = featureExt[@"checkInvoiceButtonFeature"] && [featureExt[@"checkInvoiceButtonFeature"] isEqualToNumber:@1] ? YES:NO;
//    self.applyInvoiceButtonFeature = featureExt[@"applyInvoiceButtonFeature"] && [featureExt[@"applyInvoiceButtonFeature"] isEqualToNumber:@1] ? YES:NO;
//    
//    NSDictionary *allFloorData = data[@"data"] ? : @{};
//    
//    {//店铺信息
//        NSArray* shop = allFloorData[@"shopInfoDTOList"];
//        NSDictionary* shopDic = shop.firstObject;
//        self.venderId = shopDic[@"venderId"];
//        NSArray* wareList = shopDic[@"wareInfoDTOList"];
//        NSDictionary* wareDic = wareList.firstObject;
//        self.storehouseId = wareDic[@"storehouseId"];
//        NSDictionary* otherDic = allFloorData[@"others"];
//        self.orderStatus = otherDic[@"orderStatus"];
//    }
//    NSDictionary *orderIdInfoDic = allFloorData[@"orderIdInfoDTO"] ? : @{};
//    self.orderId = orderIdInfoDic[@"orderId"] ? : @"";
//    JDISVOrderDetailOrderActionFloorModel *model = [JDISVOrderDetailOrderActionFloorModel yy_modelWithDictionary:allFloorData];
//    NSArray *buttonsViewModels = [self updateButtonViewModelsWith:model.buttonInfoList andPreModel:model.presaleInfoDTO];
//    if (buttonsViewModels) {
//        self.buttonViewModels = [NSArray arrayWithArray:buttonsViewModels];
//    } else {
//        self.buttonViewModels = [NSArray array];
//    }
//    
//    NSMutableArray *productModels = [NSMutableArray array];
//    for (JDISVOrderDetailOrderActionFloorStoreModel *shopModel in model.shopInfoList) {
//        [productModels addObjectsFromArray:shopModel.productList];
//    }
//    self.productModels = [NSArray arrayWithArray:productModels];
//    
//    BOOL presellFlag = NO;
//    BOOL groupBuyFlag = NO;
//    NSString *sendpay = allFloorData[@"others"][@"sendPay"];
//    if (sendpay.length > 44 && [sendpay characterAtIndex:43] == '1') {//预售
//        presellFlag = YES;
//    }else if (sendpay.length > 433 && [sendpay characterAtIndex:432] == '1'){//拼团
//        groupBuyFlag = YES;
//    }
//    
//    //组装拼团分享参数
//    if (groupBuyFlag) {
//        if (self.productModels.count > 0) {
//            JDISVOrderDetailOrderActionFloorStoreModelProductModel *model = self.productModels.firstObject;
//            NSMutableDictionary *param = [NSMutableDictionary dictionary];
//            [param setObject:model.productName ? : @"" forKey:@"shareDesc"];
//            [param setObject:model.productName ? : @"" forKey:@"shareTitle"];
//            if (KSAAPP) {
//                [param setObject:OrderDetailL(@"isv_order_share_sharebuy_detail") forKey:@"shareDesc"];
//                [param setObject:OrderDetailL(@"isv_order_share_sharebuy_detail") forKey:@"shareTitle"];
//            }
//            [param setObject:[PlatformService getCompleteImageUrl:model.imageShortUrl] ? : @"" forKey:@"shareImgUrl"];
//            
//            NSString *taskId = allFloorData[@"shareGroupInfoDTO"][@"groupId"] ? : @"";
//            NSString *shareUrl = [PlatformService getJumpH5UrlByType:JDISVJumpH5UrlTypeGroupBuyShare withAppendParam:taskId] ? : @"";
//            [param setObject:shareUrl forKey:@"shareUrl"];
//            
//            [param setObject:@[@"wechat", @"friends"] forKey:@"shareChannels"];
//            
//            NSString *wxPath = [[JDISVPlatformService sharedService] wxSharePath];
//            // miniProgram
//            NSMutableDictionary *mp = [NSMutableDictionary dictionary];
//            [mp setObject:@"https://www.jd.com/" forKey:@"webpageUrl"];
//            
//            NSString *path = [NSString stringWithFormat:@"%@?url=%@", wxPath, [shareUrl stringByURLEncode]];
//            [mp setObject:path forKey:@"path"];
//            
//            [mp setObject:@(YES) forKey:@"withShareTicket"];
//            [mp setObject:@(NO) forKey:@"disableforward"];
//            [mp setObject:@(1) forKey:@"miniprogramType"];
//            [param setObject:[mp copy] forKey:@"miniProgram"];
//            
//            self.sharedParams = [param copy];
//        }
//    }
//}

#pragma mark - ViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.bottomHeight = 0;
        self.buttonViewModels = [NSArray array];
    }
    return self;
}

- (NSArray * __nullable)updateButtonViewModelsWithNew:(NSArray <ORDERListItemButtonModel *> *)buttonModels {
    NSMutableArray *buttonViewModelMArr = [NSMutableArray array];
    for (ORDERListItemButtonModel *button in buttonModels) {
        JDISVOrderDetailOrderActionFloorButtonViewModel *buttonViewModel = [[JDISVOrderDetailOrderActionFloorButtonViewModel alloc] init];
        [buttonViewModel updateButtonModelWith:button];
        [buttonViewModelMArr addObject:buttonViewModel];
    }
    if (buttonViewModelMArr.count > 0) {
        [buttonViewModelMArr sortUsingComparator:^NSComparisonResult(JDISVOrderDetailOrderActionFloorButtonViewModel * _Nonnull obj1, JDISVOrderDetailOrderActionFloorButtonViewModel * _Nonnull obj2) {
            return obj1.sort > obj2.sort;
        }];
        return buttonViewModelMArr;
    } else {
        return nil;
    }
}

//- (NSArray * __nullable)updateButtonViewModelsWith:(NSArray *)buttonModels andPreModel:(NSDictionary *)preModel {
//    NSMutableArray *buttonViewModels = [NSMutableArray array];
//    for (JDISVOrderDetailOrderActionFloorModelButtonInfoModel *buttonModel in buttonModels) {
//        JDISVOrderDetailOrderActionFloorButtonViewModel *buttonViewModel = [[JDISVOrderDetailOrderActionFloorButtonViewModel alloc] init];
//        [buttonViewModel updateWithButtonModelWith:buttonModel];
//        if (buttonViewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePay && preModel &&  [preModel jdcd_getIntElementForKey:@"state"] == 0){//付定金
//            buttonViewModel.buttonModel.showLabelType = JDISVOrderDetailOrderActionFloorModelButtonTypePayFirstMoney;
//        }else if (buttonViewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePay && preModel && [preModel jdcd_getIntElementForKey:@"state"] == 3){//支付尾款
//            buttonViewModel.buttonModel.showLabelType = JDISVOrderDetailOrderActionFloorModelButtonTypePayLastMoney;
//        }else if (buttonViewModel.buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypePay && preModel && [preModel jdcd_getIntElementForKey:@"state"] == 2){//26:等待支付尾款（未到尾款支付时间）
//            buttonViewModel.buttonModel.showLabelType = JDISVOrderDetailOrderActionFloorModelButtonTypeWaitPayLastMoney;
//        }
//        //是否隐藏评价、延迟收货、申请开票、查看发票按钮
//        if (!((buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeEvaluate && self.evaluateButtonFeature == NO) || (buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeDelayReceive && self.delayReceiveButtonFeature == NO) || (buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeApplyInvoice && self.applyInvoiceButtonFeature == NO) || (buttonModel.showLabelType == JDISVOrderDetailOrderActionFloorModelButtonTypeCheckInvoice && self.checkInvoiceButtonFeature == NO))) {
//            [buttonViewModels addObject:buttonViewModel];
//        }
//    }
//    
//    if (buttonViewModels.count > 0) {
//        [buttonViewModels sortUsingComparator:^NSComparisonResult(JDISVOrderDetailOrderActionFloorButtonViewModel *btn1, JDISVOrderDetailOrderActionFloorButtonViewModel * btn2) {
//            return btn1.sort > btn2.sort;
//        }];
//        return [NSArray arrayWithArray:buttonViewModels];
//    } else {
//        return nil;
//    }
//}

- (CGFloat)bottomHeight {
    CGFloat safeBottom = 0.f;
    if (@available(iOS 11.0, *)) {
//        CGFloat bottom = [UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom;
        CGFloat bottom = [UIApplication sharedApplication].jdt_currentKeyWindow.safeAreaInsets.bottom;
        if (bottom > 0) {
            safeBottom = bottom;
        }
    }
    
    if (self.buttonViewModels && self.buttonViewModels.count > 0) {
        return safeBottom + kJDISVOrderDetailOrderActionFloorModuleBottomHeight;
    } else {
        return 0;
    }
    
}

@end
