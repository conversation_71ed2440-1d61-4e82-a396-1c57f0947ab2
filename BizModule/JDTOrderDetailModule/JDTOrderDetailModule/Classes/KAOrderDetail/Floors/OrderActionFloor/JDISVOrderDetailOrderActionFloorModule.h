//
//  JDISVOrderDetailOrderActionFloorModule.h
//  JDISVOrderDetailSDKModule
//
//  Created by 张令浩 on 2022/3/3.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
#import "JDISVOrderDetailOrderActionFloorButtonViewModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface JDISVOrderDetailOrderActionFloorModule : NSObject<JDISVFloorModuleProtocol>
@property (nonatomic, copy) NSString *orderId;
@property (nonatomic, assign) CGFloat bottomHeight;
@property (nonatomic, copy) NSArray <JDISVOrderDetailOrderActionFloorButtonViewModel *> *buttonViewModels;
@property (nonatomic, copy) NSDictionary *sharedParams;//拼团-邀请好友-分享参数

@property (nonatomic) BOOL evaluateButtonFeature; /**< 隐藏评价按钮的配置开关 */
@property (nonatomic) BOOL delayReceiveButtonFeature; /**< 隐藏延迟收货按钮的配置开关 */
@property (nonatomic) BOOL checkInvoiceButtonFeature; /**< 隐藏查看发票按钮的配置开关 */
@property (nonatomic) BOOL applyInvoiceButtonFeature; /**< 隐藏申请开票按钮的配置开关 */

@property (nonatomic, strong) NSNumber *venderId;
@property (nonatomic, strong) NSNumber *storehouseId;
@property (nonatomic, strong) NSNumber *orderStatus;
/// 购买的商品（再次购买时使用）
@property (nonatomic, copy, readonly) NSArray <ORDERListItemSkuDetailModel *> *productModels;
/// 物流轨迹（查看物流使用）
@property (nonatomic, copy, readonly) NSArray <ORDERListItemShipmentOrderModel *> *shipmentOrder;

@end

NS_ASSUME_NONNULL_END
