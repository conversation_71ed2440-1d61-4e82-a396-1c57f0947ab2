//
//  JDISVSettlementViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementSourceType) {
    JDISVSettlementSourceTypeCart = 0, /**< 购物车 */
    JDISVSettlementSourceTypeProductDetail /**< 商详 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementViewModelUpdateType ) {
    JDISVSettlementViewModelUpdateTypeMain = 0, /**< 主接口 */
    JDISVSettlementViewModelUpdateTypeResetCoupon = 1, /**< 重置优惠券 */
    JDISVSettlementViewModelUpdateTypeUseCoupon = 2, /**< 使用优惠券 */
    JDISVSettlementViewModelUpdateTypeUsePoint = 3, /**< 使用积分 */
    JDISVSettlementViewModelUpdateTypeSaveShipment = 4, /**< 保存物流 */
    JDISVSettlementViewModelUpdateTypeChangeAdress = 5, /**< 切换地址 */
    JDISVSettlementViewModelUpdateTypeRemoveStockout /**< 移除无货 */
};

@class RACSignal;

@interface JDISVSettlementViewModel : NSObject

@property (nonatomic, assign) JDISVSettlementSourceType source;
@property (nonatomic, strong) NSDictionary *routerParams;//router传入的参数
@property (nonatomic, strong) NSMutableDictionary *otherParams;//其他的业务参数统一存放这里
@property (nonatomic, assign) BOOL useBestCoupon;//智能选券,首页主接口currentOrder和切换地址接口saveConsigneeAddress需要传入
@property (nonatomic, assign) BOOL isLoc; // 是否是本地生活结算页(默认为NO)

@property (nonatomic, strong) NSArray *couponIds;//预售付尾款接口的入参：优惠券Id列表

@property (nonatomic, assign) BOOL noAnyAddress;//无任何地址，默认NO
@property (nonatomic,copy) NSNumber *deliveryAddressId; //
@property (nonatomic,copy) NSString *billingAdrressId; //

@property (nonatomic, strong) NSDictionary *allFloorOriginData; /**< 下发原始楼层数据 */
@property (nonatomic, strong) NSArray<NSArray<NSDictionary*>*>*floorGroups; /**< 楼层数据模型[section][row] */
- (NSArray<NSArray<NSDictionary*>*>*)preloadingFloorData;

/// @param source 上一个页面:购物车(JDISVSettlementSourceTypeCart)/商详(JDISVSettlementSourceTypeProductDetail)
- (instancetype)initWithSource:(JDISVSettlementSourceType)source NS_DESIGNATED_INITIALIZER;


// 数据请求后更新楼层
- (void)processFloorData:(NSDictionary * )rawData;

/// 请求主接口地址
- (RACSignal *)loadMainDataSignalWithAddressId:(NSString *)addressId;
/// 请求地址列表
- (RACSignal *)loadAddressListSignal;
/// 请求优惠券列表
- (RACSignal *)loadCouponsListSignal;

- (RACSignal *)loadSaveConsigneeAddressSignalWithId:(NSString *)addressId; // 保存收件人地址
// 重置优惠券接口
- (RACSignal *)loadResetAllCouponsParamOf:(NSDictionary *)param;

- (RACSignal *)queryInvoiceListSignal;//查询发票列表信息


/// 更改支付方式
/// @param param 传入参数
- (RACSignal *)changedPaymentType:(NSDictionary *)param;

@end

NS_ASSUME_NONNULL_END
