//
//  JDISVSettlementNetService.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <Foundation/Foundation.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>



#define SettlementNetService [JDISVSettlementNetService sharedService]


@interface JDISVSettlementNetService : NSObject

@property (assign,nonatomic) BOOL isVistor;//是否访客
@property (copy,nonatomic) NSDictionary *vistorParams;//访客入参

+ (instancetype)sharedService;

- (NSURLSessionDataTask * _Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                     requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                                      path:(NSString *)urlPath
                                  function:(NSString * _Nonnull)functionId
                                    version:(NSString * _Nonnull)version
                                parameters:(NSDictionary * _Nullable)parameters
                                  complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock;

- (NSURLSessionDataTask * _Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                     requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                              paramterType:(JDISVColorGateParameterType)paramterType
                                      path:(NSString *)urlPath
                                  function:(NSString * _Nonnull)functionId
                                    version:(NSString * _Nonnull)version
                                parameters:(NSDictionary * _Nullable)parameters
                                  complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock;
@end


