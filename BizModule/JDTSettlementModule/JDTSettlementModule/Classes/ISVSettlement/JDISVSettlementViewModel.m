//
//  JDISVSettlementViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementViewModel.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorConfigManager.h>
#import "JDISVSettlementAmountFloorNetService.h"
@import JDTInfrastructureModule;
@import JDTCommonToolModule;

@interface JDISVSettlementViewModel ()
@property (nonatomic, assign) NSTimeInterval startTimeMTA;
@property (nonatomic, assign) NSTimeInterval endTimeMTA;
@end

@implementation JDISVSettlementViewModel
- (instancetype)initWithSource:(JDISVSettlementSourceType)source
{
    self = [super init];
    if (self) {
        _source = source;
        _otherParams = @{}.mutableCopy;
        [JDISVSettlementAmountFloorNetService sharedService].source = _source;
    }
    return self;
}

#pragma mark - Data
// 预加载楼层
- (NSArray<NSArray<NSDictionary *> *> *)preloadingFloorData {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KaCheckNavBarFloor",
                @"data":@{
                    
                },
                @"ext":@{
                    
                }
            },
        ],
        @[
            @{
                @"floorId":@"KaCheckSkeletonFloor",
                @"data":@{
                    
                },
                @"ext":@{
                    
                }
            }
        ]
    ];
    return [data copy];
}

// 数据请求后更新楼层
- (void)processFloorData:(NSDictionary * )rawData {
    
    KASettlementAmountFloorNetService.isCheckoutPageFirstLoadData += 1;
    NSLog(@"-- %ld",KASettlementAmountFloorNetService.isCheckoutPageFirstLoadData);
    self.allFloorOriginData = [rawData copy];
    
    // 配置免运费楼层
    //    NSMutableArray *floorArr = [NSMutableArray arrayWithCapacity:0];
    //    if (nil != self.allFloorOriginData) {
    //
    //        NSArray *allFloors = [self.allFloorOriginData objectForKey:@"floors"];
    //        if (allFloors && allFloors.count > 0) {
    //            NSDictionary *disCountDic = nil;
    //            for (NSDictionary *floorDic in allFloors) {
    //                NSString *uuid = [floorDic objectForKey:@"uuid"];
    //                if ([uuid containsString:@"totalFreightFloor"]) {
    //                    disCountDic = floorDic;
    //                    break;
    //                }
    //            }
    //            [floorArr addObjectsFromArray:allFloors];
    //
    //            if (disCountDic) {
    //                [floorArr addObject:disCountDic];
    //            }
    //        }
    //    }
    
    
    
    NSDictionary *dataMap = @{
        @"floors": rawData[@"floors"] ? : [NSArray array],
        //        @"floors": floorArr,
        @"hierarchy": [self hierarchyMapInResultInfo:rawData],
        @"rootKey": [self rootKeyInResultInfo:rawData],
        @"source":@(_source),
        @"routerParams":_routerParams ? : @{},
        @"otherParams":_otherParams
    };
    
    //配置订单业务类型标识
    [self checkOrderTypeWithOriginData:dataMap];
    
    NSMutableArray *result = [NSMutableArray array];
    
    //取出默认楼层配置
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:KaCheckPageId];
    NSMutableArray *floors = [NSMutableArray arrayWithArray:config[@"layout"]];
    
    //按顺序添加楼层
    //    [floors insertObject:@[@"KaCheckPhoneFloor"] atIndex:3];//在店铺楼层后添加预售手机号楼层
    //    if (ISFirstMoenyFlag) {
    //        [floors replaceObjectAtIndex:5 withObject:@[@"kaCheckFirstMoneyInfoFloor",@"KSAPayWidgetFloor"]];//amount楼层替换预售定金信息楼层
    //    }
    //    [floors insertObject:@[@"KaCheckOnlinePayFloor"] atIndex:7];//在total楼层之前添加在线支付楼层
    
    NSDictionary *features = config[@"features"][@"floors"];
    for (NSArray *tempGroup in floors) {
        NSMutableArray *group = [NSMutableArray array];
        for (NSString *floorId in tempGroup) {
            NSMutableDictionary *floor = [NSMutableDictionary dictionary];
            if (floorId) {
                [floor setObject:floorId forKey:@"floorId"];
                if ([floorId isEqualToString:@"KaCheckAddressFloor"]) {
                    [floor setObject:@(self.isLoc) forKey:@"isLoc"];
                }
            }
            if (features[floorId]) {
                [floor setObject:features[floorId] forKey:@"ext"];
            }
            //            [floor setObject:dataMap forKey:@"data"];
            [floor setObject:rawData forKey:@"data"];
            
            [group addObject:floor];
        }
        
        NSDictionary *deliver = @{
            @"floorId":@"KaCheckSepratorFloor",
            @"data":@{
                
            },
            @"ext":@{
                
            }
        };
        
        if (![tempGroup containsObject:@"KaCheckNavBarFloor"] && ![tempGroup containsObject:@"KaCheckTotalFloor"]) {
            [group addObject:deliver];
        }
        
        [result addObject:group];
        
    }
    
    
    self.floorGroups = [result copy];
}

// 错误展示页面
- (void)processError:(NSError * __nonnull)error {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KaCheckNavBarFloor",
                @"data":@{
                },
                @"ext":@{
                    
                }
            },
        ],
        @[
            @{
                @"floorId":@"KaCheckEmptyFloor",
                @"data":error,
                @"ext":@{
                    
                }
            }
        ],
    ];
    
    self.floorGroups = [data copy];
}

- (void)checkOrderTypeWithOriginData:(NSDictionary *)dataMap{
    //清楚缓存
    [JDISVSettlementAmountFloorNetService sharedService].ISPresellCapacity = 0;
    [JDISVSettlementAmountFloorNetService sharedService].ISCODPayType = 0;
    [JDISVSettlementAmountFloorNetService sharedService].ISFirstMoenyCapacity = 0;
    [JDISVSettlementAmountFloorNetService sharedService].ISGroupBuyCapacity = 0;
    
    NSDictionary *resultInfo = dataMap;
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];
    NSDictionary *hierarchy = [resultInfo objectForKey:@"hierarchy"];
    NSString *rootKey = [resultInfo objectForKey:@"rootKey"];
    
    
    NSArray *rootFloorKeys = [hierarchy objectForKey:rootKey];
    
    for (NSString *uuid in rootFloorKeys) { {
        for (NSDictionary *floorDic in floorsArray) {
            if ([uuid isEqualToString:floorDic[@"uuid"]]) {
                if ([uuid containsString:@"presaleDefaultFloor"]) {
                    //预售标识
                    NSDictionary *infoDic = [floorDic objectForKey:@"info"];
                    NSDictionary *basicDic = [infoDic objectForKey:@"C-M#abstractPresaleFloor&core"];
                    BOOL presaleFlag = [[basicDic objectForKey:@"presaleFlag"] boolValue];
                    if (presaleFlag) {
                        [JDISVSettlementAmountFloorNetService sharedService].ISPresellCapacity = 1;
                    }
                    BOOL presaleEndPayFlag = [[basicDic objectForKey:@"presaleEndPayFlag"] boolValue];
                    if (presaleEndPayFlag) {// 付尾款
                        [JDISVSettlementAmountFloorNetService sharedService].ISFirstMoenyCapacity = 0;
                    }else{// 付定金
                        [JDISVSettlementAmountFloorNetService sharedService].ISFirstMoenyCapacity = 1;
                        
                    }
                }
            }
        }
    }
    }
}

- (NSDictionary *)allFloorOriginData {
    if (!_allFloorOriginData) {
        _allFloorOriginData = [NSDictionary dictionary];
    }
    return _allFloorOriginData;
}

- (NSArray<NSArray<NSDictionary *> *> *)floorGroups {
    if (!_floorGroups) {
        _floorGroups = [NSArray array];
    }
    return _floorGroups;
}

- (NSDictionary *)hierarchyMapInResultInfo:(NSDictionary *)resultInfo {
    NSDictionary *structure = resultInfo[@"structure"] ? : @{};
    NSDictionary *hierarchyMap = structure[@"hierarchy"] ? : @{};
    return hierarchyMap;
}

- (NSString *)rootKeyInResultInfo:(NSDictionary *)resultInfo {
    NSDictionary *structure = resultInfo[@"structure"] ? : @{};
    NSString *rootKey = structure[@"root"] ? : @"rootUUID";
    return rootKey;
}

#pragma mark -  优惠券
- (void)requestCoupons:(NSString *)addressId complete:(void (^)(NSDictionary *couponDict, NSError *error))completeBlock {
    NSDictionary *testParams = @{
        @"immediatelyBuy": @(NO),
        @"skuList": @[],
        @"addressId": [addressId jdcd_toNumber],
        @"payType": @"",
        @"lbsParam": @{
            @"coordinateParam": @{
                @"longitude": @(0),
                @"latitude": @(0)
            },
            @"provinceId": @"",
            @"cityId": @"",
            @"districtId": @"",
            @"townId": @""
        },
        @"selectedVoucherIds": @[],
        @"currentVoucherIds": @[]
    };
    [[OOPNetworkManager sharedManager] POST:@"checkout/c/getCoupons?apiCode=b2c.cbff.checkout.c.getCoupons" parameters:testParams headers:@{} completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            completeBlock(nil, error);
        } else {
            completeBlock(responseObject[@"data"], nil);
        }
    }];
}

#pragma mark - 请求主接口信号
- (RACSignal *)loadMainDataSignalWithAddressId:(NSString *)addressId{
    @weakify(self);
    if (((NSNumber *)self.routerParams[@"presaleEndPayment"]).boolValue){
        __weak typeof(self) weakSelf = self;
        return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
            [weakSelf requestSettlementPresaleEndPaymentDataForm:weakSelf.source addressId:addressId complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
                if (error) {
                    [weakSelf processError:error];
                    [subscriber sendError:error];
                } else {
                    SettlementNetService.isVistor = NO;
                    [weakSelf processFloorData:resultInfo];
                    [subscriber sendCompleted];
                }
            }];
            return nil;
        }];
    }else{
        self.startTimeMTA = [[NSDate date] timeIntervalSince1970];
        __weak typeof(self) weakSelf = self;
        return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//            [weakSelf requestSettlementDataForm:weakSelf.source addressId:addressId complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
//                if (error) {
//                    [weakSelf processError:error];
//                    [subscriber sendError:error];
//                    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
//                    NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
//                    [JDRouter openURL:url arg:@{@"name":@"CheckoutPageLoadError"} error:nil completion:nil];//结算加载失败埋点
//                } else {
//                    SettlementNetService.isVistor = NO;
//                    [weakSelf processFloorData:resultInfo];
//                    [subscriber sendCompleted];
//                    //接口加载时长埋点
//                    weakSelf.endTimeMTA = [[NSDate date] timeIntervalSince1970];
//                    NSTimeInterval diffT = weakSelf.endTimeMTA-weakSelf.startTimeMTA;
//                    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
//                    NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
//                    [JDRouter openURL:url arg:@{@"name":@"CheckoutPageLoadPerf",@"param":@{@"time":@(diffT)}} error:nil completion:nil];
//                }
//            }];
            
            NSMutableDictionary *testParams = [NSMutableDictionary dictionaryWithDictionary:@{
                @"immediatelyBuy": @(NO),
                @"skuList": @[],
                @"addressId": addressId,
                @"payType": @"",
                @"lbsParam": @{
                    @"coordinateParam": @{
                        @"longitude": @(0),
                        @"latitude": @(0)
                    },
                    @"provinceId": @"",
                    @"cityId": @"",
                    @"districtId": @"",
                    @"townId": @""
                },
                @"selectedVoucherIds": @[],
                @"currentVoucherIds": @[]
            }];
            
            [[OOPNetworkManager sharedManager] POST:@"checkout/c/detail?apiCode=b2c.cbff.checkout.c.detail" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
                @strongify(self);
                if (!error) {
                    if ([responseObject[@"code"] isEqualToString:@"0"]) {
                        NSDictionary *data = responseObject[@"data"];
                        [self requestCoupons:addressId complete:^(NSDictionary *couponDict, NSError *error) {
                            if (error) {
                                [self processFloorData:data];
                                [subscriber sendCompleted];
                            } else {
                                NSMutableDictionary *mData = [responseObject[@"data"] mutableCopy];
                                mData[@"couponList"] = couponDict;
                                [self processFloorData:mData];
                                [subscriber sendCompleted];
                            }
                        }];
                    }
                } else {
                    [weakSelf processError:error];
                    [subscriber sendError:error];
                }
            }];
            
            return nil;
        }];
    }
}

#pragma mark - 发票相关信号
// 查询发票列表
- (RACSignal *)queryInvoiceListSignal {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
//        NSMutableDictionary *param = [NSMutableDictionary dictionary];
//        if ([addressId jdcd_validateString]) {
//            [param addEntriesFromDictionary:@{@"id": addressId}];
//        }
        [self requestInvoiceListWithParam:nil sourceFrom:self.source complete:^(NSArray * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
//                NSError *error = [NSError errorWithDomain:@"JDISVSettlementInvoiceError" code:1 userInfo:@{@"empty_type":@"JDISVSettlementEmptyViewTypeError"}];
//                [self processError:error];
                [subscriber sendError:error];
            } else {
                if (resultInfo && [resultInfo isKindOfClass:[NSArray class]] && resultInfo.count > 0) {
                    [self.otherParams setObject:resultInfo forKey:@"originInvoiceListData"];
                    [subscriber sendCompleted];
                }
            }
        }];
        return nil;
    }];
}

#pragma mark - 地址相关信号
// 请求地址列表
- (RACSignal *)loadAddressListSignal {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
//        NSString *addressListURL = [NSString stringWithFormat:@"router://%@/addressList", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
//        [JDRouter openURL:addressListURL arg:nil error:nil completion:^(id  _Nullable object) {
//            @strongify(self)
//            NSError *error = [object objectForKey:@"error"];
//            if (error) {
//                NSError *error = [NSError errorWithDomain:@"JDISVSettlementAddressError" code:1 userInfo:@{@"empty_type":@"JDISVSettlementEmptyViewTypeError"}];
//                [self processError:error];
//                [subscriber sendError:error];
//            } else {
//                NSArray *addressList = (NSArray *)[object objectForKey:@"addressList"];
//                if (addressList && addressList.count > 0) {
//                    [subscriber sendNext:addressList];
//                } else {
//                    // 无地址信息
//                    NSError *error = [NSError errorWithDomain:@"JDISVSettlementAddressError" code:1 userInfo:@{@"empty_type":@"JDISVSettlementEmptyViewTypeNoAddress"}];
//                    self.noAnyAddress = YES;
//                    [self processError:error];
//                    [subscriber sendError:error];
//                }
//            }
//            
//        }];
        
        [[OOPNetworkManager sharedManager] POST:@"address/c/userAddressList?apiCode=b2c.cbff.address.c.userAddressList" parameters:@{} headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    JDTAddressModel *addressModel = [JDTAddressModel yy_modelWithDictionary:responseObject[@"data"]];
                    NSArray *addressList = addressModel.addressList;
                    if (addressList.count > 0) {
                        [subscriber sendNext:addressList];
                    } else {
                        // 无地址信息
                        // TODO:Juice 测试无地址流程是否通畅
                        NSError *error = [NSError errorWithDomain:@"JDISVSettlementAddressError" code:1 userInfo:@{@"empty_type":@"JDISVSettlementEmptyViewTypeNoAddress"}];
                        self.noAnyAddress = YES;
                        [self processError:error];
                        [subscriber sendError:error];
                    }
                }
            } else {
                NSError *error = [NSError errorWithDomain:@"JDISVSettlementAddressError" code:1 userInfo:@{@"empty_type":@"JDISVSettlementEmptyViewTypeError"}];
                [self processError:error];
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
}

// 保存收货地址
- (RACSignal *)loadSaveConsigneeAddressSignalWithId:(NSString *)addressId {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        if ([addressId jdcd_validateString]) {
            [param addEntriesFromDictionary:@{@"id": addressId}];
        }
        [self requestSaveAddressWithParam:[param copy] sourceFrom:self.source complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                NSError *error = [NSError errorWithDomain:@"JDISVSettlementAddressError" code:1 userInfo:@{@"empty_type":@"JDISVSettlementEmptyViewTypeError"}];
                [self processError:error];
                [subscriber sendError:error];
            } else {
                [self processFloorData:resultInfo];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

// 重置优惠券接口
- (RACSignal *)loadResetAllCouponsParamOf:(NSDictionary *)param {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        [self requestResetAllCouponsWithParam:param sourceFrom:self.source complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [self processFloorData:resultInfo];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}



//[self readLocalFileWithName:@""];
- (NSDictionary *)readLocalFileWithName:(NSString *)name {
    // 获取文件路径
    NSString *path = [[NSBundle mainBundle] pathForResource:name ofType:@"json"];
    // 将文件数据化
    NSData *data = [[NSData alloc] initWithContentsOfFile:path];
    // 对数据进行JSON格式化并返回字典形式
    return [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
}
#pragma mark - request
#pragma mark - 主接口
/// 主接口 se_currentOrder
/// @param source source
/// @param addressId 地址Id
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSettlementDataForm:(NSInteger)source
                                                   addressId:(NSString *)addressId
                                                    complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (source == 1) {
        // 立即购买
        // orderStr
//        [param addEntriesFromDictionary:@{@"orderStr":@{@"id": [addressId jdcd_validateString] ? addressId : @""}}];
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];
        
    } else {
//        [param addEntriesFromDictionary:@{@"orderStr":@{@"id": [addressId jdcd_validateString] ? addressId : @""}}];
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    //放在orderStr
    NSMutableDictionary *orderStrParam = [[NSMutableDictionary alloc]init];
    if ([addressId jdcd_validateString]) {
        [orderStrParam setObject:addressId forKey:@"id"];
    }
    //拼团场景下入参 与immediatelyBuy一样放在boay里
    NSNumber *ptFlag = _routerParams[@"ptFlag"];
    if (ptFlag.boolValue) {
        [param setObject:_routerParams[@"ptFlag"] forKey:@"ptFlag"];
    }
    //预售场景下入参
    NSNumber *isPresale = _routerParams[@"isPresale"];
    if (isPresale.boolValue) {
        [param setObject:_routerParams[@"isPresale"] forKey:@"isPresale"];
        if (_routerParams[@"payTypeForPresale"]) {
            //订单支付类型 1：全款支付  2：定金支付
            [orderStrParam setObject:_routerParams[@"payTypeForPresale"] forKey:@"payTypeForPresale"];
        }
//        if ([mobile jdcd_validateString]) {
//            [orderStrParam setObject:mobile forKey:@"userMobile"];
//        }
//        if ([areaCode jdcd_validateString]) {
//            [orderStrParam setObject:areaCode forKey:@"areaCode"];
//        }
    } else {
        //智能选券 非预售传
        if (self.useBestCoupon) {
            [orderStrParam setObject:@(self.useBestCoupon) forKey:@"useBestCoupon"];
        }
    }
    
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrParam}];
    
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_currentOrder" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        
        NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
        NSString *code = (NSString *)[responseObject objectForKey:@"code"];
        NSString *message = (NSString *)[responseObject objectForKey:@"message"];
        
        if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
            // 正常
            
            NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
            completeBlock(resultInfo, nil);
        } else {
            // 异常
            NSString *errorCode = @"-999";
            NSInteger codeInt = 0;
            if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                // TODO code == 3 未登录
                if ([message jdcd_validateString] == NO) {
                    message = SettlementLan(@"setllement_not_logged_in") ;
                }
                errorCode = @"3";
                codeInt = 3;
            } else {
                if ([resultCode jdcd_validateString]) {
                    errorCode = resultCode;
                }
                if (![message jdcd_validateString]) {
                    message = SettlementLan(@"setllement_unknown_error") ;
                }
            }
            NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                                                                                                                     }];
            completeBlock(nil,resultError);
        }
    }];
}

//预售付尾款结算主接口调结算中台
- (NSURLSessionDataTask * _Nonnull)requestSettlementPresaleEndPaymentDataForm:(NSInteger)source
                                                   addressId:(NSString *)addressId
                                                    complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    if (source == 1) {
//        // 立即购买
//        // orderStr
//        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
//        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSString *couponIds = [self.couponIds componentsJoinedByString:@","];//,为分隔符
    if ([couponIds jdcd_validateString]) {
        [param setObject:couponIds forKey:@"selectCouponIds"];
    }
    [param setObject:_routerParams[@"orderId"] ? : @"" forKey:@"orderId"];


    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"marketing_presale_balance_init" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
#if DEBUG
//        responseObject = [self readLocalFileWithName:@"testData_presellEndPay"];
//        error = nil;
#endif
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            NSNumber *success = (NSNumber *)[responseObject objectForKey:@"success"];

            
            if (success.boolValue) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"data"];
                resultInfo = [self adaptResponceModel:responseObject];
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
        
    }];
}

#pragma mark - 获取发票列表数据
- (NSURLSessionDataTask * _Nonnull)requestInvoiceListWithParam:(NSDictionary *)orderStrParam
                                                     sourceFrom:(NSInteger)source
                                                       complete:(void(^)(NSArray * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];
    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];
            
    return [SettlementNetService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"invoice_query_color" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            NSString *success = (NSString *)[responseObject objectForKey:@"success"];
            
            if (success.boolValue) {
                // 正常
                NSArray *resultInfo = (NSArray *)[responseObject objectForKey:@"data"];
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
    }];
}

#pragma mark - 地址保存地址
/// 提交订单 se_saveConsigneeAddress
/// @param orderStrParam 提单参数
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSaveAddressWithParam:(NSDictionary *)orderStrParam
                                                     sourceFrom:(NSInteger)source
                                                       complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    } else {
        //非预售 智能选券
        if (self.useBestCoupon) {
            [orderStrMap setObject:@(self.useBestCoupon) forKey:@"useBestCoupon"];
        }
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];
            
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_saveConsigneeAddress" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
    }];
}

/// 重置优惠券入参 se_reSetAllCoupons
/// @param orderStrParam 参数信息
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestResetAllCouponsWithParam:(NSDictionary *)orderStrParam
                                                           sourceFrom:(NSInteger)source
                                                             complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];
            
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_reSetAllCoupons" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
               
                
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
        
    }];
}

#pragma mark - private
- (NSDictionary *)adaptResponceModel:(NSDictionary *)responceObject{
    //把结算中台的数据转换成阿波罗的数据格式
//    NSMutableDictionary *adaptResponceData = [[NSDictionary alloc] init];
    NSDictionary *data = responceObject[@"data"];
    if (!data || ![data isKindOfClass:NSDictionary.class])
        return nil;

    NSMutableArray *floors = @[].mutableCopy;
    NSMutableArray *rootUUID = @[].mutableCopy;
    NSMutableArray *venderFloors = @[].mutableCopy;

    //关税
    NSNumber *tariffVatFee = data[@"tariffVatFee"];

    if (tariffVatFee && [tariffVatFee isKindOfClass:NSNumber.class]){
        NSDictionary *floorDic = @{};
        NSString *uuid = @"defaultTaxFloor_1";
        floorDic = @{
            @"partList":@[
                @{
                    @"info":@{
                        @"C-P#taxType&core":@{
                            @"type":@"2",
                            @"taxAmount":tariffVatFee.stringValue,
                            @"operator":@"+"
                        },
                        @"VCYBB2C-D#taxRule&basic":@{
                            @"ruleTip":data[@"tariffVatFeeTip"] ? : @""
                        }
                    }
                }
            ],
            @"uuid" : uuid,
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    }else{
        NSDictionary *floorDic = @{};
        NSString *uuid = @"defaultTaxFloor_1";
        floorDic = @{
            @"partList":@[
                @{
                    @"info":@{
                        @"C-P#taxType&core":@{
                            @"type":@"2",
                            @"taxAmount":@"0",
                            @"operator":@"+"
                        },
                        @"VCYBB2C-D#taxRule&basic":@{
                            @"ruleTip":data[@"tariffVatFeeTip"] ? : @""
                        }
                    }
                }
            ],
            @"uuid" : uuid,
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    }
        //bottomFloor
        NSDictionary *floorDic = @{};
        NSString *uuid = @"defaultProductAmountFloor_2";
        floorDic = @{
            @"info" : @{
                @"C-M#productAmountFloor&basic" : @{
                    @"factAmount" : data[@"orderFactPayTotalAmount"] ? : @"",
                @"floorType" : @"2",
                @"label" : SettlementLan(@"Total")
              }
            },
            @"uuid" : uuid
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    
    NSArray *assetVos = data[@"assetVos"];//
    if ([assetVos isKindOfClass:[NSArray class]] && assetVos.count > 0) {//
        NSDictionary *floorDic = @{};
        NSString *uuid = @"defaultProductAmountFloor_1";
        floorDic = @{
            @"info" : @{
                @"C-M#productAmountFloor&basic" : @{
                    @"amount" : data[@"totalDiscount"] ? : @"",
                @"floorType" : @"1",
              }
            },
            @"uuid" : uuid
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    }
    
    if (data[@"presalePaymentVo"] && [data[@"presalePaymentVo"] isKindOfClass:NSDictionary.class]) {//
        NSDictionary *floorDic = @{};
        NSString *uuid = @"defaultProductAmountFloor_0";
        NSDictionary *presaleDic = data[@"presalePaymentVo"];
        floorDic = @{
            @"info" : @{
                @"C-M#productAmountFloor&basic" : @{
                    @"amount" : data[@"presalePaymentVo"][@"balanceTotalAmount"] ? : @"",
                @"floorType" : @"0",
                @"label" : SettlementLan(@"checkout_presale_end_price_label") 
              }
            },
            @"uuid" : uuid
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    }
    
    float couponDiscount = 0.0;
    NSInteger couponCount = 0;
    if ((data[@"canUseCouponVos"] && [data[@"canUseCouponVos"] isKindOfClass:NSArray.class])) {
        for (NSDictionary *couponItem in data[@"canUseCouponVos"]) {
            if (((NSNumber *)couponItem[@"selected"]).boolValue) {
                if ([couponItem[@"curUseDiscount"] jdcd_validateString]){
                    NSString *curUseDiscount = couponItem[@"curUseDiscount"];
                    couponDiscount = + curUseDiscount.floatValue;
                }
                couponCount++;
            }
        }
    }
    
        floorDic = @{};
        uuid = @"homePageFloorCouponFloor";
        floorDic = @{
            @"info" : @{
                @"C-M#homePageFloorCouponFloor&estimate" : @{
                    @"isUsedCoupon" : couponCount > 0 ? @(1) : @(0)
//                "useBestCoupon" : false
              },
                @"C-M#homePageFloorCouponFloor&money" : @{
//                "couponDiscount" : "2.00",
                    @"discountAmount" : [NSString stringWithFormat:@"%.2f",couponDiscount]
              },
                @"C-M#homePageFloorCouponFloor&number" : @{
//                "availableNum" : 1,
                    @"selectedCouponNum" : @(couponCount),
//                "totalNum" : 1,
//                "usedNum" : 1
              },
                @"C-M#homePageFloorCouponFloor&sign" : @{
//                "couponSign" : "已选1张"
              }
            },
            @"uuid" : uuid
          };
    [floors addObject:floorDic];
    [rootUUID addObject:uuid];
    
    //优惠券列表数据
    uuid = @"homePageFloorCouponVosFloor";
    NSMutableArray *couponVos = @[].mutableCopy;
    if ([data[@"canUseCouponVos"] isKindOfClass:NSArray.class]) {
        [couponVos addObjectsFromArray:data[@"canUseCouponVos"]];
    }
    if ([data[@"notCanCouponVos"] isKindOfClass:NSArray.class]) {
        [couponVos addObjectsFromArray:data[@"notCanCouponVos"]];
    }
    floorDic = @{
        @"info" : @{
            @"couponVos" : couponVos,
          },
        @"uuid" : uuid
      };
    [floors addObject:floorDic];
    [rootUUID addObject:uuid];

    //imageDomain楼层
    uuid = @"productFloor";
    NSString *imageDomain = [PlatformService getCompleteImageUrl:@""] ? : @"";
    floorDic = @{
      @"info" : @{
        @"C-D#imgInfo&basic" : @{
          @"imageDomain" : imageDomain
        },
//        "C-M#allProductFloor&basic" : {
//          "stockStatus" : 2
//        }
      },
      @"uuid" : uuid
    };
    [floors addObject:floorDic];
    [rootUUID addObject:uuid];
    
    if (data[@"totalFee"]) {//运费楼层
        NSDictionary *floorDic = @{};
        NSString *uuid = @"totalFreightFloor";
        floorDic = @{
            @"info" : @{
              @"C-M#totalFreightFloor&core" : @{
                @"freightLabel" : SettlementLan(@"checkout_summary_freight_label") ,
                @"totalFreight" : data[@"totalFee"] ? : @""
//                @"originalTotalFreight": @"C-M#totalFreightFloor&core.originalTotalFreight",
//                @"discountFreight": @"C-M#totalFreightFloor&core.discountFreight"
            },
            @"uuid" : uuid
          }
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    }
    
    if (data[@"freightFullDiscountAmount"]) {//免运费楼层
        NSDictionary *floorDic = @{};
        NSString *uuid = @"discountAmountFloor";
        floorDic = @{
            @"info" : @{
              @"C-M#discountAmountFloor&core" : @{
                @"freightFullDiscountAmountLabel" : SettlementLan(@"settlement_title_shipping_fee_discount"),
                @"freightFullDiscountAmount" : data[@"freightFullDiscountAmount"] ? : @""
            },
            @"uuid" : uuid
          }
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    }
    
        //店铺
        uuid = @"defaultVenderFloor";
        floorDic = @{
            @"info" : @{
                @"C-M#venderFloor&basic" : @{
//                    @"selfVenderFlag" : VoData[@"selfVenderFlag"] ? : @(0),
//                    @"venderIcon" : VoData[@"venderUrl"] ? : @"",
                },
                @"C-M#venderFloor&core" : @{
                    @"venderId" : data[@"venderId"] ? : @"",
                    @"venderName" : data[@"venderName"] ? : @"",
                    @"storeId" : data[@"storeId"] ? : @"",
                }
            },
            @"uuid" : uuid
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
    
    //配送
    uuid = @"shipmentHomePageFloor";
    floorDic = @{
        @"info" : @{
            @"C-M#abstractShipmentFloor&basic" : @{
                //                  "selectedFlag" : true,
                @"shipmentName" : data[@"shipmentTypeName"] ? : @"",
            },
            @"C-M#abstractShipmentFloor&core" : @{
                @"shipmentId" : data[@"shipmentType"] ? : @(0),
            },
            @"C-M#shipmentHomePageFloor&basic" : @{
                //                  @"shipmentType" : data[@"shipmentType"] ? : @"",
            }
        },
        @"uuid" : uuid
    };
    [floors addObject:floorDic];
    [rootUUID addObject:uuid];
    [venderFloors addObject:uuid];
    
    //留言
    if ([data[@"remark"] jdcd_validateString]) {
        uuid = @"defaultRemarkFloor";
        floorDic = @{
            @"info" : @{
    //            @"C-D#venderRemark&vender" : @{
    //                @"relationUUIDs" : @[
                        //                    "1012_fA1I4j1148260038187126784"
    //                ]
    //            },
                @"C-M#remarkFloor&basic" : @{
                    @"remark": data[@"remark"],
                    @"isShow" : @(true)
                }
            },
            @"uuid" : uuid
        };
        [floors addObject:floorDic];
        [rootUUID addObject:uuid];
        [venderFloors addObject:uuid];
    }
    
    
    if (data[@"cartItemVos"] && [data[@"cartItemVos"] isKindOfClass:NSArray.class]){//店铺商品楼层
        NSArray *cartItemVos = data[@"cartItemVos"] ? : @[];
        for (int i = 0; i < cartItemVos.count; i++) {
            NSDictionary *VoData = cartItemVos[i];
            
            uuid = [NSString stringWithFormat:@"retailProductFloor_%d",i];
            floorDic = @{
                @"info" : @{
                    //                "C-D#promotionInfo&basic" : {
                    //                  "discount" : "0",
                    //                  "jdPrice" : "18.00",
                    //                  "rePrice" : "0"
                    //                },
                    //                "C-M#productFloor&core" : {
                    //                  "modelType" : "1"
                    //                },
                    @"C-M#retailProductFloor&basic" : @{
                        @"imageUrl" : VoData[@"picUrl"] ? : @"",
                        //                  "productType" : 1,
                        @"skuId" : VoData[@"skuId"] ? : @"",
                        @"skuName" : VoData[@"skuName"] ? : @"",
                    },
                    @"C-M#retailProductFloor&numInfo" : @{
                        @"num" : VoData[@"num"] ? : @"",
                        //                  "numMsg" : "×1"
                    },
                    @"C-M#retailProductFloor&price" : @{
                        @"price" : VoData[@"price"] ? : @"",
                        //                  "priceShow" : "¥18.00"
                    },
                    @"C-M#retailProductFloor&stock" : @{
                        //                  "stockStatus" : "现货",
                        @"stockStatusCode" : VoData[@"stockStatus"] ? : @"",
                    }
                },
                @"uuid" : uuid
            };
            [floors addObject:floorDic];
            [rootUUID addObject:uuid];
            [venderFloors addObject:uuid];
        }
    }
    
//    NSArray *allKeys = data.allKeys;
        if (data[@"userAddressVo"] && [data[@"userAddressVo"] isKindOfClass:NSDictionary.class]) {//地址楼层
            NSMutableDictionary *floorDic = @{}.mutableCopy;
            NSString *uuid = @"defaultAddressFloor";
            [floorDic setObject:uuid forKey:@"uuid"];
            NSDictionary *VoData = data[@"userAddressVo"] ? : @{};
//            NSDictionary *infoCore = @{};//@"C-M#defaultAddressFloor&core"
            NSDictionary *infoCore = @{@"C-M#defaultAddressFloor&core":@{@"addressId":VoData[@"addressId"] ? : @""}};
            NSDictionary *infoConsignee = @{@"C-M#defaultAddressFloor&consignee":@{@"name":VoData[@"name"] ? : @"",@"mobile":VoData[@"mobile"] ? : @""}};
            NSDictionary *infoBasic = @{@"C-M#defaultAddressFloor&basic":@{@"fullAddress":VoData[@"fullAddress"] ? : @"",@"addressDetail":VoData[@"addressDetail"] ? : @""}};
            NSDictionary *infoaffiliation = @{@"C-M#defaultAddressFloor&affiliation":@{@"defaultAddress":VoData[@"defAddress"] ? : @(0)}};
            NSDictionary *infoArea = @{@"C-M#defaultAddressFloor&area": @{
                @"areaIdMap": @{
                    @"idProvince": VoData[@"provinceId"] ? : @(0),
                    @"idCity": VoData[@"cityId"] ? : @(0),
                    @"idArea": VoData[@"districtId"] ? : @(0),
                    @"idTown": VoData[@"townId"] ? : @(0),
                },
                @"areaNameMap": @{
                    @"provinceName": VoData[@"provinceName"] ? : @"",
                    @"cityName": VoData[@"cityName"] ? : @"",
                    @"areaName": VoData[@"districtName"] ? : @"",
                    @"townName": VoData[@"townName"] ? : @"",
                }
            }};
            
            NSMutableDictionary *infoDic = @{}.mutableCopy;
            [infoDic addEntriesFromDictionary:infoCore];
            [infoDic addEntriesFromDictionary:infoConsignee];
            [infoDic addEntriesFromDictionary:infoBasic];
            [infoDic addEntriesFromDictionary:infoaffiliation];
            [infoDic addEntriesFromDictionary:infoArea];

            [floorDic setObject:infoDic forKey:@"info"];
            
            [floors addObject:floorDic];
            [rootUUID addObject:uuid];
        }
        
        if (data[@"presalePaymentVo"] && [data[@"presalePaymentVo"] isKindOfClass:NSDictionary.class]) {//预售
            NSMutableDictionary *floorDic = @{}.mutableCopy;
            NSString *uuid = @"presaleDefaultFloor";
            [floorDic setObject:uuid forKey:@"uuid"];
            NSDictionary *VoData = data[@"presalePaymentVo"] ? : @{};
            NSMutableDictionary *infoCore = @{}.mutableCopy;
            [infoCore setObject:VoData[@"balanceStartTime"] ? : @"" forKey:@"endPaymentBeginTime"];
            [infoCore setObject:VoData[@"balanceEndTime"] ? : @"" forKey:@"endPaymentEndTime"];
            [infoCore setObject:VoData[@"balanceStartTimeFormat"] ? : @"" forKey:@"endPaymentBeginTimeFormat"];
            [infoCore setObject:VoData[@"balanceEndTimeFormat"] ? : @"" forKey:@"endPaymentEndTimeFormat"];
            [infoCore setObject:VoData[@"earnest"] ? : @"" forKey:@"earnest"];//定金
            [infoCore setObject:VoData[@"expandAmount"] ? : @"" forKey:@"deductionAmount"];//定金可抵扣
            [infoCore setObject:VoData[@"balanceItemPrice"] ? : @"" forKey:@"factEndPaymentItem"];//尾款单价
            [infoCore setObject:VoData[@"balanceTotalAmount"] ? : @"" forKey:@"factEndPayment"];//尾款总价
            [infoCore setObject:@(YES) forKey:@"presaleFlag"];//
            [infoCore setObject:@(YES) forKey:@"presaleEndPayFlag"];//预售付尾款标识
            
            NSMutableDictionary *infoDic = @{}.mutableCopy;
            NSString *infoKey = @"C-M#abstractPresaleFloor&core";
            [infoDic setObject:infoCore forKey:infoKey];
            [floorDic setObject:infoDic forKey:@"info"];
            
            [floors addObject:floorDic];
            [rootUUID addObject:uuid];
        }
    //支付方式楼层
    NSMutableDictionary *paymentInfoFloor = @{}.mutableCopy;
    NSString *paymentInfoFlooruuid = @"paymentInfoFloor_1231306080748724225";
    [paymentInfoFloor setObject:@{@"namespace":@"core.trade-FLR#balance.payment-M#paymentInfoFloor",@"version":@"1.0.0"} forKey:@"type"];
    [paymentInfoFloor setObject:paymentInfoFlooruuid forKey:@"uuid"];
    [paymentInfoFloor setObject:@{@"C-M#paymentInfoFloor&tip":@{@"codDocument":@""}} forKey:@"info"];
    
    NSMutableDictionary *paymentInfoFloorPartlist = @{}.mutableCopy;
    [paymentInfoFloorPartlist setObject:@{@"namespace":@"core.trade-FLR#balance.payment-P#paymentType",@"version":@"1.0.0"} forKey:@"type"];
    [paymentInfoFloorPartlist setObject:@{@"C-P#paymentType&paymentType":@{@"paymentTypeId":@"4",@"paymentTypeName":@"",@"selected":@(YES),@"supported":@(YES)}} forKey:@"info"];
    [paymentInfoFloor setObject:@[paymentInfoFloorPartlist] forKey:@"partList"];
    [floors addObject:paymentInfoFloor];
    [rootUUID addObject:paymentInfoFlooruuid];
    [venderFloors addObject:paymentInfoFlooruuid];
    
    NSDictionary *adaptResponceData = @{@"structure":@{@"hierarchy":@{@"rootUUID":rootUUID,@"defaultVenderFloor":venderFloors}},@"floors":floors};
    return adaptResponceData;
}

/// 更改支付方式
/// @param param 传入参数
- (RACSignal *)changedPaymentType:(NSDictionary *)param{
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
       
        [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_savePayment" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error){
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:error.code userInfo:@{@"JDISVSettlementErrorResultCodeKey":@(error.code), NSLocalizedDescriptionKey:SettlementLan(@"setllement_opation_error")
                }];
                [subscriber sendError:resultError];
            } else {
                NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
                NSString *code = (NSString *)[responseObject objectForKey:@"code"];
                NSString *message = (NSString *)[responseObject objectForKey:@"message"];
                
                if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                    // 正常
                    NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
//                    completeBlock(resultInfo, nil);
//                    NSLog(@"%@",error.description);
                    [subscriber sendNext:resultInfo];
                } else {
                    // 异常
                    NSString *errorCode = @"-999";
                    NSInteger codeInt = 0;
                    if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                        // TODO code == 3 未登录
                        if ([message jdcd_validateString] == NO) {
                            message = SettlementLan(@"setllement_not_logged_in") ;
                        }
                        errorCode = @"3";
                        codeInt = 3;
                    } else {
                        if ([resultCode jdcd_validateString]) {
                            errorCode = resultCode;
                        }
                        if (![message jdcd_validateString]) {
                            message = SettlementLan(@"setllement_unknown_error") ;
                        }
                    }
                    NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                    }];
                    [subscriber sendError:resultError];
                }
            }
            
        }];
        return nil;
    }];
}

@end
