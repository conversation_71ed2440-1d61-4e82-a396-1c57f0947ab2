//
//  JDISVSettlementSepratorFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementSepratorFloorModule.h"

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import <JDISVThemeModule/JDISVThemeSpace.h>

#import "JDISVSettlementSepratorFloor.h"

JDISVRegisterFloorModule(KaCheckSepratorFloor, JDISVSettlementSepratorFloorModule)

@implementation JDISVSettlementSepratorFloorModule

- (Class)tableViewFloorClass {
    return JDISVSettlementSepratorFloor.class;
}

- (CGFloat)floorHeight {
    CGFloat w2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    return w2;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (BOOL)isDeliverFloor {
    return YES;
}

- (BOOL)ignoreCorner {
    return YES;
}@end
