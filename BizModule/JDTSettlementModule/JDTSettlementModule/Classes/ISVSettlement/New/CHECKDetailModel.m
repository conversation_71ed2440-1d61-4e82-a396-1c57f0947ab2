//
//  CHECKDetailModel.m
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/5.
//

#import "CHECKDetailModel.h"

@implementation CHECKStoreHouseModel

@end

@implementation CHECKPromotionModel

@end

@implementation CHECKProductSpecModel

@end

@implementation CHECKSkuItemModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"productSpecs": [CHECKProductSpecModel class],
        @"promotionList": [CHECKPromotionModel class]
    };
}

@end

@implementation CHECKDeliveryInfoModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"skuItemList": [CHECKSkuItemModel class]
    };
}

@end

@implementation CHECKVenderModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"deliveryInfoList": [CHECKDeliveryInfoModel class]
    };
}

@end

@implementation CHECKAmountSummaryModel

@end

@implementation CHECKPayTypeModel

@end

@implementation CHECKPaymentInfoModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"payTypeList": [CHECKPayTypeModel class]
    };
}

@end

@implementation CHECKSelectedVoucherModel

@end

@implementation CHECKPointsRule

@end

@implementation CHECKDetailModel

+ (nullable NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"venderList": [CHECKVenderModel class]
    };
}

@end
