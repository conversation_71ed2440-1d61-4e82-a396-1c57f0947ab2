//
//  CHECKDetailModel.h
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/5.
//

#import <Foundation/Foundation.h>
@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@interface CHECKStoreHouseModel : NSObject

@property (nonatomic, assign) NSInteger storeWarehouseId;

@property (nonatomic, assign) NSInteger num;

@property (nonatomic, copy) NSString *provinceId;

@property (nonatomic, copy) NSString *cityId;

@property (nonatomic, copy) NSString *districtId;

@property (nonatomic, copy) NSString *contactName;

@property (nonatomic, copy) NSString *contactPhone;

@property (nonatomic, copy) NSString *countryId;

@property (nonatomic, copy) NSString *latitude;

@property (nonatomic, copy) NSString *longitude;

@property (nonatomic, copy) NSString *openingStartTime;

@property (nonatomic, copy) NSString *openingEndTime;

@property (nonatomic, copy) NSString *image;

@property (nonatomic, copy) NSString *storehouseName;

@property (nonatomic, copy) NSString *detailAddress;

@property (nonatomic, copy) NSString *fullAddress;

@property (nonatomic, strong) NSNumber *distanceDesc;

@property (nonatomic, strong) NSNumber *distanceM;

@end

@interface CHECKPromotionModel : NSObject

@property (nonatomic, copy) NSString *promoId;

@property (nonatomic, copy) NSString *activityType;

@property (nonatomic, copy) NSString *activitySubType;

@property (nonatomic, strong) NSNumber *remainTime;

@end

@interface CHECKProductSpecModel : NSObject
/// 规格名，如：尺码
@property (nonatomic, copy) NSString *name;
/// 规格值，如：42
@property (nonatomic, copy) NSString *value;

@end

@interface CHECKSkuItemModel : NSObject

@property (nonatomic, assign) NSInteger num;

@property (nonatomic, copy) NSString *skuName;

@property (nonatomic, copy) NSString *imgUrl;

@property (nonatomic, copy) NSString *skuId;

@property (nonatomic, strong) NSNumber *originalPrice;

@property (nonatomic, strong) NSNumber *salePrice;

@property (nonatomic, copy) NSArray <CHECKProductSpecModel *> *productSpecs;

@property (nonatomic, copy) NSString *remark;

@property (nonatomic, copy) NSArray <CHECKPromotionModel *> *promotionList;

@property (nonatomic, assign) ProductType productType;
/// 核销门店
@property (nonatomic, strong) CHECKStoreHouseModel *storeHouse;

@end

@interface CHECKDeliveryInfoModel : NSObject

@property (nonatomic, assign) NSInteger deliveryType;
/// 短信接收 or 快递运输
@property (nonatomic, copy) NSString *deliveryName;

@property (nonatomic, copy) NSString *deliveryTips;

@property (nonatomic, copy) NSArray <CHECKSkuItemModel *> *skuItemList;

@end

@interface CHECKVenderModel : NSObject

@property (nonatomic, copy) NSString *venderName;

@property (nonatomic, strong) NSNumber *venderId;

@property (nonatomic, strong) NSNumber *shopId;

@property (nonatomic, copy) NSString *shopName;

@property (nonatomic, copy) NSArray <CHECKDeliveryInfoModel *> *deliveryInfoList;

@end

@interface CHECKAmountSummaryModel : NSObject
/// 商品总价
@property (nonatomic, strong) NSNumber *totalAmount;
/// 总价优惠，如：优惠了 5 元
@property (nonatomic, strong) NSNumber *totalDiscountAmount;
/// 运费
@property (nonatomic, strong) NSNumber *freightAmount;
/// 运费优惠
@property (nonatomic, strong) NSNumber *freightDiscountAmount;
/// 实付（优惠后的价格）
@property (nonatomic, strong) NSNumber *totalRealAmount;
/// 优惠券价格
@property (nonatomic, strong) NSNumber *voucherDiscountAmount;

@property (nonatomic, strong) NSNumber *suitPromoDiscountAmount;

@property (nonatomic, strong) NSNumber *simplePromoDiscountAmount;

@end

@interface CHECKPayTypeModel : NSObject

@property (nonatomic, copy) NSString *paymentTypeId;
/// 支付方式，如：在线支付
@property (nonatomic, copy) NSString *paymentTypeName;

@property (nonatomic, assign) BOOL selected;

@property (nonatomic, assign) BOOL supported;
/// 不支持的原因
@property (nonatomic, copy) NSString *unAvailableToast;
/// 不支持的 sku 列表
@property (nonatomic, copy) NSArray <NSString *> *notSupportedSkuList;

@end

@interface CHECKPaymentInfoModel : NSObject

@property (nonatomic, copy) NSArray <CHECKPayTypeModel *> *payTypeList;

@end

@interface CHECKSelectedVoucherModel : NSObject

@property (nonatomic, copy) NSArray <NSNumber *> *selectedVoucherIds;

@property (nonatomic, assign) NSInteger quantity;

@property (nonatomic, assign) NSInteger discountAmount;

@property (nonatomic, copy) NSString *tips;

@end

@interface CHECKPointsRule : NSObject

@property (nonatomic, assign) BOOL canUsePoints;

@property (nonatomic, copy) NSString *cannotUseContent;

@property (nonatomic, assign) NSInteger totalPoints;

@property (nonatomic, assign) NSInteger minUsePoints;

@property (nonatomic, assign) NSInteger maxUsePoints;

@property (nonatomic, copy) NSString *ruleContent;
/// 抵扣金额转化率，如：100  代表 100 积分抵扣 1 元
@property (nonatomic, assign) NSInteger conversionRate;

@end

@interface CHECKDetailModel : NSObject

@property (nonatomic, copy) NSArray <CHECKVenderModel *> *venderList;

@property (nonatomic, copy) NSArray <CHECKSkuItemModel *> *soldOutSkuList;

@property (nonatomic, copy) NSArray *limitedBuySkuList;

@property (nonatomic, strong) JDTAddressModel *userAddress;

@property (nonatomic, strong) CHECKAmountSummaryModel *amountSummary;

@property (nonatomic, strong) CHECKPaymentInfoModel *paymentInfo;

@property (nonatomic, strong) CHECKSelectedVoucherModel *selectedVoucher;

@property (nonatomic, copy) NSString *checkoutId;

@property (nonatomic, strong) CHECKPointsRule *pointsRule;

@end

NS_ASSUME_NONNULL_END
