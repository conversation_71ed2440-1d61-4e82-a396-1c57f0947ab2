//
//  CHECKProductListExpressCell.m
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/10.
//

#import "CHECKProductListExpressCell.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@interface CHECKProductListExpressCell ()

@property (nonatomic, strong) UIImageView *imgView;

@property (nonatomic, strong) UILabel *titleLabel;

@end

@implementation CHECKProductListExpressCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self.contentView addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(18);
        make.centerY.mas_equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(18, 18));
    }];
    
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.imgView.mas_right).offset(2);
        make.right.equalTo(self.contentView.mas_right).offset(-18);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    self.imgView.image = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_FILL_SMALL imageSize:CGSizeMake(18.f, 18.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    self.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
}

#pragma mark - Getter and Setter
- (void)setDelivery:(CHECKDeliveryInfoModel *)delivery {
    _delivery = delivery;
    self.titleLabel.text = [NSString stringWithFormat:@" %@", delivery.deliveryName];
}

- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [[UIImageView alloc] init];
    }
    return _imgView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
    }
    return _titleLabel;
}

@end
