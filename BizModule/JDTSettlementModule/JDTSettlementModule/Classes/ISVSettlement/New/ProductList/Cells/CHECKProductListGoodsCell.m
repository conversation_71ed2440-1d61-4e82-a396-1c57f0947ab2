//
//  CHECKProductListGoodsCell.m
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/10.
//

#import "CHECKProductListGoodsCell.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@import JDISVKAUIKitModule;

@interface CHECKProductListGoodsCell ()

@property (nonatomic, strong) UIImageView *imgView;

@property (nonatomic, strong) UILabel *nameLabel;

@property (nonatomic, strong) UILabel *specLabel;

@property (nonatomic, strong) KAPriceLabel *priceLabel;

@property (nonatomic, strong) UILabel *numLabel;

@property (nonatomic, strong) UILabel *noteLabel;

@property (nonatomic, strong) UITextField *noteTextField;

@property (nonatomic, strong) UIView *dividerView;

@end

@implementation CHECKProductListGoodsCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self.contentView addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(18);
        make.top.equalTo(self.contentView.mas_top).offset(16);
        make.size.mas_equalTo(CGSizeMake(72, 72));
    }];
    
    [self.contentView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.imgView.mas_right).offset(12);
        make.right.equalTo(self.contentView.mas_right).offset(-18);
        make.top.equalTo(self.imgView);
        make.height.mas_equalTo(@20);
    }];
    
//    self.imgView.image = [UIImage systemImageNamed:@"bag.fill"];
//    self.imgView.tintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.nameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.nameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    [self.contentView addSubview:self.specLabel];
    [self.specLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.nameLabel);
        make.top.equalTo(self.nameLabel.mas_bottom);
        make.height.mas_equalTo(@20);
    }];
    
    self.specLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.specLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    
    [self.contentView addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.bottom.equalTo(self.imgView.mas_bottom);
        make.height.mas_equalTo(@20);
    }];
    
    [self.contentView addSubview:self.numLabel];
    [self.numLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.nameLabel.mas_right);
        make.left.equalTo(self.priceLabel.mas_right);
        make.bottom.equalTo(self.imgView.mas_bottom);
        make.height.mas_equalTo(@20);
    }];
    
    self.numLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.numLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"];
    
    [self.numLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.priceLabel setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.contentView addSubview:self.noteLabel];
    [self.noteLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.imgView);
        make.right.equalTo(self.nameLabel.mas_right);
        make.top.equalTo(self.imgView.mas_bottom).offset(12);
        make.height.mas_equalTo(@20);
    }];
    
    self.noteLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.noteLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"];
    self.noteLabel.text = @"留言";
    
    [self.contentView addSubview:self.noteTextField];
    [self.noteTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.noteLabel);
        make.top.equalTo(self.noteLabel.mas_bottom).offset(4);
        make.height.mas_equalTo(@20);
//        make.bottom.equalTo(self.contentView.mas_bottom).offset(8);
    }];
    
    self.noteTextField.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.noteTextField.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.noteTextField.placeholder = @"选填，请先和商家沟通确认";
    
    [self.contentView addSubview:self.dividerView];
    [self.dividerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.noteLabel);
        make.height.mas_equalTo(@0.5);
        make.bottom.equalTo(self.contentView);
    }];
    
    self.dividerView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
}

#pragma mark - Getter and Setter
- (void)setSkuItem:(CHECKSkuItemModel *)skuItem {
    _skuItem = skuItem;
    // TODO:Juice 补充商品兜底图
    [self.imgView jdcd_setImage:skuItem.imgUrl placeHolder:[UIImage imageNamed:@""] contentMode:UIViewContentModeScaleAspectFit];
    self.nameLabel.text = skuItem.skuName;
    NSMutableArray *specValues = [NSMutableArray array];
    for (CHECKProductSpecModel *spec in skuItem.productSpecs) {
        if (spec.value.length > 0) {
            [specValues addObject:spec.value];
        }
    }
    self.specLabel.text = [specValues componentsJoinedByString:@","];
    [self.priceLabel configTextWithPrice:skuItem.salePrice.floatValue type:KAPriceTypeP2 colorType:@"#C9"];
    self.numLabel.text = [NSString stringWithFormat:@"x%@", @(skuItem.num).stringValue];
}

- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [[UIImageView alloc] init];
    }
    return _imgView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
    }
    return _nameLabel;
}

- (UILabel *)specLabel {
    if (!_specLabel) {
        _specLabel = [[UILabel alloc] init];
    }
    return _specLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
    }
    return _priceLabel;
}

- (UILabel *)numLabel {
    if (!_numLabel) {
        _numLabel = [[UILabel alloc] init];
    }
    return _numLabel;
}

- (UILabel *)noteLabel {
    if (!_noteLabel) {
        _noteLabel = [[UILabel alloc] init];
    }
    return _noteLabel;
}

- (UITextField *)noteTextField {
    if (!_noteTextField) {
        _noteTextField = [[UITextField alloc] init];
    }
    return _noteTextField;
}

- (UIView *)dividerView {
    if (!_dividerView) {
        _dividerView = [[UIView alloc] init];
    }
    return _dividerView;
}

@end
