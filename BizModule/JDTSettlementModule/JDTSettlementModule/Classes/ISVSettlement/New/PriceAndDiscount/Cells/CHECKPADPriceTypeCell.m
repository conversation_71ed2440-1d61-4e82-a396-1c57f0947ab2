//
//  CHECKPADPriceTypeCell.m
//  JDTSettlementModule
//
//  Created by lvchenzhu.1 on 2025/6/11.
//

#import "CHECKPADPriceTypeCell.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "CHECKPriceAndDiscountCellModel.h"

@interface CHECKPADPriceTypeCell ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) KAPriceLabel *priceLabel;

@end

@implementation CHECKPADPriceTypeCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(18);
        make.top.bottom.equalTo(self.contentView);
        make.centerY.equalTo(self.contentView);
    }];
    self.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    
    [self.contentView addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right).offset(-18);
        make.top.bottom.equalTo(self.titleLabel);
        make.left.equalTo(self.titleLabel.mas_right).offset(10);
    }];
    
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.priceLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
}

#pragma mark - Getter and Setter
- (void)setAmountModel:(CHECKAmountSummaryModel *)amountModel {
    _amountModel = amountModel;
    if (self.type == CHECKPriceAndDiscountCellTypeTotalAmount) {
        self.titleLabel.text = @"商品总额";
        [self.priceLabel configTextWithPrice:amountModel.totalAmount.floatValue type:KAPriceTypeP3];
    } else if (self.type == CHECKPriceAndDiscountCellTypeShippingFee) {
        self.titleLabel.text = @"运费";
        [self.priceLabel configTextWithPrice:amountModel.freightAmount.floatValue type:KAPriceTypeP4];
    }
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
    }
    return _titleLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
        _priceLabel.textAlignment = NSTextAlignmentRight;
    }
    return _priceLabel;
}

@end
