//
//  JDISVSettlementShopFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorModule.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import "JDISVSettlementShopFloor.h"
#import "JDISVSettlementShopFloorModel.h"
#import "JDISVSettlementShopFloorSectionViewModel.h"
#import "JDISVSettlementShopFloorBaseCellViewModel.h"
#import "JDISVSettlementShopFloorNetService.h"
#import "JDISVSettlementShopFloorAddressManager.h"
#import "JDISVSettlementShopFloorStepCellViewModel.h"
#import "JDISVSettlementShopFloorRoundCellViewModel.h"
#import "JDISVSettlementShopFloorProductCellViewModel.h"

JDISVRegisterFloorModule(KaCheckShopFloor, JDISVSettlementShopFloorModule);
@interface JDISVSettlementShopFloorModule ()
@property (nonatomic, assign) CGFloat height;

@property (nonatomic, strong) JDISVFloorCommonModel *commonModel;

@end

@implementation JDISVSettlementShopFloorModule
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.sectionViewModels = [NSArray array];
        _storeMapFeature = NO;
    }
    return self;
}

- (UIView *)floorView {
    UIView* v = [[JDISVSettlementShopFloor alloc] init];
    return v;
}


- (CGFloat)floorHeight {
    
    return self.height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

//- (BOOL)isAssociateScrollFloor {
//    return YES;
//}

- (BOOL)ignoreCorner {
    return YES;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.venderArr = [NSArray yy_modelArrayWithClass:[CHECKVenderModel class] json:data[@"data"][@"venderList"]];
    
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];
    NSDictionary *hierarchy = [resultInfo objectForKey:@"hierarchy"];
    NSString *rootKey = [resultInfo objectForKey:@"rootKey"];
    NSDictionary *featureExt = data[@"ext"] ? : @{}; // 获取配置开关
    
    _storeMapFeature = (featureExt[@"storeMapFeature"] && [[featureExt objectForKey:@"storeMapFeature"] isEqualToNumber:@(1)]) ? YES:NO;
    
    self.sourceType = [resultInfo[@"source"] integerValue];
    NSDictionary *routerParams = [resultInfo objectForKey:@"routerParams"];//商详传入的拼团参数
    if (routerParams) {
        [commonModel.commonData setObject:routerParams forKey:@"routerParams"];
    }
    
    self.commonModel = commonModel;
    [[JDISVSettlementShopFloorAddressManager sharedService] saveSettlementConsigneeAddressFromCommonModel:commonModel];
    
    NSMutableArray *floorModels = [NSMutableArray array];
    for (NSDictionary *floorData in floorsArray) {
        JDISVSettlementShopFloorModel *floorModel = [JDISVSettlementShopFloorModel yy_modelWithDictionary:floorData];
        [floorModels addObject:floorModel];
    }
    NSArray *rootFloorKeys = [hierarchy objectForKey:rootKey];
    
    self.sectionViewModels = [NSArray arrayWithArray:[self updateShopFloorsWith:[NSArray arrayWithArray:floorModels] rootRootFloorKeys:rootFloorKeys allFloorKeys:hierarchy commond:commonModel]];
}

- (void)updateRemarkInfo:(NSDictionary *)remarkInfo forVender:(NSString *)venderId {
    NSMutableArray *remarkArray = [NSMutableArray arrayWithArray:[self.commonModel.commonData objectForKey:@"remarkArray"]];
    for (NSDictionary *remarkInfo in remarkArray) {
        NSString *venderId = [remarkInfo objectForKey:@"venderId"];
        if ([venderId isEqualToString:venderId]) {
            [remarkArray removeObject:remarkInfo];
            break;
        }
    }
    [remarkArray addObject:remarkInfo];
    [self.commonModel.commonData setObject:[NSArray arrayWithArray:remarkArray] forKey:@"remarkArray"];
}

// 更新店铺SectionViewModels
// 主接口:更新店铺楼层
- (NSArray<NSArray *> *)updateShopFloorsWith:(NSArray<JDISVSettlementShopFloorModel *> *)floorDatas
                           rootRootFloorKeys:(NSArray<NSString *> *)rootFloorKeys
                                allFloorKeys:(NSDictionary *)allKeysDic
                                     commond:(JDISVFloorCommonModel *)commonModel {
    
    //[[floor,floor, floor], [floor,floor, floor], ...]
    NSMutableArray *venderSections = [NSMutableArray array]; //
    NSMutableArray *allStockOutSkuParamArray = [NSMutableArray array];
    NSMutableArray *shopInfosMTA = [NSMutableArray array];
    for (NSString *uuid in rootFloorKeys) {
        if ([uuid containsString:@"defaultVenderFloor"]) {
            NSArray *floorKeys = (NSArray *)[allKeysDic objectForKey:uuid];
            
            //gard
            if ((floorKeys.count > 0) == NO) continue;
            
            JDISVSettlementShopFloorSectionViewModel *venderSectionViewModel = [[JDISVSettlementShopFloorSectionViewModel alloc] init];
            [venderSectionViewModel updateSectionWithUUIDKey:uuid floorKeys:floorKeys allFloorKeys:allKeysDic allFloorData:floorDatas commonModel:_commonModel];
            if (venderSectionViewModel.floorViewModels.count > 0) {
                [venderSections addObject:venderSectionViewModel.floorViewModels];
            }
            
            // imageDomain
            if (![self.commonModel.commonData.allKeys containsObject:@"imageDomain"]) {
                NSString *imageDomain = venderSectionViewModel.imageDomain ? : @"";
                [self.commonModel.commonData setObject:imageDomain forKey:@"imageDomain"];
            }
            // unselectedAllSkuParam
            if ([venderSectionViewModel unselectedAllSkuParam]) {
                [allStockOutSkuParamArray addObject:[venderSectionViewModel unselectedAllSkuParam]];
            }
            
            //埋点数据
            [shopInfosMTA addObject:venderSectionViewModel.shopInfoMTA ? : @{}];
        }
    }
    [self.commonModel.commonData setObject:[NSArray arrayWithArray:allStockOutSkuParamArray] forKey:@"allStockOutSkuParamArray"];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"shopInfosMTA" object:shopInfosMTA];
    
    
    NSInteger count = 0;
    for (NSArray *sectionArray in venderSections) {
        for (id object in sectionArray) {
            if ([object isKindOfClass:[JDISVSettlementShopFloorProductCellViewModel class]]){
                count += 1;
            }
        }
    }
    [JDISVSettlementAmountFloorNetService sharedService].mainProductCount = count;//设置主品种类数量
    
    if (KSAAPP){
        NSMutableArray *ksaProductFloorData = [NSMutableArray array];
        for (NSArray *sectionArray in venderSections) {
            NSInteger section = [venderSections indexOfObject:sectionArray];
            for (id object in sectionArray) {
                NSInteger index = [sectionArray indexOfObject:object];
                if ([object isKindOfClass:[JDISVSettlementShopFloorRoundCellViewModel class]]){
                    if (section == 0 && index == 0){//第一个圆角Cell
                        [ksaProductFloorData addObject:object];
                    }else if (section == (venderSections.count - 1) && index == (sectionArray.count - 1)){//最后一个圆角Cell
                        [ksaProductFloorData addObject:object];
                    }
                }else{
                    [ksaProductFloorData addObject:object];
                }
            }
        }
        JDISVSettlementShopFloorStepCellViewModel *model = [[JDISVSettlementShopFloorStepCellViewModel alloc] init];
        model.stepNumber = @"3";
        model.stepTitle = SettlementLan(@"checkout_settlment_step_3");
        if (ksaProductFloorData.count > 1){
            [ksaProductFloorData insertObject:model atIndex:1];
        }else{
            [ksaProductFloorData addObject:model];
        }
        return @[ksaProductFloorData];
    }else{
        return [NSArray arrayWithArray:venderSections];
    }
}


- (CGFloat)height {
    CGFloat totalHeight = 0;
//    if (_sectionViewModels && _sectionViewModels.count > 0) {
//        for (NSArray *cellViewModels in _sectionViewModels) {
//            if (cellViewModels && cellViewModels.count > 0) {
//                for (JDISVSettlementShopFloorBaseCellViewModel *vm in cellViewModels) {
//                    totalHeight += vm.height;
//                }
//            }
//        }
//        totalHeight +=  [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"] * (_sectionViewModels.count - 1);
//    }
    for (CHECKVenderModel *vender in self.venderArr) {
        totalHeight += 21;
        for (CHECKDeliveryInfoModel *delivery in vender.deliveryInfoList) {
            if (delivery.skuItemList.count > 3) {
                totalHeight += 71;
            } else {
                for (CHECKSkuItemModel *sku in delivery.skuItemList) {
                    totalHeight += 71;
                    totalHeight += 51;
                }
            }
        }
    }
    return totalHeight;
}

#pragma mark - Singals
// 保存配送方法
- (RACSignal *)loadSaveShipmentSignalWith:(NSDictionary *)param {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        [KASettlementShopFloorNetService requestSaveShipmentWithParam:param sourceFrom:self.sourceType complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:resultInfo];
            }
        }];
        return nil;
    }];
}
@end
