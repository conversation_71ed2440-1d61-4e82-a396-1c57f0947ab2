//
//  JDISVSettlementShopFloorModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementShopFloorTypeModel;

@interface JDISVSettlementShopFloorModel : NSObject
@property (nonatomic, strong) JDISVSettlementShopFloorTypeModel *type; /**< 楼层类型模型-JDKASettlementFloorTypeModel */
@property (nonatomic, copy) NSString *uuid; /**< 楼层uuid-用于识别楼层 */
@property (nonatomic, strong) NSDictionary *info; /**< info:对应属性字典 */
@property (nonatomic, copy) NSArray *partList;
@property (nonatomic, assign) BOOL isFreeAdditionProduct;//是否赠品, 非接口下发的字段，仅iOS端上自己添加用以标记赠品
@end

/* 楼层类型模型 */
@interface JDISVSettlementShopFloorTypeModel : NSObject
@property (nonatomic, copy) NSString *nameSpace; /**< 楼层命名空间:namespace */
@property (nonatomic, copy) NSString *version; /**< 版本 */
@end

/* PartListItem模型 */
@interface JDISVSettlementShopFloorPartListItemModel : NSObject
@property (nonatomic, strong) JDISVSettlementShopFloorTypeModel *type;
@property (nonatomic, strong) NSDictionary *info; /**< info:对应属性字典 */
@end

NS_ASSUME_NONNULL_END
