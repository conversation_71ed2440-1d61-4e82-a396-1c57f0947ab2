//
//  JDISVSettlementShopFloorProductCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorProductCell.h"
#import "JDISVSettlementShopFloorProductCellViewModel.h"
#import "NSAttributedString+ISVSettlementShop.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVSettlementShopFloorProductModel.h"

@interface JDISVSettlementShopFloorProductCell()
@property (weak, nonatomic) IBOutlet UIImageView *coverImageView;
@property (weak, nonatomic) IBOutlet UIImageView *covermaskView;
@property (weak, nonatomic) IBOutlet UILabel *productNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *productPropertyLabel;
@property (weak, nonatomic) IBOutlet KAPriceLabel *priceLabel;
@property (weak, nonatomic) IBOutlet UILabel *countLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *countLabelWidth;
@property (weak, nonatomic) IBOutlet UIView *stockOutView;
@property (weak, nonatomic) IBOutlet UILabel *stockOutLabel;
@property (nonatomic,strong) UILabel *groupBuyLabel;
@property (weak, nonatomic) IBOutlet UILabel *promiseLabel;

@property (nonatomic, strong) JDISVSettlementShopFloorProductCellViewModel *productFloorViewModel;
@end

@implementation JDISVSettlementShopFloorProductCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.covermaskView.layer.masksToBounds = YES;
    self.covermaskView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.covermaskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02f];
    self.coverImageView.layer.masksToBounds = YES;
    self.coverImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.stockOutView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.7f];
    self.stockOutView.layer.masksToBounds = YES;
    self.stockOutView.layer.cornerRadius = 30.f;
    self.stockOutLabel.attributedText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shop_no_product_label")  colorKey:@"#C1" fontKey:@"#T10" weight:UIFontWeightMedium];
    
    self.groupBuyLabel = [[UILabel alloc]init];
    [self.contentView addSubview:_groupBuyLabel];
    self.groupBuyLabel.text = SettlementLan(@"setllement_joinGroupBuy") ;
    self.groupBuyLabel.frame = CGRectMake(0, 0, 100, 100);
//    self.groupBuyLabel.sd_layout.topEqualToView(self.skuTitleLabel).leftEqualToView(self.skuTitleLabel).heightIs(16).widthIs(24);
    
    CGFloat width = [SettlementLan(@"setllement_joinGroupBuy")  jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] constraintsSize:CGSizeMake(100, 16)].width + 8.f;
    [self.groupBuyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.productNameLabel);
        make.leading.equalTo(self.productNameLabel);
        make.height.equalTo(@16);
        make.width.equalTo(@(width));
    }];
    
    self.groupBuyLabel.textColor = [UIColor whiteColor];//标签写死白色，不用设计变量//[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.groupBuyLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium];
    self.groupBuyLabel.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C49"];
    self.groupBuyLabel.textAlignment = NSTextAlignmentCenter;
    self.groupBuyLabel.layer.cornerRadius = 3;
    self.groupBuyLabel.clipsToBounds = YES;
    
    self.promiseLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.promiseLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.promiseLabel.text = @"Expected delivery on 2023-08-09";
//    self.promiseLabel.hidden  = YES;
    
    
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShopFloorProductCellViewModel.class]) {
        self.productFloorViewModel = (JDISVSettlementShopFloorProductCellViewModel *)viewModel;
        
        [self.coverImageView jdcd_setImage:[PlatformService getCompleteImageUrl:_productFloorViewModel.coverImgUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
        if (_productFloorViewModel.isStockOut) {
            self.stockOutView.hidden = NO;
            self.stockOutLabel.hidden = NO;
        } else {
            self.stockOutView.hidden = YES;
            self.stockOutLabel.hidden = YES;
        }
       
        self.productPropertyLabel.attributedText = _productFloorViewModel.productPropertyAttributedString;
        self.priceLabel.attributedText = _productFloorViewModel.priceAttributedString;
        [self.priceLabel configTextWithPrice:self.productFloorViewModel.productModel.price.doubleValue middleLinePrice:self.productFloorViewModel.productModel.basicInfoOriginalPrice.doubleValue];

        self.countLabel.attributedText = _productFloorViewModel.countAttribtuedString;
        self.countLabelWidth.constant = _productFloorViewModel.countLabelWidth;
        self.promiseLabel.hidden = _productFloorViewModel.promiseAttribtuedString ? NO : YES;
        self.promiseLabel.attributedText = _productFloorViewModel.promiseAttribtuedString;
            self.promiseLabel.textAlignment = NSTextAlignmentCenter;
    }
    if(_productFloorViewModel.isPtFlag){
        [self.groupBuyLabel setHidden:NO];
        CGFloat width = [SettlementLan(@"setllement_joinGroupBuy")  jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] constraintsSize:CGSizeMake(100, 16)].width + 8.f;
        NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
        [style setFirstLineHeadIndent:width];
        [style setLineSpacing:4];
        NSMutableAttributedString *gTitle = [[NSMutableAttributedString alloc]initWithAttributedString:_productFloorViewModel.productNameAttributedString];
        [gTitle addAttributes:@{NSParagraphStyleAttributeName:style} range:NSMakeRange(0, 2)];
        self.productNameLabel.attributedText = gTitle;
        self.productNameLabel.numberOfLines = 1;
        self.productNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    }else{
        [self.groupBuyLabel setHidden:YES];
        self.productNameLabel.attributedText = _productFloorViewModel.productNameAttributedString;
        self.productNameLabel.numberOfLines = 1;
        self.productNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    }
}

@end
