//
//  JDISVSettlementShopFloorProductCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementShopFloorAdditionalProductCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, copy) NSString *coverImgUrl;

@property (nonatomic, copy) NSAttributedString *productNameAttributedString;
@property (nonatomic, copy) NSAttributedString  *productPropertyAttributedString;
@property (nonatomic, copy) NSAttributedString *priceAttributedString;
@property (nonatomic, copy) NSAttributedString *countAttribtuedString;
@property (nonatomic, assign) CGFloat countLabelWidth;

@property (nonatomic) BOOL isStockOut;

@property (nonatomic, copy) NSString *skuId;
@property (nonatomic, copy) NSString *skuNum;

- (instancetype)initWithImageDomain:(NSString *)imageDomain;
@end

NS_ASSUME_NONNULL_END
