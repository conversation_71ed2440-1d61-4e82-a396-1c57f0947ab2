//
//  JDISVSettlementShopInfoCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/9/27.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

/**
JDISVSettlementStoreItemViewModel 单个店铺信息CellViewModel(店铺信息 + SKU信息)
*/

@interface JDISVSettlementStoreInfoCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, copy) NSString *storeName; /**< 店铺名称 */
@property (nonatomic, assign) CGFloat storeNameLabelHeight;
@property (nonatomic, strong) NSArray *skuViewModelArray; /**< 商品数组 */
@property (nonatomic, strong) NSDictionary *sectionsModelDic;/**< 商品套数 */
@property (nonatomic, copy) NSString *skuTotalCountText; /**< 总共X件 */
@property (nonatomic, assign) NSInteger skuTotalCount; /**< sku总数 每件Sku的件数之和 */
@property (nonatomic, assign) CGFloat minLineSpace;
@property (nonatomic, assign) CGFloat skuCountViewWidth;

- (instancetype)initWithImageDomain:(NSString *)imageDomain;
@end

NS_ASSUME_NONNULL_END
