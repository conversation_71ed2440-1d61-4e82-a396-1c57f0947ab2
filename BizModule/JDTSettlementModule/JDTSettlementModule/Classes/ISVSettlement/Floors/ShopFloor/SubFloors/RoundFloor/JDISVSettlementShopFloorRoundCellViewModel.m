//
//  JDISVSettlementShopFloorRoundCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorRoundCellViewModel.h"

@implementation JDISVSettlementShopFloorRoundCellViewModel
- (instancetype)initWithCellType:(JDISVSettlementShopFloorRoundCellType)cellType
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShopFloorRoundCell";
        self.cellIdentifier = @"JDISVSettlementShopFloorRoundCell";
        if (KSAAPP){
            self.height = 18.f;
        }else{
            self.height = 12.f;
        }
        _cellType = cellType;
       
        self.margin = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        if (_cellType == JDISVSettlementShopFloorRoundCellTypeR1) {
            _radius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
        } else {
            _radius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R2"];
        }
    }
    return self;
}
@end
