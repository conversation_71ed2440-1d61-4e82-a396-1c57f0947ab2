//
//  JDISVSettlementShopFloorShipmentCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementShopFloorShipmentCellViewModelType) {
    JDISVSettlementShopFloorShipmentCellViewModelUpdateTypeMainInterface = 0 /**< 首次初始化 */
};

@interface JDISVSettlementShopFloorShipmentCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, copy) NSString *venderId;

@property (nonatomic, copy) NSAttributedString *shipmentFloorTitleAttributedString;
@property (nonatomic, assign) CGFloat shipmentFloorTitleLabelWidth;

@property (nonatomic, strong) UIImage *storePickTagImg;
@property (nonatomic, assign) CGFloat storePickTagWidth;

@property (nonatomic, copy) NSAttributedString *shipmentTypeAttributedString;
@property (nonatomic, copy) NSAttributedString *promiseAttributedString;
@property (nonatomic, assign) CGFloat promiseLabelHeight;

@property (nonatomic, assign) BOOL isAllLOC;//是否都是LOC商品
@property (nonatomic, assign) BOOL isStorePickup;//是否门店自提商品
@property (nonatomic, assign) BOOL needHideStandardDelivery;// 是否需要隐藏标准配送信息
@property (nonatomic, assign) BOOL showStoreBusinessTimeAndAddress;//展示门店营业时间和门店地址
@property (nonatomic, copy) NSAttributedString *businessTimeAttributedString;
@property (nonatomic, copy) NSAttributedString *storeDateAttributedString;
@property (nonatomic, copy) NSAttributedString *storeAddressAttributedString;

//- (void)updateWithData:(id)data;
- (void)updateWithData:(id __nullable)data allFloorData:(NSArray *)allFloorData;
@end

NS_ASSUME_NONNULL_END
