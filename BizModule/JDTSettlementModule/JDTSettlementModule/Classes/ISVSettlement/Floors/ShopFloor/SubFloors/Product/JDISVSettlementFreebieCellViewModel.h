//
//  JDISVSettlementShopInfoCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/9/27.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN


@interface JDISVSettlementFreebieCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, copy) NSString *skuId;
@property (nonatomic, copy) NSString *freeProductName; //赠品名称
@property (nonatomic, copy) NSString *freeCount; //赠品数量
@property (nonatomic, copy) NSString *proImgUrl;//商品图片
@property (nonatomic, strong) UIColor *freeNameColor; 
@property (nonatomic, strong) UIColor *freeCountColor;

@property (nonatomic, assign) CGFloat freeTitleWidth;//标题长度
@property (nonatomic, assign) BOOL showFreeTitle;//展示“赠品”标题

@property (nonatomic, assign) BOOL isStockOut;

@end

NS_ASSUME_NONNULL_END
