//
//  JDISVSettlementShopFloorVenderCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorVenderCellViewModel.h"

#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import "JDISVSettlementShopFloorVenderModel.h"

@interface JDISVSettlementShopFloorVenderCellViewModel ()
@property (nonatomic, strong) JDISVSettlementShopFloorVenderModel *model;
@end

@implementation JDISVSettlementShopFloorVenderCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShopFloorVenderCell";
        self.cellIdentifier = @"JDISVSettlementShopFloorVenderCell";
        
        self.height = 0;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:NSDictionary.class]) {
        NSDictionary *dicData = (NSDictionary *)data;
        self.model = [JDISVSettlementShopFloorVenderModel yy_modelWithDictionary:dicData];
        self.venderId = _model.venderId;
        if ([_model.venderName jdcd_validateString]) {
            self.venderNameAttributedString = [[NSAttributedString alloc] initWithString:_model.venderName attributes:@{
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium]
            }];
        } else {
            self.venderNameAttributedString = nil;
        }
        
        if (KSAAPP){
            /**12 + 36 + 9 */
            self.height = 57.f; /**< 12 +  8 + 16 + 9 */
        }else{
            self.height = 32.f; /**< 8 + 16 + 9 */
        }
    } else {
        self.height = 0;
    }
}
@end
