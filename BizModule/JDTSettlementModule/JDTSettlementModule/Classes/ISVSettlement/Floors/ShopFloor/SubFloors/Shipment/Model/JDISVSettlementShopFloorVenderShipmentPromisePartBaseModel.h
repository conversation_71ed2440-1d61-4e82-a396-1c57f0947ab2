//
//  JDISVSettlementShopFloorVenderShipmentPromisePartBaseModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:店铺配送Promise楼层模型 Part 基础信息
 模型命名空间:core.trade-FLR#balance.shipment-P#homePagePromisePart
 模型uuid:
 */

@interface JDISVSettlementShopFloorVenderShipmentPromisePartBaseModel : NSObject
// 内部Key:C-P#homePagePromisePart&basic
@property (nonatomic, copy) NSString *defaultPromiseMsg; /**< defaultPromiseMsg:默认Promise信息 -- 工作日双休日均可配送 */
@end

NS_ASSUME_NONNULL_END
