//
//  JDISVSettlementStoreInfoCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/10.
//

#import "JDISVSettlementStoreInfoCell.h"

//#import "JDISVSettlementProductBaseCell.h"
#import "JDISVSettlementProductCoverCell.h"
//#import "JDISVSettlementProductDetailCell.h"

#import "JDISVSettlementStoreInfoCellViewModel.h"
#import "JDISVSettlementStoreProductViewModel.h"

//#import "JDISVSettlementCellActionParam.h"

//#import "JDISVSettlementMacro.h"

#import "NSAttributedString+ISVSettlementShop.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@interface JDISVSettlementStoreInfoCell () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>
@property (weak, nonatomic) IBOutlet UIView *storeInfoContentView;
@property (weak, nonatomic) IBOutlet UILabel *storeNameLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *storeNameLabelHeight;

@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewTrailing;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewHeight;
@property (weak, nonatomic) IBOutlet UIView *skuCountView;
@property (weak, nonatomic) IBOutlet UILabel *totalCountLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImageView;

@property (nonatomic, strong) JDISVSettlementStoreInfoCellViewModel *viewModel;

@end

@implementation JDISVSettlementStoreInfoCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = UIColor.clearColor;
    self.contentView.backgroundColor = UIColor.clearColor;
//    self.storeInfoContentView.backgroundColor = UIColor.whiteColor;
    //
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    
    [self.collectionView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapToShowProductDetailSheetController)] ];
    
    [self.collectionView registerNib:[UINib nibWithNibName:@"JDISVSettlementProductCoverCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellWithReuseIdentifier:@"JDISVSettlementProductCoverCell"];
//    [self.collectionView registerNib:[UINib nibWithNibName:@"JDISVSettlementProductDetailCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellWithReuseIdentifier:@"JDISVSettlementProductDetailCell"];
    [self.collectionView registerNib:[UINib nibWithNibName:@"UICollectionViewCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellWithReuseIdentifier:@"UICollectionViewCell"];
    
    self.skuCountView.hidden = YES;
    self.rightArrowImageView.contentMode = UIViewContentModeCenter;
    self.rightArrowImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.totalCountLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"];
    self.totalCountLabel.font = [UIFont systemFontOfSize:12.f];
    self.totalCountLabel.text = @"";
    
    [self.skuCountView jd_addTapAction:@selector(tapToShowProductDetailSheetController) withTarget:self];
    
    self.delegate = [[RACSubject alloc] init];
}

- (void)updateCellWithViewModel:(__kindof id)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementStoreInfoCellViewModel.class]) {
        self.viewModel = (JDISVSettlementStoreInfoCellViewModel *)viewModel;
//        self.storeNameLabel.text = self.viewModel.storeName;
        self.totalCountLabel.text = self.viewModel.skuTotalCountText;
//        self.storeNameLabelHeight.constant = self.viewModel.storeNameLabelHeight;
//        if (self.viewModel.skuViewModelArray.count > 1) {
            // 多SKU
            self.collectionViewHeight.constant = 70.f+10+10;
            self.collectionViewTrailing.constant = _viewModel.skuCountViewWidth;
            self.skuCountView.hidden = NO;
//        } else {
//            // 单SKU
//            self.collectionViewHeight.constant = 90.f;
//            self.collectionViewTrailing.constant = 12.f;
//            self.skuCountView.hidden = YES;
//        }
        
        //
        [self.storeInfoContentView jdcd_addRoundedCorners:UIRectCornerTopLeft|UIRectCornerTopRight withRadii:CGSizeMake(8.f, 8.f) viewRect:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width - 12.f*2, self.viewModel.height)];
        
        [self.collectionView reloadData];
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    self.storeNameLabel.text = @"";
    self.skuCountView.hidden = YES;
    self.totalCountLabel.text = @"";
}

# pragma mark - CollectionView

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    if (self.viewModel.skuViewModelArray.count > 0) {
        return 1;
    } else {
        return 0;
    }
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (self.viewModel.skuViewModelArray.count > 0) {
        return self.viewModel.skuViewModelArray.count;
    } else {
        return 0;
    }
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return self.viewModel.minLineSpace;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.item < self.viewModel.skuViewModelArray.count) {
        JDISVSettlementStoreProductViewModel *productViewModel = self.viewModel.skuViewModelArray[indexPath.item];
        return productViewModel.skuCellSize;
    } else {
        return CGSizeMake(0, 0);
    }
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    UICollectionViewCell *cell = nil;
    if (indexPath.item < self.viewModel.skuViewModelArray.count) {
        JDISVSettlementStoreProductViewModel *productViewModel = self.viewModel.skuViewModelArray[indexPath.item];
        cell = [collectionView dequeueReusableCellWithReuseIdentifier:productViewModel.cellIdentifier forIndexPath:indexPath];
    }
    if (cell == nil) {
        cell = [UICollectionViewCell new];
    }
    
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.item < self.viewModel.skuViewModelArray.count) {
        
        if ([cell respondsToSelector:@selector(updateViewWithViewModel:)]) {
            JDISVSettlementStoreProductViewModel *productViewModel = self.viewModel.skuViewModelArray[indexPath.item];
            [cell performSelector:@selector(updateViewWithViewModel:) withObject:productViewModel];
        }
    }
}

//- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
//    [self tapToShowProductDetailSheetController];
//}

# pragma mark - Action
- (void)tapToShowProductDetailSheetController {
    // type: 0 打开商品浮层
    if (self.viewModel.sectionsModelDic.count > 0) {
        [self.delegate sendNext:@{@"type":@(0), @"data": self.viewModel.sectionsModelDic,@"cell_identifier":@"JDISVSettlementStoreInfoCell"}];
    }
    
}
@end
