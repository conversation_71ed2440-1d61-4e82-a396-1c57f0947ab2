//
//  JDISVSettlementShopFloorShipmentCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorShipmentCell.h"
#import "JDISVSettlementShopFloorShipmentCellViewModel.h"

@interface JDISVSettlementShopFloorShipmentCell ()
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImageView;
@property (weak, nonatomic) IBOutlet UILabel *shipmentFloorTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *shipmentFloorTitleLabelWidth;
@property (weak, nonatomic) IBOutlet UIImageView *storePickTag;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *storePickTagWidth;
@property (weak, nonatomic) IBOutlet UILabel *shipmentTypeLabel;
@property (weak, nonatomic) IBOutlet UILabel *promiseLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *promiseLabelHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *KSAInfoBgLeading;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *KSAInfoBgTop;
@property (weak, nonatomic) IBOutlet UIView *bottomLine;
@property (weak, nonatomic) IBOutlet UIView *KSAInfoBg;

//LOC门店自提
@property (weak, nonatomic) IBOutlet UIView *LOCStoreContentView;
@property (weak, nonatomic) IBOutlet UIImageView *businessTimeIcon;
@property (weak, nonatomic) IBOutlet UILabel *businessTimeLabel;
@property (weak, nonatomic) IBOutlet UILabel *businessTipLabel;
@property (weak, nonatomic) IBOutlet UIImageView *storeAddresIcon;
@property (weak, nonatomic) IBOutlet UILabel *storeAddresLabel;
@property (weak, nonatomic) IBOutlet UILabel *picUpAddressLabel;
@property (weak, nonatomic) IBOutlet UIImageView *pickUpaddressArrow;
@property (weak, nonatomic) IBOutlet UIView *pickUpAddressContentView;
@property (nonatomic, strong) JDISVSettlementShopFloorShipmentCellViewModel *viewModel;
@end

@implementation JDISVSettlementShopFloorShipmentCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.rightArrowImageView.contentMode = UIViewContentModeCenter;
    self.rightArrowImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.rightArrowImageView.userInteractionEnabled = YES;
    [self.rightArrowImageView jd_addTapAction:@selector(openShipmentController) withTarget:self];
    
    self.promiseLabel.userInteractionEnabled = YES;
    self.shipmentTypeLabel.userInteractionEnabled = YES;
    self.shipmentTypeLabel.textAlignment = NSTextAlignmentRight;
    
    [self.promiseLabel jd_addTapAction:@selector(openShipmentController) withTarget:self];
    [self.shipmentTypeLabel jd_addTapAction:@selector(openShipmentController) withTarget:self];
    
    [self.LOCStoreContentView jd_addTapAction:@selector(openShipmentController) withTarget:self];
    
    self.LOCStoreContentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
//    self.LOCStoreContentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R3"];
    
    self.pickUpAddressContentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.pickUpAddressContentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R3"];
    
    self.picUpAddressLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.picUpAddressLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    self.pickUpaddressArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    
    
    self.businessTimeIcon.contentMode = UIViewContentModeCenter;
    self.businessTimeIcon.image = [UIImage ka_iconWithName:JDIF_ICON_CLOCK_LINE_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.storeAddresIcon.contentMode = UIViewContentModeCenter;
    self.storeAddresIcon.image = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
//    self.bottomLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@""];
    self.bottomLine.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"#E0E0E0"];
    self.bottomLine.hidden = !(KSAAPP);
    if (KSAAPP){
        self.promiseLabel.textAlignment = NSTextAlignmentLeft;
        self.shipmentTypeLabel.textAlignment = NSTextAlignmentLeft;
    }else{
        self.promiseLabel.textAlignment = NSTextAlignmentRight;
        self.shipmentTypeLabel.textAlignment = NSTextAlignmentRight;
    }
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShopFloorShipmentCellViewModel.class]) {
        
        if (KSAAPP){
            self.KSAInfoBgTop.constant = 12.f + 20.f + 12.f;
            self.KSAInfoBgLeading.constant = 18.f;
        }else{
            self.KSAInfoBgTop.constant = 12.f;
            self.KSAInfoBgLeading.constant = 18.f + _viewModel.shipmentFloorTitleLabelWidth + 4.f + _viewModel.storePickTagWidth;
        }
        
        self.viewModel = (JDISVSettlementShopFloorShipmentCellViewModel *)viewModel;
        self.shipmentFloorTitleLabel.attributedText = _viewModel.shipmentFloorTitleAttributedString;
        self.shipmentFloorTitleLabelWidth.constant = _viewModel.shipmentFloorTitleLabelWidth;
        
        if (_viewModel.storePickTagImg) {
            self.storePickTag.hidden = NO;
            self.storePickTag.image = _viewModel.storePickTagImg;
            self.storePickTagWidth.constant = _viewModel.storePickTagWidth;
        } else {
            self.storePickTag.hidden = YES;
            self.storePickTagWidth.constant = 0;
        }
        
        self.shipmentTypeLabel.attributedText = _viewModel.shipmentTypeAttributedString;
        if (_viewModel.promiseAttributedString) {
            self.promiseLabel.hidden = NO;
            self.promiseLabelHeight.constant = _viewModel.promiseLabelHeight;
            self.promiseLabel.attributedText = _viewModel.promiseAttributedString;
        } else {
            self.promiseLabel.hidden = YES;
            self.promiseLabelHeight.constant = 0;
        }
        
        if (self.viewModel.showStoreBusinessTimeAndAddress) {
            self.LOCStoreContentView.hidden = NO;
            self.KSAInfoBg.hidden = YES;
            self.businessTimeLabel.attributedText = _viewModel.businessTimeAttributedString;
            self.businessTipLabel.attributedText = _viewModel.storeDateAttributedString;
            self.storeAddresLabel.attributedText = _viewModel.storeAddressAttributedString;
        }else{
            self.LOCStoreContentView.hidden = YES;
            self.KSAInfoBg.hidden = NO;
        }

        if (ISPresell && ISFirstMoenyFlag == NO) {//预售付尾款
            self.rightArrowImageView.hidden = YES;
        }else {
            self.rightArrowImageView.hidden = NO;
        }
    }
}


- (void)openShipmentController {
    if (ISPresell && ISFirstMoenyFlag == NO) //预售付尾款,不可点击配送楼层
        return;
    
    NSString *venderId = @"";
    if ([_viewModel.venderId jdcd_validateString]) {
        venderId = [_viewModel.venderId copy];
    }
    [self.delegate sendNext:@{@"cell_identifier":_viewModel.cellIdentifier,
                              @"action_type": @"open_shipment_controller",
                              @"venderId":venderId
    }];
}

@end
