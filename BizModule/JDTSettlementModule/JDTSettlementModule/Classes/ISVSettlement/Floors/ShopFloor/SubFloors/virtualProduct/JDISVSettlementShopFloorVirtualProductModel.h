//
//  JDISVSettlementShopFloorProductModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:店铺虚拟组套商品楼层模型
 模型命名空间:"core.trade-FLR#balance.product-M#virtualCommodityFloor",
 模型uuid:virtualCommodityFloor
 */

@interface JDISVSettlementShopFloorVirtualProductModel : NSObject
// 内部Key:C-M#retailProductFloor&colorSize
// 内部Key:C-M#virtualProductFloor&basic
@property (nonatomic, copy) NSString *skuName; /**< skuName:SKU名称 */
//@property (nonatomic, copy) NSString *skuCoverImageUrl; /**< imageUrl:需拼上域名 */
//@property (nonatomic, copy) NSString *colVenderType; /**< colVenderType:*/
@property (nonatomic, copy) NSString *skuCoverImageUrl; /**< imageUrl:需拼上域名 */
@property (nonatomic, copy) NSString *skuId; /**< skuId */
//@property (nonatomic, copy) NSString *numMsg;/**< "1件/套  ×1" */
@property (nonatomic, copy) NSString *priceShow;/**< "￥67.80" */
@property (nonatomic, copy) NSString *price;/**< "67.80" */
// 内部Key:C-M#productFloor&core
/**< 1:单品 2:一般套装 3:虚拟组套 4:总价促销（满赠）5:总价促销（满返）6:赠品 7:附件 8:延保 9:服务+ */
@property (nonatomic, copy) NSString *modelType; /**< modelType */

@end

NS_ASSUME_NONNULL_END
