//
//  JDISVSettlementShopFloorProductModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorAdditionalProductModel.h"

@implementation JDISVSettlementShopFloorAdditionalProductModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"firstLevelCategoryId" : @"C-M#retailProductFloor&category.firstCategoryId",
        @"secondlevelCategoryId": @"C-M#retailProductFloor&category.secondCategoryId",
        @"thirdLevelCategoryId": @"C-M#retailProductFloor&category.thirdCategoryId",
        @"colorName": @"C-M#retailProductFloor&colorSize.colorName",
        @"sizeName": @"C-M#retailProductFloor&colorSize.sizeName",
        @"numMsg": @"C-M#retailProductFloor&numInfo.numMsg",
        @"num": @"C-M#retailProductFloor&numInfo.num",
        @"stockStatusCode": @"C-M#retailProductFloor&stock.stockStatusCode",
        @"stockStatus": @"C-M#retailProductFloor&stock.stockStatus",
        @"skuName": @"C-M#retailProductFloor&basic.skuName",
        @"skuCoverImageUrl": @"C-M#retailProductFloor&basic.imageUrl",
        @"colVenderType": @"C-M#retailProductFloor&basic.colVenderType",
        @"skuId": @"C-M#retailProductFloor&basic.skuId",
        @"productType": @"C-M#retailProductFloor&basic.productType",
        @"price": @"C-M#retailProductFloor&price.price",
        @"priceShow": @"C-M#retailProductFloor&price.priceShow",
        @"modelType": @"C-M#productFloor&core.modelType"
    };
}
@end
