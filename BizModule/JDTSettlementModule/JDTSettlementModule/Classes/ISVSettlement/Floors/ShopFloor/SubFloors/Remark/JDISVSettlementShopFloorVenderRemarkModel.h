//
//  JDISVSettlementShopFloorVenderRemarkModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:店铺留言楼层模型
 模型命名空间:core.trade-FLR#balance.remark-M#defaultRemarkFloor
 模型uuid:defaultRemarkFloor
 */
@interface JDISVSettlementShopFloorVenderRemarkModel : NSObject
// 内部Key:C-M#remarkFloor&basic
@property (nonatomic) BOOL isShow; /**< isShow:是否展示 */
// 内部Key:C-D#venderRemark&vender
@property (nonatomic, copy) NSArray *relationUUIDs; /**< relationUUIDs:门店留言作为Key回传 */
@end

NS_ASSUME_NONNULL_END
