//
//  JDISVSettlementLastMoneyView.m
//  JDISVSettlementModule
//
//  Created by ext.songjian6 on 2022/5/10.
//

#import "JDISVLastMoneyView.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
@interface JDISVLastMoneyView()
@property (nonatomic,strong) UILabel *introduceLabel;
@property (nonatomic,strong) UIButton *closeBtn;
@property (nonatomic,strong) UILabel *shopNameLabel;
@property (nonatomic,strong) UILabel *lastMoneyTitleLabel;
@property (nonatomic,strong) UILabel *lastMoneyDetailLabel;
@property (nonatomic,strong) UILabel *firstMoneyTitleLabel;
@property (nonatomic,strong) UILabel *firstMoneyDetailLabel;
@property (nonatomic,strong) UIButton *understandBtn;
@property (nonatomic,strong) UIImageView *shopImgView;
//@property (nonatomic,strong) UIView *lineView;

@property (nonatomic,strong) UIView *alphaView;
@end

@implementation JDISVLastMoneyView

//初始化
-(instancetype)init{
    self = [super init];
    if (self) {
        UILabel *introduceLabel = [[UILabel alloc]init];
        self.introduceLabel = introduceLabel;
//        self.lineView = [[UIView alloc]init];
        
//        UIButton *closeBtn = [[UIButton alloc]init];
//        self.closeBtn = closeBtn;
        UILabel *shopNameLabel = [[UILabel alloc]init];
        self.shopNameLabel = shopNameLabel;
        UILabel *lastMoneyTitleLabel = [[UILabel alloc]init];
        self.lastMoneyTitleLabel = lastMoneyTitleLabel;
        UILabel *lastMoneyDetailLabel = [[UILabel alloc]init];
        self.lastMoneyDetailLabel = lastMoneyDetailLabel;
        UILabel *firstMoneyTitleLabel = [[UILabel alloc]init];
        self.firstMoneyTitleLabel = firstMoneyTitleLabel;
        UILabel *firstMoneyDetailLabel = [[UILabel alloc]init];
        self.firstMoneyDetailLabel = firstMoneyDetailLabel;
        UIButton *understandBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [understandBtn renderB3];
        self.understandBtn = understandBtn;
//        self.understandBtn.layer.cornerRadius = 15;
//        [self.understandBtn.layer masksToBounds];
        UIImageView *shopImgView = [[UIImageView alloc]init];
        shopImgView.layer.cornerRadius = 6;
        shopImgView.layer.masksToBounds = YES;
        self.shopImgView = shopImgView;
//        [self sd_addSubviews:@[introduceLabel,closeBtn,shopNameLabel,lastMoneyTitleLabel,lastMoneyDetailLabel,firstMoneyTitleLabel,firstMoneyDetailLabel,understandBtn,shopImgView,self.lineView]];
        [self addSubview:introduceLabel];
//        [self addSubview:closeBtn];
        [self addSubview:shopNameLabel];
        [self addSubview:lastMoneyTitleLabel];
        [self addSubview:lastMoneyDetailLabel];
        [self addSubview:firstMoneyTitleLabel];
        [self addSubview:firstMoneyDetailLabel];
        [self addSubview:understandBtn];
        [self addSubview:shopImgView];
//        [self addSubview:_lineView];
        
        [self setBaseStyle];
        
        UIView *alphaView = [[UIView alloc]initWithFrame:[UIApplication sharedApplication].keyWindow.bounds];
        self.alphaView = alphaView;
        alphaView.backgroundColor = [UIColor blackColor];
        alphaView.alpha = 0.65;
        [[UIApplication sharedApplication].keyWindow addSubview:alphaView];
        
        [[UIApplication sharedApplication].keyWindow addSubview:self];
        [self mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo([UIApplication sharedApplication].keyWindow);
            make.width.equalTo(@(582/2));
            make.height.equalTo(@(477/2));
        }];
        
        [self setHidden:YES];
        [self.alphaView setHidden:YES];
        
    }
    return self;
}

//size
-(void)setBaseStyle{
    
    self.introduceLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
    self.introduceLabel.text = SettlementLan(@"checkout_pre_sale_dialog_title") ;
    self.introduceLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.introduceLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightSemibold];
    self.shopNameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.shopNameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightSemibold];
//    [self.closeBtn setBackgroundImage:[UIImage settlementModuleImageNamed:@"close"] forState:UIControlStateNormal];
    self.closeBtn.backgroundColor = [UIColor redColor];
    [self.closeBtn addTarget:self action:@selector(dismiss) forControlEvents:UIControlEventTouchUpInside];
//    [self.closeBtn jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
//        [self.delegate sendNext:nil];
//    }];
    

    //[[[UIColor alloc]initWithRed:0/255.0 green:1/255.0 blue:0/255.0 alpha:1]initWithRed:153/255.0 green:153/255.0 blue:153/255.0 alpha:1]
    self.lastMoneyTitleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.lastMoneyTitleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
    
    self.lastMoneyDetailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.lastMoneyDetailLabel.font = _lastMoneyTitleLabel.font;
    
    self.firstMoneyTitleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.firstMoneyTitleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
    
    
    self.firstMoneyDetailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.firstMoneyDetailLabel.font = _firstMoneyTitleLabel.font;
    
//    self.lineView.backgroundColor = [[UIColor alloc]initWithRed:244/255.0 green:244/255.0 blue:244/255.0 alpha:1];
    
    [self.understandBtn setTitle:SettlementLan(@"setllement_i_known")  forState:UIControlStateNormal];
    
//    self.understandBtn.layer.cornerRadius = 15;
    //todo
//    [self.understandBtn setupAutoSizeWithHorizontalPadding:49/2 buttonHeight:30];
    
//    [self.understandBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
//    self.understandBtn.backgroundColor = [[UIColor alloc]initWithRed:239/255.0 green:77/255.0 blue:45/255.0 alpha:1];
    
    
//    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
//    gradientLayer.cornerRadius = 15;
//    [gradientLayer masksToBounds];
//    gradientLayer.colors = @[(__bridge id)[[UIColor alloc]initWithRed:239/255.0 green:77/255.0 blue:45/255.0 alpha:1].CGColor, (__bridge id)[[UIColor alloc]initWithRed:209/255.0 green:41/255.0 blue:26/255.0 alpha:1].CGColor];
//    gradientLayer.locations = @[@0.5, @1.0];
//    gradientLayer.startPoint = CGPointMake(0, 0);
//    gradientLayer.endPoint = CGPointMake(1.0, 0);
//    gradientLayer.frame = CGRectMake(0, 0, 210/2, 60/2);
//    [self.understandBtn.layer addSublayer:gradientLayer];
//    [self.understandBtn bringSubviewToFront:self.understandBtn.titleLabel];
//
//
//    self.understandBtn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    
    [self.understandBtn addTarget:self action:@selector(dismiss) forControlEvents:UIControlEventTouchUpInside];
    self.layer.cornerRadius = 12;
    [self.layer masksToBounds];
    
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
//
}

//origin with data
-(void)updateUIWithData:(NSDictionary *)data{

    [self.introduceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(24);
            make.centerX.equalTo(self);
    }];

    [self.shopNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.introduceLabel.mas_bottom).offset(18);
        make.leading.equalTo(self).offset(24);
        make.centerX.equalTo(self);
    }];
    
    [self.shopImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.shopNameLabel);
        make.top.equalTo(self.shopNameLabel.mas_bottom).offset(18);
        make.width.equalTo(@70);
        make.height.equalTo(@70);
    }];
    
    [self.lastMoneyTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.shopImgView.mas_trailing).offset(12);
        make.top.equalTo(self.shopNameLabel.mas_bottom).offset(23);
        make.width.equalTo(@46);
        make.height.equalTo(@16);
    }];
    
    [self.lastMoneyDetailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.mas_trailing).offset(-24);
        make.centerY.equalTo(self.lastMoneyTitleLabel);
    }];
    
    [self.firstMoneyTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.shopImgView.mas_trailing).offset(12);
        make.top.equalTo(self.lastMoneyTitleLabel.mas_bottom).offset(12);
        make.width.equalTo(@74);
        make.height.equalTo(@16);
    }];
    
    [self.firstMoneyDetailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.mas_trailing).offset(-24);
        make.centerY.equalTo(self.firstMoneyTitleLabel);
    }];

    [self.understandBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom).offset(-18);
        make.centerX.equalTo(self);
        make.width.equalTo(@(210/2));
        make.height.equalTo(@(60/2));
    }];
   
    NSString *venderName = data[@"venderName"];
    NSString *coverImgUrl = data[@"coverImgUrl"];
    NSString *depositAmount = data[@"depositAmount"];
    NSString *endPayment = data[@"endPayment"];
    
    self.shopNameLabel.text = venderName;
    self.lastMoneyTitleLabel.text = SettlementLan(@"checkout_final_money_label") ;
    
    self.firstMoneyTitleLabel.text = SettlementLan(@"checkout_front_money_label") ;
    self.firstMoneyDetailLabel.text = [NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag],depositAmount.floatValue];
    self.lastMoneyDetailLabel.text = [NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag],endPayment.floatValue];
    
//    [self.shopImgView jdcd_setImage:self.shopImgStr];
//    self.shopImgView.backgroundColor = [UIColor blackColor];
    
    
    [self.shopImgView jdcd_setImage:coverImgUrl placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    
}

- (void)dismiss{
    self.hidden = YES;
    self.alphaView.hidden = YES;
}

- (void)show{
    self.hidden = NO;
    self.alphaView.hidden = NO;
}


@end

//MVVM RAC + Model View ViewModel
