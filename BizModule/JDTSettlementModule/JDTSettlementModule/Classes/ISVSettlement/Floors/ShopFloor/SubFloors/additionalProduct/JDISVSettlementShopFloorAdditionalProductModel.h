//
//  JDISVSettlementShopFloorProductModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:店铺换购商品楼层模型
 模型命名空间:core.trade-FLR#balance.product-M#additionalProductFloor
 模型uuid:additionalProductFloor
 */

@interface JDISVSettlementShopFloorAdditionalProductModel : NSObject
// 内部Key:C-M#retailProductFloor&category
@property (nonatomic, copy) NSString *firstLevelCategoryId; /**< firstCategoryId:一级类目Id */
@property (nonatomic, copy) NSString *secondlevelCategoryId; /**< secondCategoryId:二级类目Id */
@property (nonatomic, copy) NSString *thirdLevelCategoryId; /**< thirdCategoryId:三级类目 */
// 内部Key:C-M#retailProductFloor&colorSize
@property (nonatomic, copy) NSString *colorName; /**< colorName:颜色 */
@property (nonatomic, copy) NSString *sizeName; /**< sizeName:尺寸 */
// 内部Key:C-M#retailProductFloor&numInfo
@property (nonatomic, copy) NSString *numMsg; /**< numMsg:数量信息×1 */
@property (nonatomic, copy) NSString *num; /**< num:1 */
// 内部Key:C-M#retailProductFloor&stock
@property (nonatomic, copy) NSString *stockStatusCode; /**< stockStatusCode:库存状态码 库存状态 33:有货 34:无货 */
@property (nonatomic, copy) NSString *stockStatus; /**< stockStatus:库存状态 */
// 内部Key:C-M#retailProductFloor&basic
@property (nonatomic, copy) NSString *skuName; /**< skuName:SKU名称 */
@property (nonatomic, copy) NSString *skuCoverImageUrl; /**< imageUrl:需拼上域名 */
@property (nonatomic, copy) NSString *colVenderType; /**< colVenderType:*/
@property (nonatomic, copy) NSString *skuId; /**< skuId */
/**< 1:单品 11:满返主商品 13:满赠主商品 4:一般套装 5:虚拟套装实商品 2:赠品 */
@property (nonatomic, assign) NSInteger productType; /**< productType */
// 内部Key:C-M#retailProductFloor&price
@property (nonatomic, copy) NSString *price; /**< price:价格不带币种符号 */
@property (nonatomic, copy) NSString *priceShow; /**< priceShow:价格带币种符号 */
// 内部Key:C-M#productFloor&core
/**< 1:单品 2:一般套装 3:虚拟组套 4:总价促销（满赠）5:总价促销（满返）6:赠品 7:附件 8:延保 9:服务+ */
@property (nonatomic, copy) NSString *modelType; /**< modelType */
//@property (nonatomic, copy) NSString *discount;
//@property (nonatomic, copy) NSString *rePrice;
//@property (nonatomic, copy) NSString *jdPrice;
//@property (nonatomic, assign) BOOL addFlag;
//@property (nonatomic, copy) NSString *addPrice;
@end

NS_ASSUME_NONNULL_END
