//
//  JDISVSettlementShopFloorProductCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorProductCellViewModel.h"
#import "JDISVSettlementShopFloorProductModel.h"
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>
#import "NSAttributedString+ISVSettlementShop.h"

@interface JDISVSettlementShopFloorProductCellViewModel()
@property (nonatomic, copy) NSString *imageDomain;
@end

@implementation JDISVSettlementShopFloorProductCellViewModel
- (instancetype)initWithImageDomain:(NSString *)imageDomain
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShopFloorProductCell";
        self.cellIdentifier = @"JDISVSettlementShopFloorProductCell";
//        if (KSAAPP){
//            self.height = 120.f;//18
//        }else{
//            self.height = 108.f;//9
//        }
        self.height = 108.f;//9
        _imageDomain = [imageDomain copy];
    }
    return self;
}

- (void)updateWithData:(id)data commonModel:(JDISVFloorCommonModel *)commonModel {
    
    if ([data isKindOfClass:NSDictionary.class]) {
        NSDictionary *productDataDic = (NSDictionary *)data;
        self.productModel = [JDISVSettlementShopFloorProductModel yy_modelWithDictionary: productDataDic];
        self.skuId = _productModel.skuId;
        self.skuNum = _productModel.num;
        if ([_productModel.stockStatusCode isEqualToString:@"34"]) {
            // 无货
            self.isStockOut = YES;
        } else {
            self.isStockOut = NO;
        }
        
        self.coverImgUrl = [NSString stringWithFormat:@"%@%@", _imageDomain, _productModel.skuCoverImageUrl];
        
        if ([_productModel.skuName jdcd_validateString]) {
            NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
                paragraphStyle.lineSpacing = 4;
            self.productNameAttributedString = [[NSAttributedString alloc] initWithString:_productModel.skuName attributes:@{NSParagraphStyleAttributeName:paragraphStyle,
                NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
            }];
        } else {
            self.productNameAttributedString = [[NSAttributedString alloc] initWithString:@"   "];
        }
        
        NSMutableArray *propertyInfos = [NSMutableArray array];
        if ([_productModel.sizeName jdcd_validateString]) {
            [propertyInfos addObject:_productModel.sizeName];
        }
        
        if ([_productModel.colorName jdcd_validateString]) {
            [propertyInfos addObject:_productModel.colorName];
        }
        
        
        NSString *propertyInfo = @"";
        if (propertyInfos.count > 0) {
            propertyInfo = [propertyInfos componentsJoinedByString:@" | "];
        }
        if ([propertyInfo jdcd_validateString]) {
            self.productPropertyAttributedString = [[NSAttributedString alloc] initWithString:propertyInfo attributes:@{
                NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]
            }];
        } else {
            self.productPropertyAttributedString = nil;
        }
        
//        if ([_productModel.numMsg jdcd_validateString]) {
//            self.countAttribtuedString = [[NSAttributedString alloc] initWithString:_productModel.numMsg attributes:@{
//                NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
//                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]
//            }];
//            self.countLabelWidth = ceil([_productModel.numMsg jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 14.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].width);
//        }else{
            if ([_productModel.num jdcd_validateString]) {
                NSString *num = [NSString stringWithFormat:SettlementLan(@"checkout_settlment_qty"),_productModel.num];
                self.countAttribtuedString = [[NSAttributedString alloc] initWithString:num attributes:@{
                    NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],
                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]
                }];
                self.countLabelWidth = ceil([num jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 14.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].width);
            } else {
                self.countAttribtuedString = nil;
                self.countLabelWidth = 0;
            }
//        }
        
    
        if ([_productModel.priceShow jdcd_validateString]) {
            NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
            [priceAttributedString KA_renderWithPriceStr:_productModel.priceShow type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
            self.priceAttributedString = [priceAttributedString copy];
        } else if ([_productModel.price jdcd_validateString]) {
            NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
            [priceAttributedString KA_renderWithPriceStr:_productModel.price type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
            self.priceAttributedString = [priceAttributedString copy];
        } else {
            self.priceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_no_price")  colorKey:@"#C9" fontKey:@"#T7"];
        }
        
        //拼团
        self.isPtFlag = _productModel.ptFlag;
        if (_productModel.ptFlag) {
            if ([_productModel.ptPrice jdcd_validateString]) {
                NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
                [priceAttributedString KA_renderWithPriceStr:_productModel.ptPrice type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
                self.priceAttributedString = [priceAttributedString copy];
            } else {
                self.priceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_no_price")  colorKey:@"#C9" fontKey:@"#T7"];
            }
            
            //拼团全局传参
            NSMutableDictionary *routerParams = [commonModel.commonData objectForKey:@"routerParams"];
            if (routerParams && [routerParams objectForKey:@"taskId"]) {
                NSMutableDictionary *groupBuyDic = [[NSMutableDictionary alloc] init];
                [groupBuyDic setObject:[routerParams objectForKey:@"taskId"] forKey:@"taskId"];
                [groupBuyDic setObject:@"1" forKey:@"sourceType"];
//                [groupBuyDic setObject:@(true) forKey:@"ptFlag"];
                [groupBuyDic setObject:self.skuId ? : @"" forKey:@"skuId"];
                [groupBuyDic setObject:self.skuNum ? : @"" forKey:@"skuNum"];
                [commonModel.commonData setObject:groupBuyDic forKey:@"groupBuyParams"];
            }
            
            //拼团全局标识
            [JDISVSettlementAmountFloorNetService sharedService].ISGroupBuyCapacity = 1;
        }
        
        
        if ([_productModel.promiseDateStr jdcd_validateString]){
            self.promiseAttribtuedString = [[NSAttributedString alloc] initWithString:_productModel.promiseDateStr attributes:@{
                NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]
            }];
        }else{
            self.promiseAttribtuedString = nil;
        }
        
    }
    
}
@end
