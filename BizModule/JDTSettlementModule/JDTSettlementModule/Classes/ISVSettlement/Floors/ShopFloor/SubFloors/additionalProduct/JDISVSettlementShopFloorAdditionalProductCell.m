//
//  JDISVSettlementShopFloorProductCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorAdditionalProductCell.h"
#import "JDISVSettlementShopFloorAdditionalProductCellViewModel.h"
#import "NSAttributedString+ISVSettlementShop.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
@interface JDISVSettlementShopFloorAdditionalProductCell()
@property (weak, nonatomic) IBOutlet UIImageView *coverImageView;
@property (weak, nonatomic) IBOutlet UIImageView *covermaskView;
@property (weak, nonatomic) IBOutlet UILabel *productNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *productPropertyLabel;
@property (weak, nonatomic) IBOutlet UILabel *priceLabel;
@property (weak, nonatomic) IBOutlet UILabel *countLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *countLabelWidth;
@property (weak, nonatomic) IBOutlet UIView *stockOutView;
@property (weak, nonatomic) IBOutlet UILabel *stockOutLabel;
@property (nonatomic,strong) UILabel *groupBuyLabel;

@property (nonatomic, strong) JDISVSettlementShopFloorAdditionalProductCellViewModel *productFloorViewModel;
@end

@implementation JDISVSettlementShopFloorAdditionalProductCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.covermaskView.layer.masksToBounds = YES;
    self.covermaskView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.covermaskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02f];
    self.coverImageView.layer.masksToBounds = YES;
    self.coverImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.stockOutView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.7f];
    self.stockOutView.layer.masksToBounds = YES;
    self.stockOutView.layer.cornerRadius = 30.f;
    self.stockOutLabel.attributedText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shop_no_product_label")  colorKey:@"#C7" fontKey:@"#T7" weight:UIFontWeightSemibold];
    
    self.groupBuyLabel = [[UILabel alloc]init];
    [self.contentView addSubview:_groupBuyLabel];
    self.groupBuyLabel.text = SettlementLan(@"setllement_exchanged_purchase") ;
    self.groupBuyLabel.frame = CGRectMake(0, 0, 100, 100);
//    self.groupBuyLabel.sd_layout.topEqualToView(self.skuTitleLabel).leftEqualToView(self.skuTitleLabel).heightIs(16).widthIs(24);
    [self.groupBuyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.productNameLabel);
        make.leading.equalTo(self.productNameLabel);
        make.height.equalTo(@18);
        make.width.equalTo(@28);
    }];
    
    self.groupBuyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d"];
    self.groupBuyLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium];
    self.groupBuyLabel.backgroundColor = [[[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d"] colorWithAlphaComponent:0.07];
    self.groupBuyLabel.textAlignment = NSTextAlignmentCenter;
    self.groupBuyLabel.layer.cornerRadius = 4;
    self.groupBuyLabel.clipsToBounds = YES;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShopFloorAdditionalProductCellViewModel.class]) {
        self.productFloorViewModel = (JDISVSettlementShopFloorAdditionalProductCellViewModel *)viewModel;
        
        [self.coverImageView jdcd_setImage:[PlatformService getCompleteImageUrl:_productFloorViewModel.coverImgUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
        if (_productFloorViewModel.isStockOut) {
            self.stockOutView.hidden = NO;
            self.stockOutLabel.hidden = NO;
        } else {
            self.stockOutView.hidden = YES;
            self.stockOutLabel.hidden = YES;
        }
       
//        self.productPropertyLabel.attributedText = _productFloorViewModel.productPropertyAttributedString;
        self.priceLabel.attributedText = _productFloorViewModel.priceAttributedString;
        self.countLabel.attributedText = _productFloorViewModel.countAttribtuedString;
        self.countLabelWidth.constant = _productFloorViewModel.countLabelWidth;
    }
//    if(ISGroupBuy){
        [self.groupBuyLabel setHidden:NO];
        NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
        [style setFirstLineHeadIndent:36];
        [style setLineSpacing:4];
        NSMutableAttributedString *gTitle = [[NSMutableAttributedString alloc]initWithAttributedString:_productFloorViewModel.productNameAttributedString];
        [gTitle addAttributes:@{NSParagraphStyleAttributeName:style} range:NSMakeRange(0, 2)];
        self.productNameLabel.attributedText = gTitle;
        self.productNameLabel.numberOfLines = 2;
        self.productNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
//    }else{
//    self.productNameLabel.attributedText = _productFloorViewModel.productNameAttributedString;
//    }
}

@end
