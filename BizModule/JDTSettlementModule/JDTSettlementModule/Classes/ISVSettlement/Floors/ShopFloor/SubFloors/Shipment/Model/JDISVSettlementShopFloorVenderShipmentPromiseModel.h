//
//  JDISVSettlementShopFloorVenderShipmentPromiseModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:店铺配送Promise楼层模型
 模型命名空间:core.trade-FLR#balance.shipment-M#defaultPromiseHomePageFloor
 模型uuid:defaultPromiseHomePageFloor
 */
@interface JDISVSettlementShopFloorVenderShipmentPromiseModel : NSObject
// 内部Key:C-M#defaultPromiseHomePageFloor&label
@property (nonatomic, copy) NSString *promiseLabel; /**< promiseLabel:可选门店自提标签 */
@end

NS_ASSUME_NONNULL_END
