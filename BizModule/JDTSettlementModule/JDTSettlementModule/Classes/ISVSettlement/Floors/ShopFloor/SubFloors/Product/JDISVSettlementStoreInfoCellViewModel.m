//
//  JDISVSettlementShopInfoCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/9/27.
//

#import "JDISVSettlementStoreInfoCellViewModel.h"
#import "JDISVSettlementStoreProductViewModel.h"
#import "JDISVSettlementShopFloorProductModel.h"
#import "JDISVSettlementShopFloorVirtualProductModel.h"
#import "JDISVSettlementShopFloorModel.h"

static NSString * const kJDISVSettmentShopFloorProductInfoFloorPrefix = @"retailProductFloor";//正常商品楼层
static NSString * const kJDISVSettmentShopFloorVirtualCommodityProductItemFloorPrefix = @"virtualCommodityFloor";//虚拟商品楼层

//#import "JDISVSettlementMacro.h"

@interface JDISVSettlementStoreInfoCellViewModel ()
@property (nonatomic, copy) NSString *imageDomain;
@end

@implementation JDISVSettlementStoreInfoCellViewModel
- (instancetype)initWithImageDomain:(NSString *)imageDomain
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementStoreInfoCell";
        self.cellIdentifier = @"JDISVSettlementStoreInfoCell";
        self.height = 0;
        _skuViewModelArray = [NSArray array];
        _skuTotalCount = 0;
        _storeNameLabelHeight = 0;
        _imageDomain = [imageDomain copy];
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:NSArray.class]) {
        NSArray *virtualSections = (NSArray *)data;
        
//        NSInteger updateType = dataArr.count > 1 ? 1:0;
        self.skuTotalCount = 0; // 虚拟组套商品总数
        NSInteger updateType = 1;
//        NSInteger virtualTotalCount = virtualSections.count;// 商品总套数
        NSMutableArray *totalProductViewModels = [NSMutableArray array];
        NSMutableArray *virtualSectionDatas = [NSMutableArray array];
        NSMutableArray *virtualSectionHeaderDatas = [NSMutableArray array];
        for (NSArray *vittualFloors in virtualSections) {
            NSMutableArray *productViewModels = [NSMutableArray array];
            NSMutableDictionary *vittualSectionHeader = @{}.mutableCopy;
//            NSInteger skuTotalCount = 0;//虚拟组套单套商品总数
            NSString *suitNum = 0;//虚拟组套总套数
            NSString *num = 0;//虚拟组套子品总数
            for (JDISVSettlementShopFloorModel *floorModel in vittualFloors) {
                JDISVSettlementStoreProductViewModel *skuViewModel = [[JDISVSettlementStoreProductViewModel alloc] initWithImageDomain:_imageDomain];
                if ([floorModel.uuid containsString:kJDISVSettmentShopFloorProductInfoFloorPrefix]) {
                    JDISVSettlementShopFloorProductModel *productModel = [JDISVSettlementShopFloorProductModel yy_modelWithDictionary:floorModel.info];
                    [skuViewModel updateWith:productModel forType:updateType];
                    [productViewModels addObject:skuViewModel];
                    if (totalProductViewModels.count < 3) {//最多展示3个商品
                        [totalProductViewModels addObject:skuViewModel];
                    }
//                    skuTotalCount = skuTotalCount + productModel.num.integerValue;
                } else if ([floorModel.uuid containsString:kJDISVSettmentShopFloorVirtualCommodityProductItemFloorPrefix]) {
                    JDISVSettlementShopFloorVirtualProductModel *productModel = [JDISVSettlementShopFloorVirtualProductModel yy_modelWithDictionary:floorModel.info];
                    [skuViewModel updateWith:productModel forType:updateType];
                    if (skuViewModel.skuPriceAttrText) {
                        [vittualSectionHeader setObject:skuViewModel.skuPriceAttrText forKey:@"priceShow"];//虚拟组套总价格
                    }
                } else if ([floorModel.uuid containsString:@"virtualProductFloor"]) {
                    //suitNum:虚拟组套的套数，num：虚拟组套的子品总数
                    suitNum = floorModel.info[@"C-M#virtualProductFloor&basic"][@"suitNum"] ? : @"0";
                    num = floorModel.info[@"C-M#virtualProductFloor&basic"][@"num"] ? : @"0";
                    NSString *suitNumShow = [NSString stringWithFormat:SettlementLan(@"checkout_product_list_count") ,suitNum];
                    [vittualSectionHeader setObject:suitNumShow forKey:@"suitNum"];
                }
            }
            self.skuTotalCount = self.skuTotalCount + num.integerValue;
            [virtualSectionHeaderDatas addObject:vittualSectionHeader];
            [virtualSectionDatas addObject:productViewModels];
        }
        

//        self.skuTotalCountText = [NSString stringWithFormat:SettlementLan(@"checkout_product_list_count") , @(_skuTotalCount)];
        self.skuTotalCountText = [NSString stringWithFormat:SettlementLan(@"checkout_product_list_count") , @(self.skuTotalCount)];
        self.skuViewModelArray = [totalProductViewModels copy];
        self.sectionsModelDic = @{@"headers":virtualSectionHeaderDatas,@"sections":virtualSectionDatas,@"totalCount":@(self.skuTotalCount)};
        
        
        if ([_skuTotalCountText jdcd_validateString] && _skuViewModelArray.count > 1) {
            self.skuCountViewWidth = ceil([self.skuTotalCountText jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[UIFont systemFontOfSize:12.f] maxNumberOfLines:1].width) + 12.f + 28.f;
            if (self.skuCountViewWidth < 84.f) {
                self.skuCountViewWidth = 84.f;
            }
        } else {
            self.skuCountViewWidth = 0;
        }
        
        // height
//        if (self.skuViewModelArray.count > 1) {
//            self.height = self.height + 70.f + 9.f;
//            self.minLineSpace = 6.f;
//        } else {
//            self.height = self.height + 90.f + 9.f;
//            self.minLineSpace = 0;
//        }
        if (self.skuViewModelArray.count > 0) {
            self.height = 90.f + 9.f+9.f;
            self.minLineSpace = 10.f;
        }
    }
    
}
@end

