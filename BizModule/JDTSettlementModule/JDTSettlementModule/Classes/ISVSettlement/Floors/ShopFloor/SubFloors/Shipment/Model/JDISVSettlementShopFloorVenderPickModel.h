//
//  JDISVSettlementShopFloorVenderPickModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:店铺配送Promise楼层模型
 模型命名空间:cn_ybxt_b2c-FLR#balance.shipment-M#venderPickFloor
 模型uuid:venderPickFloor
 */

@interface JDISVSettlementShopFloorVenderPickModel : NSObject
// 内部Key:VCYBB2C-M#venderPickFloor&basic
@property (nonatomic, copy) NSString *venderStoreName; /**< venderStoreName:商家门店名(门店自提) */
@property (nonatomic, copy) NSString *venderStoreDate; /**< venderStoreDate:商家门店自提时间(在营业时间内可凭码取货) */
@property (nonatomic, copy) NSString *businessHours; // 营业时间，eg："9:00-18:00",
@property (nonatomic, copy) NSString *storeAddress; // 门店地址 eg：”京东大厦“
//内部Key:VCYBB2C-M#venderPickFloor&stock
@property (nonatomic, copy) NSString *storeStockStatus; // "1"
@end

NS_ASSUME_NONNULL_END
