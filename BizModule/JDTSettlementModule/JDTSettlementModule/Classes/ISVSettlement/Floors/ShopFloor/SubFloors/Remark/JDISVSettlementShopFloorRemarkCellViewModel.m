//
//  JDISVSettlementShopFloorRemarkCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorRemarkCellViewModel.h"

#import "JDISVSettlementShopFloorModel.h"
#import "JDISVSettlementShopFloorVenderRemarkModel.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import "NSAttributedString+ISVSettlementShop.h"
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>


static const NSUInteger kJDISVSettlementShopFloorRemarkCellViewModelMaxCount = 50;

@interface JDISVSettlementShopFloorRemarkCellViewModel()
@property (nonatomic, copy) NSString *remark;
@property (nonatomic, strong) JDISVSettlementShopFloorVenderRemarkModel *model;
@property (nonatomic, copy) NSArray *relationUUIDs;
@property (nonatomic,assign) BOOL isShow;
@end

@implementation JDISVSettlementShopFloorRemarkCellViewModel
- (instancetype)initWithVenderId:(NSString *)venderId
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShopFloorRemarkCell";
        self.cellIdentifier = @"JDISVSettlementShopFloorRemarkCell";
        
        self.reamarkTitleAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_message_label")  colorKey:@"#C7" fontKey:@"#T7"];
        self.remarkTitleLabelWidth = ceil([_reamarkTitleAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
        /**placeholderRTL适配段落布局*/
        BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
        NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
        NSDictionary *attrs = @{NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSParagraphStyleAttributeName:style};
        self.reamarkTFPlaceholderAttributedString = [[NSAttributedString alloc] initWithString:SettlementLan(@"checkout_leave_msg_hint") attributes:attrs];
        
        self.venderId = venderId;
        self.remark = @"";
        self.remarkTFTintColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        self.height = 0;
        
        self.delegate = [[RACSubject alloc] init];
    }
    return self;
}

- (void)updateWithData:(id)data commonModel:(JDISVFloorCommonModel *)commonModel {
    
    if (ISPresell && ISFirstMoenyFlag == NO) {
        //预售付尾款，仅展示接口的remark, 不可点击
        self.disableUserInteraction = YES;
        JDISVSettlementShopFloorModel *floorModel = (JDISVSettlementShopFloorModel *)data;
        self.remark = floorModel.info[@"C-M#remarkFloor&basic"][@"remark"];
        if ([self.remark jdcd_validateString]) {
            self.height = KSAAPP ? 71.f : 38.f;
        }else{
            self.height = 0;
        }
        return;
    }
    
    NSArray *remarkArray = [commonModel.commonData objectForKey:@"remarkArray"];
    NSDictionary *venderRemarkInfo = nil;
    for (NSDictionary *remarkObj in remarkArray) {
        NSString *venderId = [remarkObj objectForKey:@"venderId"];
        if ([venderId isEqualToString:_venderId]) {
            venderRemarkInfo = [NSDictionary dictionaryWithDictionary:remarkObj];
        }
    }
    
    if (venderRemarkInfo == nil) {
        // 首次
        if ([data isKindOfClass:JDISVSettlementShopFloorModel.class]) {
            JDISVSettlementShopFloorModel *floorModel = (JDISVSettlementShopFloorModel *)data;
            self.model = [JDISVSettlementShopFloorVenderRemarkModel yy_modelWithDictionary:floorModel.info];
            if (_model.isShow) {
                self.height = KSAAPP ? 71.f : 38.f;
            } else {
                self.height = 0;
            }
            self.isShow = _model.isShow;
            if (_model.relationUUIDs.count > 0) {
                self.relationUUIDs = [NSArray arrayWithArray:_model.relationUUIDs];
            } else {
                self.relationUUIDs = [NSArray array];
            }
            
            // 初始化记录
            NSMutableDictionary *remarkDic = [NSMutableDictionary dictionary];
            if ([_venderId jdcd_validateString]) {
                [remarkDic addEntriesFromDictionary:@{@"venderId": _venderId}];
            }
            [remarkDic addEntriesFromDictionary:@{@"isShow": @(_isShow)}];
            if (self.relationUUIDs) {
                [remarkDic addEntriesFromDictionary:@{@"relationUUIDs": _relationUUIDs}];
            }
            
            [remarkDic addEntriesFromDictionary:@{@"remark": @""}];
            NSMutableArray *remarkArrayTemp = [NSMutableArray arrayWithArray:remarkArray];
            [remarkArrayTemp addObject:remarkDic];
            [commonModel.commonData setObject:remarkArrayTemp forKey:@"remarkArray"];
        }
    } else {
        // 其他接口刷新
        self.isShow = [[venderRemarkInfo objectForKey:@"isShow"] boolValue];
        self.relationUUIDs = [NSArray arrayWithArray:[venderRemarkInfo objectForKey:@"relationUUIDs"]];
        self.remark = [venderRemarkInfo objectForKey:@"remark"];
        self.model = [[JDISVSettlementShopFloorVenderRemarkModel alloc] init];
        self.model.isShow = _isShow;
        self.model.relationUUIDs = [NSArray arrayWithArray:_relationUUIDs];
        if (_model.isShow) {
            self.height = KSAAPP ? 71.f : 38.f;
        } else {
            self.height = 0;
        }
    }
}

- (void)updateWithUserInputRemark:(NSString *)message {
    if ([message jdcd_validateString]) {
        if (message.length > kJDISVSettlementShopFloorRemarkCellViewModelMaxCount || [self containIllegalCharacter:message]) {
            message = _remark;
        }
        self.remark = [message copy];
    } else {
        self.remark = @"";
    }
}



- (NSAttributedString *)remarkAttributedString {
    if ([_remark jdcd_validateString]) {
        return [[NSAttributedString alloc] ka_settlement_shop_initWithString:_remark colorKey:@"#C7" fontKey:@"#T7"];
    } else {
        return [[NSAttributedString alloc] ka_settlement_shop_initWithString:@"" colorKey:@"#C7" fontKey:@"#T7"];
    }
}

- (NSDictionary *)remarkInfoDictionary {
    NSMutableDictionary *remarkDic = [NSMutableDictionary dictionary];
    if ([_venderId jdcd_validateString]) {
        [remarkDic addEntriesFromDictionary:@{@"venderId": _venderId}];
    }
    [remarkDic addEntriesFromDictionary:@{@"isShow": @(_isShow)}];
    if (self.relationUUIDs) {
        [remarkDic addEntriesFromDictionary:@{@"relationUUIDs": _relationUUIDs}];
    }
    
    if ([_remark jdcd_validateString]) {
        [remarkDic addEntriesFromDictionary:@{@"remark": _remark}];
    } else {
        [remarkDic addEntriesFromDictionary:@{@"remark": @""}];
    }
    
    return [NSDictionary dictionaryWithDictionary:remarkDic];
}

//判断是否存在emoji
- (BOOL)containIllegalCharacter:(NSString *)context {
    NSString* regEx = @"[0-9a-zA-Z\\s\\u4e00-\\u9fa5\\p{P}*. \\|\\\"`]*";
    NSError* error;
    NSRegularExpression* reg = [NSRegularExpression regularExpressionWithPattern:regEx options:kNilOptions error:&error];
        
    NSString* illegalCharacters = [reg stringByReplacingMatchesInString:context options:kNilOptions range:NSMakeRange(0, context.length) withTemplate:@""];
//    NSLog(@"======%@",illegalCharacters);
    BOOL find = illegalCharacters.length>0;
    return find;
}
@end
