//
//  JDISVSettlementShopFloorVenderCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorVenderCell.h"

#import "JDISVSettlementShopFloorVenderCellViewModel.h"

@interface JDISVSettlementShopFloorVenderCell ()
@property (weak, nonatomic) IBOutlet UIImageView *venderIconImgView;
@property (weak, nonatomic) IBOutlet UILabel *venderNameLabel;
@property (weak, nonatomic) IBOutlet UIView *KSABGView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *iconLeading;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomConstraint;


@property (nonatomic, strong) JDISVSettlementShopFloorVenderCellViewModel *viewModel;
@end

@implementation JDISVSettlementShopFloorVenderCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.venderIconImgView.contentMode = UIViewContentModeCenter;
    
    if (KSAAPP){
        self.KSABGView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
        self.KSABGView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        self.KSABGView.hidden = NO;
        self.iconLeading.constant = 32;
        self.topConstraint.constant = 12;
        self.bottomConstraint.constant = 9.f;
    }else{
        self.KSABGView.hidden = YES;
        self.iconLeading.constant = 18;
        self.topConstraint.constant = 0;
        self.bottomConstraint.constant = 0;
    }

}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShopFloorVenderCellViewModel.class]) {
        self.viewModel = (JDISVSettlementShopFloorVenderCellViewModel *)viewModel;
        self.venderIconImgView.image = [UIImage ka_iconWithName:JDIF_ICON_SHOP_LINE imageSize:CGSizeMake(20.f, 20.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
        self.venderNameLabel.attributedText = _viewModel.venderNameAttributedString;
    }
}
@end
