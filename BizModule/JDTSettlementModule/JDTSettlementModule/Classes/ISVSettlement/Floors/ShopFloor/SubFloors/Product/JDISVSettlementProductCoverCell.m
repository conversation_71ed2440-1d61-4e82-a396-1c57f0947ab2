//
//  JDISVSettlementProductCoverCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/10.
//

#import "JDISVSettlementProductCoverCell.h"

#import "JDISVSettlementStoreProductViewModel.h"
#import "NSAttributedString+ISVSettlementShop.h"

@interface JDISVSettlementProductCoverCell()
@property (weak, nonatomic) IBOutlet UIView *coverContentView;
@property (weak, nonatomic) IBOutlet UIImageView *skuCoverImageView;
@property (weak, nonatomic) IBOutlet UIImageView *maskView;
@property (weak, nonatomic) IBOutlet UIImageView *soldOutImageView;
//@property (weak, nonatomic) IBOutlet UIView *stockOutView;
@property (weak, nonatomic) IBOutlet UILabel *stockOutLabel;

@end

@implementation JDISVSettlementProductCoverCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = UIColor.clearColor;
    
    self.coverContentView.backgroundColor = UIColor.clearColor;
    self.backgroundColor = UIColor.clearColor;
    
    self.skuCoverImageView.layer.masksToBounds = YES;
    self.skuCoverImageView.layer.cornerRadius = 8.f;
    
    self.maskView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.03f];
    self.maskView.layer.masksToBounds = YES;
    self.maskView.layer.cornerRadius = 4.f;

    self.soldOutImageView.hidden = YES;
    self.soldOutImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.7f];
    self.soldOutImageView.layer.masksToBounds = YES;
    self.soldOutImageView.layer.cornerRadius = 28.f;
    self.stockOutLabel.attributedText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shop_no_product_label")  colorKey:@"#C7" fontKey:@"#T7" weight:UIFontWeightSemibold];
}

- (void)updateViewWithViewModel:(JDISVSettlementStoreProductViewModel *)viewModel {
    //
    [self.skuCoverImageView jdcd_setImage:viewModel.coverImage];
    
    if (viewModel.isSoldOut) {
        self.soldOutImageView.hidden = NO;
        self.stockOutLabel.hidden = NO;
    } else {
        self.soldOutImageView.hidden = YES;
        self.stockOutLabel.hidden = YES;
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    self.skuCoverImageView.image = nil;
    self.soldOutImageView.hidden = YES;
}

@end
