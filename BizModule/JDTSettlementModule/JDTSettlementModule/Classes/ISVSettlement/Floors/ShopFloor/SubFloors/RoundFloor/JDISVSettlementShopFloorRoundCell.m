//
//  JDISVSettlementShopFloorRoundCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorRoundCell.h"

#import "JDISVSettlementShopFloorRoundCellViewModel.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/UIView+JDCDCorners.h>

@interface JDISVSettlementShopFloorRoundCell ()

@end

@implementation JDISVSettlementShopFloorRoundCell
- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShopFloorRoundCellViewModel.class]) {
        JDISVSettlementShopFloorRoundCellViewModel *cellViewModel = (JDISVSettlementShopFloorRoundCellViewModel *)viewModel;
        if (cellViewModel.cellType == JDISVSettlementShopFloorRoundCellTypeR1) {
            // 显示上圆角
            CGFloat height = cellViewModel.height;
            if (cellViewModel.radius*2 > cellViewModel.height) {
                height = cellViewModel.radius*2;
            }
            [self.contentView jdcd_addRoundedCorners:UIRectCornerTopLeft | UIRectCornerTopRight withRadii:CGSizeMake(cellViewModel.radius, cellViewModel.radius) viewRect:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]) - cellViewModel.margin * 2, height)];
            
        } else {
            // 显示下圆角
            CGFloat height = cellViewModel.height;
            CGFloat capY = 0;
            if (cellViewModel.radius*2 > cellViewModel.height) {
                height = cellViewModel.radius*2;
                capY = -(height - cellViewModel.height);
            }
            [self.contentView jdcd_addRoundedCorners:UIRectCornerBottomLeft | UIRectCornerBottomRight withRadii:CGSizeMake(cellViewModel.radius, cellViewModel.radius) viewRect:CGRectMake(0, capY, CGRectGetWidth([[UIScreen mainScreen] bounds]) - cellViewModel.margin * 2, height)];
        }
    }
}

@end
