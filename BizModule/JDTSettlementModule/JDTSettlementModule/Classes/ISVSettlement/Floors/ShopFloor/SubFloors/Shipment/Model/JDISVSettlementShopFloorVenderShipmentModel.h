//
//  JDISVSettlementShopFloorVenderShipmentModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:店铺配送楼层模型
 模型命名空间:core.trade-FLR#balance.shipment-M#shipmentHomePageFloor
 模型uuid:shipmentHomePageFloor
 */

@interface JDISVSettlementShopFloorVenderShipmentModel : NSObject
// 内部Key:C-M#shipmentHomePageFloor&basic
// 1.京东快递 2. 上门自提 3.三方快递 4. 同城速配 5.快递到车 6.京尊达 7.快递运输 8.门店自取 9.短信接收（针对loc店铺下发）10.集运运输
@property (nonatomic, copy) NSString *shipmentType; /**< shipmentType:配送方式类型 */
// 内部Key:C-M#abstractShipmentFloor&basic
@property (nonatomic) BOOL selectedFlag; /**< selectedFlag:是否选中 */
@property (nonatomic, copy) NSString *shipmentName; /**< ✅ shipmentName:配送名称 */
// 内部Key:C-M#abstractShipmentFloor&core
@property (nonatomic, copy) NSString *shipmentId; // 73:门店 66:快递运输
@end

NS_ASSUME_NONNULL_END
