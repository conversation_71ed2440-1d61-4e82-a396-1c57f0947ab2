//
//  JDISVSettlementShopFloorVenderModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:店铺楼层模型
 模型命名空间:core.trade-FLR#balance.vender-M#defaultVenderFloor
 模型uuid:defaultVenderFloor
 */

@interface JDISVSettlementShopFloorVenderModel : NSObject
// 内部Key:C-M#venderFloor&basic
@property (nonatomic, copy) NSString *venderIconUrl; /**< venderIcon:店铺Icon图标 */
@property (nonatomic) BOOL selfVenderFlag; /**< selfVenderFlag:自营标识 */
// 内部Key:C-M#venderFloor&core
@property (nonatomic, copy) NSString *venderName; /**< 店铺名称 */
@property (nonatomic, copy) NSString *venderId; /**< 店铺Id */
//@property (nonatomic, copy) NSString *storeId; /**<门店Id */
@end

NS_ASSUME_NONNULL_END
