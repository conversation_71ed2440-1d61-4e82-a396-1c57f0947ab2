//
//  JDISVSettlementFreebieCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/10.
//

#import "JDISVSettlementFreebieCell.h"
#import "JDISVSettlementFreebieCellViewModel.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "NSAttributedString+ISVSettlementShop.h"

@interface JDISVSettlementFreebieCell ()
@property (weak, nonatomic) IBOutlet UILabel *freeTitle;
@property (weak, nonatomic) IBOutlet UILabel *freeProductName;
@property (weak, nonatomic) IBOutlet UILabel *freeCount;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tagWidth;
@property (weak, nonatomic) IBOutlet UIImageView *proImg;
@property (weak, nonatomic) IBOutlet UIView *tagBgImg;
@property (weak, nonatomic) IBOutlet UIView *stockView;
@property (weak, nonatomic) IBOutlet UILabel *stockLabel;


@property (nonatomic, strong) JDISVSettlementFreebieCellViewModel *viewModel;

@end

@implementation JDISVSettlementFreebieCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = UIColor.clearColor;
    self.contentView.backgroundColor = UIColor.clearColor;
    self.freeTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.tagBgImg.clipsToBounds = YES;
    self.tagBgImg.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    self.tagBgImg.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d" alpha:0.07];
    
    self.freeTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-d" alpha:1.0];
    self.freeTitle.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.proImg.layer.masksToBounds = YES;
    self.proImg.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.proImg.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02f];
    self.proImg.layer.masksToBounds = YES;
    self.proImg.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    
    
    self.stockView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.7f];
    self.stockView.layer.masksToBounds = YES;
    self.stockView.layer.cornerRadius = 28.f;
    self.stockLabel.attributedText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shop_no_product_label")  colorKey:@"#C1" fontKey:@"#T9" weight:UIFontWeightSemibold];
    
}

- (void)updateCellWithViewModel:(__kindof id)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementFreebieCellViewModel.class]) {
        self.viewModel = (JDISVSettlementFreebieCellViewModel *)viewModel;
        
        
        [self.proImg jdcd_setImage:[PlatformService getCompleteImageUrl:self.viewModel.proImgUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
        self.tagWidth.constant = self.viewModel.freeTitleWidth + 7.0;
        self.freeTitle.text = SettlementLan(@"checkout_settlment_gift");
//        self.freeTitle.hidden = !self.viewModel.showFreeTitle;
        
        self.freeProductName.text = self.viewModel.freeProductName;
        self.freeCount.text = self.viewModel.freeCount;
        
//        self.freeTitle.textColor = self.viewModel.freeNameColor;
        self.freeProductName.textColor = self.viewModel.freeNameColor;
        self.freeCount.textColor = self.viewModel.freeCountColor;
        
        self.stockView.hidden = !self.viewModel.isStockOut;
    }
}
@end
