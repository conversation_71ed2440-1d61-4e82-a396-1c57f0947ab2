//
//  JDISVSettlementStoreProductViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementShopFloorVirtualProductModel;

@interface JDISVSettlementStoreProductViewModel : NSObject

@property (nonatomic, copy) NSString *cellName;
@property (nonatomic, copy) NSString *cellIdentifier;

@property (nonatomic, strong) NSNumber *skuId; /**< skuId */
@property (nonatomic, copy) NSString *coverImage; /**< 商品图 */
@property (nonatomic, strong) NSString *skuTitle; /**< 商品标题 */
@property (nonatomic, strong) NSAttributedString *skuPriceAttrText; /**< 商品价格 */
@property (nonatomic, assign) NSInteger skuCount; /**< 商品数量 */
@property (nonatomic, copy) NSString *skuCountText; /**< 商品数量Text */
@property (nonatomic, assign) CGSize skuCellSize; /**< 商品Cell尺寸 */
@property (nonatomic) BOOL isSoldOut; /**< 是否售空 */
@property (nonatomic,strong) NSString *marketTag;
@property (nonatomic, strong) id skuModel;
@property (nonatomic, copy) NSString *imageDomain;//图片域名

@property (nonatomic,assign) bool isGroupBuy;
- (void)updateWith:(id)data forType:(NSInteger)type;
- (instancetype)initWithImageDomain:(NSString *)imageDomain;

@end

NS_ASSUME_NONNULL_END
