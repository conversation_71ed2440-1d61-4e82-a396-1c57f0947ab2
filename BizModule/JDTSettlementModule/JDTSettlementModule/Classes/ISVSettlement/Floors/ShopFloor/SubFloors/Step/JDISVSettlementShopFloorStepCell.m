//
//  JDISVSettlementShopFloorStepCell.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu12 on 2023/5/30.
//

#import "JDISVSettlementShopFloorStepCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVSettlementShopFloorStepCellViewModel.h"

@interface JDISVSettlementShopFloorStepCell ()
@property (weak, nonatomic) IBOutlet UILabel *stepNumber;
@property (weak, nonatomic) IBOutlet UILabel *stepTitle;

@end

@implementation JDISVSettlementShopFloorStepCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.stepNumber.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.stepNumber.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
    self.stepTitle.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.stepTitle.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorStepCellViewModel *)viewModel{
    self.stepNumber.text = viewModel.stepNumber;
    self.stepTitle.text = viewModel.stepTitle;
}

@end
