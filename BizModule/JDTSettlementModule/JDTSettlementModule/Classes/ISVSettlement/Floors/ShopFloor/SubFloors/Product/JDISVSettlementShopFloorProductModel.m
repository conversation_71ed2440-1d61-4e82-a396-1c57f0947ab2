//
//  JDISVSettlementShopFloorProductModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorProductModel.h"

@implementation JDISVSettlementShopFloorProductModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"firstLevelCategoryId" : @"C-M#retailProductFloor&category.firstCategoryId",
        @"secondlevelCategoryId": @"C-M#retailProductFloor&category.secondCategoryId",
        @"thirdLevelCategoryId": @"C-M#retailProductFloor&category.thirdCategoryId",
        @"colorName": @"C-M#retailProductFloor&colorSize.colorName",
        @"sizeName": @"C-M#retailProductFloor&colorSize.sizeName",
        @"numMsg": @"C-M#retailProductFloor&numInfo.numMsg",
        @"num": @"C-M#retailProductFloor&numInfo.num",
        @"stockStatusCode": @"C-M#retailProductFloor&stock.stockStatusCode",
        @"stockStatus": @"C-M#retailProductFloor&stock.stockStatus",
        @"skuName": @"C-M#retailProductFloor&basic.skuName",
        @"skuCoverImageUrl": @"C-M#retailProductFloor&basic.imageUrl",
        @"colVenderType": @"C-M#retailProductFloor&basic.colVenderType",
        @"skuId": @"C-M#retailProductFloor&basic.skuId",
        @"productType": @"C-M#retailProductFloor&basic.productType",
        @"price": @"C-M#retailProductFloor&price.price",
        @"priceShow": @"C-M#retailProductFloor&price.priceShow",
        @"modelType": @"C-M#productFloor&core.modelType",
        @"ptFlag": @"C-D#promotionInfo&basic.ptFlag",
        @"ptPrice": @"C-D#promotionInfo&basic.ptPrice",
        @"basicInfoPromotionDiscount": @"C-D#promotionInfo&basic.discount",
        @"basicInfoOriginalPrice": @"C-D#promotionInfo&basic.jdPrice",
        @"promiseDateStr":@"VCYBB2C-D#promise&basic.promiseDateStr"
    };
}
@end
