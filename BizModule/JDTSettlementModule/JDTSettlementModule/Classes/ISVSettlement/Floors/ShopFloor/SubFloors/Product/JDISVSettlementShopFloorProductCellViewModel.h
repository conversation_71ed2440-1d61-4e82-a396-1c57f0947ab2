//
//  JDISVSettlementShopFloorProductCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

@class JDISVFloorCommonModel;
@class JDISVSettlementShopFloorProductModel;
NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementShopFloorProductCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, copy) NSString *coverImgUrl;

@property (nonatomic, copy) NSAttributedString *productNameAttributedString;
@property (nonatomic, copy) NSAttributedString  *productPropertyAttributedString;
@property (nonatomic, copy) NSAttributedString *priceAttributedString;
@property (nonatomic, copy) NSAttributedString *countAttribtuedString;
@property (nonatomic, copy) NSAttributedString *promiseAttribtuedString;
@property (nonatomic, assign) CGFloat countLabelWidth;
@property (nonatomic, strong) JDISVSettlementShopFloorProductModel *productModel;

@property (nonatomic) BOOL isStockOut;

@property (nonatomic, copy) NSString *skuId;
@property (nonatomic, copy) NSString *skuNum;

@property (nonatomic) BOOL isPtFlag;//拼团标识

- (instancetype)initWithImageDomain:(NSString *)imageDomain;

- (void)updateWithData:(id)data commonModel:(JDISVFloorCommonModel *)commonModel;
@end

NS_ASSUME_NONNULL_END
