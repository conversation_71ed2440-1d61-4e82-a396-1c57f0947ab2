//
//  JDISVSettlementShopFloorShipmentCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorShipmentCellViewModel.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import "JDISVSettlementShopFloorModel.h"
#import "JDISVSettlementShopFloorVenderPickModel.h"
#import "JDISVSettlementShopFloorVenderShipmentModel.h"
#import "JDISVSettlementShopFloorVenderShipmentPromiseModel.h"
#import "JDISVSettlementShopFloorVenderShipmentPromisePartBaseModel.h"
#import "JDISVSettlementShopFloorAddressModel.h"
#import "NSAttributedString+ISVSettlementShop.h"

static NSString * const kJDISVSettlementShopFloorShipmentCellShipmentHomeFloorPrefix = @"shipmentHomePageFloor";
static NSString * const kJDISVSettlementShopFloorShipmentCellPromiseHomeFloorPrefix = @"defaultPromiseHomePageFloor";
static NSString * const kJDISVSettlementShopFloorShipmentCellVenderPickFloorPrefix = @"venderPickFloor";
static NSString * const kJDISVSettlementShopFloorShipmentCellPromiseHomeFloorBasePartNameSpacePrefix = @"core.trade-FLR#balance.shipment-P#homePagePromisePart";

@interface JDISVSettlementShopFloorShipmentCellViewModel()
@end

@implementation JDISVSettlementShopFloorShipmentCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShopFloorShipmentCell";
        self.cellIdentifier = @"JDISVSettlementShopFloorShipmentCell";
        self.height = 0;
        
        self.shipmentFloorTitleAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_express_label")  colorKey:@"#C7" fontKey:@"#T7"];
        self.shipmentFloorTitleLabelWidth = ceil([_shipmentFloorTitleAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
        
    }
    return self;
}

- (void)updateWithData:(id)data allFloorData:(NSArray *)allFloorData{
    if ([data isKindOfClass:NSArray.class]) {
        //先取出是否全是LOC商品的flag
        for (JDISVSettlementShopFloorModel *floorData in allFloorData) {
            if ([floorData.uuid containsString:@"defaultAddressFloor"]) {
                JDISVSettlementShopFloorModel *addressFloorData = floorData;
                JDISVSettlementShopFloorAddressModel *shopAddressModel = [JDISVSettlementShopFloorAddressModel yy_modelWithDictionary:floorData.info];
                self.isAllLOC = shopAddressModel.allLocShipment;
                break;
            }
        }
         
        NSArray *floorDatas = (NSArray *)data;
        
        // If there is only one delivery method and it is equal to standard delivery, then hide this floor
        NSMutableArray *floorDataArr = [NSMutableArray arrayWithCapacity:0];
        for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
            JDISVSettlementShopFloorVenderShipmentModel *shipmentModel = [JDISVSettlementShopFloorVenderShipmentModel yy_modelWithDictionary:floorData.info];
            NSInteger partListCount  = nil == floorData.partList ? 0 : floorData.partList.count;
            if (1 == partListCount && [shipmentModel.shipmentId isEqualToString:@"66"]) {
                continue;
            }
            [floorDataArr addObject:floorData];
        }
        
        JDISVSettlementShopFloorVenderShipmentModel *shipmentModel;
        JDISVSettlementShopFloorVenderShipmentPromiseModel *promiseModel;
        JDISVSettlementShopFloorVenderPickModel *storePickModel;
        JDISVSettlementShopFloorVenderShipmentPromisePartBaseModel *shipmentPromisePartModel;
        NSInteger partListCount = 0;
        
        for (JDISVSettlementShopFloorModel *floorData in floorDataArr) {
            if ([floorData.uuid containsString:kJDISVSettlementShopFloorShipmentCellShipmentHomeFloorPrefix]) {
                shipmentModel = [JDISVSettlementShopFloorVenderShipmentModel yy_modelWithDictionary:floorData.info];
                continue;
            }
            if ([floorData.uuid containsString:kJDISVSettlementShopFloorShipmentCellPromiseHomeFloorPrefix]) {
                promiseModel = [JDISVSettlementShopFloorVenderShipmentPromiseModel yy_modelWithDictionary:floorData.info];
                NSArray *partListDatas = floorData.partList;
                partListCount = nil == partListDatas ? 0 : partListDatas.count;
                for (JDISVSettlementShopFloorModel *partListData in partListDatas) {
                    if ([partListData.type.nameSpace containsString:kJDISVSettlementShopFloorShipmentCellPromiseHomeFloorBasePartNameSpacePrefix]) {
                        shipmentPromisePartModel = [JDISVSettlementShopFloorVenderShipmentPromisePartBaseModel yy_modelWithDictionary:partListData.info];
                        continue;
                    }
                }
                continue;
            }
            if ([floorData.uuid containsString:kJDISVSettlementShopFloorShipmentCellVenderPickFloorPrefix]) {
                storePickModel = [JDISVSettlementShopFloorVenderPickModel yy_modelWithDictionary:floorData.info];
                continue;
            }
        }
        
        /**
         * The delivery information will be hidden if the following two conditions are met:
         * 1. There is only one delivery method.
         * 2. The shipmentId is equal to 66.
         */
        if ([shipmentModel.shipmentId isEqualToString:@"66"] && 1 == partListCount) {
            self.needHideStandardDelivery = YES;
        }
        
        [self updateShipmentFloorWith:shipmentModel promiseModel:promiseModel venderStorePickMode:storePickModel promisePartModel:shipmentPromisePartModel];
    } else {
        self.height = 0;
    }
}


- (void)updateShipmentFloorWith:(JDISVSettlementShopFloorVenderShipmentModel *)shipmentModel
                    promiseModel:(JDISVSettlementShopFloorVenderShipmentPromiseModel *)promiseModel
             venderStorePickMode:(JDISVSettlementShopFloorVenderPickModel *)storePickModel
               promisePartModel:(JDISVSettlementShopFloorVenderShipmentPromisePartBaseModel *)shipmentPromisePartModel {
    NSString *shipmentTypeName = @"";
    NSString *promiseStr = @"";
    NSString *venderPickTag = @"";
    NSString *businessTimeStr = @"";
    NSString *storeAddressStr = @"";
    
    // 73:门店 66:快递运输
    if ([shipmentModel.shipmentId isEqualToString:@"73"]) {
        self.isStorePickup = YES;
        // 配送方式返回多种的情况下，默认选中快递运输，展示：可选门店自提的提示语，可点击进入配送浮层切换其他配送方式
        if ([storePickModel.venderStoreName jdcd_validateString]) {
            shipmentTypeName = storePickModel.venderStoreName;
        }
        if (storePickModel.venderStoreDate) {
            promiseStr = storePickModel.venderStoreDate;
        }
        if (storePickModel.businessHours) {
            businessTimeStr = storePickModel.businessHours;
        }
        if (storePickModel.storeAddress) {
            storeAddressStr = storePickModel.storeAddress;
        }
        if ([shipmentTypeName jdcd_validateString]) {
            self.shipmentTypeAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:shipmentTypeName colorKey:@"#C7" fontKey:@"#T7"];
        }
        if ([businessTimeStr jdcd_validateString]) {
            self.businessTimeAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:[NSString stringWithFormat:SettlementLan(@"checkout_stores_list_business_time"),businessTimeStr] colorKey:@"#C6" fontKey:@"#T9"];
        }
        if ([promiseStr jdcd_validateString]) {
            self.storeDateAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:[NSString stringWithFormat:@"（%@）",promiseStr] colorKey:@"#C5" fontKey:@"#T9"];
        }
        if ([storeAddressStr jdcd_validateString]) {
            self.storeAddressAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:storeAddressStr colorKey:@"#C6" fontKey:@"#T9"];
        }
        if (self.businessTimeAttributedString && self.storeAddressAttributedString) {
            self.showStoreBusinessTimeAndAddress = YES;
        }
    } else {
        //loc 快递运输
        if (shipmentModel && [shipmentModel.shipmentName jdcd_validateString]) {
            shipmentTypeName = shipmentModel.shipmentName;
        }
        self.shipmentTypeAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:shipmentTypeName colorKey:@"#C7" fontKey:@"#T7"];
        if (shipmentPromisePartModel && [shipmentPromisePartModel.defaultPromiseMsg jdcd_validateString]) {
            promiseStr = shipmentPromisePartModel.defaultPromiseMsg;
        }
    }
    //venderPickTag
    if (promiseModel && [promiseModel.promiseLabel jdcd_validateString]) {
        venderPickTag = promiseModel.promiseLabel;
    }
    if ([venderPickTag jdcd_validateString]) {
        self.storePickTagWidth = ceil([venderPickTag jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].width) + 4.f*2;
        self.storePickTagImg = [self venderStoreTagWithInfo:venderPickTag Frame:CGRectMake(0, 0, _storePickTagWidth, 18.f)];
    } else {
        self.storePickTagImg = nil;
        self.storePickTagWidth = 0;
    }

    //计算总高度
    if (self.showStoreBusinessTimeAndAddress) {//LOC 自提
        //LOC高度计算
        //
        CGFloat maxWidth = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f - 32.f;
        CGFloat bussTimeLabelHeight = [self.businessTimeAttributedString.string jdcd_sizeWithContainer:CGSizeMake(maxWidth, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].height;
        CGFloat timeTipHeight = [self.storeDateAttributedString boundingRectWithSize:CGSizeMake(maxWidth, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size.height;
        CGFloat storeAddHeight = [self.storeAddressAttributedString.string jdcd_sizeWithContainer:CGSizeMake(maxWidth, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].height;
        CGFloat pickheight = 20.f + 4.f + 8.f + bussTimeLabelHeight + timeTipHeight + 8 + storeAddHeight + 8;
        self.height = 12.f + 20.f + 8.f + pickheight + 12.f;
        
        
    } else if ([promiseStr jdcd_validateString]) {
        self.promiseAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:promiseStr colorKey:@"#C5" fontKey:@"#T7"];
        CGFloat maxWidth = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f - self.storePickTagWidth -4.f  - self.shipmentFloorTitleLabelWidth - 20.f;
        if (KSAAPP){
            maxWidth = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f - 24.f  + 4.0f;
        }
        self.promiseLabelHeight = [promiseStr jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(maxWidth, MAXFLOAT)].height + 1.f;
        self.height = 12.f + 20.f + 4.f + self.promiseLabelHeight + 12.f;
        
        if (KSAAPP){
            self.height = 12.f + 20.f + 12.f + 20.f + 4.f + self.promiseLabelHeight + 12.f;
        }
    } else {
        self.promiseAttributedString = nil;
        self.promiseLabelHeight = 0;
        self.height = 12.f + 20.f + 12.f;
        
        if (KSAAPP){
            self.height = 12.f + 20.f + 12.f + 20.f + 12.f;
        }
    }
}

# pragma mark - Private
- (UIImage *)venderStoreTagWithInfo:(NSString *)info Frame:(CGRect)frame {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:frame];
    [defaultTagBtn setTitle:info forState:UIControlStateNormal];
    [defaultTagBtn renderL3WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    
    return [self getImageFromView:defaultTagBtn];
}


- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}
@end
