//
//  JDISVSettlementShopFloorRemarkCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorRemarkCell.h"

#import "JDISVSettlementShopFloorRemarkCellViewModel.h"

#import "UITextField+JDISVSettlementShop.h"

@interface JDISVSettlementShopFloorRemarkCell()
@property (weak, nonatomic) IBOutlet UILabel *remarkTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *remarkTitleLabelWidth;
@property (weak, nonatomic) IBOutlet JDISVSettlementShopFloorRemarkCellTextFiled *remarkTF;
@property (weak, nonatomic) IBOutlet UIView *bottomLine;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *remarkTFTrailing;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *remarkTFTop;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *remarkTFLeading;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *remarkTFHeight;


@property (nonatomic, strong) RACDisposable *textDispose;

@property (nonatomic, strong) JDISVSettlementShopFloorRemarkCellViewModel *viewModel;
@end

@implementation JDISVSettlementShopFloorRemarkCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    
    self.remarkTF.backgroundColor = [UIColor clearColor];
    self.remarkTF.returnKeyType = UIReturnKeyDone;
    self.remarkTF.rightViewMode = UITextFieldViewModeWhileEditing;
    self.bottomLine.backgroundColor = [UIColor jdcd_colorWithHexColorString:@"#E0E0E0"];
    self.bottomLine.hidden = !(KSAAPP);
    
    if (KSAAPP){
        self.remarkTFHeight.constant = 30.f;
        self.remarkTFLeading.constant = 18.f;
        self.remarkTFTrailing.constant = 18.f;
        self.remarkTFTop.constant = 12.f + 20.f + 2.f;
    }else{
        self.remarkTFHeight.constant = 20.f;
        self.remarkTFTop.constant = 12.f;
        self.remarkTFTrailing.constant = 34.f;
    }
    
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShopFloorRemarkCellViewModel.class]) {
        self.viewModel = (JDISVSettlementShopFloorRemarkCellViewModel *)viewModel;
        
        self.remarkTF.userInteractionEnabled = !self.viewModel.disableUserInteraction;
        
        self.remarkTF.tintColor = _viewModel.remarkTFTintColor;
        
        /**RTL适配：需要先配置 textAlignment，再设置placeholder*/
        self.remarkTF.textAlignment = KSAAPP ? NSTextAlignmentLeft : NSTextAlignmentRight;
        self.remarkTF.attributedPlaceholder = _viewModel.reamarkTFPlaceholderAttributedString;
        self.remarkTitleLabel.attributedText = _viewModel.reamarkTitleAttributedString;
        self.remarkTitleLabelWidth.constant = _viewModel.remarkTitleLabelWidth;
        if (!(KSAAPP)){
            self.remarkTFLeading.constant = 18.f + _viewModel.remarkTitleLabelWidth + 12.f;
        }
        
        [self.textDispose dispose];
        @weakify(self)
        self.textDispose = [[[self.remarkTF ka_settlement_shop_inputTextSignal] skip:1] subscribeNext:^(NSString *remark) {
            @strongify(self)
            [self.viewModel updateWithUserInputRemark:remark];
            self.remarkTF.attributedText = [self.viewModel remarkAttributedString];
            [self.delegate sendNext:
             @{
                @"cell_identifier":@"JDISVSettlementShopFloorRemarkCell",
                @"action_type": @"user_input",
                @"venderId":self.viewModel.venderId ? : @"",
                @"data_info":[self.viewModel remarkInfoDictionary] ? : @{}
            }];
        }];
        self.remarkTF.attributedText = [self.viewModel remarkAttributedString];
    }
}
#pragma mark TextField

- (void)textFieldEndEditing:(id)sender {
    [self.remarkTF resignFirstResponder];
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    if ([textField isFirstResponder]) {
        [self.remarkTF resignFirstResponder];
    }
    
    return YES;
}

#pragma mark TextField
- (void)clearTextFieldContent {
    [self.viewModel updateWithData:@"" forType:JDISVSettlementShopFloorRemarkCellViewModelUpdateTypeInput];
    self.remarkTF.attributedText = [self.viewModel remarkAttributedString];
}
@end

@implementation JDISVSettlementShopFloorRemarkCellTextFiled

//- (CGRect)textRectForBounds:(CGRect)bounds {
//    return CGRectMake(bounds.origin.x, bounds.origin.y, bounds.size.width-14, bounds.size.height);
//}
//
//- (CGRect)placeholderRectForBounds:(CGRect)bounds {
//    return CGRectMake(bounds.origin.x, bounds.origin.y, bounds.size.width-14, bounds.size.height);
//}

@end
