//
//  JDISVSettlementStoreProductViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/10.
//

#import "JDISVSettlementStoreProductViewModel.h"
#import "JDISVSettlementShopFloorProductModel.h"
#import "JDISVSettlementShopFloorVirtualProductModel.h"
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>
#import "NSAttributedString+ISVSettlementShop.h"

@interface JDISVSettlementStoreProductViewModel()

@end
@implementation JDISVSettlementStoreProductViewModel
- (instancetype)initWithImageDomain:(NSString *)imageDomain
{
    self = [super init];
    if (self) {
        _imageDomain = [imageDomain copy];
    }
    return self;
}

- (void)updateWith:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementShopFloorProductModel.class]) {
        JDISVSettlementShopFloorProductModel *skuModel = (JDISVSettlementShopFloorProductModel *)data;
        self.skuModel = skuModel;
        
        self.skuId = @(skuModel.skuId.integerValue);
        self.coverImage = [NSString stringWithFormat:@"%@%@", _imageDomain, skuModel.skuCoverImageUrl];
        self.skuTitle = skuModel.skuName;
//        self.isGroupBuy = self.skuModel.isGroupBuy;
//        self.marketTag = self.skuModel.marketTag;
        // 价格
//        if ([_skuModel.price jdcd_validateString]) {
//            NSString *priceText = [NSString stringWithFormat:@"￥%@", self.skuModel.price];
//            NSRange pointRange = [priceText rangeOfString:@"."];
//            //
//            NSMutableAttributedString *priceAttrText = [[NSMutableAttributedString alloc] initWithString:priceText attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:18.f], NSForegroundColorAttributeName: [UIColor bq_red]}];
//            [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(0, 1)];
//            if (pointRange.location != NSNotFound) {
//                // 含有"."
//                NSInteger decimalsLength = priceText.length - pointRange.location; // 小数点后的位数包含小数点
//                if (decimalsLength > 0) {
//                    [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(pointRange.location, decimalsLength)];
//                }
//            }
//            self.skuPriceAttrText = [priceAttrText copy];
//        } else {
//            NSString *priceText = @"￥--";
//            NSMutableAttributedString *priceAttrText = [[NSMutableAttributedString alloc] initWithString:priceText attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:12.f], NSForegroundColorAttributeName: [UIColor bq_red]}];
//            self.skuPriceAttrText = [priceAttrText copy];
//        }
        if ([skuModel.priceShow jdcd_validateString]) {
            NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
            [priceAttributedString KA_renderWithPriceStr:skuModel.priceShow type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
            self.skuPriceAttrText = [priceAttributedString copy];
        } else {
            self.skuPriceAttrText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_no_price")  colorKey:@"#C9" fontKey:@"#T7"];
        }
        
        self.skuCountText = skuModel.numMsg;
//        if (type == 0) {
//            // 单Sku
//            self.skuCellSize = CGSizeMake([UIScreen mainScreen].bounds.size.width - 12.f * 2 * 2 , 90.f);
//            self.cellName = @"JDISVSettlementProductDetailCell";
//            self.cellIdentifier = @"JDISVSettlementProductDetailCell";
//        } else {
            // 多SKU
            self.skuCellSize = CGSizeMake(70.f, 70.f);
            self.cellName = @"JDISVSettlementProductCoverCell";
            self.cellIdentifier = @"JDISVSettlementProductCoverCell";
//        }
        
        // stockStatus 33-有货 | 34-无货
        if (skuModel.stockStatusCode.integerValue == 34) {
            // 34-无货
            self.isSoldOut = YES;
        } else {
            // 33-有货
            self.isSoldOut = NO;
        }
    } else if ([data isKindOfClass:JDISVSettlementShopFloorVirtualProductModel.class]) {
        JDISVSettlementShopFloorVirtualProductModel *skuModel = (JDISVSettlementShopFloorVirtualProductModel *)data;
        self.skuModel = skuModel;
        
        self.skuId = @(skuModel.skuId.integerValue);
        self.coverImage = [NSString stringWithFormat:@"%@%@", _imageDomain, skuModel.skuCoverImageUrl];
        self.skuTitle = skuModel.skuName;
//        self.isGroupBuy = self.skuModel.isGroupBuy;
//        self.marketTag = self.skuModel.marketTag;
        // 价格
//        if ([_skuModel.price jdcd_validateString]) {
//            NSString *priceText = [NSString stringWithFormat:@"￥%@", self.skuModel.price];
//            NSRange pointRange = [priceText rangeOfString:@"."];
//            //
//            NSMutableAttributedString *priceAttrText = [[NSMutableAttributedString alloc] initWithString:priceText attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:18.f], NSForegroundColorAttributeName: [UIColor bq_red]}];
//            [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(0, 1)];
//            if (pointRange.location != NSNotFound) {
//                // 含有"."
//                NSInteger decimalsLength = priceText.length - pointRange.location; // 小数点后的位数包含小数点
//                if (decimalsLength > 0) {
//                    [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(pointRange.location, decimalsLength)];
//                }
//            }
//            self.skuPriceAttrText = [priceAttrText copy];
//        } else {
//            NSString *priceText = @"￥--";
//            NSMutableAttributedString *priceAttrText = [[NSMutableAttributedString alloc] initWithString:priceText attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:12.f], NSForegroundColorAttributeName: [UIColor bq_red]}];
//            self.skuPriceAttrText = [priceAttrText copy];
//        }
        if ([skuModel.priceShow jdcd_validateString]) {
            NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
            [priceAttributedString KA_renderWithPriceStr:skuModel.priceShow type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
            self.skuPriceAttrText = [priceAttributedString copy];
        } else {
            self.skuPriceAttrText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_no_price")  colorKey:@"#C9" fontKey:@"#T7"];
        }
        
//        self.skuCountText = skuModel.numMsg;
//        if (type == 0) {
//            // 单Sku
//            self.skuCellSize = CGSizeMake([UIScreen mainScreen].bounds.size.width - 12.f * 2 * 2 , 90.f);
//            self.cellName = @"JDISVSettlementProductDetailCell";
//            self.cellIdentifier = @"JDISVSettlementProductDetailCell";
//        } else {
            // 多SKU
            self.skuCellSize = CGSizeMake(70.f, 70.f);
            self.cellName = @"JDISVSettlementProductCoverCell";
            self.cellIdentifier = @"JDISVSettlementProductCoverCell";
//        }
        
        // stockStatus 33-有货 | 34-无货
//        if (self.skuModel.stockStatus.integerValue == 34) {
//            // 34-无货
//            self.isSoldOut = YES;
//        } else {
//            // 33-有货
//            self.isSoldOut = NO;
//        }
    }
}

@end

