//
//  JDISVSettlementShopFloorRemarkCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, JDISVSettlementShopFloorRemarkCellViewModelUpdateType) {
    JDISVSettlementShopFloorRemarkCellViewModelUpdateTypeMainInterface = 0, /**< 初始化 */
    JDISVSettlementShopFloorRemarkCellViewModelUpdateTypeInput = -1 /**< 用户输入 */
};

@class JDISVFloorCommonModel;

@interface JDISVSettlementShopFloorRemarkCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, strong) RACSubject *delegate;

@property (nonatomic,assign) BOOL disableUserInteraction;//是否禁止输入框点击
@property (nonatomic, copy) NSAttributedString *reamarkTitleAttributedString;
@property (nonatomic, assign) CGFloat remarkTitleLabelWidth;
@property (nonatomic, copy) NSAttributedString *reamarkTFPlaceholderAttributedString;

// TextField设置
@property (nonatomic, strong) UIColor *remarkTFTintColor;

@property (nonatomic, copy) NSString *venderId;

- (NSAttributedString *)remarkAttributedString;
- (instancetype)initWithVenderId:(NSString *)venderId;
- (void)updateWithData:(id)data commonModel:(JDISVFloorCommonModel *)commonModel;
- (void)updateWithUserInputRemark:(NSString *)message;
- (NSDictionary *)remarkInfoDictionary;
@end

NS_ASSUME_NONNULL_END
