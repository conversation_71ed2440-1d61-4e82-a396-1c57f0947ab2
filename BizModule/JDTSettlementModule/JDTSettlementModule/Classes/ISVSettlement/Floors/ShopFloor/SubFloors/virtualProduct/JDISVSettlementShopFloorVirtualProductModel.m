//
//  JDISVSettlementShopFloorProductModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorVirtualProductModel.h"

@implementation JDISVSettlementShopFloorVirtualProductModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{

        @"skuName": @"C-M#virtualProductFloor&basic.skuName",
//        @"skuCoverImageUrl": @"C-M#retailProductFloor&basic.imageUrl",
//        @"colVenderType": @"C-M#retailProductFloor&basic.colVenderType",
        @"skuCoverImageUrl": @"C-M#virtualProductFloor&basic.imageUrl",
        @"skuId": @"C-M#retailProductFloor&basic.skuId",
//        @"numMsg": @"C-M#virtualProductFloor&basic.numMsg",
        @"priceShow": @"C-M#virtualProductFloor&basic.priceShow",
        @"price": @"C-M#virtualProductFloor&basic.price",
        @"modelType": @"C-M#productFloor&core.modelType"
    };
}
@end
