//
//  JDISVSettlementShopFloorRoundCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, JDISVSettlementShopFloorRoundCellType) {
    JDISVSettlementShopFloorRoundCellTypeR1 = 0, /**< 左上、右上圆角 */
    JDISVSettlementShopFloorRoundCellTypeR2 /**< 左下、右下圆角 */
};
@interface JDISVSettlementShopFloorRoundCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, assign) JDISVSettlementShopFloorRoundCellType cellType;

@property (nonatomic, assign) CGFloat radius;
@property (nonatomic, assign) CGFloat margin;
- (instancetype)initWithCellType:(JDISVSettlementShopFloorRoundCellType)cellType;
@end

NS_ASSUME_NONNULL_END
