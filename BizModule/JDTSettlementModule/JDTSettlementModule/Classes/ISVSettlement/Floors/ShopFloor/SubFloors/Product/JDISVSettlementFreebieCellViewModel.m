//
//  JDISVSettlementShopInfoCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/9/27.
//

#import "JDISVSettlementFreebieCellViewModel.h"
#import "JDISVSettlementShopFloorModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>




@implementation JDISVSettlementFreebieCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementFreebieCell";
        self.cellIdentifier = @"JDISVSettlementFreebieCell";
        self.height = 84.f;
        self.freeTitleWidth = [SettlementLan(@"checkout_settlment_gift") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake(MAXFLOAT, 20.f)].width + 1.f;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:NSDictionary.class]) {
//        {
//            "type":{
//                "namespace":"core.trade-FLR#balance.product-M#additionalProductFloor",
//                "version":"1.0.0"
//            },
//            "uuid":"additionalProductFloor_34132565",
//            "info":{
//                "C-M#additionalProductFloor&basic":{
//                    "giftType":"4"
//                },
//                "C-M#additionalProductFloor&core":{
//                    "modelType":"7"
//                },
//                "C-M#retailProductFloor&numInfo":{
//                    "skuId":"123",
//                    "skuName":"skuname",
//                    "num":"12",
//                    "stockStatusCode":"33"
//                }
//            }
//        }
        
//        NSString *giftType = data[@"C-M#additionalProductFloor&basic"][@"giftType"];
//        NSString *modelType = data[@"C-M#additionalProductFloor&core"][@"modelType"];
        NSDictionary *numInfo = [data jdcd_getDicElementForKey:@"C-M#retailProductFloor&numInfo"];
        NSDictionary *basicInfo = [data jdcd_getDicElementForKey:@"C-M#retailProductFloor&basic"];
        NSDictionary *stockInfo = [data jdcd_getDicElementForKey:@"C-M#retailProductFloor&stock"];
        NSString *skuId = basicInfo[@"skuId"];
        NSString *skuName = basicInfo[@"skuName"];
        NSString *num = numInfo[@"num"];
        NSString *stockStatusCode = stockInfo[@"stockStatusCode"];
        NSString *imageUrl = basicInfo[@"imageUrl"];
        
        self.skuId = skuId;
        self.freeProductName = skuName;
        self.proImgUrl = imageUrl;
        
        if ([stockStatusCode isEqualToString:@"34"]) {
            // 无货
            self.isStockOut = YES;
//            self.freeCount = SettlementLan(@"checkout_shop_no_product_label");
            self.freeCount = [NSString stringWithFormat:SettlementLan(@"checkout_settlment_qty"),num];
            self.freeNameColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
            self.freeCountColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        } else {
            self.isStockOut = NO;
            self.freeCount = [NSString stringWithFormat:SettlementLan(@"checkout_settlment_qty"),num];
            self.freeNameColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
            self.freeCountColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        }
    }
}
@end

