//
//  JDISVSettlementShopFloorAddressModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:地址楼层模型
 模型命名空间:core.trade-FLR#balance.address-M#defaultAddressFloor
 模型uuid:defaultAddressFloor
 */
@class JDISVSettlementShopFloorAreaIdModel,JDISVSettlementShopFloorAreaNameModel;
@interface JDISVSettlementShopFloorAddressModel : NSObject
// 内部Key:C-M#defaultAddressFloor&core
@property (nonatomic, copy) NSString *addressId; /**< addressId */
// 内部Key:C-M#defaultAddressFloor&affiliation
@property (nonatomic, copy) NSString *pin; /**< pin */
@property (nonatomic) BOOL selected; /**< selected:是否选中 */
@property (nonatomic) BOOL isDefaultAddress; /**< defaultAddress:是否是默认地址 */
// 内部Key:C-M#defaultAddressFloor&basic
@property (nonatomic, copy) NSString *detailAdress; /**< addressDetail */
@property (nonatomic, copy) NSString *fullAddress; /**< fullAddress */
// 内部Key:C-M#defaultAddressFloor&privacy
@property (nonatomic, copy) NSString *encryptedMobile; /**< encryptedMobile:加密的电话 */
@property (nonatomic, copy) NSString *starredMobile; /**< starredMobile:150****0000 掩盖的电话 */
@property (nonatomic, copy) NSString *encryptedName; /**< encryptedName:加密姓名 */
@property (nonatomic, copy) NSString *encryptedAddressDetail; /**< encryptedAddressDetail:加密详细地址 */
@property (nonatomic, copy) NSString *encryptedFullAddress; /**< encryptedFullAddress:加密全地址 */
// 内部Key:C-M#defaultAddressFloor&coordinate
@property (nonatomic, assign) NSInteger coordType; /**< 坐标系类型 */
@property (nonatomic, copy) NSString *latitude; /**< 维度 */
@property (nonatomic, copy) NSString *longitude; /**< 经度 */
// 内部Key:VCYBB2C-D#venderPickTip&vednerPickTip
@property (nonatomic, copy) NSString *venderPickTip; /**< 自提提示 */
// 内部Key:VCYBB2C-D#venderPickTip&allLocShipment
@property (nonatomic, assign) BOOL allLocShipment; /**< 是否都是LOC商品 */
// 内部Key:C-M#defaultAddressFloor&area
@property (nonatomic, strong) JDISVSettlementShopFloorAreaIdModel *areaId; /**< 地区Id模型 */
@property (nonatomic, strong) JDISVSettlementShopFloorAreaNameModel *areaName;  /**< 地区Name模型 */
// 内部Key:C-M#defaultAddressFloor&consignee
@property (nonatomic, copy) NSString *phone; /**< 未加密 一般下发为空, 使用加密字段解密 */
@property (nonatomic, copy) NSString *mobile; /**< 未加密 一般下发为空, 使用加密字段解密 */
@property (nonatomic, copy) NSString *name; /**< 未加密 一般下发为空, 使用加密字段解密 */
@property (nonatomic, copy) NSString *email; /**< 未加密 一般下发为空, 使用加密字段解密 */
// 内部Key:C-D#addressMark&basic
@property (nonatomic, assign) NSInteger areaGeography;
@property (nonatomic) BOOL virtualAddress;
// 内部Key:C-D#addressTag&basic
@property (nonatomic, copy) NSString *tagId; /**< tagId */
@property (nonatomic, copy) NSString *tagName; /**< tagName */
@property (nonatomic, copy) NSString *tagSource; /**< tagSource */
@end


/// 地区Id模型
@interface JDISVSettlementShopFloorAreaIdModel : NSObject
@property (nonatomic, strong) NSNumber *provinceId; /**< 一级地址 */
@property (nonatomic, strong) NSNumber *cityId; /**< 二级地址 */
@property (nonatomic, strong) NSNumber *areaId; /**< 三级地址 */
@property (nonatomic, strong) NSNumber *townId; /**< 四级地址 */
@end

/// 地区Name模型
@interface JDISVSettlementShopFloorAreaNameModel : NSObject
@property (nonatomic, copy) NSString *provinceName;
@property (nonatomic, copy) NSString *cityName;
@property (nonatomic, copy) NSString *areaName;
@property (nonatomic, copy) NSString *townName;
@end

NS_ASSUME_NONNULL_END
