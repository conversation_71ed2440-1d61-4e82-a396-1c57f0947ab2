//
//  JDISVSettlementShopFloorAddressModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorAddressModel.h"

@implementation JDISVSettlementShopFloorAddressModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"pin": @"C-M#defaultAddressFloor&affiliation.pin",
        @"selected": @"C-M#defaultAddressFloor&affiliation.selected",
        @"isDefaultAddress": @"C-M#defaultAddressFloor&affiliation.defaultAddress",
        @"detailAdress": @"C-M#defaultAddressFloor&basic.addressDetail",
        @"fullAddress": @"C-M#defaultAddressFloor&basic.fullAddress",
        @"encryptedMobile": @"C-M#defaultAddressFloor&privacy.encryptedMobile",
        @"starredMobile": @"C-M#defaultAddressFloor&privacy.starredMobile",
        @"encryptedName": @"C-M#defaultAddressFloor&privacy.encryptedName",
        @"encryptedAddressDetail": @"C-M#defaultAddressFloor&privacy.encryptedAddressDetail",
        @"encryptedFullAddress": @"C-M#defaultAddressFloor&privacy.encryptedFullAddress",
        @"coordType": @"C-M#defaultAddressFloor&coordinate.coordType",
        @"latitude": @"C-M#defaultAddressFloor&coordinate.latitude",
        @"longitude": @"C-M#defaultAddressFloor&coordinate.longitude",
        @"venderPickTip": @"VCYBB2C-D#venderPickTip&vednerPickTip.venderPickTip",
        @"allLocShipment": @"VCRYPE-D#venderPickTip&vednerPickTip.allLocShipment",
        @"areaId": @"C-M#defaultAddressFloor&area.areaIdMap",
        @"areaName": @"C-M#defaultAddressFloor&area.areaNameMap",
        @"phone": @"C-M#defaultAddressFloor&consignee.phone",
        @"mobile": @"C-M#defaultAddressFloor&consignee.mobile",
        @"name": @"C-M#defaultAddressFloor&consignee.name",
        @"email": @"C-M#defaultAddressFloor&consignee.email",
        @"areaGeography": @"C-D#addressMark&basic.areaGeography",
        @"virtualAddress": @"C-D#addressMark&basic.virtualAddress",
        @"addressId": @"C-M#defaultAddressFloor&core.addressId",
        @"tagId": @"C-D#addressTag&basic.tagId",
        @"tagName": @"C-D#addressTag&basic.tagName",
        @"tagSource": @"C-D#addressTag&basic.tagSource"
    };
}
@end

@implementation JDISVSettlementShopFloorAreaIdModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"provinceId": @"idProvince",
        @"cityId": @"idCity",
        @"areaId": @"idArea",
        @"townId": @"idTown"
    };
}

@end

@implementation JDISVSettlementShopFloorAreaNameModel

@end
