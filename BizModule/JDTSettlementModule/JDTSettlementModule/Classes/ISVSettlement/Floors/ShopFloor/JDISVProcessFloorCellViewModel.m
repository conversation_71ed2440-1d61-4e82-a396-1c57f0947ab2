//
//  JDISVProcessFloorCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.songjian6 on 2022/8/16.
//

#import "JDISVProcessFloorCellViewModel.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>


@implementation JDISVProcellModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"payStepType" : @"C-M#abstractPresaleFloor&core.payStepType",
        @"stage" : @"C-M#abstractPresaleFloor&core.stage",
        @"factPayType" : @"C-M#abstractPresaleFloor&core.factPayType",
        @"factEarnest" : @"C-M#abstractPresaleFloor&core.factEarnest",
        @"deductionAmount" : @"C-M#abstractPresaleFloor&core.deductionAmount",
        @"factEndPayment" : @"C-M#abstractPresaleFloor&core.factEndPayment",
        @"earnestAfterCoupon" : @"C-M#abstractPresaleFloor&core.earnestAfterCoupon",
        @"presaleFreight" : @"C-M#abstractPresaleFloor&core.presaleFreight",
        @"endPaymentBeginTime" : @"C-M#abstractPresaleFloor&core.endPaymentBeginTime",
        @"endPaymentEndTime" : @"C-M#abstractPresaleFloor&core.endPaymentEndTime",
        @"endPaymentBeginTimeFormat" : @"C-M#abstractPresaleFloor&core.endPaymentBeginTimeFormat",
        @"endPaymentEndTimeFormat" : @"C-M#abstractPresaleFloor&core.endPaymentEndTimeFormat",
        @"earnest" : @"C-M#abstractPresaleFloor&core.earnest",
        @"presaleFlag" : @"C-M#abstractPresaleFloor&core.presaleFlag"
    };
}
@end

@implementation JDISVProcessFloorCellViewModel
-(instancetype)init{
    
    self = [super init];
    if (self) {
        self.cellName = @"JDISVProcessFloorCell";
        self.cellIdentifier = @"JDISVProcessFloorCell";
    }
    return self;
}

- (void)updateWithData:(id __nullable)data allFloorData:(NSDictionary *)depositFloorData{
    
    if ([data isKindOfClass:NSDictionary.class]) {
        
        //取出presaleTotalDepositFloor楼层的定金和文案
        NSDictionary *depositData = depositFloorData[@"C-M#presaleTotalDepositFloor&basic"];
        NSString *depositAmount = depositData[@"depositAmount"] ? : @"";
        NSString *depositTitle = depositData[@"depositTitle"] ? : SettlementLan(@"checkout_front_money") ;
        
        NSDictionary *DataDic = (NSDictionary *)data;
        JDISVProcellModel *deposit = [JDISVProcellModel yy_modelWithDictionary:DataDic];

        //earnest: 定金，结算尾款时结算中台接口下发
        //depositAmount: 定金，结算定金时阿波罗中台接口下发
        self.depositAmount = deposit.earnest ? : depositAmount;
        self.payStepType = deposit.payStepType;
//        self.stage = deposit.stage;
        self.factPayType = deposit.factPayType;
//        self.factEarnest = deposit.factEarnest;
        self.deductionAmount = deposit.deductionAmount;
        self.factEndPayment = deposit.factEndPayment;
        self.earnestAfterCoupon = deposit.earnestAfterCoupon;
        self.presaleFreight = deposit.presaleFreight;
        self.presaleFlag = deposit.presaleFlag;
        
//        self.factEarnest = [NSString stringWithFormat:@"¥%.2f",[deposit.factEarnest floatValue]];
        
//        if([deposit.stage isEqualToString:@"0"]){
//            if(deposit.expamount){
//                self.expamount = deposit.expamount;
//            }
//        }else{
//            if(deposit.earnestAfterCoupon){
//                self.earnestAfterCoupon = [NSString stringWithFormat:@"¥%.2f",[deposit.earnestAfterCoupon floatValue]];
//            }
//        }
//        self.factEndPayment = [NSString stringWithFormat:@"¥%.2f",[deposit.factEndPayment floatValue]];
        
        self.endPaymentBeginTime = deposit.endPaymentBeginTime;
        self.endPaymentEndTime = deposit.endPaymentEndTime;
        
        self.endPaymentBeginTimeFormat = deposit.endPaymentBeginTimeFormat;
        self.endPaymentEndTimeFormat = deposit.endPaymentEndTimeFormat;
        
        NSTimeInterval intervel = [self.endPaymentBeginTime doubleValue]/1000;
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:intervel];
        
        NSDateFormatter *dateFromatter = [[NSDateFormatter alloc]init];
        dateFromatter.dateFormat = SettlementLan(@"setllement_date_formatter") ;
        NSString *dateStr = [dateFromatter stringFromDate:date];
        
        intervel = [self.endPaymentEndTime doubleValue]/1000;
        NSDate *endDate = [NSDate dateWithTimeIntervalSince1970:intervel];
        NSString *endTime = [dateFromatter stringFromDate:endDate];
        
        
//        self.timeSinceWithEndStr = [NSString stringWithFormat:SettlementLan(@"checkout_pre_sale_end_period") ,dateStr,endTime];
        self.timeSinceWithEndStr = [NSString stringWithFormat:SettlementLan(@"checkout_pre_sale_end_period") ,self.endPaymentBeginTimeFormat,self.endPaymentEndTimeFormat];
        if(ISFirstMoenyFlag){
            //付定金
            self.topStepAtt = [[NSMutableAttributedString alloc] initWithString:SettlementLan(@"checkout_pre_sale_stage_new_1") attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
            self.topPriceAtt = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag],[self.depositAmount floatValue]] attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular]}];
            self.topDescAtt = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:SettlementLan(@"checkout_pre_sale_disocunt_prefix") ,[self.deductionAmount floatValue]] attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
            
            self.bottomStepAtt = [[NSMutableAttributedString alloc] initWithString:SettlementLan(@"checkout_pre_sale_stage_new_2") attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
            self.bottomPriceAtt = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag],[self.factEndPayment floatValue]] attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular]}];
            
            self.bottomDescAtt = [[NSMutableAttributedString alloc] initWithString:self.timeSinceWithEndStr attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
        }else{
            //付尾款
            
            self.topStepAtt = [[NSMutableAttributedString alloc] initWithString:SettlementLan(@"checkout_pre_sale_stage_new_1") attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
            self.topPriceAtt = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag],[self.depositAmount floatValue]] attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular]}];
            self.topDescAtt = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:SettlementLan(@"checkout_pre_sale_disocunt_prefix") ,[self.deductionAmount floatValue]] attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
            
            self.bottomStepAtt = [[NSMutableAttributedString alloc] initWithString:SettlementLan(@"checkout_pre_sale_stage_new_2") attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]}];
            self.bottomPriceAtt = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag],[self.factEndPayment floatValue]] attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular]}];
            
            self.bottomDescAtt = [[NSMutableAttributedString alloc] initWithString:self.timeSinceWithEndStr attributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"],NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"]}];
        }
        
        //高度计算
        self.height = 12.f + [self getAttStrHeight:self.topStepAtt] + 4.f + [self getAttStrHeight:self.topPriceAtt] + 4.f + [self getAttStrHeight:self.topDescAtt] + 10.f + [self getAttStrHeight:self.bottomStepAtt] + 4.f + [self getAttStrHeight:self.bottomPriceAtt] + 4.f + [self getAttStrHeight:self.bottomDescAtt] + 12.f;
        
//        if ([self.stage intValue] == 0) {
//            self.height = 105;
//        }else{
//            self.height = 141;
//        }
    }
}


- (CGFloat)getAttStrHeight:(NSMutableAttributedString *)att{
    CGFloat showWidth = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2) - 36.f - 31.f - 12.f;
    return [att boundingRectWithSize:CGSizeMake(showWidth, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading context:nil].size.height + 1.f;
}
@end
