//
//  JDISVSettlementShipmentStoreCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentStoreCell.h"

#import "JDISVSettlementShipmentStoreCellViewModel.h"

@interface JDISVSettlementShipmentStoreCell ()
@property (weak, nonatomic) IBOutlet UIButton *checkRadio;
@property (weak, nonatomic) IBOutlet UILabel *storeNameLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *storeName_distanceLabel_space;

@property (weak, nonatomic) IBOutlet UIImageView *recentRemarkImgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *recentRemarkImgViewWidth;
@property (weak, nonatomic) IBOutlet UILabel *distanceLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *distanceLabelWidth;
@property (weak, nonatomic) IBOutlet UIImageView *bussinessTimeIconImgView;
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeLabel;
@property (weak, nonatomic) IBOutlet UIImageView *locationIconImgView;
@property (weak, nonatomic) IBOutlet UILabel *locationLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *locationLabelHeight;
@property (weak, nonatomic) IBOutlet UILabel *stockoutTipLabel;


@property (nonatomic, strong) JDISVSettlementShipmentStoreCellViewModel *cellViewModel;
@end

@implementation JDISVSettlementShipmentStoreCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.backgroundColor = [UIColor clearColor];
    
    [self.checkRadio renderB7];
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShipmentStoreCellViewModel.class]) {
        self.cellViewModel = (JDISVSettlementShipmentStoreCellViewModel *)viewModel;
        
        if (_cellViewModel.checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusUnChecked) {
            self.checkRadio.enabled = YES;
            self.checkRadio.jdisv_selected_B7 = NO;
        } else if (_cellViewModel.checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusChecked) {
            self.checkRadio.enabled = YES;
            self.checkRadio.jdisv_selected_B7 = YES;
        } else {
            self.checkRadio.enabled = NO;
        }
        
        self.storeNameLabel.attributedText = _cellViewModel.storeNameAttributedString;
        
        if (_cellViewModel.recentRemarkImg) {
            self.recentRemarkImgView.hidden = NO;
            self.recentRemarkImgView.image = _cellViewModel.recentRemarkImg;
//            self.recentRemarkImgViewWidth.constant = 28;
            self.storeName_distanceLabel_space.constant = 28+6+6;
        } else {
            self.recentRemarkImgView.hidden = YES;
//            self.recentRemarkImgViewWidth.constant = 10;
            self.storeName_distanceLabel_space.constant = 6;
        }
        
        self.distanceLabel.attributedText = _cellViewModel.distanceAttributedString;
        self.distanceLabelWidth.constant = _cellViewModel.distanceLabelWidth;
        
        self.bussinessTimeIconImgView.image = _cellViewModel.bussinessTimeIconImg;
        self.bussinessTimeTitleLabel.attributedText = _cellViewModel.bussinessTimeTitleAttributedString;
        self.bussinessTimeLabel.attributedText = _cellViewModel.bussinessTimeAttributedString;
        
        self.locationIconImgView.image = _cellViewModel.locationIconImg;
        self.locationLabel.attributedText = _cellViewModel.locationAttributedString;
        self.locationLabelHeight.constant = _cellViewModel.locationLabelHeight;
        
        if (_cellViewModel.stockoutTipAttributedString) {
            self.stockoutTipLabel.hidden = NO;
            self.stockoutTipLabel.attributedText = _cellViewModel.stockoutTipAttributedString;
        } else {
            self.stockoutTipLabel.hidden = YES;
        }
    }
}
@end
