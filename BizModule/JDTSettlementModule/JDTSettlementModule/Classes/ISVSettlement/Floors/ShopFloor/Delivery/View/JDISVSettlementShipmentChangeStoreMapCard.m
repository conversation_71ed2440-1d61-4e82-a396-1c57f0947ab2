//
//  JDISVSettlementShipmentChangeStoreMapCard.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentChangeStoreMapCard.h"

#import "JDISVSettlementShipmentChangeStoreMapCardViewModel.h"

#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/UIView+JDCDCorners.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@interface JDISVSettlementShipmentChangeStoreMapCard()
@property (weak, nonatomic) IBOutlet UIView *topView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topViewHeight;
@property (weak, nonatomic) IBOutlet UILabel *storeNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeLabel;
@property (weak, nonatomic) IBOutlet UILabel *addressLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *addressLabelHeight;

// Bottom
@property (weak, nonatomic) IBOutlet UIView *bottomView;
@property (weak, nonatomic) IBOutlet UIImageView *recentTagImgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *distanceLabelLeading;
@property (weak, nonatomic) IBOutlet UILabel *distanceLabel;
@property (weak, nonatomic) IBOutlet UIButton *checkButton;
@property (weak, nonatomic) IBOutlet UILabel *stockoutLabel;


@property (nonatomic, strong) JDISVSettlementShipmentChangeStoreMapCardViewModel *viewModel;
@end

@implementation JDISVSettlementShipmentChangeStoreMapCard

- (void)awakeFromNib {
    [super awakeFromNib];
    self.delegate = [[RACSubject alloc] init];
    self.backgroundColor = [UIColor clearColor];
    self.bottomView.backgroundColor = [UIColor clearColor];
    [self.bottomView.layer insertSublayer:[self bottomViewShapeLayerTineHeight:5.f tineWidth:15.f inParentViewRect:CGSizeMake(260.f, 32.f)] atIndex:0];
}

- (void)updateCardViewWith:(JDISVSettlementShipmentChangeStoreMapCardViewModel *)viewModel {
    self.viewModel = viewModel;
    self.topView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C14"];
    self.topView.layer.masksToBounds = YES;
    self.topViewHeight.constant = _viewModel.topViewHeight;
    [self.topView jdcd_addRoundedCorners:UIRectCornerTopLeft|UIRectCornerTopRight withRadii:CGSizeMake(6.f, 6.f) viewRect:CGRectMake(0, 0, _viewModel.width, _viewModel.topViewHeight)];
    
    self.storeNameLabel.attributedText = _viewModel.storeNameAttributedString;
    self.bussinessTimeLabel.attributedText = _viewModel.bussinessTimeAttribtedString;
    self.addressLabel.attributedText = _viewModel.addressAttributedString;
    self.addressLabelHeight.constant = _viewModel.addressLabelHeight;
    
    if (_viewModel.recentTagImgView) {
        self.recentTagImgView.hidden = NO;
        self.recentTagImgView.image = _viewModel.recentTagImgView;
    } else {
        self.recentTagImgView.hidden = YES;
        self.recentTagImgView.image = _viewModel.recentTagImgView;
    }
    
    self.distanceLabelLeading.constant = _viewModel.distanceLabelLeading;
    self.distanceLabel.attributedText = _viewModel.distanceAttributedString;
    
    if (_viewModel.stockout) {
        // 无货
        self.checkButton.hidden = YES;
        self.stockoutLabel.hidden = NO;
        self.stockoutLabel.attributedText = _viewModel.stockoutAttributedString;
    } else {
        // 有货
        self.checkButton.hidden = NO;
        self.stockoutLabel.hidden = YES;
        if (_viewModel.isCheck) {
            self.checkButton.enabled = YES;
            self.checkButton.jdisv_selected_B7 = YES;
        } else {
            self.checkButton.enabled = YES;
            self.checkButton.jdisv_selected_B7 = NO;
        }
    }
    
    [self.checkButton addTarget:self action:@selector(tapSelectedAction) forControlEvents:UIControlEventTouchUpInside];
}

- (CAShapeLayer *)bottomViewShapeLayerTineHeight:(CGFloat)tineHeight tineWidth:(CGFloat)tineWidth inParentViewRect:(CGSize)parentSize {
    CGFloat bottomRadius = 6.f;
    CGPoint leftBottomRadiusCenter = CGPointMake(bottomRadius, parentSize.height - tineHeight - bottomRadius);
    CGPoint rightBottomRadiusCenter = CGPointMake(parentSize.width - bottomRadius, parentSize.height - tineHeight - bottomRadius);
    
    UIBezierPath *path = [UIBezierPath bezierPath];
    [path moveToPoint:CGPointMake(0, 0)];
    [path addLineToPoint:CGPointMake(0, leftBottomRadiusCenter.y)];
    // // 左圆角
    [path addArcWithCenter:leftBottomRadiusCenter radius:bottomRadius startAngle:-M_PI endAngle:-(M_PI_2+M_PI) clockwise:NO];
    // 尖
    CGPoint tineLeftPoint = CGPointMake((parentSize.width - tineWidth)/2.f, parentSize.height - tineHeight);
    CGPoint tineRightPoint = CGPointMake((parentSize.width + tineWidth)/2.f, parentSize.height - tineHeight);
    CGPoint tineBottomPoint = CGPointMake(parentSize.width / 2.f, parentSize.height);
    
    [path addLineToPoint:tineLeftPoint];
    [path addLineToPoint:tineBottomPoint];
    [path addLineToPoint:tineRightPoint];
    [path addLineToPoint:CGPointMake(rightBottomRadiusCenter.x, parentSize.height-tineHeight)];
    [path addArcWithCenter:rightBottomRadiusCenter radius:bottomRadius startAngle:-(M_PI_2+M_PI) endAngle:0 clockwise:NO];
    [path addLineToPoint:CGPointMake(parentSize.width, 0)];
    [path closePath];
    
    // 设置路径画布
    CAShapeLayer *bottomLayer = [CAShapeLayer layer];
    bottomLayer.position = CGPointMake(0, 0);

    bottomLayer.path = path.CGPath;
    bottomLayer.fillColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"].CGColor;
    bottomLayer.lineCap = kCALineCapRound;
    bottomLayer.lineJoin = kCALineJoinRound;
    
    
    return bottomLayer;
}

#pragma mark - action
- (void)tapSelectedAction {
    [self.delegate sendNext:self.viewModel.storeId];
}

@end
