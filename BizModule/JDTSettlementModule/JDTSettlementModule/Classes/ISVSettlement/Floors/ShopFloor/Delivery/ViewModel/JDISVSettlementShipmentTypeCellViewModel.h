//
//  JDISVSettlementShipmentTypeCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementShipmentTypeModel;
@interface JDISVSettlementShipmentTypeCellViewModel : NSObject
@property (nonatomic, copy) NSString *shipmentTypeTitle;
@property (nonatomic) BOOL selected;
@property (nonatomic, assign) CGSize cellSize;
@property (nonatomic, assign) CGFloat cornerRadius;

@property (nonatomic, strong, readonly) JDISVSettlementShipmentTypeModel *shipmentTypeModel;

- (void)updateViewModelWith:(id)data;
- (void)updateSelectedStatus:(BOOL)selected;
@end

NS_ASSUME_NONNULL_END
