//
//  JDISVSettlementShipmentChangeStoreController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentChangeStoreController.h"

//#import <JDISVTencentMapSDK3DModule/JDISVTencentMapSDK3DModule-umbrella.h>
#import <JDISVDZNEmptyDataSetModule/JDISVDZNEmptyDataSetModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVSettlementShipmentChangeStoreContentView.h"
#import "JDISVSettlementShipmentChangeStoreMapCard.h"
#import "JDISVSettlementShipmentStoreCellViewModel.h"

#import "JDISVSettlementShipmentChangeStoreViewModel.h"
#import "JDISVSettlementShipmentChangeStoreMapCardViewModel.h"
#import "JDISVSettlementShipmentVenderStoreModel.h"
#import "JDISVSettlementShipmentMapCardAnnotationUserModel.h"


static NSString * const kJDISVSettlementShipmentChangeStoreControllerConsigneeAnnotionIdentifier = @"JDISVSettlementConsigneeAnnotion";
static NSString * const kJDISVSettlementShipmentChangeStoreControllerStoreAnnotionIdentifier = @"JDISVSettlementStoreAnnotion";

@interface JDISVSettlementShipmentChangeStoreController ()<JDISVSettlementShopFloorBaseControllerDataSource, JDISVSettlementShopFloorBaseControllerDelegate, JDISVSettlementShopFloorBaseControllerNavigationDelegate, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>
// Map

@property (weak, nonatomic) IBOutlet UIImageView *mapLogo;


@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mapViewTop;
// Content
@property (weak, nonatomic) IBOutlet JDISVSettlementShipmentChangeStoreContentView *contentView;
@property (weak, nonatomic) IBOutlet UIView *contentTitleView;
@property (weak, nonatomic) IBOutlet UILabel *contentTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentTitleLabelTop;
@property (weak, nonatomic) IBOutlet UILabel *addressLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowView;
@property (weak, nonatomic) IBOutlet UITableView *tableView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentViewBottom;

@property (nonatomic, strong) JDISVSettlementShipmentChangeStoreViewModel *changeStoreViewModel;
@property (nonatomic, copy) NSArray *mapAnnotions;

@end

@implementation JDISVSettlementShipmentChangeStoreController
- (void)updateWithViewModel:(JDISVSettlementShopFloorBaseViewModel *)viewModel {
    self.changeStoreViewModel = (JDISVSettlementShipmentChangeStoreViewModel *)viewModel;
}

- (void)viewDidLoad {
    [super viewDidLoad];

    [self.navigationController setNavigationBarHidden:YES animated:YES];
    
    self.tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.contentTitleView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

    [self.contentTitleView jd_addTapAction:@selector(tapContentTitleView) withTarget:self];
    
    //滑动回调
    [self processContentViewMoveDelegate];
    
    // 滑动结束回调
    [self processContentViewModeEndDelegate];
    
    [self setupContentView];
    
    [self initializeMap];
    
    [self reloadView];
    
    [self loadData];

}

#pragma mark - Reload

- (void)reloadView {
    [self setupContentView];
    [self addAnnotationsMapView];
    [self.tableView reloadData];
}

#pragma mark - Content View

- (void)contentViewChangeAnimation {
    if (!_changeStoreViewModel.mapFeatureFlag) {
        return;
    }
    
    @weakify(self)
    [UIView animateWithDuration:0.5 animations:^{
        @strongify(self)
        self.contentViewBottom.constant = [self.changeStoreViewModel contentViewBottom];
        self.contentViewHeight.constant = [self.changeStoreViewModel contentViewHeight];
        [self.contentView setFrame:CGRectMake(0, [self.changeStoreViewModel contentViewY], CGRectGetWidth([[UIScreen mainScreen] bounds]), [self.changeStoreViewModel contentViewHeight])];
    } completion:^(BOOL finished) {
        //
        [self setupContentView];
    }];
}

- (void)setupContentView {
    
    self.contentView.backgroundColor = [UIColor clearColor];
    
    self.rightArrowView.contentMode = UIViewContentModeCenter;
    self.rightArrowView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    self.rightArrowView.userInteractionEnabled = YES;
    
    @weakify(self)
    [self.rightArrowView jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
        @strongify(self)
        [self changeStoreAddress];
    }];

    
    // 标题
    self.contentTitleLabel.textAlignment = [self.changeStoreViewModel contentTitleAlignment];
    self.contentTitleLabelTop.constant = [self.changeStoreViewModel contentTitleTop];
    self.contentTitleLabel.attributedText = [self.changeStoreViewModel contentTitleAttributedString];
    if ([self.changeStoreViewModel contentTitleViewRadius] == 0) {
        self.contentTitleView.layer.cornerRadius = 0;
        self.contentTitleView.layer.masksToBounds = NO;
       
    } else {
        self.contentTitleView.layer.masksToBounds = YES;
        [self.contentTitleView jdcd_addRoundedCorners:UIRectCornerTopLeft | UIRectCornerTopRight withRadii:CGSizeMake([self.changeStoreViewModel contentTitleViewRadius], [self.changeStoreViewModel contentTitleViewRadius]) viewRect:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 55.f)];

    }
    // 地址
    if (self.changeStoreViewModel.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold   ) {
        
        self.addressLabel.hidden = NO;
        self.addressLabel.attributedText = _changeStoreViewModel.addressAttributedString;
        self.rightArrowView.hidden = NO;
    } else {
        self.addressLabel.hidden = YES;
        self.addressLabel.attributedText = _changeStoreViewModel.addressAttributedString;
        self.rightArrowView.hidden = YES;
    }
    
    [self.addressLabel jd_addTapActionWithBlock:^(UIGestureRecognizer * _Nonnull gestureRecoginzer) {
        @strongify(self)
        [self changeStoreAddress];
    }];
    
    [self.contentView setFrame:CGRectMake(0, [self.changeStoreViewModel contentViewY], CGRectGetWidth([[UIScreen mainScreen] bounds]), [self.changeStoreViewModel contentViewHeight])];
    self.contentViewBottom.constant = [self.changeStoreViewModel contentViewBottom];
    self.contentViewHeight.constant = [self.changeStoreViewModel contentViewHeight];
    
}


#pragma mark - Load Data
- (void)loadData {
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.changeStoreViewModel signalOfLoadStoreListInfoWith:nil actionType:JDISVSettlementShipmentChangeStoreViewModelUpdateTypeReuqest] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    } completed:^{
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [self reloadView];
    }];
}

- (void)loadDataAfterChangeAddress:(JDISVAddress *)address {
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.changeStoreViewModel signalOfLoadStoreListInfoWith:address actionType:JDISVSettlementShipmentChangeStoreViewModelUpdateTypeChangeAddress] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    } completed:^{
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [self reloadView];
    }];
}

#pragma mark - Action

- (void)tapStoreCardStoreIdOf:(NSString *)storeId {
    JDISVSettlementShipmentStoreCellViewModel *selectedStoreViewModel;
    for (JDISVSettlementShipmentStoreCellViewModel *storeViewModel in self.changeStoreViewModel.cellViewModels) {
        if ([storeViewModel.storeModel.storeId isEqualToString:storeId]) {
            selectedStoreViewModel = storeViewModel;
            break;
        }
    }
    if (selectedStoreViewModel) {
        NSUInteger index = [self.changeStoreViewModel.cellViewModels indexOfObject:selectedStoreViewModel];
        [self selectedStoreIndexOf:index];
    }
}

- (void)tapContentTitleView {
    @weakify(self)
    [[self.changeStoreViewModel signalOfChangeContentViewStatusActionOf:JDISVSettlementShipmentChangeStoreViewModelContentActionTypeTap offsetY:0] subscribeCompleted:^{
        @strongify(self)
        [self contentViewChangeAnimation];
    }];
}

- (void)selectedStoreIndexOf:(NSInteger)index {
    @weakify(self)
    [[self.changeStoreViewModel signalOfSelectedStoreIndexOf:index] subscribeNext:^(JDISVSettlementShipmentVenderStoreModel *storeModel) {
        @strongify(self)
        [self reloadView];
        if (storeModel && self.selectedCallback) {
            self.selectedCallback(storeModel);
            [self dismissViewControllerAnimated:YES completion:nil];
        }
    }];
}

- (void)changeStoreAddress {
    JDISVAddress *addr = [self.changeStoreViewModel venderAddress];
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
    if (addr) {
        [arg setObject:addr forKey:@"address"];
        [arg setObject:@(NO) forKey:@"showUserAddress"];
    }
    @weakify(self)
    NSString *cascaderAddressViewControllerURL = [NSString stringWithFormat:@"router://%@/cascaderAddressViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
    UIViewController *cascaderController  = [JDRouter openURL:cascaderAddressViewControllerURL arg:[arg copy] error:nil completion:^(JDISVAddress *object) {
        // code
        @strongify(self)
        [self loadDataAfterChangeAddress:object];
    }];
    KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:cascaderController presentingViewController:self];
    delegate.defultHeader.titleLabel.text = SettlementLan(@"setllement_address_title") ;
    cascaderController.transitioningDelegate = delegate;
    [self presentViewController:cascaderController animated:YES completion:nil];
}


/// 处理滑动回调
- (void)processContentViewMoveDelegate {
    if (!_changeStoreViewModel.mapFeatureFlag) {
        return;
    }
    
    @weakify(self)
    [self.contentView.delegate subscribeNext:^(NSNumber *value) {
        @strongify(self)
        CGFloat offSetY = [value doubleValue];
        [[self.changeStoreViewModel signalOfChangeContentViewStatusActionOf:JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMoved offsetY:offSetY] subscribeNext:^(NSNumber *x) {
            CGFloat contentViewY = [x doubleValue];
                [self.contentView setFrame:CGRectMake(0, contentViewY, CGRectGetWidth([[UIScreen mainScreen] bounds]), [self.changeStoreViewModel contentViewHeight])];
            } completed:^{
                [self setupContentView];
            }];
    }];
}

/// 处理滑动结束回调
- (void)processContentViewModeEndDelegate {
    if (!_changeStoreViewModel.mapFeatureFlag) {
        return;
    }
    @weakify(self)
    [self.contentView.toucheEndDelegate subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        [[self.changeStoreViewModel signalOfChangeContentViewStatusActionOf:JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMapMoveEnd offsetY:0] subscribeCompleted:^{
            [self contentViewChangeAnimation];
        }];
    }];
    
}

#pragma mark - Delegate
-(void)clickCellIndexOf:(NSIndexPath *)indexPath {
    
    [self selectedStoreIndexOf:indexPath.row];
}


#pragma mark - Map

- (void)initializeMap {
    
}

- (void)addAnnotationsMapView {
   
}

- (void)adjustMapToCenter {
    
}

#pragma mark - DataSource
- (JDISVSettlementShopFloorBaseViewModel *)controllerViewModel {
    return _changeStoreViewModel;
}

- (UITableView *)mainTableView {
    return _tableView;
}

- (CGFloat)mainTableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 6.f;
}

- (UIView *)mainTableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 12.f)];
    header.backgroundColor = [UIColor clearColor];
    return header;
}

- (NSBundle *)moduleBundle {
    return [NSBundle jdisvSettlement_bundle];
}

- (NSString *)titleOfNavigationView {
    return SettlementLan(@"checkout_stores_bar_title") ;
}

- (void)navigationBackButtonAction:(KANavigationBarButtonStandardItem * _Nonnull)sender {
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - DZNEmptyDataSet
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView {
    return [self.changeStoreViewModel showEmptyView];
}

- (UIView *)customViewForEmptyDataSet:(UIScrollView *)scrollView {
    KAEmptyView *emptyView = [[KAEmptyView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0) type:KAEmptyViewTypeNotAction];
    NSLayoutConstraint *heightConstraint = [NSLayoutConstraint constraintWithItem:emptyView attribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:nil attribute:NSLayoutAttributeNotAnAttribute multiplier:1 constant:self.tableView.frame.size.height];
    [emptyView addConstraint:heightConstraint];
    emptyView.offSetY = -100;
    emptyView.attDecrible = [self.changeStoreViewModel emptyViewContent];
    emptyView.decLabel.numberOfLines = 0;
    emptyView.decLabel.textAlignment = NSTextAlignmentCenter;
    emptyView.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:JDISVImageTypeNoDataStore];
    return emptyView;
}

#pragma mark - 子类重写
- (CGRect)frameOfTableView {
    return CGRectNull;
}

- (void)customSetupTableView:(UITableView *)tableView {
    tableView.emptyDataSetSource = self;
    tableView.emptyDataSetDelegate = self;
}

#pragma mark - Getter
- (JDISVAddress *)venderAddress {
    return [_changeStoreViewModel venderAddress];
}

#pragma mark - Private
- (UIImage *)imageWithView:(UIView *)view
{
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, view.opaque, 0.0f);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage * snapshotImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return snapshotImage;
}
@end
