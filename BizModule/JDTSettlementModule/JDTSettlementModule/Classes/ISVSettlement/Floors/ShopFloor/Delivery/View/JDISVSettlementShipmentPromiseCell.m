//
//  JDISVSettlementShipmentPromiseCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentPromiseCell.h"

#import "JDISVSettlementShopFloorBaseCellViewModel.h"
#import "JDISVSettlementShipmentPromiseCellViewModel.h"

@interface JDISVSettlementShipmentPromiseCell ()
@property (weak, nonatomic) IBOutlet UILabel *promiseLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *promiseLabelHeight;

@property (nonatomic, strong) JDISVSettlementShipmentPromiseCellViewModel *viewModel;
@end

@implementation JDISVSettlementShipmentPromiseCell
- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShipmentPromiseCellViewModel.class]) {
        self.viewModel = (JDISVSettlementShipmentPromiseCellViewModel *)viewModel;
        self.promiseLabel.attributedText = _viewModel.promiseAttributedString;
        self.promiseLabelHeight.constant = _viewModel.promiseLabelHeight;
    }
}
@end
