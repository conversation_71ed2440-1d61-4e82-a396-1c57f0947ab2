//
//  JDISVSettlementShipmentMapCardAnnotationUserModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger,JDISVSettlementShipmentMapCardAnnotationType) {
    JDISVSettlementShipmentMapCardAnnotationTypeConsignee = 0, /**< 收件人 */
    JDISVSettlementShipmentMapCardAnnotationTypeStore /**< 门店 */
};
@interface JDISVSettlementShipmentMapCardAnnotationUserModel : NSObject
@property (nonatomic, assign) JDISVSettlementShipmentMapCardAnnotationType annotationType;
@property (nonatomic, copy) NSString *storeId;

- (instancetype)initWith:(JDISVSettlementShipmentMapCardAnnotationType)annotionType storeId:(NSString * __nullable)storeId;
@end

NS_ASSUME_NONNULL_END
