//
//  JDISVSettlementShipmentViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorBaseViewModel.h"

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, JDISVSettlementShipmentViewModelUpdateType) {
    JDISVSettlementShipmentViewModelUpdateTypeRequestShipmentTypes = 0, /**< 请求配送方式 */
    JDISVSettlementShipmentViewModelUpdateTypeChangeSelcetdTab, /**< 切换选中 */
    JDISVSettlementShipmentViewModelUpdateTypeSelectStore, /**< 门店选择切换 */
};

@class JDISVAddress;
@class RACSignal;
@class JDISVSettlementShopFloorBaseCellViewModel;
@class JDISVSettlementShipmentInfoModel;

@interface JDISVSettlementShipmentViewModel : JDISVSettlementShopFloorBaseViewModel
@property (nonatomic, assign, readonly) NSUInteger source;

@property (nonatomic, assign) NSUInteger selectedIndex;
@property (nonatomic, assign) BOOL isUserClickSelected;//是否用户操作选中

@property (nonatomic, assign) CGFloat shipmentViewHeight;
@property (nonatomic, assign) CGFloat bottomViewHeight;

@property (nonatomic, copy, nullable) NSArray *shipmentTypeViewModelArray;

@property (nonatomic, strong) JDISVAddress *venderAddress;

#pragma mark - Update
- (void)updateWithVenderId:(NSString *)venderId sourceFrom:(NSUInteger)source;
- (void)updateWith:(id)data forType:(NSInteger)type;
- (JDISVSettlementShipmentInfoModel *)shipmentInfoModel;
- (NSString *)selectedStoreId;
#pragma mark - 信号
- (RACSignal *)signalOfRequestShipmentTypes;
- (void)updateVenderAddressWith:(id)data;
#pragma mark - TableView
- (NSInteger)numberOfSections;
- (NSInteger)numberOfRowsInSection:(NSInteger)section;
- (CGFloat)heightForRowAtIndexPath:(NSIndexPath *)indexPath;
- (JDISVSettlementShopFloorBaseCellViewModel *)viewModelForCurrentCell;
#pragma mark - Getter
- (NSDictionary *)saveShipmentRequestOrderStrParam;
- (JDISVAddress *)venderAddress;
@end

NS_ASSUME_NONNULL_END
