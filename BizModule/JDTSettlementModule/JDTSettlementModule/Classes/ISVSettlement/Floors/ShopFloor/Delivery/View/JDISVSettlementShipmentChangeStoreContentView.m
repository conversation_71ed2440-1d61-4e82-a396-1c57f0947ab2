//
//  JDISVSettlementShipmentChangeStoreContentView.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentChangeStoreContentView.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@interface JDISVSettlementShipmentChangeStoreContentView ()
@property (nonatomic, strong) UIPanGestureRecognizer *panGestureRecognizer;
@property (nonatomic, assign) CGFloat preOffsetY;
@end

@implementation JDISVSettlementShipmentChangeStoreContentView

- (instancetype)initWithCoder:(NSCoder *)coder
{
    self = [super initWithCoder:coder];
    if (self) {
        self.delegate = [[RACSubject alloc] init];
        self.toucheEndDelegate = [[RACSubject alloc] init];
        self.panGestureRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
        self.panGestureRecognizer.cancelsTouchesInView = YES; // 不向table传递hitTest
        self.panGestureRecognizer.delaysTouchesBegan = YES; // 不向table传递hitTest
        [self addGestureRecognizer:self.panGestureRecognizer];
        self.preOffsetY = 0;
    }
    return self;
}

- (void)handlePanGesture:(UIPanGestureRecognizer *)pan {
    //
    if (pan.state == UIGestureRecognizerStateChanged) {
        CGPoint translation = [pan translationInView:self];
        CGFloat offsetY = translation.y - self.preOffsetY;
        [self.delegate sendNext:@(offsetY)];
        self.preOffsetY = translation.y;

    } else if (pan.state == UIGestureRecognizerStateEnded) {
        self.preOffsetY = 0;
        [self.toucheEndDelegate sendNext:@""];
    }
}

- (void)commitTranslation:(CGPoint)translation {
    CGFloat offsetY = translation.y;
    
    [self.delegate sendNext:@(offsetY)];
}


@end
