//
//  JDISVSettlementShipmentChangeStoreMapCardViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentChangeStoreMapCardViewModel.h"

#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "NSAttributedString+ISVSettlementShop.h"

#import "JDISVSettlementShipmentVenderStoreModel.h"

#import "JDISVSettlementShipmentMapCardAnnotationUserModel.h"

@interface JDISVSettlementShipmentChangeStoreMapCardViewModel()
@property (nonatomic, strong) JDISVSettlementShipmentVenderStoreModel *model;
@end

@implementation JDISVSettlementShipmentChangeStoreMapCardViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.width = 260.f;
    }
    return self;
}

- (void)updateViewModelWith:(id)data {
    if ([data isKindOfClass:JDISVSettlementShipmentVenderStoreModel.class]) {
        self.model = (JDISVSettlementShipmentVenderStoreModel *)data;
        
        self.annotionUserData = [[JDISVSettlementShipmentMapCardAnnotationUserModel alloc] initWith:JDISVSettlementShipmentMapCardAnnotationTypeStore storeId:_model.storeId];
        
        self.venderId = _model.venderId;
        self.storeId = _model.storeId;
        self.longtitude = [_model.longitude doubleValue];
        self.latitude = [_model.latitude doubleValue];
        
        NSString *storeName = @"";
        if ([_model.storeName jdcd_validateString]) {
            storeName =  _model.storeName;
        }
        
        self.storeNameAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:storeName colorKey:@"#C1" fontKey:@"#T9" weight:UIFontWeightMedium];
        NSString *bussinessTime = SettlementLan(@"checkout_stores_list_business_time_1") ;
        if ([_model.businessHours jdcd_validateString]) {
            bussinessTime = [NSString stringWithFormat:@"%@%@", bussinessTime, _model.businessHours];
        }
        self.bussinessTimeAttribtedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:bussinessTime colorKey:@"#C1" fontKey:@"#T11"];
        NSString *address= SettlementLan(@"checkout_stores_list_address") ;
        if ([_model.storeAddress jdcd_validateString]) {
            address = [NSString stringWithFormat:@"%@%@", address, _model.storeAddress];
        }
        self.addressAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:address colorKey:@"#C1" fontKey:@"#T11"];
        CGFloat labelWidth = self.width - 12.f*2;
        self.addressLabelHeight = [self.addressAttributedString ka_settlement_shop_heightOfAttributedStringWidth:labelWidth maxNumberOfLines:2 fontKey:@"#T11"];
        self.topViewHeight = 10.f /**< top */ + 17.f /**< storeName */ + 4.f + 14.f /**< bussinessTime */ + 2.5f + _addressLabelHeight + 10.f;
        self.height = _topViewHeight + 4.5*2 + 18.f + 5.f + 2.f;
        // Bottom
        if ([_model.recentlyMark isEqualToString:@"1"]) {
            self.recentTagImgView = [self recentTag];
            self.distanceLabelLeading = 44.f;
        } else {
            self.recentTagImgView = nil;
            self.distanceLabelLeading = 12.f;
        }
        NSString *distance = SettlementLan(@"checkout_stores_list_distance_address") ;
        if (_model.distance) {
            distance = [NSString stringWithFormat:@"%@%@", distance, _model.distance];
            
        }
        self.distanceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:distance colorKey:@"#C5" fontKey:@"#T11"];
        if ([_model.stockStatus isEqualToString:@"0"]) {
            // 无货
            self.stockout = YES;
            self.stockoutAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_stores_no_product")  colorKey:@"#C9" fontKey:@"#T11"];
        } else {
            // 有货
            self.stockout = NO;
            self.stockoutAttributedString = nil;
        }
    } else {
        self.height = 0;
    }
    
}

- (void)updateCheckStatus:(BOOL)check {
    if (_stockout == NO) {
        self.isCheck = check;
    }
}

#pragma mark - Privita

- (UIImage *)recentTag {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:CGRectMake(0, 0, 32, 18)];
    [defaultTagBtn setTitle:SettlementLan(@"checkout_stores_list_recent")  forState:UIControlStateNormal];
    [defaultTagBtn renderL3WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    
    return [self getImageFromView:defaultTagBtn];
}


- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}
@end
