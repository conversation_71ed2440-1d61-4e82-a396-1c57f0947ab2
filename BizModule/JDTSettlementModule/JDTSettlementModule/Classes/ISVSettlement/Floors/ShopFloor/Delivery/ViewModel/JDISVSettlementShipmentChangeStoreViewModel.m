//
//  JDISVSettlementShipmentChangeStoreViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentChangeStoreViewModel.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVPlatformModule/JDISVAddress.h>
#import <JDISVPlatformModule/JDISVSubAddress.h>

#import "JDISVSettlementShipmentStoreCellViewModel.h"

#import "JDISVSettlementShipmentInfoModel.h"
#import "JDISVSettlementShopFloorMainModel.h"
#import "JDISVSettlementShopFloorModel.h"
#import "JDISVSettlementShipmentVenderStoreModel.h"

//#import "JDISVSettlementConsigneeAddressService.h"
#import "JDISVSettlementShopFloorNetService.h"
#import "JDISVSettlementShopFloorAddressManager.h"

#import "NSAttributedString+ISVSettlementShop.h"

@interface JDISVSettlementShipmentChangeStoreViewModel ()
@property (nonatomic, strong, nullable) JDISVSettlementShipmentInfoModel *shipmentInfoModel;
@property (nonatomic, assign) NSUInteger source;
@property (nonatomic, copy) NSString *rawSelectedStoreId;
@property (nonatomic, copy, readwrite) NSArray *cellViewModels;

@property (nonatomic) BOOL hasRequest;

// contentView
@property (nonatomic, assign) CGFloat contentTitleTop;
@property (nonatomic, assign) NSTextAlignment contentTitleAlignment;
@property (nonatomic, assign) CGFloat contentTitleViewRadius;
@property (nonatomic, copy) NSAttributedString *contentTitleAttributedString;
@property (nonatomic, assign) CGFloat contentViewHeight;
@property (nonatomic, assign) CGFloat contentViewBottom;
@property (nonatomic, assign) CGFloat contentViewY;
// 地址
@property (nonatomic, strong) JDISVAddress *venderAddress;

@property (nonatomic, assign) CGFloat contentViewFlodHeight;
@property (nonatomic, assign) CGFloat contentViewFlexHeight;
@end

@implementation JDISVSettlementShipmentChangeStoreViewModel
- (instancetype)initWithSelectedStoreModel:(JDISVSettlementShipmentInfoModel * __nullable)shipmentInfoModel
                           selectedStoreId:(NSString *)selectedStoreId
                                sourceForm:(NSInteger)source
                             venderAddress:(JDISVAddress *)venderAddress
                                mapFeature:(BOOL)mapFeatureFlag
{
    self = [super init];
    if (self) {
        _shipmentInfoModel = shipmentInfoModel;
        _rawSelectedStoreId = [selectedStoreId copy];
        _source = source;
        _hasRequest = NO;
        _mapFeatureFlag = mapFeatureFlag;
        
        _contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold;
        
        _contentViewFlodHeight = 44.f + [UIWindow ka_uikit_safeAreaInsets].bottom;
        _contentViewFlexHeight = 60.f;
        self.consigneeLocationCoorddinate = CLLocationCoordinate2DMake([[JDISVSettlementShopFloorAddressManager sharedService] consigneeAddressLatitude], [[JDISVSettlementShopFloorAddressManager sharedService] consigneeAddressLongitude]);
        // 店铺地址
        [self updateVenderAddresssWith:venderAddress];
        
    }
    return self;
}

#pragma mark - Update
- (void)updateWithViewModelWith:(id)data forType:(JDISVSettlementShipmentChangeStoreViewModelUpdateType)type {
    if (type == JDISVSettlementShipmentChangeStoreViewModelUpdateTypeReuqest) {
        if ([data isKindOfClass:JDISVSettlementShopFloorMainModel.class]) {
            JDISVSettlementShopFloorMainModel *mainModel = (JDISVSettlementShopFloorMainModel *)data;
            [self updateVenderStoreViewModelsWith:[self venderStoreModelsFrom:mainModel.floors]];
        }
    } else if (type == JDISVSettlementShipmentChangeStoreViewModelUpdateTypeChangeStore) {
        JDISVSettlementShipmentStoreCellViewModel *selectedStoreViewModel = (JDISVSettlementShipmentStoreCellViewModel *)data;
        for (JDISVSettlementShipmentStoreCellViewModel *cellViewModel in self.cellViewModels) {
            if (cellViewModel.checkStatus != JDISVSettlementShipmentStoreCellViewModelSelectedStatusDisable) {
                [cellViewModel updateWithData:@(0) forType:JDISVSettlementShipmentStoreCellViewModelUpdateTypeChangeCheckStatus];
            }
        }
        [selectedStoreViewModel updateWithData:@(1) forType:JDISVSettlementShipmentStoreCellViewModelUpdateTypeChangeCheckStatus];
    } else if (type == JDISVSettlementShipmentChangeStoreViewModelUpdateTypeChangeAddress) {
        if ([data isKindOfClass:JDISVAddress.class]) {
            // 门店地址更新
            [self updateVenderAddresssWith:(JDISVAddress *)data];
        }
        if ([data isKindOfClass:JDISVSettlementShopFloorMainModel.class]) {
            JDISVSettlementShopFloorMainModel *mainModel = (JDISVSettlementShopFloorMainModel *)data;
            [self updateVenderStoreViewModelsWith:[self venderStoreModelsFrom:mainModel.floors]];
        }
    }
}

// 更新店铺地址
- (void)updateVenderAddresssWith:(JDISVAddress *)venderAddress {
    // title
    self.venderAddress = venderAddress;
    if ([[self levelAddress] jdcd_validateString]) {
        _addressAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:[self levelAddress] colorKey:@"#C7" fontKey:@"#T7"];
    } else {
        _addressAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"setllement_plase_choose")  colorKey:@"#C7" fontKey:@"#T7"];
    }
}

// 更新店铺数据
- (void)updateVenderStoreViewModelsWith:(NSArray *)storeModels {
    NSMutableArray *tempCellArray = [NSMutableArray array];
    for (JDISVSettlementShipmentVenderStoreModel *storeModel in storeModels) {
        JDISVSettlementShipmentStoreCellViewModel *cellViewModel = [[JDISVSettlementShipmentStoreCellViewModel alloc] init];
        [cellViewModel updateWithData:storeModel forType:JDISVSettlementShipmentStoreCellViewModelUpdateTypeInterface];
       
        if ([_rawSelectedStoreId jdcd_validateString] && [storeModel.storeId isEqualToString:_rawSelectedStoreId]) {
            // 选中
            [cellViewModel updateWithData:@(1) forType:JDISVSettlementShipmentStoreCellViewModelUpdateTypeChangeCheckStatus];
        } else {
            // 未选中
            [cellViewModel updateWithData:@(0) forType:JDISVSettlementShipmentStoreCellViewModelUpdateTypeChangeCheckStatus];
        }
        [tempCellArray addObject:cellViewModel];
    }
    if (tempCellArray.count > 0) {
        self.cellViewModels = [NSArray arrayWithArray:tempCellArray];
        self.sectionViewModels = @[[NSArray arrayWithArray:_cellViewModels]];
    } else {
        self.sectionViewModels = [NSArray array];
        self.cellViewModels = [NSArray array];
    }
}

- (JDISVSettlementShipmentVenderStoreModel * __nullable)currentSelectedStoreModel{
    for (JDISVSettlementShipmentStoreCellViewModel *cellViewModel in self.cellViewModels) {
        if (cellViewModel.checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusChecked) {
            return cellViewModel.storeModel;
        }
    }
    
    return nil;
}

- (NSArray * __nullable)venderStoreModelsFrom:(NSArray *)floorDatas {
    NSMutableArray *venderStoreArray = [NSMutableArray array];
    for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:@"venderPickSubFloor"]) {
            if (floorData.partList == nil || !(floorData.partList.count > 0)) return nil;
            for (JDISVSettlementShopFloorModel *partItem in floorData.partList) {
                if ([partItem.type.nameSpace isEqualToString:@"cn_ybxt_b2c-FLR#balance.shipment-P#venderStorePart"]) {
                    JDISVSettlementShipmentVenderStoreModel *storeModel = [JDISVSettlementShipmentVenderStoreModel yy_modelWithDictionary:partItem.info];
                    [venderStoreArray addObject:storeModel];
                }
            }
        }
    }
    return [NSArray arrayWithArray:venderStoreArray];
    
}

#pragma mark - Signal
/// 请求门店列表信息
- (RACSignal *)signalOfLoadStoreListInfoWith:(JDISVAddress * __nullable)address actionType:(JDISVSettlementShipmentChangeStoreViewModelUpdateType)actionType {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        self.hasRequest = YES;
        [KASettlementShopFloorNetService requestSelfStoresListInfoWithParam:[self requestSelfDeliveryStoresOrderStrParamBy:address] sourceFrom:self.source complete:^(JDISVSettlementShopFloorMainModel * _Nonnull model, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                // 更新地址
                [self updateWithViewModelWith:address forType:actionType];
                [self updateWithViewModelWith:model forType:actionType];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}


- (RACSignal *)signalOfSelectedStoreIndexOf:(NSUInteger)index {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        JDISVSettlementShipmentStoreCellViewModel *selectedCellViewModel;
        if (index < self.cellViewModels.count) {
            selectedCellViewModel = [self.cellViewModels objectAtIndex:index];
        }
        
        if (selectedCellViewModel && selectedCellViewModel.checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusUnChecked) {
            [self updateWithViewModelWith:selectedCellViewModel forType:JDISVSettlementShipmentChangeStoreViewModelUpdateTypeChangeStore];
            [subscriber sendNext:selectedCellViewModel.storeModel];
        } else if (selectedCellViewModel && selectedCellViewModel.checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusChecked) {
            [subscriber sendNext:selectedCellViewModel.storeModel];
        } else {
            [subscriber sendNext:nil];
        }
        
        
        return nil;
    }];
}

# pragma mark - ContentView

/// ContentView
- (RACSignal *)signalOfChangeContentViewStatusActionOf:(JDISVSettlementShipmentChangeStoreViewModelContentActionType)actionType
                                               offsetY:(CGFloat) offsetY {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        if (actionType == JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMapMoved) {
            // 地图移动-收起
            if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
                self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusFold;
                [subscriber sendCompleted];
            }
        } else if (actionType == JDISVSettlementShipmentChangeStoreViewModelContentActionTypeTap) {
            // 点击标题-展开
            if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusFold) {
                self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold;
                [subscriber sendCompleted];
            }
        } else if (actionType == JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMapMoveEnd) {
            // 点击标题-移动结束
            if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusSwipeUp) {
                // 向上滑动
                if (self.contentViewY > [self contentViewMaxY] - self.contentViewFlexHeight) {
                    self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusFold;
                } else {
                    self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold;
                }
            } else {
                // 向下滑动
                if (self.contentViewY < [self contentViewMinY] + self.contentViewFlexHeight) {
                    self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold;
                } else {
                    self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusFold;
                }
            }
            [subscriber sendCompleted];
        } else {
            // 移动
            if (offsetY < 0 ) {
                // 向上滑动
                self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusSwipeUp;
            } else {
                // 向下滑动
                self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusSwipeDown;
            }
            
            if (self.contentViewY >= [self contentViewMinY] && self.contentViewY <= [self contentViewMaxY]) {
                self.contentViewY = self.contentViewY + offsetY;
                if (self.contentViewY < [self contentViewMinY]) {
                    // 超过展开
                    self.contentViewY = [self contentViewMinY];
                }
                if (self.contentViewY > [self contentViewMaxY]) {
                    // 小于收起
                    self.contentViewY = [self contentViewMaxY];
                }
                [subscriber sendNext:@(self.contentViewY)];
            } else {
                if (self.contentViewY < [self contentViewMinY]) {
                    self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold;
                } else {
                    self.contentViewStyle = JDISVSettlementShipmentChangeStoreViewModelContentStatusFold;
                }
                [subscriber sendCompleted];
            }
        }
        return nil;
    }];
}

/// 空白页提示
- (NSAttributedString *)emptyViewContent {
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    [style setLineSpacing:4];
    return [[NSAttributedString alloc] initWithString:SettlementLan(@"checkout_shipment_list_empty_tips")  attributes:@{NSParagraphStyleAttributeName:style}];
}

- (CGFloat)emptyViewHeight {
    // TODO:Table-Title
    return 100;
}

- (BOOL)showEmptyView {
    if (_hasRequest == NO) return NO;
    
    if (self.sectionViewModels && self.sectionViewModels.count > 0 && self.cellViewModels.count > 0) {
        return NO;
    } else {
        return YES;
    }
}

#pragma mark - ContentView
- (CGFloat)contentViewHeight {
    if (_mapFeatureFlag) {
        // 展示地图
        return ceil([[UIScreen mainScreen] bounds].size.height * 0.5f);
    }
    return ceil([[UIScreen mainScreen] bounds].size.height - [UIWindow ka_uikit_navigationHeight]);
}

- (CGFloat)contentViewMaxY {
    if (_mapFeatureFlag) {
        // 展示地图
        return ceil(CGRectGetHeight([[UIScreen mainScreen] bounds]) - self.contentViewFlodHeight);
    }
    return [UIWindow ka_uikit_navigationHeight];
    
}

- (CGFloat)contentViewMinY {
    if (_mapFeatureFlag) {
        // 展示地图
        return ceil(CGRectGetHeight([[UIScreen mainScreen] bounds]) - [self contentViewHeight]);
    }
    return [UIWindow ka_uikit_navigationHeight];
}

- (CGFloat)contentViewBottom {
    if (_mapFeatureFlag) {
        // 展示地图
        if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusFold) {
            // 收起
            return -(self.contentViewHeight - self.contentViewFlodHeight);
        } if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
            // 展开
            return 0;
        } else {
            // 移动
            return 0;
        }
    }
    
    return 0;
    
}

- (CGFloat)contentViewY {
    if (_mapFeatureFlag) {
        // 展示地图
        if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusFold) {
            // 收起
            _contentViewY = CGRectGetHeight([[UIScreen mainScreen] bounds]) - self.contentViewFlodHeight;
            return ceil(_contentViewY);
        } if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
            // 展开
            _contentViewY = CGRectGetHeight([[UIScreen mainScreen] bounds]) - [self contentViewHeight];
            return ceil(_contentViewY);
        } else {
            // 移动
            return ceil(_contentViewY);
        }
    }
    
    return [UIWindow ka_uikit_navigationHeight];
}

- (CGFloat)contentTitleTop {
    if (_mapFeatureFlag) {
        // 展示地图
        if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
            return 20.f;
        } else {
            return 11.f;
        }
    }
    return 20.f;;
}

- (NSTextAlignment)contentTitleAlignment {
    if (_mapFeatureFlag) {
        // 展示地图
        if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
            return NSTextAlignmentLeft;
        } else {
            return NSTextAlignmentCenter;
        }
    }
    return NSTextAlignmentLeft;
}

- (CGFloat)contentTitleViewRadius {
    if (_mapFeatureFlag) {
        // 展示地图
        if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
            return [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R30"];
        } else {
            return 0;
        }
    }
    return 0;
}

- (NSAttributedString *)contentTitleAttributedString {
    if (self.contentViewStyle == JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold) {
        return _contentTitleAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shipment_list_title")  colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightSemibold];
    } else {
        return _contentTitleAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_stores_click_open_stores")  colorKey:@"#C7" fontKey:@"#T6"];
    }
}

#pragma mark - Vender
- (NSString *)venderId {
    return _shipmentInfoModel.venderId;
}

- (JDISVAddress *)venderAddress {
    return _venderAddress;
}

- (JDISVSettlementShipmentStoreCellViewModel * __nullable)selectedStoreViewModel {
    JDISVSettlementShipmentStoreCellViewModel *selectedViewModel;
    for (JDISVSettlementShipmentStoreCellViewModel *storeViewModel in self.cellViewModels) {
        if (storeViewModel.checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusChecked) {
            selectedViewModel = storeViewModel;
            break;
        }
    }
    if (selectedViewModel) {
        return selectedViewModel;
    }
    
    return nil;
}

- (NSString *)storeIdOfMaybeShowMapAnnotationCustomCalloutView {
    if ([self selectedStoreViewModel]) {
        return [self selectedStoreViewModel].storeModel.storeId;
    } else {
        JDISVSettlementShipmentStoreCellViewModel *recentlyStoreViewModel;
        for (JDISVSettlementShipmentStoreCellViewModel *storeViewModel in self.cellViewModels) {
            if ([storeViewModel.storeModel.recentlyMark isEqualToString:@"1"]) {
                recentlyStoreViewModel = storeViewModel;
                break;;
            }
        }
        return recentlyStoreViewModel.storeModel.storeId;
    }
}

# pragma mark - Private
- (NSString *)levelAddress {
    
    if (_venderAddress == nil) return @"";
    
    NSMutableString *levelAddress = [NSMutableString stringWithString:@""];
    if ([_venderAddress.province.addressName jdcd_validateString]) {
        [levelAddress appendString:[_venderAddress.province.addressName jdcd_validateString] ? _venderAddress.province.addressName : @""];
    }
    if ([_venderAddress.province.addressName isEqualToString:_venderAddress.city.addressName] == NO) {
        [levelAddress appendString:[_venderAddress.city.addressName jdcd_validateString] ? _venderAddress.city.addressName : @""];
    }
    if ([_venderAddress.district.addressName jdcd_validateString]) {
        [levelAddress appendString:[_venderAddress.district.addressName jdcd_validateString] ? _venderAddress.district.addressName : @""];
    }
    if ([_venderAddress.town.addressName jdcd_validateString]) {
        [levelAddress appendString:[_venderAddress.town.addressName jdcd_validateString] ? _venderAddress.town.addressName : @""];
    }
    return [NSString stringWithString:levelAddress];
}

- (NSDictionary *)requestSelfDeliveryStoresOrderStrParamBy:(JDISVAddress * __nullable)address {
    NSMutableDictionary *tempParams = [NSMutableDictionary dictionary];
    if ([_shipmentInfoModel.bundleUuid jdcd_validateString]) {
        [tempParams addEntriesFromDictionary:@{@"bundleUuid": _shipmentInfoModel.bundleUuid}];
    }

    if (address) {
        // address - 选择了级联地址

        if (address.province.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idProvince": [address.province.addressID stringValue]}];
        } else {
            [tempParams addEntriesFromDictionary:@{@"idProvince": @""}];
        }
        if (address.city.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idCity": [address.city.addressID stringValue]}];
        } else {
            [tempParams addEntriesFromDictionary:@{@"idCity": @""}];
        }
        
        if (address.district.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idArea": [address.district.addressID stringValue]}];
        } else {
            if (KSAAPP) {
                [tempParams addEntriesFromDictionary:@{@"idArea": @"0"}];//KSA三四级补0
            } else {
                [tempParams addEntriesFromDictionary:@{@"idArea": @""}];
            }
        }
        
        if (address.town.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idTown": [address.town.addressID stringValue]}];
        } else {
            if (KSAAPP) {
                [tempParams addEntriesFromDictionary:@{@"idTown": @"0"}];//KSA三四级补0
            } else {
                [tempParams addEntriesFromDictionary:@{@"idTown": @""}];
            }
        }
        
        
    } else {
        // 未选择级联地址
        if (_venderAddress.province.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idProvince": [_venderAddress.province.addressID stringValue]}];
        } else {
            [tempParams addEntriesFromDictionary:@{@"idProvince": @""}];
        }
        if (_venderAddress.city.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idCity": [_venderAddress.city.addressID stringValue]}];
        } else {
            [tempParams addEntriesFromDictionary:@{@"idCity": @""}];
        }
        
        if (_venderAddress.district.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idArea": [_venderAddress.district.addressID stringValue]}];
        } else {
            if (KSAAPP) {
                [tempParams addEntriesFromDictionary:@{@"idArea": @"0"}];//KSA三四级补0
            } else {
                [tempParams addEntriesFromDictionary:@{@"idArea": @""}];
            }
        }
        
        if (_venderAddress.town.addressID) {
            [tempParams addEntriesFromDictionary:@{@"idTown": [_venderAddress.town.addressID stringValue]}];
        } else {
            if (KSAAPP) {
                [tempParams addEntriesFromDictionary:@{@"idTown": @"0"}];//KSA三四级补0
            } else {
                [tempParams addEntriesFromDictionary:@{@"idTown": @""}];
            }
        }
        
    }
    if ([[[JDISVSettlementShopFloorAddressManager sharedService] fullAddress] jdcd_validateString]) {
        [tempParams addEntriesFromDictionary:@{@"address": [[JDISVSettlementShopFloorAddressManager sharedService] fullAddress]}];
        
        
        [tempParams addEntriesFromDictionary:@{@"longitude":[NSString stringWithFormat:@"%@",@([[JDISVSettlementShopFloorAddressManager sharedService] consigneeAddressLongitude])]}];
        [tempParams addEntriesFromDictionary:@{@"latitude":[NSString stringWithFormat:@"%@",@([[JDISVSettlementShopFloorAddressManager sharedService] consigneeAddressLatitude])]}];
    }
    
    if ([_shipmentInfoModel.venderId jdcd_validateString]) {
        [tempParams addEntriesFromDictionary:@{@"venderId": _shipmentInfoModel.venderId}];
    }
    
    return [NSDictionary dictionaryWithDictionary:tempParams];
}

@end
