//
//  JDISVSettlementShipmentTypeModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:店铺配送浮层物流类型Model->PartList
 模型命名空间:core.trade-FLR#balance.shipment-M#defaultShipmentInfoFloor ->partList
 模型partList namespace:core.trade-FLR#balance.shipment-P#shipmentInfo
 */
@interface JDISVSettlementShipmentTypeModel : NSObject
// 内部Key:C-P#shipmentInfo&basic

@property (nonatomic) BOOL selectedFlag; /**< selectedFlag:是否选中 */
@property (nonatomic, copy) NSString *shipmentInfoId; /**< shipmentInfoId:物流信息Id */
@property (nonatomic, copy) NSString *shipmentInfoName; /**< shipmentInfoName:物流信息名称 */
@property (nonatomic, copy) NSString *shipmentInfoType; /**< shipmentInfoType:物流信息类型 */
@property (nonatomic) BOOL availableFlag; /**< availableFlag:是否支持 */
@property (nonatomic, copy) NSString *shipmentUuid;
@end

NS_ASSUME_NONNULL_END
