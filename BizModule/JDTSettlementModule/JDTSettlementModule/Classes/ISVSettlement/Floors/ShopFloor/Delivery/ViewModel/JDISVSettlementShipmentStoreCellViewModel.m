//
//  JDISVSettlementShipmentStoreCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentStoreCellViewModel.h"
#import "JDISVSettlementShipmentVenderStoreModel.h"
#import "JDISVSettlementShipmentChangeStoreMapCardViewModel.h"

#import "NSAttributedString+ISVSettlementShop.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVSettlementShipmentStoreCellViewModel ()
@property (nonatomic, strong, readwrite) JDISVSettlementShipmentVenderStoreModel *storeModel;
@end

@implementation JDISVSettlementShipmentStoreCellViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShipmentStoreCell";
        self.cellIdentifier = @"JDISVSettlementShipmentStoreCell";
        self.registerType = JDISVSettlementShopFloorBaseCellViewModelRegisterTypeXib;
        self.cardViewModel = [[JDISVSettlementShipmentChangeStoreMapCardViewModel alloc] init];
        self.height = 0;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if (type == JDISVSettlementShipmentStoreCellViewModelUpdateTypeInterface) {
        [self updateViewModelAfterInterfaceWith:data];
        [self.cardViewModel updateViewModelWith:data];
    } else if (type == JDISVSettlementShipmentStoreCellViewModelUpdateTypeChangeCheckStatus) {
        if ([data isKindOfClass:NSNumber.class] && self.checkStatus != JDISVSettlementShipmentStoreCellViewModelSelectedStatusDisable) {
            self.checkStatus = [(NSNumber *)data integerValue];
            if (_checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusChecked) {
                [self.cardViewModel updateCheckStatus:YES];
            }
            if (_checkStatus == JDISVSettlementShipmentStoreCellViewModelSelectedStatusUnChecked) {
                [self.cardViewModel updateCheckStatus:NO];
            }
            
        }
    }
}

/// 接口请求后更新ViewModel
- (void)updateViewModelAfterInterfaceWith:(id)data {
    if ([data isKindOfClass:JDISVSettlementShipmentVenderStoreModel.class]) {
        self.storeModel = (JDISVSettlementShipmentVenderStoreModel *)data;
        self.latitude = [_storeModel.latitude doubleValue];
        self.longitude = [_storeModel.longitude doubleValue];
        // 店铺名
        if ([_storeModel.storeName jdcd_validateString]) {
            if ([_storeModel.stockStatus isEqualToString:@"0"]) {
                // 无货
                self.storeNameAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:_storeModel.storeName colorKey:@"#C5" fontKey:@"#T6" weight:UIFontWeightMedium];
            } else {
                // 有货
                self.storeNameAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:_storeModel.storeName colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightMedium];
            }
        } else {
            self.storeNameAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:@"" colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightMedium];
        }
        
        // 最近标识
        if ([_storeModel.recentlyMark isEqualToString:@"1"]) {
            self.recentRemarkImg = [self recentTag];
        } else {
            self.recentRemarkImg = nil;
        }
        
        // 距离
        if ([_storeModel.distance jdcd_validateString]) {
            NSString *distance = [NSString stringWithFormat:SettlementLan(@"checkout_stores_list_distance") ,_storeModel.distance];
            if ([_storeModel.stockStatus isEqualToString:@"0"]) {
                // 无货
                self.distanceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:distance colorKey:@"#C5" fontKey:@"#T7"];
            } else {
                // 有货
                self.distanceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:distance colorKey:@"#C7" fontKey:@"#T7"];
            }
            self.distanceLabelWidth = [self.distanceAttributedString ka_settlement_shop_widthOfAttributedStringHeight:20 fontKey:@"#T7"];
        } else {
            self.distanceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:@"" colorKey:@"#C7" fontKey:@"#T7"];
            self.distanceLabelWidth = 0;
        }
        
        // 营业时间
        self.bussinessTimeIconImg = [UIImage ka_iconWithName:JDIF_ICON_CLOCK_LINE_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        self.bussinessTimeTitleAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_stores_list_business_time_label")  colorKey:@"#C5" fontKey:@"#T7"];
        if ([_storeModel.businessHours jdcd_validateString]) {
            self.bussinessTimeAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:_storeModel.businessHours colorKey:@"#C5" fontKey:@"#T7"];
        } else {
            self.bussinessTimeAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:@"" colorKey:@"#C5" fontKey:@"#T7"];
        }
        
        // 地址
        self.locationIconImg = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        if ([_storeModel.storeAddress jdcd_validateString]) {
            self.locationAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:_storeModel.storeAddress colorKey:@"#C5" fontKey:@"#T7"];
            CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 66.f - 18.f;
            self.locationLabelHeight = [self.locationAttributedString ka_settlement_shop_heightOfAttributedStringWidth:labelWidth maxNumberOfLines:0 fontKey:@"#T7"];
        } else {
            self.locationAttributedString = nil;
            self.locationLabelHeight = 0;
        }
        
        self.height = 12.f /**< top */ + 23.f /**< store name */ + 12.f + 20.f /**< bussiness hours */ + 6.f + _locationLabelHeight + 12.f;
        
        
        // 无货标识
        if ([_storeModel.stockStatus isEqualToString:@"0"]) {
            // 无货
            self.checkStatus = JDISVSettlementShipmentStoreCellViewModelSelectedStatusDisable;
            self.stockoutTipAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"setllement_store_out_of_stock")  colorKey:@"#C9" fontKey:@"#T9"];
            self.height = self.height + 6.f + 17.f;
        } else {
            // 有货
            self.checkStatus = JDISVSettlementShipmentStoreCellViewModelSelectedStatusUnChecked;
            self.stockoutTipAttributedString = nil;
        }
        
    }
}

#pragma mark - Privita

- (UIImage *)recentTag {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:CGRectMake(0, 0, 32, 18)];
    [defaultTagBtn setTitle:SettlementLan(@"checkout_stores_list_recent")  forState:UIControlStateNormal];
    [defaultTagBtn renderL3WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    [defaultTagBtn setFrame:CGRectMake(0, 0, [defaultTagBtn jdcd_getSize].width + 10.0, 18)];
    return [self getImageFromView:defaultTagBtn];
}


- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}
@end
