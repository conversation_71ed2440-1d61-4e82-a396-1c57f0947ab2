//
//  JDISVSettlementShipmentSelectStoreCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentSelectStoreCell.h"

#import "JDISVSettlementShipmentSelectStoreCellViewModel.h"

@interface JDISVSettlementShipmentSelectStoreCell ()
@property (weak, nonatomic) IBOutlet UILabel *storeNameLabel;
@property (weak, nonatomic) IBOutlet UIImageView *recentMarkImgView;
@property (weak, nonatomic) IBOutlet UILabel *distanceLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *storeName_distanceLabel_space;
@property (weak, nonatomic) IBOutlet UIImageView *bussinessTimeIcon;
@property (weak, nonatomic) IBOutlet UIImageView *addressIcon;
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *bussinessTimeLabel;
@property (weak, nonatomic) IBOutlet UILabel *addressLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *addressLabelHeight;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImgView;
@property (weak, nonatomic) IBOutlet UIView *tipBgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tipBgViewHeight;
@property (weak, nonatomic) IBOutlet UILabel *tipsLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *recentMarkImgViewWidth;


@property (nonatomic, strong) JDISVSettlementShipmentSelectStoreCellViewModel *viewModel;
@end

@implementation JDISVSettlementShipmentSelectStoreCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.rightArrowImgView.contentMode = UIViewContentModeCenter;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShipmentSelectStoreCellViewModel.class]) {
        self.viewModel = (JDISVSettlementShipmentSelectStoreCellViewModel *)viewModel;
        self.storeNameLabel.attributedText = _viewModel.storeNameAttributedString;
        if (_viewModel.recentMartImg) {
            self.recentMarkImgView.hidden = NO;
            self.recentMarkImgView.image = _viewModel.recentMartImg;
            self.storeName_distanceLabel_space.constant = 28+6+6;
            self.recentMarkImgViewWidth.constant = self.viewModel.recentMartImg.size.width;
        } else {
            self.recentMarkImgView.hidden = YES;
            self.storeName_distanceLabel_space.constant = 6;
            self.recentMarkImgViewWidth.constant = 0;
        }
        
        self.distanceLabel.attributedText = _viewModel.distanceAttributedString;
        self.bussinessTimeIcon.image = _viewModel.bussinessTimeIcon;
        self.addressIcon.image = _viewModel.addressIcon;

        self.bussinessTimeTitleLabel.attributedText = _viewModel.bussinessTimeTitleAttributedString;
        if (_viewModel.bussinessTimeAttributedString) {
            self.bussinessTimeLabel.hidden = NO;
            self.bussinessTimeLabel.attributedText = _viewModel.bussinessTimeAttributedString;
        } else {
            self.bussinessTimeLabel.hidden = YES;
        }
        
        self.addressLabel.attributedText = _viewModel.addressAttributedString;
        self.addressLabelHeight.constant = _viewModel.addressLabelHeight;
        
        self.rightArrowImgView.image = _viewModel.rightArrowImg;
        
        self.tipBgView.backgroundColor = _viewModel.tipBgColor;
        self.tipBgViewHeight.constant = _viewModel.tipBgViewHeight;
        self.tipBgView.layer.masksToBounds = YES;
        self.tipBgView.layer.cornerRadius = _viewModel.tipBgViewCornerRadius;
        self.tipsLabel.attributedText = _viewModel.tipAttributedString;
    }
}

@end
