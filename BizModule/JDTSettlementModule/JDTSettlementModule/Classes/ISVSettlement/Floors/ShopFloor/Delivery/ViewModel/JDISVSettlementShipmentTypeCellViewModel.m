//
//  JDISVSettlementShipmentTypeCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentTypeCellViewModel.h"
#import "JDISVSettlementShipmentTypeModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

static const CGFloat kJDISVSettlementShipmentTypeCellViewModelLabelHeight = 30.f;
@interface JDISVSettlementShipmentTypeCellViewModel ()
@property (nonatomic, strong, readwrite) JDISVSettlementShipmentTypeModel *shipmentTypeModel;
@end

@implementation JDISVSettlementShipmentTypeCellViewModel
- (void)updateViewModelWith:(id)data {
    if ([data isKindOfClass:JDISVSettlementShipmentTypeModel.class]) {
        self.shipmentTypeModel = (JDISVSettlementShipmentTypeModel *)data;
        self.shipmentTypeTitle = _shipmentTypeModel.shipmentInfoName;
        self.selected = _shipmentTypeModel.selectedFlag;
        self.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R51"];
    }
}

- (void)updateSelectedStatus:(BOOL)selected {
    _selected = selected;
}

- (CGSize)cellSize {
    CGFloat labelHeight = 12.f*2 +kJDISVSettlementShipmentTypeCellViewModelLabelHeight;
    
    return CGSizeMake([self labelWidthWith:_shipmentTypeTitle selected:_selected], labelHeight);
}

- (CGFloat )labelWidthWith:(NSString *)title selected:(BOOL)selected {
    UIFont *titleFont;
    if (selected) {
        titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium];
    } else {
        titleFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    }
    
    return ceil([title jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, kJDISVSettlementShipmentTypeCellViewModelLabelHeight) textFont:titleFont maxNumberOfLines:1].width) + 12.f*2;
}
@end
