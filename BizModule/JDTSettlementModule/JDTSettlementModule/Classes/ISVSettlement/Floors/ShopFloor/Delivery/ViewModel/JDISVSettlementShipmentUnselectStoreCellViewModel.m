//
//  JDISVSettlementShipmentUnselectStoreCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentUnselectStoreCellViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "NSAttributedString+ISVSettlementShop.h"

@implementation JDISVSettlementShipmentUnselectStoreCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.height = 71.f;
        self.cellName = @"JDISVSettlementShipmentUnselectStoreCell";
        self.cellIdentifier = @"JDISVSettlementShipmentUnselectStoreCell";
        
        self.rightArrowImg = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        self.tipAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shipment_please_select_store")  colorKey:@"#C7" fontKey:@"#T7"];
        self.selectedOtherStoreAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shipment_select_other_store")  colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightMedium];
    }
    return self;
}
@end
