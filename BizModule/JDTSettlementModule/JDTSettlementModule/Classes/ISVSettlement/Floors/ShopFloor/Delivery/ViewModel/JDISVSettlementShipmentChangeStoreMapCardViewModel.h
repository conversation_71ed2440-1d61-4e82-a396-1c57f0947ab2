//
//  JDISVSettlementShipmentChangeStoreMapCardViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@class JDISVSettlementShipmentMapCardAnnotationUserModel;

@interface JDISVSettlementShipmentChangeStoreMapCardViewModel : NSObject
@property (nonatomic, copy) NSString *venderId;
@property (nonatomic, copy) NSString *storeId;
@property (nonatomic, assign) CGFloat longtitude;
@property (nonatomic, assign) CGFloat latitude;

@property (nonatomic, assign) CGFloat height; /**< 卡片高度 */
@property (nonatomic, assign) CGFloat width;
@property (nonatomic, assign) CGFloat topViewHeight;

@property (nonatomic, copy) NSAttributedString *storeNameAttributedString;
@property (nonatomic, copy) NSAttributedString *bussinessTimeAttribtedString;
@property (nonatomic, copy) NSAttributedString *addressAttributedString;

@property (nonatomic, assign) CGFloat addressLabelHeight;

@property (nonatomic, strong, nullable) UIImage *recentTagImgView;
@property (nonatomic, assign) CGFloat distanceLabelLeading;
@property (nonatomic, copy) NSAttributedString *distanceAttributedString;
@property (nonatomic) BOOL stockout;
@property (nonatomic, copy, nullable) NSAttributedString *stockoutAttributedString;

@property (nonatomic, strong) JDISVSettlementShipmentMapCardAnnotationUserModel *annotionUserData;

@property (nonatomic) BOOL isCheck;

- (void)updateViewModelWith:(id)data;
- (void)updateCheckStatus:(BOOL)check;
@end

NS_ASSUME_NONNULL_END
