//
//  JDISVSettlementShipmentVenderStoreModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

/*
 模型名称:店铺配送自提门店模型
 模型命名空间:cn_ybxt_b2c-FLR#balance.shipment-M#venderPickSubFloor -> partList
 模型partList venderPickSubFloor - > cn_ybxt_b2c-FLR#balance.shipment-P#venderStorePart
 */
NS_ASSUME_NONNULL_BEGIN
@interface JDISVSettlementShipmentVenderStoreModel : NSObject
// 内部Key:VCYBB2C-P#venderStorePart&basic
@property (nonatomic, copy) NSString *distance; /**< distance:距离 */
@property (nonatomic, copy) NSString *latitude; /**< latitude:维度 */
@property (nonatomic, copy) NSString *longitude;/**< longitude:经度 */
@property (nonatomic) BOOL storeMarkFlag; /**< storeMarkFlag:是否可以自提 */
@property (nonatomic, copy) NSString *venderId; /**< venderId:店铺Id */
@property (nonatomic, copy) NSString *businessHours; /**< businessHours:营业时间 */
@property (nonatomic, copy) NSString *storeId; /**< storeId:门店Id */
@property (nonatomic, copy) NSString *venderStoreStockTab; /**< venderStoreStockTab:仓标签 (门店自提库存接口切换需要回传参数) */
@property (nonatomic, copy) NSString *venderSource; /**< venderSource:站点渠道来源 (门店自提库存接口切换需要回传参数) */
@property (nonatomic, copy) NSString *storeAddress; /**< storeAddress:门店地址 */
@property (nonatomic, copy) NSString *stockStatus; /**< stockStatus:是否有货，0：没货，1：有货 */
@property (nonatomic, copy) NSString *storeName; /**< storeName:门店名称 */
@property (nonatomic, copy) NSString *wareHouseId; /**< wareHouseId:仓库ID */
// 0：不是最近距离的门店 1：是最近距离的门店
@property (nonatomic, copy) NSString *recentlyMark; /**< recentlyMark:最近距离门店的标记 */
@end

NS_ASSUME_NONNULL_END
