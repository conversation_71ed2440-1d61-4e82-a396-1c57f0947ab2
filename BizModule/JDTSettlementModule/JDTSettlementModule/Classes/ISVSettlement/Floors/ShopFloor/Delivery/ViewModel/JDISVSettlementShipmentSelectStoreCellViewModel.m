//
//  JDISVSettlementShipmentSelectStoreCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentSelectStoreCellViewModel.h"

#import "JDISVSettlementShipmentVenderStoreModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import "NSAttributedString+ISVSettlementShop.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVSettlementShipmentSelectStoreCellViewModel ()
@property (nonatomic, strong) JDISVSettlementShipmentVenderStoreModel *model;
@end

@implementation JDISVSettlementShipmentSelectStoreCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementShipmentSelectStoreCell";
        self.cellIdentifier = @"JDISVSettlementShipmentSelectStoreCell";
        self.height = 0;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementShipmentVenderStoreModel.class]) {
        self.model = (JDISVSettlementShipmentVenderStoreModel *)data;
        NSString *storeName = @"";
        if ([_model.storeName jdcd_validateString]) {
            storeName = [_model.storeName copy];
        }
        self.storeNameAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:storeName colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightMedium];
        if ([_model.recentlyMark isEqualToString:@"1"]) {
            self.recentMartImg = [self recentTag];
        } else {
            self.recentMartImg = nil;
        }
        NSString *distance = SettlementLan(@"distance") ;
        if ([_model.distance jdcd_validateString]) {
            distance = [NSString stringWithFormat:SettlementLan(@"checkout_stores_list_distance") , _model.distance];
        }
        self.distanceAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:distance colorKey:@"#C7" fontKey:@"#T7"];
        
        self.bussinessTimeIcon = [UIImage ka_iconWithName:JDIF_ICON_CLOCK_LINE_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        self.addressIcon = [UIImage ka_iconWithName:JDIF_ICON_LOCATION_LINE_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        self.rightArrowImg = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        self.bussinessTimeTitleAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_stores_list_business_time_label")  colorKey:@"#C7" fontKey:@"#T7"];
        
        if ([_model.businessHours jdcd_validateString]) {
            self.bussinessTimeAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:_model.businessHours colorKey:@"#C7" fontKey:@"#T7"];
        } else {
            self.bussinessTimeAttributedString = nil;
        }
         
        NSString *address = @"";
        if ([_model.storeAddress jdcd_validateString]) {
            address = [_model.storeAddress copy];
        }
        self.addressAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:address colorKey:@"#C7" fontKey:@"#T7"];
        if ([self.addressAttributedString.string jdcd_validateString]) {
            self.addressLabelHeight = ceil([self.addressAttributedString ka_settlement_shop_heightOfAttributedStringWidth:CGRectGetWidth([[UIScreen mainScreen] bounds]) - 34.f - 85.f maxNumberOfLines:0 fontKey:@"#T7"]);
        } else {
            self.addressLabelHeight = 20.f;
        }
        self.tipBgColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07f];
        self.tipBgViewCornerRadius = 4.f;
        self.tipAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_stores_list_Tips") colorKey:@"#C12" fontKey:@"#T9"];
        CGFloat tipLabelHeight = ceil([self.tipAttributedString ka_settlement_shop_heightOfAttributedStringWidth:CGRectGetWidth([[UIScreen mainScreen] bounds]) - 30.f * 2 maxNumberOfLines:0 fontKey:@"#T9"]);
        self.tipBgViewHeight = 12.f * 2 + tipLabelHeight;
        self.height = 12.f /**<top */ + 23.f /**< storeName */ + 12.f + 20.f /**< bussinessTime */ + 6.f + self.addressLabelHeight + 18.f + self.tipBgViewHeight + 29.f;
        
    }
}

- (UIImage *)recentTag {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    [defaultTagBtn setTitle:SettlementLan(@"checkout_stores_list_recent")  forState:UIControlStateNormal];
    [defaultTagBtn renderL3WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    
    CGFloat width = [SettlementLan(@"checkout_stores_list_recent") jdcd_getStringSize:defaultTagBtn.titleLabel.font constraintsSize:CGSizeMake(MAXFLOAT, 18)].width + 7.f;
    [defaultTagBtn setFrame:CGRectMake(0, 0, width, 18)];
    
    return [self getImageFromView:defaultTagBtn];
}


- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}
@end
