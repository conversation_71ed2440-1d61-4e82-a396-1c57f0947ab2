//
//  JDISVSettlementShipmentTypeCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentTypeCell.h"
#import "JDISVSettlementShipmentTypeCellViewModel.h"

#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

@interface JDISVSettlementShipmentTypeCell ()
@property (weak, nonatomic) IBOutlet KALabel *shipmentTypeButton;

@property (nonatomic, strong) JDISVSettlementShipmentTypeCellViewModel *shipmentTypeViewModel;
@end

@implementation JDISVSettlementShipmentTypeCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.shipmentTypeButton.userInteractionEnabled = NO;
}

- (void)updateWith:(JDISVSettlementShipmentTypeCellViewModel *)shipmentTypeViewModel {
    _shipmentTypeViewModel = shipmentTypeViewModel;
    [self.shipmentTypeButton renderWithType:KALabelTypeL1];
    [self.shipmentTypeButton setKATitle:_shipmentTypeViewModel.shipmentTypeTitle];
    [self.shipmentTypeButton renderL1WithCornerRadius:_shipmentTypeViewModel.cornerRadius];
    
    self.shipmentTypeButton.jdisv_selected_L1 = _shipmentTypeViewModel.selected ? @(1):@(0);
}


@end
