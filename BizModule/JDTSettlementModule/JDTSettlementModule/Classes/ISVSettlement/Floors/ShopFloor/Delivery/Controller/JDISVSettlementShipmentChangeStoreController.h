//
//  JDISVSettlementShipmentChangeStoreController.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <UIKit/UIKit.h>
#import "JDISVSettlementShopFloorBaseController.h"
NS_ASSUME_NONNULL_BEGIN
@class JDISVSettlementShipmentVenderStoreModel;
@class JDISVAddress;
typedef void(^JDISVSettlementShipmentChangeStoreControllerSelectedCallback)(JDISVSettlementShipmentVenderStoreModel *selectedStoreModel);
@interface JDISVSettlementShipmentChangeStoreController : JDISVSettlementShopFloorBaseController
@property (nonatomic, copy) JDISVSettlementShipmentChangeStoreControllerSelectedCallback selectedCallback;  /**< 点击关闭 浮层消失回调 */
- (void)updateWithViewModel:(JDISVSettlementShipmentVenderStoreModel *)selectedStoreModel;
- (JDISVAddress *)venderAddress;
@end

NS_ASSUME_NONNULL_END
