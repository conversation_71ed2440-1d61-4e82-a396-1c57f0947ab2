//
//  JDISVSettlementShipmentPromiseCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentPromiseCellViewModel.h"
#import "JDISVSettlementShipmentDeliveryPromiseModel.h"

#import "NSAttributedString+ISVSettlementShop.h"

@implementation JDISVSettlementShipmentPromiseCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.height = 0;
        self.cellName = @"JDISVSettlementShipmentPromiseCell";
        self.cellIdentifier = @"JDISVSettlementShipmentPromiseCell";
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementShipmentDeliveryPromiseModel.class]) {
        JDISVSettlementShipmentDeliveryPromiseModel *model = (JDISVSettlementShipmentDeliveryPromiseModel *)data;
        if ([model.showTip jdcd_validateString]) {
            self.promiseAttributedString = [[NSAttributedString alloc] ka_settlement_shop_initWithString:model.showTip colorKey:@"#C7" fontKey:@"#T7"];
            CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f*2;
            self.promiseLabelHeight = [self.promiseAttributedString ka_settlement_shop_heightOfAttributedStringWidth:labelWidth maxNumberOfLines:0 fontKey: @"#T7"];
            self.height = self.promiseLabelHeight + 12.f*2;
        } else {
            self.promiseAttributedString = nil;
            self.height = 0;
        }
    }
}
@end
