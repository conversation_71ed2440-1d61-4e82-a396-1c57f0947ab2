//
//  JDISVSettlementShipmentDeliveryPromiseModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:店铺配送浮层配送Promise模型
 模型命名空间:core.trade-FLR#balance.shipment-M#defaultPromiseTimeFloor
 uuid:efaultPromiseTimeFloor
 */
@interface JDISVSettlementShipmentDeliveryPromiseModel : NSObject
// 内部Key:C-M#defaultPromiseTimeFloor&hint
@property (nonatomic, copy) NSString *showTip; /**< showTip:promise提示信息 */
@end

NS_ASSUME_NONNULL_END
