//
//  JDISVSettlementShipmentController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentController.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import "JDISVSettlementShopFloorBaseCellViewModel.h"
#import "JDISVSettlementShipmentViewModel.h"
#import "JDISVSettlementShipmentTypeCellViewModel.h"
#import "JDISVSettlementShipmentChangeStoreViewModel.h"

#import "JDISVSettlementShopFloorCellActionParam.h"

#import "JDISVSettlementShopFloorBaseCell.h"
#import "JDISVSettlementShipmentTypeCell.h"

#import <JDISVMasonryModule/Masonry.h>
#import "NSBundle+JDISVSettlement.h"
#import "UIImage+JDISVSettlement.h"
#import "NSAttributedString+ISVSettlementShop.h"

#import "JDISVSettlementShipmentChangeStoreController.h"



@interface JDISVSettlementShipmentController () <UITableViewDelegate, UITableViewDataSource, UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>
@property (weak, nonatomic) IBOutlet UILabel *shipmentTitleLabel;
@property (weak, nonatomic) IBOutlet UIButton *closeButton;

// 配送方式
@property (weak, nonatomic) IBOutlet UIView *shipmentTypeView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *shipmentTypeViewHeight;
@property (weak, nonatomic) IBOutlet UICollectionView *shipmentCollectionView;



@property (weak, nonatomic) IBOutlet UITableView *tableView;

// 底部
@property (weak, nonatomic) IBOutlet UIView *bottomView;
@property (weak, nonatomic) IBOutlet UIButton *confirmButton;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomViewBottomSpace;

// ViewModel
@property (nonatomic, strong) JDISVSettlementShipmentViewModel *viewModel;

@property (nonatomic) BOOL mapFeatureFlag;
@end

@implementation JDISVSettlementShipmentController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    [self loadShipmentTypesData];
    
}



#pragma mark - UI
- (void)setupUI {
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.shipmentTitleLabel.superview.backgroundColor = [UIColor clearColor];
    self.shipmentTypeView.backgroundColor = [UIColor clearColor];
    self.shipmentCollectionView.backgroundColor = [UIColor clearColor];
    self.bottomView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
//    [self.closeButton setImage:[UIImage jdisvSettlement_imageNamed:@"jdka_settlement_float_close"] forState:UIControlStateNormal];
    [self.closeButton addTarget:self action:@selector(clickCloseButton) forControlEvents:UIControlEventTouchUpInside];
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
    [self.closeButton setImage:image forState:UIControlStateNormal];
    self.closeButton.layer.cornerRadius = 9;
    self.closeButton.layer.masksToBounds = YES;
    self.closeButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(18);
        make.width.height.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
    }];
    
    self.shipmentTitleLabel.attributedText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shipment_title")  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
    
    // 配送方式
    self.shipmentTypeViewHeight.constant = 0;
    // Collection
    self.shipmentCollectionView.delegate = self;
    self.shipmentCollectionView.dataSource = self;
    
    [self.shipmentCollectionView registerClass:NSClassFromString(@"UICollectionViewCell") forCellWithReuseIdentifier:@"UICollectionViewCell"];
    [self.shipmentCollectionView registerNib:[UINib nibWithNibName:@"JDISVSettlementShipmentTypeCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellWithReuseIdentifier:@"JDISVSettlementShipmentTypeCell"];
    
    // TableView
    [self setupTableView];
    
    // BottomView
    self.bottomViewHeight.constant = 0;
    self.bottomViewBottomSpace.constant = [self isv_safeAreaInsets].bottom;
    [self.confirmButton addTarget:self action:@selector(clickConfirmButton) forControlEvents:UIControlEventTouchUpInside];
    [self.confirmButton setTitle:SettlementLan(@"settlement_discount_code_detainment_commit") forState:UIControlStateNormal];
    [self.confirmButton setTitle:SettlementLan(@"settlement_discount_code_detainment_commit") forState:UIControlStateHighlighted];
    [self.confirmButton setTitle:SettlementLan(@"settlement_discount_code_detainment_commit") forState:UIControlStateDisabled];
    [self.confirmButton renderB1];
}

- (UIEdgeInsets)isv_safeAreaInsets {
    if (@available(iOS 11.0, *)) {
        UIEdgeInsets safeAreaInsets = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
        if (safeAreaInsets.bottom > 0) {
            return safeAreaInsets;
        }
        return UIEdgeInsetsMake(20, 0, 0, 0);
    }
    return UIEdgeInsetsMake(20, 0, 0, 0);
}

- (void)setupTableView {
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
  
    self.tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // Group样式的TableView Header和Footer会有空白
    self.tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    
    [self.tableView registerClass:UITableViewCell.class forCellReuseIdentifier:@"UITableViewCell"];
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShipmentPromiseCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier:@"JDISVSettlementShipmentPromiseCell"];
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShipmentSelectStoreCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier:@"JDISVSettlementShipmentSelectStoreCell"];
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShipmentUnselectStoreCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier:@"JDISVSettlementShipmentUnselectStoreCell"];
}

- (void)reloadShipmentTypeView {
    CGFloat cellsWidth = 0;
    for (JDISVSettlementShipmentTypeCellViewModel *shipmentTypeViewModel in self.viewModel.shipmentTypeViewModelArray) {
        cellsWidth += shipmentTypeViewModel.cellSize.width;
    }
    CGFloat contentViewWidth = 0;
    if (self.viewModel.shipmentTypeViewModelArray.count > 0) {
        contentViewWidth = cellsWidth + (self.viewModel.shipmentTypeViewModelArray.count - 1) * 12.f + 18.f * 2;
    }
    
    self.shipmentCollectionView.contentSize = CGSizeMake(contentViewWidth, self.viewModel.shipmentViewHeight);
    
    [self.shipmentCollectionView reloadData];
}

- (void)reloadView {
    self.shipmentTypeViewHeight.constant = self.viewModel.shipmentViewHeight;
    self.bottomViewHeight.constant = self.viewModel.bottomViewHeight;
    
    self.shipmentTypeView.hidden = (self.viewModel.shipmentViewHeight == 0);
    self.bottomView.hidden = (self.viewModel.bottomViewHeight == 0);
    
    // ShipmentTypeView
    [self reloadShipmentTypeView];
    
    [self.tableView reloadData];
}

#pragma mark - Data
- (void)updateViewModelWithVenderId:(NSString *)venderId sourceFrom:(NSUInteger)source mapFeatureSwith:(BOOL)flag {
    _mapFeatureFlag = flag;
    [self.viewModel updateWithVenderId:venderId sourceFrom:source];
}

- (void)loadShipmentTypesData {
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel signalOfRequestShipmentTypes] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    } completed:^{
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [self reloadView];
    }];
}

#pragma mark - Action
- (void)openChangeShipmentSelfStorePageWithMapFeatureSwitch:(BOOL)flag {
    JDISVSettlementShipmentChangeStoreController *changeStoreController = [[JDISVSettlementShipmentChangeStoreController alloc] initWithNibName:@"JDISVSettlementShipmentChangeStoreController" bundle:[NSBundle jdisvSettlement_bundle]];
    JDISVSettlementShipmentChangeStoreViewModel *changeStoreViewModel = [[JDISVSettlementShipmentChangeStoreViewModel alloc]
                                                                        initWithSelectedStoreModel:[self.viewModel shipmentInfoModel]
                                                                                    selectedStoreId:[self.viewModel selectedStoreId]
                                                                                         sourceForm:_viewModel.source
                                                                                    venderAddress:self.viewModel.venderAddress
                                                                                        mapFeature:_mapFeatureFlag];
    [changeStoreController updateWithViewModel:changeStoreViewModel];
    
    @weakify(self)
    changeStoreController.selectedCallback = ^(JDISVSettlementShipmentVenderStoreModel * _Nonnull selectedStoreModel) {
        @strongify(self)
        [self.viewModel updateWith:selectedStoreModel forType:JDISVSettlementShipmentViewModelUpdateTypeSelectStore];
        [self reloadView];
    };
    
    UINavigationController *changeStoreRootController = [[UINavigationController alloc] initWithRootViewController:changeStoreController];
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:changeStoreRootController presentingViewController:self];
    presentationVC.type = KAFloatLayerTypeCustom;
    presentationVC.contentHeight = [UIScreen mainScreen].bounds.size.height;
    presentationVC.insets = UIEdgeInsetsZero;
    
    presentationVC.animationDirection = KAFloatLayerDirectionRigthToLeft;
    
    presentationVC.dismissComplete = ^{
        if ([changeStoreController venderAddress]) {
            @strongify(self)
            self.viewModel.venderAddress = [changeStoreController venderAddress];
        }
    };
    
    
    changeStoreRootController.transitioningDelegate = presentationVC;
    [self presentViewController:changeStoreRootController animated:YES completion:nil];
}

- (void)clickCellIndexOf:(NSIndexPath *)indexPath {
    if (_viewModel.selectedIndex == 1) {
        // 打开选择门店浮层
        [self openChangeShipmentSelfStorePageWithMapFeatureSwitch:_mapFeatureFlag];
    }
}

- (void)processCellActionWithParames:(JDISVSettlementShopFloorCellActionParam *)param {
    
}

- (void)changeSelectedIndex:(NSUInteger)index {
    if (index == _viewModel.selectedIndex) return;
    [self.viewModel updateWith:@(index) forType:JDISVSettlementShipmentViewModelUpdateTypeChangeSelcetdTab];
    
    [self reloadView];
}

- (void)clickCloseButton {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)clickConfirmButton {
    if (self.viewModel.isUserClickSelected) {
        if (self.confirmCallBack) {
            self.confirmCallBack([self.viewModel saveShipmentRequestOrderStrParam]);
        }
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - TableView

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (_viewModel) {
        return [self.viewModel numberOfSections];
    }
    
    return 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (_viewModel) {
        return [self.viewModel numberOfRowsInSection:section];
    }
    
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (_viewModel) {
        return [self.viewModel heightForRowAtIndexPath:indexPath];
    }
    
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
   
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {

    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementShopFloorBaseCell *cell;
    JDISVSettlementShopFloorBaseCellViewModel *itemViewModel;
    if (_viewModel) {
        itemViewModel = [self.viewModel viewModelForCurrentCell];
    }
    cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.cellIdentifier forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
        @weakify(self)
        [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(JDISVSettlementShopFloorCellActionParam *parames) {
            @strongify(self)
            [self processCellActionWithParames:parames];
        }];
    }
    
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    JDISVSettlementShopFloorBaseCellViewModel *itemViewModel;
    if (_viewModel) {
        itemViewModel = [self.viewModel viewModelForCurrentCell];
    }
    if ([cell respondsToSelector:@selector(updateCellWithViewModel:)]) {
        [cell performSelector:@selector(updateCellWithViewModel:) withObject:itemViewModel];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    [self clickCellIndexOf:indexPath];
    
    
}

#pragma mark - CollectionView
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    if (self.viewModel.shipmentTypeViewModelArray && self.viewModel.shipmentTypeViewModelArray.count > 0) {
        return 1;
    }
    return 0;
    
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    
    if (self.viewModel.shipmentTypeViewModelArray && self.viewModel.shipmentTypeViewModelArray.count > 0) {
        return self.viewModel.shipmentTypeViewModelArray.count;
    }
    return 0;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.viewModel.shipmentTypeViewModelArray && self.viewModel.shipmentTypeViewModelArray.count > 0 && indexPath.row < self.viewModel.shipmentTypeViewModelArray.count) {
        JDISVSettlementShipmentTypeCellViewModel *shipmentTypeCellViewModel = [self.viewModel.shipmentTypeViewModelArray objectAtIndex:indexPath.row];
        return shipmentTypeCellViewModel.cellSize;
    }
    return CGSizeZero;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    
    if (self.viewModel.shipmentTypeViewModelArray && self.viewModel.shipmentTypeViewModelArray.count > 0) {
        return 12.f;
    }
    return 0;
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, 18, 0, 18);
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementShipmentTypeCell *cell = nil;
    cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JDISVSettlementShipmentTypeCell" forIndexPath:indexPath];
    if (cell == nil) {
        cell = [UICollectionViewCell new];
    }
    
    JDISVSettlementShipmentTypeCellViewModel *shipmentTypeCellViewModel;
    if (self.viewModel.shipmentTypeViewModelArray && self.viewModel.shipmentTypeViewModelArray.count > 0 && indexPath.row < self.viewModel.shipmentTypeViewModelArray.count) {
        shipmentTypeCellViewModel = [self.viewModel.shipmentTypeViewModelArray objectAtIndex:indexPath.row];
    }
    if ([cell respondsToSelector: @selector(updateWith:)] && shipmentTypeCellViewModel) {
        [cell performSelector:@selector(updateWith:) withObject:shipmentTypeCellViewModel];
    }

    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementShipmentTypeCellViewModel *shipmentTypeCellViewModel;
    if (self.viewModel.shipmentTypeViewModelArray && self.viewModel.shipmentTypeViewModelArray.count > 0 && indexPath.row < self.viewModel.shipmentTypeViewModelArray.count) {
        shipmentTypeCellViewModel = [self.viewModel.shipmentTypeViewModelArray objectAtIndex:indexPath.row];
    }
    if ([cell respondsToSelector: @selector(updateWith:)] && shipmentTypeCellViewModel) {
        [cell performSelector:@selector(updateWith:) withObject:shipmentTypeCellViewModel];
    }
    
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [self changeSelectedIndex:indexPath.row];
}

#pragma mark - Lazy Load
- (JDISVSettlementShipmentViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[JDISVSettlementShipmentViewModel alloc] init];
    }
    
    return _viewModel;
}


@end
