//
//  JDISVSettlementShipmentStoreCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, JDISVSettlementShipmentStoreCellViewModelSelectedStatus) {
    JDISVSettlementShipmentStoreCellViewModelSelectedStatusUnChecked = 0, /**< 未选中 */
    JDISVSettlementShipmentStoreCellViewModelSelectedStatusChecked = 1, /**< 选中 */
    JDISVSettlementShipmentStoreCellViewModelSelectedStatusDisable = -1 /**< 不可选 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementShipmentStoreCellViewModelUpdateType) {
    JDISVSettlementShipmentStoreCellViewModelUpdateTypeInterface = 0, /**< 主接口更新 */
    JDISVSettlementShipmentStoreCellViewModelUpdateTypeChangeCheckStatus, /**< 修改选中状态 */
};

@class JDISVSettlementShipmentVenderStoreModel;
@class JDISVSettlementShipmentChangeStoreMapCardViewModel;

@interface JDISVSettlementShipmentStoreCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic, assign) JDISVSettlementShipmentStoreCellViewModelSelectedStatus checkStatus;
@property (nonatomic, copy) NSAttributedString *storeNameAttributedString;
@property (nonatomic, strong, nullable) UIImage *recentRemarkImg;
@property (nonatomic, copy, nullable) NSAttributedString *distanceAttributedString;
@property (nonatomic, assign) CGFloat distanceLabelWidth;
@property (nonatomic, strong) UIImage *bussinessTimeIconImg;
@property (nonatomic, copy) NSAttributedString *bussinessTimeTitleAttributedString;
@property (nonatomic, copy) NSAttributedString *bussinessTimeAttributedString;
@property (nonatomic, strong) UIImage *locationIconImg;
@property (nonatomic, copy, nullable) NSAttributedString *locationAttributedString;
@property (nonatomic, assign) CGFloat locationLabelHeight;
@property (nonatomic, copy, nullable) NSAttributedString *stockoutTipAttributedString;

@property (nonatomic, assign) CGFloat latitude;
@property (nonatomic, assign) CGFloat longitude;
@property (nonatomic, strong) JDISVSettlementShipmentChangeStoreMapCardViewModel *cardViewModel;
@property (nonatomic, strong, readonly) JDISVSettlementShipmentVenderStoreModel *storeModel;
@end

NS_ASSUME_NONNULL_END
