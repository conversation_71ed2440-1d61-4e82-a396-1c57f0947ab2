//
//  JDISVSettlementShipmentChangeStoreViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorBaseViewModel.h"
#import <CoreLocation/CoreLocation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementShipmentChangeStoreViewModelUpdateType) {
    JDISVSettlementShipmentChangeStoreViewModelUpdateTypeReuqest = 0, /**< 网络接口 */
    JDISVSettlementShipmentChangeStoreViewModelUpdateTypeChangeStore, /**< 修改选中门店 */
    JDISVSettlementShipmentChangeStoreViewModelUpdateTypeChangeAddress, /**< 修改地址 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementShipmentChangeStoreViewModelContentStatus) {
    JDISVSettlementShipmentChangeStoreViewModelContentStatusFold = 0, /**< 收起 */
    JDISVSettlementShipmentChangeStoreViewModelContentStatusUnfold, /**< 展开 */
    JDISVSettlementShipmentChangeStoreViewModelContentStatusSwipeUp, /**< 向上滑动 */
    JDISVSettlementShipmentChangeStoreViewModelContentStatusSwipeDown /**< 向下滑动 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementShipmentChangeStoreViewModelContentActionType) {
    JDISVSettlementShipmentChangeStoreViewModelContentActionTypeTap = 0, /**< 点击 */
    JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMoved, /**< 滑动 */
    JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMapMoveEnd, /**< 移动结束 */
    JDISVSettlementShipmentChangeStoreViewModelContentActionTypeMapMoved /**< 地图移动 */
};

@class JDISVAddress;
@class JDISVSettlementShipmentInfoModel;
@class JDISVSettlementShipmentStoreCellViewModel;
@class RACSignal;

@interface JDISVSettlementShipmentChangeStoreViewModel : JDISVSettlementShopFloorBaseViewModel
// Map
@property (nonatomic, assign) CLLocationCoordinate2D consigneeLocationCoorddinate;

// ContentView
@property (nonatomic, copy) NSAttributedString *addressAttributedString;

@property (nonatomic, copy, readonly) NSArray *cellViewModels;

@property (nonatomic, assign) JDISVSettlementShipmentChangeStoreViewModelContentStatus contentViewStyle;

@property (nonatomic) BOOL mapFeatureFlag;


- (instancetype)initWithSelectedStoreModel:(JDISVSettlementShipmentInfoModel * __nullable)shipmentInfoModel
                           selectedStoreId:(NSString *)selectedStoreId
                                sourceForm:(NSInteger)source
                             venderAddress:(JDISVAddress *)venderAddress
                                mapFeature:(BOOL)mapFeatureFlag;

/// ContentView
/// @param actionType 动作类型
/// @param offsetY 滑动时的Offset
- (RACSignal *)signalOfChangeContentViewStatusActionOf:(JDISVSettlementShipmentChangeStoreViewModelContentActionType)actionType
                                               offsetY:(CGFloat) offsetY;

/// 请求门店列表信息
- (RACSignal *)signalOfLoadStoreListInfoWith:(JDISVAddress * __nullable)address actionType:(JDISVSettlementShipmentChangeStoreViewModelUpdateType)actionType;

- (RACSignal *)signalOfSelectedStoreIndexOf:(NSUInteger)index;

/// Getter
- (NSString *)venderId;
- (JDISVAddress *)venderAddress;
- (NSAttributedString *)emptyViewContent;
- (CGFloat)emptyViewHeight;
- (BOOL)showEmptyView;

- (CGFloat)contentViewHeight;
- (CGFloat)contentViewY;
- (CGFloat)contentViewMaxY;
- (CGFloat)contentViewMinY;
- (CGFloat)contentViewBottom;
- (CGFloat)contentTitleTop;
- (NSTextAlignment)contentTitleAlignment;
- (CGFloat)contentTitleViewRadius;
- (NSAttributedString *)contentTitleAttributedString;

- (JDISVSettlementShipmentStoreCellViewModel * __nullable)selectedStoreViewModel;
- (NSString *)storeIdOfMaybeShowMapAnnotationCustomCalloutView;
@end

NS_ASSUME_NONNULL_END
