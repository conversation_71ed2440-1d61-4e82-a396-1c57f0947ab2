//
//  JDISVSettlementShipmentViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShipmentViewModel.h"

#import "JDISVSettlementShopFloorModel.h"
#import "JDISVSettlementShopFloorMainModel.h"
#import "JDISVSettlementShipmentInfoModel.h"
#import "JDISVSettlementShipmentDeliveryPromiseModel.h"
#import "JDISVSettlementShipmentVenderStoreModel.h"
#import "JDISVSettlementShipmentTypeModel.h"
#import "JDISVSettlementShipmentPromiseCellViewModel.h"
#import "JDISVSettlementShipmentSelectStoreCellViewModel.h"
#import "JDISVSettlementShipmentUnselectStoreCellViewModel.h"
#import "JDISVSettlementShipmentTypeCellViewModel.h"
#import "JDISVSettlementShopFloorAddressModel.h"
#import "JDISVSettlementShopFloorBaseCellViewModel.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import "JDISVSettlementShopFloorNetService.h"
#import "JDISVSettlementShopFloorAddressManager.h"

static const CGFloat kJDISVSettlementShipmentViewModelTitleViewHeight = 54.f;
static const CGFloat kJDISVSettlementShipmentViewModelBottomViewHeight = 50.f;

@interface JDISVSettlementShipmentViewModel ()
// 公共参数
@property (nonatomic, copy) NSString *venderId;
@property (nonatomic, assign, readwrite) NSUInteger source;
// model
@property (nonatomic, strong) JDISVSettlementShipmentInfoModel *shipmentInfoModel; /**< 门店类型 */
@property (nonatomic, strong) JDISVSettlementShipmentDeliveryPromiseModel *promiseModel; /**< promise */
@property (nonatomic, strong) JDISVSettlementShipmentVenderStoreModel *venderStoreModel; /**< 店铺门店 */

@property (nonatomic, strong) JDISVSettlementShipmentPromiseCellViewModel *promiseCellViewModel; /**< Promise ViewModel */
@property (nonatomic, strong) JDISVSettlementShipmentSelectStoreCellViewModel *selectedStoreCellViewModel; /**< 已选店铺 ViewModel*/
@property (nonatomic, strong) JDISVSettlementShipmentUnselectStoreCellViewModel *unSelectedStoreCellViewModel; /**< 未选店铺 */

@end

@implementation JDISVSettlementShipmentViewModel

#pragma mark - Data

- (void)updateWithVenderId:(NSString *)venderId sourceFrom:(NSUInteger)source{
    self.venderId = [venderId copy];
    self.source = source;

    [self updateVenderAddressWith:[[JDISVSettlementShopFloorAddressManager sharedService] addressFloorModel]];
}

- (void)updateWith:(id)data forType:(NSInteger)type {
    if (type == JDISVSettlementShipmentViewModelUpdateTypeRequestShipmentTypes) {
        // 更新title
        if ([data isKindOfClass:JDISVSettlementShopFloorMainModel.class]) {
            JDISVSettlementShopFloorMainModel *model = (JDISVSettlementShopFloorMainModel *)data;
            
            [self updateShipmentModelsWith:model.floors];
            // ShipmentType
            [self updateShipmentTypesWith:_shipmentInfoModel.shipmentTypes];
            // Promise
            [self updatePromiseViewModelWith:_promiseModel];
            // 已选店铺
            [self updateSelectedStoreViewModelWith:_venderStoreModel];
            // 未选店铺
            [self updateUnselectedStoreViewModel];
        }
    } else if (type == JDISVSettlementShipmentViewModelUpdateTypeChangeSelcetdTab) {
        NSNumber *selectedIndex = (NSNumber *)data;
        self.isUserClickSelected = YES;
        self.selectedIndex = [selectedIndex unsignedIntegerValue];
        [self.shipmentTypeViewModelArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            JDISVSettlementShipmentTypeCellViewModel *shipmentTypeCellViewModel = (JDISVSettlementShipmentTypeCellViewModel *)obj;
            [shipmentTypeCellViewModel updateSelectedStatus:[selectedIndex unsignedIntegerValue] == idx];
        }];
    } else if (type == JDISVSettlementShipmentViewModelUpdateTypeSelectStore) {
        // 门店选择 - 选中门店
        self.isUserClickSelected = YES;
        if ([data isKindOfClass:JDISVSettlementShipmentVenderStoreModel.class]) {
            JDISVSettlementShipmentVenderStoreModel *storeModel = (JDISVSettlementShipmentVenderStoreModel *)data;
            [self updateSelectedStoreViewModelWith:storeModel];
        } else {
            [self updateSelectedStoreViewModelWith:nil];
        }
        
    }
}

#pragma mark - 模型更新

// 更新模型
- (void)updateShipmentModelsWith:(NSArray *)floorDatas {
    for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:@"defaultShipmentInfoFloor"]) {
            // // ShipmentInfoModel
            self.shipmentInfoModel = [JDISVSettlementShipmentInfoModel yy_modelWithDictionary:floorData.info];
            NSMutableArray *shipmentTypeModels = [NSMutableArray array];
            for (JDISVSettlementShopFloorModel *partItem in floorData.partList) {
                if ([partItem.type.nameSpace isEqualToString:@"core.trade-FLR#balance.shipment-P#shipmentInfo"]) {
                    JDISVSettlementShipmentTypeModel *shipmentTypeModel = [JDISVSettlementShipmentTypeModel yy_modelWithDictionary:partItem.info];
                    [shipmentTypeModels addObject:shipmentTypeModel];
                }
            }
            if (shipmentTypeModels.count > 0) {
                self.shipmentInfoModel.shipmentTypes = [NSArray arrayWithArray:shipmentTypeModels];
            } else {
                self.shipmentInfoModel.shipmentTypes = nil;
            }
        } else if ([floorData.uuid containsString:@"defaultPromiseTimeFloor"]) {
            // Promise
            self.promiseModel = [JDISVSettlementShipmentDeliveryPromiseModel yy_modelWithDictionary:floorData.info];
        } else if ([floorData.uuid containsString:@"venderPickSubFloor"]) {
            // 已选
            for (JDISVSettlementShopFloorModel *partItem in floorData.partList) {
                if ([partItem.type.nameSpace isEqualToString:@"cn_ybxt_b2c-FLR#balance.shipment-P#venderStorePart"]) {
                    self.venderStoreModel = [JDISVSettlementShipmentVenderStoreModel yy_modelWithDictionary:partItem.info];
                }
            }
        }
    }
}

// 更新ShipmentTypes
- (void)updateShipmentTypesWith:(NSArray *)shipmentTypeModels {
    NSMutableArray *shipmentViewModels = [NSMutableArray array];
    for (JDISVSettlementShipmentTypeModel *shipmentTypeModel in shipmentTypeModels) {
        JDISVSettlementShipmentTypeCellViewModel *typeCellViewModel = [[JDISVSettlementShipmentTypeCellViewModel alloc] init];
        [typeCellViewModel updateViewModelWith:shipmentTypeModel];
        [shipmentViewModels addObject:typeCellViewModel];
    }
    if (shipmentViewModels.count > 0) {
        self.shipmentTypeViewModelArray = [NSArray arrayWithArray:shipmentViewModels];
        JDISVSettlementShipmentTypeCellViewModel *typeCellViewModel = [self.shipmentTypeViewModelArray firstObject];
        self.selectedIndex = typeCellViewModel.selected ? 0 : 1;
    } else {
        self.shipmentTypeViewModelArray = nil;
    }
}

// 更新Promise
- (void)updatePromiseViewModelWith:(JDISVSettlementShipmentDeliveryPromiseModel *)promiseModel {
    if (promiseModel) {
        self.promiseCellViewModel = [[JDISVSettlementShipmentPromiseCellViewModel alloc] init];
        [self.promiseCellViewModel updateWithData:promiseModel forType:0];
    }
}

// 更新已选店铺
- (void)updateSelectedStoreViewModelWith:(JDISVSettlementShipmentVenderStoreModel *)venderStoreModel {
    if (venderStoreModel) {
        self.venderStoreModel = venderStoreModel;
        self.selectedStoreCellViewModel = [[JDISVSettlementShipmentSelectStoreCellViewModel alloc] init];
        [self.selectedStoreCellViewModel updateWithData:venderStoreModel forType:0];
    } else {
        self.venderStoreModel = nil;
        self.selectedStoreCellViewModel = nil;
    }
}

// 更新未选店铺
- (void)updateUnselectedStoreViewModel {
    self.unSelectedStoreCellViewModel = [[JDISVSettlementShipmentUnselectStoreCellViewModel alloc] init];
}

- (void)updateVenderAddressWith:(id)data {
    if ([data isKindOfClass:JDISVSettlementShopFloorAddressModel.class]) {
        JDISVSettlementShopFloorAddressModel *addressFloorModel = (JDISVSettlementShopFloorAddressModel *)data;
        JDTAddressItemModel *address = [[JDTAddressItemModel alloc] init];
        address.provinceId = addressFloorModel.areaId.provinceId.stringValue;
        address.provinceName = addressFloorModel.areaName.provinceName;
        address.cityId = addressFloorModel.areaId.cityId.stringValue;
        address.cityName = addressFloorModel.areaName.cityName;
        address.districtId = addressFloorModel.areaId.areaId.stringValue;
        address.districtName = addressFloorModel.areaName.areaName;
        address.townId = addressFloorModel.areaId.townId.stringValue;
        address.townName = addressFloorModel.areaName.townName;
        address.addressId = addressFloorModel.addressId.integerValue;
        address.fullAddress = addressFloorModel.fullAddress;
        address.addressDetail = addressFloorModel.detailAdress;
        self.venderAddress = address;
    } else if ([data isKindOfClass:JDTAddressItemModel.class]) {
        self.venderAddress = (JDTAddressItemModel *)data;
    }
}

- (JDTAddressItemModel *)venderAddress {
    return _venderAddress;
}

#pragma mark - TableView
- (NSInteger)numberOfSections {
    if (self.shipmentTypeViewModelArray.count > 0) {
        return 1;
    } else {
        return 0;
    }
}

- (NSInteger)numberOfRowsInSection:(NSInteger)section {
    if (self.shipmentTypeViewModelArray.count > 0) {
        return 1;
    } else {
        return 0;
    }
}

- (CGFloat)heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (_selectedIndex == 0) {
        return _promiseCellViewModel.height;
    } else {
        if (_selectedStoreCellViewModel) {
            return _selectedStoreCellViewModel.height;
        }
        
        return _unSelectedStoreCellViewModel.height;
    }
}


- (JDISVSettlementShopFloorBaseCellViewModel *)viewModelForCurrentCell {
    if (_selectedIndex == 0) {
        return _promiseCellViewModel;
    } else {
        if (_selectedStoreCellViewModel) {
            return _selectedStoreCellViewModel;
        }
        
        return _unSelectedStoreCellViewModel;
    }
}

- (JDISVSettlementShipmentInfoModel *)shipmentInfoModel {
    return _shipmentInfoModel;
}

- (NSString *)selectedStoreId {
    if (_selectedIndex == 1 && _venderStoreModel && [_venderStoreModel.storeId jdcd_validateString]) {
        return _venderStoreModel.storeId;
    }
    
    return @"";
}

#pragma mark - 信号
- (RACSignal *)signalOfRequestShipmentTypes {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSString *venderId = @"";
        if ([self.venderId jdcd_validateString]) {
            venderId = [self.venderId copy];
        }
        NSDictionary *param = @{@"venderIds": [NSArray arrayWithObject:venderId]};
        [KASettlementShopFloorNetService requestShipmentTypesWithParam:param sourceFrom:self.source complete:^(JDISVSettlementShopFloorMainModel * _Nonnull model, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [self updateWith:model forType:JDISVSettlementShipmentViewModelUpdateTypeRequestShipmentTypes];
                [subscriber sendCompleted];
            }
        }];
        
        return nil;
    }];
}

#pragma mark - Getter

- (CGFloat)shipmentViewHeight {
    if (self.shipmentTypeViewModelArray && self.shipmentTypeViewModelArray.count > 0) {
        return kJDISVSettlementShipmentViewModelTitleViewHeight;
    } else {
        return 0;
    }
}

- (CGFloat)bottomViewHeight {
    if (self.shipmentTypeViewModelArray && self.shipmentTypeViewModelArray.count > 0) {
        return kJDISVSettlementShipmentViewModelBottomViewHeight;
    } else {
        return 0;
    }
}

- (NSDictionary *)saveShipmentRequestOrderStrParam {
    NSMutableDictionary *tempDic = [NSMutableDictionary dictionary];
    if (_selectedIndex < _shipmentTypeViewModelArray.count) {
        JDISVSettlementShipmentTypeCellViewModel *typeCellViewModel = [self.shipmentTypeViewModelArray objectAtIndex:_selectedIndex];
        // shipmentUUid
        if ([typeCellViewModel.shipmentTypeModel.shipmentUuid jdcd_validateString]) {
            [tempDic addEntriesFromDictionary:@{@"shipmentUUid": typeCellViewModel.shipmentTypeModel.shipmentUuid}];
        }
        // shipmentId
        if ([typeCellViewModel.shipmentTypeModel.shipmentInfoId jdcd_validateString]) {
            [tempDic addEntriesFromDictionary:@{@"shipmentId": typeCellViewModel.shipmentTypeModel.shipmentInfoId}];
        }
        // venderId
        if ([self.shipmentInfoModel.venderId jdcd_validateString]) {
            [tempDic addEntriesFromDictionary:@{@"venderId": self.shipmentInfoModel.venderId}];
        }
        // bundleUuid
        if ([self.shipmentInfoModel.bundleUuid jdcd_validateString]) {
            [tempDic addEntriesFromDictionary:@{@"bundleUuid": self.shipmentInfoModel.bundleUuid}];
        }
        // orderUuid
        if ([self.shipmentInfoModel.orderUuid jdcd_validateString]) {
            [tempDic addEntriesFromDictionary:@{@"orderUuid": self.shipmentInfoModel.orderUuid}];
        }
        // combinationBundleUuid
        if ([self.shipmentInfoModel.combinationBundleUuid jdcd_validateString]) {
            [tempDic addEntriesFromDictionary:@{@"combinationBundleUuid": self.shipmentInfoModel.combinationBundleUuid}];
        }
        // venderStore
        if (_selectedIndex == 1 && _venderStoreModel) {
            // 门店自提
            NSMutableDictionary *venderStoreDic = [NSMutableDictionary dictionary];
            // storeId
            if ([_venderStoreModel.storeId jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"storeId": _venderStoreModel.storeId}];
            }
            // storeName
            if ([_venderStoreModel.storeName jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"storeName": _venderStoreModel.storeName}];
            }
            // storeAddress
            if ([_venderStoreModel.storeAddress jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"storeAddress": _venderStoreModel.storeAddress}];
            }
            // businessHours
            if ([_venderStoreModel.businessHours jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"businessHours": _venderStoreModel.businessHours}];
            }
            // distance
            if ([_venderStoreModel.distance jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"distance": _venderStoreModel.distance}];
            }
            // latitude
            if ([_venderStoreModel.latitude jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"latitude": _venderStoreModel.latitude}];
            }
            // longitude
            if ([_venderStoreModel.longitude jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"longitude": _venderStoreModel.longitude}];
            }
            // recentlyMark
            if ([_venderStoreModel.recentlyMark jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"recentlyMark": _venderStoreModel.recentlyMark}];
            }
            // warehouseId
            if ([_venderStoreModel.wareHouseId jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"warehouseId": _venderStoreModel.wareHouseId}];
            }
            // stockStatus
            if ([_venderStoreModel.stockStatus jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"stockStatus": _venderStoreModel.stockStatus}];
            }
            // venderStoreStockTab
            if ([_venderStoreModel.venderStoreStockTab jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"venderStoreStockTab": _venderStoreModel.venderStoreStockTab}];
            }
            // vendSource
            if ([_venderStoreModel.venderSource jdcd_validateString]) {
                [venderStoreDic addEntriesFromDictionary:@{@"vendSource": _venderStoreModel.venderSource}];
            }
            
            [tempDic addEntriesFromDictionary:@{@"venderStore": [NSDictionary dictionaryWithDictionary:venderStoreDic]}];
        } else {
            // 快递运输
            [tempDic addEntriesFromDictionary:@{@"venderStore": [NSDictionary dictionary]}];
        }
    }
    
    return [NSDictionary dictionaryWithDictionary:tempDic];
    
}
@end
