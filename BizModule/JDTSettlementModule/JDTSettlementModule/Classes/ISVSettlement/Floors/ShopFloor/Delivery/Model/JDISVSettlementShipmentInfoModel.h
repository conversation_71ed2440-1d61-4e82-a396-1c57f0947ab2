//
//  JDISVSettlementShipmentInfoModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:店铺配送浮层默认物流信息Model
 模型命名空间:core.trade-FLR#balance.shipment-M#defaultShipmentInfoFloor
 uuid:defaultShipmentInfoFloor_1018653347178393605
 */
@interface JDISVSettlementShipmentInfoModel : NSObject
// 内部Key:VCYBB2C-D#shipmentList&relation
@property (nonatomic, copy) NSString *orderUuid; /**< orderUuid:订单uuid */
@property (nonatomic, copy) NSString *combinationBundleUuid; /**< combinationBundleUuid:相关包裹uuid */
// 内部Key:C-M#venderFloor&core
@property (nonatomic, copy) NSString *venderId; /**< venderId:店铺Id */
// 内部Key:C-M#defaultShipmentInfoFloor&basic
@property (nonatomic, copy) NSString *bundleUuid; /**< bundleUuid:包裹uuid */

@property (nonatomic, copy, nullable) NSArray *shipmentTypes;
@end

NS_ASSUME_NONNULL_END
