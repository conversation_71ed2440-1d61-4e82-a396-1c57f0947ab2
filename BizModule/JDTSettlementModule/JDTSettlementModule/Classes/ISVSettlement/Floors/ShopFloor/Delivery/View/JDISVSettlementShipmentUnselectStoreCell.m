//
//  JDISVSettlementShipmentUnselectStoreCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementShipmentUnselectStoreCell.h"

#import "JDISVSettlementShipmentUnselectStoreCellViewModel.h"

@interface JDISVSettlementShipmentUnselectStoreCell ()
@property (weak, nonatomic) IBOutlet UILabel *tipLabel;
@property (weak, nonatomic) IBOutlet UILabel *selectOtherStoreLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrow;

@property (nonatomic, strong) JDISVSettlementShipmentUnselectStoreCellViewModel *viewModel;
@end

@implementation JDISVSettlementShipmentUnselectStoreCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.rightArrow.contentMode = UIViewContentModeCenter;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementShopFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementShipmentUnselectStoreCellViewModel.class]) {
        self.viewModel = (JDISVSettlementShipmentUnselectStoreCellViewModel *)viewModel;
        self.tipLabel.attributedText = _viewModel.tipAttributedString;
        self.selectOtherStoreLabel.attributedText = _viewModel.selectedOtherStoreAttributedString;
        self.rightArrow.image = _viewModel.rightArrowImg;
        
    }
}


@end
