//
//  JDISVSettlementShopFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloor.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>

#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import "JDISVSettlementShopFloorModule.h"
#import "JDISVSettlementShopFloorSectionViewModel.h"
#import "JDISVSettlementShopFloorBaseCellViewModel.h"
#import "JDISVSettlementShopFloorBaseCell.h"

#import "JDISVSettlementShipmentController.h"

#import <NSBundle+JDISVSettlement.h>
#import "JDISVProcessFloorCell.h"
#import "JDISVSettlementProductSheetController.h"

@interface JDISVSettlementShopFloor()<UITableViewDelegate, UITableViewDataSource, JDCDISVActionTransferProtocol>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) JDISVSettlementShopFloorModule *viewModel;
@end

@implementation JDISVSettlementShopFloor

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    
    self.tableView.backgroundColor = [UIColor clearColor];
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    self.tableView.scrollEnabled = NO;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // Group样式的TableView Header和Footer会有空白
    self.tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorProductCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorProductCell"];//商品Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementFreebieCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementFreebieCell"];//赠品Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementStoreInfoCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementStoreInfoCell"];//店铺Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorAdditionalProductCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorAdditionalProductCell"];//换购商品Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorRemarkCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorRemarkCell"];//留言Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorRoundCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorRoundCell"];//圆角
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorShipmentCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorShipmentCell"];//配送Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorVenderCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorVenderCell"];//供货商
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementShopFloorStepCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementShopFloorStepCell"];//步骤
    [self.tableView registerClass:UITableViewCell.class forCellReuseIdentifier:@"UITableViewCell"];
    [[self tableView] registerClass:JDISVProcessFloorCell.class forCellReuseIdentifier:@"JDISVProcessFloorCell"];
    [self addSubview:self.tableView];
    @weakify(self)
    [RACObserve(_tableView, scrollEnabled) subscribeNext:^(NSNumber*  _Nullable x) {
        if(x.boolValue){
            dispatch_async(dispatch_get_main_queue(), ^{
                @strongify(self)
                self.tableView.scrollEnabled = NO;
            });
        }
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(0);
        make.trailing.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
    }];
    
}

//- (UIScrollView *)associateScrollView{
//    return self.tableView;
//}
//
//- (CGRect)scrollViewFrame{
//    return CGRectMake(0, 0, UIScreen.mainScreen.bounds.size.width, [self.viewModel floorHeight]);
//}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
    [self.tableView reloadData];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.tableView.scrollEnabled = NO;
    });
    
//    if (self.viewModel == nil || self.viewModel.sectionViewModels == nil) {
//        return  0;
//    }
//    
//    if (self.viewModel.sectionViewModels > 0) {
//        return  self.viewModel.sectionViewModels.count;
//    } else {
//        return  0;
//    }
    return self.viewModel.venderArr.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
//    if (section < self.viewModel.sectionViewModels.count) {
//        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:section];
//        if (cellViewModels.count > 0) {
//            return cellViewModels.count;
//        } else {
//            return 0;
//        }
//    } else {
//        return 0;
//    }
    
    NSUInteger rows = 0;
    for (CHECKDeliveryInfoModel *delivery in self.viewModel.venderArr[section].deliveryInfoList) {
        // 配送方式行
        rows += 1;
        if (delivery.skuItemList.count > 3) {
            // 超过 4 件商品聚合为一行（显示商品图列表，点击弹窗查看所有商品）
            rows += 1;
        } else {
            rows += delivery.skuItemList.count;
        }
        return rows;
    }
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
//    if (self.viewModel!=nil && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
//        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
//        if (indexPath.row < cellViewModels.count) {
//            JDISVSettlementShopFloorBaseCellViewModel *cellViewModel = [cellViewModels objectAtIndex:indexPath.row];
//            return cellViewModel.height;
//        } else {
//            return 0;
//        }
//    } else {
//        return 0;
//    }
    return 100;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0.01f;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == self.viewModel.sectionViewModels.count-1) {
        return 0.01f;
    }else{
        return [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], 0.01f)];
    header.backgroundColor = [UIColor clearColor];
    return header;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"])];
    footer.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    return footer;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementShopFloorBaseCell *cell = nil;
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementShopFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.cellIdentifier forIndexPath:indexPath];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
                __weak typeof(self) weakSelf = self;
                [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(NSDictionary *parames) {
                    __strong typeof(weakSelf) strongSelf = weakSelf;
                    [strongSelf processOrderDelegateActionWithParames:parames];
                }];
            }
        }
        
    }
    
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementShopFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            if ([cell respondsToSelector:@selector(updateCellWithViewModel:)]) {
                [cell performSelector:@selector(updateCellWithViewModel:) withObject:itemViewModel];
            }
        }
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
}

#pragma mark - Actions
- (void)processOrderDelegateActionWithParames:(NSDictionary *)parames {
    NSString *cellIdentifier = [parames objectForKey:@"cell_identifier"];
    if ([cellIdentifier isEqualToString:@"JDISVSettlementShopFloorRemarkCell"]) {
        // 跟新用户输入到commonModel
        NSString *actionType = [parames objectForKey:@"action_type"];
        if ([actionType isEqualToString:@"user_input"]) {
            NSString *venderId = [parames objectForKey:@"venderId"];
            NSDictionary *userRemarkInfo = [parames objectForKey:@"data_info"];
            
            [self.viewModel updateRemarkInfo:userRemarkInfo forVender:venderId];
        }
        
    } else if ([cellIdentifier isEqualToString:@"JDISVSettlementShopFloorShipmentCell"]) {
        // 打开配送浮层
        NSString *actionType = [parames objectForKey:@"action_type"];
        if ([actionType isEqualToString:@"open_shipment_controller"]) {
            NSString *venderId = [parames objectForKey:@"venderId"];
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementShopFloorOpenShipmentControllerAction"];
            action.value = venderId;
            [self isv_sendAction:action];
        }
    } else if ([cellIdentifier isEqualToString:@"JDISVSettlementStoreInfoCell"]) {
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementShopFloorOpenProductSheetControllerAction"];
        action.value = parames;
        [self isv_sendAction:action];
    }
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    
    if ([action.actionType isEqualToString:@"JDISVSettlementShopFloorOpenShipmentControllerAction"]) {
        NSString *venderId = [(NSString *)action.value copy];
        [self openShipmentControllerOfVender:venderId controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:@"JDISVSettlementShopFloorOpenProductSheetControllerAction"]) {
        NSDictionary *params = [(NSDictionary *)action.value copy];
        NSNumber *type = [params objectForKey:@"type"];
        if ([type integerValue] == 0) {
            // 商品浮层
            NSArray *storeProductViewModels = (NSArray *)[params objectForKey:@"data"];
            [self openProductDetailSheetController:storeProductViewModels controller:controller];
        }
        return YES;
    }
    
    return NO;
}

// MARK:打开商品浮层
- (void)openProductDetailSheetController:(NSArray *)storeProductViewModels controller:(UIViewController *)controller{
    UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"JDISVSettlementProductSheetController" bundle:[NSBundle jdisvSettlement_bundle]];
    JDISVSettlementProductSheetController *productSheetController = [storyBoard instantiateViewControllerWithIdentifier:@"JDISVSettlementProductSheetController"];
    [productSheetController updateViewModelWithData:storeProductViewModels];
    
    __weak typeof(self) weakSelf = self;
    productSheetController.dismissBlock = ^ {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        //
    };
//    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:productSheetController presentingViewController:controller];
//    presentationVC.type = KAFloatLayerTypeCustom;
//    presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
//    productSheetController.transitioningDelegate = presentationVC;
    productSheetController.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    [controller presentViewController:productSheetController animated:NO completion:nil];
}

- (void)openShipmentControllerOfVender:(NSString *)venderId controller:(UIViewController *)controller{
    JDISVSettlementShipmentController *shipmentController = [[JDISVSettlementShipmentController alloc] initWithNibName:@"JDISVSettlementShipmentController" bundle:[NSBundle jdisvSettlement_bundle]];
    [shipmentController updateViewModelWithVenderId:venderId sourceFrom:_viewModel.sourceType mapFeatureSwith:self.viewModel.storeMapFeature];

    @weakify(self)
    shipmentController.confirmCallBack = ^(NSDictionary * _Nonnull orderParm) {
        @strongify(self)
        [self loadSaveShipmentDataWith:orderParm controller:controller];
    };

    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:shipmentController presentingViewController:controller];
    presentationVC.type = KAFloatLayerTypeCustom;
    presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
    shipmentController.transitioningDelegate = presentationVC;

    [controller presentViewController:shipmentController animated:YES completion:nil];
}

- (void)loadSaveShipmentDataWith:(NSDictionary *)param controller:(UIViewController *)controller {
    @weakify(self)
    [PlatformService showLoadingInView:controller.view];
    
    [[self.viewModel loadSaveShipmentSignalWith:param] subscribeNext:^(NSDictionary *resultInfo) {
        @strongify(self)
        [PlatformService dismissInView:controller.view];
        [self sendRefreshListActionWithData:resultInfo];
    } error:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:controller.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    }];
}

#pragma mark - 发送Action
- (void)sendRefreshListActionWithData:(NSDictionary *)dictionary {
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShopFloorSaveShipmentCompleteAction"];
    action.value = dictionary;
    [self isv_sendAction:action];
}

@end
