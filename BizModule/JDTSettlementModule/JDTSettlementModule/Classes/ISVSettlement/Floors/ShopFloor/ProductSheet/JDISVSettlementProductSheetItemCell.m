//
//  JDISVSettlementProductSheetItemCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/12.
//

#import "JDISVSettlementProductSheetItemCell.h"

#import "JDISVSettlementProductSheetViewModel.h"
#import "JDISVSettlementProductSheetItemViewModel.h"
#import "NSAttributedString+ISVSettlementShop.h"

@interface JDISVSettlementProductSheetItemCell()
@property (strong, nonatomic) UIImageView *skuImageView;
@property (strong, nonatomic)  UIImageView *skuCoverMaskView;
@property (strong, nonatomic)  UIImageView *skuSoldoutImageView;
@property (strong, nonatomic) UILabel *skuSoldoutLabel;
@property (weak, nonatomic) IBOutlet UILabel *skuTitleLabel; /**< Title */
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *skuTitleLabelHeight;
@property (weak, nonatomic) IBOutlet UILabel *skuPriceLabel; /**< 价格 */
@property (weak, nonatomic) IBOutlet UILabel *skuCountLabel; /**< 数量 */
@property (weak, nonatomic) IBOutlet UILabel *skuShopTitleLabel; /**< 门店 */
@property (weak, nonatomic) IBOutlet UILabel *skuShopLabel; /**< 门店名 */

@property (weak, nonatomic) IBOutlet UIImageView *separatorLine; /**< 分割线 */
@property (nonatomic,strong) UILabel *groupBuyLabel;

@end

@implementation JDISVSettlementProductSheetItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.contentView.backgroundColor = UIColor.clearColor;
    self.backgroundColor = UIColor.clearColor;
    self.skuCountLabel.textAlignment = NSTextAlignmentRight;
    NSArray *subViews = self.contentView.subviews;
    UIImageView *i1 = subViews[0];
    UIImageView *i2 = subViews[6];
//    UIImageView *i3 = subViews[7];
    
    
    [i1 setHidden:YES];
    [i2 setHidden:YES];
//    [i3 setHidden:YES];
    
    
    self.groupBuyLabel = [[UILabel alloc]init];
    [self.contentView addSubview:_groupBuyLabel];
//    self.groupBuyLabel.text = SettlementLan(@"setllement_exchanged_purchase") ;
//    self.groupBuyLabel.frame = CGRectMake(0, 0, 100, 100);
//    self.groupBuyLabel.sd_layout.topEqualToView(self.skuTitleLabel).leftEqualToView(self.skuTitleLabel).heightIs(18).widthIs(32);
//    self.groupBuyLabel.sd_cornerRadius = @(2);
    
    self.groupBuyLabel.textColor = [[UIColor alloc]initWithRed:208/255.0 green:109/255.0 blue:96/255.0 alpha:1];
    self.groupBuyLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    self.groupBuyLabel.backgroundColor = [[UIColor alloc] initWithRed:253/255.0 green:244/255.0 blue:243/255.0 alpha:1];
    self.groupBuyLabel.textAlignment = NSTextAlignmentCenter;

//    253    244    243
    
    
    //(origin = (x = 18, y = 12), size = (width = 90, height = 90))
    self.skuImageView = [[UIImageView alloc]init];
    [self.contentView addSubview:self.skuImageView];
    
//    self.skuImageView.frame = CGRectMake(48, 12, 60, 90);
  
    
    //(18 12; 90 90)
    self.skuCoverMaskView = [[UIImageView alloc]init];
    
    [self.contentView addSubview:self.skuCoverMaskView];
    self.skuCoverMaskView.layer.masksToBounds = YES;
    self.skuCoverMaskView.layer.cornerRadius = 4.f;
    self.skuCoverMaskView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.03];
    
//    (28 22; 70 70)
    self.skuSoldoutImageView = [[UIImageView alloc]init];
    [self.contentView addSubview:self.skuSoldoutImageView];
    
//    self.skuSoldoutImageView.alpha = 0;
//    self.skuSoldoutImageView.contentMode = UIViewContentModeCenter;
        
//    self.skuSoldoutImageView.image = [UIImage jdisvSettlement_imageNamed:@"settlementmodule_store_sku_soldout_large"];
    
//    self.skuSoldoutImageView.hidden = YES;
    self.skuSoldoutImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8"];
    self.skuSoldoutImageView.layer.masksToBounds = YES;
    self.skuSoldoutImageView.layer.cornerRadius = 35.f;
    
    self.skuSoldoutLabel = [[UILabel alloc]init];
    self.skuSoldoutLabel.textAlignment = NSTextAlignmentCenter;
    [self.skuSoldoutImageView addSubview:self.skuSoldoutLabel];
    self.skuSoldoutLabel.attributedText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_shop_no_product_label")  colorKey:@"#C7" fontKey:@"#T7" weight:UIFontWeightSemibold];
    
    self.skuTitleLabel.backgroundColor = UIColor.clearColor;
    self.skuTitleLabel.text = @"";
    self.skuTitleLabel.font = [UIFont systemFontOfSize:14.f];
    self.skuTitleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    self.skuCountLabel.text = @"";
    self.skuCountLabel.font = [UIFont systemFontOfSize:12.f];
    self.skuCountLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    
    self.skuPriceLabel.text = @"";
    self.skuPriceLabel.attributedText = nil;
    
    self.skuShopTitleLabel.text = SettlementLan(@"checkout_store_label") ;
    self.skuShopTitleLabel.font = [UIFont systemFontOfSize:12.f weight:UIFontWeightMedium];
    self.skuShopTitleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.skuShopTitleLabel.hidden = YES;
    
    self.skuTitleLabelHeight.constant = 0.f;
    
    self.skuShopLabel.text = @"";
    self.skuShopLabel.hidden = YES;
    self.skuShopLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.skuShopLabel.font = [UIFont systemFontOfSize:12.f];
    
    self.separatorLine.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
}

- (void)updateCellWith:(JDISVSettlementProductSheetItemViewModel *)viewModel {
    
    

    if ([viewModel isKindOfClass:JDISVSettlementProductSheetItemViewModel.class]) {
        [self.skuImageView  jdcd_setImage:viewModel.skuImageUrl];
        if (viewModel.isSoldout) {
            self.skuSoldoutImageView.alpha = 0.7;
                
        } else {
            self.skuSoldoutImageView.alpha = 0;
            
        }
        self.skuTitleLabel.text = viewModel.skuTitle;
        self.skuTitleLabelHeight.constant = viewModel.skuTitleHeight;
        self.skuPriceLabel.attributedText = viewModel.priceAttrText;
        self.skuCountLabel.text = viewModel.countText;
        
        if ([viewModel.shopNameText jdcd_validateString]) {
            self.skuShopLabel.text = viewModel.shopNameText;
            self.skuShopLabel.hidden = NO;
            self.skuShopTitleLabel.hidden = NO;
        } else {
            self.skuShopLabel.text = @"";
            self.skuShopLabel.hidden = YES;
            self.skuShopTitleLabel.hidden = YES;
        }

        if ([viewModel.marketTag  isEqualToString:@"3"]) {
            [self.groupBuyLabel setHidden:NO];
            NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
            [style setFirstLineHeadIndent:32 + 7];
            NSMutableAttributedString *gTitle = [[NSMutableAttributedString alloc]initWithString:viewModel.skuTitle];
            [gTitle addAttributes:@{NSParagraphStyleAttributeName:style} range:NSMakeRange(0, 2)];
            self.skuTitleLabel.attributedText = gTitle;
            
            self.skuImageView.frame = CGRectMake(38, 12, 70, 70);
            [self.skuImageView jdcd_frameRTL];
            self.skuCoverMaskView.frame = self.skuImageView.frame;
            [self.skuCoverMaskView jdcd_frameRTL];
    
            //有换购情况
            
//            self.skuSoldoutImageView.frame = CGRectMake(, 10, 60, 60);
//            self.skuSoldoutImageView.sd_layout.centerXEqualToView(self.skuImageView).centerYEqualToView(self.skuImageView).widthIs(50).heightIs(50);
        }else{
            //正常情况
            self.skuImageView.frame = CGRectMake(18, 12, 90, 90);
            [self.skuImageView jdcd_frameRTL];
            
            self.skuCoverMaskView.frame = self.skuImageView.frame;
            self.skuTitleLabel.text = viewModel.skuTitle;
            [self.groupBuyLabel setHidden:YES];
            
            
            self.skuSoldoutImageView.frame = CGRectMake(28, 22, 70, 70);
            [self.skuSoldoutImageView jdcd_frameRTL];
            self.skuSoldoutLabel.frame = CGRectMake(0, 25, 70, 20);
            [self.skuSoldoutLabel jdcd_frameRTL];
        }
    }
    
    
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    self.skuSoldoutImageView.hidden = YES;
    self.skuTitleLabel.text = @"";
    self.skuTitleLabelHeight.constant = 0.f;
    self.skuCountLabel.text = @"";
    self.skuPriceLabel.text = @"";
    self.skuPriceLabel.attributedText = nil;
    self.skuShopTitleLabel.hidden = YES;
    self.skuShopLabel.text = @"";
    self.skuShopLabel.hidden = YES;
}

@end
