//
//  JDISVSettlementProductSheetViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/12.
//

#import "JDISVSettlementProductSheetViewModel.h"

#import "JDISVSettlementProductSheetItemViewModel.h"
#import "JDISVSettlementStoreProductViewModel.h"

@implementation JDISVSettlementProductSheetViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.contentHeight = 0;
        self.totalCountLabel = @"";
        self.itemArray = [NSArray array];
    }
    return self;
}

- (void)updateViewModelWith:(id)data {
    if ([data isKindOfClass:NSDictionary.class]) {
        NSNumber *skuTotalCount = [(NSDictionary *)data objectForKey:@"totalCount"];
        NSArray *sectionsHeaders = [(NSDictionary *)data objectForKey:@"headers"];
        NSDictionary *storeProductViewModels = [(NSDictionary *)data objectForKey:@"sections"];;
        NSMutableArray *sectionsViewModels = [NSMutableArray array];

        for (NSArray *items in storeProductViewModels) {
            NSMutableArray *itemViewModels = [NSMutableArray array];
            for (JDISVSettlementStoreProductViewModel *storeProductViewModel in items) {
                JDISVSettlementProductSheetItemViewModel *productSheetItemViewModel = [[JDISVSettlementProductSheetItemViewModel alloc] init];
                productSheetItemViewModel.imageDomain = storeProductViewModel.imageDomain;
                [productSheetItemViewModel updateViewModelWith:storeProductViewModel.skuModel forType:0];
                [itemViewModels addObject:productSheetItemViewModel];

            }
            [sectionsViewModels addObject:itemViewModels];
        }
        
        self.headerArray = [NSArray arrayWithArray:sectionsHeaders];
        self.itemArray = [NSArray arrayWithArray:sectionsViewModels];
        
        self.totalCountLabel = [NSString stringWithFormat:SettlementLan(@"checkout_product_list_count") , @(skuTotalCount.integerValue)];
        
        self.contentHeight = 0;
        for (NSArray *sections in self.itemArray) {
            for (JDISVSettlementProductSheetItemViewModel *itemViewModel in sections) {
                self.contentHeight = self.contentHeight + itemViewModel.height;
            }
        }
        
        self.contentHeight = self.contentHeight + 55.f;
    }
}
@end
