//
//  JDISVSettlementProductSheetController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/12.
//

#import "JDISVSettlementProductSheetController.h"

#import "JDISVSettlementProductSheetItemCell.h"

#import "JDISVSettlementProductSheetViewModel.h"
#import "JDISVSettlementProductSheetItemViewModel.h"
#import <JDISVMasonryModule/Masonry.h>

@interface JDISVSettlementProductSheetController () <UITableViewDelegate,UITableViewDataSource>
@property (weak, nonatomic) IBOutlet UIImageView *maskView; /**< 遮罩图层 */
@property (weak, nonatomic) IBOutlet UIView *contentView; /**< 半屏内容视图 */
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentViewHeight; /**< 半屏内容视图高度 */
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentViewBottom; /**< 半屏内容视图底部约束 */

@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UIButton *closeButton;
@property (weak, nonatomic) IBOutlet UILabel *totalCountLabel;

@property (weak, nonatomic) IBOutlet UITableView *tableView;

@property (nonatomic, strong) JDISVSettlementProductSheetViewModel *viewModel;

@end

@implementation JDISVSettlementProductSheetController

- (instancetype)initWithCoder:(NSCoder *)coder
{
    self = [super initWithCoder:coder];
    if (self) {
        _viewModel = [[JDISVSettlementProductSheetViewModel alloc] init];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    [self updateView];
}

- (void)viewDidAppear:(BOOL)animated {
    
    [super viewDidAppear:animated];
    
    [self showDataView];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor clearColor];
    self.maskView.backgroundColor = [UIColor blackColor];
    self.maskView.hidden = YES;
    self.maskView.alpha = 0;
    [self.maskView jd_addTapAction:@selector(dismissAction:) withTarget:self];
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    //
    self.titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    self.titleLabel.font = [UIFont systemFontOfSize:18.f weight:UIFontWeightMedium];
    self.titleLabel.text = SettlementLan(@"checkout_product_list_title") ;

    //
    self.totalCountLabel.text = @"";
    self.totalCountLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.totalCountLabel.font = [UIFont systemFontOfSize:12.f];
    
//    [self.closeButton setImage:[UIImage jdisvSettlement_imageNamed:@"jdka_settlement_float_close"] forState:UIControlStateNormal];
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
    [self.closeButton setImage:image forState:UIControlStateNormal];
//    [self.closeButton addTarget:self action:@selector(closeAction:) forControlEvents:UIControlEventTouchUpInside];
    self.closeButton.layer.cornerRadius = 9;
    self.closeButton.layer.masksToBounds = YES;
    self.closeButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
//    [self.contentView addSubview:self.closeButton];
//    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.mas_equalTo(18);
//        make.centerY.mas_equalTo(self.totalCountLabel);
//        make.width.height.mas_equalTo(18);
//        make.trailing.mas_equalTo(-18);
//    }];
    
    
    // tableView
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.tableView.backgroundColor = UIColor.clearColor;
//    self.tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, 0.1f)];
//    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, 0.1f)];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    // contentView
    self.contentView.hidden = YES;
    self.contentViewHeight.constant = [self heightForContentView];
    self.contentViewBottom.constant = -[self heightForContentView];
    
}

#pragma mark - Data

- (void)updateViewModelWithData:(NSArray *)data {
    [self.viewModel updateViewModelWith:data];
    [self.tableView reloadData];
}

- (void)updateView {
    self.totalCountLabel.text = self.viewModel.totalCountLabel;
    [self.tableView reloadData];
}

#pragma mark - 高度计算
// 返回容器视图的高度 - 内容不足屏幕60%:高度为屏幕的60%  超出：高度为屏幕的80%
- (CGFloat)heightForContentView {
    
    CGFloat maxHeight = ceilf([UIScreen mainScreen].bounds.size.height * 0.8);
//    CGFloat minHeight = ceilf([UIScreen mainScreen].bounds.size.height * 0.6);
//    
//    CGFloat tableViewContentHeight = 0.0f;
//    
//
//    
//    CGFloat currentHeight = self.viewModel.contentHeight;
//    currentHeight = MIN(MAX(minHeight, currentHeight), maxHeight);
    
    return maxHeight;
}

#pragma mark - Action

- (void)showDataView {
    
    [self.contentView jdcd_addRoundedCorners:(UIRectCornerTopLeft | UIRectCornerTopRight) withRadii:CGSizeMake(8.f, 8.f)];
    
    CGFloat bottom = 0;
    if (@available(iOS 11.0, *)) {
        bottom = self.view.safeAreaInsets.bottom;
    }
    CGFloat originy = [UIScreen mainScreen].bounds.size.height - [self heightForContentView];
    //
    self.contentView.hidden = NO;
    self.maskView.hidden = NO;
    
    [UIView animateWithDuration:0.3 animations:^{
        self.maskView.alpha = 0.65;
        self.contentView.frame = CGRectMake(0, originy, [UIScreen mainScreen].bounds.size.width, [self heightForContentView]);
    } completion:^(BOOL finished) {
        self.contentViewBottom.constant = 0;
    }];
}

- (void)hideDataView:(void (^)(void))completion {
    CGFloat width = [UIScreen mainScreen].bounds.size.width;
    CGFloat originy = [UIScreen mainScreen].bounds.size.height;
    
    //
    [UIView animateWithDuration:0.3 animations:^{
        self.maskView.alpha = 0;
        self.contentView.frame = CGRectMake(0, originy, width, [self heightForContentView]);
    } completion:^(BOOL finished) {
        self.contentView.hidden = NO;
        self.maskView.hidden = NO;
        
        if (completion) {
            completion();
        }
    }];
}

- (IBAction)closeAction:(id)sender {
    __weak typeof(self) weakSelf = self;
    [weakSelf hideDataView:^{
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf dismissViewControllerAnimated:NO completion:^{
            __strong typeof(weakSelf) strongSelf = weakSelf;
            if (strongSelf.dismissBlock) {
                strongSelf.dismissBlock();
            }
        }];
    }];
}

- (void)dismissAction:(id)sender {
    __weak typeof(self) weakSelf = self;
    [weakSelf hideDataView:^{
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf dismissViewControllerAnimated:NO completion:^{
            __strong typeof(weakSelf) strongSelf = weakSelf;
            if (strongSelf.dismissBlock) {
                strongSelf.dismissBlock();
            }
        }];
    }];
}

#pragma mark - TableView

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.viewModel.itemArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *rows = self.viewModel.itemArray[section];
    return rows.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
//    if (self.viewModel.itemArray.count > 0) {
//        JDISVSettlementProductSheetItemViewModel *itemViewModel = [self.viewModel.itemArray objectAtIndex:indexPath.row];
//        return itemViewModel.height;
//    } else {
//        return 0;
//    }
    NSArray *rows = self.viewModel.itemArray[indexPath.section];
    if (rows > 0) {
        JDISVSettlementProductSheetItemViewModel *itemViewModel = [rows objectAtIndex:indexPath.row];
        return itemViewModel.height;
    } else {
        return 0;
    }
    
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    NSArray *rows = self.viewModel.itemArray[section];
    if (rows > 0) {
        return 20;
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], 0.01f)];
    header.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    UILabel *label = [[UILabel alloc]init];
    label.text = SettlementLan(@"checkout_layout_virtual_main") ;
    label.textAlignment = NSTextAlignmentCenter;
    label.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    label.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    label.layer.cornerRadius = 4;
    label.layer.masksToBounds = YES;
    [header addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(20);
        make.centerY.mas_equalTo(header);
        make.width.mas_equalTo([SettlementLan(@"checkout_layout_virtual_main") jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] constraintsSize:CGSizeMake(MAXFLOAT, 16.f)].width + 8.f);
        make.height.mas_equalTo(16);
    }];
    UILabel *label2 = [[UILabel alloc]init];
    label2.backgroundColor = UIColor.clearColor;
    label2.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    label2.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    label2.textAlignment = NSTextAlignmentLeft;
    [header addSubview:label2];
    [label2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(label.mas_trailing).mas_offset(8);
        make.centerY.mas_equalTo(header);
        make.width.mas_equalTo(150);
        make.height.mas_equalTo(20);
    }];
    UILabel *label3 = [[UILabel alloc]init];
    label3.backgroundColor = UIColor.clearColor;
    label3.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    label3.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    label3.textAlignment = NSTextAlignmentRight;
    [header addSubview:label3];
    [label3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(-18);
        make.centerY.mas_equalTo(header);
        make.width.mas_equalTo(60);
        make.height.mas_equalTo(16);
    }];
    
    if (section < self.viewModel.headerArray.count) {
        NSDictionary *headerDic = self.viewModel.headerArray[section];
        if ([headerDic[@"priceShow"] isKindOfClass:NSAttributedString.class]) {
            label2.attributedText = headerDic[@"priceShow"];
        }
        label3.text = headerDic[@"suitNum"];
    }
    return header;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementProductSheetItemCell *cell = nil;
    NSArray *rows = self.viewModel.itemArray[indexPath.section];
    if (indexPath.row < rows.count) {
        cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVSettlementProductSheetItemCell" forIndexPath:indexPath];
    }
    if (cell == nil) {
        cell = [UITableViewCell new];
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    NSArray *rows = self.viewModel.itemArray[indexPath.section];
    if (indexPath.row < rows.count) {
        JDISVSettlementProductSheetItemViewModel *itemViewModel = [rows objectAtIndex:indexPath.row];
        if ([cell respondsToSelector:@selector(updateCellWith:)]) {
            [cell performSelector:@selector(updateCellWith:) withObject:itemViewModel];
        }
    }
}



@end
