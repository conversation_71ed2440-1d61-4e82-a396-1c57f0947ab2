//
//  JDISVSettlementProductSheetItemViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/13.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementProductSheetItemViewModel : NSObject
@property (nonatomic, assign) CGFloat height;
@property (nonatomic, copy) NSString *skuImageUrl;
@property (nonatomic,assign) BOOL isSoldout; /**< 是否售空 */
@property (nonatomic, copy) NSString *skuTitle;
@property (nonatomic, assign) CGFloat skuTitleHeight;
@property (nonatomic, strong) NSAttributedString *priceAttrText;
@property (nonatomic, assign) NSInteger count;
@property (nonatomic, copy) NSString *countText;
@property (nonatomic, copy) NSString *shopNameText;
@property (nonatomic,copy) NSString *marketTag;
@property (nonatomic, copy) NSString *imageDomain;//图片域名

- (void)updateViewModelWith:(id)data forType:(NSInteger)type;
@end

NS_ASSUME_NONNULL_END
