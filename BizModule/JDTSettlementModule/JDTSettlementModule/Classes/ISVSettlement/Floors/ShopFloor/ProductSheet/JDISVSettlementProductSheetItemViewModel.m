//
//  JDISVSettlementProductSheetItemViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2020/10/13.
//

#import "JDISVSettlementProductSheetItemViewModel.h"

#import "JDISVSettlementShopFloorProductModel.h"
#import "JDISVSettlementShopFloorVirtualProductModel.h"
#import "NSAttributedString+ISVSettlementShop.h"

@implementation JDISVSettlementProductSheetItemViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.height = 0;
    }
    return self;
}

- (void)updateViewModelWith:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementShopFloorProductModel.class]) {
        JDISVSettlementShopFloorProductModel *model = (JDISVSettlementShopFloorProductModel *)data;
        //
//        self.marketTag = model.marketTag;
        self.skuImageUrl = [NSString stringWithFormat:@"%@%@", _imageDomain, model.skuCoverImageUrl];
        // stockStatus 33-有货 | 34-无货
        if (model.stockStatusCode.integerValue == 34) {
            // 34-无货
            self.isSoldout = YES;
        } else {
            // 33-有货
            self.isSoldout = NO;
        }
        
        self.skuTitle = model.skuName;
        self.skuTitleHeight = ceil([self.skuTitle jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - 120.f - 18.f, CGFLOAT_MAX) textFont:[UIFont systemFontOfSize:14.f] maxNumberOfLines:2].height);
        
//        NSString *priceText = @"￥--";
//        if ([model.price jdcd_validateString]) {
//            priceText = [NSString stringWithFormat:@"￥%@", model.price];
//        }
        
//        NSMutableAttributedString *priceAttrText = [[NSMutableAttributedString alloc] initWithString:priceText
//                                                                                          attributes:@{NSForegroundColorAttributeName: [UIColor bq_red],
//                                                                                                       NSFontAttributeName: [UIFont systemFontOfSize:18.f]}];
//        NSRange pointRange = [priceText rangeOfString:@"."];
//        [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(0, 1)];
//        if (pointRange.location != NSNotFound) {
//            // 含有"."
//            NSInteger decimalsLength = priceText.length - pointRange.location; // 小数点后的位数包含小数点
//            if (decimalsLength > 0) {
//                [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(pointRange.location, decimalsLength)];
//            }
//        }
        if ([model.priceShow jdcd_validateString]) {
            model.priceShow = [model.priceShow stringByReplacingOccurrencesOfString:@"￥" withString:[NSString getJDCDPriceTag]];
            model.priceShow = [model.priceShow stringByReplacingOccurrencesOfString:@"¥" withString:[NSString getJDCDPriceTag]];
            NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
            [priceAttributedString KA_renderWithPriceStr:model.priceShow type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
            self.priceAttrText = [priceAttributedString copy];
        } else {
            self.priceAttrText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:[NSString stringWithFormat:@"%@--",[NSString getJDCDPriceTag]] colorKey:@"#C9" fontKey:@"#T7"];
        }
        
//        self.count = model.num.integerValue;
//        self.countText = [NSString stringWithFormat:@"X%ld", self.count];
        self.countText = model.numMsg;
        
//        self.shopNameText = model.shopName;
        
        CGFloat shopNameTextHeight = ceil([self.shopNameText jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - 148.f -47.f, CGFLOAT_MAX) textFont:[UIFont systemFontOfSize:12.f] maxNumberOfLines:0].height);
        
        self.height = 12.f + self.skuTitleHeight + 18.f + 12.f;
        if ([self.shopNameText jdcd_validateString]) {
            self.height = self.height + 8.f + shopNameTextHeight;
        }
        
        if (self.height < 114.f) {
            self.height = 114.f;
        }
    } else if ([data isKindOfClass:JDISVSettlementShopFloorVirtualProductModel.class]) {
        JDISVSettlementShopFloorVirtualProductModel *model = (JDISVSettlementShopFloorVirtualProductModel *)data;
        //
//        self.marketTag = model.marketTag;
        self.skuImageUrl = [NSString stringWithFormat:@"%@%@", _imageDomain, model.skuCoverImageUrl];
        // stockStatus 33-有货 | 34-无货
//        if (model.stockStatus.integerValue == 34) {
//            // 34-无货
//            self.isSoldout = YES;
//        } else {
//            // 33-有货
//            self.isSoldout = NO;
//        }
        
        self.skuTitle = model.skuName;
        self.skuTitleHeight = ceil([self.skuTitle jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - 120.f - 18.f, CGFLOAT_MAX) textFont:[UIFont systemFontOfSize:14.f] maxNumberOfLines:2].height);
        
//        NSString *priceText = @"￥--";
//        if ([model.price jdcd_validateString]) {
//            priceText = [NSString stringWithFormat:@"￥%@", model.price];
//        }
        
//        NSMutableAttributedString *priceAttrText = [[NSMutableAttributedString alloc] initWithString:priceText
//                                                                                          attributes:@{NSForegroundColorAttributeName: [UIColor bq_red],
//                                                                                                       NSFontAttributeName: [UIFont systemFontOfSize:18.f]}];
//        NSRange pointRange = [priceText rangeOfString:@"."];
//        [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(0, 1)];
//        if (pointRange.location != NSNotFound) {
//            // 含有"."
//            NSInteger decimalsLength = priceText.length - pointRange.location; // 小数点后的位数包含小数点
//            if (decimalsLength > 0) {
//                [priceAttrText addAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12.f]} range:NSMakeRange(pointRange.location, decimalsLength)];
//            }
//        }
        if ([model.priceShow jdcd_validateString]) {
            model.priceShow = [model.priceShow stringByReplacingOccurrencesOfString:@"￥" withString:[NSString getJDCDPriceTag]];
            model.priceShow = [model.priceShow stringByReplacingOccurrencesOfString:@"¥" withString:[NSString getJDCDPriceTag]];
            NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
            [priceAttributedString KA_renderWithPriceStr:model.priceShow type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
            self.priceAttrText = [priceAttributedString copy];
        } else {
            self.priceAttrText = [[NSAttributedString alloc] ka_settlement_shop_initWithString:[NSString stringWithFormat:@"%@--",[NSString getJDCDPriceTag]] colorKey:@"#C9" fontKey:@"#T7"];
        }
        
//        self.count = model.num.integerValue;
//        self.countText = [NSString stringWithFormat:@"X%ld", self.count];
//        self.countText = model.numMsg;
        
//        self.shopNameText = model.shopName;
        
        CGFloat shopNameTextHeight = ceil([self.shopNameText jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - 148.f -47.f, CGFLOAT_MAX) textFont:[UIFont systemFontOfSize:12.f] maxNumberOfLines:0].height);
        
        self.height = 12.f + self.skuTitleHeight + 18.f + 12.f;
        if ([self.shopNameText jdcd_validateString]) {
            self.height = self.height + 8.f + shopNameTextHeight;
        }
        
        if (self.height < 114.f) {
            self.height = 114.f;
        }
    }
}
@end
