//
//  UITextField+JDISVSettlementShop.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "UITextField+JDISVSettlementShop.h"
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVReactiveObjCModule/NSObject+RACDescription.h>
@implementation UITextField (JDISVSettlementShop)
- (RACSignal *)ka_settlement_shop_inputTextSignal {
    @weakify(self);
    return [[[[[[RACSignal
                 defer:^RACSignal * _Nonnull{
                     @strongify(self);
                     return [RACSignal return:self];
                 }]
                concat:[self rac_signalForControlEvents:UIControlEventAllEditingEvents]]
               filter:^BOOL(UITextField *x) {
                   if (!x.markedTextRange) {
                       return YES;
                   } else {
                       return NO;
                   }
               }]
              map:^(UITextField *x) {
                  return x.text;
              }]
             takeUntil:self.rac_willDeallocSignal]
            setNameWithFormat:@"%@ -rac_inputTextSignal", RACDescription(self)];
}
@end
