//
//  JDISVSettlementProcessCell.m
//  JDISVSettlementModule
//
//  Created by ext.songjian6 on 2022/5/7.
//

#import "JDISVProcessFloorCell.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVProcessFloorCellViewModel.h"
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

//#define SETTLEMENTYESIMAGE [UIImage ka_iconWithName:JDIF_ICON_UNDO imageSize:CGSizeMake(7, 7) color:[[UIColor alloc]initWithRed:0.82 green:0.84 blue:0.89 alpha:1]]
//#define SETTLEMENTNOIMAGE [UIImage ka_iconWithName:JDIF_ICON_RADIO_FILL imageSize:CGSizeMake(7, 7) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]]

@interface JDISVProcessFloorCell ()

@property (nonatomic,strong) UIView *bgView;
@property (nonatomic,strong) UIImageView *topImageView;
@property (nonatomic,strong) UIView *lineView;
@property (nonatomic,strong) UIImageView *bottomImageView;

@property (nonatomic,strong)  UILabel *topLeftLabel;
@property (nonatomic,strong)  UILabel *bottomLeftLabel;
@property (nonatomic,strong)  UILabel *topRightLabel ;
@property (nonatomic,strong)  UILabel *topRightBottomLabel ;
@property (nonatomic,strong)  UILabel *bottomRightLabel ;
@property (nonatomic,strong)  UILabel *bottomRightBottomLabel ;
@property (nonatomic,strong)  UIImageView *isPayFirstMoneyImageView;
@property (nonatomic,strong)  UIView *payView;
@property (nonatomic,strong) UILabel *payStrLabel;
@end
@implementation JDISVProcessFloorCell

-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        
        self.bgView = [[UIView alloc] init];
        self.bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        self.bgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R4"];
        
        [self.contentView addSubview:self.bgView];
        [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(18.f);
            make.trailing.mas_equalTo(self.contentView).mas_offset(-18.f);
        }];
        
        UILabel *topLeftLabel = [[UILabel alloc]init];
        topLeftLabel.text = @"topleft";
        self.topLeftLabel = topLeftLabel;
        UILabel *topRightLabel = [[UILabel alloc]init];
        topRightLabel.text = @"topRightLabel";
        UILabel *topRightBottomLabel = [[UILabel alloc]init];
        topRightBottomLabel.text = @"topRightBottomLabel";
        [self.bgView addSubview:topLeftLabel];
        [self.bgView addSubview:topRightLabel];
        [self.bgView addSubview:topRightBottomLabel];
        topLeftLabel.text = SettlementLan(@"checkout_pre_sale_stage_new_1") ;
        topRightBottomLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        topLeftLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        topRightLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        topLeftLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        topRightLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        topRightBottomLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        
        UILabel *bottomLeftLabel = [[UILabel alloc]init];
        bottomLeftLabel.text = @"bottomLeftLabel";
        self.bottomLeftLabel = bottomLeftLabel;
        UILabel *bottomRightLabel = [[UILabel alloc]init];
        bottomRightLabel.text = @"bottomRightLabel";
        UILabel *bottomRightBottomLabel = [[UILabel alloc]init];
        bottomRightBottomLabel.text = @"bottomRightBottomLabel";
        [self.bgView addSubview:bottomLeftLabel];
        [self.bgView addSubview:bottomRightLabel];
        [self.bgView addSubview:bottomRightBottomLabel];
        bottomLeftLabel.text = SettlementLan(@"checkout_pre_sale_stage_new_2") ;
        bottomRightBottomLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        bottomLeftLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        bottomRightLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        bottomLeftLabel.textColor = bottomRightLabel.textColor;
        bottomRightLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        bottomRightBottomLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        
        [topLeftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.bgView).offset(31);
            make.top.mas_equalTo(self.bgView).mas_offset(12);
            make.trailing.mas_equalTo(self.bgView).mas_offset(-12);
        }];
        
        [topRightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(topLeftLabel);
            make.top.equalTo(topLeftLabel.mas_bottom).offset(4);
            make.trailing.mas_equalTo(self.bgView).mas_offset(-12);
        }];
        
        [topRightBottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(topLeftLabel);
            make.top.mas_equalTo(topRightLabel.mas_bottom).mas_offset(4);
            make.trailing.mas_equalTo(self.bgView).mas_offset(-12);
        }];
        
        [bottomLeftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.bgView).offset(31);
            make.top.mas_equalTo(topRightBottomLabel.mas_bottom).mas_offset(10);
            make.trailing.mas_equalTo(self.bgView).mas_offset(-12);
        }];
        
        [bottomRightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(bottomLeftLabel);
            make.top.mas_equalTo(bottomLeftLabel.mas_bottom).mas_offset(4);
            make.trailing.mas_equalTo(self.bgView).mas_offset(-12);
        }];
        
        [bottomRightBottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(bottomLeftLabel);
            make.top.mas_equalTo(bottomRightLabel.mas_bottom).mas_offset(4);
            make.trailing.mas_equalTo(self.bgView).mas_offset(-12);
        }];
        
        self.topRightLabel = topRightLabel;
        self.topRightBottomLabel = topRightBottomLabel;
        
        self.bottomRightLabel = bottomRightLabel;
        self.bottomRightBottomLabel = bottomRightBottomLabel;
        
        
        self.topImageView = [[UIImageView alloc]init];
        self.topImageView.layer.cornerRadius = 3.5;
        self.topImageView.layer.borderWidth = 2.f;
        self.topImageView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"].CGColor;
        self.topImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        
        self.bottomImageView = [[UIImageView alloc] init];
        self.bottomImageView.layer.cornerRadius = 3.5;
        self.bottomImageView.layer.borderWidth = 2.f;
        self.bottomImageView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"].CGColor;
        self.bottomImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        
        self.lineView = [[UIView alloc]init];
        self.lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C4"];
        [self.bgView addSubview:self.lineView];
        [self.bgView addSubview:self.topImageView];
        [self.bgView addSubview:self.bottomImageView];
        
        
        [self.topImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.topLeftLabel);
            make.leading.mas_equalTo(self.bgView).mas_offset(12);
            make.size.mas_equalTo(CGSizeMake(7, 7));
        }];

        [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.topImageView);
            make.top.mas_equalTo(self.topImageView.mas_bottom).mas_offset(-3.5);
            make.bottom.mas_equalTo(self.bottomImageView.mas_top).mas_offset(3.5);
            make.width.mas_offset(1);
        }];

        [self.bottomImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(self.topImageView);
            make.size.mas_equalTo(CGSizeMake(7, 7));
            make.centerY.mas_equalTo(self.bottomLeftLabel);
        }];
        
    }
    
    return self;;
}


-(void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel{
    JDISVProcessFloorCellViewModel *model = (JDISVProcessFloorCellViewModel *)viewModel;
    if(ISFirstMoenyFlag){
        //付定金
            self.topImageView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"].CGColor;
            self.topImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

            self.bottomImageView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"].CGColor;
            self.bottomImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        
    }else{
        //付尾款
        self.topImageView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"].CGColor;
        self.topImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];

        self.bottomImageView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"].CGColor;
        self.bottomImageView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    self.topLeftLabel.attributedText = model.topStepAtt;
    self.topRightLabel.attributedText = model.topPriceAtt;
    self.topRightBottomLabel.attributedText = model.topDescAtt;
    self.bottomLeftLabel.attributedText = model.bottomStepAtt;
    self.bottomRightLabel.attributedText = model.bottomPriceAtt;
    self.bottomRightBottomLabel.attributedText = model.bottomDescAtt;
}
@end
