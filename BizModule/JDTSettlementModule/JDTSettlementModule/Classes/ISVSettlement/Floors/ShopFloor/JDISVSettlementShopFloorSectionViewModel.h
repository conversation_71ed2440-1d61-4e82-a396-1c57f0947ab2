//
//  JDISVSettlementShopFloorSectionViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVFloorCommonModel;

@interface JDISVSettlementShopFloorSectionViewModel : NSObject
@property (nonatomic, copy) NSString *imageDomain;
@property (nonatomic, copy) NSArray *floorViewModels; /**< 楼层ViewModels */
- (void)updateSectionWithUUIDKey:(NSString *)uuidKey
                       floorKeys:(NSArray *)floorKeys
                    allFloorKeys:(NSDictionary *)allKeysDic
                    allFloorData:(NSArray *)floorDatas
                     commonModel:(JDISVFloorCommonModel *)commonModel;
- (NSDictionary *)unselectedAllSkuParam;
@property (nonatomic,strong)NSDictionary *shopInfoMTA;//埋点数据
@end

NS_ASSUME_NONNULL_END
