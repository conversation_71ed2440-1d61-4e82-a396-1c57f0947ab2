//
//  JDISVSettlementShopFloorNetService.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
#define KASettlementShopFloorNetService [JDISVSettlementShopFloorNetService sharedService]

@class JDISVSettlementShopFloorMainModel;

@interface JDISVSettlementShopFloorNetService : NSObject
@property (nonatomic, copy) NSString *imageDomain;
+ (instancetype)sharedService;


/// 获取配送方式 se_getSelfDeliveryStores
/// @param orderStrParam
/// "orderStr":{
///     "bundleUuid":"BundleRelation_1020459379682418688",
///        "address":"北京市北京市朝阳区王四营乡广渠路一八号院(地铁七号线百子湾站B口)",
///     "idProvince":"110000",
///         "idCity":"110100",
///         "idArea":"110105",
///         "idTown":"110105042",
///         "venderId":"100562"}
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSelfStoresListInfoWithParam:(NSDictionary *)orderStrParam
                                                           sourceFrom:(NSInteger)source
                                                             complete:(void(^)(JDISVSettlementShopFloorMainModel *model, NSError *  _Nullable error))completeBlock;

#pragma mark - 配送方式
/// 获取配送方式 se_shipmentTypes
/// @param orderStrParam {venderIds:["100562"]}}
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestShipmentTypesWithParam:(NSDictionary *)orderStrParam
                                                        sourceFrom:(NSInteger)source
                                                        complete:(void(^)(JDISVSettlementShopFloorMainModel *model, NSError *  _Nullable error))completeBlock;


/// 保存配送方式 se_saveShipment
/// @param orderStrParam
/// "orderStr":
/// {
///    "shipmentUUid":"101612_1021197128644808704",
///    "shipmentId":"73",
///    "venderId":"100562",
///    "bundleUuid":"BundleRelation_1021096669303775233",
///    "orderUuid":"-737451729",
///    "combinationBundleUuid":"CombinationBundleRelation_1021096669119225859",
///    "venderStore":{
///        "storeId":"226",
///        "storeName":"回归门店AA1",
///        "storeAddress":"北京市北京市西城区新街口街道新龙大厦",
///        "businessHours":"00:00-23:00",
///        "distance":"0.02 km",
///        "latitude":"39.91169",
///        "longitude":"116.366272",
///        "recentlyMark":"1",
///        "warehouseId":"226",
///        "stockStatus":"1",
///        "venderStoreStockTab":"2",
///        "vendSource":"0"
///    }
/// }
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSaveShipmentWithParam:(NSDictionary *)orderStrParam
                                                     sourceFrom:(NSInteger)source
                                                       complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock;
@end

NS_ASSUME_NONNULL_END
