//
//  JDISVSettlementShopFloorAddressManager.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@class JDISVSettlementShopFloorAddressModel,JDISVFloorCommonModel;
@interface JDISVSettlementShopFloorAddressManager : NSObject
+ (instancetype)sharedService;

- (NSString * __nullable)decryptString:(NSString * __nonnull)encryptedStr;
- (void)saveSettlementConsigneeAddress:(JDISVSettlementShopFloorAddressModel *)consigneeAddressModel toCommonModel:(JDISVFloorCommonModel *)commonModel;
- (void)saveSettlementConsigneeAddressFromCommonModel:(JDISVFloorCommonModel *)commonModel;

- (NSNumber *)addressId;
- (NSString *)fullAddress;
- (NSDictionary *)requestShipmentSelfStoreParam;
- (NSString * __nullable)decryptString:(NSString * __nonnull)encryptedStr;
- (NSString *)levelAddress;
- (CGFloat)consigneeAddressLatitude;
- (CGFloat)consigneeAddressLongitude;
- (JDISVSettlementShopFloorAddressModel *)addressFloorModel;
@end

NS_ASSUME_NONNULL_END
