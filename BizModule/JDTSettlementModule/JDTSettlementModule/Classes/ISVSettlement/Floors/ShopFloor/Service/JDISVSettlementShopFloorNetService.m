//
//  JDISVSettlementShopFloorNetService.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorNetService.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "JDISVSettlementShopFloorMainModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

@implementation JDISVSettlementShopFloorNetService
+ (instancetype)sharedService {
    static JDISVSettlementShopFloorNetService * _sharedService = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedService = [[JDISVSettlementShopFloorNetService alloc] init];
    });
    
    return _sharedService;
}

/// 获取配送方式 se_getSelfDeliveryStores
/// @param orderStrParam
/// "orderStr":{
///     "bundleUuid":"BundleRelation_1020459379682418688",
///        "address":"北京市北京市朝阳区王四营乡广渠路一八号院(地铁七号线百子湾站B口)",
///     "idProvince":"110000",
///         "idCity":"110100",
///         "idArea":"110105",
///         "idTown":"110105042",
///         "venderId":"100562"}
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSelfStoresListInfoWithParam:(NSDictionary *)orderStrParam
                                                           sourceFrom:(NSInteger)source
                                                             complete:(void(^)(JDISVSettlementShopFloorMainModel *model, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }else{
        [param setObject:@(NO) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }else{
        [param setObject:@(NO) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];

    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_getSelfDeliveryStores" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                JDISVSettlementShopFloorMainModel *model = [JDISVSettlementShopFloorMainModel yy_modelWithDictionary:resultInfo];
                
                completeBlock(model, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
        
    }];
}

#pragma mark - 配送方式
/// 获取配送方式 se_shipmentTypes
/// @param orderStrParam {venderIds:["100562"]}}
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestShipmentTypesWithParam:(NSDictionary *)orderStrParam
                                                           sourceFrom:(NSInteger)source
                                                             complete:(void(^)(JDISVSettlementShopFloorMainModel *model, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];

    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_shipmentTypes" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                JDISVSettlementShopFloorMainModel *model = [JDISVSettlementShopFloorMainModel yy_modelWithDictionary:resultInfo];
                
                completeBlock(model, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
        
    }];
}

/// 保存配送方式 se_saveShipment
/// @param orderStrParam
/// "orderStr":
/// {
///    "shipmentUUid":"101612_1021197128644808704",
///    "shipmentId":"73",
///    "venderId":"100562",
///    "bundleUuid":"BundleRelation_1021096669303775233",
///    "orderUuid":"-737451729",
///    "combinationBundleUuid":"CombinationBundleRelation_1021096669119225859",
///    "venderStore":{
///        "storeId":"226",
///        "storeName":"回归门店AA1",
///        "storeAddress":"北京市北京市西城区新街口街道新龙大厦",
///        "businessHours":"00:00-23:00",
///        "distance":"0.02 km",
///        "latitude":"39.91169",
///        "longitude":"116.366272",
///        "recentlyMark":"1",
///        "warehouseId":"226",
///        "stockStatus":"1",
///        "venderStoreStockTab":"2",
///        "vendSource":"0"
///    }
/// }
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSaveShipmentWithParam:(NSDictionary *)orderStrParam
                                                     sourceFrom:(NSInteger)source
                                                       complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];

    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_saveShipment" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
        
    }];
}


@end
