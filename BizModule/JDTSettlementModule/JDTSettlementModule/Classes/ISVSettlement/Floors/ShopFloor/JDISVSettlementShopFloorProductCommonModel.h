//
//  JDISVSettlementShopFloorProductCommonModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:商品基本信息楼层
 模型命名空间:core.trade-FLR#balance.product-M#allProductFloor
 模型uuid: productFloor_1015583528770347008
 */
@interface JDISVSettlementShopFloorProductCommonModel : NSObject
// 内部Key:C-M#allProductFloor&basic
// 库存状态 1 全部无货 2 全部有货 3 部分有货
@property (nonatomic, assign) NSInteger stockStatus; /**< stockStatus:库存状态  */
@property (nonatomic, copy) NSString *imageDomain; /**< imageDomain:图片域名 */
@end

NS_ASSUME_NONNULL_END
