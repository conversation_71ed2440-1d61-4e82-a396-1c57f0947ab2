//
//  JDISVButtonFloorModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.songjian6 on 2022/7/14.
//

#import "JDISVPhoneFloorModel.h"
#import "JDISVPhoneFloor.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>


JDISVRegisterFloorModule(KaCheckPhoneFloor, JDISVPhoneFloorModel)

static NSString * const kJDISVSettmentPresaleMobileFloorPrefix = @"presaleMobileFloor";

@interface JDISVPhoneFloorModel ()
@property (nonatomic ,assign) CGFloat height;
@end
@implementation JDISVPhoneFloorModel
- (UIView *)floorView {
    UIView* view = [[JDISVPhoneFloor alloc] init];
    return view;
}

- (CGFloat)floorHeight {
    return self.height;
}


- (JDISVFloorType)floorType{
    return JDISVFloorTypeScrollFloor;
    
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.height = 0;
    
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];

    for (NSDictionary *floorData in floorsArray) {
        if ([floorData[@"uuid"] containsString:kJDISVSettmentPresaleMobileFloorPrefix]) {
            NSString *userMobile = floorData[@"info"][@"C-M#presaleMobileFloor&core"][@"userMobile"];
            if ([userMobile jdcd_validateString]) {
                self.userMobile = userMobile;
                self.height = 56;
                UIFont *font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
                self.height = self.height - font.lineHeight + [SettlementLan(@"checkout_final_payment_number") jdcd_getStringSize:font constraintsSize:CGSizeMake(200, MAXFLOAT)].height;
                
                [commonModel.commonData setObject:userMobile forKey:@"presaleUserMobile"];//手机号传给提单接口
            }
            break;
        }
    }
}

- (void)updateViewModelWith:(NSArray *)floorDatas {
//    if (floorDatas == nil) {
//        self.onlinePayItemModel = nil;
//    }
//    for (JDISVSettlementOnlinePayFloorModel *floorData in floorDatas) {
//        if ([floorData.uuid containsString:kJDISVSettmentOnlinePayFloorPrefix]) {
//            JDISVSettlementOnlinePayFloorItemModel *amountItemModel = [JDISVSettlementOnlinePayFloorItemModel yy_modelWithDictionary:floorData.info];
////            if ([amountItemModel.floorType isEqualToString:@"2"]) {
//                // type:2 应付金额
//                self.onlinePayItemModel = amountItemModel;
//                break;
////            }
//        }
//    }
    
//    if (_onlinePayItemModel) {
//        NSString *amount = SettlementLan(@"checkout_pay_online") ;
//        if ([_onlinePayItemModel.amount jdcd_validateString]) {
//            amount = [_onlinePayItemModel.amount copy];
//        }
//        NSAttributedString *priceAttributedString = [[NSAttributedString alloc] initWithString:amount attributes:@{
//            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
//            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
//        }];
//        self.amountAttributedString = priceAttributedString;
//
//        self.height = 56;
//    } else {
//        self.height = 0;
//        self.amountAttributedString = nil;
//    }
}

@end
