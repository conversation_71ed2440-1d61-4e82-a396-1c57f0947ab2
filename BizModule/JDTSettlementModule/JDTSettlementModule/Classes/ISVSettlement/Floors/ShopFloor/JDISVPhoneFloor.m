//
//  JDISVButtonFloor.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.songjian6 on 2022/7/14.
//

#import "JDISVPhoneFloor.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVPhoneFloorModel.h"
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

//#import <JDISVSDAutoLayoutModule/JDISVSDAutoLayoutModule-umbrella.h>


@interface JDISVPhoneFloor ()
@property (nonatomic,strong) UILabel *topLeftLabel;
@property (nonatomic,strong) UILabel *topRightLabel;

@property (nonatomic, strong) JDISVPhoneFloorModel *viewModel;
@end
@implementation JDISVPhoneFloor

-(instancetype)initWithFrame:(CGRect)frame{
    self  = [super initWithFrame:frame];
    if (self) {
        

        
        UILabel *topLeftLabel = [[UILabel alloc]init];
        topLeftLabel.numberOfLines = 0;
        self.topLeftLabel = topLeftLabel;
        [self addSubview:topLeftLabel];
        
        topLeftLabel.text = SettlementLan(@"checkout_final_payment_number") ;
        
        topLeftLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        
        topLeftLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        
//        topLeftLabel.sd_layout.leftSpaceToView(superView, 12).centerYEqualToView(superView).autoHeightRatio(0);
        [topLeftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self).offset(18);
            make.width.mas_lessThanOrEqualTo(@(200));
            make.centerY.equalTo(self);
        }];
        
        UILabel *topRightLabel = [[UILabel alloc]init];
        topRightLabel.textAlignment = NSTextAlignmentRight;
        self.topRightLabel = topRightLabel;
        [self addSubview:topRightLabel];
        
        //font
        //textColor
        topRightLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
        topRightLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        [topRightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self);
            make.trailing.equalTo(self).offset(-34);
            make.leading.mas_equalTo(topLeftLabel.mas_trailing).mas_offset(10);
        }];
        
        self.topLeftLabel.text = SettlementLan(@"checkout_final_payment_number") ;
//        self.topRightLabel.text = [@"18518715819" stringByReplacingCharactersInRange:NSMakeRange(3, 4) withString:@"****"];;

//        [self addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(contactService)]];
    }
    return self;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
//    [self updateWithViewModel:_viewModel];
    if ([self.viewModel.userMobile jdcd_validateString]) {
        self.topRightLabel.text = [self.viewModel.userMobile stringByReplacingCharactersInRange:NSMakeRange(3, 4) withString:@"****"];
    }
}



-(void)contactService{

}
@end



