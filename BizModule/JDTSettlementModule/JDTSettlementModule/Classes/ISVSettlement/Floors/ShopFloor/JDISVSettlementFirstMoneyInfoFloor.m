//
//  JDISVButtonFloor.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.songjian6 on 2022/7/14.
//

#import "JDISVSettlementFirstMoneyInfoFloor.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "UIImage+JDISVSettlement.h"
#import "JDISVSettlementFirstMoneyInfoFloorMoudle.h"

@interface JDISVSettlementFirstMoneyInfoFloor ()
@property (nonatomic,strong) UILabel *topLeftLabel;
@property (nonatomic,strong) UILabel *topRightLabel;
@property (nonatomic,strong)  UIImageView *agreeSwitch ;
@property (nonatomic,strong)  UIView *ciycleWhiteView ;
@property (nonatomic,strong) NSNumber *isSelected;
@property (nonatomic,strong) UILabel *sumMoneyTitleLabel;
@property (nonatomic,strong) UILabel *sumMoneyLabel;
//发票
@property (weak, nonatomic) UILabel *invoiceTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *invoiceInfoLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImageView;

@property (nonatomic, strong) JDISVSettlementFirstMoneyInfoFloorMoudle *viewModel;

@end
@implementation JDISVSettlementFirstMoneyInfoFloor

-(instancetype)initWithFrame:(CGRect)frame{
    self  = [super initWithFrame:frame];
    if (self) {
        
        UIFont *normalFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        
        
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeSwitch:) name:@"changeSwitchMode" object:nil];
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        
        UILabel *topLeftLabel = [[UILabel alloc]init];
        topLeftLabel.text = SettlementLan(@"checkout_front_money") ;
        self.topLeftLabel = topLeftLabel;
        [self addSubview:topLeftLabel];
        
        
        topLeftLabel.font = normalFont;
        
        topLeftLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
//        topLeftLabel.sd_layout.leftSpaceToView(self, 12).topSpaceToView(self, 18).autoHeightRatio(0);
        [topLeftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self).offset(18);
            make.top.equalTo(self).offset(18);
        }];
        
        UILabel *topRightLabel = [[UILabel alloc]init];
        
        self.topRightLabel = topRightLabel;
        [self addSubview:topRightLabel];
        
        topRightLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        topRightLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
//        topRightLabel.sd_layout.rightSpaceToView(self, 12).topSpaceToView(self, 18).autoHeightRatio(0);
        [topRightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self).offset(-18);
            make.top.equalTo(self).offset(18);
        }];
        
        
        UILabel *agreePayLabel = [[UILabel alloc]init];
        [self addSubview:agreePayLabel];
        agreePayLabel.text = SettlementLan(@"checkout_pre_sale_agree_label") ;
        
        agreePayLabel.textColor = topLeftLabel.textColor;
        agreePayLabel.font = topLeftLabel.font;
//        agreePayLabel.sd_layout.topSpaceToView(topLeftLabel, 39/2).leftEqualToView(topLeftLabel).autoHeightRatio(0);
        [agreePayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(topLeftLabel.mas_bottom).offset(44/2);
            make.leading.equalTo(topLeftLabel);
        }];
        
        
        
        UILabel *agreeDetailPayLabel = [[UILabel alloc]init];
        [self addSubview:agreeDetailPayLabel];
        agreeDetailPayLabel.text = SettlementLan(@"checkout_pre_sale_agree_tips") ;
        
        agreeDetailPayLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        agreeDetailPayLabel.font = normalFont;
//        agreeDetailPayLabel.sd_layout.topSpaceToView(agreePayLabel, 4).leftEqualToView(topLeftLabel).autoHeightRatio(0);
        [agreeDetailPayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(agreePayLabel.mas_bottom).offset(4);
            make.leading.equalTo(topLeftLabel);
        }];
        
        //发票
        UILabel *invoiceTitle = [[UILabel alloc]init];
        self.invoiceTitleLabel = invoiceTitle;
        [self addSubview:invoiceTitle];
        invoiceTitle.text = SettlementLan(@"checkout_invoice_label") ;
        self.invoiceTitleLabel.textColor = topLeftLabel.textColor;
        self.invoiceTitleLabel.font = topLeftLabel.font;
        [self.invoiceTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(agreeDetailPayLabel.mas_bottom).offset(39/2);
            make.leading.equalTo(topLeftLabel);
        }];
        
        UIImageView *rightArrowImageView = [[UIImageView alloc]init];
        rightArrowImageView.backgroundColor = [UIColor clearColor];
        rightArrowImageView.userInteractionEnabled = YES;
        rightArrowImageView.layer.cornerRadius = 8;
        rightArrowImageView.layer.masksToBounds = YES;
        self.rightArrowImageView = rightArrowImageView;
        self.rightArrowImageView.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        self.rightArrowImageView.contentMode = UIViewContentModeCenter;
        [self addSubview:self.rightArrowImageView];
        [rightArrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.invoiceTitleLabel);
            make.trailing.equalTo(self.mas_trailing).offset(-22);
            make.width.equalTo(@(20));
            make.height.equalTo(@(20));
        }];
        
        UILabel *invoiceInfo = [[UILabel alloc]init];
        self.invoiceInfoLabel = invoiceInfo;
        [self addSubview:invoiceInfo];
        self.invoiceInfoLabel.textColor = topLeftLabel.textColor;
        self.invoiceInfoLabel.font = topLeftLabel.font;
        [self.invoiceInfoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.invoiceTitleLabel);
            make.leading.equalTo(self.invoiceTitleLabel.mas_trailing).offset(18);
            make.trailing.equalTo(self.rightArrowImageView.mas_leading).offset(0);
        }];
        
        [self.invoiceInfoLabel jd_addTapAction:@selector(clickInfoImageView) withTarget:self];
        [self.rightArrowImageView jd_addTapAction:@selector(clickInfoImageView) withTarget:self];
        
        
        UIView *segmentionLineView = [[UIView alloc]init];
        [self addSubview:segmentionLineView];
        segmentionLineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
//        segmentionLineView.sd_layout.topSpaceToView(agreeDetailPayLabel, 18).leftSpaceToView(self, 0).rightSpaceToView(self, 0).heightIs(1);
        
        
        UIImageView *agreeSwitch = [[UIImageView alloc]init];
        agreeSwitch.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        agreeSwitch.userInteractionEnabled = YES;
        agreeSwitch.layer.cornerRadius = 8;
        agreeSwitch.layer.masksToBounds = YES;
        self.agreeSwitch = agreeSwitch;
        
        [agreeSwitch addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(switchAction)]];
//        agreeSwitch.image = [UIImage jdisvSettlement_imageNamed:@"switchNo"];
        
        [self addSubview:agreeSwitch];
        [agreeSwitch mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(agreePayLabel);
            make.trailing.equalTo(topRightLabel);
            make.width.equalTo(@(45));
            make.height.equalTo(@(16));
        }];
        
        UIView *ciycleWhiteView = [[UIImageView alloc]init];
        ciycleWhiteView.backgroundColor = [UIColor whiteColor];//[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        ciycleWhiteView.userInteractionEnabled = YES;
        ciycleWhiteView.layer.cornerRadius = 11;
        ciycleWhiteView.layer.masksToBounds = NO;
            // 阴影颜色
        ciycleWhiteView.layer.shadowColor = [[UIColor blackColor] colorWithAlphaComponent:0.1].CGColor;
            // 阴影偏移，默认(0, -3)
        ciycleWhiteView.layer.shadowOffset = CGSizeMake(0,2);
            // 阴影透明度，默认0
        ciycleWhiteView.layer.shadowOpacity = 1;
            // 阴影半径，默认3
        ciycleWhiteView.layer.shadowRadius = 6;
        self.ciycleWhiteView = ciycleWhiteView;
        [ciycleWhiteView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(switchAction)]];
        [self addSubview:ciycleWhiteView];
        [ciycleWhiteView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(agreeSwitch);
            make.leading.equalTo(agreeSwitch);
            make.width.equalTo(@(22));
            make.height.equalTo(@(22));
        }];
        
        UILabel *sumMoneyTitleLabel = [[UILabel alloc]init];
        self.sumMoneyTitleLabel = sumMoneyTitleLabel;
        sumMoneyTitleLabel.textColor = topLeftLabel.textColor;
        sumMoneyTitleLabel.font = topLeftLabel.font;
        sumMoneyTitleLabel.text = SettlementLan(@"checkout_pre_sale_sum_label") ;
        [self addSubview:sumMoneyTitleLabel];
        
        UILabel *sumMoneyLabel = [[UILabel alloc]init];
        self.sumMoneyLabel = sumMoneyLabel;
        
        [self addSubview:sumMoneyLabel];
        
        
        UILabel *moneyDetailLabel = [[UILabel alloc]init];
        moneyDetailLabel.numberOfLines = 0;
        moneyDetailLabel.textAlignment = NSTextAlignmentRight;
        [self addSubview:moneyDetailLabel];
        moneyDetailLabel.text = SettlementLan(@"checkout_pre_sale_sum_tips") ;
        
        moneyDetailLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        moneyDetailLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
//        moneyDetailLabel.sd_layout.bottomSpaceToView(self, 18).rightEqualToView(topRightLabel).autoHeightRatio(0);
        [moneyDetailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.mas_bottom).offset(-18);
            make.trailing.equalTo(topRightLabel);
            make.leading.equalTo(topLeftLabel);
        }];
        
        
//        sumMoneyLabel.sd_layout.bottomSpaceToView(moneyDetailLabel, 5.5).rightEqualToView(topRightLabel).autoHeightRatio(0);
        [sumMoneyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(moneyDetailLabel.mas_top).offset(-5.5);
            make.trailing.equalTo(topRightLabel);
        }];
        [sumMoneyTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(moneyDetailLabel.mas_top).offset(-5.5);
            make.trailing.equalTo(sumMoneyLabel.mas_leading);
        }];
        [segmentionLineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.sumMoneyTitleLabel.mas_top).offset(-18);
            make.leading.equalTo(self);
            make.trailing.equalTo(self);
            make.height.equalTo(@(0.5));
        }];
        
//        self.isSelected = @(FALSE);
        
        
    }
    return self;
}

- (void)switchAction{
    if([self.isSelected boolValue] == YES){
        self.isSelected = [NSNumber numberWithBool:NO];
//        self.agreeSwitch.image = [UIImage jdisvSettlement_imageNamed:@"switchNo"];
        self.viewModel.isSwitchOpen = NO;
        
        self.agreeSwitch.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        [self.ciycleWhiteView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.agreeSwitch);
            make.leading.equalTo(self.agreeSwitch);
            make.width.equalTo(@(22));
            make.height.equalTo(@(22));
        }];
    }else{
        self.isSelected = [NSNumber numberWithBool:YES];
//        self.agreeSwitch.image = [UIImage jdisvSettlement_imageNamed:@"switch"];
        self.viewModel.isSwitchOpen = YES;
        
        self.agreeSwitch.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        [self.ciycleWhiteView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.agreeSwitch);
            make.trailing.equalTo(self.agreeSwitch);
            make.width.equalTo(@(22));
            make.height.equalTo(@(22));
        }];

        
    }
    
    
//    @{@"cellName":@"JDISVSettlementFirstMoneyCell"}
//    @{@"isSelected":self.isSelected}
    
//    JDCDISVAction *action = [[JDCDISVAction alloc]initWithType:@"JDISVSettlementFirstMoneyInfoFloor" broadcastAction:NO];
//    action.value = @{@"isSelected":self.isSelected};
//    [self isv_sendAction:action];
}

-(void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    self.viewModel = floorModel;
//    self.isSelected = @(self.viewModel.isSwitchOpen);
    if (self.viewModel.isSwitchOpen) {
        self.isSelected = @(self.viewModel.isSwitchOpen);
//        self.agreeSwitch.image = [UIImage jdisvSettlement_imageNamed:@"switchNo"];
        self.agreeSwitch.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        [self.ciycleWhiteView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.agreeSwitch);
            make.trailing.equalTo(self.agreeSwitch);
            make.width.equalTo(@(22));
            make.height.equalTo(@(22));
        }];
    }
    //合计
    double depositValue = self.viewModel.depositAmount.doubleValue;
    NSString *deposit = [NSString stringWithFormat:@"%.2f",depositValue];
    NSMutableAttributedString *depositpriceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
    [depositpriceAttributedString KA_renderWithPriceStr:deposit type:KAPriceTypeP4 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]];
    self.topRightLabel.attributedText = depositpriceAttributedString;
    
    
    NSString *sumMoney = [NSString stringWithFormat:@"%@",deposit];
//    NSArray *moneyAry = [sumMoney componentsSeparatedByString:@"."];
//    NSString *firstMoney = moneyAry[0];
    NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:@""];
    [priceAttributedString KA_renderWithPriceStr:sumMoney type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    self.sumMoneyLabel.attributedText = priceAttributedString;
    
    //更新发票
    if (_viewModel.invoiceFeatureFlag) {
        self.invoiceInfoLabel.attributedText = _viewModel.invoiceInfoAttributedString;
    }else{
        self.invoiceTitleLabel.hidden = YES;
        self.invoiceInfoLabel.hidden = YES;
        self.rightArrowImageView.hidden = YES;
    }
}



- (void)clickInfoImageView {
    // 打开发票页面H5
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementFirstMoneyFloorOpenInvoicePage"];
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVSettlementFirstMoneyFloorOpenInvoicePage"]) {
        [self openUseInvoicePageWithController:controller];
        return YES;
    }
    
    return NO;
}

- (void)openUseInvoicePageWithController:(UIViewController *)controller {
    @weakify(self)
    void(^backActionBlock)(id data, NSString *string) = ^void(id data, NSString *string){
        @strongify(self)
        //收到H5返回的选择发票回调后，调获取发票列表主接口
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorSelectInvoiceRequestAndRefresh"];
        [self isv_sendAction:action];
    };

    NSString *h5Url = [PlatformService getJumpH5UrlByType:JDISVJumpH5UrlTypeInvoiceList withAppendParam:@""] ? : @"";
    NSDictionary* param = @{@"text_url":h5Url,@"backAction":backActionBlock};
    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
    NSString* routerStr= [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
    [JDRouter openURL:routerStr arg:param error:nil completion:^(id obj){
        [controller.navigationController pushViewController:obj animated:YES];
    }];
}
@end



