//
//  JDISVSettlementShopFloorSectionViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "JDISVSettlementShopFloorSectionViewModel.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>

#import "JDISVSettlementShopFloorModel.h"
#import "JDISVSettlementShopFloorRoundCellViewModel.h"
#import "JDISVSettlementShopFloorBaseCellViewModel.h"
#import "JDISVSettlementShopFloorVenderCellViewModel.h"
#import "JDISVSettlementShopFloorProductCellViewModel.h"
#import "JDISVSettlementShopFloorAdditionalProductCellViewModel.h"
#import "JDISVSettlementStoreInfoCellViewModel.h"
#import "JDISVSettlementShopFloorShipmentCellViewModel.h"
#import "JDISVSettlementShopFloorRemarkCellViewModel.h"
#import "JDISVSettlementShopFloorProductCommonModel.h"
#import "JDISVSettlementFreebieCellViewModel.h"
#import "JDISVSettlementShopFloorNetService.h"

#import "JDISVProcessFloorCellViewModel.h"

static NSString * const kJDISVSettmentShopFloorProductCommonModelPrefix = @"productFloor";

static NSString * const kJDISVSettmentShopFloorProductFloorPrefix = @"retailProductFloor";//正常商品楼层
static NSString * const kJDISVSettmentShopFloorTotalPriceProductFloorPrefix = @"totalPriceProductFloor";//换购楼层：含有正常商品和换购商品的楼层
static NSString * const kJDISVSettmentShopFloorAdditionalProductFloorPrefix = @"additionalProductFloor";//换购商品楼层
static NSString * const kJDISVSettmentShopFloorVirtualProductFloorPrefix = @"virtualProductFloor";//虚拟组套楼层：含有正常商品和虚拟商品的楼层
static NSString * const kJDISVSettmentShopFloorvirtualCommodityProductFloorPrefix = @"virtualCommodityFloor";//虚拟商品楼层
static NSString * const kJDISVSettmentShopFloorPresellFloorPrefix = @"presaleDefaultFloor";//预售楼层

static NSString * const kJDISVSettmentShopFloorShipmentHomeFloorPrefix = @"shipmentHomePageFloor";
static NSString * const kJDISVSettmentShopFloorPromiseHomeFloorPrefix = @"defaultPromiseHomePageFloor";
static NSString * const kJDISVSettmentShopFloorVenderPickFloorPrefix = @"venderPickFloor";


static NSString * const kJDISVSettmentShopFloorRemarkFloorPrefix = @"defaultRemarkFloor";

@interface JDISVSettlementShopFloorSectionViewModel()
@property (nonatomic, strong) JDISVSettlementShopFloorVenderCellViewModel *venderFloorViewModel; /**< 店铺楼层 */

@property (nonatomic, strong) JDISVSettlementShopFloorProductCommonModel *productCommonModel;
@end

@implementation JDISVSettlementShopFloorSectionViewModel
- (void)updateSectionWithUUIDKey:(NSString *)uuidKey
                       floorKeys:(NSArray *)floorKeys
                    allFloorKeys:(NSDictionary *)allKeysDic
                    allFloorData:(NSArray *)floorDatas
                     commonModel:(JDISVFloorCommonModel *)commonModel {
    
    NSMutableArray *venderFloors = [NSMutableArray array];
    
    JDISVSettlementShopFloorRoundCellViewModel *topViewModel = [[JDISVSettlementShopFloorRoundCellViewModel alloc] initWithCellType:JDISVSettlementShopFloorRoundCellTypeR1];
    [venderFloors addObject:topViewModel];
    
    self.venderFloorViewModel = [[JDISVSettlementShopFloorVenderCellViewModel alloc] init];
    for (JDISVSettlementShopFloorModel *floorModel in floorDatas) {
        if ([floorModel.uuid isEqualToString:uuidKey]) {
            [self.venderFloorViewModel updateWithData:floorModel.info forType:0];
            [venderFloors addObject:_venderFloorViewModel];
            break;
        }
    }
    
    // 商品通用信息模型
    for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:kJDISVSettmentShopFloorProductCommonModelPrefix]) {
            self.productCommonModel = [JDISVSettlementShopFloorProductCommonModel yy_modelWithDictionary:floorData.info];
            //TODO: 强制修改为本地域名配置
            NSString *temp = @"imurl.png";
            NSString *resultString = [PlatformService getCompleteImageUrl:temp moduleType:@""];
            resultString = [resultString substringWithRange:NSMakeRange(0, [resultString rangeOfString:temp].location)];
            self.productCommonModel.imageDomain = resultString;
            
            KASettlementShopFloorNetService.imageDomain = _productCommonModel.imageDomain;
            self.imageDomain = _productCommonModel.imageDomain;
            [[NSUserDefaults standardUserDefaults] setObject:self.imageDomain  forKey:@"imageDomain"];
            [[NSUserDefaults standardUserDefaults] synchronize];
            break;
        }
    }
    /*8.28 返回参数中不在包含 C-D#imgInfo&basic.imageDomain 使用本地设置图片头部域名 */
    self.productCommonModel = [JDISVSettlementShopFloorProductCommonModel new];
    //TODO: 强制修改为本地域名配置
    NSString *temp = @"imurl.png";
    NSString *resultString = [PlatformService getCompleteImageUrl:temp moduleType:@""];
    resultString = [resultString substringWithRange:NSMakeRange(0, [resultString rangeOfString:temp].location)];
    self.productCommonModel.imageDomain = resultString;
    
    KASettlementShopFloorNetService.imageDomain = _productCommonModel.imageDomain;
    self.imageDomain = _productCommonModel.imageDomain;
    [[NSUserDefaults standardUserDefaults] setObject:self.imageDomain  forKey:@"imageDomain"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    // 获取商品的floorData，正常商品和子商品按顺序加到商品数组中，顺序与接口下发一致
    NSMutableArray *productFloorDatas = [NSMutableArray array];
//    NSMutableArray *virtualSectionFloors = [NSMutableArray array];//虚拟组套楼层
    NSMutableArray *virtualSectionDatas = [NSMutableArray array];//虚拟组套商品
    NSMutableArray *shipmentFloorDatas = [NSMutableArray array];
    JDISVSettlementShopFloorModel *remarkFloorData;
    JDISVSettlementShopFloorModel *presellFloorData;//预售
    
    for (NSString *floorKey in floorKeys) {
        //换购:含有正常商品和换购商品的处理，retailProductFloor和additionalProductFloor
        if ([floorKey containsString:kJDISVSettmentShopFloorTotalPriceProductFloorPrefix]) {
            NSArray *totalProductfloorKeys = (NSArray *)[allKeysDic objectForKey:floorKey];
            NSMutableArray *totalPriceProductFloorDatas = [NSMutableArray array];
                for (NSString *totalProductfloorkey in totalProductfloorKeys) {
                    for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
                        if ([totalProductfloorkey isEqualToString:floorData.uuid]) {
                            if ([totalProductfloorkey containsString:kJDISVSettmentShopFloorProductFloorPrefix]) {
                                //正常商品放在第0个
                                [totalPriceProductFloorDatas insertObject:floorData atIndex:0];
                            }else{
                                [totalPriceProductFloorDatas addObject:floorData];
                            }
                            continue;
                        }
                    }
                }
            [productFloorDatas addObjectsFromArray:totalPriceProductFloorDatas];
        }
        
        //虚拟组套：虚拟商品的处理
        if ([floorKey containsString:kJDISVSettmentShopFloorVirtualProductFloorPrefix]) {
            NSArray *originVirtualflooKeys = (NSArray *)[allKeysDic objectForKey:floorKey];
            NSMutableArray *virtualFloorKeys = originVirtualflooKeys.mutableCopy;
            [virtualFloorKeys addObject:floorKey];//把虚拟楼层也加到里面
            NSMutableArray *virtualproductFloorDatas = [[NSMutableArray alloc]init];
            for (NSString *virtualFloorKey in virtualFloorKeys) {
                for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
                    if ([floorData.uuid isEqualToString:virtualFloorKey]) {
                        [virtualproductFloorDatas addObject:floorData];
                        continue;
                    } else if ([floorData.uuid isEqualToString:floorKey]) {
                        [virtualproductFloorDatas addObject:floorData];//添加虚拟组套楼层
                        continue;
                    }
                }
            }
            [virtualSectionDatas addObject:virtualproductFloorDatas];
        }

        for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
            if ([floorKey isEqualToString:floorData.uuid]) {
                /* ------------商品楼层--------------- */
                if ([floorData.uuid containsString:kJDISVSettmentShopFloorProductFloorPrefix]) {
                    //
                    [productFloorDatas addObject:floorData];
                    
                    //赠品：主商品下挂的赠品
                    NSArray *originAdditionalFloorKeys = (NSArray *)[allKeysDic objectForKey:floorKey];
                    for (NSString *additionalKey in originAdditionalFloorKeys) {
                        for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
                            if ([floorData.uuid isEqualToString:additionalKey]) {
                                floorData.isFreeAdditionProduct = YES;//手动标记为赠品，用以区分换购的子品
                                [productFloorDatas addObject:floorData];
                                continue;
                            }
                        }
                    }
                    
                    continue;
                }
                /* ------------预售楼层--------------- */
                for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
                    if ([floorData.uuid containsString:kJDISVSettmentShopFloorPresellFloorPrefix]) {
                        presellFloorData = floorData;
                        break;
                    }
                }
                /* ------------配送楼层--------------- */
                if ([floorData.uuid containsString:kJDISVSettmentShopFloorShipmentHomeFloorPrefix] ||
                    [floorData.uuid containsString:kJDISVSettmentShopFloorPromiseHomeFloorPrefix] ||
                    [floorData.uuid containsString:kJDISVSettmentShopFloorVenderPickFloorPrefix]) {
                    //
                    [shipmentFloorDatas addObject:floorData];
                    continue;
                }
                /* ------------留言楼层--------------- */
                if ([floorData.uuid containsString:kJDISVSettmentShopFloorRemarkFloorPrefix]) {
                    //
                    remarkFloorData = floorData;
                    continue;
                }
            }
        }
    }
    
    
    NSMutableArray *skuids = @[].mutableCopy;
    NSMutableArray *skuInfos = @[].mutableCopy;
    if (virtualSectionDatas.count > 0) {
        //虚拟组套商品，横向排列
        JDISVSettlementStoreInfoCellViewModel *productFloorViewModel = [[JDISVSettlementStoreInfoCellViewModel alloc] initWithImageDomain:_productCommonModel.imageDomain];
        [productFloorViewModel updateWithData:virtualSectionDatas forType:0];
        [venderFloors addObject:productFloorViewModel];
        
        for (NSArray *virtualFloors in virtualSectionDatas) {
            for (JDISVSettlementShopFloorModel *floorModel in virtualFloors) {
                if ([floorModel.uuid containsString:kJDISVSettmentShopFloorProductFloorPrefix]) {
                    NSDictionary *floorDic = floorModel.info;
                    NSDictionary *basicDic = [floorDic objectForKey:@"C-M#retailProductFloor&basic"];
                    NSString *skuid = basicDic[@"skuId"];
                    if ([skuid jdcd_validateString]) {
                        [skuInfos addObject:@{@"skuId":skuid}];
                        [skuids addObject:skuid];
                    }
                }
            }
        }
    }
    
    if (productFloorDatas.count > 0){
        //竖向排列商品
        BOOL hasShowFreeTitle = NO;
        for (JDISVSettlementShopFloorModel *floorModel in productFloorDatas) {
            if ([floorModel.uuid containsString:kJDISVSettmentShopFloorAdditionalProductFloorPrefix]) {
                NSString *skuid = @"";
                //换购子品和赠品，key都是@"additionalProductFloor"；
                if (floorModel.isFreeAdditionProduct) {
                    //赠品
                    JDISVSettlementFreebieCellViewModel *freeProductFloorViewModel = [[JDISVSettlementFreebieCellViewModel alloc] init];
                    [freeProductFloorViewModel updateWithData:floorModel.info forType:0];
                    if (hasShowFreeTitle == NO) {
                        hasShowFreeTitle = YES;
                        freeProductFloorViewModel.showFreeTitle = YES;//仅第一个赠品展示”赠品“标题
                    }
                    [venderFloors addObject:freeProductFloorViewModel];
                    skuid = freeProductFloorViewModel.skuId;
                }else{
                    //换购商品
                    hasShowFreeTitle = NO;
                    JDISVSettlementShopFloorAdditionalProductCellViewModel *productFloorViewModel = [[JDISVSettlementShopFloorAdditionalProductCellViewModel alloc] initWithImageDomain:_productCommonModel.imageDomain];
                    [productFloorViewModel updateWithData:floorModel.info forType:0];
                    [venderFloors addObject:productFloorViewModel];
                    skuid = productFloorViewModel.skuId;
                }
                if ([skuid jdcd_validateString]) {
                    [skuInfos addObject:@{@"skuId":skuid}];
                    [skuids addObject:skuid];
                }
            } else if ([floorModel.uuid containsString:kJDISVSettmentShopFloorProductFloorPrefix]) {
                //正常商品
                hasShowFreeTitle = NO;
                JDISVSettlementShopFloorProductCellViewModel *productFloorViewModel = [[JDISVSettlementShopFloorProductCellViewModel alloc] initWithImageDomain:_productCommonModel.imageDomain];
                [productFloorViewModel updateWithData:floorModel.info commonModel:commonModel];
                [venderFloors addObject:productFloorViewModel];

                NSString *skuid = productFloorViewModel.skuId;
                if ([skuid jdcd_validateString]) {
                    [skuInfos addObject:@{@"skuId":skuid}];
                    [skuids addObject:skuid];
                }
            }
        }
    }
    
    //埋点数据
    self.shopInfoMTA = @{
        @"shopId":_venderFloorViewModel.venderId ? : @"",
        @"skuinfo":skuInfos};
    [commonModel.commonData setObject:skuids forKey:@"skuids_mta"];
    
    //预售楼层
    if (presellFloorData) {
        JDISVSettlementShopFloorModel *depositFloorData;
        for (JDISVSettlementShopFloorModel *floorData in floorDatas) {
            if ([floorData.uuid containsString:@"presaleTotalDepositFloor"]) {
                depositFloorData = floorData;
                break;
            }
        }
        JDISVProcessFloorCellViewModel *processViewModel = [[JDISVProcessFloorCellViewModel alloc]init];
        [processViewModel updateWithData:presellFloorData.info allFloorData:depositFloorData.info];
        [venderFloors addObject:processViewModel];
        
        if (ISPresell && ISFirstMoenyFlag == NO){
            //预售付尾款，组装预售商品金额旁边的info按钮点击后的弹窗数据
            NSString *coverImgUrl = @"";
            for (id cellModel in venderFloors){
                if ([cellModel isKindOfClass:JDISVSettlementShopFloorProductCellViewModel.class]) {
                    coverImgUrl = ((JDISVSettlementShopFloorProductCellViewModel *)cellModel).coverImgUrl ? : @"";
                    break;
                }
            }
            NSString *venderName = _venderFloorViewModel.venderNameAttributedString.string ? : @"";
            NSString *depositAmount = processViewModel.depositAmount ? : @"";//定金
            NSString *endPayment = processViewModel.factEndPayment ? : @"";//尾款
            [commonModel.commonData setObject:@{@"venderName":venderName,@"coverImgUrl":coverImgUrl,@"depositAmount":depositAmount,@"endPayment":endPayment} forKey:@"presaleToastParams"];
        }
    }
    
    
    // 配送楼层
    JDISVSettlementShopFloorShipmentCellViewModel *shipmentFloorViewModel = [[JDISVSettlementShopFloorShipmentCellViewModel alloc] init];
    shipmentFloorViewModel.venderId = _venderFloorViewModel.venderId;
    [shipmentFloorViewModel updateWithData:shipmentFloorDatas allFloorData:floorDatas];
    // 不需要隐藏配送信息是，就添加配送信息展示
    if (!shipmentFloorViewModel.needHideStandardDelivery) {
        [venderFloors addObject:shipmentFloorViewModel];
    }
    
    JDISVSettlementShopFloorRoundCellViewModel *bottomViewModel = [[JDISVSettlementShopFloorRoundCellViewModel alloc] initWithCellType:JDISVSettlementShopFloorRoundCellTypeR2];
    [venderFloors addObject:bottomViewModel];
    

    
    self.floorViewModels = [NSArray arrayWithArray:venderFloors];
}

- (NSDictionary *)unselectedAllSkuParam {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    for (JDISVSettlementShopFloorBaseCellViewModel *floorViewModel in self.floorViewModels) {
        if ([floorViewModel isKindOfClass:JDISVSettlementShopFloorProductCellViewModel.class]) {
            JDISVSettlementShopFloorProductCellViewModel *productFloorViewModel = (JDISVSettlementShopFloorProductCellViewModel *)floorViewModel;
            if ([productFloorViewModel.skuId jdcd_validateString]) {
                [param addEntriesFromDictionary:@{@"skuId": productFloorViewModel.skuId}];
            }
            if (productFloorViewModel.skuNum) {
                [param addEntriesFromDictionary:@{@"num": @([productFloorViewModel.skuNum integerValue])}];
            }
            [param addEntriesFromDictionary:@{@"itemType": @(1)}];
        }
    }
    
    return [NSDictionary dictionaryWithDictionary:param];
}
@end
