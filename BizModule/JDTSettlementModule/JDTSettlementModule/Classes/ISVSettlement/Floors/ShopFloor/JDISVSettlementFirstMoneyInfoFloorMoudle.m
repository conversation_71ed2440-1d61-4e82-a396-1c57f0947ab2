//
//  JDISVButtonFloorModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.songjian6 on 2022/7/14.
//

#import "JDISVSettlementFirstMoneyInfoFloorMoudle.h"
#import "JDISVSettlementFirstMoneyInfoFloor.h"
#import "NSAttributedString+JDISVSettlementAmount.h"

static NSString * const kJDISVSettmentPresaleDepositFloorPrefix = @"presaleTotalDepositFloor";

JDISVRegisterFloorModule(kaCheckFirstMoneyInfoFloor, JDISVSettlementFirstMoneyInfoFloorMoudle)
@interface JDISVSettlementFirstMoneyInfoFloorMoudle ()
@property (nonatomic ,assign) CGFloat height;
@property (nonatomic ,strong) JDISVFloorCommonModel *commonModel;
@end

@implementation JDISVSettlementFirstMoneyInfoFloorMoudle
- (UIView *)floorView {
    UIView* view = [[JDISVSettlementFirstMoneyInfoFloor alloc] init];
    return view;
}

- (CGFloat)floorHeight {
    return self.height;
}


- (JDISVFloorType)floorType{
    return JDISVFloorTypeScrollFloor;
    
}

-(void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
    NSDictionary *featureExt = data[@"ext"] ? : @{}; // 获取配置开关
    
    self.commonModel = commonModel;
    self.height = 0;
    
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];

    for (NSDictionary *floorData in floorsArray) {
        if ([floorData[@"uuid"] containsString:kJDISVSettmentPresaleDepositFloorPrefix]) {
//            "agreePayDeposit" : "同意支付定金",
//            "depositAmount" : "1.00",
//            "depositTitle" : "定金",
//            "refundDeposit" : "0"
            NSDictionary *depositFloorDic = floorData[@"info"][@"C-M#presaleTotalDepositFloor&basic"];
            if (depositFloorDic && [depositFloorDic isKindOfClass:[NSDictionary class]]) {
                NSString *agreePayDeposit = depositFloorDic[@"agreePayDeposit"];
                NSString *depositAmount = depositFloorDic[@"depositAmount"];
                NSString *depositTitle = depositFloorDic[@"depositTitle"];

                self.agreePayDeposit = agreePayDeposit;
                self.depositAmount = depositAmount;
                self.depositTitle = depositTitle;
                self.isSwitchOpen = NO;

                self.height = 230 - 38;
                
                BOOL invoiceFeatureFlag = ([featureExt objectForKey:@"invoiceFeature"] && [[featureExt objectForKey:@"invoiceFeature"] isEqualToNumber:@1]) ? YES : NO;
                self.invoiceFeatureFlag = invoiceFeatureFlag;
                if (invoiceFeatureFlag) {
                    NSDictionary *otherParams = resultInfo[@"otherParams"];
                    [self updateInvoiceDataByRouterParams:otherParams[@"originInvoiceListData"] commond:commonModel];
                    self.height = 230;
                }
            }
            break;
        }
    }
}

- (void)updateInvoiceDataByRouterParams:(NSDictionary *)originInvoiceData commond:(JDISVFloorCommonModel *)commonModel{
    NSString *showMsg = SettlementLan(@"checkout_invoice_type_personal") ;
    if (originInvoiceData && [originInvoiceData isKindOfClass:[NSArray class]]) {
        for (NSDictionary *invoiceDic in originInvoiceData){
            if (invoiceDic && [invoiceDic isKindOfClass:[NSDictionary class]]) {
                NSNumber *isSelected = (NSNumber *)invoiceDic[@"isSelected"];
                if (isSelected.boolValue) {
                    //找到选中的发票
                    if (invoiceDic[@"invoiceTitleType"]) {
                        NSInteger invoiceTitleType =((NSNumber *)invoiceDic[@"invoiceTitleType"]).integerValue;
                        if (invoiceTitleType == 0) {
                            showMsg = SettlementLan(@"checkout_invoice_type_personal") ;
                        }else if (invoiceTitleType == 1) {
                            showMsg = SettlementLan(@"checkout_invoice_type_company") ;
                        }
                        [commonModel.commonData setObject:invoiceDic forKey:@"selectedInvoiceData"];
                    }
                    break;
                }
            }
        }
    }
    
    self.invoiceInfoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:showMsg colorKey:@"#C7" fontKey:@"#T7"];
//    self.pointInfoLabelWidth = ceil([_pointInfoAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
}

-(void)setIsSwitchOpen:(BOOL)isSwitchOpen{
    _isSwitchOpen = isSwitchOpen;
    [self.commonModel.commonData setObject:@(isSwitchOpen).stringValue forKey:@"depositSwitchOpen"];
}
@end
