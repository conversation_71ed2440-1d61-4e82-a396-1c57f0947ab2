//
//  JDISVSettlementShopFloorModule.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
#import "CHECKDetailModel.h"

NS_ASSUME_NONNULL_BEGIN

@class RACSignal;

@interface JDISVSettlementShopFloorModule : NSObject<JDISVFloorModuleProtocol>
/// 显示的商品
@property (nonatomic, copy) NSArray <CHECKVenderModel *> *venderArr;

@property (nonatomic, copy) NSArray *sectionViewModels;
@property (nonatomic, assign) NSInteger sourceType;
@property (nonatomic) BOOL storeMapFeature; // 地图配置开关，默认为NO
- (void)updateRemarkInfo:(NSDictionary *)remarkInfo forVender:(NSString *)venderId;

// 保存配送方法
- (RACSignal *)loadSaveShipmentSignalWith:(NSDictionary *)param;
@end

NS_ASSUME_NONNULL_END
