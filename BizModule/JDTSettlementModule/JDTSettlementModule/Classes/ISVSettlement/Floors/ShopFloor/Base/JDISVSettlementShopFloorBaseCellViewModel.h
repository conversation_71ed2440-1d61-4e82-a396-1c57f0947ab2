//
//  JDISVSettlementShopFloorBaseCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import <Foundation/Foundation.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVImageModule/JDISVImageModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import <JDISVCategoryModule/UIView+JDCDCorners.h>
#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSUInteger, JDISVSettlementShopFloorBaseCellViewModelRegisterType) {
    JDISVSettlementShopFloorBaseCellViewModelRegisterTypeXib, /**< Xib绘制*/
    JDISVSettlementShopFloorBaseCellViewModelRegisterTypeClass, /**< Class代码绘制*/
    JDISVSettlementShopFloorBaseCellViewModelRegisterTypeStoryBoard /**< StoryBoard代码绘制*/
};
@interface JDISVSettlementShopFloorBaseCellViewModel : NSObject
@property (nonatomic, assign) JDISVSettlementShopFloorBaseCellViewModelRegisterType registerType;
@property (nonatomic, assign) CGFloat height;
@property (nonatomic, copy) NSString *cellIdentifier;
@property (nonatomic, copy) NSString *cellName;

- (void)updateWithData:(id __nullable)data forType:(NSInteger)type;
@end

NS_ASSUME_NONNULL_END
