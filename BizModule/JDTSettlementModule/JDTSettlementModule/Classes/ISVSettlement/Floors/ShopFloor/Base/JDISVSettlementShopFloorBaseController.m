//
//  JDISVSettlementShopFloorBaseController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorBaseController.h"

#import "JDISVSettlementShopFloorBaseViewModel.h"
#import "JDISVSettlementShopFloorBaseCell.h"
#import "JDISVSettlementShopFloorBaseCellViewModel.h"
#import "JDISVSettlementShopFloorCellActionParam.h"

@interface JDISVSettlementShopFloorBaseController ()<UITableViewDelegate, UITableViewDataSource, JDISVSettlementShopFloorBaseControllerDelegate, JDISVSettlementShopFloorBaseControllerDataSource, JDISVSettlementShopFloorBaseControllerNavigationDelegate>
@property (nonatomic, strong, readwrite) UITableView *tableView;
@property (nonatomic, weak) id<JDISVSettlementShopFloorBaseControllerDelegate> delegate;
@property (nonatomic, weak) id<JDISVSettlementShopFloorBaseControllerDataSource> dataSource;
@property (nonatomic, weak) id <JDISVSettlementShopFloorBaseControllerNavigationDelegate> navigationDelegate;

@property (nonatomic, strong) JDISVSettlementShopFloorBaseViewModel *viewModel;
// 导航栏
@property (nonatomic, strong) KANavigationBar *controllerNavigationBar;
@property (nonatomic, strong) KANavigationBarBackgrounItem *navigationBackgroundItem; /**< 导航栏背景Item */
@end

@implementation JDISVSettlementShopFloorBaseController

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        _delegate = self;
        _dataSource = self;
        _navigationDelegate = self;
    }
    return self;
}

- (instancetype)init;
{
    self = [super init];
    if (self) {
        _delegate = self;
        _dataSource = self;
        _navigationDelegate = self;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupMainControllerView];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    
    if (!CGRectEqualToRect([self frameOfTableView], CGRectNull)) {
        self.tableView.frame = [self frameOfTableView];
    }
}

#pragma mark - setup UI
- (void)setupMainControllerView {
    [self setupNavigationView];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    [self initializeTableView];
}

- (void)setupNavigationView {
    self.controllerNavigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:self.controllerNavigationBar];
    
    NSString *title = SettlementLan(@"title") ;
    if (self.navigationDelegate) {
        title = [self.navigationDelegate titleOfNavigationView];
    }
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    self.controllerNavigationBar
    .decorator.title(title, NSTextAlignmentCenter)
    .backgroundColor()
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (self.navigationDelegate && [self.navigationDelegate respondsToSelector:@selector(navigationBackButtonAction:)]) {
            [self.navigationDelegate navigationBackButtonAction:sender];
        } else {
            [strongSelf.navigationController popViewControllerAnimated:YES];
        }
        
    }) //返回按钮
    .render();
//    self.navigationBackgroundItem = [[KANavigationBarBackgrounItem alloc] init];
//    //self.navigationBackgroundItem.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
//
//    [self.controllerNavigationBar pushNavigationBarItem:self.navigationBackgroundItem];
    [self.navigationController setNavigationBarHidden:YES animated:YES];
}

// MARK:TableView配置
- (void)initializeTableView {
    if (self.dataSource && [self.dataSource respondsToSelector:@selector(mainTableView)]) {
        self.tableView = [self.dataSource mainTableView];
    } else {
        self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        [self.view addSubview:_tableView];
    }
    
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // Group样式的TableView Header和Footer会有空白
    self.tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];

    if (!CGRectEqualToRect([self frameOfTableView], CGRectNull)) {
        self.tableView.frame = [self frameOfTableView];
    }
    
    
    [self customSetupTableView:self.tableView];

}

/// 返回TableView的Frame
- (CGRect)frameOfTableView {
    //
    CGFloat safeBottom = 0.f;
    CGFloat safeTop = [UIWindow ka_uikit_navigationHeight];
    if (@available(iOS 11.0, *)) {
        CGFloat bottom = [UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom;

        if (bottom > 0) {
            safeBottom = bottom;
        }
    }
    
    CGRect tableFrame = CGRectMake(0, safeTop, CGRectGetWidth([[UIScreen mainScreen] bounds]), CGRectGetHeight([[UIScreen mainScreen] bounds]) - safeTop - safeBottom);
    
    return tableFrame;
}

// 自定义设置TableView
- (void)customSetupTableView:(UITableView *)tableView {
    [self registerTableViewCellWithClassName:UITableViewCell.class forCellReuseIdentifier:@"UITableViewCell"];
}

#pragma mark - Cell 注册
// MARK:注册Cell
- (void)registerTableViewCellWith:(JDISVSettlementShopFloorBaseCellViewModel *)cellViewModel {
    if (cellViewModel.registerType == JDISVSettlementShopFloorBaseCellViewModelRegisterTypeXib) {
        [self registerTableViewCellClassWithNibName:cellViewModel.cellName bundle:[self.dataSource moduleBundle] forCellReuseIdentifier:cellViewModel.cellIdentifier];
    } else if (cellViewModel.registerType == JDISVSettlementShopFloorBaseCellViewModelRegisterTypeClass) {
        [self registerTableViewCellWithClassName:NSClassFromString(cellViewModel.cellName) forCellReuseIdentifier:cellViewModel.cellIdentifier];
    } else {
        return;
    }
}

- (void)registerTableViewCellWithClassName:(nonnull Class)aClass forCellReuseIdentifier:(nonnull NSString *)identifier {
    [self.tableView registerClass:aClass forCellReuseIdentifier:identifier];
}

- (void)registerTableViewCellClassWithNibName:(nonnull NSString *)name bundle:(nonnull NSBundle *)bundle forCellReuseIdentifier:(NSString *)identifier {
    [self.tableView registerNib:[UINib nibWithNibName:name bundle:bundle] forCellReuseIdentifier: identifier];
}

- (void)reloadTableView {
    [self.tableView reloadData];
}

- (CGFloat)tableViewContentOffsetY {
    if (self.tableView == nil) {
        return 0;
    }
    return self.tableView.contentOffset.y;
}

#pragma mark - Table DataSource && Delegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.viewModel == nil || self.viewModel.sectionViewModels == nil) {
        return  0;
    }
    
    if (self.viewModel.sectionViewModels > 0) {
        return  self.viewModel.sectionViewModels.count;
    } else {
        return  0;
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:section];
        if (cellViewModels.count > 0) {
            return cellViewModels.count;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.viewModel!=nil && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementShopFloorBaseCellViewModel *cellViewModel = [cellViewModels objectAtIndex:indexPath.row];
            return cellViewModel.height;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(mainTableView:heightForHeaderInSection:)]) {
        return [self.delegate mainTableView:tableView heightForHeaderInSection:section];
    }
    return 0.01f;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(mainTableView:heightForFooterInSection:)]) {
        return [self.delegate mainTableView:tableView heightForFooterInSection:section];
    }
    return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(mainTableView:viewForHeaderInSection:)]) {
        return [self.delegate mainTableView:tableView viewForHeaderInSection:section];
    }
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.01f)];
    header.backgroundColor = [UIColor clearColor];
    return header;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(mainTableView:viewForFooterInSection:)]) {
        return [self.delegate mainTableView:tableView viewForFooterInSection:section];
    }
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.01f)];
    footer.backgroundColor = [UIColor clearColor];
    return footer;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementShopFloorBaseCell *cell = nil;
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementShopFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            [self registerTableViewCellWith:itemViewModel];
            cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.cellIdentifier forIndexPath:indexPath];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
                __weak typeof(self) weakSelf = self;
                [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(JDISVSettlementShopFloorCellActionParam *parames) {
                    __strong typeof(weakSelf) strongSelf = weakSelf;
                    [strongSelf.delegate processCellActionWithParames:parames];
                }];
            }
        }
        
    }
    
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementShopFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            if ([cell respondsToSelector:@selector(updateCellWithViewModel:)]) {
                [cell performSelector:@selector(updateCellWithViewModel:) withObject:itemViewModel];
            }
        }
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(clickCellIndexOf:)]) {
        [self.delegate clickCellIndexOf:indexPath];
    }
    
    
}

#pragma mark - ScrollView
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if ([self.delegate respondsToSelector:@selector(mainTableViewDidScroll:)]) {
        [self.delegate mainTableViewDidScroll:scrollView];
    }
}
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    if ([self.delegate respondsToSelector:@selector(mainTableViewWillBeginDragging:)]) {
        [self.delegate mainTableViewWillBeginDragging:scrollView];
    }
}

- (JDISVSettlementShopFloorBaseViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [self.dataSource controllerViewModel];
    }
    
    return _viewModel;
}
@end
