//
//  JDISVSettlementShopFloorCellActionParam.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import "JDISVSettlementShopFloorCellActionParam.h"
NSString * const KASettlementShopFloorActionTypeKey = @"KASettlementShopFloorActionTypeKey";
NSString * const KASettlementShopFloorActionDataKey = @"KASettlementShopFloorActionDataKey";
@implementation JDISVSettlementShopFloorCellActionParam
+ (instancetype)initWithIdentifier:(NSString *)identifier parames:(NSDictionary *)actionParams {
    
    JDISVSettlementShopFloorCellActionParam *model = [[JDISVSettlementShopFloorCellActionParam alloc] init];
    model.cellIdentifier = identifier;
    model.actionParams = actionParams;
    
    return model;
}
@end
