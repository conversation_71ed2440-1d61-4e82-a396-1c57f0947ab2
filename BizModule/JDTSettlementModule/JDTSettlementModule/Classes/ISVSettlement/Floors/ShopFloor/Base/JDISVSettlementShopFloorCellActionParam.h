//
//  JDISVSettlementShopFloorCellActionParam.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
FOUNDATION_EXPORT NSString * const KASettlementShopFloorActionTypeKey;
FOUNDATION_EXPORT NSString * const KASettlementShopFloorActionDataKey;
@interface JDISVSettlementShopFloorCellActionParam : NSObject
@property (nonatomic, copy) NSString *cellIdentifier;
@property (nonatomic, copy) NSDictionary *actionParams;

+ (instancetype)initWithIdentifier:(NSString * _Nonnull)identifier parames:(NSDictionary * _Nullable)actionParams;
@end

NS_ASSUME_NONNULL_END
