//
//  JDISVProcessFloorCellViewModel.h
//  JDISVSettlementModule
//
//  Created by ext.songjian6 on 2022/8/16.
//

#import "JDISVSettlementShopFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVProcellModel : NSObject
//"C-M#abstractPresaleFloor&core"
@property (nonatomic,copy) NSString *payStepType;//1(预售支付步骤类型)
@property (nonatomic,copy) NSString *stage;//0(预售阶段)
@property (nonatomic,copy) NSString *factPayType;// "1",
@property (nonatomic,copy) NSString *factEarnest;//
@property (nonatomic,copy) NSString *deductionAmount;//定金可抵扣金额
@property (nonatomic,copy) NSString *factEndPayment;//(应付尾款)
@property (nonatomic,copy) NSString *earnestAfterCoupon;//(优惠后定金)
@property (nonatomic,copy) NSString *presaleFreight;// (预售运费)
@property (nonatomic,copy) NSString *endPaymentBeginTime;// 预售规定尾款开始时间
@property (nonatomic,copy) NSString *endPaymentEndTime;//预售规定尾款结束时间
@property (nonatomic,copy) NSString *endPaymentBeginTimeFormat;// 预售规定尾款开始时间
@property (nonatomic,copy) NSString *endPaymentEndTimeFormat;//预售规定尾款结束时间
@property (nonatomic,assign) BOOL presaleFlag;//预售标识
@property (nonatomic,copy) NSString *earnest;//定金, 付尾款结算时，结算中台接口会下发
@end


@interface JDISVProcessFloorCellViewModel : JDISVSettlementShopFloorBaseCellViewModel
@property (nonatomic,copy) NSString *depositAmount;//定金
@property (nonatomic,copy) NSString *payStepType;//1(预售支付步骤类型)
@property (nonatomic,copy) NSString *stage;//0(预售阶段)
@property (nonatomic,copy) NSString *factPayType;// "1",
@property (nonatomic,copy) NSString *factEarnest;//
@property (nonatomic,copy) NSString *deductionAmount;//定金可抵扣金额
@property (nonatomic,copy) NSString *factEndPayment;//(应付尾款)
@property (nonatomic,copy) NSString *earnestAfterCoupon;//(优惠后定金)
@property (nonatomic,copy) NSString *presaleFreight;// (预售运费)
@property (nonatomic,copy) NSString *endPaymentBeginTime;// 预售规定尾款开始时间
@property (nonatomic,copy) NSString *endPaymentEndTime;//预售规定尾款结束时间
@property (nonatomic,copy) NSString *endPaymentBeginTimeFormat;// 预售规定尾款开始时间
@property (nonatomic,copy) NSString *endPaymentEndTimeFormat;//预售规定尾款结束时间
@property (nonatomic,assign) BOOL presaleFlag;//预售标识

@property (nonatomic, copy) NSString *timeSinceWithEndStr;

@property (nonatomic, copy) NSMutableAttributedString *topStepAtt;
@property (nonatomic, copy) NSMutableAttributedString *topPriceAtt;
@property (nonatomic, copy) NSMutableAttributedString *topDescAtt;

@property (nonatomic, copy) NSMutableAttributedString *bottomStepAtt;
@property (nonatomic, copy) NSMutableAttributedString *bottomPriceAtt;
@property (nonatomic, copy) NSMutableAttributedString *bottomDescAtt;

/*
 self.topRightLabel.text = [NSString stringWithFormat:@"¥%.2f",[model.depositAmount floatValue]];
 self.topRightBottomLabel.text = [NSString stringWithFormat:SettlementLan(@"checkout_pre_sale_disocunt_prefix") ,[model.deductionAmount floatValue]];
 self.bottomRightLabel.text = [NSString stringWithFormat:@"¥%.2f",[model.factEndPayment floatValue]];
 self.bottomRightBottomLabel.text = model.timeSinceWithEndStr;
 */


- (void)updateWithData:(id __nullable)data allFloorData:(NSDictionary *)depositFloorData;

@end

NS_ASSUME_NONNULL_END
