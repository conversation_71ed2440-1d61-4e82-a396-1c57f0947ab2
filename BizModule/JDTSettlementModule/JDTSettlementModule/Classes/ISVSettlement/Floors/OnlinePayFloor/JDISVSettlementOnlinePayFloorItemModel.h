//
//  JDISVSettlementOnlinePayFloorItemModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:通用金额模型
 模型命名空间:core.trade-FLR#balance.productAmount-M#defaultProductAmountFloor
 模型uuid:defaultProductAmountFloor
 */

@interface JDISVSettlementOnlinePayFloorItemModel : NSObject
// C-M#productAmountFloor&basic
@property (nonatomic, assign) BOOL mixPayment;
@property (nonatomic, copy) NSString *paymentName;
@property (nonatomic, assign) BOOL selected;
@property (nonatomic, assign) BOOL supported;
@property (nonatomic, copy) NSString *paymentId;

@end

NS_ASSUME_NONNULL_END
