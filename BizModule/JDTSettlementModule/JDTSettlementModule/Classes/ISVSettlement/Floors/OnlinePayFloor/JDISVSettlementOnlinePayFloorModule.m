//
//  JDISVSettlementOnlinePayFloorModule.m
//  JDISVSettlementModule-JDISVSettlementModule
//
//  Created by shiliang<PERSON> on 2022/10/28.
//

#import "JDISVSettlementOnlinePayFloorModule.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "NSBundle+JDISVSettlement.h"
#import "JDISVSettlementOnlinePayFloorModel.h"
#import "JDISVSettlementOnlinePayFloorItemModel.h"

static NSString * const kJDISVSettmentOnlinePayFloorPrefix = @"defaultPaymentFloor";

JDISVRegisterFloorModule(KaCheckOnlinePayFloor, JDISVSettlementOnlinePayFloorModule)



@interface JDISVSettlementOnlinePayFloorModule()
@property (nonatomic, strong) JDISVSettlementOnlinePayFloorItemModel *onlinePayItemModel;
@end

@implementation JDISVSettlementOnlinePayFloorModule

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.height = 56;
        
    }
    return self;
}

- (UIView *)floorView {
    UIView *v = [[NSBundle jdisvSettlement_bundle] loadNibNamed:@"JDISVSettlementOnlinePayFloor" owner:nil options:nil].firstObject;
    return v;
}

- (CGFloat)floorHeight {
    return self.height;
}

- (JDISVFloorType)floorType{
    return JDISVFloorTypeScrollFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];

//     转楼层模型
    NSMutableArray *floorsModels = [NSMutableArray array];
    for (NSDictionary *floorData in floorsArray) {
        JDISVSettlementOnlinePayFloorModel *floorModel = [JDISVSettlementOnlinePayFloorModel yy_modelWithDictionary:floorData];
        [floorsModels addObject:floorModel];
    }

    [self updateViewModelWith:floorsModels];
}

- (void)updateViewModelWith:(NSArray *)floorDatas {
    if (floorDatas == nil) {
        self.onlinePayItemModel = nil;
    }
    for (JDISVSettlementOnlinePayFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:kJDISVSettmentOnlinePayFloorPrefix]) {
            JDISVSettlementOnlinePayFloorItemModel *amountItemModel = [JDISVSettlementOnlinePayFloorItemModel yy_modelWithDictionary:floorData.info];
//            if ([amountItemModel.floorType isEqualToString:@"2"]) {
                // type:2 应付金额
                self.onlinePayItemModel = amountItemModel;
                break;
//            }
        }
    }
    
    if (_onlinePayItemModel) {
        NSString *amount = SettlementLan(@"checkout_pay_online") ;
        if ([_onlinePayItemModel.paymentName jdcd_validateString]) {
            amount = [_onlinePayItemModel.paymentName copy];
        }
        NSAttributedString *priceAttributedString = [[NSAttributedString alloc] initWithString:amount attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        self.amountAttributedString = priceAttributedString;
        
        self.height = 56;
    } else {
        self.height = 0;
        self.amountAttributedString = nil;
    }
}

@end
