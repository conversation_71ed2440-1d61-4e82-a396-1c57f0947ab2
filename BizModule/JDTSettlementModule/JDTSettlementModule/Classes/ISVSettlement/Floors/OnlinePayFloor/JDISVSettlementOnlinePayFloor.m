//
//  JDISVSettlementOnlinePayFloor.m
//  JDISVSettlementModule
//
//  Created by shiliang6 on 2022/10/28.
//

#import "JDISVSettlementOnlinePayFloor.h"
#import "JDISVSettlementOnlinePayFloorModule.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "UIImage+JDISVSettlement.h"

@interface JDISVSettlementOnlinePayFloor ()<JDCDISVActionTransferProtocol>

@property (weak, nonatomic) IBOutlet UILabel *payTitleLabel;
@property (weak, nonatomic) IBOutlet UIButton *infoBtn;

@property (nonatomic, strong) JDISVSettlementOnlinePayFloorModule *viewModel;

@end


@implementation JDISVSettlementOnlinePayFloor

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];

    }
    return self;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self updateWithViewModel:_viewModel];
}

- (void)updateWithViewModel:(JDISVSettlementOnlinePayFloorModule *)viewModel {
    if (viewModel.amountAttributedString) {
        self.payTitleLabel.attributedText = viewModel.amountAttributedString;
    }
//    else {
//
//    }

    [self.infoBtn setImage:[UIImage jdisvSettlement_imageNamed:@"jdka_settlement_onlinePayInfo"] forState:UIControlStateNormal];
    [self.infoBtn addTarget:self action:@selector(infoAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)infoAction{
    [KAAlert alert].config
    .renderW4(SettlementLan(@"setllement_pay_type_illustrate") , SettlementLan(@"setllement_pay_type_illustrate_des") )
    .addFillAction(SettlementLan(@"setllement_i_known") , ^{
        // 收回
    })
    .alertShow();
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
