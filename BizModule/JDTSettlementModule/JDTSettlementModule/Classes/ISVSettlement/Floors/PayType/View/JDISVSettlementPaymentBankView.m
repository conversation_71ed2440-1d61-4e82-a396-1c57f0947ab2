//
//  JDISVSettlementPaymentBankView.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu12 on 2023/5/31.
//

#import "JDISVSettlementPaymentBankView.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVSettlementNetService.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>


@interface JDISVSettlementPaymentBankView ()<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>

@property (strong,nonatomic) UICollectionView *collectionView;
@property (assign,nonatomic) CGFloat itemHeight;

@end


@implementation JDISVSettlementPaymentBankView

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self){
        self.backgroundColor = [UIColor clearColor];
        self.clipsToBounds = YES;
        [self addSubview:self.collectionView];
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.bottom.mas_equalTo(self);
        }];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    self.itemHeight = self.frame.size.height;
}

- (void)setDataList:(NSArray<JDISVSettlementPaymentBankListModel *> *)dataList{
    _dataList = dataList;
//    if (_dataList && _dataList.count > 0){
//        [self loadPayTypeData];
//    }
    [self.collectionView reloadData];
}


- (UICollectionView *)collectionView{
    if (!_collectionView){
        KARTLCollectionViewFlowLayout *layout = [[KARTLCollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
        layout.minimumInteritemSpacing = 8.f;
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.dataSource = self;
        _collectionView.delegate = self;
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.userInteractionEnabled = YES;
        [_collectionView registerClass:[JDISVSettlementPaymentBankViewCell class] forCellWithReuseIdentifier:@"JDISVSettlementPaymentBankViewCell"];
    }
    return _collectionView;
}

#pragma mark - <UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.dataList ? self.dataList.count : 0;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    __block JDISVSettlementPaymentBankViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JDISVSettlementPaymentBankViewCell" forIndexPath:indexPath];
    __block JDISVSettlementPaymentBankListModel *model = self.dataList[indexPath.row];
    JDWeakSelf
    //    NSString *str = @"https://imgdownload.zode.sa/da/jfsksalydpro-000-da/t1/************/2023/***********/2870/64f69fbdE03c9f0a7/f6d7c5346633ac4e.png.webp";
    //    str = @"https://p.upyun.com/demo/webp/webp/jpg-0.webp";
    [cell.contentImg jdcd_setImage:[PlatformService getCompleteImageUrl:model.logoUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit completion:^(NSError * _Nullable error) {
        JDStrongSelf
        if (model.contentImg){
            return;
        }
        model.contentImg = cell.contentImg.image ? cell.contentImg.image : [PlatformService getCompleteImageUrl:model.logoUrl moduleType:@""];
        model.itemWidth =  model.contentImg.size.width*self.itemHeight/model.contentImg.size.height;
        [collectionView reloadItemsAtIndexPaths:@[indexPath]];
    }];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.row < self.dataList.count){
        JDISVSettlementPaymentBankListModel *model = self.dataList[indexPath.row];
        if (model.itemWidth > 0){
            return CGSizeMake(model.itemWidth, self.itemHeight);
        }else{
            return CGSizeMake(20, self.itemHeight);
        }
    }else{
        return CGSizeMake(20, self.itemHeight);
    }
}
@end



@implementation JDISVSettlementPaymentBankViewCell

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self){
        self.backgroundColor = [UIColor clearColor];
        [self.contentView addSubview:self.contentImg];
        [self.contentImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.bottom.trailing.mas_equalTo(self.contentView);
        }];
        self.clipsToBounds = YES;
    }
    return self;
}

- (UIImageView *)contentImg {
    if (!_contentImg){
        _contentImg = [[UIImageView alloc] init];
        _contentImg.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _contentImg;
}

@end
