//
//  JDISVSettlmentPaymentErrorCell.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu12 on 2023/5/31.
//

#import "JDISVSettlmentPaymentErrorCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

@interface JDISVSettlmentPaymentErrorCell ()
@property (weak, nonatomic) IBOutlet UIImageView *productImg;
@property (weak, nonatomic) IBOutlet UILabel *productname;
@property (weak, nonatomic) IBOutlet UILabel *qtyLabel;

@end

@implementation JDISVSettlmentPaymentErrorCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.productImg.clipsToBounds = YES;
    self.productImg.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    
    self.productname.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.productname.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    
    self.qtyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.qtyLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.qtyLabel.hidden =YES;
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)configData:(JDISVSettlementPayTypePartListItemInfoModel *)dataModel{
    if (dataModel){
        self.productname.text = dataModel.skuName;
        [self.productImg jdcd_setImage:[PlatformService getCompleteImageUrl:dataModel.skuImgUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    }
}

@end
