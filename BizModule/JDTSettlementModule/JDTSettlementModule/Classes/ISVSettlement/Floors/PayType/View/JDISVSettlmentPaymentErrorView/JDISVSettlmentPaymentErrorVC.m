//
//  JDISVSettlmentPaymentErrorVC.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/5/31.
//

#import "JDISVSettlmentPaymentErrorVC.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "UIImage+JDISVSettlement.h"
#import "JDISVSettlmentPaymentErrorCell.h"
#import <JDISVCategoryModule/NSString+JDCDLang.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

static CGFloat CELLHEIGHT = 82.f;
@interface JDISVSettlmentPaymentErrorVC ()<UITableViewDelegate,UITableViewDataSource>

@property (strong,nonatomic) UIView *bgView;
@property (strong,nonatomic) UILabel *titleLabel;
@property (strong,nonatomic) UIButton *closeBtn;
@property (strong,nonatomic) UITableView *tableView;

@end

@implementation JDISVSettlmentPaymentErrorVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self setupUI];
    [self updateUI];
}

- (void)setDataList:(NSArray *)dataList{
    _dataList = dataList;
    [self.tableView reloadData];
    [self updateUI];
}

- (void)setupUI{
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.65];
    [self.view addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view);
        make.width.mas_offset([UIScreen mainScreen].bounds.size.width - 84);
    }];
    
    
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.closeBtn];
    [self.bgView addSubview:self.tableView];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.bgView).mas_offset(18);
        make.leading.mas_equalTo(self.bgView).mas_offset(39);
        make.trailing.mas_equalTo(self.bgView).mas_offset(-39);
        
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.trailing.mas_equalTo(self.bgView);
        make.width.height.mas_offset(30);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(18);
        make.leading.mas_equalTo(self.bgView).mas_offset(24);
        make.trailing.mas_equalTo(self.bgView).mas_offset(-24);
        make.height.mas_offset(0);
        make.bottom.mas_equalTo(self.bgView).mas_offset(-6);
    }];
}

- (void)updateUI{
    NSInteger count = (self.dataList ? self.dataList.count : 0);
    CGFloat height = count * CELLHEIGHT;
    if (height > ([UIScreen mainScreen].bounds.size.height * 0.75)){
        height = ([UIScreen mainScreen].bounds.size.height * 0.75);
        self.tableView.scrollEnabled = YES;
    }else{
        self.tableView.scrollEnabled = NO;
    }
    [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_offset(height);
    }];
    self.titleLabel.text = SettlementLan(@"checkout_selttment_payment_error2");
}


#pragma mark - <action>

- (void)clostbtnPressed:(UIButton *)sender{
    [self dismissViewControllerAnimated:NO completion:nil];
}

#pragma mark - <UITableViewDelegate,UITableViewDataSource>

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.dataList ? self.dataList.count : 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return CELLHEIGHT;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVSettlmentPaymentErrorCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVSettlmentPaymentErrorCell"];
    [cell configData:self.dataList[indexPath.row]];
    return cell;
}


#pragma mark - <getter>
- (UIView *)bgView{
    if (!_bgView){
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _bgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R62"];
        _bgView.clipsToBounds = YES;
    }
    return _bgView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel){
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.numberOfLines = 0;
    }
    return _titleLabel;
}

- (UIButton *)closeBtn{
    if (!_closeBtn){
        _closeBtn = [[UIButton alloc] init];
        [_closeBtn setImage:[UIImage jdisvSettlement_imageNamed:@"Subtractchecout_requriement_close"] forState:UIControlStateNormal];
        [_closeBtn setImage:[UIImage jdisvSettlement_imageNamed:@"Subtractchecout_requriement_close"] forState:UIControlStateSelected];
        [_closeBtn setImage:[UIImage jdisvSettlement_imageNamed:@"Subtractchecout_requriement_close"] forState:UIControlStateHighlighted];
        [_closeBtn addTarget:self action:@selector(clostbtnPressed:) forControlEvents:UIControlEventTouchUpInside];
        _closeBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        _closeBtn.contentEdgeInsets = UIEdgeInsetsMake(14, 0, 0, 14);
        if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
            _closeBtn.contentEdgeInsets = UIEdgeInsetsMake(14, 14, 0, 0);
        }
    }
    return _closeBtn;
}


- (UITableView *)tableView{
    if (!_tableView){
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVSettlmentPaymentErrorCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlmentPaymentErrorCell"];
        _tableView.backgroundColor = self.bgView.backgroundColor;
        _tableView.scrollEnabled = NO;
        _tableView.clipsToBounds = YES;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableView;
}

@end
