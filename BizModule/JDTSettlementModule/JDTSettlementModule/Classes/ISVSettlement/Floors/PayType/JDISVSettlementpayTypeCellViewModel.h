//
//  JDISVSettlementpayTypeCellViewModel.h
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/7/4.
//

#import <Foundation/Foundation.h>
#import "JDISVSettlementPayTypeModel.h"
#import "CHECKDetailModel.h"
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementpayTypeCellViewModel : NSObject

@property (strong,nonatomic) NSAttributedString *onlinePaymentAttributed;///标题文本
@property (strong,nonatomic) UIImage *onlineStatusImg;///选中状态图片
@property (strong,nonatomic) NSArray *bankIcons;///银行图标集合
@property (strong,nonatomic) UIImage *cashOrDeliveryNotAvailable;///COD不可用图标
@property (assign,nonatomic) CGFloat bankViewWidth;

@property (assign,nonatomic) BOOL needReloadBanks;//是否需要获取银行信息

@property (strong,nonatomic) JDISVSettlementPayTypePartListItemModel *dataModel;

@property (nonatomic, strong) CHECKPayTypeModel *payTypeModel;

//+ (RACSignal *)reloadBankListData;

@end

NS_ASSUME_NONNULL_END
