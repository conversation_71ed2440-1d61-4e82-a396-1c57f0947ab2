//
//  JDISVSettlementPayTypeModel.h
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chen<PERSON>yu<PERSON> on 2023/5/31.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementPaymentBankListModel;
@class JDISVSettlementPayTypeNameSpaceModel;
@class JDISVSettlementPayTypePartListItemModel;
@class JDISVSettlementPayTypeInfoModel;
@class JDISVSettlementPayTypePartListItemInfoModel;


@interface JDISVSettlementPayTypeModel : NSObject

@property (strong,nonatomic) JDISVSettlementPayTypeNameSpaceModel *type;
@property (strong,nonatomic) NSString *uuid;
@property (strong,nonatomic) NSArray *partList;
@property (strong,nonatomic) JDISVSettlementPayTypeInfoModel *info;


@end

@interface JDISVSettlementPayTypeNameSpaceModel : NSObject

@property (strong,nonatomic) NSString *namespace;
@property (strong,nonatomic) NSString *version;

@end


@interface JDISVSettlementPayTypePartListItemModel : NSObject

@property (strong,nonatomic) JDISVSettlementPayTypeNameSpaceModel *type;
@property (strong,nonatomic) JDISVSettlementPayTypePartListItemInfoModel *info;
@end

@interface JDISVSettlementPayTypePartListItemInfoModel : NSObject

//C-P#paymentType&paymentType
@property (assign,nonatomic) NSInteger paymentTypeId;
@property (strong,nonatomic) NSString *unAvailableToast;
@property (strong,nonatomic) NSString *paymentTypeName;
@property (assign,nonatomic) BOOL selected;
@property (assign,nonatomic) BOOL supported;
//C-P#showSku&showSku
@property (strong,nonatomic) NSString *skuName;
@property (strong,nonatomic) NSString *skuId;
@property (strong,nonatomic) NSString *skuImgUrl;

@end

@interface JDISVSettlementPayTypeInfoModel : NSObject
//C-M#paymentInfoFloor&tip.codDocument
@property (strong,nonatomic) NSString *codDocument;

@end



@interface JDISVSettlementPaymentBankListModel :NSObject


@property (assign,nonatomic) NSInteger payMethod;
@property (strong,nonatomic) NSString *desc;
@property (strong,nonatomic) NSString *logoUrl;

@property (strong,nonatomic) UIImage *contentImg;
@property (assign,nonatomic) CGFloat itemWidth;

@end

NS_ASSUME_NONNULL_END
