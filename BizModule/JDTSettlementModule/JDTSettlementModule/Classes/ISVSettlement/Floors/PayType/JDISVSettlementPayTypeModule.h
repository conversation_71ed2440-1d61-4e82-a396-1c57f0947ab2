//
//  JDISVSettlementPayTypeModule.h
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/5/31.
//

#import <Foundation/Foundation.h>
#import "JDISVSettlementPayTypeModel.h"
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

typedef NS_ENUM(NSInteger,JDISVSettlementPayType) {
    JDISVSettlementPayTypeOnline = 4,///线上支付
    JDISVSettlementPayTypeCashOrDelivery = 1,///线下支付
};

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementPayTypeModule : NSObject<JDISVFloorModuleProtocol>

@property (strong,nonatomic) JDISVSettlementPayTypeModel *dataModel;

@property (strong,nonatomic) NSAttributedString *stepNumberAttributed;//步骤数字
@property (strong,nonatomic) NSAttributedString *stepTitleAttributed;///步骤文本

@property (assign,nonatomic) JDISVSettlementPayType currentPayType;///当前选中的支付方式
@property (assign,nonatomic) BOOL cashOrDeliverIsAvailable;///线下支付是否可用
@property (assign,nonatomic) CGFloat bankViewWidth;///银行卡icon展示宽度


@property (assign,nonatomic) BOOL hiddenCOD;///是否隐藏COD 默认NO， 方可结算页不展示手动设置为YES

@property (strong,nonatomic) NSArray *payTypeList;//支付方式列表
@property (strong,nonatomic) NSArray *unAvailableToastProList;//不可Cod商品列表 可能为空
@property (strong,nonatomic) NSString *unAvailableToast;//不可COD原因
@property (strong,nonatomic) RACSubject *signal;
@property (strong,nonatomic) NSDictionary *needChangedPaymentType;


@end

NS_ASSUME_NONNULL_END
