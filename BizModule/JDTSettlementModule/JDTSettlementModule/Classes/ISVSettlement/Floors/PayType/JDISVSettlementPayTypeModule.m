//
//  JDISVSettlementPayTypeModule.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chen<PERSON>yu<PERSON> on 2023/5/31.
//

#import "JDISVSettlementPayTypeModule.h"
#import "JDISVSettlementPayTypeFloor.h"
#import "NSAttributedString+ISVSettlementShop.h"
#import "UIImage+JDISVSettlement.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import "JDISVSettlementpayTypeCellViewModel.h"
#import "JDISVSettlementAmountFloorNetService.h"
#import "JDISVSettlementpayTypeCellViewModel.h"
#import "CHECKDetailModel.h"

JDISVRegisterFloorModule(KaCheckPaymentFloor, JDISVSettlementPayTypeModule);

@interface JDISVSettlementPayTypeModule ()


@property (assign,nonatomic) CGFloat height;

@end

@implementation JDISVSettlementPayTypeModule

- (JDISVFloorType)floorType{
    return JDISVFloorTypeScrollFloor;
}

- (Class)tableViewFloorClass{
    return JDISVSettlementPayTypeFloor.class;
}

-(BOOL)preLoadFloor{
    return YES;
}

- (CGFloat)floorHeight {
    
    return _height;
}


- (instancetype)init{
    self = [super init];
    if (self){
        self.stepNumberAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:@"2"  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
        self.stepTitleAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_settlment_payment_title")  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
        self.signal = [RACSubject subject];
    }
    return self;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    CHECKPaymentInfoModel *model = [CHECKPaymentInfoModel yy_modelWithDictionary:data[@"data"][@"paymentInfo"]];
    NSMutableArray *payTypeMArr = [NSMutableArray array];
    for (CHECKPayTypeModel *payModel in model.payTypeList) {
        if ([payModel.paymentTypeId isEqualToString:@"1"]) {
            if (payModel.selected == YES) {
                self.currentPayType = JDISVSettlementPayTypeOnline;
            }
            JDISVSettlementpayTypeCellViewModel *onlinePay = [[JDISVSettlementpayTypeCellViewModel alloc] init];
            onlinePay.onlinePaymentAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_payment_online")  colorKey:@"#C7" fontKey:@"#T7"];
            onlinePay.onlineStatusImg = payModel.selected ? [[UIImage jdisvSettlement_imageNamed:@"settlement_payment_selected"] jdcd_imageWithTintColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] : [UIImage jdisvSettlement_imageNamed:@"settlement_payment_normal"];
            onlinePay.needReloadBanks = YES;
            onlinePay.payTypeModel = payModel;
            [payTypeMArr addObject:onlinePay];
        }
    }
    self.payTypeList = [payTypeMArr copy];
    self.height = 173.f - (56 * 2.f) + 6.f + (self.payTypeList.count * 56.f);
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
    NSArray *floors = [[data jdcd_getDicElementForKey:@"data"] jdcd_getArrayElementForKey:@"floors"];
    
    for (NSDictionary *floor in floors) {
        if ([[floor jdcd_getStringElementForKey:@"uuid"] containsString:@"paymentInfoFloor"]){
            self.dataModel = [JDISVSettlementPayTypeModel yy_modelWithDictionary:floor];
            break;
        }
    }
    
    if (self.dataModel){
        __block BOOL needRestToOnlinePay = NO;
        self.cashOrDeliverIsAvailable = YES;
        NSMutableArray *temp = [[NSMutableArray alloc] init];//存储不可用COD的商品
        NSMutableArray *paytypeList = [[NSMutableArray alloc] init];//存储不可用COD的商品
        for (JDISVSettlementPayTypePartListItemModel *item in self.dataModel.partList) {
            if ([item.type.namespace containsString:@"core.trade-FLR#balance.payment-P#showSku"]){//不可用商品
                [temp addObject:item];
            }else if ([item.type.namespace containsString:@"core.trade-FLR#balance.payment-P#paymentType"]){//支付方式
                if (item.info.paymentTypeId == 4){//在线支付
                    if (item.info.selected){
                        self.currentPayType = JDISVSettlementPayTypeOnline;
                        [JDISVSettlementAmountFloorNetService sharedService].ISCODPayType = NO;
                    }
                    JDISVSettlementpayTypeCellViewModel *onlinePay = [[JDISVSettlementpayTypeCellViewModel alloc] init];

                    onlinePay.onlinePaymentAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_payment_online")  colorKey:@"#C7" fontKey:@"#T7"];
                    
                    onlinePay.onlineStatusImg = item.info.selected ? [[UIImage jdisvSettlement_imageNamed:@"settlement_payment_selected"] jdcd_imageWithTintColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] : [UIImage jdisvSettlement_imageNamed:@"settlement_payment_normal"];

                    onlinePay.needReloadBanks = YES;
                    onlinePay.cashOrDeliveryNotAvailable = nil;
                    onlinePay.dataModel = item;
                    [paytypeList addObject:onlinePay];
                    
                }
                else if (item.info.paymentTypeId == 1){//COD 支付
                    if (item.info.selected){
                        self.currentPayType = JDISVSettlementPayTypeCashOrDelivery;
                        [JDISVSettlementAmountFloorNetService sharedService].ISCODPayType = YES;
                    }
                    self.cashOrDeliverIsAvailable = item.info.supported;//当前COD是否可用
                    self.unAvailableToast = item.info.unAvailableToast;//COD不可用原因
                    
                    if (ISPresell){//预售商品不展示COD
                        [self autoChangedToOnlinepayment];
                        continue;
                    }
                    if (SettlementNetService.isVistor){//访客结算不展示COD
                        [self autoChangedToOnlinepayment];
                        continue;
                    }
                    JDISVSettlementpayTypeCellViewModel *codPay = [[JDISVSettlementpayTypeCellViewModel alloc] init];

                    codPay.onlinePaymentAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_payment_COD")  colorKey:@"#C7" fontKey:@"#T7"];
                    codPay.onlineStatusImg = item.info.selected ? [[UIImage jdisvSettlement_imageNamed:@"settlement_payment_selected"] jdcd_imageWithTintColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]] : [UIImage jdisvSettlement_imageNamed:@"settlement_payment_normal"];
                    if (!self.cashOrDeliverIsAvailable){//线下支付不可用
                        codPay.cashOrDeliveryNotAvailable = [UIImage jdisvSettlement_imageNamed:@"settlement_payment_help"];
                        if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                            codPay.cashOrDeliveryNotAvailable = [[UIImage jdisvSettlement_imageNamed:@"settlement_payment_help"] JDCDRTL];
                        }
                        codPay.onlinePaymentAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_payment_COD")  colorKey:@"#C7" fontKey:@"#T7"];
                        codPay.onlineStatusImg = nil;
                        if (item.info.selected){
                            JDWeakSelf
                            [KAAlert alert].config
                            .renderW3a(SettlementLan(@"checkout_payment_exchanged_to_online_pay"), NSTextAlignmentCenter)
                            .ka_addAction(SettlementLan(@"checkout_pre_sale_dialog_confirm"), ^(UIButton * _Nonnull button) {
                                [button renderB5];
                            }, ^{//切换到在线支付
                                JDStrongSelf
                                [self autoChangedToOnlinepayment];
                            })
                            .jdcd_clickBackgroundClose(NO)
                            .alertShow();
                        }
                    }else{//线下支付可用
                        codPay.cashOrDeliveryNotAvailable = nil;
                        codPay.onlinePaymentAttributed = [[NSAttributedString alloc] ka_settlement_shop_initWithString:SettlementLan(@"checkout_payment_COD")  colorKey:@"#C7" fontKey:@"#T7"];
                    }
                    codPay.dataModel = item;
                    [paytypeList addObject:codPay];
                }
            }
        }
        //页面重置为在线支付
        if (needRestToOnlinePay){
            for (JDISVSettlementpayTypeCellViewModel *item in paytypeList) {
                if (item.dataModel.info.paymentTypeId == 4){//在线支付
                    item.dataModel.info.selected = YES;
                    item.onlineStatusImg = [[UIImage jdisvSettlement_imageNamed:@"settlement_payment_selected"] jdcd_imageWithTintColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
                }else if (item.dataModel.info.paymentTypeId == 1){//COD 支付
                    item.dataModel.info.selected = NO;
                }
            }
        }
        
        self.payTypeList = [paytypeList copy];
        CGFloat OnlinePayHeight = 0;
        for (JDISVSettlementpayTypeCellViewModel* vm in self.payTypeList){
            if(vm.needReloadBanks)
                OnlinePayHeight = 22;
        }
        self.unAvailableToastProList = [temp copy];
        self.height = 173.f - (56 * 2.f) + 6.f + (self.payTypeList.count * 56.f) + OnlinePayHeight;
    }else{
        self.height = 0;
    }
    
    if(self.payTypeList.count>1){
        JDISVSettlementpayTypeCellViewModel* vm =  self.payTypeList[1];
        if(vm.dataModel.info.selected == YES){
            commonModel.commonData[@"isCOD"]= @(1);
        }else{
            commonModel.commonData[@"isCOD"]= @(0);
        }
    }else{
        commonModel.commonData[@"isCOD"]= @(0);
    }
    
}

- (void)autoChangedToOnlinepayment {
    if (self.currentPayType == JDISVSettlementPayTypeOnline) {
        return;
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.signal sendNext:@{@"paymentId":@(JDISVSettlementPayTypeOnline)}];
    });
}

@end
