//
//  JDISVSettlementPayTypeModel.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chen<PERSON><PERSON><PERSON> on 2023/5/31.
//

#import "JDISVSettlementPayTypeModel.h"

@implementation JDISVSettlementPayTypeModel

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"type": @"JDISVSettlementPayTypeNameSpaceModel",
             @"info": @"JDISVSettlementPayTypeInfoModel",
             @"partList":@"JDISVSettlementPayTypePartListItemModel"
    };
}

@end

@implementation JDISVSettlementPayTypeNameSpaceModel


@end


@implementation JDISVSettlementPayTypePartListItemModel

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"type": @"JDISVSettlementPayTypeNameSpaceModel",
             @"info":@"JDISVSettlementPayTypePartListItemInfoModel"
    };
}


@end

@implementation JDISVSettlementPayTypePartListItemInfoModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"paymentTypeId": @"C-P#paymentType&paymentType.paymentTypeId",
        @"unAvailableToast": @"C-P#paymentType&paymentType.unAvailableToast",
        @"paymentTypeName": @"C-P#paymentType&paymentType.paymentTypeName",
        @"selected": @"C-P#paymentType&paymentType.selected",
        @"supported": @"C-P#paymentType&paymentType.supported",
        @"skuName":@"C-P#showSku&showSku.skuName",
        @"skuId":@"C-P#showSku&showSku.skuId",
        @"skuImgUrl":@"C-P#showSku&showSku.skuImgUrl"
    };
}

@end

@implementation JDISVSettlementPayTypeInfoModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"codDocument": @"C-M#paymentInfoFloor&tip.codDocument"
    };
}

@end


@implementation JDISVSettlementPaymentBankListModel

- (void)setContentImg:(UIImage *)contentImg{
    _contentImg = contentImg;
    //   _contentImg.size.width
    //13
//    self.itemWidth = _contentImg.size.width*13.f/_contentImg.size.height;
    
}


@end
