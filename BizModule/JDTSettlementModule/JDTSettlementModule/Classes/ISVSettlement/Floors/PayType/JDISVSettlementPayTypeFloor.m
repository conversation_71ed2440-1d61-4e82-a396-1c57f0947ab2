//
//  JDISVSettlementPayTypeFloor.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chen<PERSON>yu<PERSON> on 2023/5/31.
//

#import "JDISVSettlementPayTypeFloor.h"
#import "JDISVSettlementPayTypeModule.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import "JDISVSettlementPaymentBankView.h"
#import "JDISVSettlmentPaymentErrorVC.h"
#import "JDISVSettlementpayTypeCell.h"
#import "JDISVSettlementpayTypeCellViewModel.h"

@interface JDISVSettlementPayTypeFloor ()<JDCDISVActionDelegate,UITableViewDelegate,UITableViewDataSource>

@property (strong,nonatomic) UILabel *stepNumberLabel;
@property (strong,nonatomic) UILabel *stepTitleLabel;

@property (strong,nonatomic) UITableView *tableView;

@property (strong,nonatomic) JDISVSettlementPayTypeModule *viewModel;

@end

@implementation JDISVSettlementPayTypeFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        [self setupUI];
        self.contentView.clipsToBounds = YES;
    }
    return self;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    JDWeakSelf
    [[self.viewModel.signal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        JDStrongSelf
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVPaymentTypeChangedAction"];
        action.value = x;
        [self isv_sendAction:action];
    }];
    self.stepTitleLabel.attributedText = _viewModel.stepTitleAttributed;
    self.stepNumberLabel.attributedText = _viewModel.stepNumberAttributed;
    [self.tableView reloadData];
}


#pragma mark - <JDCDISVActionDelegate>

- (void)action:(JDCDISVAction *)action{
    
}

#pragma mark - <custom logic>

- (void)onlineViewPressed{
//    JDISVSettlementSharedManager.shared.shouldSelectPaymentMethod = YES;
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVPaymentTypeChangedAction"];
    action.value = @{@"paymentId":@(JDISVSettlementPayTypeOnline)};
    [self isv_sendAction:action];
}

- (void)cashViewPressed{
    if (!self.viewModel.cashOrDeliverIsAvailable){///TODO: 错误顺序
        if (self.viewModel.unAvailableToastProList && self.viewModel.unAvailableToastProList.count > 0){//包含不符合COD付款商品
            NSMutableArray *dataArray = [NSMutableArray array];
            for (JDISVSettlementPayTypePartListItemModel *item in self.viewModel.unAvailableToastProList) {
                [dataArray addObject:item.info];
            }
            JDISVSettlmentPaymentErrorVC *vc = [[JDISVSettlmentPaymentErrorVC alloc] init];
            vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
            vc.dataList = dataArray;
            [[PlatformService getTopViewController] presentViewController:vc animated:NO completion:nil];
        }else{
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:self.viewModel.unAvailableToast ? : SettlementLan(@"checkout_selttment_payment_error1")];
        }
    }else{
//        JDISVSettlementSharedManager.shared.shouldSelectPaymentMethod = YES;
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVPaymentTypeChangedAction"];
        action.value = @{@"paymentId":@(JDISVSettlementPayTypeCashOrDelivery)};
        [self isv_sendAction:action];
    }
}

#pragma mark - <UITableViewDelegate,UITableViewDataSource>

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return self.viewModel.payTypeList ? self.viewModel.payTypeList.count : 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVSettlementpayTypeCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVSettlementpayTypeCell"];
    [cell configData:self.viewModel.payTypeList[indexPath.section]];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVSettlementpayTypeCellViewModel* cellViewModel = self.viewModel.payTypeList[indexPath.section];
    if(cellViewModel.needReloadBanks){
        return 44+22;
    }
    return 44;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 12.f;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 0.1f;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    return [[UIView alloc] initWithFrame:CGRectMake(0, 0, 200, 12.f)];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVSettlementpayTypeCellViewModel *model = self.viewModel.payTypeList[indexPath.section];
//    if (model.dataModel.info.paymentTypeId == 1) {//COD
//        [self cashViewPressed];
//    }else if (model.dataModel.info.paymentTypeId == 4){//online
//        [self onlineViewPressed];
//    }
    if ([model.payTypeModel.paymentTypeId isEqualToString:PaymentTypeOnline]) {
        [self onlineViewPressed];
    } else if ([model.payTypeModel.paymentTypeId isEqualToString:PaymentTypeCOD]) {
        [self cashViewPressed];
    }
}

#pragma mark - <getter>
- (UILabel *)stepNumberLabel{
    if (!_stepNumberLabel){
        _stepNumberLabel = [[UILabel alloc] init];
    }
    return _stepNumberLabel;
}

- (UILabel *)stepTitleLabel{
    if (!_stepTitleLabel){
        _stepTitleLabel = [[UILabel alloc] init];
    }
    return _stepTitleLabel;
}

- (UITableView *)tableView{
    if (!_tableView){
        _tableView = [[UITableView alloc] init];
        _tableView.delegate =self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerClass:[JDISVSettlementpayTypeCell class] forCellReuseIdentifier:@"JDISVSettlementpayTypeCell"];
        _tableView.backgroundColor = [UIColor clearColor];
        _tableView.scrollEnabled = NO;
    }
    return _tableView;
}

#pragma mark - <UI布局>
- (void)setupUI{
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self.contentView addSubview:self.stepNumberLabel];
    [self.contentView addSubview:self.stepTitleLabel];
    [self.contentView addSubview:self.tableView];
    
    [self.stepNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).mas_offset(18);
        make.leading.mas_equalTo(self.contentView).mas_offset(18);
        make.height.mas_offset(25.f);
    }];
    
    [self.stepTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.stepNumberLabel);
        make.leading.mas_equalTo(self.stepNumberLabel.mas_trailing).mas_offset(6);
        make.height.mas_equalTo(self.stepNumberLabel);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.stepNumberLabel.mas_bottom).mas_offset(12);
        make.leading.mas_equalTo(self.contentView).mas_offset(18);
        make.trailing.mas_equalTo(self.contentView).mas_offset(-18);
        make.bottom.mas_equalTo(self.contentView).mas_offset(-6);
    }];
}



@end
