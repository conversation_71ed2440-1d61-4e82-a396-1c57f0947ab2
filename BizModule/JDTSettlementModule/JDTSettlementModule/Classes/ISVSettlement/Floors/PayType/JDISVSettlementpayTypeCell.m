//
//  JDISVSettlementpayTypeCell.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/7/4.
//

#import "JDISVSettlementpayTypeCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import "JDISVSettlementPaymentBankView.h"

@interface JDISVSettlementpayTypeCell ()

@property (strong,nonatomic) UILabel *onlieLabel;
@property (strong,nonatomic) JDISVSettlementPaymentBankView *bankImagesView;
@property (strong,nonatomic) UIImageView *cashNotAvailableImg;
@property (strong,nonatomic) UIButton *changedBtn;

@end

@implementation JDISVSettlementpayTypeCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.contentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R4"];
        [self.contentView addSubview:self.onlieLabel];
        [self.contentView addSubview:self.bankImagesView];
        [self.contentView addSubview:self.cashNotAvailableImg];
        [self.contentView addSubview:self.changedBtn];
        
        
        [self.onlieLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(12);
        }];
        
        [self.bankImagesView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.onlieLabel);
            make.leading.mas_equalTo(self.onlieLabel.mas_trailing).mas_offset(8);
            make.trailing.mas_equalTo(self.changedBtn.mas_leading).mas_greaterThanOrEqualTo(@(0));
            make.height.mas_offset(13.f);
        }];
        
        [self.cashNotAvailableImg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.onlieLabel);
            make.leading.mas_equalTo(self.onlieLabel.mas_trailing).mas_offset(6);
            make.width.height.mas_offset(12);
        }];
        
        [self.changedBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.contentView);
            make.trailing.mas_equalTo(self.contentView).mas_offset(-5.5);
            make.width.height.mas_offset(34.f);
        }];
    }
    return self;
}

- (UILabel *)onlieLabel {
    if (!_onlieLabel){
        _onlieLabel = [[UILabel alloc] init];
    }
    return _onlieLabel;
}

- (JDISVSettlementPaymentBankView *)bankImagesView{
    if(!_bankImagesView){
        _bankImagesView = [[JDISVSettlementPaymentBankView alloc] initWithFrame:CGRectZero];
    }
    return _bankImagesView;
}

- (UIImageView *)cashNotAvailableImg{
    if (!_cashNotAvailableImg){
        _cashNotAvailableImg = [[UIImageView alloc] init];
    }
    return _cashNotAvailableImg;
}

- (UIButton *)changedBtn{
    if (!_changedBtn){
        _changedBtn = [[UIButton alloc] init];
        _changedBtn.contentEdgeInsets = UIEdgeInsetsMake(6.5, 6.5, 6.5, 6.5);
        _changedBtn.userInteractionEnabled = NO;
    }
    return _changedBtn;
}

- (void)configData:(JDISVSettlementpayTypeCellViewModel *)viewModel{
    if (viewModel){
        self.onlieLabel.attributedText = viewModel.onlinePaymentAttributed;
        self.cashNotAvailableImg.image = viewModel.cashOrDeliveryNotAvailable;
        self.bankImagesView.hidden = !viewModel.needReloadBanks;
        
//        self.bankImagesView.dataList = viewModel.bankIcons;
//        self.changedBtn.selected = viewModel.dataModel.info.selected;
//        if (JDISVSettlementSharedManager.shared.shouldSelectPaymentMethod) {
            [self.changedBtn setImage:viewModel.onlineStatusImg forState:UIControlStateSelected];
            [self.changedBtn setImage:viewModel.onlineStatusImg forState:UIControlStateNormal];
            [self.changedBtn setImage:viewModel.onlineStatusImg forState:UIControlStateHighlighted];
//        } else {
//            UIImage *onlineStatusImg =  [UIImage jdisvSettlement_imageNamed:@"settlement_payment_normal"];
//            [self.changedBtn setImage:onlineStatusImg forState:UIControlStateSelected];
//            [self.changedBtn setImage:onlineStatusImg forState:UIControlStateNormal];
//            [self.changedBtn setImage:onlineStatusImg forState:UIControlStateHighlighted];
//        }
        
        if (viewModel.dataModel.info.selected) {
            self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.05];
        }else{
            self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        }
        
        if (viewModel.needReloadBanks && (!viewModel.bankIcons || viewModel.bankIcons.count == 0)){
            JDWeakSelf
            [[JDISVSettlementpayTypeCellViewModel reloadBankListData] subscribeNext:^(id  _Nullable x) {
               JDStrongSelf
                viewModel.bankIcons = x;
                self.bankImagesView.dataList = viewModel.bankIcons;
            }];
            [self.onlieLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.contentView).mas_offset(14);
                make.leading.mas_equalTo(self.contentView).mas_offset(12);
            }];
            [self.bankImagesView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.onlieLabel.mas_bottom).mas_offset(8);
                make.leading.mas_equalTo(self.contentView).mas_offset(12);
                make.trailing.mas_equalTo(self.changedBtn.mas_leading).mas_greaterThanOrEqualTo(@(0));
                make.height.mas_offset(13.f);
            }];
        }else if (viewModel.needReloadBanks && viewModel.bankIcons && viewModel.bankIcons.count > 0){
            self.bankImagesView.dataList = viewModel.bankIcons;
        }
        
//        if (!self.bankImagesView.hidden){
//            [self.bankImagesView mas_updateConstraints:^(MASConstraintMaker *make) {
//                make.width.mas_offset(viewModel.bankViewWidth);
//            }];
//        }
    }
}


@end
