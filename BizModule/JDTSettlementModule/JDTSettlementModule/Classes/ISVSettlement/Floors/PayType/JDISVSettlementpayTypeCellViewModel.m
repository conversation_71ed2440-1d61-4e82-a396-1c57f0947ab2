//
//  JDISVSettlementpayTypeCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/7/4.
//

#import "JDISVSettlementpayTypeCellViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "JDISVSettlementNetService.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

@implementation JDISVSettlementpayTypeCellViewModel

+ (RACSignal *)reloadBankListData{
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[JDISVSettlementNetService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"commercepay.payment.queryPayMethods" version:@"" parameters:@{@"type":@"1",@"key":@"2",@"buId":@"405",@"tenantId":@"405"} complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error){
            }else{
                if ([responseObject isKindOfClass:[NSDictionary class]]){
                    if ([responseObject jdcd_getNumberElementForKey:@"success"].boolValue){
                        NSMutableArray *bankList = [NSMutableArray array];
                        for (NSDictionary *item in [responseObject jdcd_getArrayElementForKey:@"data"]) {
                            [bankList addObject:[JDISVSettlementPaymentBankListModel yy_modelWithDictionary:item]];
                        }
                        [subscriber sendNext:bankList];
                        [subscriber sendCompleted];
                    }
                }
            }
        }];
        return nil;
    }];
}

@end
