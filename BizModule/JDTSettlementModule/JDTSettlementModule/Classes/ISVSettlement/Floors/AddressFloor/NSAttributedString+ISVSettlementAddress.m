//
//  NSAttributedString+ISVSettlementAddress.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/9.
//

#import "NSAttributedString+ISVSettlementAddress.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

@implementation NSAttributedString (ISVSettlementAddress)
- (instancetype)ka_settlement_address_initWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey {
    return [[NSAttributedString alloc] initWithString:str attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:fontKey],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:colorKey]
    }];
}
- (instancetype)ka_settlement_address_initWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight{
    return [[NSAttributedString alloc] initWithString:str attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:fontKey weight:weight],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:colorKey]
    }];
}

- (CGFloat)ka_settlement_address_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, height) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey] maxNumberOfLines:1].width);
}

- (CGFloat)ka_settlement_address_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, height) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey weight:weight] maxNumberOfLines:1].width);
}

- (CGFloat)ka_settlement_address_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey] maxNumberOfLines:line].height);
}

- (CGFloat)ka_settlement_address_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey weight:weight] maxNumberOfLines:line].height);
}
@end
