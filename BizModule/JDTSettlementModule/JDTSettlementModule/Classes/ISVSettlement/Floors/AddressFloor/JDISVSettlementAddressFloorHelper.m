//
//  JDISVSettlementAddressFloorHelper.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/8.
//

#import "JDISVSettlementAddressFloorHelper.h"

#import "JDISVSettlementAddressFloorAddressModel.h"
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>

#import <JDISVCategoryModule/NSString+JDCDExtend.h>
//#import <JDBFoundationModule/JDBFoundationModule-umbrella.h>
#include <CommonCrypto/CommonCrypto.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

static NSString * const kJDISVSettlementAddressFloorHelperDecryptKey = @"np!u5chin@adm!n1aaaaaaa2";

@interface JDISVSettlementAddressFloorHelper ()
@property (nonatomic, strong) JDISVSettlementAddressFloorAddressModel *currentConsigneeAddress;
@property (nonatomic, copy) NSString *address; /**< 全地址 */
@property (nonatomic, copy) NSString *levelAddress;
@property (nonatomic, copy) NSString *provinceId;
@property (nonatomic, copy) NSString *cityId;
@property (nonatomic, copy) NSString *areaId;
@property (nonatomic, copy) NSString *townId;

@property (nonatomic, copy) NSString *latitude;
@property (nonatomic, copy) NSString *longitude;

@property (nonatomic, copy) NSNumber *addressId;
@end

@implementation JDISVSettlementAddressFloorHelper
+ (instancetype)sharedService {
    static JDISVSettlementAddressFloorHelper * _sharedService = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedService = [[JDISVSettlementAddressFloorHelper alloc] init];
    });
    
    return _sharedService;
}
- (void)saveSettlementConsigneeAddress:(JDISVSettlementAddressFloorAddressModel *)consigneeAddressModel toCommonModel:(JDISVFloorCommonModel *)commonModel {
    self.currentConsigneeAddress = consigneeAddressModel;
    
    self.addressId = [_currentConsigneeAddress.addressId jdcd_toNumber];
    
    // address
    NSMutableString *areaAddress = [NSMutableString stringWithString:@""];
    if ([_currentConsigneeAddress.areaName.provinceName jdcd_validateString]) {
        [areaAddress appendString:_currentConsigneeAddress.areaName.provinceName];
    }
    if ([_currentConsigneeAddress.areaName.cityName jdcd_validateString] && [_currentConsigneeAddress.areaName.provinceName isEqualToString:_currentConsigneeAddress.areaName.cityName] == NO) {
        [areaAddress appendString:_currentConsigneeAddress.areaName.cityName];
    }
    if ([_currentConsigneeAddress.areaName.areaName jdcd_validateString]) {
        [areaAddress appendString:_currentConsigneeAddress.areaName.areaName];
    }
    if ([_currentConsigneeAddress.areaName.townName jdcd_validateString]) {
        [areaAddress appendString:_currentConsigneeAddress.areaName.townName];
    }
    self.levelAddress = [areaAddress copy];
    
    
    NSString *detailAddress = [self decryptString:_currentConsigneeAddress.encryptedFullAddress];
    if ([detailAddress jdcd_validateString]) {
        self.address = [detailAddress copy];
    } else {
        self.address = @"";
    }
    
    // provinceId
    if (_currentConsigneeAddress.areaId.provinceId) {
        self.provinceId = [_currentConsigneeAddress.areaId.provinceId stringValue];
    } else {
        self.provinceId = @"";
    }
    // cityId
    if (_currentConsigneeAddress.areaId.cityId) {
        self.cityId = [_currentConsigneeAddress.areaId.cityId stringValue];
    } else {
        self.cityId = @"";
    }
    // areaId
    if (_currentConsigneeAddress.areaId.areaId) {
        self.areaId = [_currentConsigneeAddress.areaId.areaId stringValue];
    } else {
        self.areaId = @"";
    }
    // townId
    if (_currentConsigneeAddress.areaId.townId) {
        self.townId = [_currentConsigneeAddress.areaId.townId stringValue];
    } else {
        self.townId = @"";
    }
    
    if ([_currentConsigneeAddress.latitude jdcd_validateString]) {
        _latitude = self.currentConsigneeAddress.latitude;
    }
    
    if ([_currentConsigneeAddress.longitude jdcd_validateString]) {
        _longitude = self.currentConsigneeAddress.longitude;
    }
    [commonModel.commonData addEntriesFromDictionary:[self addressMapInCommonModel]];
}

- (NSString * __nullable)decryptString:(NSString * __nonnull)encryptedStr {
    return [self do3DecEncryptStr:encryptedStr];
    //kJDISVSettlementAddressFloorHelperDecryptKey);
}
-(NSString*)base64Encoding:(NSString*)str{
    NSData* data = [str dataUsingEncoding:NSUTF8StringEncoding];
    return [data base64EncodedDataWithOptions:0];
}

-(NSString*)base64Decoding:(NSString*)str{
    NSData* data = [[NSData alloc] initWithBase64EncodedString:str options:0];
    return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
}

-(NSString*)do3DecEncryptStr:(NSString *)encryptStr{
    if (![encryptStr jdcd_validateString]) {
        return nil;
    }
//    NSData *encryptData = [GTMBase64 decodeData:[encryptStr dataUsingEncoding:NSUTF8StringEncoding]];
    
    NSData* encryptData = [[NSData alloc] initWithBase64EncodedString:encryptStr options:0];
    size_t plainTextBufferSize = [encryptData length];
    const void *vplainText = [encryptData bytes];
    
    CCCryptorStatus ccStatus;
    uint8_t *bufferPtr = NULL;
    size_t bufferPtrSize = 0;
    size_t movedBytes = 0;
    
    bufferPtrSize = (plainTextBufferSize + kCCBlockSize3DES) & ~(kCCBlockSize3DES - 1);
    bufferPtr = malloc( bufferPtrSize * sizeof(uint8_t));
    memset((void *)bufferPtr, 0x0, bufferPtrSize);
    
    const void *vkey = (const void *) [kJDISVSettlementAddressFloorHelperDecryptKey UTF8String];
    
    const void *vinitVec = nil;
    
    ccStatus = CCCrypt(kCCDecrypt,
                       kCCAlgorithm3DES,
                       kCCOptionPKCS7Padding|kCCOptionECBMode,
                       vkey,
                       kCCKeySize3DES,
                       vinitVec,
                       vplainText,
                       plainTextBufferSize,
                       (void *)bufferPtr,
                       bufferPtrSize,
                       &movedBytes);
    
    NSString *result = [[NSString alloc] initWithData:[NSData dataWithBytes:(const void *)bufferPtr
                                                                      length:(NSUInteger)movedBytes] encoding:NSUTF8StringEncoding];
    
    free(bufferPtr);
    return result;
}
- (NSString *)levelAddress {
    return _levelAddress;
}

- (NSDictionary *)requestShipmentSelfStoreParam {
    return @{
        @"address": _address,
        @"idProvince": _provinceId,
        @"idCity": _cityId,
        @"idArea": _areaId,
        @"idTown": _townId,
    };
}

- (NSString *)fullAddress {
    return _address;
}

- (NSNumber *)addressId {
    return _addressId;
}

- (CGFloat)consigneeAddressLatitude {
    return [_latitude doubleValue];
}
- (CGFloat)consigneeAddressLongitude {
    return [_longitude doubleValue];
}

- (JDISVSettlementAddressFloorAddressModel *)addressFloorModel {
    return _currentConsigneeAddress;
}

- (NSDictionary *)addressMapInCommonModel {
    NSDictionary *addressMap = @{
        @"consigneeAddressMap": [[self addressFloorModel] yy_modelToJSONObject] // 模型转字段
    };
    return addressMap;
}
@end
