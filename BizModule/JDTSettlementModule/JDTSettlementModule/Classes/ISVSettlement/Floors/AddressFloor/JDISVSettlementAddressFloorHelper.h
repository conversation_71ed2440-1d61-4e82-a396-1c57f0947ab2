//
//  JDISVSettlementAddressFloorHelper.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementAddressFloorAddressModel,JDISVFloorCommonModel;

@interface JDISVSettlementAddressFloorHelper : NSObject
+ (instancetype)sharedService;

- (NSString * __nullable)decryptString:(NSString * __nonnull)encryptedStr;
- (void)saveSettlementConsigneeAddress:(JDISVSettlementAddressFloorAddressModel *)consigneeAddressModel toCommonModel:(JDISVF<PERSON>orCommonModel *)commonModel;

- (NSNumber *)addressId;
- (NSString *)fullAddress;
- (NSDictionary *)requestShipmentSelfStoreParam;
- (NSString * __nullable)decryptString:(NSString * __nonnull)encryptedStr;
- (NSString *)levelAddress;
- (CGFloat)consigneeAddressLatitude;
- (CGFloat)consigneeAddressLongitude;
- (JDISVSettlementAddressFloorAddressModel *)addressFloorModel;
@end

NS_ASSUME_NONNULL_END
