//
//  JDISVSettlementAddressFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/8.
//

#import "JDISVSettlementAddressFloorModule.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import "NSBundle+JDISVSettlement.h"
#import "NSAttributedString+ISVSettlementAddress.h"

#import "JDISVSettlementAddressFloorAddressModel.h"
#import "JDISVSettlementAddressFloorHelper.h"

JDISVRegisterFloorModule(KaCheckAddressFloor, JDISVSettlementAddressFloorModule)

@interface JDISVSettlementAddressFloorModule ()
@property (nonatomic, assign) CGFloat height;

@property (nonatomic, strong) JDISVSettlementAddressFloorAddressModel *model;

@property (nonatomic, assign) NSInteger sourceType;

@end

@implementation JDISVSettlementAddressFloorModule
- (UINib *)tableViewFloorNib {

    return [UINib nibWithNibName:@"JDISVSettlementAddressFloor" bundle:[NSBundle jdisvSettlement_bundle]];
}


- (CGFloat)floorHeight {
    
    return _height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}


- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
    // 遍历字典转换模型
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];
    NSDictionary *hierarchy = [resultInfo objectForKey:@"hierarchy"];
    NSString *rootKey = [resultInfo objectForKey:@"rootKey"];
    self.sourceType = [resultInfo[@"source"] integerValue];
    
    // 根节点Key数组
    NSArray *rootFloorKeys = [hierarchy objectForKey:rootKey];
    NSString *addressUUID = @"";
    for (NSString *floorKey in rootFloorKeys) {
        // 遍历获取地址楼层uuid
        if ([floorKey containsString:@"defaultAddressFloor"]) {
            addressUUID = [floorKey copy];
            break;
        }
    }
    
    if ([data[@"isLoc"] isKindOfClass:NSNumber.class]) {
        NSNumber *isLoc = data[@"isLoc"] ?: @(0);
        self.isLoc = isLoc.boolValue;
    }
    
    for (NSDictionary *floorMap in floorsArray) {
        if ([floorMap.allKeys containsObject:@"uuid"] && [floorMap[@"uuid"] isEqualToString:addressUUID]) {
            NSDictionary *addressInfo = floorMap[@"info"] ? : @{};
            self.model = [JDISVSettlementAddressFloorAddressModel yy_modelWithDictionary:addressInfo];
            self.commonModel = commonModel;
            [[JDISVSettlementAddressFloorHelper sharedService] saveSettlementConsigneeAddress:_model toCommonModel:_commonModel];
            
            [self updateWithModel:_model forType:0];
            break;
        }
    }
}

#pragma mark - ViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        
        self.height = 0;
    }
    return self;
}

- (void)updateWithModel:(JDISVSettlementAddressFloorAddressModel *)model forType:(NSInteger)type {
    if ([model isKindOfClass:JDISVSettlementAddressFloorAddressModel.class]) {
        
        self.model = model;
        
        if (_model.allLocShipment || _model.allVenderPickShipment) {
            //全是LOC商品或者全是门店自提商品，地址UI隐藏区域地址和详细地址，姓名改到区域Label上显示
            // 姓名
            NSString *name = @"";
            if ([_model.encryptedName jdcd_validateString]) {
                NSString *deName = [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedName];
                if ([deName jdcd_validateString]) {
                    name = [deName copy];
                }
            }else if ([_model.name jdcd_validateString]) {
                name = _model.name;
            }
            NSAttributedString *areaAttributedString = [[NSAttributedString alloc] initWithString:[name copy] attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            
            NSMutableAttributedString *areaMutableAttributedString = [[NSMutableAttributedString alloc] init];
            if (_model.isDefaultAddress) {
                // 默认地址
                // 默认地址图标
                //
                CGFloat tagWidth = ceil([SettlementLan(@"checkout_address_default") jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:1].width) + 8.f;
                CGRect defaultAddressFrame = CGRectMake(0, -3 , tagWidth, 18);
                NSTextAttachment *defaultAddressTagAttach = [[NSTextAttachment alloc] init];
                defaultAddressTagAttach.image = [self deaultAddressTagWithFrame:CGRectMake(0, 0, tagWidth, 18)];
                defaultAddressTagAttach.bounds = defaultAddressFrame;
                NSAttributedString *defaultAddressTagAttributedString = [NSAttributedString attributedStringWithAttachment:defaultAddressTagAttach];
                // 空白占位
                CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
                NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
                whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
                whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
                NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
                [areaMutableAttributedString appendAttributedString:defaultAddressTagAttributedString];
                [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
                
            }
            if ([_model.tagName jdcd_validateString]) {
                CGFloat tagWidth = ceil([_model.tagName jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:2].width) + 8.f;
    //            if (tagWidth < 30.f) {
    //                tagWidth = 30.f;
    //            }
                CGRect tagFrame = CGRectMake(0, -3 , tagWidth, 18);
                NSTextAttachment *tagAttach = [[NSTextAttachment alloc] init];
                tagAttach.image = [self addressTagWithTitle:_model.tagName frame:tagFrame];
                tagAttach.bounds = tagFrame;
                NSAttributedString *tagAttributedString = [NSAttributedString attributedStringWithAttachment:tagAttach];
                // 空白占位
                CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
                NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
                whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
                whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
                NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
                [areaMutableAttributedString appendAttributedString:tagAttributedString];
                [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
            }
            
            [areaMutableAttributedString appendAttributedString:areaAttributedString];
            
            if ([areaMutableAttributedString.string jdcd_validateString]) {
                if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
                    style.baseWritingDirection = NSWritingDirectionRightToLeft;
                    [areaMutableAttributedString addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, areaAttributedString.length)];
                }
                self.areaAttributedString = [areaMutableAttributedString copy];
                self.addressLabelHeight = ceil([areaAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f - 36.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium] maxNumberOfLines:2].height);
            } else {
                self.areaAttributedString = nil;
                self.addressLabelHeight = 0;
            }
            
            // 详细地址
            self.detailAddressAttributedString = nil;
            self.detailAddressLabelHeight = 0;
            
            // 姓名
            self.nameAttribtuedString = nil;
            self.nameLabelWidth = 0;
            
            // 电话
            NSString *mobile = @"";
            if ([_model.starredMobile  jdcd_validateString]) {
                    mobile = _model.starredMobile;
            }else if ([_model.mobile jdcd_validateString]) {
                mobile = _model.mobile;
            }
            if ([mobile jdcd_validateString]) {
                self.mobileAttributedString = [[NSAttributedString alloc] initWithString:mobile attributes:@{
                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium],
                    NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
                }];
            } else {
                self.mobileAttributedString = nil;
            }
            //
            self.height = 18.f/*上边距*/ + 26.f /**< 姓名 */ + 4.f + 17.f/**< 电话*/ + 18.f/*下边距*/ ;
     }else{
        NSMutableString *areaAddress = [NSMutableString stringWithString:@""];
        if ([_model.areaName.provinceName jdcd_validateString]) {
            [areaAddress appendString:_model.areaName.provinceName];
        }
        if ([_model.areaName.cityName isEqualToString:_model.areaName.cityName] && [_model.areaName.provinceName isEqualToString:_model.areaName.cityName] == NO) {
            [areaAddress appendString:_model.areaName.cityName];
        }
        if ([_model.areaName.areaName jdcd_validateString]) {
            [areaAddress appendString:_model.areaName.areaName];
        }
        if ([_model.areaName.townName jdcd_validateString]) {
            [areaAddress appendString:_model.areaName.townName];
        }
        //[areaAddress copy]
        NSAttributedString *areaAttributedString = [[NSAttributedString alloc] initWithString:[areaAddress copy] attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        NSMutableAttributedString *areaMutableAttributedString = [[NSMutableAttributedString alloc] init];

        if (_model.isDefaultAddress) {
            // 默认地址
            // 默认地址图标
            CGFloat tagWidth = ceil([SettlementLan(@"checkout_address_default") jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:1].width) + 8.f;
            CGRect defaultAddressFrame = CGRectMake(0, -3 , tagWidth, 18);
            NSTextAttachment *defaultAddressTagAttach = [[NSTextAttachment alloc] init];
            defaultAddressTagAttach.image = [self deaultAddressTagWithFrame:CGRectMake(0, 0, tagWidth, 18)];
            defaultAddressTagAttach.bounds = defaultAddressFrame;
            NSAttributedString *defaultAddressTagAttributedString = [NSAttributedString attributedStringWithAttachment:defaultAddressTagAttach];
            // 空白占位
            CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
            NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
            whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
            whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
            NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
            [areaMutableAttributedString appendAttributedString:defaultAddressTagAttributedString];
            [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
        }
        if ([_model.tagName jdcd_validateString]) {
            CGFloat tagWidth = ceil([_model.tagName jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:2].width) + 8.f;
//            if (tagWidth < 30.f) {
//                tagWidth = 30.f;
//            }
            CGRect tagFrame = CGRectMake(0, -3 , tagWidth, 18);
            NSTextAttachment *tagAttach = [[NSTextAttachment alloc] init];
            tagAttach.image = [self addressTagWithTitle:_model.tagName frame:tagFrame];
            tagAttach.bounds = tagFrame;
            NSAttributedString *tagAttributedString = [NSAttributedString attributedStringWithAttachment:tagAttach];
            // 空白占位
            CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
            NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
            whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
            whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
            NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
            [areaMutableAttributedString appendAttributedString:tagAttributedString];
            [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
        }
        
        [areaMutableAttributedString appendAttributedString:areaAttributedString];
        
        if ([areaMutableAttributedString.string jdcd_validateString]) {
            if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
                style.baseWritingDirection = NSWritingDirectionRightToLeft;
                [areaMutableAttributedString addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, areaAttributedString.length)];
            }
            self.areaAttributedString = [areaMutableAttributedString copy];
            self.addressLabelHeight = ceil([areaAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f - 36.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium] maxNumberOfLines:2].height);
        } else {
            self.areaAttributedString = nil;
        }
        
        // 详细地址
        NSString *detailAddress = @"";
        if ([_model.encryptedAddressDetail jdcd_validateString]) {
            NSString *deDetailAddress= [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedAddressDetail];
            if ([deDetailAddress jdcd_validateString]) {
                detailAddress = [deDetailAddress copy];
            }
        }else if ([_model.detailAdress jdcd_validateString]) {
            detailAddress = _model.detailAdress;
        }
        if ([detailAddress jdcd_validateString]) {
            self.detailAddressAttributedString = [[NSAttributedString alloc] initWithString:detailAddress attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            self.detailAddressLabelHeight = ceil([detailAddress jdcd_sizeWithContainer:CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f - 36.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium] maxNumberOfLines:2].height);
        } else {
            self.detailAddressAttributedString = nil;
            self.detailAddressLabelHeight = 0;
        }
        
        // 姓名
        NSString *name = @"";
        if ([_model.encryptedName jdcd_validateString]) {
            NSString *deName = [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedName];
            if ([deName jdcd_validateString]) {
                name = [deName copy];
            }
        }else if ([_model.name jdcd_validateString]) {
            name = _model.name;
        }
        
        if ([name jdcd_validateString]) {
            self.nameAttribtuedString = [[NSAttributedString alloc] initWithString:name attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            self.nameLabelWidth = ceil([name jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2].width);
        } else {
            self.nameAttribtuedString = nil;
            self.nameLabelWidth = 0;
        }
        
        // 电话
        NSString *mobile = @"";
        if ([_model.starredMobile  jdcd_validateString]) {
                mobile = _model.starredMobile;
        }else if ([_model.mobile jdcd_validateString]) {
            mobile = _model.mobile;
        }
        if ([mobile jdcd_validateString]) {
            self.mobileAttributedString = [[NSAttributedString alloc] initWithString:mobile attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
        } else {
            self.mobileAttributedString = nil;
        }
        self.height = 18.f + self.addressLabelHeight + 6.f + _detailAddressLabelHeight + 6.f + 20.f/**< 姓名*/ + 18.f ;
     }
        
        //下面处理tip提示相关
        if (ISPresell == YES && ISFirstMoenyFlag == YES) {
            self.height = self.height+ 28/**< 预售tip */ + 9.f;
        }
        
        // 提示
        if ([_model.venderPickTip jdcd_validateString]) {
            self.tipLabelAttributedString = [[NSAttributedString alloc] ka_settlement_address_initWithString:_model.venderPickTip colorKey:@"#C12" fontKey:@"#T9"];
            CGFloat tipWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2 - 30.f*2;
            self.tipLabelHeight = [self.tipLabelAttributedString ka_settlement_address_heightOfAttributedStringWidth:tipWidth maxNumberOfLines:0 fontKey:@"#T9"];
            self.tipBgViewHeight = self.tipLabelHeight + 6.f*2;
            self.tipBgViewColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07f];
            // 标签倒角
            self.tipBgViewCorners = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
            self.height = self.height + self.tipBgViewHeight + 6.f;
            
        }
        
    } else {
        self.height = 0;
    }
}

# pragma mark - Help Methods
- (UIImage *)deaultAddressTagWithFrame:(CGRect)frame {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:frame];
    [defaultTagBtn setTitle:SettlementLan(@"checkout_address_default")  forState:UIControlStateNormal];
    [defaultTagBtn renderL4WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    [defaultTagBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];//标签写死白色，不用设计变量
    return [self getImageFromView:defaultTagBtn];
}

- (UIImage *)addressTagWithTitle:(NSString *)title frame:(CGRect)frame{
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:frame];
    [defaultTagBtn setTitle:title forState:UIControlStateNormal];
    [defaultTagBtn renderL4WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    [defaultTagBtn jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C14") forState:UIControlStateNormal];
    [defaultTagBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];//标签写死白色，不用设计变量
    return [self getImageFromView:defaultTagBtn];
}

- (UIImage *)placeHolderImageWithFrame:(CGRect)frame {
    UIView *placeHolderView = [[UIView alloc] initWithFrame:frame];
    placeHolderView.backgroundColor = [UIColor clearColor];
    
    return [self getImageFromView:placeHolderView];
}

- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

#pragma mark - Signal
//MARK: 保存收货地址
- (RACSignal *)loadSaveConsigneeAddressId:(NSString *)addressId {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        if ([addressId jdcd_validateString]) {
            [param addEntriesFromDictionary:@{@"id": addressId}];
        }
        [self requestSaveAddressWithParam:[param copy] sourceFrom:self.sourceType complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:resultInfo];
            }
        }];
        return nil;
    }];
}

#pragma mark - Request
//MARK:地址保存地址
/// 地址保存地址 se_saveConsigneeAddress
/// @param orderStrParam 提单参数
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSaveAddressWithParam:(NSDictionary *)orderStrParam
                                                     sourceFrom:(NSInteger)source
                                                       complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];
    
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_saveConsigneeAddress" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
    }];
}


@end
