//
//  JDISVKSASettlementAddressStepCell.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chen<PERSON>yu<PERSON> on 2023/6/1.
//

#import "JDISVKSASettlementAddressStepCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVKSASettlementAddressStepViewModel.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>


@interface JDISVKSASettlementAddressStepCell ()

@property (strong,nonatomic) UILabel *stepNumberLabel;
@property (strong,nonatomic) UILabel *stepTitleLabel;

@property (strong,nonatomic) UIButton *modifyBtn;
@end

@implementation JDISVKSASettlementAddressStepCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        [self.contentView addSubview:self.stepTitleLabel];
        [self.contentView addSubview:self.stepNumberLabel];
        [self.contentView addSubview:self.modifyBtn];
        
        if (SettlementNetService.isVistor){
            self.modifyBtn.hidden = NO;//访客结算展示修改地址入口
        } else {
            self.modifyBtn.hidden = YES;
        }
        
        [self.stepNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(18.f);
            make.height.mas_offset(25.f);
        }];
        
        [self.stepTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.stepNumberLabel);
            make.leading.mas_equalTo(self.stepNumberLabel.mas_trailing).mas_offset(8.f);
            make.height.mas_equalTo(self.stepNumberLabel);
        }];
        
        [self.modifyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.stepNumberLabel);
            make.trailing.mas_equalTo(self.contentView.mas_trailing).mas_offset(-18.f);
            make.height.mas_equalTo(self.stepNumberLabel);
        }];
        /*高度计算 25.f + 12.f*/
    }
    return self;
}

- (UILabel *)stepTitleLabel{
    if (!_stepTitleLabel){
        _stepTitleLabel = [[UILabel alloc] init];
        _stepTitleLabel.backgroundColor = [UIColor clearColor];
    }
    return _stepTitleLabel;
}

- (UILabel *)stepNumberLabel{
    if (!_stepNumberLabel){
        _stepNumberLabel = [[UILabel alloc] init];
        _stepNumberLabel.backgroundColor = [UIColor clearColor];
    }
    return _stepNumberLabel;
}

- (UIButton *)modifyBtn{
    if (!_modifyBtn){
        _modifyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_modifyBtn setTitle:@"Modify" forState:UIControlStateNormal];
        [_modifyBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"] forState:UIControlStateNormal];
        _modifyBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
        [_modifyBtn addTarget:self action:@selector(modifyClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _modifyBtn;
}

- (void)configDataModel:(JDISVKSASettlementAddressBaseViewModel *)model{
    if ([model isKindOfClass:[JDISVKSASettlementAddressStepViewModel class]]){
        JDISVKSASettlementAddressStepViewModel *data = model;
        self.stepNumberLabel.attributedText = data.stepNumAttributed;
        self.stepTitleLabel.attributedText = data.StepTitleAttributed;
    }
}

- (void)modifyClick{
    [self.delegate sendNext:@{@"cell_identifier":@"JDISVKSASettlementAddressStepCell",
                              @"action_type": @"vistorClickModify",
    }];
}


@end
