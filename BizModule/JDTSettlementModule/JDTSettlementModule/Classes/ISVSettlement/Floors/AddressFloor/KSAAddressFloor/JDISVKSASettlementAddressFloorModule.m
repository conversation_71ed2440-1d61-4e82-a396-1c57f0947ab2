//
//  JDISVKSASettlementAddressFloorModule.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/1.
//

#import "JDISVKSASettlementAddressFloorModule.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

#import "NSBundle+JDISVSettlement.h"
#import "NSAttributedString+ISVSettlementAddress.h"

#import "JDISVSettlementAddressFloorAddressModel.h"
#import "JDISVSettlementAddressFloorHelper.h"
#import "JDISVKSASettlementAddressFloor.h"

#import "JDISVKSASettlementAddressStepViewModel.h"
#import "JDISVKSASettlementAddressNotesViewModel.h"
#import "JDISVKSASettlementAddressInfoNormalViewModel.h"
#import "CHECKDetailModel.h"

@import JDTCommonToolModule;

JDISVRegisterFloorModule(KaKSACheckAddressFloor, JDISVKSASettlementAddressFloorModule)
@interface JDISVKSASettlementAddressFloorModule ()


@property (nonatomic, assign) CGFloat height;

@property (nonatomic, strong) JDISVSettlementAddressFloorAddressModel *model;

@property (nonatomic, assign) NSInteger sourceType;


@end


@implementation JDISVKSASettlementAddressFloorModule

- (Class)tableViewFloorClass{
    return JDISVKSASettlementAddressFloor.class;
}

- (CGFloat)floorHeight {
    
    return self.height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSArray <CHECKVenderModel *> *venderArr = [NSArray yy_modelArrayWithClass:[CHECKVenderModel class] json:data[@"data"][@"venderList"]];
    JDTAddressItemModel *address = [JDTAddressItemModel yy_modelWithDictionary:data[@"data"][@"userAddress"]];
    
    [self.ksa_addressInfoCellViewModels removeAllObjects];
    
    // 1、步骤标题 cell
    JDISVKSASettlementAddressStepViewModel *stepViewModel = [[JDISVKSASettlementAddressStepViewModel alloc] init];
    stepViewModel.stepNumAttributed = [[NSAttributedString alloc] initWithString:@"1" attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
    }];
    stepViewModel.StepTitleAttributed = [[NSAttributedString alloc] initWithString:SettlementLan(@"checkout_settlment_step") attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
    }];
    stepViewModel.height = 37.f;
    [self.ksa_addressInfoCellViewModels addObject:stepViewModel];
    
    // 2、详细信息 cell
    JDISVKSASettlementAddressInfoNormalViewModel *addressInfoViewModel = [[JDISVKSASettlementAddressInfoNormalViewModel alloc] init];
    // 2.1、姓名
    addressInfoViewModel.nameAttributed = [[NSAttributedString alloc] initWithString:address.name attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
    }];
    addressInfoViewModel.nameLabelWidth = ceil([address.name jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 22.5f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2].width);
    // 2.2、电话
    addressInfoViewModel.phoneAttributed = [[NSAttributedString alloc] initWithString:address.mobile attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
    }];
    addressInfoViewModel.phoneLabelWidth = ceil([address.mobile jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 22.5f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2].width);
    // 2.3、地址
    addressInfoViewModel.detailAddressAttributed = [[NSAttributedString alloc] initWithString:address.fullAddress attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
    }];
    CGFloat maxShowWidth = [UIScreen mainScreen].bounds.size.width - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]/*楼层左右间距和*/ - 36/*背景左右间距和*/ - 12.f/*背景内容左间距*/ - 32/*背景内容右间距*/;
    addressInfoViewModel.detailAddressHeight = ceil([address.fullAddress jdcd_sizeWithContainer:CGSizeMake(maxShowWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:0].height);
    
    addressInfoViewModel.height = 0/*背景与Cell顶部间距*/ + 12/*顶部间距*/ + 22.5/*名字高度*/ + 5/*名字和电话间距*/ + 22.5/*电话高度*/ + 5/**电话与详细地址间距*/ + addressInfoViewModel.detailAddressHeight/*详细地址高度*/ + 12/*底部间距*/ + 6.f/*背景与cell底部间距*/;
    
    [self.ksa_addressInfoCellViewModels addObject:addressInfoViewModel];
    // 2.4、tips 信息
    BOOL foundTips = NO;
    for (CHECKVenderModel *vender in venderArr) {
        for (CHECKDeliveryInfoModel *delivery in vender.deliveryInfoList) {
            if (delivery.deliveryTips.length > 0) {
                JDISVKSASettlementAddressNotesViewModel *addressNoteViewModel = [[JDISVKSASettlementAddressNotesViewModel alloc] init];
                addressNoteViewModel.noteAttributed = [[NSAttributedString alloc] initWithString:delivery.deliveryTips attributes:@{
                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightMedium],
                    NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]
                }];
                addressNoteViewModel.height = ceil([addressNoteViewModel.noteAttributed.string jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0].height);
                [self.ksa_addressInfoCellViewModels addObject:addressNoteViewModel];
                foundTips = YES;
                break;
            }
        }
        if (foundTips) {
            break;
        }
    }
    
    CGFloat allHeight = 36.f;
    for (JDISVKSASettlementAddressBaseViewModel *model in self.ksa_addressInfoCellViewModels) {
        allHeight += model.height;
    }
    self.height = allHeight;
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel{
    // 遍历字典转换模型
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];
    NSDictionary *hierarchy = [resultInfo objectForKey:@"hierarchy"];
    NSString *rootKey = [resultInfo objectForKey:@"rootKey"];
    self.sourceType = [resultInfo[@"source"] integerValue];
    
    // 根节点Key数组
    NSArray *rootFloorKeys = [hierarchy objectForKey:rootKey];
    NSString *addressUUID = @"";
    for (NSString *floorKey in rootFloorKeys) {
        // 遍历获取地址楼层uuid
        if ([floorKey containsString:@"defaultAddressFloor"]) {
            addressUUID = [floorKey copy];
            break;
        }
    }
    
    if ([data[@"isLoc"] isKindOfClass:NSNumber.class]) {
        NSNumber *isLoc = data[@"isLoc"] ?: @(0);
        self.isLoc = isLoc.boolValue;
    }
    
    for (NSDictionary *floorMap in floorsArray) {
        if ([floorMap.allKeys containsObject:@"uuid"] && [floorMap[@"uuid"] isEqualToString:addressUUID]) {
            NSDictionary *addressInfo = floorMap[@"info"] ? : @{};
            self.model = [JDISVSettlementAddressFloorAddressModel yy_modelWithDictionary:addressInfo];
            self.commonModel = commonModel;
            [[JDISVSettlementAddressFloorHelper sharedService] saveSettlementConsigneeAddress:_model toCommonModel:_commonModel];
            
            if (KSAAPP){
                [self ksa_updateWithModel:_model forType:0];
            }else{
                [self updateWithModel:_model forType:0];
            }
            break;
        }
    }
}

#pragma mark - ViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.stepNumberAttributed = [[NSAttributedString alloc] ka_settlement_address_initWithString:@"2"  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
        self.stepTitleAttributed = [[NSAttributedString alloc] ka_settlement_address_initWithString:SettlementLan(@"checkout_settlment_payment_title")  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
        self.height = 0;
    }
    return self;
}

- (void)updateWithModel:(JDISVSettlementAddressFloorAddressModel *)model forType:(NSInteger)type {
    if ([model isKindOfClass:JDISVSettlementAddressFloorAddressModel.class]) {
        
        self.model = model;
        
        if (_model.allLocShipment || _model.allVenderPickShipment) {
            //全是LOC商品或者全是门店自提商品，地址UI隐藏区域地址和详细地址，姓名改到区域Label上显示
            // 姓名
            NSString *name = @"";
            if ([_model.encryptedName jdcd_validateString]) {
                NSString *deName = [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedName];
                if ([deName jdcd_validateString]) {
                    name = [deName copy];
                }
            }else if ([_model.name jdcd_validateString]) {
                name = _model.name;
            }
            NSAttributedString *areaAttributedString = [[NSAttributedString alloc] initWithString:[name copy] attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            
            NSMutableAttributedString *areaMutableAttributedString = [[NSMutableAttributedString alloc] init];
            if (_model.isDefaultAddress) {
                // 默认地址
                // 默认地址图标
                //
                CGFloat tagWidth = ceil([SettlementLan(@"checkout_address_default") jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:1].width) + 8.f;
                CGRect defaultAddressFrame = CGRectMake(0, -3 , tagWidth, 18);
                NSTextAttachment *defaultAddressTagAttach = [[NSTextAttachment alloc] init];
                defaultAddressTagAttach.image = [self deaultAddressTagWithFrame:CGRectMake(0, 0, tagWidth, 18)];
                defaultAddressTagAttach.bounds = defaultAddressFrame;
                NSAttributedString *defaultAddressTagAttributedString = [NSAttributedString attributedStringWithAttachment:defaultAddressTagAttach];
                // 空白占位
                CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
                NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
                whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
                whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
                NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
                [areaMutableAttributedString appendAttributedString:defaultAddressTagAttributedString];
                [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
                
            }
            if ([_model.tagName jdcd_validateString]) {
                CGFloat tagWidth = ceil([_model.tagName jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:2].width) + 8.f;
    //            if (tagWidth < 30.f) {
    //                tagWidth = 30.f;
    //            }
                CGRect tagFrame = CGRectMake(0, -3 , tagWidth, 18);
                NSTextAttachment *tagAttach = [[NSTextAttachment alloc] init];
                tagAttach.image = [self addressTagWithTitle:_model.tagName frame:tagFrame];
                tagAttach.bounds = tagFrame;
                NSAttributedString *tagAttributedString = [NSAttributedString attributedStringWithAttachment:tagAttach];
                // 空白占位
                CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
                NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
                whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
                whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
                NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
                [areaMutableAttributedString appendAttributedString:tagAttributedString];
                [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
            }
            
            [areaMutableAttributedString appendAttributedString:areaAttributedString];
            
            if ([areaMutableAttributedString.string jdcd_validateString]) {
                if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
                    style.baseWritingDirection = NSWritingDirectionRightToLeft;
                    [areaMutableAttributedString addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, areaAttributedString.length)];
                }
                self.areaAttributedString = [areaMutableAttributedString copy];
                self.addressLabelHeight = ceil([areaAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f - 36.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium] maxNumberOfLines:2].height);
            } else {
                self.areaAttributedString = nil;
                self.addressLabelHeight = 0;
            }
            
            // 详细地址
            self.detailAddressAttributedString = nil;
            self.detailAddressLabelHeight = 0;
            
            // 姓名
            self.nameAttribtuedString = nil;
            self.nameLabelWidth = 0;
            
            // 电话
            NSString *mobile = @"";
            if ([_model.starredMobile  jdcd_validateString]) {
                    mobile = _model.starredMobile;
            }else if ([_model.mobile jdcd_validateString]) {
                mobile = _model.mobile;
            }
            if ([mobile jdcd_validateString]) {
                self.mobileAttributedString = [[NSAttributedString alloc] initWithString:mobile attributes:@{
                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium],
                    NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
                }];
            } else {
                self.mobileAttributedString = nil;
            }
            //
            self.height = 18.f/*上边距*/ + 26.f /**< 姓名 */ + 4.f + 17.f/**< 电话*/ + 18.f/*下边距*/ ;
     }else{
        NSMutableString *areaAddress = [NSMutableString stringWithString:@""];
        if ([_model.areaName.provinceName jdcd_validateString]) {
            [areaAddress appendString:_model.areaName.provinceName];
        }
        if ([_model.areaName.cityName isEqualToString:_model.areaName.cityName] && [_model.areaName.provinceName isEqualToString:_model.areaName.cityName] == NO) {
            [areaAddress appendString:_model.areaName.cityName];
        }
        if ([_model.areaName.areaName jdcd_validateString]) {
            [areaAddress appendString:_model.areaName.areaName];
        }
        if ([_model.areaName.townName jdcd_validateString]) {
            [areaAddress appendString:_model.areaName.townName];
        }
        //[areaAddress copy]
        NSAttributedString *areaAttributedString = [[NSAttributedString alloc] initWithString:[areaAddress copy] attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        NSMutableAttributedString *areaMutableAttributedString = [[NSMutableAttributedString alloc] init];

        if (_model.isDefaultAddress) {
            // 默认地址
            // 默认地址图标
            CGFloat tagWidth = ceil([SettlementLan(@"checkout_address_default") jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:1].width) + 8.f;
            CGRect defaultAddressFrame = CGRectMake(0, -3 , tagWidth, 18);
            NSTextAttachment *defaultAddressTagAttach = [[NSTextAttachment alloc] init];
            defaultAddressTagAttach.image = [self deaultAddressTagWithFrame:CGRectMake(0, 0, tagWidth, 18)];
            defaultAddressTagAttach.bounds = defaultAddressFrame;
            NSAttributedString *defaultAddressTagAttributedString = [NSAttributedString attributedStringWithAttachment:defaultAddressTagAttach];
            // 空白占位
            CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
            NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
            whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
            whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
            NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
            [areaMutableAttributedString appendAttributedString:defaultAddressTagAttributedString];
            [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
        }
        if ([_model.tagName jdcd_validateString]) {
            CGFloat tagWidth = ceil([_model.tagName jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 16.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:2].width) + 8.f;
//            if (tagWidth < 30.f) {
//                tagWidth = 30.f;
//            }
            CGRect tagFrame = CGRectMake(0, -3 , tagWidth, 18);
            NSTextAttachment *tagAttach = [[NSTextAttachment alloc] init];
            tagAttach.image = [self addressTagWithTitle:_model.tagName frame:tagFrame];
            tagAttach.bounds = tagFrame;
            NSAttributedString *tagAttributedString = [NSAttributedString attributedStringWithAttachment:tagAttach];
            // 空白占位
            CGRect whitePlaceHolderFrame = CGRectMake(0, -3, 7, 18);
            NSTextAttachment *whitePlaceHolderAttach = [[NSTextAttachment alloc] init];
            whitePlaceHolderAttach.image = [self placeHolderImageWithFrame:whitePlaceHolderFrame];
            whitePlaceHolderAttach.bounds = whitePlaceHolderFrame;
            NSAttributedString *whitePlaceHolderAttributedString = [NSAttributedString attributedStringWithAttachment:whitePlaceHolderAttach];
            [areaMutableAttributedString appendAttributedString:tagAttributedString];
            [areaMutableAttributedString appendAttributedString:whitePlaceHolderAttributedString];
        }
        
        [areaMutableAttributedString appendAttributedString:areaAttributedString];
        
        if ([areaMutableAttributedString.string jdcd_validateString]) {
            if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
                NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
                style.baseWritingDirection = NSWritingDirectionRightToLeft;
                [areaMutableAttributedString addAttribute:NSParagraphStyleAttributeName value:style range:NSMakeRange(0, areaAttributedString.length)];
            }
            self.areaAttributedString = [areaMutableAttributedString copy];
            self.addressLabelHeight = ceil([areaAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f - 36.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium] maxNumberOfLines:2].height);
        } else {
            self.areaAttributedString = nil;
        }
        
        // 详细地址
        NSString *detailAddress = @"";
        if ([_model.encryptedAddressDetail jdcd_validateString]) {
            NSString *deDetailAddress= [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedAddressDetail];
            if ([deDetailAddress jdcd_validateString]) {
                detailAddress = [deDetailAddress copy];
            }
        }else if ([_model.detailAdress jdcd_validateString]) {
            detailAddress = _model.detailAdress;
        }
        if ([detailAddress jdcd_validateString]) {
            self.detailAddressAttributedString = [[NSAttributedString alloc] initWithString:detailAddress attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            self.detailAddressLabelHeight = ceil([detailAddress jdcd_sizeWithContainer:CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f - 36.f - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium] maxNumberOfLines:2].height);
        } else {
            self.detailAddressAttributedString = nil;
            self.detailAddressLabelHeight = 0;
        }
        
        // 姓名
        NSString *name = @"";
        if ([_model.encryptedName jdcd_validateString]) {
            NSString *deName = [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedName];
            if ([deName jdcd_validateString]) {
                name = [deName copy];
            }
        }else if ([_model.name jdcd_validateString]) {
            name = _model.name;
        }
        
        if ([name jdcd_validateString]) {
            self.nameAttribtuedString = [[NSAttributedString alloc] initWithString:name attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            self.nameLabelWidth = ceil([name jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2].width);
        } else {
            self.nameAttribtuedString = nil;
            self.nameLabelWidth = 0;
        }
        
        // 电话
        NSString *mobile = @"";
        if ([_model.starredMobile  jdcd_validateString]) {
                mobile = _model.starredMobile;
        }else if ([_model.mobile jdcd_validateString]) {
            mobile = _model.mobile;
        }
        if ([mobile jdcd_validateString]) {
            self.mobileAttributedString = [[NSAttributedString alloc] initWithString:mobile attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
        } else {
            self.mobileAttributedString = nil;
        }
        self.height = 18.f + self.addressLabelHeight + 6.f + _detailAddressLabelHeight + 6.f + 20.f/**< 姓名*/ + 18.f ;
     }
        
        //下面处理tip提示相关
        if (ISPresell == YES && ISFirstMoenyFlag == YES) {
            self.height = self.height+ 28/**< 预售tip */ + 9.f;
        }
        
        // 提示
        if ([_model.venderPickTip jdcd_validateString]) {
            self.tipLabelAttributedString = [[NSAttributedString alloc] ka_settlement_address_initWithString:_model.venderPickTip colorKey:@"#C12" fontKey:@"#T9"];
            CGFloat tipWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2 - 30.f*2;
            self.tipLabelHeight = [self.tipLabelAttributedString ka_settlement_address_heightOfAttributedStringWidth:tipWidth maxNumberOfLines:0 fontKey:@"#T9"];
            self.tipBgViewHeight = self.tipLabelHeight + 6.f*2;
            self.tipBgViewColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07f];
            // 标签倒角
            self.tipBgViewCorners = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
            self.height = self.height + self.tipBgViewHeight + 6.f;
            
        }
        
    } else {
        self.height = 0;
    }
}

- (NSMutableArray *)ksa_addressInfoCellViewModels{
    if (!_ksa_addressInfoCellViewModels){
        _ksa_addressInfoCellViewModels = [[NSMutableArray alloc] init];
    }
    return _ksa_addressInfoCellViewModels;
}


- (void)ksa_updateWithModel:(JDISVSettlementAddressFloorAddressModel *)model forType:(NSInteger)type{
    if ([model isKindOfClass:JDISVSettlementAddressFloorAddressModel.class]) {
        [self.ksa_addressInfoCellViewModels removeAllObjects];
        
        JDISVKSASettlementAddressStepViewModel *stepViewModel = [[JDISVKSASettlementAddressStepViewModel alloc] init];///步骤Cell
        stepViewModel.stepNumAttributed = [[NSAttributedString alloc] initWithString:@"1" attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        stepViewModel.StepTitleAttributed = [[NSAttributedString alloc] initWithString:SettlementLan(@"checkout_settlment_step") attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        stepViewModel.height = 37.f;
        [self.ksa_addressInfoCellViewModels addObject:stepViewModel];
        
        
        JDISVKSASettlementAddressInfoNormalViewModel *addressInfoViewModel = [[JDISVKSASettlementAddressInfoNormalViewModel alloc] init];///详细信息Cell
        // 姓名
        NSString *name = @"";
        if ([_model.encryptedName jdcd_validateString]) {
            NSString *deName = [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedName];
            if ([deName jdcd_validateString]) {
                name = [deName copy];
            }
        }else if ([_model.name jdcd_validateString]) {
            name = _model.name;
        }
        
        if ([name jdcd_validateString]) {
            addressInfoViewModel.nameAttributed = [[NSAttributedString alloc] initWithString:name attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
            }];
            addressInfoViewModel.nameLabelWidth = ceil([name jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 22.5f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2].width);
        } else {
            addressInfoViewModel.nameAttributed = nil;
            addressInfoViewModel.nameLabelWidth = 0;
        }
        
        // 电话
        self.mobileAttributedString = nil;
        addressInfoViewModel.phoneAttributed = nil;
        addressInfoViewModel.phoneLabelWidth = 0;
//        if (model.allLocShipment){
//            // 电话
//            self.mobileAttributedString = nil;
//            addressInfoViewModel.phoneAttributed = nil;
//            addressInfoViewModel.phoneLabelWidth = 0;
//        }else{
//            // 电话
//            NSString *mobile = @"";
//            if ([_model.starredMobile  jdcd_validateString]) {
//                    mobile = _model.starredMobile;
//            }else if ([_model.mobile jdcd_validateString]) {
//                mobile = _model.mobile;
//            }
//            if ([mobile jdcd_validateString]) {
//                addressInfoViewModel.phoneAttributed = [[NSAttributedString alloc] initWithString:mobile attributes:@{
//                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium],
//                    NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
//                }];
//                addressInfoViewModel.phoneLabelWidth = ceil([mobile jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 22.5f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:2].width);
//            } else {
//                self.mobileAttributedString = nil;
//                addressInfoViewModel.phoneAttributed = nil;
//                addressInfoViewModel.phoneLabelWidth = 0;
//            }
//        }
        
//        if (_model.isDefaultAddress) {
//            // 默认地址
//            // 默认地址图标
//            CGFloat tagWidth = ceil([SettlementLan(@"checkout_address_default") jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:1].width) + 8.f;
//            addressInfoViewModel.defalutTagWidth = tagWidth;
//            
//            addressInfoViewModel.defalutTagAttributed = [[NSAttributedString alloc] initWithString:SettlementLan(@"checkout_address_default") attributes:@{
//                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium],
//                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"]
//            }];
//            addressInfoViewModel.addressTagleading = 12.f;
//        }else{
//            addressInfoViewModel.addressTagleading = 0.f;
//        }
        
        if ([_model.tagName jdcd_validateString]) {//地址标签
            CGFloat tagWidth = ceil([_model.tagName jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium] maxNumberOfLines:1].width) + 8.f;
            addressInfoViewModel.addressTagWidth = tagWidth;
            addressInfoViewModel.addressTagAttributed = [[NSAttributedString alloc] initWithString:_model.tagName attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T11" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"]
            }];
        }
        
        CGFloat maxShowWidth = [UIScreen mainScreen].bounds.size.width - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]/*楼层左右间距合*/ - 36/*背景左右间距合*/ - 12.f/*背景内容左间距合*/ - 32/*背景内容右间距*/;
        
        
        NSString *mobile = @"";
        NSString *areaCode = [_model.areaCode stringByReplacingOccurrencesOfString:@"0966" withString:@"966"];
        if ([_model.starredMobile  jdcd_validateString]) {
            mobile = [NSString stringWithFormat:@"%@ %@",areaCode ? [NSString stringWithFormat:@"+%@",areaCode] : @"",_model.starredMobile];
        }else if ([_model.mobile jdcd_validateString]) {
            mobile = [NSString stringWithFormat:@"%@ %@",areaCode ? [NSString stringWithFormat:@"+%@",areaCode] : @"",_model.mobile];
        }
        addressInfoViewModel.areAddressAttributed = [[NSAttributedString alloc] initWithString:mobile attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightMedium],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        addressInfoViewModel.areAddressHeight = ceil([mobile jdcd_sizeWithContainer:CGSizeMake(maxShowWidth, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium] maxNumberOfLines:0].height);
        
//        if (model.allLocShipment){
//
//        }else{
//            NSMutableString *areaAddress = [NSMutableString stringWithString:@""];
//            if ([_model.areaName.provinceName jdcd_validateString]) {
//                [areaAddress appendString:_model.areaName.provinceName];
//            }
//            if ([_model.areaName.cityName isEqualToString:_model.areaName.cityName] && [_model.areaName.provinceName isEqualToString:_model.areaName.cityName] == NO) {
//                [areaAddress appendString:_model.areaName.cityName];
//            }
//            if ([_model.areaName.areaName jdcd_validateString]) {
//                [areaAddress appendString:_model.areaName.areaName];
//            }
//            if ([_model.areaName.townName jdcd_validateString]) {
//                [areaAddress appendString:_model.areaName.townName];
//            }
//            addressInfoViewModel.areAddressAttributed = [[NSAttributedString alloc] initWithString:[areaAddress copy] attributes:@{
//                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"],
//                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
//            }];
//            addressInfoViewModel.areAddressHeight = ceil([areaAddress jdcd_sizeWithContainer:CGSizeMake(maxShowWidth, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:0].height);
//        }
        
        
        if (model.allLocShipment){//全是LOC商品
            addressInfoViewModel.detailAddressAttributed = nil;
            addressInfoViewModel.detailAddressHeight = 0;
            addressInfoViewModel.height = 0/*背景与Cell顶部间距*/ + 12/*顶部间距*/ + 22.5/*名字高度*/ + 5/*名字和地址间距*/ + addressInfoViewModel.areAddressHeight/*区域地址高度*/ + 0/**区域地址与详细地址间距*/ + addressInfoViewModel.detailAddressHeight/*详细地址高度*/ + 12/*底部间距*/ + 6.f/*背景与cell底部间距*/;
        }else{
            // 详细地址
            
            NSMutableString *areaAddress = [NSMutableString stringWithString:@""];
            if ([_model.areaName.provinceName jdcd_validateString]) {
                [areaAddress appendString:_model.areaName.provinceName];
            }
            if ([_model.areaName.cityName isEqualToString:_model.areaName.cityName] && [_model.areaName.provinceName isEqualToString:_model.areaName.cityName] == NO) {
                [areaAddress appendString:_model.areaName.cityName];
            }
            if ([_model.areaName.areaName jdcd_validateString]) {
                [areaAddress appendString:_model.areaName.areaName];
            }
            if ([_model.areaName.townName jdcd_validateString]) {
                [areaAddress appendString:_model.areaName.townName];
            }
            
            NSString *detailAddress = @"";
            if ([_model.encryptedAddressDetail jdcd_validateString]) {
                NSString *deDetailAddress= [[JDISVSettlementAddressFloorHelper sharedService] decryptString:_model.encryptedAddressDetail];
                if ([deDetailAddress jdcd_validateString]) {
                    detailAddress = [deDetailAddress copy];
                }
            }else if ([_model.detailAdress jdcd_validateString]) {
                detailAddress = _model.detailAdress;
            }
            NSString *fullAddress = [NSString stringWithFormat:@"%@%@",areaAddress ?: @"",detailAddress ?: @""];
            if (KSAAPP){
                //KSA地址顺序调整
                NSMutableArray *addressArr = @[].mutableCopy;
                NSString* lang = [NSString getKAUseLang];
                if([lang isEqualToString:@"en"]){
                    if([detailAddress isKindOfClass:NSString.class] && detailAddress.length){
                        [addressArr addObject:detailAddress];
                    }
                    if([_model.areaName.townName isKindOfClass:NSString.class] && _model.areaName.townName.length){
                        [addressArr addObject:_model.areaName.townName];
                    }
                    if([_model.areaName.areaName isKindOfClass:NSString.class] && _model.areaName.areaName.length){
                        [addressArr addObject:_model.areaName.areaName];
                    }
                    if([_model.areaName.cityName isKindOfClass:NSString.class] && _model.areaName.cityName.length){
                        [addressArr addObject:_model.areaName.cityName];
                    }
                    if([_model.areaName.provinceName isKindOfClass:NSString.class] && _model.areaName.provinceName.length){
                        [addressArr addObject:_model.areaName.provinceName];
                    }
                    fullAddress = [addressArr componentsJoinedByString:@", "];
                } else {
                    if([_model.areaName.provinceName isKindOfClass:NSString.class] && _model.areaName.provinceName.length){
                        [addressArr addObject:_model.areaName.provinceName];
                    }
                    if([_model.areaName.cityName isKindOfClass:NSString.class] && _model.areaName.cityName.length){
                        [addressArr addObject:_model.areaName.cityName];
                    }
                    if([_model.areaName.areaName isKindOfClass:NSString.class] && _model.areaName.areaName.length){
                        [addressArr addObject:_model.areaName.areaName];
                    }
                    if([_model.areaName.townName isKindOfClass:NSString.class] && _model.areaName.townName.length){
                        [addressArr addObject:_model.areaName.townName];
                    }
                    if([detailAddress isKindOfClass:NSString.class] && detailAddress.length){
                        [addressArr addObject:detailAddress];
                    }
                    fullAddress = [addressArr componentsJoinedByString:@"، "];
                }
            }
            
            if ([fullAddress jdcd_validateString]) {
                addressInfoViewModel.detailAddressAttributed = [[NSAttributedString alloc] initWithString:fullAddress attributes:@{
                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"],
                    NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
                }];
                addressInfoViewModel.detailAddressHeight = ceil([fullAddress jdcd_sizeWithContainer:CGSizeMake(maxShowWidth, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:0].height);
            } else {
                addressInfoViewModel.detailAddressAttributed = nil;
                addressInfoViewModel.detailAddressHeight = 0;
            }
            
            addressInfoViewModel.height = 0/*背景与Cell顶部间距*/ + 12/*顶部间距*/ + 22.5/*名字高度*/ + 5/*名字和地址间距*/ + addressInfoViewModel.areAddressHeight/*区域地址高度*/ + 5/**区域地址与详细地址间距*/ + addressInfoViewModel.detailAddressHeight/*详细地址高度*/ + 12/*底部间距*/ + 6.f/*背景与cell底部间距*/;
        }
        
        [self.ksa_addressInfoCellViewModels addObject:addressInfoViewModel];
        
        
        // ----下面处理tip提示相关----
        if ([self.model.venderPickTip jdcd_validateString]){
            JDISVKSASettlementAddressNotesViewModel *addressNoteViewModel = [[JDISVKSASettlementAddressNotesViewModel alloc] init];///提示信息Cell
            addressNoteViewModel.noteAttributed = [[NSAttributedString alloc] initWithString:self.model.venderPickTip attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]
            }];
            
            addressNoteViewModel.height = ceil([addressNoteViewModel.noteAttributed.string jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0].height);
            [self.ksa_addressInfoCellViewModels addObject:addressNoteViewModel];
        }
        
        if (ISPresell == YES && ISFirstMoenyFlag == YES) {
            JDISVKSASettlementAddressNotesViewModel *addressNoteViewModel = [[JDISVKSASettlementAddressNotesViewModel alloc] init];///提示信息Cell
            addressNoteViewModel.noteAttributed = [[NSAttributedString alloc] initWithString:SettlementLan(@"checkout_address_pre_sale_tips") attributes:@{
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightMedium],
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]
            }];
            
            addressNoteViewModel.height = ceil([addressNoteViewModel.noteAttributed.string jdcd_sizeWithContainer:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0].height);
            
            [self.ksa_addressInfoCellViewModels addObject:addressNoteViewModel];
        }
        
        CGFloat allHeight = 36.f;
        for (JDISVKSASettlementAddressBaseViewModel *model in self.ksa_addressInfoCellViewModels) {
            allHeight += model.height;
        }
        self.height = allHeight;
        
    } else {
        self.height = 0;
    }
}

# pragma mark - Help Methods
- (UIImage *)deaultAddressTagWithFrame:(CGRect)frame {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:frame];
    [defaultTagBtn setTitle:SettlementLan(@"checkout_address_default")  forState:UIControlStateNormal];
    [defaultTagBtn renderL4WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    [defaultTagBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];//标签写死白色，不用设计变量
    return [self getImageFromView:defaultTagBtn];
}

- (UIImage *)addressTagWithTitle:(NSString *)title frame:(CGRect)frame{
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:frame];
    [defaultTagBtn setTitle:title forState:UIControlStateNormal];
    [defaultTagBtn renderL4WithCornerRadius:[[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]];
    [defaultTagBtn jdisv_setBackgroundColorPicker:JDISVColorPickerWithKey(@"#C14") forState:UIControlStateNormal];
    [defaultTagBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];//标签写死白色，不用设计变量
    return [self getImageFromView:defaultTagBtn];
}

- (UIImage *)placeHolderImageWithFrame:(CGRect)frame {
    UIView *placeHolderView = [[UIView alloc] initWithFrame:frame];
    placeHolderView.backgroundColor = [UIColor clearColor];
    
    return [self getImageFromView:placeHolderView];
}

- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

#pragma mark - Signal
//MARK: 保存收货地址
- (RACSignal *)loadSaveConsigneeAddressId:(NSString *)addressId {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *param = [NSMutableDictionary dictionary];
        if ([addressId jdcd_validateString]) {
            [param addEntriesFromDictionary:@{@"id": addressId}];
        }
        [self requestSaveAddressWithParam:[param copy] sourceFrom:self.sourceType complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:resultInfo];
            }
        }];
        return nil;
    }];
}

#pragma mark - Request
//MARK:地址保存地址
/// 地址保存地址 se_saveConsigneeAddress
/// @param orderStrParam 提单参数
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSaveAddressWithParam:(NSDictionary *)orderStrParam
                                                     sourceFrom:(NSInteger)source
                                                       complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    if (source == 1) {
        // 商详
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];

    } else {
        [param addEntriesFromDictionary:@{@"immediatelyBuy":@(NO), @"verticalTag":@"cn_ybxt_b2c"}];
    }
    
    NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
    [orderStrMap addEntriesFromDictionary:orderStrParam];
    //拼团固定参数处理
    if (ISGroupBuy) {
        [param setObject:@(YES) forKey:@"ptFlag"];
    }
    //预售场景下入参
    if (ISPresell) {
        [param setObject:@(YES) forKey:@"isPresale"];
    }
    
    [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];
    
    return [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_saveConsigneeAddress" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            completeBlock(nil, error);
        } else {
            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            
            if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                // 正常
                NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                completeBlock(resultInfo, nil);
            } else {
                // 异常
                NSString *errorCode = @"-999";
                NSInteger codeInt = 0;
                if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                    // TODO code == 3 未登录
                    if ([message jdcd_validateString] == NO) {
                        message = SettlementLan(@"setllement_not_logged_in") ;
                    }
                    errorCode = @"3";
                    codeInt = 3;
                } else {
                    if ([resultCode jdcd_validateString]) {
                        errorCode = resultCode;
                    }
                    if (![message jdcd_validateString]) {
                        message = SettlementLan(@"setllement_unknown_error") ;
                    }
                }
                NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message
                }];
                completeBlock(nil,resultError);
            }
        }
    }];
}


@end
