//
//  JDISVKSASettlementAddressFloor.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/1.
//

#import "JDISVKSASettlementAddressFloor.h"
#import "JDISVKSASettlementAddressInfoNormalCell.h"
#import "JDISVKSASettlementAddressNotesCell.h"
#import "JDISVKSASettlementAddressStepCell.h"
#import "JDISVKSASettlementAddressFloorModule.h"
#import "JDISVKSASettlementAddressBaseViewModel.h"
#import "JDISVKSASettlementAddressBaseCell.h"

#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import "JDISVSettlementAddressFloorAddressModel.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVKSASettlementAddressFloorModule.h"

@interface JDISVKSASettlementAddressFloor ()<UITableViewDelegate,UITableViewDataSource>

@property (strong,nonatomic) UITableView *tableView;
@property (strong,nonatomic) JDISVKSASettlementAddressFloorModule *viewModel;

@end

@implementation JDISVKSASettlementAddressFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        self.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
        self.layer.masksToBounds = YES;
        self.contentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
        self.contentView.layer.masksToBounds = YES;
        
        [self.contentView addSubview:self.tableView];
        [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView).offset(18);
            make.leading.trailing.mas_equalTo(self.contentView);
            make.bottom.mas_equalTo(self.contentView).offset(-18);
        }];
        
    }
    return self;
}

- (UITableView *)tableView{
    if (!_tableView){
        _tableView = [[UITableView alloc] init];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.scrollEnabled = NO;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = [UIColor clearColor];
        [_tableView registerClass:[JDISVKSASettlementAddressInfoNormalCell class] forCellReuseIdentifier:@"JDISVKSASettlementAddressInfoNormalCell"];
        [_tableView registerClass:[JDISVKSASettlementAddressNotesCell class] forCellReuseIdentifier:@"JDISVKSASettlementAddressNotesCell"];
        [_tableView registerClass:[JDISVKSASettlementAddressStepCell class] forCellReuseIdentifier:@"JDISVKSASettlementAddressStepCell"];
        _tableView.userInteractionEnabled = YES;
    }
    return _tableView;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    self.viewModel = floorModel;
    
    [self.tableView reloadData];
}


#pragma mark - <UITableViewDelegate,UITableViewDataSource>

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if (self.viewModel){
        return self.viewModel.ksa_addressInfoCellViewModels.count;
    }else{
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.viewModel!=nil && indexPath.row < self.viewModel.ksa_addressInfoCellViewModels.count) {
        JDISVKSASettlementAddressBaseViewModel *model = self.viewModel.ksa_addressInfoCellViewModels[indexPath.row];
        return model.height;
    } else {
        return 0;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVKSASettlementAddressBaseCell *cell = nil;
    if (self.viewModel!=nil && indexPath.row < self.viewModel.ksa_addressInfoCellViewModels.count) {
        JDISVKSASettlementAddressBaseViewModel *itemViewModel = self.viewModel.ksa_addressInfoCellViewModels[indexPath.row];
        cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.identifity forIndexPath:indexPath];
        [cell configDataModel:itemViewModel];
        
        if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
            __weak typeof(self) weakSelf = self;
            [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(NSDictionary *parames) {
                __strong typeof(weakSelf) strongSelf = weakSelf;
                [self processOrderDelegateActionWithParames:parames];
            }];
        }
    }
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    if (self.viewModel!=nil && indexPath.row < self.viewModel.ksa_addressInfoCellViewModels.count) {
        JDISVKSASettlementAddressBaseViewModel *model = self.viewModel.ksa_addressInfoCellViewModels[indexPath.row];
        if ([model.identifity isEqualToString:@"JDISVKSASettlementAddressInfoNormalCell"]){
            if (ISPresell && ISFirstMoenyFlag == NO)
                //付尾款不可更改地址
                return;
            if (SettlementNetService.isVistor)
                //访客结算不可更改地址
                return;
            // 点击弹出切换地址
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorAddressfloorClickChangeAddressAction"];
            NSDictionary *consigneeAddressMap = [self.viewModel.commonModel.commonData objectForKey:@"consigneeAddressMap"] ? : @{};
            JDISVSettlementAddressFloorAddressModel *addressModel = [JDISVSettlementAddressFloorAddressModel yy_modelWithDictionary:consigneeAddressMap];
            action.value = addressModel.addressId;
            [self isv_sendAction:action];
//            NSDictionary *consigneeAddressMap = [self.viewModel.commonModel.commonData objectForKey:@"consigneeAddressMap"] ? : @{};
//            JDISVSettlementAddressFloorAddressModel *addressModel = [JDISVSettlementAddressFloorAddressModel yy_modelWithDictionary:consigneeAddressMap];
//            NSNumber *addressId = [addressModel.addressId jdcd_toNumber];
//            [self changeAddressWithSelectedId:addressId controller:controller];
        }
    }
}

- (void)processOrderDelegateActionWithParames:(NSDictionary *)parames {
    NSString *cellIdentifier = [parames objectForKey:@"cell_identifier"];
    if ([cellIdentifier isEqualToString:@"JDISVKSASettlementAddressStepCell"]) {
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAddressFloorClickVistorModifyAddressAction"];
        [self isv_sendAction:action];
    }
}


// 切换地址
- (void)changeAddressWithSelectedId:(NSNumber *)addressId controller:(UIViewController *)controller {
    
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
    [arg addEntriesFromDictionary:@{@"canSelect": @1}];
    
    if (addressId) {
        [arg addEntriesFromDictionary:@{@"addressId": addressId}];
    }
    
    NSString *addressListViewControllerURL = [NSString stringWithFormat:@"router://%@/addressListViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
    [JDRouter openURL:addressListViewControllerURL arg:[arg copy] error:nil completion:^(NSDictionary *object) {
        UIViewController *vc = [object objectForKey:@"viewController"];
        if (vc) {
            [controller.navigationController pushViewController:vc animated:YES];
        }
        JDISVAddress *selectedAddress= [object objectForKey:@"info"];
        if (selectedAddress) {
            // code
            [PlatformService setDefaultAddress:selectedAddress];
            [self loadSaveAddress:selectedAddress controller:controller];
        }
    }];
}

- (void)loadSaveAddress:(JDISVAddress *)address controller:(UIViewController *)controller{
    @weakify(self)
    [PlatformService showLoadingInView:controller.view];
    [[self.viewModel loadSaveConsigneeAddressId:[address.addressID stringValue]] subscribeNext:^(NSDictionary *data) {
            @strongify(self)
            [PlatformService dismissInView:controller.view];
            [PlatformService setDefaultAddress:address];
            [self sendRefreshListActionWithData:data];
        } error:^(NSError * _Nullable error) {
            [PlatformService dismissInView:controller.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        }];
}

#pragma mark - 发送Action
- (void)sendRefreshListActionWithData:(NSDictionary *)dictionary {
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVAddressFloorChangeAddressCompleteAction"];
    action.value = dictionary;
    [self isv_sendAction:action];
}

@end
