//
//  JDISVKSASettlementAddressBaseCell.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chen<PERSON><PERSON><PERSON> on 2023/6/1.
//

#import "JDISVKSASettlementAddressBaseCell.h"

@implementation JDISVKSASettlementAddressBaseCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}


- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.contentView.backgroundColor = [UIColor clearColor];
        self.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)configDataModel:(JDISVKSASettlementAddressBaseViewModel *)model{
    
}

- (RACSubject *)delegate {
    if (_delegate == nil) {
        _delegate = [[RACSubject alloc] init];
    }
    
    return _delegate;
}
@end
