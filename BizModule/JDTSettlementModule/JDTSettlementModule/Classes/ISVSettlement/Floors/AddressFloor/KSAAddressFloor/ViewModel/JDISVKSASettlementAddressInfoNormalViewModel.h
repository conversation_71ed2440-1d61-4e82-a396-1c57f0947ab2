//
//  JDISVKSASettlementAddressInfoNormalViewModel.h
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/1.
//

#import <Foundation/Foundation.h>
#import "JDISVKSASettlementAddressBaseViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface JDISVKSASettlementAddressInfoNormalViewModel : JDISVKSASettlementAddressBaseViewModel

@property (strong,nonatomic) NSAttributedString *nameAttributed;
@property (assign,nonatomic) CGFloat nameLabelWidth;
@property (strong,nonatomic) NSAttributedString *phoneAttributed;
@property (assign,nonatomic) CGFloat phoneLabelWidth;
@property (strong,nonatomic) NSAttributedString *defalutTagAttributed;
@property (assign,nonatomic) CGFloat defalutTagWidth;
@property (strong,nonatomic) NSAttributedString *addressTagAttributed;
@property (assign,nonatomic) CGFloat addressTagleading;
@property (assign,nonatomic) CGFloat addressTagWidth;
@property (strong,nonatomic) NSAttributedString *areAddressAttributed; // 废弃
@property (assign,nonatomic) CGFloat areAddressHeight; // 废弃
@property (strong,nonatomic) NSAttributedString *detailAddressAttributed;
@property (assign,nonatomic) CGFloat detailAddressHeight;
@property (assign,nonatomic) UIImage *arrowImg;

@end

NS_ASSUME_NONNULL_END
