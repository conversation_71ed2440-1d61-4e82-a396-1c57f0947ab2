//
//  JDISVKSASettlementAddressInfoNormalCell.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/1.
//

#import "JDISVKSASettlementAddressInfoNormalCell.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVKSASettlementAddressInfoNormalViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVKSASettlementAddressInfoNormalCell ()
///背景View
@property (strong,nonatomic) UIView *cusTomBgView;
///名字
@property (strong,nonatomic) UILabel *nameLabel;
///默认标签
@property (strong,nonatomic) UILabel *defauleTagLabel;
///地址标签
@property (strong,nonatomic) UILabel *addressTagLabel;
/// 手机号
@property (strong,nonatomic) UILabel *phoneLabel;
///详细地址
@property (strong,nonatomic) UILabel *detailAddressLabel;
///右箭头
@property (strong,nonatomic) UIImageView *arrowRight;

@end

@implementation JDISVKSASettlementAddressInfoNormalCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        [self.contentView addSubview: self.cusTomBgView];
        
        [self.cusTomBgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(18);
            make.trailing.mas_equalTo(self.contentView).mas_offset(-18);
        }];
        
        
        [self.cusTomBgView addSubview:self.nameLabel];
        [self.cusTomBgView addSubview:self.defauleTagLabel];
        [self.cusTomBgView addSubview:self.addressTagLabel];
        [self.cusTomBgView addSubview:self.phoneLabel];
        [self.cusTomBgView addSubview:self.detailAddressLabel];
        [self.cusTomBgView addSubview:self.arrowRight];
        if (SettlementNetService.isVistor){
            self.arrowRight.hidden = YES;//访客结算不可更改地址
        }
        
        [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.cusTomBgView).mas_offset(12);
            make.leading.mas_equalTo(self.cusTomBgView).mas_offset(12);
            make.height.mas_offset(22.5);
            make.width.mas_lessThanOrEqualTo(@(100));
        }];
        
        [self.addressTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.nameLabel);
            make.leading.mas_equalTo(self.nameLabel.mas_trailing).mas_offset(12);
            make.width.mas_offset(43.f);
            make.height.mas_offset(18.f);
        }];
        
        [self.defauleTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.nameLabel);
            make.leading.mas_equalTo(self.addressTagLabel.mas_trailing).mas_offset(12);
            make.width.mas_offset(43.f);
            make.height.mas_equalTo(self.addressTagLabel);
        }];
        
        [self.phoneLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.nameLabel.mas_bottom).mas_offset(5);
            make.leading.mas_equalTo(self.cusTomBgView).mas_offset(12);
            make.trailing.mas_equalTo(self.cusTomBgView).mas_offset(-32);
            make.height.mas_offset(22.5f);
        }];
        
        [self.detailAddressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.phoneLabel.mas_bottom).mas_offset(5);
            make.leading.mas_equalTo(self.phoneLabel);
            make.trailing.mas_equalTo(self.phoneLabel);
            make.height.mas_offset(17);
            make.bottom.mas_equalTo(self.cusTomBgView).mas_offset(-12);
        }];
        
        [self.arrowRight mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.cusTomBgView);
            make.trailing.mas_equalTo(self.cusTomBgView).mas_offset(-12);
            make.size.mas_offset(CGSizeMake(12, 12));
        }];
        
//        高度计算
//        0/*背景与Cell顶部间距*/ + 12/*顶部间距*/ + 22.5/*名字高度*/ + 5/*名字和地址间距*/ + areAddressStringHeight/*区域地址高度*/ + 5/**区域地址与详细地址间距*/ + detailAddressStringHeight/*详细地址高度*/ + 12/*底部间距*/ + 6.f/*背景与cell底部间距*/
//        区域地址与详细地址最大显示宽度
//        [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f)/*楼层左右间距合*/ - 36/*背景左右间距合*/ - 12.f/*背景内容左间距合*/ - 32/*背景内容右间距*/
    }
    return self;
}

#pragma mark - <getter>

- (UIView *)cusTomBgView{
    if (!_cusTomBgView){
        _cusTomBgView = [[UIView alloc] init];
        _cusTomBgView.clipsToBounds = YES;
        _cusTomBgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R4"];
        _cusTomBgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.05];
    }
    return _cusTomBgView;
}

- (UILabel *)nameLabel{
    if (!_nameLabel){
        _nameLabel = [[UILabel alloc] init];
//        _nameLabel.backgroundColor = [UIColor grayColor];
    }
    return _nameLabel;
}

- (UILabel *)defauleTagLabel{
    if (!_defauleTagLabel){
        _defauleTagLabel = [[UILabel alloc] init];
        _defauleTagLabel.textAlignment = NSTextAlignmentCenter;
//        _defauleTagLabel.backgroundColor = [UIColor grayColor];
        _defauleTagLabel.clipsToBounds = YES;
        _defauleTagLabel.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        _defauleTagLabel.layer.borderWidth = 0.5f;
        _defauleTagLabel.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"].CGColor;
    }
    return _defauleTagLabel;
}

- (UILabel *)addressTagLabel{
    if (!_addressTagLabel){
        _addressTagLabel = [[UILabel alloc] init];
        _addressTagLabel.textAlignment = NSTextAlignmentCenter;
//        _addressTagLabel.backgroundColor = [UIColor grayColor];
        _addressTagLabel.clipsToBounds = YES;
        _addressTagLabel.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        _addressTagLabel.layer.borderWidth = 0.5f;
        _addressTagLabel.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"].CGColor;
    }
    return _addressTagLabel;
}

- (UILabel *)phoneLabel{
    if (!_phoneLabel){
        _phoneLabel = [[UILabel alloc] init];
        _phoneLabel.numberOfLines = 0;
//        _phoneLabel.backgroundColor = [UIColor grayColor];
    }
    return _phoneLabel;
}

- (UILabel *)detailAddressLabel{
    if (!_detailAddressLabel){
        _detailAddressLabel = [[UILabel alloc] init];
        _detailAddressLabel.numberOfLines = 0;
//        _detailAddressLabel.backgroundColor = [UIColor grayColor];
    }
    return _detailAddressLabel;
}

- (UIImageView *)arrowRight{
    if (!_arrowRight){
        _arrowRight = [[UIImageView alloc] init];
        _arrowRight.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        
    }
    return _arrowRight;
}

- (void)configDataModel:(JDISVKSASettlementAddressBaseViewModel *)model{
    if ([model isKindOfClass:[JDISVKSASettlementAddressInfoNormalViewModel class]]){
        JDISVKSASettlementAddressInfoNormalViewModel *data = (JDISVKSASettlementAddressInfoNormalViewModel *)model;
        self.nameLabel.attributedText = data.nameAttributed;
        self.defauleTagLabel.attributedText = data.defalutTagAttributed;
        self.addressTagLabel.attributedText = data.addressTagAttributed;
        self.phoneLabel.attributedText = data.phoneAttributed;
        self.detailAddressLabel.attributedText = data.detailAddressAttributed;
        
        
        CGFloat maxWidth = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36 - 12 - 36 - data.addressTagWidth - data.defalutTagWidth - 12 - 12;
        
        [self.nameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_lessThanOrEqualTo(@(maxWidth));
        }];
        
        [self.addressTagLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_offset(data.addressTagWidth);
        }];
        
        [self.defauleTagLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_offset(data.defalutTagWidth);
            make.leading.mas_equalTo(self.addressTagLabel.mas_trailing).mas_offset(data.addressTagleading);
        }];
        
//        [self.phoneLabel mas_updateConstraints:^(MASConstraintMaker *make) {
//            make.height.mas_offset(22.5);
//        }];
        CGFloat top = data.detailAddressHeight > 0 ? 5.f : 0.f;
        [self.detailAddressLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.phoneLabel.mas_bottom).mas_offset(top);
            make.height.mas_offset(data.detailAddressHeight);
        }];
    }
}

@end
