//
//  JDISVKSASettlementAddressNotesCell.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/1.
//

#import "JDISVKSASettlementAddressNotesCell.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVKSASettlementAddressNotesViewModel.h"

@interface JDISVKSASettlementAddressNotesCell ()

@property (strong,nonatomic) UILabel *noteLabel;

@end

@implementation JDISVKSASettlementAddressNotesCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        [self.contentView addSubview: self.noteLabel];

        [self.noteLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(18.f);
            make.trailing.mas_equalTo(self.contentView).mas_offset(-18.f);
        }];
        
//        高度计算
//        noteLabelStringHeight
    }
    return self;
}

- (UILabel *)noteLabel{
    if (!_noteLabel){
        _noteLabel = [[UILabel alloc] init];
        _noteLabel.backgroundColor = [UIColor clearColor];
        _noteLabel.numberOfLines = 0;
    }
    return _noteLabel;
}

- (void)configDataModel:(JDISVKSASettlementAddressBaseViewModel *)model{
    if ([model isKindOfClass:[JDISVKSASettlementAddressNotesViewModel class]]){
        JDISVKSASettlementAddressNotesViewModel *data = model;
        self.noteLabel.attributedText = data.noteAttributed;
    }
}

@end
