//
//  JDISVKSASettlementAddressFloorModule.h
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu12 on 2023/6/1.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@class RACSignal;

@interface JDISVKSASettlementAddressFloorModule : NSObject<JDISVFloorModuleProtocol>

@property (strong,nonatomic) NSAttributedString *stepNumberAttributed;//步骤数字
@property (strong,nonatomic) NSAttributedString *stepTitleAttributed;///步骤文本

@property (nonatomic, copy, nullable) NSAttributedString *areaAttributedString; /**< 区域地址 */
@property (nonatomic, copy, nullable) NSAttributedString *detailAddressAttributedString; /**< 详细地址 */
@property (nonatomic, assign) CGFloat detailAddressLabelHeight;
@property (nonatomic, assign) CGFloat addressLabelHeight;
@property (nonatomic, copy, nullable) NSAttributedString *nameAttribtuedString;
@property (nonatomic, assign) CGFloat nameLabelWidth;
@property (nonatomic, copy, nullable) NSAttributedString *mobileAttributedString;

@property (nonatomic, copy, nullable) NSAttributedString *tipLabelAttributedString;
@property (nonatomic, assign) CGFloat tipLabelHeight;
@property (nonatomic, strong) UIColor *tipBgViewColor;
@property (nonatomic, assign) CGFloat tipBgViewHeight;
@property (nonatomic, assign) CGFloat tipBgViewCorners;

@property (nonatomic, strong) JDISVFloorCommonModel *commonModel;

@property (nonatomic, assign) BOOL isLoc; // 是否是本地生活结算页(默认为NO)

@property (strong,nonatomic) NSMutableArray *ksa_addressInfoCellViewModels;

// 保存收货地址
- (RACSignal *)loadSaveConsigneeAddressId:(NSString *)addressId;

@end

NS_ASSUME_NONNULL_END
