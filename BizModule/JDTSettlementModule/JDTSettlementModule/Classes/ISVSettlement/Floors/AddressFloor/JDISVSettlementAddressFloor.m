//
//  JDISVSettlementAddressFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/8.
//

#import "JDISVSettlementAddressFloor.h"
#import "JDISVSettlementAddressFloorModule.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>

#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import "JDISVSettlementAddressFloorAddressModel.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
@interface JDISVSettlementAddressFloor () <JDCDISVActionTransferProtocol>
@property (weak, nonatomic) IBOutlet UILabel *areaAddressLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *areaAddressLabelHeight;
@property (weak, nonatomic) IBOutlet UILabel *detailAddressLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *detailAddressLabelHeight;
@property (weak, nonatomic) IBOutlet UILabel *nameLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *nameLabelWidth;
@property (weak, nonatomic) IBOutlet UILabel *mobileLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *mobileLabelLeading;

@property (weak, nonatomic) IBOutlet UIImageView *rightArrow;

@property (weak, nonatomic) IBOutlet UIView *tipBgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tipBgViewHeight;
@property (weak, nonatomic) IBOutlet UILabel *tipBgLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tipBgLabelHeight;

@property (weak, nonatomic) IBOutlet UIImageView *separatorLine;

@property (nonatomic, strong) JDISVSettlementAddressFloorModule *viewModel;

@property (nonatomic,strong) UIView *firstMoneyView;
@property (nonatomic,strong) UILabel *firstMoneyLabel;

@end

@implementation JDISVSettlementAddressFloor

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.layer.masksToBounds = YES;
    self.contentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
    self.contentView.layer.masksToBounds = YES;
    
    self.rightArrow.contentMode = UIViewContentModeCenter;
    self.rightArrow.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    
    self.separatorLine.contentMode = UIViewContentModeScaleAspectFill;
    self.separatorLine.image = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDecorateLine];
    
    UIView *firstMoneyview = [[UIView alloc]init];
    firstMoneyview.layer.masksToBounds = YES;
    firstMoneyview.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    firstMoneyview.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07f];
    [self.contentView addSubview:firstMoneyview];
    [firstMoneyview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-18);
        make.trailing.equalTo(self.contentView).offset(-18);
        make.leading.equalTo(self.contentView).offset(18);
        make.height.mas_equalTo(@28);
    }];
    self.firstMoneyView = firstMoneyview;
    
    self.firstMoneyLabel = [[UILabel alloc]init];
    self.firstMoneyLabel.text = [NSString stringWithFormat:SettlementLan(@"%@"),SettlementLan(@"checkout_address_pre_sale_tips")] ;
    [firstMoneyview addSubview:self.firstMoneyLabel];
    self.firstMoneyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
    self.firstMoneyLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightRegular];
    [self.firstMoneyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(firstMoneyview);
        make.trailing.equalTo(firstMoneyview).offset(-12);
        make.leading.equalTo(firstMoneyview).offset(12);
        make.top.equalTo(firstMoneyview);
    }];
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
    // 更新cell视图
    if (_viewModel.areaAttributedString) {
        self.areaAddressLabel.attributedText = _viewModel.areaAttributedString;
        self.areaAddressLabelHeight.constant = _viewModel.addressLabelHeight;
    } else {
        self.areaAddressLabelHeight.constant = 0.f;
    }
    
    if (_viewModel.detailAddressAttributedString) {
        self.detailAddressLabel.attributedText = _viewModel.detailAddressAttributedString;
        self.detailAddressLabelHeight.constant = _viewModel.detailAddressLabelHeight;
    } else {
        self.detailAddressLabelHeight.constant = 0.f;
    }
    
    if (_viewModel.nameAttribtuedString) {
        self.nameLabel.attributedText = _viewModel.nameAttribtuedString;
        self.nameLabelWidth.constant = _viewModel.nameLabelWidth;
    } else {
        self.nameLabelWidth.constant = 0.f;
    }
    
    if (_nameLabelWidth.constant > 0) {
        self.mobileLabelLeading.constant = _viewModel.nameLabelWidth + 6.f + 18.f;
    } else {
        self.mobileLabelLeading.constant = 18.f;
    }
    if (_viewModel.mobileAttributedString) {
        self.mobileLabel.attributedText = _viewModel.mobileAttributedString;
    }
    
    if (_viewModel.tipBgViewHeight != 0) {
        self.tipBgView.hidden = NO;
        self.tipBgLabel.hidden = NO;
        
        self.tipBgView.backgroundColor = _viewModel.tipBgViewColor;
        self.tipBgView.layer.masksToBounds = YES;
        self.tipBgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        self.tipBgViewHeight.constant = _viewModel.tipBgViewHeight;
        
        self.tipBgLabel.attributedText = _viewModel.tipLabelAttributedString;
        self.tipBgLabelHeight.constant = _viewModel.tipLabelHeight;
        self.tipBgLabel.textAlignment = NSTextAlignmentLeft;
        
    } else {
        self.tipBgView.hidden = YES;
        self.tipBgLabel.hidden = YES;
    }
    
    if (ISPresell) {
        if (ISFirstMoenyFlag) {//付定金
            [self.firstMoneyView setHidden:NO];
            self.rightArrow.hidden = NO;
        }else{//付尾款
            [self.firstMoneyView setHidden:YES];
            self.rightArrow.hidden = YES;//付尾款不可更改地址
        }
    }else {//非预售
        [self.firstMoneyView setHidden:YES];
        self.rightArrow.hidden = NO;
    }
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    
    if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]) {
        id<JDISVFloorModuleProtocol> module = (id<JDISVFloorModuleProtocol>)action.value;
        if ([module isKindOfClass:JDISVSettlementAddressFloorModule.class]) {
            if (ISPresell && ISFirstMoenyFlag == NO)
                //付尾款不可更改地址
                return YES;
            
            // 点击弹出切换地址
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorAddressfloorClickChangeAddressAction"];
            NSDictionary *consigneeAddressMap = [self.viewModel.commonModel.commonData objectForKey:@"consigneeAddressMap"] ? : @{};
            JDISVSettlementAddressFloorAddressModel *addressModel = [JDISVSettlementAddressFloorAddressModel yy_modelWithDictionary:consigneeAddressMap];
            action.value = addressModel.addressId;
            [self isv_sendAction:action];
//            NSDictionary *consigneeAddressMap = [self.viewModel.commonModel.commonData objectForKey:@"consigneeAddressMap"] ? : @{};
//            JDISVSettlementAddressFloorAddressModel *addressModel = [JDISVSettlementAddressFloorAddressModel yy_modelWithDictionary:consigneeAddressMap];
//            NSNumber *addressId = [addressModel.addressId jdcd_toNumber];
//            [self changeAddressWithSelectedId:addressId controller:controller];
        }
        return YES;
    }
    
    return NO;
}

// 切换地址
- (void)changeAddressWithSelectedId:(NSNumber *)addressId controller:(UIViewController *)controller {
    
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
    [arg addEntriesFromDictionary:@{@"canSelect": @1}];
    
    if (addressId) {
        [arg addEntriesFromDictionary:@{@"addressId": addressId}];
    }
    
    NSString *addressListViewControllerURL = [NSString stringWithFormat:@"router://%@/addressListViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
    [JDRouter openURL:addressListViewControllerURL arg:[arg copy] error:nil completion:^(NSDictionary *object) {
        UIViewController *vc = [object objectForKey:@"viewController"];
        if (vc) {
            [controller.navigationController pushViewController:vc animated:YES];
        }
        JDISVAddress *selectedAddress= [object objectForKey:@"info"];
        if (selectedAddress) {
            // code
            [PlatformService setDefaultAddress:selectedAddress];
            [self loadSaveAddress:selectedAddress controller:controller];
        }
    }];
}

- (void)loadSaveAddress:(JDISVAddress *)address controller:(UIViewController *)controller{
    @weakify(self)
    [PlatformService showLoadingInView:controller.view];
    [[self.viewModel loadSaveConsigneeAddressId:[address.addressID stringValue]] subscribeNext:^(NSDictionary *data) {
            @strongify(self)
            [PlatformService dismissInView:controller.view];
            [PlatformService setDefaultAddress:address];
            [self sendRefreshListActionWithData:data];
        } error:^(NSError * _Nullable error) {
            [PlatformService dismissInView:controller.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        }];
}

#pragma mark - 发送Action
- (void)sendRefreshListActionWithData:(NSDictionary *)dictionary {
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVAddressFloorChangeAddressCompleteAction"];
    action.value = dictionary;
    [self isv_sendAction:action];
}

@end
