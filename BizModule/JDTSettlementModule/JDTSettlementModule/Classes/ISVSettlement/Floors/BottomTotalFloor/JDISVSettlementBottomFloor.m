//
//  JDISVSettlementBottomFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import "JDISVSettlementBottomFloor.h"
#import "JDISVSettlementController.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import "JDISVSettlementBottomFloorMoudle.h"

#import "JDISVSettlementOrderSuccessController.h"
#import "JDISVSettlementOrderStockOutSkuViewModel.h"
#import "JDISVSettlementOrderStockOutSkuCell.h"

#import "NSBundle+JDISVSettlement.h"
#import "JDISVSettlementTracker.h"

#import "JDISVServingTrayModel.h"
#import "JDISVServingTrayViewController.h"

@interface JDISVSettlementBottomFloor ()<JDCDISVActionTransferProtocol>
@property (weak, nonatomic) IBOutlet UILabel *amountTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *amountLabel;
@property (weak, nonatomic) IBOutlet UIButton *submitButton;
//@property (nonatomic,strong) NSMutableArray *skuids;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *buttonWidth;
@property (nonatomic, strong) JDISVSettlementBottomFloorMoudle *viewModel;

@end

@implementation JDISVSettlementBottomFloor

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setup];

    }
    return self;
}

//- (void)skuSetData:(NSNotification *)fication{
//    self.skuids = fication.object;
//    NSLog(@"");
//}

- (void)setup {
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self updateWithViewModel:_viewModel];
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(skuSetData:) name:@"skuSetData" object:nil];
}

- (void)updateWithViewModel:(JDISVSettlementBottomFloorMoudle *)viewModel {
    self.amountTitleLabel.attributedText = viewModel.titleAttributedString;
    if (viewModel.amountAttributedString) {
        self.amountLabel.hidden = NO;
        self.amountLabel.attributedText = viewModel.amountAttributedString;
    } else {
        self.amountLabel.hidden = YES;
    }
    
    [self.submitButton renderB2];
    [self configBtn:self.submitButton title:SettlementLan(@"checkout_submit")];
    [self.submitButton addTarget:self action:@selector(submitAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)configBtn:(UIButton *)button title:(NSString *)title {
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitle:title forState:UIControlStateHighlighted];
    [button setTitle:title forState:UIControlStateDisabled];
    CGFloat width = [button jdcd_getSizeOfWidth:MAXFLOAT height:40].width + 20.f;
    width = width < 100 ? 100.f : width;
    self.buttonWidth.constant = width;
}

- (void)submitAction {
    //如果首页的isagree == false 就发出弹框 否则就按照原来的走
    NSString *isAgree = self.viewModel.commonModel.commonData[@"depositSwitchOpen"];
    if (isAgree && isAgree.boolValue == NO) {
        @weakify(self)
        [KAAlert alert].config
            .renderW3a(SettlementLan(@"checkout_dialog_pre_sale_title") , NSTextAlignmentCenter)
            .ksa_addLineAction(SettlementLan(@"checkout_dialog_pre_sale_negative"), ^{
                
            })
            .ksa_addFillAction(SettlementLan(@"checkout_dialog_pre_sale_positive") , ^{
                @strongify(self)
                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorSubmitAction"];
                [self isv_sendAction:action];
            })
            .alertShow();
        return;
    }
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorSubmitAction"];
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    
    if ([action.actionType isEqualToString:@"JDISVSettlementBottomFloorSubmitAction"]) {
        // 提交订单操作
        [self invokeSubmitAction:controller];
        return YES;
    }
    
    return NO;
}

- (void)invokeSubmitAction:(UIViewController *)controller {
    
    if (self.viewModel.payAmountAndCODCheck){// 0元单，不支持COD
        @weakify(self)
        [KAAlert alert].config
            .renderW3(SettlementLan(@"checkout_payment_type_error") )
            .addFillAction(SettlementLan(@"checkout_pre_sale_dialog_confirm") , ^{
                @strongify(self)
                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVPaymentTypeChangedAction"];
                action.value = @{@"paymentId":@(4)};
                [self isv_sendAction:action];
            })
            .alertShow();
        return;
    }
    @weakify(self)
        __block NSString *amountStr = nil;
        __block NSString *orderIdStr = nil;

        [PlatformService showLoadingInView:controller.view];
        [[[self.viewModel loadSubmitOrderSignal] doNext:^(NSDictionary*  orderInfo) {
            NSString *orderId = [orderInfo objectForKey:@"orderId"];
            NSString* amount = self.viewModel.amountAttributedString.string;
            amount = [amount stringByReplacingOccurrencesOfString:@"¥" withString:@""];
            amount = [amount stringByReplacingOccurrencesOfString:@"￥" withString:@""];
            amount = [amount stringByReplacingOccurrencesOfString:@"" withString:@""];
            amountStr = amount;
            orderIdStr = orderId;
        }]
        subscribeNext:^(NSDictionary *orderInfo) {
            @strongify(self)
            [PlatformService dismissInView:controller.view];
            NSNumber *resultType = [orderInfo objectForKey:@"resultType"];
            NSString *orderId = [orderInfo objectForKey:@"orderId"];
            if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeZeroOrder) {
                // 0元单
                //提单成功后绑定订单和发票关系
                [self processOrderAndSelectedInvoiceActionWithOrderId:orderId controller:controller];
                JDISVSettlementOrderSuccessController *orderSuccessController = [[JDISVSettlementOrderSuccessController alloc] initWithNibName:@"JDISVSettlementOrderSuccessController" bundle:[NSBundle jdisvSettlement_bundle]];
                [orderSuccessController updateWithOrderId:orderId];
                [controller.navigationController pushViewController:orderSuccessController animated:YES];
                
                [self submitSuccessRefreshShoppingCart];
                [self deleteCasheCouponCode];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypePay) {
                NSString *idPaymentType = [orderInfo jdcd_getStringElementForKey:@"idPaymentType"];
                if ([idPaymentType isEqualToString:@"1"]){//COD支付
                    //跳转COD提单成功页
                    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
                    NSString* routerStr= [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
                    NSMutableString *url = [PlatformService getJumpH5UrlByType:JDISVJumpH5CODSuccess withAppendParam:@""];
                    [JDRouter openURL:routerStr arg:@{@"text_url":[NSString stringWithFormat:@"%@?successFlag=true&cod=true&orderId=%@",url,orderId],@"openOrderListForClickBackBtn":@(YES)} error:nil completion:^(id obj){
                        [controller.navigationController pushViewController:obj animated:YES];
                    }];
                }else{
                    //跳转收银台
                    NSString *jsonstr = [orderInfo objectForKey:@"payUrl"];
                    NSDictionary *jsonDic = [JDISVSettlementBottomFloor dictionaryWithJsonString:jsonstr];
                    NSString *payUrl = [jsonDic objectForKey:@"httpUrl"];
                    NSDictionary* param = @{@"text_url":payUrl ? : @"", @"openOrderListForClickBackBtn":@(YES)};
                    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
                    NSString* routerStr= [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
                    [JDRouter openURL:routerStr arg:param error:nil completion:^(id obj){
                        [controller.navigationController pushViewController:obj animated:YES];
                    }];
                    
                    //提单成功后绑定订单和发票关系
                    [self processOrderAndSelectedInvoiceActionWithOrderId:orderId controller:controller];
                    
                    //删除结算页
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [self removeCurrentController:controller];
                    });
                }
                if (KSAAPP){
                    //埋点
                    NSDictionary *eventParam = @{@"page_id":@"SettleAccounts_OrderNew",
                                                 @"orderid":orderIdStr?:@"",
                                                 @"paytype":[idPaymentType isEqualToString:@"1"] ? @(2) : @(1),
                                                 @"amount":amountStr?:@"",
                                                 @"skuId":self.viewModel.commonModel.commonData[@"skuids_mta"] ? : @[]
                    };
                    [JDISVSettlementTracker Event:@"Neworder_Submit"
                                           param:eventParam];
                }else{
                    //埋点
                    NSDictionary *eventParam = @{@"page_id":@"SettleAccounts_OrderNew",
                                                 @"orderid":orderIdStr?:@"",
                                                 @"paytype":SettlementLan(@"checkout_pay_online"),
                                                 @"amount":amountStr?:@"",
                                                 @"skuId":self.viewModel.commonModel.commonData[@"skuids_mta"] ? : @[]
                    };
                    [JDISVSettlementTracker Event:@"Neworder_Submit"
                                           param:eventParam];
                }
                
                [self submitSuccessRefreshShoppingCart];
                [self deleteCasheCouponCode];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeStockOutAll) {
                // 全部无货
                NSArray *productParams = [orderInfo objectForKey:@"stockoutParam"];
                [self processAllStockOutActionWithProductParam:productParams controller:controller];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeStockOutPart) {
                // 部分无货
                NSArray *productViewModels = [orderInfo objectForKey:@"stockoutSkuViewModelArray"];
                [self processPartStockOutActionWithProductParam:productViewModels controller:controller];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeGroupBuyError) {
                // 拼团异常
                NSString *message = [orderInfo objectForKey:@"message"];
                [self processGroupBuyErrorActionWithMessage:message controller:controller];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypePinLimitBuy) {
                // pin限购
                NSString *message = [orderInfo objectForKey:@"message"];
                [self processLimitBuyActionWithMessage:message limitType:JDISVSettlementBottomFloorMoudleSubmitResultTypePinLimitBuy controller:controller];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeMinLimitBuy) {
                // 最小限购
                NSString *message = [orderInfo objectForKey:@"message"];
                [self processLimitBuyActionWithMessage:message limitType:JDISVSettlementBottomFloorMoudleSubmitResultTypeMinLimitBuy controller:controller];
            } else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeMaxLimitBuy) {
                // 最大限购
                NSString *message = [orderInfo objectForKey:@"message"];
                [self processLimitBuyActionWithMessage:message limitType:JDISVSettlementBottomFloorMoudleSubmitResultTypeMaxLimitBuy controller:controller];
            }else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeCodeUnUse){
                //TODO: KSA二期功能 需要新增优惠码不可用逻辑
            }else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeSomeLimit){//托盘商品
                [self servingTrayQuota:[orderInfo objectForKey:@"partList"] controller:controller];
            }else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeAllLimit){
                [self servingTrayQuota:nil controller:controller];
            }else if ([resultType integerValue] == JDISVSettlementBottomFloorMoudleSubmitResultTypeCODPaymentError){//COD 提单报错
                JDWeakSelf
                NSString *message = [orderInfo objectForKey:@"message"];
                [KAAlert alert].config.renderW3a(message, NSTextAlignmentCenter).addFillAction(SettlementLan(@"checkout_pre_sale_dialog_confirm"), ^{
                    JDStrongSelf
                    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVPaymentTypeChangedAction"];
                    action.value = @{@"paymentId":@(4)};
                    [self isv_sendAction:action];
                }).jdcd_clickBackgroundClose(NO)
                    .alertShow();
            }
        } error:^(NSError * _Nullable error) {
                @strongify(self)
            [PlatformService dismissInView:controller.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        }];
}

- (void)processOrderAndSelectedInvoiceActionWithOrderId:(NSString *)orderId controller:(UIViewController *)controller{
    [[[self.viewModel bindOrderAndSelectInvoiceSignalWithOrderId:orderId] doNext:^(NSDictionary*  orderInfo) {
        
    }]
     subscribeNext:^(NSDictionary *orderInfo) {
        
    } error:^(NSError * _Nullable error) {
        
    }];
}

///删除缓存优惠码
- (void)deleteCasheCouponCode{
    NSString *useCouponCode = [JDISVSettlementAmountFloorNetService sharedService].submitUseCouponCode;
    if ([useCouponCode jdcd_validateString]){//该单使用了优惠码
        if ([useCouponCode isEqualToString:[JDISVCouponTagModel loadPromotionCode]]){//如果是使用的缓存优惠码
            [JDISVCouponTagModel deletePromotionCode];//删除缓存优惠码
        }
    }
}

/// 提单成功通知刷新购物车
- (void)submitSuccessRefreshShoppingCart{
    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShoppingCart ];
    NSString* routerStr= [NSString stringWithFormat:@"router://%@/forceGetCartCount",WebModule];
    [JDRouter openURL:routerStr arg:nil error:nil completion:nil];
}


// 删除当前界面
- (void)removeCurrentController:(UIViewController *)controller{
    
    NSMutableArray *array = [NSMutableArray arrayWithArray:controller.navigationController.viewControllers];
    for (UIViewController *vc in array) {
        if ([vc isKindOfClass:NSClassFromString(@"JDISVSettlementController")]) {
            [array removeObject:vc];
            break;
        }
    }
    controller.navigationController.viewControllers = array;
}

//处理拼团异常
- (void)processGroupBuyErrorActionWithMessage:(NSString *)message controller:(UIViewController *)controller{
    @weakify(self)
    [KAAlert alert].config
        .renderW3(message)
        .addFillAction(SettlementLan(@"checkout_join_buy_back") , ^{
            @strongify(self)
            [controller.navigationController popViewControllerAnimated:YES];
        })
        .alertShow();
}

/// 处理全部无货
- (void)processAllStockOutActionWithProductParam:(NSArray *)products controller:(UIViewController *)controller{
    if (!SettlementNetService.isVistor){
        @weakify(self)
        [KAAlert alert].config
        .renderW3a(SettlementLan(@"checkout_all_out_stock_title"), NSTextAlignmentCenter)
        .ksa_addLineAction(SettlementLan(@"setllement_changed_address") , ^{
            @strongify(self)
            // 更新地址刷新 changeAddressWithSelectedId
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorSubmitActionChangeAddressAction"];
            action.value = [self addressIdInCommonModel];
            [self isv_sendAction:action];
        })
        .ksa_addFillAction(SettlementLan(@"setllement_delete_back") , ^{
            @strongify(self)
            if (self.viewModel.sourceType == 1) {
                // 商详进入
                [self removeStockoutSkuWith:products controller:controller completeBlock:^(BOOL success, NSString *message) {
                    if (success == NO) {
                        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:message];
                    }
                    [controller.navigationController popViewControllerAnimated:YES];
                }];
            } else {
                [controller.navigationController popViewControllerAnimated:YES];
            }
        })
        .alertShow();
    }else{//访客购物车
        @weakify(self)
        [KAAlert alert].config
        .renderW3a(SettlementLan(@"checkout_all_out_stock_title"), NSTextAlignmentCenter)
        .ksa_addFillAction(SettlementLan(@"setllement_delete_back") , ^{
            @strongify(self)
            if (self.viewModel.sourceType == 1) {
                // 商详进入
                [self removeStockoutSkuWith:products controller:controller completeBlock:^(BOOL success, NSString *message) {
                    if (success == NO) {
                        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:message];
                    }
                    [controller.navigationController popViewControllerAnimated:YES];
                }];
            } else {
                [controller.navigationController popViewControllerAnimated:YES];
            }
        })
        .alertShow();
    }
}


/// 处理部分无货
- (void)processPartStockOutActionWithProductParam:(NSArray *)productViewModels controller:(UIViewController *)controller {
    BOOL isHaveSubSkuStockOut = NO;
    //KSA 新接口调试，结算不再判断加价购子品无货，可直接在结算页删除无货商品
//    for (JDISVSettlementOrderStockOutSkuViewModel *stockoutSkuViewModel in productViewModels) {
//        if ([stockoutSkuViewModel.skuModelType isEqualToString:@"7"]) {
//            //加价购子品无货
//            isHaveSubSkuStockOut = YES;
//            break;
//        }
//    }
    
    //是否虚拟组套子品无货
    if ([self.viewModel checkHaveVirtualProductStockOutBySkuIds:productViewModels]) {
        isHaveSubSkuStockOut = YES;
    }
    
    if (isHaveSubSkuStockOut) {
        //有子品无货, 仅提示，不作反选或者删除逻辑
        @weakify(self)
        //list view
        __block UITableView *tempTableView;
        [KAAlert alert].config
        .ksaRenderW5List(SettlementLan(@"checkout_some_out_stock_title") , ^(UITableView * _Nonnull tableView) {
            tempTableView = tableView;
            //组册cell
            [tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"CELL"];
            [tableView registerClass:[JDISVSettlementOrderStockOutSkuCell class] forCellReuseIdentifier:@"JDISVSettlementOrderStockOutSkuCell"];
        }, ^UITableViewCell * _Nonnull(UITableView * _Nonnull tableView, NSInteger item) {
            //返回cell
            if (item < productViewModels.count) {
                JDISVSettlementOrderStockOutSkuViewModel *viewModel = [productViewModels objectAtIndex:item];
                JDISVSettlementOrderStockOutSkuCell *cell = [[[NSBundle jdisvSettlement_bundle] loadNibNamed:@"JDISVSettlementOrderStockOutSkuCell" owner:nil options:nil] firstObject];
                cell.selectionStyle = UITableViewCellSelectionStyleNone;
                [cell updateCellWithViewModel:viewModel];
                return cell;
            } else {
                return [UITableViewCell new];
            }
        }, ^NSInteger(UITableView * _Nonnull tableView) {
            //cell个数
            if (productViewModels) {
                return productViewModels.count;
            } else {
                return 0;
            }
            
        }, ^CGFloat(UITableView * _Nonnull tableView, NSInteger item) {
            if (item < productViewModels.count) {
                JDISVSettlementOrderStockOutSkuViewModel *viewModel = [productViewModels objectAtIndex:item];
                return viewModel.height;
            } else {
                return 0;
            }

        }, ^(UITableView * _Nonnull tableView, NSInteger item) {
            //cell点击
        })
        .addFillAction(SettlementLan(@"checkout_some_out_stock_positive_add") , ^{
            [controller.navigationController popViewControllerAnimated:YES];
        })
        .alertShow();
        return;
    }
    
    if (!SettlementNetService.isVistor){
        @weakify(self)
        //list view
        __block UITableView *tempTableView;
        [KAAlert alert].config
        .ksaRenderW5List(SettlementLan(@"checkout_some_out_stock_title") , ^(UITableView * _Nonnull tableView) {
            tempTableView = tableView;
            //组册cell
            [tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"CELL"];
            [tableView registerClass:[JDISVSettlementOrderStockOutSkuCell class] forCellReuseIdentifier:@"JDISVSettlementOrderStockOutSkuCell"];
        }, ^UITableViewCell * _Nonnull(UITableView * _Nonnull tableView, NSInteger item) {
            //返回cell
            if (item < productViewModels.count) {
                JDISVSettlementOrderStockOutSkuViewModel *viewModel = [productViewModels objectAtIndex:item];
                JDISVSettlementOrderStockOutSkuCell *cell = [[[NSBundle jdisvSettlement_bundle] loadNibNamed:@"JDISVSettlementOrderStockOutSkuCell" owner:nil options:nil] firstObject];
                cell.selectionStyle = UITableViewCellSelectionStyleNone;
                [cell updateCellWithViewModel:viewModel];
                return cell;
            } else {
                return [UITableViewCell new];
            }
        }, ^NSInteger(UITableView * _Nonnull tableView) {
            //cell个数
            if (productViewModels) {
                return productViewModels.count;
            } else {
                return 0;
            }
            
        }, ^CGFloat(UITableView * _Nonnull tableView, NSInteger item) {
            if (item < productViewModels.count) {
                JDISVSettlementOrderStockOutSkuViewModel *viewModel = [productViewModels objectAtIndex:item];
                return viewModel.height;
            } else {
                return 0;
            }

        }, ^(UITableView * _Nonnull tableView, NSInteger item) {
            //cell点击
        })
        .ksa_addLineAction(SettlementLan(@"checkout_some_out_stock_negative") , ^{
            @strongify(self)
            // 更新地址刷新 changeAddressWithSelectedId
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorSubmitActionChangeAddressAction"];
            action.value = [self addressIdInCommonModel];
            [self isv_sendAction:action];
        })
        .ksa_addFillAction(SettlementLan(@"checkout_some_out_stock_positive") , ^{
            //KSA使用新接口，删除无货商品
            [PlatformService showLoadingInView:controller.view];
            NSMutableArray *productSkuArray = [[NSMutableArray alloc] init];
            for (JDISVSettlementOrderStockOutSkuViewModel *stockoutSkuViewModel in productViewModels) {
                NSDictionary *sku = @{@"sku":stockoutSkuViewModel.skuId,@"num":stockoutSkuViewModel.skuCount
                };
                [productSkuArray addObject:sku];
            }
            NSMutableDictionary *arg = [NSMutableDictionary dictionary];
            [arg setValue:productSkuArray forKey:@"skus"];
            if (self.viewModel.sourceType == 1) {//从商祥进入的立即购买
                [arg setValue:@(7) forKey:@"refer"];
            }
            [self ksa_newDeleteProductWith:arg controller:controller];
        })
        .alertShow();
    }else{//访客结算页
        @weakify(self)
        //list view
        __block UITableView *tempTableView;
        [KAAlert alert].config
        .ksaRenderW5List(SettlementLan(@"checkout_some_out_stock_title") , ^(UITableView * _Nonnull tableView) {
            tempTableView = tableView;
            //组册cell
            [tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"CELL"];
            [tableView registerClass:[JDISVSettlementOrderStockOutSkuCell class] forCellReuseIdentifier:@"JDISVSettlementOrderStockOutSkuCell"];
        }, ^UITableViewCell * _Nonnull(UITableView * _Nonnull tableView, NSInteger item) {
            //返回cell
            if (item < productViewModels.count) {
                JDISVSettlementOrderStockOutSkuViewModel *viewModel = [productViewModels objectAtIndex:item];
                JDISVSettlementOrderStockOutSkuCell *cell = [[[NSBundle jdisvSettlement_bundle] loadNibNamed:@"JDISVSettlementOrderStockOutSkuCell" owner:nil options:nil] firstObject];
                cell.selectionStyle = UITableViewCellSelectionStyleNone;
                [cell updateCellWithViewModel:viewModel];
                return cell;
            } else {
                return [UITableViewCell new];
            }
        }, ^NSInteger(UITableView * _Nonnull tableView) {
            //cell个数
            if (productViewModels) {
                return productViewModels.count;
            } else {
                return 0;
            }
            
        }, ^CGFloat(UITableView * _Nonnull tableView, NSInteger item) {
            if (item < productViewModels.count) {
                JDISVSettlementOrderStockOutSkuViewModel *viewModel = [productViewModels objectAtIndex:item];
                return viewModel.height;
            } else {
                return 0;
            }

        }, ^(UITableView * _Nonnull tableView, NSInteger item) {
            //cell点击
        })
        .ksa_addFillAction(SettlementLan(@"checkout_some_out_stock_positive") , ^{
            //KSA使用新接口，删除无货商品
            [PlatformService showLoadingInView:controller.view];
            NSMutableArray *productSkuArray = [[NSMutableArray alloc] init];
            for (JDISVSettlementOrderStockOutSkuViewModel *stockoutSkuViewModel in productViewModels) {
                NSDictionary *sku = @{@"sku":stockoutSkuViewModel.skuId,@"num":stockoutSkuViewModel.skuCount
                };
                [productSkuArray addObject:sku];
            }
            NSMutableDictionary *arg = [NSMutableDictionary dictionary];
            [arg setValue:productSkuArray forKey:@"skus"];
            if (self.viewModel.sourceType == 1) {//从商祥进入的立即购买
                [arg setValue:@(7) forKey:@"refer"];
            }
            [self ksa_newDeleteProductWith:arg controller:controller];
        })
        .alertShow();
    }
    
}

//- (void)removeSubSkuStockoutSkuWith:(NSArray *)skuParamArray controller:(UIViewController *)controller completeBlock:(void(^)(BOOL success, NSString *message))complete {
//    NSString *unselect = [NSString stringWithFormat:@"router://%@/removeProduct", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)]];
//    [PlatformService showLoadingInView:controller.view];
//    [JDRouter openURL:unselect arg:@{@"operations":@{@"products":[NSArray arrayWithArray:skuParamArray]}} error:nil completion:^(id  _Nullable object) {
//        [PlatformService dismissInView:controller.view];
//        NSDictionary *callObj = (NSDictionary *)object;
//        NSString *code = [callObj objectForKey:@"code"];
//        if ([code isEqualToString:@"0"]) {
//            complete(YES, nil);
//        } else {
//            NSString *message = [callObj objectForKey:@"message"];
//            complete(NO, message);
//        }
//    }];
//}

- (void)removeStockoutSkuWith:(NSArray *)skuParamArray controller:(UIViewController *)controller completeBlock:(void(^)(BOOL success, NSString *message))complete {
    NSString *unselect = [NSString stringWithFormat:@"router://%@/unSelect", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)]];
    [PlatformService showLoadingInView:controller.view];
    [JDRouter openURL:unselect arg:@{@"operations":@{@"products":[NSArray arrayWithArray:skuParamArray]}} error:nil completion:^(id  _Nullable object) {
        [PlatformService dismissInView:controller.view];
        NSDictionary *callObj = (NSDictionary *)object;
        NSString *code = [callObj objectForKey:@"code"];
        if ([code isEqualToString:@"0"]) {
            complete(YES, nil);
        } else {
            NSString *message = [callObj objectForKey:@"message"];
            complete(NO, message);
        }
    }];
}

- (void)ksa_removeStockoutSkuWith:(NSDictionary *)param controller:(UIViewController *)controller completeBlock:(void(^)(BOOL success, NSString *message))complete{
    [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"operateCart" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error){
            complete(NO,SettlementLan(@"setllement_unknown_error"));
        } else {
//            NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
//            NSString *code = (NSString *)[responseObject objectForKey:@"code"];
            NSString *message = (NSString *)[responseObject objectForKey:@"message"];
            NSString *success = (NSString *)[responseObject objectForKey:@"success"];
            if (success.boolValue) {
                complete(YES,@"");
            } else {// 异常
                if (![message jdcd_validateString]) {
                    message = SettlementLan(@"setllement_unknown_error") ;
                }
                complete(NO,message);
            }
        }
    }];
}

- (void)ksa_newDeleteProductWith:(NSDictionary *)arg controller:(UIViewController *)controller{
    NSString *balanceURL = [NSString stringWithFormat:@"router://%@/balanceRemoveCart", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)]];
    @weakify(self)
    [JDRouter openURL:balanceURL arg:arg error:nil completion:^(id  _Nullable object) {
        [PlatformService dismissInView:controller.view];
        if ([object isKindOfClass:[NSDictionary class]]){
            NSDictionary *result = object;
            NSString *message = [result jdcd_getStringElementForKey:@"message"];
            NSNumber *success = [result jdcd_getNumberElementForKey:@"success"];
            NSInteger code = [result jdcd_getIntegerElementForKey:@"code"];
            if (code == 0 && success.boolValue){//删除成功
                @strongify(self)
                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementBottomFloorSubmitActionRefreshAfterRemoveStockoutAction"];
                action.value = [self addressIdInCommonModel];
                [self isv_sendAction:action];
            }else{
                if (message && message.length > 0){
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:message];
                }else{
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:SettlementLan(@"settlement_discount_code_delete_failed")];
                }
            }
        }else{
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:SettlementLan(@"settlement_discount_code_delete_failed")];
        }
    }];
}

/// 处理限购
- (void)processLimitBuyActionWithMessage:(NSString *)message limitType:(JDISVSettlementBottomFloorMoudleSubmitResultType)type controller:(UIViewController *)controller{
    @weakify(self)
    switch (type) {
        case JDISVSettlementBottomFloorMoudleSubmitResultTypePinLimitBuy:{
            [KAAlert alert].config
            .renderW3(message)
            .addFillAction(SettlementLan(@"checkout_pre_sale_dialog_confirm"), ^{
            })
            .alertShow();
        }
            break;
        case JDISVSettlementBottomFloorMoudleSubmitResultTypeMinLimitBuy:{
            [KAAlert alert].config
            .renderW3(message)
            .addFillAction(SettlementLan(@"checkout_join_buy_back"), ^{
                [controller.navigationController popViewControllerAnimated:YES];
            })
            .alertShow();
        }
            break;
        case JDISVSettlementBottomFloorMoudleSubmitResultTypeMaxLimitBuy:{
            [KAAlert alert].config
            .renderW3(message)
            .addFillAction(SettlementLan(@"checkout_join_buy_back"), ^{
                [controller.navigationController popViewControllerAnimated:YES];
            })
            .alertShow();
        }
            break;
        default:
            break;
    }
    
}

/// 托盘商品限购弹框
/// - Parameter objec: object
- (void)servingTrayQuota:(NSArray *)productList controller:(UIViewController *)controller{
    JDISVServingTrayViewController *vc = [[JDISVServingTrayViewController alloc] init];
    vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
    vc.dataList = productList;
    @weakify(self)
    vc.pressedBlock = ^(NSInteger index) {
        @strongify(self)
        if (index == 1){//返回购物车
            if ([JDISVSettlementAmountFloorNetService sharedService].source == 0){//从购物车进入
                [controller.navigationController popViewControllerAnimated:YES];
            }else{//从商祥进入
                NSString *backToURL = [NSString stringWithFormat:@"router://%@/shoppingCartController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)]];
                UIViewController *shopcart = [JDRouter openURL:backToURL arg:@{} error:nil completion:nil];
                [controller.navigationController pushViewController:shopcart animated:YES];
                //删除结算页
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self removeCurrentController:controller];
                });
            }
        }else if (index == 2){//移除商品
            [PlatformService showLoadingInView:controller.view];
            NSMutableArray *productSkuArray = [[NSMutableArray alloc] init];
            for (JDISVServingTrayModel *productModel in productList) {
                NSDictionary *sku = @{@"sku":productModel.info.skuId,@"num":productModel.info.num
                };
                [productSkuArray addObject:sku];
            }
            
            NSMutableDictionary *arg = [NSMutableDictionary dictionary];
            [arg setValue:productSkuArray forKey:@"skus"];
            if (self.viewModel.sourceType == 1) {//从商祥进入的立即购买
                [arg setValue:@(7) forKey:@"refer"];
            }
            [self ksa_newDeleteProductWith:arg controller:controller];
        }
    };
    [[PlatformService getTopViewController] presentViewController:vc animated:NO completion:nil];
}

- (NSDictionary *)addressIdInCommonModel {
    NSMutableDictionary *addressDic = [[NSMutableDictionary alloc] init];
    NSDictionary *addressMap = [self.viewModel.commonModel.commonData objectForKey:@"consigneeAddressMap"] ? : @{};
    NSDictionary *defaultAddressMap = [addressMap objectForKey:@"C-M#defaultAddressFloor&core"] ? : @{};
    NSString *addressId = [defaultAddressMap objectForKey:@"addressId"] ? : @"";
    [addressDic setObject:addressId forKey:@"addressId"];
    
    NSDictionary *mobileMap = [addressMap objectForKey:@"C-M#defaultAddressFloor&privacy"] ? : @{};
    NSString *mobile = [mobileMap objectForKey:@"starredMobile"];
    if (![mobile jdcd_validateString]) {
        mobileMap = [addressMap objectForKey:@"C-M#defaultAddressFloor&consignee"] ? : @{};
        mobile = [mobileMap objectForKey:@"mobile"];
    }
    [addressDic setObject:mobile ? : @"" forKey:@"mobile"];
    
    return addressDic;
}

#pragma mark - Tool Func
+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString
{
    if (jsonString == nil) {
        return nil;
    }

    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err)
    {
        NSLog(@"json error：%@",err);
        return nil;
    }
    return dic;
}
@end
