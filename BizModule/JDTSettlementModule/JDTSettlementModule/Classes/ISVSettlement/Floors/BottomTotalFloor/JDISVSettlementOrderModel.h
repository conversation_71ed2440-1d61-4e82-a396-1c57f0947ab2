//
//  JDISVSettlementOrderModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:订单提单成功模型
 模型命名空间:core.trade-FLR#balance.order-M#defaultOrderFloor
 模型uuid: defaultOrderFloor
 */

@interface JDISVSettlementOrderModel : NSObject
// 内部域Key:VCYBB2C-D#submitResult&basic
@property (nonatomic, copy) NSString *orderId;
@property (nonatomic) BOOL zeroOrderFlag; /**< 0元单标识 */
@property (nonatomic, copy) NSString *payId; /**< 支付Id */
@property (nonatomic, copy) NSString *payUrl; /**< pay url */
@property (nonatomic, copy) NSString *idPaymentType;/**<支付类型>*/
@end

NS_ASSUME_NONNULL_END
