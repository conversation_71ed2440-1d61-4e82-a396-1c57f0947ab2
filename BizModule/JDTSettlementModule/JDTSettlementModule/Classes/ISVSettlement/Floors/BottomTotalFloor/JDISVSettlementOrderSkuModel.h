//
//  JDISVSettlementOrderSkuModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:订单提单缺货商品模型
 模型命名空间:core.trade-FLR#balance.order-M#defaultOrderFloor -> PartList
 模型namespace: core.trade-FLR#balance.order-P#skuInfo
 */
@interface JDISVSettlementOrderSkuModel : NSObject
// 内部域Key:C-P#skuOrderPart&basic
@property (nonatomic, copy) NSString *skuName; /**< skuName */
@property (nonatomic, copy) NSString *imgUrl;
@property (nonatomic, copy) NSString *num;
@property (nonatomic, copy) NSString *skuId;
@property (nonatomic, copy) NSString *skuModelType;
// 内部域Key:C-P#skuOrderPart&price
@property (nonatomic, copy) NSString *price;
@end

NS_ASSUME_NONNULL_END
