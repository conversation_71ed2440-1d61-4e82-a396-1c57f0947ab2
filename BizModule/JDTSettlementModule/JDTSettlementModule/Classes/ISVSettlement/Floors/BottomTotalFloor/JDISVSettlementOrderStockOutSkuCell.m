//
//  JDISVSettlementOrderStockOutSkuCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/16.
//

#import "JDISVSettlementOrderStockOutSkuCell.h"

#import "JDISVSettlementOrderStockOutSkuViewModel.h"

#import "NSAttributedString+JDISVSettlementBottom.h"

@interface JDISVSettlementOrderStockOutSkuCell()
@property (weak, nonatomic) IBOutlet UIImageView *skuImageMaskView;
@property (weak, nonatomic) IBOutlet UIImageView *skuImageView;
@property (weak, nonatomic) IBOutlet UILabel *skuLabel;
@property (weak, nonatomic) IBOutlet UILabel *numLabel;
@property (weak, nonatomic) IBOutlet UIView *stockoutMaskView;
@property (weak, nonatomic) IBOutlet UILabel *stockoutLabel;
@property (nonatomic, strong) JDISVSettlementOrderStockOutSkuViewModel *viewModel;
@end

@implementation JDISVSettlementOrderStockOutSkuCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = UIColor.clearColor;
    self.contentView.backgroundColor = UIColor.clearColor;
    self.skuImageMaskView.layer.masksToBounds = YES;
    self.skuImageMaskView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.skuImageMaskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02f];
    self.skuImageView.layer.masksToBounds = YES;
    self.skuImageView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"];
    self.stockoutMaskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.7f];
    self.stockoutMaskView.layer.masksToBounds = YES;
    self.stockoutMaskView.layer.cornerRadius = 30.f;
    self.stockoutLabel.attributedText = [[NSAttributedString alloc] ka_settlement_bottom_initWithString:SettlementLan(@"checkout_shop_no_product_label")  colorKey:@"#C1" fontKey:@"#T8" weight:UIFontWeightSemibold];
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementBottomFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementOrderStockOutSkuViewModel.class]) {
        self.viewModel = (JDISVSettlementOrderStockOutSkuViewModel *)viewModel;
        [self.skuImageView jdcd_setImage:[PlatformService getCompleteImageUrl:_viewModel.coverImageUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
        if (_viewModel.skuTitleAttributedString) {
            self.skuLabel.hidden = NO;
            self.skuLabel.attributedText = _viewModel.skuTitleAttributedString;
        } else {
            self.skuLabel.hidden = YES;
        }
        
        if (_viewModel.skuNumAttributedString) {
            self.numLabel.hidden = NO;
            self.numLabel.attributedText = _viewModel.skuNumAttributedString;
        } else {
            self.numLabel.hidden = YES;
        }
    }
}
@end
