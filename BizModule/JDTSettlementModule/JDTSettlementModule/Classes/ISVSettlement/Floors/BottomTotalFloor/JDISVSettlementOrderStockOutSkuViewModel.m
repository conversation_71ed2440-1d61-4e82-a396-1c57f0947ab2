//
//  JDISVSettlementOrderStockOutSkuViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import "JDISVSettlementOrderStockOutSkuViewModel.h"

#import "JDISVSettlementOrderSkuModel.h"

#import "NSAttributedString+JDISVSettlementBottom.h"

@interface JDISVSettlementOrderStockOutSkuViewModel()
@property (nonatomic, strong) JDISVSettlementOrderSkuModel *orderSkuModel;
@end

@implementation JDISVSettlementOrderStockOutSkuViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementOrderStockOutSkuCell";
        self.cellIdentifier = @"JDISVSettlementOrderStockOutSkuCell";
        self.registerType = JDISVSettlementBottomFloorBaseCellViewModelRegisterTypeXib;
        self.height = 82.f;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementOrderSkuModel.class]) {
        self.orderSkuModel = (JDISVSettlementOrderSkuModel *)data;
        self.skuId = _orderSkuModel.skuId;
        self.skuCount = _orderSkuModel.num;
        self.skuModelType = _orderSkuModel.skuModelType;
        if ([_orderSkuModel.skuName jdcd_validateString]) {
            self.skuTitleAttributedString = [[NSAttributedString alloc] ka_settlement_bottom_initWithString:_orderSkuModel.skuName colorKey:@"#C5" fontKey:@"#T9"];
        } else {
            self.skuTitleAttributedString = nil;
        }
        
        if ([_orderSkuModel.num jdcd_validateString]) {
            NSString *countStr = [NSString stringWithFormat:SettlementLan(@"checkout_settlment_qty"), _orderSkuModel.num];
            self.skuNumAttributedString = [[NSAttributedString alloc] ka_settlement_bottom_initWithString:countStr colorKey:@"#C5" fontKey:@"#T9"];
        } else {
            self.skuNumAttributedString = nil;
        }
        if ([_orderSkuModel.imgUrl jdcd_validateString]) {
            self.coverImageUrl = [NSString stringWithFormat:@"%@%@", self.imageDomain, _orderSkuModel.imgUrl];
        }
        self.height = 83.f;
    } else {
        self.height = 0;
    }
}
@end
