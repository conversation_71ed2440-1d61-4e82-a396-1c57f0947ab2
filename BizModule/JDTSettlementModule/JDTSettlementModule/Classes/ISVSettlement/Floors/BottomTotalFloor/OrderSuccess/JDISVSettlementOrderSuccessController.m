//
//  JDISVSettlementOrderSuccessController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/16.
//

#import "JDISVSettlementOrderSuccessController.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

#import "NSAttributedString+JDISVSettlementBottom.h"

@interface JDISVSettlementOrderSuccessController ()
@property (weak, nonatomic) IBOutlet UIImageView *successImgView;
@property (weak, nonatomic) IBOutlet UILabel *submitSuccessTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *submitSuccessInfoLabel;
@property (weak, nonatomic) IBOutlet UIButton *backToHomeButton;
@property (weak, nonatomic) IBOutlet UIButton *checkOrderListButton;
// 导航栏
@property (nonatomic, strong) KANavigationBar *controllerNavigationBar;
@property (nonatomic, strong) KANavigationBarBackgrounItem *navigationBackgroundItem; /**< 导航栏背景Item */

@property (nonatomic, copy) NSString *orderId;
@end

@implementation JDISVSettlementOrderSuccessController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
}

-(void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    
    [self removeLast];
}

-(void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    
    [self removeCurrent];
}


- (void)updateWithOrderId:(NSString *)orderId {
    self.orderId = orderId;
}

- (void)setupUI {
    [self setupNavigationView];
    
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    self.successImgView.superview.backgroundColor = [UIColor clearColor];
    
//    self.successImgView.contentMode = UIViewContentModeCenter;
//    self.successImgView.image = [UIImage ka_iconWithName:JDIF_ICON_SUCCESS_BIG imageSize:CGSizeMake(30.f, 30.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C11"]];
//    self.successImgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C11"];
//    self.successImgView.layer.masksToBounds = YES;
//    self.successImgView.layer.cornerRadius = 15.f;
    self.successImgView.image = [UIImage jdisvSettlement_imageNamed:@"jdka_settlement_zero_success_pay"];

    self.submitSuccessTitleLabel.attributedText = [[NSAttributedString alloc] ka_settlement_bottom_initWithString:SettlementLan(@"checkout_result_submit_order_pay_success")  colorKey:@"#C7" fontKey:@"#T3" weight:UIFontWeightMedium];
//    NSString *orderInfo = [NSString stringWithFormat:SettlementLan(@"checkout_result_order_id") , _orderId];
//    self.submitSuccessInfoLabel.attributedText = [[NSAttributedString alloc] ka_settlement_bottom_initWithString:orderInfo colorKey:@"#C9-1" fontKey:@"#T7"];
    NSMutableAttributedString *amountTmpAttrText = [[NSMutableAttributedString alloc] init];
    [amountTmpAttrText KA_renderWithPrice:0.f font0:@"#T6" font1:@"#T3" font2:@"#T3" colorType:@"#C9-1"];
    self.submitSuccessInfoLabel.attributedText = amountTmpAttrText;
    
    self.backToHomeButton.enabled = YES;
    self.checkOrderListButton.enabled = YES;
    
    [self.backToHomeButton addTarget:self action:@selector(backToHomeAction) forControlEvents:UIControlEventTouchUpInside];
    [self.checkOrderListButton addTarget:self action:@selector(checkOrderListAction) forControlEvents:UIControlEventTouchUpInside];
    
    [self configBtn:self.backToHomeButton title:SettlementLan(@"checkout_result_go_home") ];
    [self configBtn:self.checkOrderListButton title:SettlementLan(@"checkout_result_go_order") ];
    
    [self.backToHomeButton renderB3];
    [self.checkOrderListButton renderB5];
}

- (void)setupNavigationView {
    self.controllerNavigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self.view addSubview:self.controllerNavigationBar];
    
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    self.controllerNavigationBar
    .decorator.title(@"", NSTextAlignmentCenter)
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.navigationController popViewControllerAnimated:YES];
        
    }) //返回按钮
    .render();
    self.navigationBackgroundItem = [[KANavigationBarBackgrounItem alloc] init];
    self.navigationBackgroundItem.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];

    [self.controllerNavigationBar pushNavigationBarItem:self.navigationBackgroundItem];
    [self.navigationController setNavigationBarHidden:YES animated:YES];
}

- (void)backToHomeAction {
    
    [self.navigationController popToRootViewControllerAnimated:YES];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 调到首页
        [[JDISVPlatformService sharedService] showPage:ISVShowHomePage];
    });
    
    // 移除当前页面
//    __weak typeof(self) weakSelf = self;
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        __strong typeof(weakSelf) strongSelf = weakSelf;
//        [strongSelf removeLast];
////        [strongSelf.navigationController popViewControllerAnimated:YES];
//
//
//    });
}

- (void)checkOrderListAction {
    // 跳转到订单列表
    //
    NSString *orderListMainControllerURL = [NSString stringWithFormat:@"router://%@/orderListMainController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeOrderList)]];
    @weakify(self)
    [JDRouter openURL:orderListMainControllerURL arg:@{@"order_list_type": @0} error:nil completion:^(id  _Nullable object) {
        @strongify(self)
        
        UIViewController *orderListMainController = (UIViewController *)object;
        [self.navigationController pushViewController:orderListMainController animated:YES];
        
        // 移除上一级页面
//        __weak typeof(self) weakSelf = self;
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            __strong typeof(weakSelf) strongSelf = weakSelf;
//            [self removeLast];
//        });
    }];
    
    
}

/// 删除上一级界面
- (void)removeLast {
    
    NSMutableArray *array = [NSMutableArray arrayWithArray:self.navigationController.viewControllers];
    for (UIViewController *controller in array) {
        if ([controller isKindOfClass:NSClassFromString(@"JDISVSettlementController")]) {
            [array removeObject:controller];
            break;
        }
    }
    self.navigationController.viewControllers = array;
}
//删除当前界面
- (void)removeCurrent {
    
    NSMutableArray *array = [NSMutableArray arrayWithArray:self.navigationController.viewControllers];
    for (UIViewController *controller in array) {
        if ([controller isKindOfClass:NSClassFromString(@"JDISVSettlementOrderSuccessController")]) {
            [array removeObject:controller];
            break;
        }
    }
    self.navigationController.viewControllers = array;
}

- (void)configBtn:(UIButton *)button title:(NSString *)title {
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitle:title forState:UIControlStateHighlighted];
    [button setTitle:title forState:UIControlStateDisabled];
}

@end
