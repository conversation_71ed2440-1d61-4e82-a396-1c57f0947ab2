//
//  NSAttributedString+JDISVSettlementBottom.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import <Foundation/Foundation.h>


NS_ASSUME_NONNULL_BEGIN

@interface NSAttributedString (JDISVSettlementBottom)
- (instancetype)ka_settlement_bottom_initWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey;
- (instancetype)ka_settlement_bottom_initWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight;

- (CGFloat)ka_settlement_bottom_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey;
- (CGFloat)ka_settlement_bottom_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight;

- (CGFloat)ka_settlement_bottom_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey;
- (CGFloat)ka_settlement_bottom_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight;
@end

NS_ASSUME_NONNULL_END
