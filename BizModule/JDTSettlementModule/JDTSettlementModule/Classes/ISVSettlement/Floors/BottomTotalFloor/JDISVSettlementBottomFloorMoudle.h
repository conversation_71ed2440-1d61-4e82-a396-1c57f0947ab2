//
//  JDISVSettlementBottomFloorMoudle.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/15.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementBottomFloorMoudleSubmitResultType ) {
    JDISVSettlementBottomFloorMoudleSubmitResultTypeZeroOrder = 0, /**< 0元单 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypePay = 1, /**< 支付 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypeStockOutAll = 2, /**< 全部无货 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypeStockOutPart = 3, /**< 部分无货 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypeGroupBuyError = 4, /**< 拼团错误 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypePinLimitBuy = 5, /**< pin限购 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypeMinLimitBuy = 6, /**< 最小限购 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypeMaxLimitBuy = 7, /**< 最大限购 */
    JDISVSettlementBottomFloorMoudleSubmitResultTypeCodeUnUse = 8, /**<优惠码不可用>*/
    JDISVSettlementBottomFloorMoudleSubmitResultTypeSomeLimit= 9,/**<部分托盘商品限购>*/
    JDISVSettlementBottomFloorMoudleSubmitResultTypeAllLimit= 10,/**<全部托盘商品限购>*/
    JDISVSettlementBottomFloorMoudleSubmitResultTypeCODPaymentError = 11,/**<COD提单报错>*/
};

@class JDISVFloorCommonModel;
@class RACSignal;
@interface JDISVSettlementBottomFloorMoudle : NSObject<JDISVFloorModuleProtocol>
@property (nonatomic, assign) NSInteger sourceType;
@property (nonatomic, assign) CGFloat height;
@property (nonatomic, strong) JDISVFloorCommonModel *commonModel;
@property (nonatomic, copy) NSAttributedString *titleAttributedString;
@property (nonatomic, strong) NSNumber *deliveryAddressId;
@property (nonatomic, copy) NSString *billingAddressId;
@property (nonatomic, copy, nullable) NSAttributedString *amountAttributedString;

//检查是否有虚拟子品无货
- (BOOL)checkHaveVirtualProductStockOutBySkuIds:(NSArray *)productModels;
// 提交订单
- (RACSignal *)loadSubmitOrderSignal;
//提单后绑定发票和订单关系
-(RACSignal *)bindOrderAndSelectInvoiceSignalWithOrderId:(NSString *)orderId;


/// 检查支付金额是否为0元，并且选择了COD
- (BOOL)payAmountAndCODCheck;
@end

NS_ASSUME_NONNULL_END
