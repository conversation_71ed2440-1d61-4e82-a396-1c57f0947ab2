//
//  JDISVServingTrayModel.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/5/24.
//

#import "JDISVServingTrayModel.h"

@implementation JDISVServingTrayModel
+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"type": @"JDISVServingTrayTypeModel",
             @"info": @"JDISVServingTrayInfoModel"
    };
}

@end

@implementation JDISVServingTrayTypeModel

@end

@implementation JDISVServingTrayInfoModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"maxNum": @"VCYBB2C-D#submitResult&limit.maxNum",
        @"minNum": @"VCYBB2C-D#submitResult&limit.minNum",
        @"skuName": @"C-P#skuOrderPart&basic.skuName",
        @"imgUrl": @"C-P#skuOrderPart&basic.imgUrl",
        @"num": @"C-P#skuOrderPart&basic.num",
        @"skuId": @"C-P#skuOrderPart&basic.skuId",
        @"price": @"C-P#skuOrderPart&price.price",
        @"limitBuyType": @"C-P#skuOrderPart&limit.limitBuyType",
        @"limitBuyMessage": @"C-P#skuOrderPart&limit.limitBuyMessage",
    };
}

@end
