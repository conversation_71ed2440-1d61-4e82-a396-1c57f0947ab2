//
//  JDISVServingTrayCell.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/5/24.
//

#import "JDISVServingTrayCell.h"
#import "UIImage+JDISVSettlement.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@interface JDISVServingTrayCell ()
@property (unsafe_unretained, nonatomic) IBOutlet UIImageView *productImg;
@property (unsafe_unretained, nonatomic) IBOutlet UIImageView *soldOutImg;
@property (unsafe_unretained, nonatomic) IBOutlet UILabel *skuName;
@property (unsafe_unretained, nonatomic) IBOutlet UILabel *numberLabel;
@property (unsafe_unretained, nonatomic) IBOutlet UILabel *tipLabel;

@end

@implementation JDISVServingTrayCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    
    self.productImg.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
    self.productImg.clipsToBounds = YES;
    self.skuName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.skuName.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.numberLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    self.numberLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.tipLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"];
    self.tipLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    self.soldOutImg.hidden = YES;
    
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)configData:(JDISVServingTrayModel *)model{
    if (model){
        [self.productImg jdcd_setImage:[PlatformService getCompleteImageUrl:model.info.imgUrl moduleType:@""] placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
        self.skuName.text = model.info.skuName;
        self.numberLabel.text = [NSString stringWithFormat:@"x%@",model.info.num];
        
        if ([model.info.minNum jdcd_validateString] && model.info.minNum.integerValue > 0 && (![model.info.maxNum jdcd_validateString] || model.info.maxNum.integerValue <= 0)) {//展示最小购买
            self.tipLabel.text = [NSString stringWithFormat:SettlementLan(@"checkout_minimum_purchase"),model.info.minNum];
        }else if ([model.info.maxNum jdcd_validateString] && model.info.maxNum.integerValue > 0 && (![model.info.minNum jdcd_validateString] || model.info.minNum.integerValue <= 0)){//展示最大购买数
            self.tipLabel.text = [NSString stringWithFormat:SettlementLan(@"checkout_limited_to_pieces"),model.info.maxNum];
        }else if ([model.info.minNum jdcd_validateString] && model.info.minNum.integerValue > 0 && [model.info.maxNum jdcd_validateString] && model.info.maxNum.integerValue > 0){//同时展示最大购买，最小购买
            NSString *limt = [NSString stringWithFormat:SettlementLan(@"checkout_limited_to_pieces"),model.info.maxNum];
            NSString *max = [NSString stringWithFormat:SettlementLan(@"checkout_minimum_purchase"),model.info.minNum];
            self.tipLabel.text = [NSString stringWithFormat:@"%@,%@",limt,max];
        }
    }
}

@end
