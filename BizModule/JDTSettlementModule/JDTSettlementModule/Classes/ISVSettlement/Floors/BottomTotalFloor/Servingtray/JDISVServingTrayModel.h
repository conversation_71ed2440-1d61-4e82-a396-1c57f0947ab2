//
//  JDISVServingTrayModel.h
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/5/24.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@class JDISVServingTrayTypeModel;
@class JDISVServingTrayInfoModel;

@interface JDISVServingTrayModel : NSObject

@property (strong,nonatomic) JDISVServingTrayTypeModel *type;
@property (strong,nonatomic) JDISVServingTrayInfoModel *info;

@end

@interface JDISVServingTrayTypeModel : NSObject

@property (strong,nonatomic) NSString *namespace;
@property (strong,nonatomic) NSString *version;

@end

@interface JDISVServingTrayInfoModel : NSObject

@property (strong,nonatomic) NSString *maxNum;
@property (strong,nonatomic) NSString *minNum;
@property (strong,nonatomic) NSString *skuName;
@property (strong,nonatomic) NSString *imgUrl;
@property (strong,nonatomic) NSString *num;
@property (strong,nonatomic) NSString *skuId;
@property (strong,nonatomic) NSString *price;
@property (strong,nonatomic) NSString *limitBuyType;
@property (strong,nonatomic) NSString *limitBuyMessage;

@end

NS_ASSUME_NONNULL_END
