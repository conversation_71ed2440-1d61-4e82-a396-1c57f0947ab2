//
//  JDISVServingTrayViewController.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu12 on 2023/5/24.
//

#import "JDISVServingTrayViewController.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "UIImage+JDISVSettlement.h"
#import "JDISVServingTrayCell.h"
#import <JDISVCategoryModule/NSString+JDCDLang.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

static CGFloat CELLHEIGHT = 82.f;

@interface JDISVServingTrayViewController ()<UITableViewDelegate,UITableViewDataSource>

@property (strong,nonatomic) UIView *bgView;
@property (strong,nonatomic) UILabel *titleLabel;
@property (strong,nonatomic) UIButton *closeBtn;
@property (strong,nonatomic) UITableView *tableView;
@property (strong,nonatomic) UIView *bottomView;
@property (strong,nonatomic) UIButton *backToShopingCart;
@property (strong,nonatomic) UIButton *removeGoods;


@end

@implementation JDISVServingTrayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self setupUI];
    [self updateUI];
}

- (void)setDataList:(NSArray<JDISVServingTrayModel *> *)dataList{
    _dataList = dataList;
    [self.tableView reloadData];
    [self updateUI];
}

- (void)setupUI{
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.65];
    [self.view addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view);
        make.width.mas_offset([UIScreen mainScreen].bounds.size.width - 84);
    }];
    
    
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.closeBtn];
    [self.bgView addSubview:self.tableView];
    [self.bgView addSubview: self.bottomView];
    
    [self.bottomView addSubview:self.backToShopingCart];
    [self.bottomView addSubview:self.removeGoods];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.bgView).mas_offset(18);
        make.leading.mas_equalTo(self.bgView).mas_offset(39);
        make.trailing.mas_equalTo(self.bgView).mas_offset(-39);
        
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.trailing.mas_equalTo(self.bgView);
        make.width.height.mas_offset(30);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(18);
        make.leading.mas_equalTo(self.bgView).mas_offset(24);
        make.trailing.mas_equalTo(self.bgView).mas_offset(-24);
        make.height.mas_offset(0);
    }];
    
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.tableView.mas_bottom).mas_offset(6);
        make.leading.trailing.mas_equalTo(self.bgView);
        make.bottom.mas_equalTo(self.bgView);
    }];
    
}

- (void)updateUI{
    NSInteger count = (self.dataList ? self.dataList.count : 0);
    BOOL haveListView = (self.dataList ? self.dataList.count : 0) > 0 ? YES : NO;
    [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_offset(count * CELLHEIGHT);
    }];
    
    if (haveListView){
        self.titleLabel.text = SettlementLan(@"checkout_serving_tray_title_1");
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.bgView).mas_offset(18);
        }];
        [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.tableView.mas_bottom).offset(6);
        }];
        
        self.backToShopingCart.hidden = NO;
        self.removeGoods.hidden = NO;
        
        if ([[NSString getKAUseLang] isEqualToString:@"zh-Hans"]){
            [self.backToShopingCart mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.bottomView).mas_offset(18);
                make.leading.mas_equalTo(self.bottomView).mas_offset(30);
                make.height.mas_offset(30);
                make.bottom.mas_equalTo(self.bottomView).offset(-18);
            }];
            
            [self.removeGoods mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.centerY.mas_equalTo(self.backToShopingCart);
                make.leading.mas_equalTo(self.backToShopingCart.mas_trailing).mas_offset(12);
                make.width.height.mas_equalTo(self.backToShopingCart);
                make.trailing.mas_equalTo(self.bottomView).mas_offset(-30);
            }];
        }else{
            [self.backToShopingCart mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.bottomView).mas_offset(18);
                make.leading.mas_equalTo(self.bottomView).mas_offset(20);
                make.trailing.mas_equalTo(self.bottomView).mas_offset(-20);
                make.height.mas_offset(30);
            }];
            
            [self.removeGoods mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.mas_equalTo(self.backToShopingCart.mas_bottom).mas_offset(12);
                make.leading.mas_equalTo(self.bottomView).mas_offset(20);
                make.trailing.mas_equalTo(self.bottomView).mas_offset(-20);
                make.height.mas_equalTo(self.backToShopingCart);
                make.bottom.mas_equalTo(self.bottomView).offset(-18);
            }];
        }
        [self.bgView.layer layoutIfNeeded];
        [PlatformService setDefaultButtonStyleWith:self.backToShopingCart buttonType:JDISVButtonRedBorderRedTitleWhiteBgUIType];
        [PlatformService setDefaultButtonStyleWith:self.removeGoods buttonType:JDISVButtonOrangeGridentType];
    }else{
        self.titleLabel.text = SettlementLan(@"checkout_serving_tray_title_2");
        [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.bgView).mas_offset(40);
        }];
        [self.bottomView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.tableView.mas_bottom).offset(0);
        }];
        self.backToShopingCart.hidden = NO;
        self.removeGoods.hidden = YES;
        
        [self.backToShopingCart mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.bottomView).mas_offset(18);
            make.leading.mas_equalTo(self.bottomView).mas_offset(20);
            make.trailing.mas_equalTo(self.bottomView).mas_offset(-20);
            make.height.mas_offset(30);
            make.bottom.mas_equalTo(self.bottomView).offset(-18);
        }];
        [self.bgView.layer layoutIfNeeded];
        [PlatformService setDefaultButtonStyleWith:self.backToShopingCart buttonType:JDISVButtonOrangeGridentType];
    }
}


#pragma mark - <action>

- (void)clostbtnPressed:(UIButton *)sender{
    [self dismissViewControllerAnimated:NO completion:nil];
}

#pragma mark - <UITableViewDelegate,UITableViewDataSource>

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    return self.dataList ? self.dataList.count : 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return CELLHEIGHT;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVServingTrayCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JDISVServingTrayCell"];
    [cell configData:self.dataList[indexPath.row]];
    return cell;
}


#pragma mark - <getter>
- (UIView *)bgView{
    if (!_bgView){
        _bgView = [[UIView alloc] init];
        _bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        _bgView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R62"];
        _bgView.clipsToBounds = YES;
    }
    return _bgView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel){
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        _titleLabel.numberOfLines = 0;
    }
    return _titleLabel;
}

- (UIButton *)closeBtn{
    if (!_closeBtn){
        _closeBtn = [[UIButton alloc] init];
        [_closeBtn setImage:[UIImage jdisvSettlement_imageNamed:@"Subtractchecout_requriement_close"] forState:UIControlStateNormal];
        [_closeBtn addTarget:self action:@selector(clostbtnPressed:) forControlEvents:UIControlEventTouchUpInside];
        _closeBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        _closeBtn.contentEdgeInsets = UIEdgeInsetsMake(14, 0, 0, 14);
        if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft){
            _closeBtn.contentEdgeInsets = UIEdgeInsetsMake(14, 14, 0, 0);
        }
    }
    return _closeBtn;
}


- (UITableView *)tableView{
    if (!_tableView){
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerNib:[UINib nibWithNibName:@"JDISVServingTrayCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVServingTrayCell"];
        _tableView.backgroundColor = self.bgView.backgroundColor;
        _tableView.scrollEnabled = NO;
        _tableView.clipsToBounds = YES;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    }
    return _tableView;
}

- (UIView *)bottomView{
    if (!_bottomView){
        _bottomView = [[UIView alloc] init];
        _bottomView.backgroundColor = self.bgView.backgroundColor;
    }
    return _bottomView;
}
/*
 "checkout_return_to_shopping_cart" = "返回购物车";
 "checkout_remove_goods" = "移除商品";
 */
    
- (UIButton *)backToShopingCart{
    if (!_backToShopingCart){
        _backToShopingCart = [[UIButton alloc] init];
        [_backToShopingCart setTitle:SettlementLan(@"checkout_return_to_shopping_cart") forState:UIControlStateNormal];
        [_backToShopingCart setTitle:SettlementLan(@"checkout_return_to_shopping_cart") forState:UIControlStateHighlighted];
        [_backToShopingCart setTitle:SettlementLan(@"checkout_return_to_shopping_cart") forState:UIControlStateDisabled];
        _backToShopingCart.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        [_backToShopingCart addTarget:self action:@selector(returnToshopingCart) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backToShopingCart;
}

- (UIButton *)removeGoods{
    if (!_removeGoods){
        _removeGoods = [[UIButton alloc] init];
        [_removeGoods setTitle:SettlementLan(@"checkout_remove_goods") forState:UIControlStateNormal];
        [_removeGoods setTitle:SettlementLan(@"checkout_remove_goods") forState:UIControlStateHighlighted];
        [_removeGoods setTitle:SettlementLan(@"checkout_remove_goods") forState:UIControlStateDisabled];
        _removeGoods.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightBold];
        [_removeGoods addTarget:self action:@selector(removeGods) forControlEvents:UIControlEventTouchUpInside];
    }
    return _removeGoods;
}

- (void)returnToshopingCart{
    if (self.pressedBlock){
        self.pressedBlock(1);
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)removeGods{
    if (self.pressedBlock){
        self.pressedBlock(2);
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}




@end
