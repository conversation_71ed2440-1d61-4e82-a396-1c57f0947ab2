//
//  JDISVServingTrayViewController.h
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by ext.chenhongyu12 on 2023/5/24.
//

#import <UIKit/UIKit.h>
#import "JDISVServingTrayModel.h"

typedef void(^ServingTrayBottomBtnBlock)(NSInteger index);

NS_ASSUME_NONNULL_BEGIN

@interface JDISVServingTrayViewController : UIViewController

@property (strong,nonatomic) NSArray<JDISVServingTrayModel *> *dataList;
@property (copy,nonatomic) ServingTrayBottomBtnBlock pressedBlock;

@end

NS_ASSUME_NONNULL_END
