//
//  JDISVSettlementEmptyFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementEmptyFloor.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>

#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import "JDISVSettlementEmptyFloorModule.h"

static NSString * const kJDISVSettlementEmptyFloorRequestAction = @"JDISVSettlementEmptyFloorRequestAction";

@interface JDISVSettlementEmptyFloor()<JDCDISVActionTransferProtocol>
@property (nonatomic, strong) JDISVSettlementEmptyFloorModule *viewModel;
@end

@implementation JDISVSettlementEmptyFloor

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
    }
    return self;
}

- (void)floorDidLoad:(nonnull id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    @weakify(self)
    UIImage *errorImage = [[JDISVResourceManager shareInstance] imageWithImageType:self.viewModel.imageType];
    UIView *ev = [[JDISVPlatformService sharedService] getLoadErrorViewWithMessage:self.viewModel.emptyViewInfo errorImage:errorImage buttonText:self.viewModel.buttonTitle reloadButtonBlock:^(UIView * _Nonnull errorView) {
        @strongify(self)
        [self removeFromSuperview];
        
        JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVSettlementEmptyFloorRequestAction];
        action.value = self.viewModel.refreshType;
        [self isv_sendAction:action];
    }];
    
    [self addSubview:ev];
    [ev mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self);
    }];
}
@end
