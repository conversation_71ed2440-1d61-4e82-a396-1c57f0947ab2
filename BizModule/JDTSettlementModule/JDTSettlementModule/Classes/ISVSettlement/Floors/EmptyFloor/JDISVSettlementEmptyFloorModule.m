//
//  JDISVSettlementEmptyFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementEmptyFloorModule.h"

#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>

#import "JDISVSettlementEmptyFloor.h"

JDISVRegisterFloorModule(KaCheckEmptyFloor, JDISVSettlementEmptyFloorModule)

@interface JDISVSettlementEmptyFloorModule ()

@end

@implementation JDISVSettlementEmptyFloorModule
- (UIView *)floorView {
    UIView* view = [[JDISVSettlementEmptyFloor alloc] init];
    return view;
}

- (CGFloat)floorHeight {
    return [UIScreen mainScreen].bounds.size.height - [self isv_pd_navigationBar_safeAreaInsets].top - 44.f;;
}

- (CGFloat)floorTopMarge {
    CGFloat statusBarHeight = [self isv_pd_navigationBar_safeAreaInsets].top;
    return statusBarHeight + 44.f;
}

- (JDISVFloorType)floorType{
    return JDISVFloorTypeErrorFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSError *error = data[@"data"];
    self.emptyViewInfo = SettlementLan(@"setllement_reload_failed") ;
    self.buttonTitle = SettlementLan(@"setllement_repty") ;
    self.imageType = JDISVImageTypeFailToLoad;
    self.refreshType = @0;
    NSString *emptyType = [error.userInfo objectForKey:@"empty_type"] ? : @"";
    if ([error.domain isEqualToString:@"JDISVSettlementAddressError"] && [emptyType isEqualToString:@"JDISVSettlementEmptyViewTypeNoAddress"]) {
        // 无地址提示 无地址
        self.emptyViewInfo = SettlementLan(@"checkout_dialog_no_address_title") ;
        self.buttonTitle = SettlementLan(@"checkout_no_address_positive") ;
        self.imageType = JDISVImageTypeNoDataAddress;
        self.refreshType = @1;
    }
}

#pragma mark - private

- (UIEdgeInsets)isv_pd_navigationBar_safeAreaInsets {
    if (@available(iOS 11.0, *)) {
        UIEdgeInsets safeAreaInsets = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
        if (safeAreaInsets.bottom > 0) {
            return safeAreaInsets;
        }
        return UIEdgeInsetsMake(20, 0, 0, 0);
    }
    return UIEdgeInsetsMake(20, 0, 0, 0);
}
@end
