//
//  JDISVSettlementNavigationFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementNavigationFloor.h"

#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/UIImage+JDCDGradient.h>
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import "JDISVSettlementNavigationFloorModule.h"

static NSString * const kJDISVSettlementNavigationFloorBackAction = @"JDISVSettlementNavigationFloorBackAction";

@interface JDISVSettlementNavigationFloor ()<JDCDISVActionTransferProtocol>
// 导航栏
@property (nonatomic, strong) KANavigationBar *orderTrackNavigationBar;
// 导航背景
@property (nonatomic, strong) KANavigationBarBackgrounItem *navigationBackgroundItem;
@property (nonatomic, strong) JDISVSettlementNavigationFloorModule *viewModel;
@end

@implementation JDISVSettlementNavigationFloor

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
}

- (void)setup {
    // 初试状态 只显示back 背景色:同背景色
    self.orderTrackNavigationBar = [[KANavigationBar alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIWindow ka_uikit_navigationHeight])];
    [self addSubview:self.orderTrackNavigationBar];
    //导航内容组装
    __weak typeof(self) weakSelf = self;
    self.orderTrackNavigationBar
    .decorator
    .backButton(^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf navigationBackItemClicked];
    }) //返回按钮
    .title(SettlementLan(@"checkout_title_bar_title") , NSTextAlignmentCenter) //标题
    .render();
    self.navigationBackgroundItem = [[KANavigationBarBackgrounItem alloc] init];
    self.navigationBackgroundItem.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self.orderTrackNavigationBar pushNavigationBarItem:self.navigationBackgroundItem];
}


#pragma mark - Action
- (void)navigationBackItemClicked {
    JDCDISVAction *action = [JDCDISVAction actionWithType:kJDISVSettlementNavigationFloorBackAction];
    [self isv_sendAction:action];
}

#pragma mark - JDCDISVActionTransferProtocol

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:kJDISVSettlementNavigationFloorBackAction]) {
        //back
        [self addCartByController:controller action:action];
        [controller.navigationController popViewControllerAnimated:YES];
        return YES;
    }
    
    return NO;
}

- (void)addCartByController:(UIViewController *)controller  action:(JDCDISVAction *)action {
    NSDictionary *pramData = self.viewModel.data;
    if (pramData != nil) {
        NSNumber* isBuyNow = pramData[@"isNormalBuyNow"];
        BOOL isNormalBuyNow = [isBuyNow boolValue];
        if (isNormalBuyNow) {
            NSMutableDictionary *item = [NSMutableDictionary dictionary];
            [item setObject:pramData[@"skuId"] forKey:@"skuId"];
            [item setObject:pramData[@"skuCount"] forKey:@"num"];
            [item setObject:@(1) forKey:@"itemType"];
        
            NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShoppingCart];
            [JDRouter openURL:[NSString stringWithFormat:@"router://%@/addCart", module] arg:@{@"operations": @{@"products": @[item]}} error:nil completion:^(NSDictionary *  _Nullable object) {
                NSString *code = [object objectForKey:@"code"];
                if ([code isEqual:@"0"]) {
                    JDCDISVAction *ac = [JDCDISVAction actionWithType:@"JDISVPDAddCartSuccessAction" broadcastAction:YES];
                    [self isv_sendAction:ac];
                }
            }];
        }
    }
}
@end
