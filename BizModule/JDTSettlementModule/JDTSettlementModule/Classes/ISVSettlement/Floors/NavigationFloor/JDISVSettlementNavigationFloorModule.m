//
//  JDISVSettlementNavigationFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementNavigationFloorModule.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVSettlementNavigationFloor.h"


JDISVRegisterFloorModule(KaCheckNavBarFloor, JDISVSettlementNavigationFloorModule)

@interface JDISVSettlementNavigationFloorModule ()

@end

@implementation JDISVSettlementNavigationFloorModule
- (instancetype)init
{
    self = [super init];
    if (self) {
    }
    return self;
}

- (UIView *)floorView {
    return [[JDISVSettlementNavigationFloor alloc] init];
}

- (CGFloat)floorHeight {
    CGFloat statusBarHeight = [self isv_pd_navigationBar_safeAreaInsets].top;
    return statusBarHeight + 44;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeTopFixFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    if (data[@"data"][@"routerParams"] != nil) {
        self.data = data[@"data"][@"routerParams"];
        self.commonModel = commonModel;
    }
}

#pragma mark - private
- (UIEdgeInsets)isv_pd_navigationBar_safeAreaInsets {
    if (@available(iOS 11.0, *)) {
        UIEdgeInsets safeAreaInsets = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
        if (safeAreaInsets.bottom > 0) {
            return safeAreaInsets;
        }
        return UIEdgeInsetsMake(20, 0, 0, 0);
    }
    return UIEdgeInsetsMake(20, 0, 0, 0);
}
@end
