//
//  JDISVSettlementAmountFloorNetService.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#define KASettlementAmountFloorNetService [JDISVSettlementAmountFloorNetService sharedService]

@class JDISVSettlementAmountFloorMainModel;

@interface JDISVSettlementAmountFloorNetService : NSObject

@property (nonatomic,assign) bool ISPresellCapacity;
@property (nonatomic,assign) bool ISFirstMoenyCapacity;
@property (nonatomic,assign) bool ISGroupBuyCapacity;
@property (nonatomic,assign) bool ISCODPayType;//是否使用了COD支付
@property (nonatomic,assign) NSInteger mainProductCount;//主品种类数量
@property (nonatomic,assign) NSInteger source;
@property (nonatomic,strong) NSString *submitUseCouponCode;//支付时，使用的优惠码

@property (nonatomic,assign) NSInteger isCheckoutPageFirstLoadData;//结算页加载数据的次数

+ (instancetype)sharedService;


/// 获取优惠券列表
/// @param orderStrParam 是否使用智能选券
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestCouponListWithParam:(NSDictionary *)orderStrParam
                                                   sourceFrom:(NSInteger)source
                                                     complete:(void(^)(JDISVSettlementAmountFloorMainModel *model, NSError *  _Nullable error))completeBlock;

/// 使用/取消使用优惠券 se_useOrCancelCoupon
/// @param orderStrParam 优惠券信息
/// @param source 来源
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestUseOrCancelCouponWithParam:(NSDictionary *)orderStrParam
                                                           sourceFrom:(NSInteger)source
                                                            complete:(void(^)(JDISVSettlementAmountFloorMainModel *model, NSError *  _Nullable error))completeBlock;

/// 积分刷新接口 se_useJdBeanPay
/// @param orderStrParam 积分参数
/// @param source source
/// @param completeBlock 完成回调
- (NSURLSessionDataTask * _Nonnull)requestSettlementUsePointWithParam:(NSDictionary *)orderStrParam
                                                           sourceFrom:(NSInteger)source
                                                             complete:(void(^)(NSDictionary * _Nullable resultInfo, NSError *  _Nullable error))completeBlock;

@end

NS_ASSUME_NONNULL_END
