//
//  JDISVSettlementAmountFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorModule.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import "JDISVSettlementAmountFloor.h"

#import "JDISVSettlementAmountFloorModel.h"
#import "JDISVSettlementAmountFloorSectionViewModel.h"
#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

#import "JDISVSettlementAmountFloorNetService.h"

#import "JDISVSettlementAmountFloorDiscountCodeCellViewModel.h"
#import "JDISVDiscountCodeViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import "JDISVSettlementAmountFloorFreightCellViewModel.h"
#import "JDISVSettlementAmountFloorDiscountCellViewModel.h"
#import "CHECKDetailModel.h"
#import "CHECKPriceAndDiscountFloor.h"
#import "CHECKCouponModel.h"

JDISVRegisterFloorModule(KaCheckAmountFloor, JDISVSettlementAmountFloorModule);
@interface JDISVSettlementAmountFloorModule ()

@property (nonatomic, assign) CGFloat height;

@end

@implementation JDISVSettlementAmountFloorModule
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.sectionViewModels = [NSArray array];
        _height = 0;
    }
    return self;
}

//- (UIView *)floorView {
//    UIView* v = [[JDISVSettlementAmountFloor alloc] init];
//    return v;
//}

- (Class)tableViewFloorClass {
    return [CHECKPriceAndDiscountFloor class];
}

- (CGFloat)floorHeight {
    
    return self.height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (BOOL)ignoreCorner {
    return YES;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSMutableArray *cellDataMArr = [NSMutableArray array];
    CHECKAmountSummaryModel *amount = [CHECKAmountSummaryModel yy_modelWithDictionary:data[@"data"][@"amountSummary"]];
    CHECKCouponModel *coupon = [CHECKCouponModel yy_modelWithDictionary:data[@"data"][@"couponList"]];
    self.couponModel = coupon;
    
    CHECKPriceAndDiscountCellModel *totalPriceModel = [[CHECKPriceAndDiscountCellModel alloc] init];
    totalPriceModel.cellType = CHECKPriceAndDiscountCellTypeTotalAmount;
    totalPriceModel.data = amount;
    totalPriceModel.cellHeight = 18/*顶部间距*/ + 22/*高度*/ + 6/*底部间距*/;
    [cellDataMArr addObject:totalPriceModel];
    
    CHECKPriceAndDiscountCellModel *shippingFeeModel = [[CHECKPriceAndDiscountCellModel alloc] init];
    shippingFeeModel.cellType = CHECKPriceAndDiscountCellTypeShippingFee;
    shippingFeeModel.data = amount;
    shippingFeeModel.cellHeight = 6/*顶部间距*/ + 22/*高度*/ + 6/*底部间距*/;
    [cellDataMArr addObject:shippingFeeModel];
    
    CHECKPriceAndDiscountCellModel *couponModel = [[CHECKPriceAndDiscountCellModel alloc] init];
    couponModel.cellType = CHECKPriceAndDiscountCellTypeDiscountCoupon;
    couponModel.data = coupon;
    couponModel.cellHeight = 6/*顶部间距*/ + 22/*高度*/ + 6/*底部间距*/;
    [cellDataMArr addObject:couponModel];
    
    self.cellDataArr = [cellDataMArr copy];
}

- (void)updateData2:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];
    NSDictionary *featureExt = data[@"ext"] ? : @{}; // 获取配置开关
    self.sourceType = [resultInfo[@"source"] integerValue];
    self.commonModel = commonModel;
    
    // 转楼层模型
    NSMutableArray *floorsModels = [NSMutableArray array];
    for (NSDictionary *floorData in floorsArray) {
        JDISVSettlementAmountFloorModel *floorModel = [JDISVSettlementAmountFloorModel yy_modelWithDictionary:floorData];
        if(floorModel.uuid.length == 0){
            floorModel.uuid = floorModel.info[@"uuid"];
        }
        [floorsModels addObject:floorModel];
    }
    
    //预售付尾款，保存优惠券VC列表数据
    if (ISPresell && ISFirstMoenyFlag == NO) {
        for (JDISVSettlementAmountFloorModel *floorData in floorsModels) {
            if ([floorData.uuid containsString:@"homePageFloorCouponVosFloor"]) {
                self.couponVos = floorData.info[@"couponVos"];//优惠券VC列表数据
            }
        }
    }
    
    // 金额Section
    NSMutableArray *sections = [NSMutableArray array];

    NSArray *amountSection = [self updateAmountFloorsWith:[NSArray arrayWithArray:floorsModels] allOriginData:data commond:commonModel featureExt:featureExt];

    if (amountSection.count > 0) {
//        [sections addObject:[NSArray arrayWithArray:amountSection]];
        // 添加批免运费楼层展示数据
        BOOL haveDiscount = NO;
        for (JDISVSettlementAmountFloorBaseCellViewModel *cellVM in amountSection) {
            if ([cellVM isKindOfClass:[JDISVSettlementAmountFloorDiscountCellViewModel class]]) {
                haveDiscount = YES;
                break;;
            }
        }
        
        if (!haveDiscount) {
            JDISVSettlementAmountFloorDiscountCellViewModel *disCountCellVM = nil;
            for (JDISVSettlementAmountFloorBaseCellViewModel *cellVM in amountSection) {
                if ([cellVM isKindOfClass:[JDISVSettlementAmountFloorFreightCellViewModel class]]) {
                    JDISVSettlementAmountFloorFreightCellViewModel *freightCellVM = (JDISVSettlementAmountFloorFreightCellViewModel *)cellVM;
                    id dataDic = [freightCellVM yy_modelToJSONObject];
                    disCountCellVM = [JDISVSettlementAmountFloorDiscountCellViewModel yy_modelWithDictionary:dataDic];
                    
                    disCountCellVM.amountTitleAttributedString = freightCellVM.freightTitleAttributedString;
                    NSMutableAttributedString *titleAttributedString = [disCountCellVM.amountTitleAttributedString mutableCopy];
                    [titleAttributedString replaceCharactersInRange:NSMakeRange(0, titleAttributedString.length) withString:SettlementLan(@"settlement_title_shipping_fee_discount")];
                    disCountCellVM.amountTitleAttributedString = titleAttributedString;
                    disCountCellVM.freightModel = freightCellVM.model;
//                    UIColor *textColor = [UIColor colorWithRed:94/255.0 green:205/255.0 blue:184/255.0 alpha:1.0];
//
//                    disCountCellVM.amountAttributedString = freightCellVM.freightAmountAttributedString;
//                    NSMutableAttributedString *originalAttributedString = [disCountCellVM.amountAttributedString mutableCopy];
//                    NSString *text = originalAttributedString.string;
//                    text = [text stringByReplacingOccurrencesOfString:@"+" withString:@"-"];
//
//                    NSMutableAttributedString *newAttributedString = [[NSMutableAttributedString alloc] initWithString:text];
//                    UIColor *newColor = textColor;
//                    [newAttributedString addAttribute:NSForegroundColorAttributeName value:newColor range:NSMakeRange(0, newAttributedString.length)];
//
//                    // 获取原始属性字符串的字体属性
//                    NSRange range = NSMakeRange(0, originalAttributedString.length);
//                    [originalAttributedString enumerateAttributesInRange:range options:0 usingBlock:^(NSDictionary<NSAttributedStringKey,id> *attrs, NSRange subRange, BOOL *stop) {
//                        id fontAttribute = attrs[NSFontAttributeName];
//                        if (fontAttribute) {
//                            // 将字体属性应用到新的属性字符串上
//                            [newAttributedString addAttribute:NSFontAttributeName value:fontAttribute range:NSMakeRange(0, newAttributedString.length)];
//                        }
//                    }];
                    
                    NSString *amount = @"";
                    if ([disCountCellVM.freightModel.discountFreight jdcd_validateString]) {
                        amount = [NSString stringWithFormat:@"%@", disCountCellVM.freightModel.discountFreight];
                    }
                    NSMutableAttributedString *amountPrice = [[NSMutableAttributedString alloc] init];
                    [amountPrice KA_renderWithPriceStr:amount type:KAPriceTypeP4 colorType:@"#11"];
                    
                    NSString *operatorStr = @"-";
                    NSMutableAttributedString *operatorAttr = [[NSMutableAttributedString alloc] init];
                    [operatorAttr KA_renderWithPriceStr:amount type:KAPriceTypeP4 colorType:@"#11"];
                    
                    NSMutableAttributedString *freightTmpAttributedString = [[NSMutableAttributedString alloc] initWithString:operatorStr attributes:@{
                        NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C11"],
                        NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
                    }];
                    [freightTmpAttributedString appendAttributedString:amountPrice];
                    
                    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:freightTmpAttributedString.string attributes:@{
                        NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C11"],
                        NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
                    }];
                    disCountCellVM.amountAttributedString = [attributedString copy];

//                    disCountCellVM.amountAttributedString = newAttributedString;
                    disCountCellVM.cellName = @"JDISVSettlementAmountFloorDiscountCell";
                    disCountCellVM.cellIdentifier = @"JDISVSettlementAmountFloorDiscountCell";
                }
            }
            NSMutableArray *tempArr = [amountSection mutableCopy];
//            NSString *amountStr = disCountCellVM.amountAttributedString.string;
//            NSArray *amountArr = [amountStr componentsSeparatedByString:@" "];
//            if (disCountCellVM && amountArr && amountArr.count == 2) {
//                CGFloat amount = [[amountArr objectAtIndex:1] floatValue];
//                if (amount > 0) {
//                }
//            }
            if ([disCountCellVM.freightModel.discountFreight floatValue] > 0) {
                [tempArr addObject:disCountCellVM];
            }
            [sections addObject:[NSArray arrayWithArray:tempArr]];
        } else {
            [sections addObject:[NSArray arrayWithArray:amountSection]];
        }
    }
    self.sectionViewModels = [NSArray arrayWithArray:sections];
    [self autoUseCouponCodeLogic];
}

// 主接口更新金额楼层
- (NSArray *)updateAmountFloorsWith:(NSArray<JDISVSettlementAmountFloorModel *> *)floorDatas allOriginData:(NSDictionary *)allOriginData commond:(JDISVFloorCommonModel *)commonModel featureExt:(NSDictionary *)featureExt{
    
    JDISVSettlementAmountFloorSectionViewModel *amountSectionViewModel = [[JDISVSettlementAmountFloorSectionViewModel alloc] init];
    [amountSectionViewModel updateSectionWithUUIDKey:nil floorKeys:nil allFloorData:floorDatas allOriginData:allOriginData commonModel:commonModel featureExt:featureExt];
    
    NSArray *amountSections = [NSArray arrayWithArray:amountSectionViewModel.floorViewModels];
    
    return amountSections;
}

/// 判断是否需要自动使用缓存的优惠码
- (void)autoUseCouponCodeLogic{
    JDISVSettlementAmountFloorDiscountCodeCellViewModel *discountCodeAmountViewModel = nil;
    for (id itemObjec in [self.sectionViewModels firstObject]) {
        if ([itemObjec isKindOfClass:[JDISVSettlementAmountFloorDiscountCodeCellViewModel class]]){
            discountCodeAmountViewModel = itemObjec;
            break;
        }
    }
    if (discountCodeAmountViewModel &&
        discountCodeAmountViewModel.codeUserStatus == JDISVSettlementCouponCodeUseStatusCanUse){//判断是否立即使用缓存优惠码
        NSString *copyCode = [JDISVCouponTagModel loadPromotionCode];//获取缓存优惠码
        if ([copyCode jdcd_validateString]){
            NSDictionary *params = @{@"clientVersion":@"1.0.0",
                     @"client":@"iOS",
                     @"lang":@"zh_CN",
                     @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                     @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                    @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                     @"orderStr":@{@"couponCode":copyCode,
                                   @"selected":[NSNumber numberWithBool:YES]}};
            [[JDISVDiscountCodeViewModel vaildateCouponCodeWith:params isDelete:NO] subscribeNext:^(id  _Nullable x) {
               
            }];
        }
    }
}

- (CGFloat)height {
    CGFloat totalHeight = 0;
//    if (_sectionViewModels && _sectionViewModels.count > 0) {
//        for (NSArray *cellViewModels in _sectionViewModels) {
//            if (cellViewModels && cellViewModels.count > 0) {
//                for (JDISVSettlementAmountFloorBaseCellViewModel *vm in cellViewModels) {
//                    totalHeight += vm.height;
//                }
//            }
//        }
//        totalHeight +=  [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"] * (_sectionViewModels.count - 1);
//    }
    for (CHECKPriceAndDiscountCellModel *cellData in self.cellDataArr) {
        totalHeight += cellData.cellHeight;
    }
    return totalHeight;
}


#pragma mark - 信号
// 使用积分信号
- (RACSignal *)loadUsePointDataSignalParamOf:(NSDictionary *)param {
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        [KASettlementAmountFloorNetService requestSettlementUsePointWithParam:param sourceFrom:self.sourceType complete:^(NSDictionary * _Nullable resultInfo, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:resultInfo];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}
@end
