//
//  JDISVSettlementAmountFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloor.h"

#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>

#import <NSBundle+JDISVSettlement.h>

#import "JDISVSettlementAmountFloorModule.h"
#import "JDISVSettlementAmountFloorBaseCell.h"
#import "JDISVSettlementAmountFloorBaseCellViewModel.h"
#import "JDISVSettlementAmountFloorPointModel.h"
#import "JDISVSettlementCouponViewModel.h"
#import "JDISVSettlementPointInputController.h"
#import "JDISVSettlementPointController.h"
#import "JDISVSettlementCouponController.h"
#import "JDISVSumMoneyTableViewCell.h"
#import "JDISVLastMoneyView.h"
#import "JDISVDiscountCodeController.h"
#import "JDISVDiscountCodeViewModel.h"
#import "JDISVSettlementAmountFloorDisocuntCodeCell.h"
#import "JDISVSettlementPub.h"
#import "JDISVSettlementAmountFloorTariffCell.h"
#import "JDISVSettlementAmountFloorCODCell.h"
#import "JDISVSettlementAmountFloorDiscountCell.h"

@interface JDISVSettlementAmountFloor ()<UITableViewDelegate, UITableViewDataSource, JDCDISVActionTransferProtocol>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) JDISVSettlementAmountFloorModule *viewModel;
@property (nonatomic, strong) JDISVSettlementCouponController *couponVC;
@property (nonatomic, strong)  JDISVLastMoneyView *lastMoneyView;//预售尾款弹窗
@property (nonatomic, strong) JDISVDiscountCodeViewModel *discountCodeviewModel ;

@end

@implementation JDISVSettlementAmountFloor
- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    
    self.tableView.backgroundColor = [UIColor clearColor];
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    self.tableView.scrollEnabled = NO;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;
    self.tableView.estimatedRowHeight = 0;
    self.tableView.estimatedSectionHeaderHeight = 0;
    self.tableView.estimatedSectionFooterHeight = 0;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // Group样式的TableView Header和Footer会有空白
    self.tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorCouponCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorCouponCell"];//优惠券Cell
    [self.tableView registerClass:[JDISVSettlementAmountFloorDisocuntCodeCell class] forCellReuseIdentifier:@"JDISVSettlementAmountFloorDisocuntCodeCell"];//优惠码
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorFreightCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorFreightCell"];//运费Cell
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorDiscountCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorDiscountCell"];//免运费
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorItemCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorItemCell"];//商品金额？？
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorPointCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorPointCell"];//积分
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorInvoiceCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorInvoiceCell"];//发票
    [self.tableView registerNib:[UINib nibWithNibName:@"JDISVSettlementAmountFloorRoundCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: @"JDISVSettlementAmountFloorRoundCell"];//圆角Cell
    [self.tableView registerClass:[JDISVSettlementAmountFloorTariffCell class] forCellReuseIdentifier:@"JDISVSettlementAmountFloorTariffCell"];//关税
    [self.tableView registerClass:[JDISVSettlementAmountFloorCODCell class] forCellReuseIdentifier:@"JDISVSettlementAmountFloorCODCell"];//COD
    [self.tableView registerClass:UITableViewCell.class forCellReuseIdentifier:@"UITableViewCell"];
    [[self tableView] registerClass:[JDISVSumMoneyTableViewCell class] forCellReuseIdentifier:@"JDISVSumMoneyTableViewCell"];
    [self addSubview:self.tableView];
    @weakify(self)
    [RACObserve(_tableView, scrollEnabled) subscribeNext:^(NSNumber*  _Nullable x) {
        if(x.boolValue){
            dispatch_async(dispatch_get_main_queue(), ^{
                @strongify(self)
                self.tableView.scrollEnabled = NO;
            });
        }
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(0);
        make.trailing.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
    }];
    
}

//- (UIScrollView *)associateScrollView{
//    return self.tableView;
//}
//
//- (CGRect)scrollViewFrame{
//    return CGRectMake(0, 0, UIScreen.mainScreen.bounds.size.width, [self.viewModel floorHeight]);
//}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
    [self.tableView reloadData];
    
    if (self.couponVC) {
        [self.couponVC reloadPresaleEndPaymentUpdateWithCouponVos:self.viewModel.couponVos];
    }
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.tableView.scrollEnabled = NO;
    });
    
    if (self.viewModel == nil || self.viewModel.sectionViewModels == nil) {
        return  0;
    }
    
    if (self.viewModel.sectionViewModels > 0) {
        return  self.viewModel.sectionViewModels.count;
    } else {
        return  0;
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:section];
        if (cellViewModels.count > 0) {
            return cellViewModels.count;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.viewModel!=nil && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementAmountFloorBaseCellViewModel *cellViewModel = [cellViewModels objectAtIndex:indexPath.row];
            return cellViewModel.height;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 0.01f;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], 0.01f)];
    header.backgroundColor = [UIColor clearColor];
    return header;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]) - [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"], [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"])];
    footer.backgroundColor = [UIColor clearColor];
    return footer;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementAmountFloorBaseCell *cell = nil;
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.cellIdentifier forIndexPath:indexPath];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
                __weak typeof(self) weakSelf = self;
                [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(NSDictionary *parames) {
                    __strong typeof(weakSelf) strongSelf = weakSelf;
                    [strongSelf processOrderDelegateActionWithParames:parames];
                }];
            }
        }
        
    }
    
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            if ([cell respondsToSelector:@selector(updateCellWithViewModel:)]) {
                [cell performSelector:@selector(updateCellWithViewModel:) withObject:itemViewModel];
            }
        }
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
}

#pragma mark - Actions
- (void)processOrderDelegateActionWithParames:(NSDictionary *)parames {
    NSString *cellIdentifier = [parames objectForKey:@"cell_identifier"];
    NSString *actionType = [parames objectForKey:@"action_type"];
    if ([cellIdentifier isEqualToString:@"JDISVSettlementAmountFloorCouponCell"]) {
        if ([actionType isEqualToString:@"show_select_coupon_page"]) {
            // 展示选择优惠券页面
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorOpenCouponPage"];
            [self isv_sendAction:action];
        }
    } else if ([cellIdentifier isEqualToString:@"JDISVSettlementAmountFloorPointCell"]) {
        if ([actionType isEqualToString:@"show_info_detail"]) {
            // 提示不可用原因
            NSDictionary *infoMap = [parames objectForKey:@"data"];
            NSString *title = [infoMap objectForKey:@"title"];
            NSString *content = [infoMap objectForKey:@"content"];
            [self showIntroduceAlertWithTitle:title andContent:content];
        } else if ([actionType isEqualToString:@"show_select_point_page"]) {
            // 打开积分页面
            JDISVSettlementAmountFloorPointModel *amountFloorPointModel = [parames objectForKey:@"data"];
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorOpenPointPage"];
            action.value = amountFloorPointModel;
            [self isv_sendAction:action];
        }
    } else if([cellIdentifier isEqualToString:@"JDISVSettlementAmountEndPaymentPresaleFloorItemCell"]){
        JDISVLastMoneyView *lastMoneyView = [[JDISVLastMoneyView alloc]init];
        self.lastMoneyView = lastMoneyView;
        [self.lastMoneyView show];
        [self.lastMoneyView updateUIWithData:self.viewModel.commonModel.commonData[@"presaleToastParams"]];
    } else if ([cellIdentifier isEqualToString:@"JDISVSettlementAmountFloorInvoiceCell"]) {
        if ([actionType isEqualToString:@"show_select_invoice_page"]) {
            // 打开发票页面H5
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorOpenInvoicePage"];
            [self isv_sendAction:action];
        }
    }else if ([cellIdentifier isEqualToString:@"JDISVSettlementAmountFloorDisocuntCodeCell"]){
        if ([actionType isEqualToString:@"show_select_discount_code_page"]){
            // 展示兑换优惠码页面
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorOpenDiscountCodePage"];
            [self isv_sendAction:action];
        }
    } else if ([cellIdentifier isEqualToString:@"JDISVSettlementAmountFloorTariffCell"]) {
        if ([actionType isEqualToString:@"show_info_tariff"]) {
            // 提示不可用原因
            NSDictionary *infoMap = [parames objectForKey:@"data"];
            NSString *title = [infoMap objectForKey:@"title"];
            NSString *content = [infoMap objectForKey:@"content"];
            [self showIntroduceAlertWithTitle:content andContent:@""];
        }
    }
       
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    
    if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorOpenPointPage"]) {
        // 打开积分页
        JDISVSettlementAmountFloorPointModel *amountFloorPointModel = (JDISVSettlementAmountFloorPointModel *)action.value;
//        [self openUsePointPageWith:amountFloorPointModel controller:controller];
        [self JH_openUsePointPageWith:amountFloorPointModel controller:controller];
        return YES;
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorOpenCouponPage"]) {
        [self openUseCouponPageWithController:controller];
        return YES;
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorOpenInvoicePage"]) {
        [self openUseInvoicePageWithController:controller];
        return YES;
    }else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorOpenDiscountCodePage"]){
        [self openDiscountCodePagewithController:controller];
        return YES;
    }
    
    return NO;
}
#pragma mark - Action
/// 展示说明弹窗
- (void)showIntroduceAlertWithTitle:(NSString *)title andContent:(NSString *)content {
    [KAAlert alert].config
    .renderW4Alignment(title, content, NSTextAlignmentLeft)
    .addFillAction(SettlementLan(@"setllement_i_known") ,^{
        // 收回
    })
    .alertShow();
}
///标品 - 打开使用积分浮层
/// @param data 浮层数据模型
- (void)openUsePointPageWith:(JDISVSettlementAmountFloorPointModel *)data controller:(UIViewController *)controller{
    JDISVSettlementPointController *pointController = [[JDISVSettlementPointController alloc] initWithNibName:@"JDISVSettlementPointController" bundle:[NSBundle jdisvSettlement_bundle]];
    [pointController updateViewModelWith:data];
    
    @weakify(self)
    pointController.confirmCallback = ^(NSDictionary * _Nonnull param) {
        @strongify(self)
        [PlatformService showLoadingInView:controller.view];
        [[self.viewModel loadUsePointDataSignalParamOf:param] subscribeNext:^(NSDictionary *resultInfo) {
            @strongify(self)
            [PlatformService dismissInView:controller.view];
            // 通知外部刷新
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorOpenPointPageUsePointRefresh"];
            action.value = resultInfo;
            [self isv_sendAction:action];
        } error:^(NSError * _Nullable error) {
            [PlatformService dismissInView:controller.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        }];
    };
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:pointController presentingViewController:controller];
    presentationVC.type = KAFloatLayerTypeCustom;
    presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
    pointController.transitioningDelegate = presentationVC;
    [controller presentViewController:pointController animated:YES completion:nil];
}

/// 极狐定开 - 打开使用积分浮层
/// /// @param data 浮层数据模型
- (void)JH_openUsePointPageWith:(JDISVSettlementAmountFloorPointModel *)data controller:(UIViewController *)controller{
    JDISVSettlementPointInputController *pointController = [[JDISVSettlementPointInputController alloc] init];
    [pointController updateViewModelWith:data];
    
    @weakify(self)
    pointController.confirmCallback = ^(NSDictionary * _Nonnull param) {
        @strongify(self)
        [PlatformService showLoadingInView:controller.view];
        [[self.viewModel loadUsePointDataSignalParamOf:param] subscribeNext:^(NSDictionary *resultInfo) {
            @strongify(self)
            [PlatformService dismissInView:controller.view];
            // 通知外部刷新
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorOpenPointPageUsePointRefresh"];
            action.value = resultInfo;
            [self isv_sendAction:action];
        } error:^(NSError * _Nullable error) {
            [PlatformService dismissInView:controller.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        }];
    };
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:pointController presentingViewController:controller];
    presentationVC.presentedView.backgroundColor = [UIColor clearColor];
    presentationVC.type = KAFloatLayerTypeCustom;
    presentationVC.contentHeight = 500;
    presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
    presentationVC.animationDuration = 0.1;
    pointController.transitioningDelegate = presentationVC;
    [controller presentViewController:pointController animated:YES completion:nil];
}

/// 打开使用优惠券浮层
- (void)openUseCouponPageWithController:(UIViewController *)controller {
    if (ISPresell && ISFirstMoenyFlag == NO) {//预售付尾款
        if (self.couponVC) {
            [self.couponVC presaleEndPaymentUpdateWithCouponVos:nil];
            return;
        }
        JDISVSettlementCouponController *couponController = [[JDISVSettlementCouponController alloc] initWithNibName:@"JDISVSettlementCouponController" bundle:[NSBundle jdisvSettlement_bundle]];
        [couponController presaleEndPaymentUpdateWithCouponVos:self.viewModel.couponVos];

        @weakify(self)
        // 点击确认按钮
        couponController.confirmCallback = ^(NSDictionary * _Nonnull param) {
            @strongify(self)
            NSArray *couponIds = param[@"couponIds"];//选中的优惠券Id列表
            [self.viewModel.commonModel.commonData setObject:couponIds ? : @[] forKey:@"couponIds"];
        };
        // 点击关闭
        couponController.closeCallback = ^(NSDictionary * _Nonnull param) {
            @strongify(self)
            NSArray *couponIds = param[@"couponIds"];//选中的优惠券Id列表
            [self.viewModel.commonModel.commonData setObject:couponIds ? : @[] forKey:@"couponIds"];
        };
        //勾选优惠券刷新接口
        couponController.refreshMainPageCallback = ^(NSDictionary * _Nonnull param) {
            @strongify(self)
            NSArray *couponIds = param[@"couponIds"] ? : @[];//选中的优惠券Id列表
            //预售付尾款，选择优惠券，通知外部刷新列表
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorPresaleEndPaymentUseCouponRefresh"];
            action.value = couponIds;
            [self isv_sendAction:action];
        };
        
        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:couponController presentingViewController:self];
        presentationVC.type = KAFloatLayerTypeCustom;
        presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
        
        presentationVC.dismissComplete = ^{
            self.couponVC = nil;
            NSArray *couponIds = couponController.viewModel.couponIds;//选中的优惠券Id列表
            [self.viewModel.commonModel.commonData setObject:couponIds ? : @[] forKey:@"couponIds"];
        };
        
        couponController.transitioningDelegate = presentationVC;
        self.couponVC = couponController;
        [controller presentViewController:couponController animated:YES completion:nil];
    }else{
        JDISVSettlementCouponController *couponController = [[JDISVSettlementCouponController alloc] initWithNibName:@"JDISVSettlementCouponController" bundle:[NSBundle jdisvSettlement_bundle]];
            [couponController updateWithSource:self.viewModel.sourceType];
        __block BOOL hasRequest = NO;
        @weakify(self)
        // 点击确认按钮
        couponController.confirmCallback = ^(NSDictionary * _Nonnull param) {
            @strongify(self)
            hasRequest = YES;
            // 通知外部请求数据并刷新

            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorUseCouponRequestAndRefresh"];
            action.value = [self  addressIdInCommonModel];
            [self isv_sendAction:action];
        };

        // 点击关闭
        couponController.closeCallback = ^(NSDictionary * _Nonnull param) {
            @strongify(self)
            hasRequest = YES;
            JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorResetCoupons"];
            action.value = [NSDictionary dictionaryWithDictionary:param];
            [self isv_sendAction:action];
        };

        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:couponController presentingViewController:self];
        presentationVC.type = KAFloatLayerTypeCustom;
        presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);

        presentationVC.dismissComplete = ^{
            NSDictionary *param = [couponController.viewModel resetAllCouponParam];
            if (hasRequest == NO && param) {
                JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorResetCoupons"];
                action.value = [NSDictionary dictionaryWithDictionary:param];
                [self isv_sendAction:action];
            }
        };

        couponController.transitioningDelegate = presentationVC;

        [controller presentViewController:couponController animated:YES completion:nil];
    }
}

/// 打开优惠码兑换页面
- (void)openDiscountCodePagewithController:(UIViewController *)controller{
    JDWeakSelf;
    if(!self.discountCodeviewModel){
        self.discountCodeviewModel = [[JDISVDiscountCodeViewModel alloc] init];
        self.discountCodeviewModel.isFirst = YES;
        
        
        NSArray *floors = [self.viewModel.commonModel.allFloorOriginData jdcd_getArrayElementForKey:@"floors"];
        NSMutableArray* tmpArr = [NSMutableArray array];
        for (NSDictionary *dic in floors) {
            if ([[dic jdcd_getStringElementForKey:@"uuid"] containsString:@"homePageFloorCouponFloor"]){
                NSArray* useCode = [dic jdcd_getArrayElementForKey:@"partList"];
                for(NSDictionary* codeDic in useCode){
                    NSDictionary* infoDic = [codeDic jdcd_getDicElementForKey:@"info"];
                    NSDictionary* baseInfo = [infoDic jdcd_getDicElementForKey:@"VCYBB2C-P#used&basic"];
                    JDISVSettlementAmoutFloorDiscountCodeModel* settleCode = [JDISVSettlementAmoutFloorDiscountCodeModel yy_modelWithDictionary:[dic jdcd_getDicElementForKey:@"info"]];
                    [tmpArr addObject:settleCode];
                }
                self.discountCodeviewModel.settlePageCodes = [tmpArr copy];
                break;
            }
        }
        
    }
    
    JDISVDiscountCodeController *discountCode = [[JDISVDiscountCodeController alloc] initWithDiscountCodeViewModel:self.discountCodeviewModel];
    
    discountCode.confirmCallback = ^(NSDictionary * _Nonnull param) {
        JDStrongSelf;
        JDCDISVAction *action = [JDCDISVAction actionWithType:[param jdcd_getStringElementForKey:@"type"]];
        action.value = [param jdcd_getDicElementForKey:@"value"];
        [self isv_sendAction:action];
    };
    discountCode.closeCallback = ^(NSDictionary * _Nonnull param) {//关闭按钮点击事件
        JDStrongSelf;
        JDISVDiscountCodeController *vc = [param jdcd_getObjectForKey:@"presentedViewController" witClass:JDISVDiscountCodeController.class];
        [self closeFloatlayerWithDiscountCodeVC:vc];
    };
    
    KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:discountCode presentingViewController:self];
    presentationVC.type = KAFloatLayerTypeCustom;
    presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
    presentationVC.dismissComplete = ^{
        
    };
    presentationVC.tapTransparentBackgorundBlock = ^(UIViewController * _Nonnull presentedViewController) {//
        JDStrongSelf
        JDISVDiscountCodeController *vc = presentedViewController.presentedViewController;
        [self closeFloatlayerWithDiscountCodeVC:vc];
    };
    
    discountCode.transitioningDelegate = presentationVC;
    [controller presentViewController:discountCode animated:YES completion:nil];
}

/// - Parameter controller: controller
- (void)closeFloatlayerWithDiscountCodeVC:(JDISVDiscountCodeController *)controller{
    [[IQKeyboardManager sharedManager] resignFirstResponder];//收起键盘
    if ([controller whetherItIsNecessaryToDisplayTheRetainingBulletBox]){
        __weak typeof(controller) weakController = controller;
        [PlatformService showDefaultAlertWithTitle:SettlementLan(@"settlement_discount_code_detainment_desc")
                                    negativeAction:^{
            
        }
                                    positiveAction:^{
            __strong typeof(controller) sController = weakController;
            [sController dismissViewControllerAnimated:YES completion:nil];
        }
                                 cancelButtonTitle:SettlementLan(@"checkout_exit_dialog_cancel")
                                   doneButtonTitle:SettlementLan(@"settlement_discount_code_detainment_commit")
                               touchDoneCloseAlert:YES];
    }else{
        [controller dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)openUseInvoicePageWithController:(UIViewController *)controller {
    @weakify(self)
    void(^backActionBlock)(id data, NSString *string) = ^void(id data, NSString *string){
        @strongify(self)
        //收到H5返回的选择发票回调后，调获取发票列表主接口
        JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVSettlementAmountFloorSelectInvoiceRequestAndRefresh"];
        [self isv_sendAction:action];
    };

    NSString *h5Url = [PlatformService getJumpH5UrlByType:JDISVJumpH5UrlTypeInvoiceList withAppendParam:@""] ? : @"";
    NSDictionary* param = @{@"text_url":h5Url,@"backAction":backActionBlock};
    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
    NSString* routerStr= [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
    [JDRouter openURL:routerStr arg:param error:nil completion:^(id obj){
        // sl to do
        [controller.navigationController pushViewController:obj animated:YES];
    }];
}

- (NSDictionary *)addressIdInCommonModel {
    NSMutableDictionary *addressDic = [[NSMutableDictionary alloc] init];
    NSDictionary *addressMap = [self.viewModel.commonModel.commonData objectForKey:@"consigneeAddressMap"] ? : @{};
    NSDictionary *defaultAddressMap = [addressMap objectForKey:@"C-M#defaultAddressFloor&core"] ? : @{};
    NSString *addressId = [defaultAddressMap objectForKey:@"addressId"] ? : @"";
    [addressDic setObject:addressId forKey:@"addressId"];
    
    NSDictionary *mobileMap = [addressMap objectForKey:@"C-M#defaultAddressFloor&privacy"] ? : @{};
    NSString *mobile = [mobileMap objectForKey:@"starredMobile"];
    if (![mobile jdcd_validateString]) {
        mobileMap = [addressMap objectForKey:@"C-M#defaultAddressFloor&consignee"] ? : @{};
        mobile = [mobileMap objectForKey:@"mobile"];
    }
    [addressDic setObject:mobile ? : @"" forKey:@"mobile"];
    
    return addressDic;
}

#pragma mark - <dealloc>

- (void)dealloc{
    NSLog(@"JDISVSettlementAmountFloor dealloc");
}


@end
