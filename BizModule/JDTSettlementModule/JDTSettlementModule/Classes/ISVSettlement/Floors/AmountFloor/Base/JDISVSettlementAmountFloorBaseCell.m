//
//  JDISVSettlementAmountFloorBaseCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorBaseCell.h"

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

@implementation JDISVSettlementAmountFloorBaseCell

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel * _Nullable)viewModel {
    
}

- (RACSubject *)delegate {
    if (_delegate == nil) {
        _delegate = [[RACSubject alloc] init];
    }
    
    return _delegate;
}

@end
