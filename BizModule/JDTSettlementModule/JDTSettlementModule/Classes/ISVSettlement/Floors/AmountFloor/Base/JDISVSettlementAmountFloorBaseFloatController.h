//
//  JDISVSettlementAmountFloorBaseFloatController.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <UIKit/UIKit.h>
#import "JDISVSettlementAmountFloorBaseViewModel.h"
#import "JDISVSettlementAmountFloorBaseCellViewModel.h"
#import "JDISVSettlementAmountFloorBaseCell.h"

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementAmountFloorBaseViewModel;


@protocol JDISVSettlementAmountFloorBaseFloatControllerDataSource <NSObject>

@required
- (JDISVSettlementAmountFloorBaseViewModel *)floatControllerViewModel;
- (NSBundle *)floatControllerModuleBundle;
- (UITableView *)floatControllerTableView;

@end

@protocol JDISVSettlementAmountFloorBaseFloatControllerDelegate <NSObject>

@optional

- (void)customFloatControllerUI;

- (CGFloat)floatContentTableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section;
- (CGFloat)floatContentTableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section;
- (UIView *)floatContentTableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section;
- (UIView *)floatContentTableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section;

- (void)processCellActionWithParames:(NSDictionary *)parames;
- (void)clickCellIndexOf:(NSIndexPath *)indexPath;
- (void)mainTableViewDidScroll:(UITableView *)tableView;
@end


@interface JDISVSettlementAmountFloorBaseFloatController : UIViewController<JDISVSettlementAmountFloorBaseFloatControllerDataSource, JDISVSettlementAmountFloorBaseFloatControllerDelegate>

@end

NS_ASSUME_NONNULL_END
