//
//  JDISVSettlementAmountFloorBaseFloatController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorBaseFloatController.h"

@interface JDISVSettlementAmountFloorBaseFloatController ()<UITableViewDataSource, UITableViewDelegate>
@property (nonatomic, weak) id<JDISVSettlementAmountFloorBaseFloatControllerDelegate> delegate;
@property (nonatomic, weak) id<JDISVSettlementAmountFloorBaseFloatControllerDataSource> dataSource;
@property (nonatomic, strong) JDISVSettlementAmountFloorBaseViewModel *viewModel;
@end

@implementation JDISVSettlementAmountFloorBaseFloatController
#pragma mark - 生命周期

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        _delegate = self;
        _dataSource = self;
    }
    return self;
}

- (void)loadView {
    [super loadView];
    
    [self setupMainControllerView];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
}


#pragma mark - 绘制UI
- (void)setupMainControllerView {
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self initializeTableView];
    if (self.delegate && [self.delegate respondsToSelector:@selector(customFloatControllerUI)]) {
        [self.delegate customFloatControllerUI];
    }
}

// MARK:TableView配置
- (void)initializeTableView {
    
    UITableView *tableView = [[UITableView alloc] init];
    if (self.dataSource && [self.dataSource respondsToSelector:@selector(floatControllerTableView)]) {
        tableView = [self.dataSource floatControllerTableView];
    }
    
    if (@available(iOS 11.0, *)) {
        tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    tableView.delegate = self;
    tableView.dataSource = self;
    
    if (@available(iOS 11.0, *)) {
        tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    tableView.estimatedRowHeight = 0;
    tableView.estimatedSectionHeaderHeight = 0;
    tableView.estimatedSectionFooterHeight = 0;
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // Group样式的TableView Header和Footer会有空白
    tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    
}

#pragma mark - Cell 注册
// MARK:注册Cell
- (void)registerTableViewCellWith:(JDISVSettlementAmountFloorBaseCellViewModel *)cellViewModel {
    if (cellViewModel.registerType == JDISVSettlementAmountFloorBaseCellViewModelRegisterTypeXib) {
        [self registerTableViewCellClassWithNibName:cellViewModel.cellName bundle:[self.dataSource floatControllerModuleBundle] forCellReuseIdentifier:cellViewModel.cellIdentifier];
    } else if (cellViewModel.registerType == JDISVSettlementAmountFloorBaseCellViewModelRegisterTypeClass) {
        [self registerTableViewCellWithClassName:NSClassFromString(cellViewModel.cellName) forCellReuseIdentifier:cellViewModel.cellIdentifier];
    } else {
        return;
    }
}

- (void)registerTableViewCellWithClassName:(nonnull Class)aClass forCellReuseIdentifier:(nonnull NSString *)identifier {
    UITableView *tableView = [[UITableView alloc] init];
    if (self.dataSource && [self.dataSource respondsToSelector:@selector(floatControllerTableView)]) {
        tableView = [self.dataSource floatControllerTableView];
    }
    [tableView registerClass:aClass forCellReuseIdentifier:identifier];
}

- (void)registerTableViewCellClassWithNibName:(nonnull NSString *)name bundle:(nonnull NSBundle *)bundle forCellReuseIdentifier:(NSString *)identifier {
    UITableView *tableView = [[UITableView alloc] init];
    if (self.dataSource && [self.dataSource respondsToSelector:@selector(floatControllerTableView)]) {
        tableView = [self.dataSource floatControllerTableView];
    }
    [tableView registerNib:[UINib nibWithNibName:name bundle:bundle] forCellReuseIdentifier: identifier];
}

#pragma mark - Table DataSource && Delegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.viewModel == nil || self.viewModel.sectionViewModels == nil) {
        return  0;
    }
    
    if (self.viewModel.sectionViewModels > 0) {
        return  self.viewModel.sectionViewModels.count;
    } else {
        return  0;
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:section];
        if (cellViewModels.count > 0) {
            return cellViewModels.count;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.viewModel!=nil && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementAmountFloorBaseCellViewModel *cellViewModel = [cellViewModels objectAtIndex:indexPath.row];
            return cellViewModel.height;
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(floatContentTableView:heightForHeaderInSection:)]) {
        return [self.delegate floatContentTableView:tableView heightForHeaderInSection:section];
    }
    return 0.01f;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(floatContentTableView:heightForFooterInSection:)]) {
        return [self.delegate floatContentTableView:tableView heightForFooterInSection:section];
    }
    return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(floatContentTableView:viewForHeaderInSection:)]) {
        return [self.delegate floatContentTableView:tableView viewForHeaderInSection:section];
    }
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.01f)];
    header.backgroundColor = [UIColor clearColor];
    return header;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (self.delegate && [self.delegate respondsToSelector:@selector(floatContentTableView:viewForFooterInSection:)]) {
        return [self.delegate floatContentTableView:tableView viewForFooterInSection:section];
    }
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.01f)];
    footer.backgroundColor = [UIColor clearColor];
    return footer;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementAmountFloorBaseCell *cell = nil;
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            [self registerTableViewCellWith:itemViewModel];
            cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.cellIdentifier forIndexPath:indexPath];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
                __weak typeof(self) weakSelf = self;
                [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(NSDictionary *parames) {
                    __strong typeof(weakSelf) strongSelf = weakSelf;
                    [strongSelf.delegate processCellActionWithParames:parames];
                }];
            }
        }
        
    }
    
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    if (self.viewModel && self.viewModel.sectionViewModels && indexPath.section < self.viewModel.sectionViewModels.count) {
        NSArray *cellViewModels = [self.viewModel.sectionViewModels objectAtIndex:indexPath.section];
        if (indexPath.row < cellViewModels.count) {
            JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel = [cellViewModels objectAtIndex:indexPath.row];
            if ([cell respondsToSelector:@selector(updateCellWithViewModel:)]) {
                [cell performSelector:@selector(updateCellWithViewModel:) withObject:itemViewModel];
            }
        }
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(clickCellIndexOf:)]) {
        [self.delegate clickCellIndexOf:indexPath];
    }
    
    
}

#pragma mark - Lazy Load
- (JDISVSettlementAmountFloorBaseViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [self.dataSource floatControllerViewModel];
    }
    
    return _viewModel;
}

@end
