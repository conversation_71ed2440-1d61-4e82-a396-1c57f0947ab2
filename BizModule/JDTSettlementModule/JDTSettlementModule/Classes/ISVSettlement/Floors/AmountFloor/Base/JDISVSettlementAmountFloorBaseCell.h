//
//  JDISVSettlementAmountFloorBaseCell.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <UIKit/UIKit.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVImageModule/JDISVImageModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import <JDISVCategoryModule/UIView+JDCDCorners.h>
#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

NS_ASSUME_NONNULL_BEGIN
@class JDISVSettlementAmountFloorBaseCellViewModel;
@interface JDISVSettlementAmountFloorBaseCell : UITableViewCell
@property (nonatomic, strong) RACSubject * _Nullable delegate;

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel * _Nullable)viewModel;
@end

NS_ASSUME_NONNULL_END
