//
//  JDISVSettlementAmountFloorBaseController.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <UIKit/UIKit.h>

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVImageModule/JDISVImageModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import <JDISVCategoryModule/UIView+JDCDCorners.h>
#import <JDISVCategoryModule/UIView+JDCDGesture.h>
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import "NSBundle+JDISVSettlement.h"
#import "UIImage+JDISVSettlement.h"

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementAmountFloorCellActionParam;
@class JDISVSettlementAmountFloorBaseViewModel;
@class KANavigationBarButtonStandardItem;
@protocol JDISVSettlementAmountFloorBaseControllerDataSource <NSObject>

@required
- (JDISVSettlementAmountFloorBaseViewModel *)controllerViewModel;
- (NSBundle *)moduleBundle;
- (UITableView *)mainTableView;
@end

@protocol JDISVSettlementAmountFloorBaseControllerDelegate <NSObject>

@optional
- (CGFloat)mainTableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section;
- (CGFloat)mainTableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section;
- (UIView *)mainTableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section;
- (UIView *)mainTableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section;

- (void)processCellActionWithParames:(JDISVSettlementAmountFloorCellActionParam *)parames;
- (void)clickCellIndexOf:(NSIndexPath *)indexPath;
- (void)mainTableViewDidScroll:(UITableView *)tableView;
- (void)mainTableViewWillBeginDragging:(UITableView *)tableView;
@end

@protocol JDISVSettlementAmountFloorBaseControllerNavigationDelegate <NSObject>

@required
- (NSString *)titleOfNavigationView;
@optional
- (void)navigationBackButtonAction:(KANavigationBarButtonStandardItem * _Nonnull)sender;
@end

@interface JDISVSettlementAmountFloorBaseController : UIViewController
/// 返回TableView的Frame
/// @return CGRectNull 则不进行设置
- (CGRect)frameOfTableView;
/// 自定义设置TableView
- (void)customSetupTableView:(UITableView *)tableView;

- (void)reloadTableView;
@end

NS_ASSUME_NONNULL_END
