//
//  JDISVDiscountCodeController.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/3/17.
//

#import <UIKit/UIKit.h>
#import "JDISVDiscountCodeViewModel.h"
NS_ASSUME_NONNULL_BEGIN

@class RACSubject;

typedef void(^JDISVSettlementDiscountCodeControllerCloseCallback)(NSDictionary *param);

@interface JDISVDiscountCodeController : UIViewController
@property (nonatomic, copy) JDISVSettlementDiscountCodeControllerCloseCallback closeCallback;  /**< 点击关闭 浮层消失回调 */
@property (nonatomic, copy) JDISVSettlementDiscountCodeControllerCloseCallback confirmCallback; /**< 点击确定按钮，回调>*/
@property (nonatomic, strong) JDISVDiscountCodeViewModel *viewModel;

- (instancetype)initWithDiscountCodeViewModel:(JDISVDiscountCodeViewModel *)viewmodel;

///返回是否需需要挽留弹框
- (BOOL)whetherItIsNecessaryToDisplayTheRetainingBulletBox;


@end

NS_ASSUME_NONNULL_END
