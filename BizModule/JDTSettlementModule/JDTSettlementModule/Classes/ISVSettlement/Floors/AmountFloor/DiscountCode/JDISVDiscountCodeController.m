//
//  JDISVDiscountCodeController.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by ext.chenhongyu12 on 2023/3/17.
//

#import "JDISVDiscountCodeController.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>//主题配置类
#import "JDISVSettlementPub.h"//共用类
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>//分类扩展类
#import "UIImage+JDISVSettlement.h"//图片扩展
#import "NSBundle+JDISVSettlement.h"//bound扩展
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>//字体图标
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>//RAC
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>//masonry 布局
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>//基础类
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>//控件类
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>//键盘处理类
#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>//控件类
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>//RAC
#import <JDISVCategoryModule/UIColor+JDCDExtend.h>
#import <JDISVYYModelModule/YYModel.h>
#import "JDISVSettlementCouponItemModel.h"
#import "JDISVSettlementAmountFloorMainModel.h"
#import "JDISVDiscountCodeViewModel.h"


#define ALPHANUM @"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"

@interface JDISVDiscountCodeController ()<UITextFieldDelegate,UITableViewDelegate,UITableViewDataSource>

@property (strong, nonatomic) UIView *couponTopTitleView;
@property (strong, nonatomic) UILabel *controllerTitleLabel;
@property (strong, nonatomic) UIButton *titleTipBtn;
@property (strong, nonatomic) UIButton *closeButton;
@property (strong, nonatomic) UIView *tipBg;
@property (strong, nonatomic) UILabel *tipLabel;
@property (strong, nonatomic) UIView *inputBgView;
@property (strong, nonatomic) UITextField *codeInput;
@property (strong, nonatomic) UILabel *errorLabel;
@property (strong, nonatomic) UIButton *sureBtn;
@property (strong, nonatomic) UIButton *queryBtn;

@property (strong ,nonatomic) NSString *haveInfoCoponCode;

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UILabel *decutionTipLabel;
@property (nonatomic, strong) UILabel *decutionValueLabel;
@property (nonatomic, strong) UIButton * btnReferrals;
@property (nonatomic, assign)  NSInteger  baseQueryId;
@property (strong,nonatomic) NSArray<JDISVSettlementCouponItemModel*>* recommandCodes;


@end

@implementation JDISVDiscountCodeController

- (instancetype)initWithDiscountCodeViewModel:(JDISVDiscountCodeViewModel *)viewmodel{
    self = [super init];
    if (self){
        self.viewModel = viewmodel;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.baseQueryId = 20000;
    // Do any additional setup after loading the view.
    [self setupUI];
    [self viewControllerLogic];
    if(self.viewModel.isFirst){
        [self requestCodes];
        self.viewModel.discount = @(0);
        self.viewModel.isFirst = NO;
    }
    self.decutionValueLabel.text = [NSString stringWithFormat:@"%@ %0.2f",[NSString getJDCDPriceTag] ,self.viewModel.discount.doubleValue];
}

#pragma mark - <页面搭建>

- (void)setupUI{
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self.view addSubview:self.controllerTitleLabel];
    [self.view addSubview:self.titleTipBtn];
    [self.view addSubview:self.closeButton];
    [self.view addSubview:self.tipBg];
    [self.view addSubview:self.inputBgView];
    [self.view addSubview:self.codeInput];
    [self.view addSubview:self.errorLabel];
    [self.view addSubview:self.sureBtn];
    [self.view addSubview: self.queryBtn];
    [self.view addSubview:self.decutionTipLabel];
    [self.view addSubview:self.decutionValueLabel];
    [self.view addSubview:self.btnReferrals];
    [self.view addSubview:self.tableView];
    
    CGFloat marginTop = 18.0;
    CGFloat marginLeft = 18.0;
    CGFloat marginRight = 18.0;
    
    [self.controllerTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view).mas_offset(marginTop);
        make.centerX.mas_equalTo(self.view);
    }];
    
    [self.titleTipBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.controllerTitleLabel);
        make.leading.mas_equalTo(self.controllerTitleLabel.mas_trailing).mas_offset(3);
        make.width.height.mas_offset(20);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.controllerTitleLabel);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
        make.width.height.mas_offset(20);
    }];
    
    [self.tipBg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.controllerTitleLabel.mas_bottom).offset(marginTop);
        make.leading.mas_equalTo(self.view).mas_offset(marginLeft);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
        make.height.mas_greaterThanOrEqualTo(29);
    }];
    
    [self.codeInput mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.tipBg.mas_bottom).mas_offset(marginTop);
        make.leading.mas_equalTo(self.view).mas_offset(marginLeft);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight - 70 - 6);
        make.height.mas_offset(43.5);
    }];
    
    [self.queryBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.codeInput);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
        make.width.mas_offset(70);
        make.height.mas_offset(43.5);
    }];
    
    [self.inputBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.tipBg.mas_bottom).mas_offset(marginTop);
        make.leading.mas_equalTo(self.view).mas_offset(marginLeft);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
        make.height.mas_offset(43.5);
    }];

    [self.errorLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.codeInput.mas_bottom).mas_offset(6);
        make.leading.mas_equalTo(self.view).mas_offset(marginLeft);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
    }];
    
    
    CGFloat bottom = [PlatformService isIphoneX] ? -39.0 : -5.0;
    
    [self.sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view).mas_offset(bottom);
        make.leading.mas_equalTo(self.view).mas_offset(marginLeft);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
        make.height.mas_offset(40);
    }];
    
    [self.decutionTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.errorLabel.mas_bottom).mas_offset(8);
        make.leading.mas_equalTo(self.tipBg.mas_leading);
        make.height.mas_offset(17);
    }];
    
    [self.decutionValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.errorLabel.mas_bottom).mas_offset(8);
        make.leading.mas_equalTo(self.decutionTipLabel.mas_trailing).mas_offset(1);
        make.height.mas_offset(17);
    }];
    
    [self.btnReferrals mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.decutionTipLabel.mas_centerY);
        make.trailing.mas_equalTo(self.view).mas_offset(-marginRight);
        make.height.mas_equalTo(17);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.decutionTipLabel.mas_bottom).mas_offset(8);
        make.leading.mas_equalTo(self.view.mas_leading).mas_offset(marginRight);
        make.trailing.mas_equalTo(self.view.mas_trailing).mas_offset(-marginRight);
        make.bottom.mas_equalTo(self.sureBtn.mas_top).mas_offset(-8);
    }];
    self.tableView.separatorStyle  = UITableViewCellSeparatorStyleNone;
    
    //默认状态
    [self uploadViewWith:NO andErrorMsg:@""];

}

#pragma mark - <getter>

- (UILabel *)controllerTitleLabel {
    if(!_controllerTitleLabel) {
        _controllerTitleLabel = [[UILabel alloc] init];
        _controllerTitleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T5");
        _controllerTitleLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _controllerTitleLabel.text = SettlementLan(@"settlement_discount_code_title");
        _controllerTitleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _controllerTitleLabel;
}
-(UIButton *)titleTipBtn{
    if(!_titleTipBtn){
        _titleTipBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        UIImage* img = [UIImage ka_iconWithName:JDIF_ICON_TIPS imageSize:CGSizeMake(16, 16) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        [_titleTipBtn setTitle:@"" forState:UIControlStateNormal];
        [_titleTipBtn setImage:img forState:UIControlStateNormal];
        [_titleTipBtn addTarget:self action:@selector(tapTitleTip) forControlEvents:UIControlEventTouchUpInside];
        
    }
    return _titleTipBtn;
}

- (UIButton *)closeButton{
    if (!_closeButton) {
        _closeButton = [[UIButton alloc] init];
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
        UIImage *closeIcon = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
        [_closeButton setImage:closeIcon forState:UIControlStateNormal];
    }
    return _closeButton;
}

- (UIView *)tipBg {
    if (!_tipBg){
        _tipBg = [[UIView alloc] init];
        _tipBg.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07];
        _tipBg.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"];
        
        [_tipBg addSubview:self.tipLabel];
        [self.tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.tipBg).mas_offset(4);
            make.bottom.equalTo(self.tipBg).mas_offset(-4);
            make.leading.mas_equalTo(self.tipBg).mas_offset(12);
            make.trailing.mas_equalTo(self.tipBg).mas_offset(-12);
        }];
    }
    return _tipBg;
}

- (UILabel *)tipLabel{
    if(!_tipLabel){
        _tipLabel = [[UILabel alloc] init];
        _tipLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
        _tipLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C12");
        _tipLabel.text = SettlementLan(@"settlement_discount_code_tip");
        _tipLabel.numberOfLines = 0;
    }
    return _tipLabel;
}

- (UITextField *)codeInput{
    if(!_codeInput){
        _codeInput = [[UITextField alloc] init];
        _codeInput.backgroundColor = UIColor.clearColor;
        _codeInput.layer.cornerRadius = 43.5/2.0;
        _codeInput.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _codeInput.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];

        _codeInput.delegate = self;
        _codeInput.text = [self.viewModel defalutText];
        _codeInput.clearButtonMode = UITextFieldViewModeWhileEditing;
        _codeInput.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 12, 1)];
        _codeInput.leftViewMode = UITextFieldViewModeAlways;
        _codeInput.textAlignment = NSTextAlignmentLeft;

        [_codeInput clearButtonRectForBounds:CGRectMake(0, 0, 30, 30)];
        NSMutableAttributedString *AttributedStr = [[NSMutableAttributedString alloc]initWithString:SettlementLan(@"settlement_discount_code_placeholder")];
        [AttributedStr addAttribute:NSFontAttributeName
                            value:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
                            range:NSMakeRange(0, AttributedStr.string.length)];
        [AttributedStr addAttribute:NSForegroundColorAttributeName
                            value:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]
                            range:NSMakeRange(0, AttributedStr.string.length)];
        [AttributedStr addAttribute:NSParagraphStyleAttributeName
                              value:[[NSMutableParagraphStyle alloc] init]
                              range:NSMakeRange(0, AttributedStr.string.length)];
        _codeInput.attributedPlaceholder = AttributedStr;
    }
    return  _codeInput;
}

- (UILabel *)errorLabel{
    if(!_errorLabel){
        _errorLabel = [[UILabel alloc] init];
        _errorLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _errorLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C13");
        _errorLabel.text = @"";
        _errorLabel.numberOfLines = 0;
    }
    return _errorLabel;
}

- (UIButton *)sureBtn{
    if(!_sureBtn){
        _sureBtn = [[UIButton alloc] init];
        //处理点击闪烁问题
        [_sureBtn setTitle:SettlementLan(@"checkout_confirm") forState:UIControlStateNormal];
        [_sureBtn setTitle:SettlementLan(@"checkout_confirm") forState:UIControlStateHighlighted];
        [_sureBtn setTitle:SettlementLan(@"checkout_confirm") forState:UIControlStateDisabled];
        [_sureBtn renderB1];
        //处理点击闪烁问题
        [_sureBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateNormal];
        [_sureBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateHighlighted];
        [_sureBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"] forState:UIControlStateDisabled];
    }
    return  _sureBtn;
}

- (UIButton *)queryBtn{
    if (!_queryBtn){
        _queryBtn = [[UIButton alloc] init];
        _queryBtn.layer.cornerRadius = 43.5/2.f;
        _queryBtn.layer.borderWidth = 0.5;
        _queryBtn.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"].CGColor;
        _queryBtn.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _queryBtn.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
        [_queryBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] forState:UIControlStateNormal];
        [_queryBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] forState:UIControlStateHighlighted];
        [_queryBtn setTitleColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"] forState:UIControlStateDisabled];
        [_queryBtn setTitle:SettlementLan(@"checkout_query_title") forState:UIControlStateNormal];
        [_queryBtn addTarget:self action:@selector(quertBtnPressed) forControlEvents:UIControlEventTouchUpInside];
    }
    return _queryBtn;
}

- (UIView *)inputBgView{
    if (!_inputBgView){
        _inputBgView = [[UIView alloc] init];
        _inputBgView.layer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"].CGColor;
        _inputBgView.layer.borderWidth = 0;
        _inputBgView.layer.cornerRadius = 43.5/2.f;
        _inputBgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _inputBgView;
}
- (UIView*)tableView{
    if(!_tableView){
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
    }
    return _tableView;
}

-(UILabel*)decutionTipLabel{
    if(!_decutionTipLabel){
        _decutionTipLabel = [[UILabel alloc] init];
        _decutionTipLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _decutionTipLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
        _decutionTipLabel.text = SettlementLan(@"checkout_Total_deduction");
        _decutionTipLabel.numberOfLines = 1;
    }
    return _decutionTipLabel;
}

-(UILabel*)decutionValueLabel{
    if(!_decutionValueLabel){
        _decutionValueLabel = [[UILabel alloc] init];
        _decutionValueLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _decutionValueLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _decutionValueLabel.numberOfLines = 1;
    }
    return _decutionValueLabel;
}

- (UIButton *)btnReferrals{
    if (!_btnReferrals){
        _btnReferrals = [[UIButton alloc] init];
        
        _btnReferrals.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
        _btnReferrals.backgroundColor = UIColor.clearColor;
        [_btnReferrals setTitleColor:[UIColor jdcd_colorWithHexColorString:@"4D84FE"] forState:UIControlStateNormal];
        [_btnReferrals setTitleColor:[UIColor jdcd_colorWithHexColorString:@"4D84FE"] forState:UIControlStateHighlighted];
        [_btnReferrals setTitleColor:[UIColor jdcd_colorWithHexColorString:@"4D84FE"] forState:UIControlStateDisabled];
        [_btnReferrals setTitle:SettlementLan(@"checkout_use_referrals") forState:UIControlStateNormal];
        [_btnReferrals addTarget:self action:@selector(tapReferrals) forControlEvents:UIControlEventTouchUpInside];
    }
    return _btnReferrals;
}


#pragma mark - <logic<Funcation>>
/// 页面事件处理
- (void)viewControllerLogic{
    JDWeakSelf;
    
    [[self.closeButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf;
        [self closeFloatingLayerWith:self];
    }];
    [[self.sureBtn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf;
        [self closeFloatingLayerWith:self];
    }];

    [[self.codeInput rac_signalForControlEvents:UIControlEventEditingChanged] subscribeNext:^(__kindof UIControl * _Nullable x) {
        JDStrongSelf;
        NSString *tem = self.codeInput.text;
        if ([tem length] > 15) {
            tem = [tem substringWithRange:NSMakeRange(0, 15)];
        }
        
        if (tem.length == 0 || !tem){
            [self uploadViewWith:NO andErrorMsg:nil];
        }
        
        if ([self isOnlyAlphaNumeric:tem]){
            self.codeInput.text = tem;
            [self uploadViewWith:NO andErrorMsg:nil];
        }else{

        }
    }];
}

- (BOOL)isOnlyAlphaNumeric:(NSString *)str {
  NSCharacterSet *numAndLetterCharSet = [[NSCharacterSet alphanumericCharacterSet] invertedSet];
  return ([str rangeOfCharacterFromSet:numAndLetterCharSet].location == NSNotFound);
}

- (void)tapTitleTip{
    
    NSString* t1 = SettlementLan(@"checkout_coupon_titletip");
    NSString* t2 = SettlementLan(@"checkout_coupon_detailtip");
    
    [KAAlert alert].config.renderW3(t1)
        .jdcd_addContent(^(UILabel * _Nonnull label) {
            label.text = t2;
            label.textAlignment = NSTextAlignmentLeft;
            label.lineBreakMode = NSLineBreakByWordWrapping;
            label.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"];
            label.font = [UIFont systemFontOfSize:14];
        })
        .addFillAction(SettlementLan(@"checkout_confirm"), ^{
        }).jdcd_clickBackgroundClose(NO)
        .alertShow();
}

-(void)requestCodes{
    
    JDWeakSelf
    [[self.viewModel requestCodes] subscribeNext:^(NSDictionary*  _Nullable x) {
        JDStrongSelf
        self.recommandCodes = [self createCoupoModelFromList:x];
        
        self.viewModel.userDatas = self.recommandCodes;
        if(self.viewModel.settlePageCodes.count){
            for(JDISVSettlementCouponItemModel* itemModel in self.viewModel.userDatas){
                for(JDISVSettlementAmoutFloorDiscountCodeModel* settleCode in self.viewModel.settlePageCodes){
                    if(settleCode.selected && [settleCode.couponCode isEqualToString:itemModel.couponCode]){
                        itemModel.selected = YES;
                    }
                }
            }
        }
        
        if ([[self.viewModel defalutText] jdcd_validateString]){
            [self quertBtnPressed];
        }
        [self.tableView reloadData];
    } error:^(NSError * _Nullable error) {
        JDStrongSelf
        if ([[self.viewModel defalutText] jdcd_validateString]){
            [self quertBtnPressed];
        }
    } completed:^{
    }];
}


-(NSArray*)createCoupoModelFromList:(NSDictionary*)data{
    NSMutableArray* tmp = [[NSMutableArray alloc] init];
    JDISVSettlementAmountFloorMainModel *model = [JDISVSettlementAmountFloorMainModel yy_modelWithDictionary:data];
    for (JDISVSettlementAmountFloorModel *floorData in model.floors) {
        if ([floorData.type.nameSpace containsString:@"#couponSingleFloor"]) {
            JDISVSettlementCouponItemModel* couponItem = [JDISVSettlementCouponItemModel yy_modelWithDictionary:floorData.info];
            couponItem.orderIndex = 10;
            couponItem.isQuery = NO;
            couponItem.selected = NO;
            couponItem.canUseCode = YES;
            if(couponItem.couponType.integerValue == 14){
                JDISVSettlementAmountFloorPartListItemModel* partDic = floorData.partList.firstObject;
                NSDictionary* info = partDic.info;
                NSDictionary* basic = [info jdcd_getDicElementForKey:@"C-P#discountInfo&basic"];
                NSString* strOff = basic[@"discount"];
                NSNumber* discount = @([strOff floatValue]);
                NSInteger discountInt = discount.floatValue*100;
                couponItem.couponCodeDiscount = 100 - discountInt;
            }
            [tmp addObject:couponItem];
        }
    }
    return [tmp copy];
}

-(void)selectQueryCode:(JDISVSettlementCouponItemModel*)item{
    if(item.selected){
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage
                                              message:SettlementLan(@"checkout_coupon_auto_select")];
    }else{
        @weakify(self)
        [PlatformService showLoadingInView:self.view];
        [[self useCoupon:item.couponCode] subscribeNext:^(id  _Nullable x) {
            @strongify(self)
            item.selected = YES;
            [self.tableView reloadData];
            NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
            NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
            [self getTotalDiscount:floors];
            [PlatformService dismissInView:self.view];
        } error:^(NSError * _Nullable error) {
            @strongify(self)
            [self userActiontips:error.userInfo];
            [PlatformService dismissInView:self.view];
        }];
    }
}

- (void)quertBtnPressed{
    if (self.codeInput.text.jdcd_trimWhitespace.length == 0){
        [self uploadViewWith:YES andErrorMsg:SettlementLan(@"setllement_discount_code_error_tip")];
        return;
    }
    if (![self isOnlyAlphaNumeric:self.codeInput.text]){
        [self uploadViewWith:YES andErrorMsg:SettlementLan(@"setllement_discount_code_error_tip")];
        return;
    }
    NSInteger count = 0;
    JDISVSettlementCouponItemModel* existitem;
    NSInteger selectCount = 0;
    for(JDISVSettlementCouponItemModel* item  in self.viewModel.userDatas){
        if(item.isQuery){
            count++;
        };
        if([item.couponCode isEqualToString:self.codeInput.text]){
            existitem = item;
        }
        if(item.selected){
            selectCount++;
        }
    }
    if(existitem){
        [self selectQueryCode:existitem];
        return;
    }
    if(count >= 20){
        [self uploadViewWith:YES andErrorMsg:SettlementLan(@"checkout_coupon_maxinum")];
        return;
    }
    [self.codeInput resignFirstResponder];
    JDWeakSelf
    [PlatformService showLoadingInView:self.view];
    [[[self useCoupon:self.codeInput.text] flattenMap:^__kindof RACSignal * _Nullable(NSDictionary*  _Nullable useInfo) {
        [self uploadViewWith:NO andErrorMsg:@""];
        NSDictionary* value = [useInfo jdcd_getDicElementForKey:@"value"];
        NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
        [self getTotalDiscount:floors];
        return [self.viewModel fastbePromocodeDetail:self.codeInput.text];
    }] subscribeNext:^(NSDictionary*  _Nullable useInfo) {
        
        [self showQueryCode:useInfo select:selectCount<20];
        [PlatformService dismissInView:self.view];
    } error:^(NSError * _Nullable error) {
        JDStrongSelf
        [self userActiontips:error.userInfo];
        [PlatformService dismissInView:self.view];
    }];
}

-(void)showQueryCode:(NSDictionary*)codeInfo
              select:(BOOL)select{
    JDISVSettlementCouponItemModel* couponItem = [[JDISVSettlementCouponItemModel alloc] init];
    
    couponItem.timeBegin =  [codeInfo jdcd_getStringElementForKey:@"beginTimeFormat"];
    couponItem.timeEnd = [codeInfo jdcd_getStringElementForKey:@"endTimeFormat"];
    couponItem.discount = [codeInfo jdcd_getStringElementForKey:@"discount"];
    couponItem.limitDesc = [codeInfo jdcd_getStringElementForKey:@"limitStr"];
    couponItem.couponCode = [codeInfo jdcd_getStringElementForKey:@"promoCode"];
    couponItem.quota = [codeInfo jdcd_getStringElementForKey:@"quota"];
    couponItem.selected = select;
    couponItem.isQuery = YES;
    couponItem.canUseCode = YES;
    couponItem.orderIndex = self.baseQueryId++;
    couponItem.activityId = [codeInfo jdcd_getStringElementForKey:@"activityId"];
    couponItem.couponType = [codeInfo jdcd_getNumberElementForKey:@"type"];
    couponItem.codeBatchId = [codeInfo jdcd_getStringElementForKey:@"promoCodeId"];
    couponItem.maxDiscount = [codeInfo jdcd_getStringElementForKey:@"maxDiscount"];
    if(couponItem.couponType.intValue == 14){
        couponItem.couponCodeDiscount = [couponItem.discount intValue];
    }
    if(self.viewModel.userDatas.count){
        NSMutableArray* tmpArr = [self.viewModel.userDatas mutableCopy];
        [tmpArr insertObject:couponItem atIndex:0];
        self.viewModel.userDatas  = [tmpArr copy];
    }else{
        self.viewModel.userDatas = @[couponItem];
    }
    [self uploadViewWith:NO andErrorMsg:@""];
    
    [self.tableView reloadData];
}


- (void)vaildateDiscountCodeToService{
    [[JDISVPlatformService sharedService] showLoadingInView:self.view];
    JDWeakSelf;
    [[self.viewModel vaildateDiscountCodeToService:self.codeInput.text] subscribeNext:^(id  _Nullable x) {
        JDStrongSelf;
        [[JDISVPlatformService sharedService] dismissInView:self.view];
        if (self.confirmCallback) {
            self.confirmCallback(x);
        }
        [self userActiontips:x];
        [self dismissViewControllerAnimated:YES completion:nil];
    } error:^(NSError * _Nullable error) {
        JDStrongSelf;
        [[JDISVPlatformService sharedService] dismissInView:self.view];
        [self userActiontips:error.userInfo];
    }];
}

- (void)userActiontips:(NSDictionary *)info{
    NSInteger toastType =  [info jdcd_getIntegerElementForKey:@"ToastType"];
    if (toastType == ISVInstantTypeOnlyMessage) {
        [self uploadViewWith:YES andErrorMsg:[info jdcd_getStringElementForKey:@"msg"]];
    }else{
        [PlatformService showDefaultToastWithIconType:toastType message:[info jdcd_getStringElementForKey:@"msg"]];
    }
}

-(void)tapReferrals{
    [PlatformService showLoadingInView:self.view];
    @weakify(self)
    [[[self unSelectAll] flattenMap:^__kindof RACSignal * _Nullable(id  _Nullable _) {
        @strongify(self)
        
        return [self selectRecommendCode];
    }] subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        if(x){
            NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
            NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
            [self getTotalDiscount:floors];
            [self.tableView reloadData];
        }
        [self uploadViewWith:NO andErrorMsg:@""];
        [PlatformService dismissInView:self.view];
    } error:^(NSError * _Nullable error) {
        @strongify(self)
        [self userActiontips:error.userInfo];
        [self.tableView reloadData];
        [PlatformService dismissInView:self.view];
    }];
}

/// - Parameter controller: contoller
- (void)closeFloatingLayerWith:(UIViewController *)controller{
    if (self.closeCallback) {
        self.closeCallback(@{@"presentedViewController":self});
    }
}

- (BOOL)whetherItIsNecessaryToDisplayTheRetainingBulletBox{
    return [self.viewModel whetherItIsNecessaryToDisplayTheRetainingBulletBox:self.codeInput.text];
}

- (void)uploadViewWith:(BOOL)state andErrorMsg:(NSString *)errorStr{
    if (state) {//
        self.inputBgView.layer.borderWidth = KSAAPP ? 0 : 0.5;
        self.errorLabel.hidden = NO;
        self.errorLabel.text = errorStr;
    }
    else{//
        self.inputBgView.layer.borderWidth = 0;
        self.errorLabel.hidden = YES;
        self.errorLabel.text = @"";
    }
}

#pragma mark - <UITextFieldDelegate>

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{
    if (string == nil || [string isEqualToString:@""]) {//
        return YES;
    }
    
    if (textField.text.length < 15) {

        return YES;
    }else{
        return NO;
    }
}

- (void)textFieldDidBeginEditing:(UITextField *)textField{
    self.inputBgView.layer.borderWidth = 0;
}

- (void)textFieldDidEndEditing:(UITextField *)textField{
    self.inputBgView.layer.borderWidth = 0;
}

- (BOOL)textFieldShouldClear:(UITextField *)textField{
    return YES;
}


#pragma mark - <UITableDelegate>
- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    UITableViewCell* cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"defaultCell"];
    JDISVSettlementCouponItemModel* model = self.viewModel.userDatas[indexPath.row];
    CGFloat height =  [self getHeight:model];
    UIView* subView = [self getCellView:model];
    subView.frame = CGRectMake(0, 0, self.tableView.frame.size.width, height);
    [cell.contentView addSubview:subView];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section { 
    NSInteger count = self.viewModel.userDatas.count;
    return count;
}

-(CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVSettlementCouponItemModel* model = self.viewModel.userDatas[indexPath.row];
    return [self getHeight:model]+10;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    JDISVSettlementCouponItemModel* model = self.viewModel.userDatas[indexPath.row];
    if(!model.canUseCode){
        return;
    }
    JDWeakSelf;
    [PlatformService showLoadingInView:self.view];
    if(model.selected){
        [[self unSelectCoupon:model del:NO]subscribeNext:^(id  _Nullable x) {
            JDStrongSelf;
            [self.tableView reloadData];
            [PlatformService dismissInView:self.view];
            NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
            NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
            [self getTotalDiscount:floors];
        } error:^(NSError * _Nullable error) {
            JDStrongSelf;
            [self userActiontips:error.userInfo];
            [PlatformService dismissInView:self.view];
        }];
    }else{
        NSInteger selectCount = 0;
        for(JDISVSettlementCouponItemModel* item  in self.viewModel.userDatas){
            if(item.selected){
                selectCount++;
            }
        }
        if(selectCount >= 20){
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage
                                                  message:SettlementLan( @"checkout_coupon_maxinum_use")];
            return;
        }
        
        [[self selectCouponWithModule:model] subscribeNext:^(id  _Nullable x) {
            JDStrongSelf;
            model.selected = YES;
            [self.tableView reloadData];
            NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
            NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
            [self getTotalDiscount:floors];
            [PlatformService dismissInView:self.view];
            [self uploadViewWith:NO andErrorMsg:@""];
        } error:^(NSError * _Nullable error) {
            JDStrongSelf;
            [self userActiontips:error.userInfo];
            [PlatformService dismissInView:self.view];
        }];
    }
}

-(void)getTotalDiscount:(NSArray*)floors{
    for(NSDictionary* dic in floors){
        NSString* uuid = [dic jdcd_getStringElementForKey:@"uuid"];
        if([uuid hasPrefix:@"homePageFloorCouponFloor_"]){
            NSDictionary* infoDic = [dic jdcd_getDicElementForKey:@"info"];
            NSDictionary* disDic = [infoDic jdcd_getDicElementForKey:@"C-M#homePageFloorCouponFloor&money"];
            self.viewModel.discount = [disDic jdcd_getNumberElementForKey:@"couponCodeDiscount"];
            self.decutionValueLabel.text = [NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag] ,self.viewModel.discount.doubleValue];
            break;
        }
    }
}


-(void)unselectConflicCodesAndSelectCode:(NSArray<JDISVSettlementCouponItemModel*>*)conflicicts
                            code:(NSString*)code{
    
    NSInteger selectCount = 0;
    for(JDISVSettlementCouponItemModel* item  in self.viewModel.userDatas){
        if(item.selected){
            selectCount++;
        }
    }
    NSMutableArray* actIds = [NSMutableArray array];
    NSMutableArray* batchIds = [NSMutableArray array];
    NSMutableArray* codes = [NSMutableArray array];

    for(JDISVSettlementCouponItemModel* model in conflicicts){
        [actIds addObject:model.activityId?:@""];
        [batchIds addObject:model.codeBatchId?:@""];
        [codes addObject:model.couponCode?:@""];
    }
    [PlatformService showLoadingInView:self.view];
    RACSignal* signal = [[self unSelect:actIds batchId:batchIds codes:codes] flattenMap:^__kindof RACSignal * _Nullable(NSDictionary*  _Nullable x) {
        NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
        NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
        [self getTotalDiscount:floors];
        return [[self useCoupon:code] flattenMap:^__kindof RACSignal * _Nullable(NSDictionary*  _Nullable floorInfo) {
            NSDictionary* valueCic = [floorInfo jdcd_getDicElementForKey:@"value"];
            NSArray* floors = [valueCic jdcd_getArrayElementForKey:@"floors"];
            [self getTotalDiscount:floors];
            return [self requestCodeInfo:code];
        }];
    }];
    JDWeakSelf;
    [signal subscribeNext:^(id  _Nullable x) {
        JDStrongSelf
        if(x){
            [self showQueryCode:x select:selectCount<20];
        }else{
            [self.tableView reloadData];
        }
        [PlatformService dismissInView:self.view];
    } error:^(NSError * _Nullable error) {
        JDStrongSelf;
        [self userActiontips:error.userInfo];
        [PlatformService dismissInView:self.view];
    }completed:^{
        [self.tableView reloadData];
        [PlatformService dismissInView:self.view];
    }];
}


-(CGFloat)getHeight:(JDISVSettlementCouponItemModel*)couponModel{
//    self.haveInfoCoponCode = [codeInfo jdcd_getStringElementForKey:@"promoCode"];
    NSInteger couponCodeType = couponModel.couponType.integerValue;
    NSNumber *couponCodeId = @([couponModel.couponCode intValue]);
    JDISVCouponTagModel *model = [JDISVCouponTagModel createCodeWithCouponType:couponCodeType couponKind:2 batchId:nil couponId:couponCodeId shopId:nil venderId:nil shopName:nil];
    model.fromSettle = YES;
    NSString *title = @"";
    NSString *price = couponModel.discount;
    NSString *quto = couponModel.quota;
    NSString *maxDiscount = couponModel.discount;
    if (couponCodeType == 12){//直降
        title = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),quto];
    }else if (couponCodeType == 13){//满减
        title = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),quto];
    }else if (couponCodeType == 14){//满折
        title = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),quto];
        price = price;
    }
    NSString *detail = @"";
    detail = couponModel.limitDesc ? couponModel.limitDesc : couponModel.couponLimitInfo;

    CGFloat height = [model getCouponCodeCellHeightWithTitle:title detail:detail maxDiscount:maxDiscount];
    return height;
}

-(UIView*)getCellView:(JDISVSettlementCouponItemModel*)couponModel{
    NSInteger couponCodeType = couponModel.couponType.integerValue;
    NSNumber *couponCodeId = @([couponModel.couponCode intValue]);
    
    JDISVCouponTagModel *model = [JDISVCouponTagModel createCodeWithCouponType:couponCodeType couponKind:2 batchId:nil couponId:couponCodeId shopId:nil venderId:nil shopName:nil];
    model.fromSettle = YES;
    NSString *title = @"";
    NSString *price = couponModel.discount;
    NSString *quto = couponModel.quota;
    NSString *maxDiscount;
    if(couponModel.isQuery){
        maxDiscount = couponModel.maxDiscount;
    } else {
        maxDiscount = couponModel.highDiscount;
    }
    if (couponCodeType == 12){//直降
        title = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),quto];
    }else if (couponCodeType == 13){//满减
        title = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),quto];
    }else if (couponCodeType == 14){//满折
        title = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),quto];
        price =  @(couponModel.couponCodeDiscount).stringValue ;
    }
    
    BOOL isShop = YES;
    int limitType = couponModel.couponLimitType.intValue;
    if(limitType == 0 || limitType == 1){
        isShop = NO;
    }
    NSString *detail = couponModel.limitDesc;
    
    NSString *beginTimeFMT = couponModel.timeBegin;
    NSString *endTimeFMT = couponModel.timeEnd;
    
    NSString *timeInfo = [NSString stringWithFormat:@"%@-%@",beginTimeFMT,endTimeFMT];
    NSString *code = couponModel.couponCode;
    NSString *couponLimitInfo = couponModel.couponLimitInfo;
    JDWeakSelf;
    return [model createSettleCodeViewWithTitle:title
                                         detail:detail
                                       timeInfo:timeInfo
                                          Price:price
                                couponLimitInfo:couponLimitInfo
                                    maxDiscount:maxDiscount
                                           code:code
                                         isShop:isShop
                                       isSelect:couponModel.selected
                                       isEnable:couponModel.canUseCode
                                     isConflict:couponModel.isConflict
                                      canDelete:couponModel.isQuery
                                       delTap:^{
        
        JDStrongSelf;
        [self deleteCoupon:couponModel];
    }];
}

-(void)deleteCoupon:(JDISVSettlementCouponItemModel*)item{
    if(item.selected){
        JDWeakSelf
        [PlatformService showLoadingInView:self.view];
        [[self unSelectCoupon:item del:YES] subscribeNext:^(id  _Nullable x) {
            JDStrongSelf;
            NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
            NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
            [self getTotalDiscount:floors];
            [self.tableView reloadData];
            [PlatformService dismissInView:self.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage
                                                  message:SettlementLan(@"checkout_coupon_del_ok")];
            
        } error:^(NSError * _Nullable error) {
            JDStrongSelf;
            [PlatformService dismissInView:self.view];
        }];
    }else{
        NSMutableArray* tmpArr = [self.viewModel.userDatas mutableCopy];
        [tmpArr removeObject:item];
        self.viewModel.userDatas = [tmpArr copy];
        [self.tableView reloadData];
    }
}
#pragma mark - <RACSignal action>
-(RACSignal* )unSelectAll{
    NSMutableArray* actArr = [NSMutableArray array];
    NSMutableArray* batchArr = [NSMutableArray array];
    NSMutableArray* codes = [NSMutableArray array];
    for(JDISVSettlementCouponItemModel* item in self.viewModel.userDatas){
        if(item.selected){
            [actArr addObject:item.activityId?:@""];
            [batchArr addObject:item.codeBatchId?:@""];
            [codes addObject:item.couponCode?:@""];
        }
    }
    return [self unSelect:actArr 
                  batchId:batchArr
                    codes:codes];
}

-(RACSignal*)unSelect:(NSArray*)actId
              batchId:(NSArray*)batchId
                codes:(NSArray*)codes{
    if(actId.count != batchId.count){
        return [RACSignal return:nil];
    }
    if(actId.count == 0){
        return [RACSignal return:nil];
    }
    
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        NSMutableArray* tmpArr = [NSMutableArray array];
        for(int i=0; i< actId.count; i++){
            
            NSDictionary* selectItem = @{@"actId":[actId objectAtIndex:i],
                                         @"batchId":[batchId objectAtIndex:i]};
            [tmpArr addObject:selectItem];
        }
        
        NSDictionary *params = @{@"clientVersion":@"1.0.0",
                                 @"client":@"iOS",
                                 @"lang":@"zh_CN",
                                 @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                                 @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                                 @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                                 @"orderStr":@{@"selectedCouponCode":@[],
                                               @"cancelCouponCode":[tmpArr copy]}};
        
        [[JDISVDiscountCodeViewModel vaildateCouponCodeWith:params isDelete:NO] subscribeNext:^(NSDictionary*  _Nullable x) {
            for(JDISVSettlementCouponItemModel* item in self.viewModel.userDatas){
                if([codes containsObject:item.couponCode] ){
                    item.selected = NO;
                    
                }
            }
            [subscriber sendNext:x];
        } error:^(NSError * _Nullable error) {
            [subscriber sendError:error];
        }];
        
        return nil;
    }];
}

-(RACSignal*)selectRecommendCode{
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableArray* selectedArr = [NSMutableArray array];
        for(JDISVSettlementCouponItemModel* data in self.viewModel.userDatas){
            if(!data.isQuery){
                [selectedArr addObject:data.couponCode];
            }
        }
        if(selectedArr.count == 0){
            [subscriber sendNext:nil];
        }else{
            NSDictionary *params = @{@"clientVersion":@"1.0.0",
                                     @"client":@"iOS",
                                     @"lang":@"zh_CN",
                                     @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                                     @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                                     @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                                     @"orderStr":@{@"selectedCouponCode":[selectedArr copy],
                                                   @"cancelCouponCode":@[]}};
            [[JDISVDiscountCodeViewModel vaildateCouponCodeWith:params isDelete:NO] subscribeNext:^(id  _Nullable x) {
                for(JDISVSettlementCouponItemModel* data in self.viewModel.userDatas){
                    if(!data.isQuery){
                        data.selected = YES;
                    }
                }
                [subscriber sendNext:x];
            } error:^(NSError * _Nullable error) {
                [subscriber sendError:error];
            }];
        }
        return nil;
    }];
}


-(RACSignal*)useCoupon:(NSString*)code{
    
    RACSignal* rac =  [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableArray* selectedArr = [NSMutableArray array];
        [selectedArr insertObject:code atIndex:0];
        NSDictionary *params = @{@"clientVersion":@"1.0.0",
                                 @"client":@"iOS",
                                 @"lang":@"zh_CN",
                                 @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                                 @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                                 @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                                 @"orderStr":@{@"selectedCouponCode":selectedArr,
                                               @"cancelCouponCode":@[]}};
        [[JDISVDiscountCodeViewModel vaildateCouponCodeWith:params isDelete:NO] subscribeNext:^(id  _Nullable x) {
            [self uploadViewWith:NO andErrorMsg:@""];
            [subscriber sendNext:x];
        } error:^(NSError * _Nullable error) {
            [subscriber sendError:error];
        }];
        return nil;
    }];
    
    return [rac flattenMap:^__kindof RACSignal * _Nullable(NSDictionary*  _Nullable x) {
        NSMutableArray* conflictArr =  [NSMutableArray array];
        NSMutableArray* unUsableArr =  [NSMutableArray array];
        NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
        NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
        BOOL queryCodeUnUse = NO;
        for(NSDictionary* dic in floors){
            NSString* uuid = [dic jdcd_getStringElementForKey:@"uuid"];
            if([uuid hasPrefix:@"homePageFloorCouponFloor_"]){
                NSArray* partList = [dic jdcd_getArrayElementForKey:@"partList"];
                
                for (NSDictionary* floorDic in partList){
                    NSDictionary* typeDic = [floorDic jdcd_getDicElementForKey:@"type"];
                    NSString* name = [typeDic jdcd_getStringElementForKey:@"namespace"];
                    if([name containsString:@"balance.coupon-P#conflict"]){
                        NSDictionary* info = [floorDic jdcd_getDicElementForKey:@"info"];
                        NSDictionary* basic = [info jdcd_getDicElementForKey:@"VCYBB2C-P#conflict&basic"];
                        JDISVSettlementCouponItemModel* couponItem = [[JDISVSettlementCouponItemModel alloc] init];
                        
                        couponItem.activityId = [basic jdcd_getStringElementForKey:@"activityId"];
                        couponItem.couponCode = [basic jdcd_getStringElementForKey:@"couponCode"];
                        couponItem.codeBatchId = [basic jdcd_getStringElementForKey:@"batchId"];
                        [conflictArr addObject:couponItem];
                    }
                    
                    if([name containsString:@"balance.coupon-P#unUsable"]){
                        NSDictionary* info = [floorDic jdcd_getDicElementForKey:@"info"];
                        NSDictionary* basic = [info jdcd_getDicElementForKey:@"VCYBB2C-P#unUsable&basic"];
                        JDISVSettlementCouponItemModel* couponItem = [[JDISVSettlementCouponItemModel alloc] init];
                        
                        couponItem.activityId = [basic jdcd_getStringElementForKey:@"activityId"];
                        couponItem.couponCode = [basic jdcd_getStringElementForKey:@"couponCode"];
                        couponItem.codeBatchId = [basic jdcd_getStringElementForKey:@"batchId"];
                        if([couponItem.couponCode isEqualToString:code]){
                            queryCodeUnUse = YES;
                        }
                        [unUsableArr addObject:couponItem];
                        
                    }
                }
                break;
            }
        }
        if(conflictArr.count){
            return [self processConflict:code conflictArr:conflictArr];
        }
        
        if(queryCodeUnUse){
            JDISVSettlementCouponItemModel* exitModule;
            for(JDISVSettlementCouponItemModel* couponItem in self.viewModel.userDatas){
                if([couponItem.couponCode isEqualToString:code]){
                    exitModule = couponItem;
                    break;
                }
            }
            if(exitModule){
                exitModule.canUseCode = NO;
            }else{
              [self uploadViewWith:YES andErrorMsg:SettlementLan(@"checkout_coupon_query_unuse")];
            }
            [self.tableView reloadData];
            [PlatformService dismissInView:self.view];
            return [RACSignal empty];
        }
        
        if(unUsableArr.count){
            return [self processUnUse:unUsableArr];
        }
        return [RACSignal return:x];
    }];
}

-(RACSignal*)selectCouponWithModule:(JDISVSettlementCouponItemModel*)model{
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[self useCoupon:model.couponCode]
         subscribe:subscriber] ;
        return nil;
    }];
}

-(RACSignal*)processConflict:(NSString*)code
                 conflictArr:(NSArray*)confilictArr{
    [PlatformService dismissInView:self.view];
    [PlatformService showDefaultAlertWithTitle:SettlementLan(@"checkout_coupon_sure_select") negativeAction:^{
        
    } positiveAction:^{
        [self unselectConflicCodesAndSelectCode:confilictArr code:code];
    } cancelButtonTitle:SettlementLan(@"checkout_coupon_cancel") doneButtonTitle:SettlementLan(@"checkout_exit_dialog_Sure") touchDoneCloseAlert:YES];
    return [RACSignal empty];
}

-(RACSignal*)processUnUse:(NSArray<JDISVSettlementCouponItemModel*>*)unUseArr{
    NSMutableArray* actIds = [NSMutableArray array];
    NSMutableArray* batchIds = [NSMutableArray array];
    NSMutableArray* codes = [NSMutableArray array];
    NSMutableArray* unUseModules = [NSMutableArray array];
    for(JDISVSettlementCouponItemModel* model in unUseArr){
        for(JDISVSettlementCouponItemModel* useModel in self.viewModel.userDatas){
            if([useModel.couponCode isEqualToString:model.couponCode]){
                if(useModel.selected) {
                    [actIds addObject:model.activityId?:@""];
                    [batchIds addObject:model.codeBatchId?:@""];
                    [codes addObject:model.couponCode?:@""];
                    [unUseModules addObject:model];
                }else{
                    useModel.canUseCode = NO;
                }
            }
        }
    }
    return [[self unSelect:actIds batchId:batchIds codes:codes] doNext:^(id  _Nullable x) {
        for(JDISVSettlementCouponItemModel* model in unUseModules){
            model.selected = NO;
            model.canUseCode  = NO;
        }
        [self.tableView reloadData];
    }];
}

-(RACSignal*)unSelectCoupon:(JDISVSettlementCouponItemModel*)model
                        del:(BOOL)del{
    RACSignal* rac = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        NSString* actId = model.activityId?:@"";
        NSString* batchId = model.codeBatchId?:@"";
        NSDictionary *params = @{@"clientVersion":@"1.0.0",
                                 @"client":@"iOS",
                                 @"lang":@"zh_CN",
                                 @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                                 @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                                 @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                                 @"orderStr":@{@"selectedCouponCode":@[],
                                               @"cancelCouponCode":@[@{@"actId":actId,
                                                                       @"batchId":batchId}]}};
        JDWeakSelf;
        [[JDISVDiscountCodeViewModel vaildateCouponCodeWith:params isDelete:NO] subscribeNext:^(NSDictionary*  _Nullable x) {
            JDStrongSelf;
            model.selected = NO;
            if(del){
                NSMutableArray* tmpArr = [self.viewModel.userDatas mutableCopy];
                [tmpArr removeObject:model];
                self.viewModel.userDatas = [tmpArr copy];
            }
            [subscriber sendNext:x];
            [self uploadViewWith:NO andErrorMsg:@""];
        } error:^(NSError * _Nullable error) {
            [subscriber sendError:error];
        }];
        return nil;
    }];
    
    return [rac flattenMap:^__kindof RACSignal * _Nullable(NSDictionary*  _Nullable x) {
        NSMutableArray* unUsableArr =  [NSMutableArray array];
        NSDictionary* value = [x jdcd_getDicElementForKey:@"value"];
        NSArray* floors = [value jdcd_getArrayElementForKey:@"floors"];
        for(NSDictionary* dic in floors){
            NSString* uuid = [dic jdcd_getStringElementForKey:@"uuid"];
            if([uuid hasPrefix:@"homePageFloorCouponFloor_"]){
                NSArray* partList = [dic jdcd_getArrayElementForKey:@"partList"];
                
                for (NSDictionary* floorDic in partList){
                    NSDictionary* typeDic = [floorDic jdcd_getDicElementForKey:@"type"];
                    NSString* name = [typeDic jdcd_getStringElementForKey:@"namespace"];
                    
                    if([name containsString:@"balance.coupon-P#unUsable"]){
                        NSDictionary* info = [floorDic jdcd_getDicElementForKey:@"info"];
                        NSDictionary* basic = [info jdcd_getDicElementForKey:@"VCYBB2C-P#unUsable&basic"];
                        JDISVSettlementCouponItemModel* couponItem = [[JDISVSettlementCouponItemModel alloc] init];
                        
                        couponItem.activityId = [basic jdcd_getStringElementForKey:@"activityId"];
                        couponItem.couponCode = [basic jdcd_getStringElementForKey:@"couponCode"];
                        couponItem.codeBatchId = [basic jdcd_getStringElementForKey:@"batchId"];
                        [unUsableArr addObject:couponItem];
                    }
                }
                break;
            }
        }
    
        if(unUsableArr.count){
            return [self processUnUse:unUsableArr];
        }
        return [RACSignal return:x];
    }];
}

-(RACSignal*)requestCodeInfo:(NSString*)code{
    for(JDISVSettlementCouponItemModel* model in self.viewModel.userDatas){
        if([model.couponCode isEqualToString:code]){
            model.selected = YES;
            [PlatformService dismissInView:self.view];
            [self.tableView reloadData];
            return [RACSignal empty];
        }
    }
    return [self.viewModel fastbePromocodeDetail:self.codeInput.text];
}




#pragma mark - <dealloc>
- (void)dealloc{
    NSLog(@"JDISVDiscountCodeController dealloc");
}
@end
