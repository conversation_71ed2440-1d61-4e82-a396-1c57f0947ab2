//
//  JDISVDiscountCodeViewModel.h
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/3/20.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>//RAC
#import "JDISVSettlementAmoutFloorDiscountCodeModel.h"
#import "JDISVSettlementCouponItemModel.h"
//#import "JDISVSettlementAmoutFloorDiscountCodeModel.h"
NS_ASSUME_NONNULL_BEGIN


@interface JDISVDiscountCodeViewModel : NSObject

//@property (strong,nonatomic) RACSubject *delegate;
//@property (strong,nonatomic) JDISVSettlementAmoutFloorDiscountCodeModel *couponDiscount;

@property (strong,nonatomic) NSArray <JDISVSettlementCouponItemModel*>*  userDatas;
@property (strong,nonatomic) NSArray <JDISVSettlementAmoutFloorDiscountCodeModel*> * settlePageCodes;
@property (strong,nonatomic) NSNumber* discount;
@property (assign,nonatomic) BOOL isFirst;
/// 返回是否需要展示删除提示弹框
/// - Parameter codeInputText: 用户输入文本
- (BOOL)vaildateShowDeleteAlert:(NSString *)codeInputText;


/// 确定按钮点击请求
- (RACSignal *)vaildateDiscountCodeToService:(NSString *)codeInputText;

/// 删除使用优惠码
/// @param params 传入参数
/// @param isdelete 是否为删除优惠码
+ (RACSignal *)vaildateCouponCodeWith:(NSDictionary *)params isDelete:(BOOL)isdelete;

/// 输入框默认展示文本
- (NSString *)defalutText;


///返回是否需需要挽留弹框 也可用作前后值的比对是否有变化
- (BOOL)whetherItIsNecessaryToDisplayTheRetainingBulletBox:(NSString *)codeInputText;
/// 获取优惠码详情
- (RACSignal *)fastbePromocodeDetail:(NSString *)code;

- (RACSignal *)requestCodes;


@end

NS_ASSUME_NONNULL_END
