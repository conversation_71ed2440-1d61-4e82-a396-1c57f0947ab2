//
//  JDISVDiscountCodeViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/3/20.
//

#import "JDISVDiscountCodeViewModel.h"
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>//IQkeyborad
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>//基础类
#import "JDISVSettlementPub.h"//module共用类
#import "JDISVSettlementAmountFloorNetService.h"

@implementation JDISVDiscountCodeViewModel

/// 返回请求用户验证/删除优惠码的请求参数
/// - Parameter codeInptText: 输入文本
- (NSDictionary *)validateUserInputDiscountCode:(NSString *)codeInptText{
    
    if (!codeInptText || codeInptText.length == 0){//输入框为空
            return @{};
        
    }else{//输入框不为空
        //兑换优惠码
        //使用新输入的优惠券
        return @{@"clientVersion":@"1.0.0",
                 @"client":@"iOS",
                 @"lang":@"zh_CN",
                 @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                 @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                 @"orderStr":@{@"couponCode":codeInptText,
                               @"selected":[NSNumber numberWithBool:YES]}};
    }
}

/// 返回是否需要展示删除提示弹框
/// - Parameter codeInputText: 用户输入文本
- (BOOL)vaildateShowDeleteAlert:(NSString *)codeInputText{
    return NO;
//    if (!codeInputText || codeInputText.length == 0){//输入框为空
//        if (_couponDiscount && _couponDiscount.couponCodeFlag && _couponDiscount.couponDiscountBasic){
//            //Model 不为空，_couponDiscount.couponCodeFlag == YES使用了优惠券 ，_couponDiscount.couponDiscountBasic不为空
//            //删除之前使用的优惠券
//            return YES;
//        }else{
//            return NO;
//        }
//    }else{//输入框不为空
//        //使用新输入的优惠券
//        return NO;
//    }
}

/// 确定按钮点击请求
- (RACSignal *)vaildateDiscountCodeToService:(NSString *)codeInputText{
    NSDictionary *inputDic = [self validateUserInputDiscountCode:codeInputText];
    NSInteger isDelete = [self vaildateShowDeleteAlert:codeInputText];
    return [JDISVDiscountCodeViewModel vaildateCouponCodeWith:inputDic isDelete:isDelete];
}

+ (RACSignal *)vaildateCouponCodeWith:(NSDictionary *)params isDelete:(BOOL)isdelete{
    __block NSInteger isDelete = isdelete;
    RACSignal* rac =  [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_useOrCancelCouponCode" version:@"" parameters:params complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error){//操作失败toast
                NSError *resError = [NSError errorWithDomain:@"settlement.discountCoide.vaildate.error" code:-1 userInfo:@{@"msg":isDelete ? SettlementLan(@"settlement_discount_code_delete_failed") : SettlementLan(@"settlement_discount_code_failed"),@"ToastType":@(ISVInstantTypeFail)}];
                [subscriber sendError:resError];
            }
            else{
                if ([responseObject isKindOfClass:[NSDictionary class]]){
                    NSString *resultCode = [responseObject jdcd_getStringElementForKey:@"resultCode"];
                    if (resultCode.jdcd_validateString && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]){//操作成功toast
                        //TODO: 兑换成功，通知结算页面 用户使用了优惠码减免，刷新数据和页面
                        [subscriber sendNext:@{@"value":[responseObject jdcd_getDicElementForKey:@"resultInfo"],@"type":@"JDISVSettlementAmountFloorResetDiscountCodeExchang",@"msg":isDelete ? SettlementLan(@"settlement_discount_code_delete_success") : SettlementLan(@"settlement_discount_code_success"),@"ToastType":@(ISVInstantTypeFinish)}];
                    }else{//错误信息返回
                        NSString *errormsg = [responseObject jdcd_getStringElementForKey:@"message"];
                        NSError *resError = [NSError errorWithDomain:@"settlement.discountCoide.vaildate.error" code:-1 userInfo:@{@"msg":errormsg,@"ToastType":@(ISVInstantTypeOnlyMessage)}];
                        [subscriber sendError:resError];
                    }
                }else{//操作失败toast
                    NSError *resError = [NSError errorWithDomain:@"settlement.discountCoide.vaildate.error" code:-1 userInfo:@{@"msg":isDelete ? SettlementLan(@"settlement_discount_code_delete_failed") : SettlementLan(@"settlement_discount_code_failed"),@"ToastType":@(ISVInstantTypeFail)}];
                    [subscriber sendError:resError];
                }
            }
        }];
        return nil;
    }] ;
    
     return [rac doNext:^(id  _Nullable x) {
         [[NSNotificationCenter defaultCenter] postNotificationName:@"JDISVSettlementAutoUseCouponCode" object:x];
    }];
}
- (RACSignal *)requestCodes{
    return [[RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        NSDictionary *params = @{@"clientVersion":@"1.0.0",
                                 @"client":@"iOS",
                                 @"lang":@"zh_CN",
                                 @"isPresale":[NSNumber numberWithBool:(ISPresell || ISFirstMoenyFlag) ? YES : NO],
                                 @"ptFlag":[NSNumber numberWithBool:ISGroupBuy],
                                 @"immediatelyBuy":[NSNumber numberWithBool:KASettlementAmountFloorNetService.source == 1 ? YES : NO],
                                 @"orderStr":@{},

        };
        [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"se_getCashCouponCodesNew" version:@"" parameters:params complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            if (error){
                [subscriber sendError:error];
            } else {
                NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
                NSString *code = (NSString *)[responseObject objectForKey:@"code"];
                NSString *message = (NSString *)[responseObject objectForKey:@"message"];
                
                if ([resultCode jdcd_validateString] && [resultCode isEqualToString:@"C-DM@60-CD@1.100000"]) {
                    // 正常
                    NSDictionary *resultInfo = (NSDictionary *)[responseObject objectForKey:@"resultInfo"];
                    [subscriber sendNext:resultInfo];
                } else {
                    // 异常
                    NSString *errorCode = @"-999";
                    NSInteger codeInt = 0;
                    if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                        if ([message jdcd_validateString] == NO) {
                            message = SettlementLan(@"setllement_not_logged_in") ;
                        }
                        errorCode = @"3";
                        codeInt = 3;
                    } else {
                        if ([resultCode jdcd_validateString]) {
                            errorCode = resultCode;
                        }
                        if (![message jdcd_validateString]) {
                            message = SettlementLan(@"setllement_unknown_error") ;
                        }
                    }
                    NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey":errorCode, NSLocalizedDescriptionKey:message}];
                    [subscriber sendError:resultError];
                }
            }
        }];
        return nil;
    }] deliverOnMainThread];
}
/// 获取优惠码详情
- (RACSignal *)fastbePromocodeDetail:(NSString *)code{
    if (![code jdcd_validateString]){
        return nil;
    }
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [PlatformService showLoadingInView:[PlatformService getTopViewController].view];
        [SettlementNetService request:JDCDHTTPSessionRequestTypeGet requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"marketing_promocode_detail" version:@"" parameters:@{@"promoCode":code} complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            [PlatformService dismissInView:[PlatformService getTopViewController].view];
            if (error){
                NSError *resError = [NSError errorWithDomain:@"settlement.discountCoide.detail.error" code:-1 userInfo:@{@"msg":SettlementLan(@"checkout_group_buy_network_error"),@"ToastType":@(ISVInstantTypeFail)}];
                [subscriber sendError:resError];
            }
            else{
                if ([responseObject isKindOfClass:[NSDictionary class]]){
                    BOOL resultCode = [responseObject jdcd_getNumberElementForKey:@"result"].boolValue;
                    if (resultCode){
                        [subscriber sendNext:[responseObject jdcd_getDicElementForKey:@"data"]];
                    }else{
                        NSString *errormsg = [responseObject jdcd_getStringElementForKey:@"msg"];
                        NSError *resError = [NSError errorWithDomain:@"settlement.discountCoide.detail.error" code:-1 userInfo:@{@"msg":[errormsg jdcd_validateString] ? errormsg : SettlementLan(@"checkout_group_buy_network_error"),@"ToastType":@(ISVInstantTypeOnlyMessage)}];
                        [subscriber sendError:resError];
                    }
                }else{
                    NSString *errormsg = [responseObject jdcd_getStringElementForKey:@"msg"];
                    NSError *resError = [NSError errorWithDomain:@"settlement.discountCoide.detail.error" code:-1 userInfo:@{@"msg":[errormsg jdcd_validateString] ? errormsg : SettlementLan(@"checkout_group_buy_network_error"),@"ToastType":@(ISVInstantTypeOnlyMessage)}];
                    [subscriber sendError:resError];
                }
            }
        }];
        return nil;
    }];
}

/// 输入框默认展示文本
- (NSString *)defalutText{
    return [JDISVCouponTagModel loadPromotionCode];
}

///返回是否需需要挽留弹框
- (BOOL)whetherItIsNecessaryToDisplayTheRetainingBulletBox:(NSString *)codeInputText{
    return NO;
    //判断逻辑 ：与上一次进入该页面的值，做对比，如果有变动返回YES 没有变动返回NO
//    if (_couponDiscount && _couponDiscount.couponCodeFlag && _couponDiscount.couponDiscountBasic){//进入时已经选择了优惠码
//        if (codeInputText.length > 0){
//            if ([_couponDiscount.couponCode isEqualToString:codeInputText]){
//                return NO;
//            }else{
//                return YES;
//            }
//        }else{
//            return YES;
//        }
//    }else{//进入时还没有选择过优惠码
//        if (codeInputText.length > 0){
//            return YES;
//        }else{
//            return NO;
//        }
//    }
}

#pragma mark - <dealloc>
- (void)dealloc{
    NSLog(@"JDISVDiscountCodeViewModel dealloc");
}


@end
