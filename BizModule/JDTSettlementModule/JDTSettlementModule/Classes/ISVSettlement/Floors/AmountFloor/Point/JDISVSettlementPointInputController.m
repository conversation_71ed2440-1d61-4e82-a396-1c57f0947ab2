
//
//  JDISVSettlementPointController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointInputController.h"

//#import <JDISVDZNEmptyDataSetModule/JDISVDZNEmptyDataSetModule-umbrella.h>

#import "JDISVSettlementPointViewModel.h"

#import "JDISVSettlementAmountFloorBaseViewModel.h"
#import "JDISVSettlementPointTableHeaderView.h"
#import "JDISVSettlementPointTableHeaderViewModel.h"

#import "NSBundle+JDISVSettlement.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "NSAttributedString+JDISVSettlementAmount.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>
#import "UIImage+JDISVSettlement.h"
#import "JDISVSettlementAmountFloorPointModel.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
//#define RATE 10.0

@interface JDISVSettlementPointInputController () <UITextFieldDelegate>

@property (strong, nonatomic) UIView *bgView;
@property (strong, nonatomic) UILabel *sheetTitleLabel;
@property (strong, nonatomic) UILabel *bottomLabel;
@property (strong, nonatomic) UILabel *rightLabel;
@property (strong, nonatomic) UIButton *confirmButton;
@property (strong, nonatomic) UIButton *cancleButton;
@property (strong, nonatomic) UIButton *closeButton;
@property (strong, nonatomic) UITextField *remarkTF;
@property (assign, nonatomic) CGFloat keyboardHeight;
@property (assign, nonatomic) BOOL isShowKeyboard;

@property (strong, nonatomic) NSString *smallPoint;//最少使用积分;
@property (strong, nonatomic) NSString *largePoint;//最多使用积分;
@property (strong, nonatomic) NSString *useCountPoint;//使用的积分;
@property (assign, nonatomic) CGFloat rate;//积分换算金额比例

// ViewModel
@property (nonatomic, strong) JDISVSettlementPointViewModel *pointViewModel;
@property (nonatomic, strong) JDISVSettlementAmountFloorPointModel *pointModel;

@property (strong, nonatomic) NSString *pointDisamount;//接口换算后的积分抵扣金额

@end

@implementation JDISVSettlementPointInputController

- (void)viewDidLoad {
    [super viewDidLoad];
    
//    [self reloadView];
    [self setupUI];
    [self updateUIText];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [IQKeyboardManager sharedManager].enable = NO;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = NO;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyBoardDidShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyBoardDidHide:) name:UIKeyboardWillHideNotification object:nil];
}
- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    
    
    [self.remarkTF becomeFirstResponder];
    
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardDidShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardDidHideNotification object:nil];
}

# pragma mark - UI
- (void)setupUI{
    
    self.view.backgroundColor = [UIColor clearColor];
    
    UIView *bgView = [[UIView alloc] init];
    self.bgView = bgView;
    bgView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    [self.view addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(0);
        make.height.mas_equalTo(200);
        make.leading.mas_equalTo(self.view).offset(0);
        make.trailing.mas_equalTo(self.view).offset(0);
    }];
    
    
    UILabel *sheetTitleLabel = [[UILabel alloc]init];
    self.sheetTitleLabel = sheetTitleLabel;
    sheetTitleLabel.textAlignment = NSTextAlignmentCenter;
    sheetTitleLabel.attributedText = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_points_list_title")  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
    [bgView addSubview:sheetTitleLabel];
    [sheetTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(16);
        make.leading.mas_equalTo(bgView).offset(0);
        make.trailing.mas_equalTo(bgView).offset(0);
        make.height.mas_equalTo(24);
    }];
    
//    self.contentTitleView = [[UIView alloc] init];
//    self.contentTitleView.backgroundColor = [UIColor yellowColor];
//    [_bgView addSubview:self.contentTitleView];
    
    self.closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.closeButton addTarget:self action:@selector(clickCloseButton) forControlEvents:UIControlEventTouchUpInside];
    [self.closeButton setImage:[UIImage jdisvSettlement_imageNamed:@"jdka_settlement_float_close"] forState:UIControlStateNormal];
    [bgView addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(16);
        make.trailing.mas_equalTo(bgView).offset(-18);
        make.height.mas_equalTo(20);
        make.width.mas_equalTo(20);
    }];
        
    UILabel *leftLabel = [[UILabel alloc]init];
    leftLabel.text = SettlementLan(@"checkout_points_use") ;
    leftLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    leftLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [bgView addSubview:leftLabel];
    [leftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(68);
        make.leading.mas_equalTo(bgView).offset(65);
        make.height.mas_equalTo(20);
//        make.width.mas_equalTo(30);
    }];
    
    UILabel *centerLabel = [[UILabel alloc]init];
    centerLabel.textAlignment = NSTextAlignmentRight;
    centerLabel.text = SettlementLan(@"checkout_points_use_suffix") ;
    centerLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    centerLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [bgView addSubview:centerLabel];
    [centerLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(leftLabel.mas_top);
        make.trailing.mas_equalTo(bgView).offset(-103);
        make.height.mas_equalTo(20);
//        make.width.mas_equalTo(76);
    }];
    
    UILabel *rightLabel = [[UILabel alloc]init];
    self.rightLabel = rightLabel;
    rightLabel.textAlignment = NSTextAlignmentLeft;
    rightLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    rightLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"];
    [bgView addSubview:rightLabel];
    [rightLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(leftLabel.mas_top);
        make.leading.mas_equalTo(centerLabel.mas_trailing).offset(4);
        make.trailing.mas_equalTo(bgView);
        make.height.mas_equalTo(20);
    }];
    
    UILabel *bottomLabel = [[UILabel alloc]init];
    self.bottomLabel = bottomLabel;
    bottomLabel.textAlignment = NSTextAlignmentCenter;
    bottomLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    bottomLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    [bgView addSubview:bottomLabel];
    [bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(centerLabel.mas_bottom).offset(12);
        make.leading.mas_equalTo(bgView);
        make.trailing.mas_equalTo(bgView);
        make.height.mas_equalTo(16);
    }];
    
  
    self.cancleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.cancleButton addTarget:self action:@selector(clickCancleButton) forControlEvents:UIControlEventTouchUpInside];
    [self.cancleButton setTitle:SettlementLan(@"checkout_not_use_points")  forState:UIControlStateNormal];
    [self.cancleButton setTitle:SettlementLan(@"checkout_not_use_points")  forState:UIControlStateHighlighted];
    [self.cancleButton setTitle:SettlementLan(@"checkout_not_use_points")  forState:UIControlStateDisabled];
    [self.cancleButton renderB5WithCornerRadius:40/2];
    [bgView addSubview:self.cancleButton];
    [self.cancleButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(bgView).offset(-18);
        make.leading.mas_equalTo(bgView).offset(21);
//        make.trailing.mas_equalTo(bgView).offset(-18);
        make.width.mas_equalTo(([UIScreen mainScreen].bounds.size.width-22-22-12)/2);
        make.height.mas_equalTo(40);
    }];
    
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.confirmButton addTarget:self action:@selector(clickConfirmButton) forControlEvents:UIControlEventTouchUpInside];
    [self.confirmButton setTitle:SettlementLan(@"checkout_use_points")  forState:UIControlStateNormal];
    [self.confirmButton setTitle:SettlementLan(@"checkout_use_points")  forState:UIControlStateHighlighted];
    [self.confirmButton setTitle:SettlementLan(@"checkout_use_points")  forState:UIControlStateDisabled];
    [self.confirmButton renderB1];
    [bgView addSubview:self.confirmButton];
    [self.confirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(bgView).offset(-18);
        make.trailing.mas_equalTo(bgView).offset(-21);
        make.width.mas_equalTo(([UIScreen mainScreen].bounds.size.width-21.5-21.5-12)/2);
        make.height.mas_equalTo(40);
    }];
    
    self.remarkTF = [[UITextField alloc] init];
    self.remarkTF.backgroundColor = [UIColor clearColor];
    self.remarkTF.textAlignment = NSTextAlignmentCenter;
    self.remarkTF.delegate = self;
    self.remarkTF.returnKeyType = UIReturnKeyDone;
    self.remarkTF.rightViewMode = UITextFieldViewModeWhileEditing;
    self.remarkTF.keyboardType = UIKeyboardTypeNumberPad;
    self.remarkTF.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    [self.remarkTF addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
//    [self.remarkTF becomeFirstResponder];
    [bgView addSubview:self.remarkTF];
//    self.remarkTF.attributedPlaceholder = _viewModel.reamarkTFPlaceholderAttributedString;
//    self.remarkTitleLabel.attributedText = _viewModel.reamarkTitleAttributedString;
    [self.remarkTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(leftLabel.mas_top);
        make.leading.mas_equalTo(leftLabel.mas_trailing).offset(0);
        make.trailing.mas_equalTo(centerLabel.mas_leading).offset(0);
        make.height.mas_equalTo(20);
//        make.bottom.mas_equalTo(bgView).offset(0);
    }];
}

- (void)updateUIText{
    self.remarkTF.placeholder = self.useCountPoint.integerValue > 0 ? self.useCountPoint : self.smallPoint;
    @try {
        if([[NSString getKAUseLang] isEqualToString:@"en"]){
            self.rightLabel.text = [NSString stringWithFormat:@"%@%.2f",[NSString getJDCDPriceTag],0.0];
        }else{
            self.rightLabel.text = [NSString stringWithFormat:@"%.2f%@",0.0,@"ريال"];
        }
    } @catch (NSException *exception) {
        
    }
    if (self.largePoint.integerValue > 0) {

            self.bottomLabel.text = [NSString stringWithFormat:SettlementLan(@"checkout_use_points_hint")
                                     ,self.smallPoint,self.largePoint]; ;

    }
}

- (void)keyBoardDidShow:(NSNotification*)aNotification {
    
    if (self.isShowKeyboard)
        return;
    self.isShowKeyboard = YES;
    
        NSDictionary* info = [aNotification userInfo];
        CGSize kbSize = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue].size;
        self.keyboardHeight = kbSize.height;
    // 获取键盘动画时间
        CGFloat animationTime  = [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    // 定义好动作
        void (^animation)(void) = ^void(void) {
                self.bgView.frame = CGRectMake(0, self.bgView.frame.origin.y-self.keyboardHeight, self.bgView.frame.size.width,  self.bgView.frame.size.height);
    };
    
    if (animationTime > 0) {
                [UIView animateWithDuration:animationTime animations:animation];
            } else {
                    animation();
                }
}
- (void)keyBoardDidHide:(NSNotification*)aNotification {
    if (self.isShowKeyboard == NO)
        return;
    self.isShowKeyboard = NO;
        NSDictionary* info = (NSDictionary *)[aNotification userInfo];
        CGSize kbSize = [[info objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue].size;
        self.keyboardHeight = kbSize.height;
    // 获取键盘动画时间
        CGFloat animationTime  = [[info objectForKey:UIKeyboardAnimationDurationUserInfoKey] floatValue];
    // 定义好动作
        void (^animation)(void) = ^void(void) {
                self.bgView.frame = CGRectMake(0, self.bgView.frame.origin.y+self.keyboardHeight, self.bgView.frame.size.width,  self.bgView.frame.size.height);

    };
    
    if (animationTime > 0) {
                [UIView animateWithDuration:animationTime animations:animation];
            } else {
                    animation();
                }
}


# pragma mark - Data
- (void)updateViewModelWith:(id)data {
    if ([data isKindOfClass:JDISVSettlementAmountFloorPointModel.class]) {
        self.pointModel = (JDISVSettlementAmountFloorPointModel *)data;
        self.rate = _pointModel.rate ? _pointModel.rate.floatValue : 100.0;
        self.useCountPoint = _pointModel.useCount ? : @"0";
        self.smallPoint = _pointModel.useLowLimit ? : @"0";
        self.largePoint = _pointModel.useHighLimit ? : @"0";
    }
}


#pragma mark TextField delegate
- (void)textFieldDidChange:(UITextField *)textField {
    self.rightLabel.text = [NSString stringWithFormat:@"%@%.2f",[NSString getJDCDPriceTag],self.remarkTF.text.floatValue/self.rate];
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
     
    [self.remarkTF resignFirstResponder];
}

- (BOOL)textFieldShouldEndEditing:(UITextField *)textField{
    return YES;

}
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField{
    return YES;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    if ([textField isFirstResponder]) {
        [self.remarkTF resignFirstResponder];
    }
    
    return YES;
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string{

    if ([string jdcd_validateString]){
        
        if (![string jdcd_isNum]){
            return NO;
        }
        NSString *value = [NSString stringWithFormat:@"%@%@",textField.text ?: @"" , string ?: @""];
        if (value.floatValue > self.largePoint.floatValue){
            return NO;
        }
    }
    
    return YES;
}

# pragma mark - Action
- (void)clickCloseButton {
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)clickCancleButton {
    self.pointDisamount = @"0";
    if (self.confirmCallback) {
        self.confirmCallback([self usingPointPayParam]);
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)clickConfirmButton {
    self.pointDisamount = self.remarkTF.text ? : @"0";
    if (self.confirmCallback) {
        self.confirmCallback([self usingPointPayParam]);
    }
    [self dismissViewControllerAnimated:YES completion:nil];
    
//    if (self.remarkTF.text.integerValue > self.largePoint.integerValue)
//        return;
//
//    [self requestPointWithParam:self.remarkTF.text complete:^(NSDictionary * _Nullable data, NSError * _Nullable error) {
//        if (error) {
//            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
//        }else{
//            NSNumber *pointDisamount  = (NSNumber *)[data objectForKey:@"data"];
//            self.pointDisamount = [NSString stringWithFormat:@"%@",pointDisamount ? : @""];
//            if (self.confirmCallback) {
//                self.confirmCallback([self usingPointPayParam]);
//            }
//            [self dismissViewControllerAnimated:YES completion:nil];
//        }
//    }];
}

- (NSDictionary *)usingPointPayParam {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    // usedFlag
    if (self.pointDisamount.integerValue > 0) {
        [param addEntriesFromDictionary:@{@"usedFlag": @(YES)}];
    } else {
        [param addEntriesFromDictionary:@{@"usedFlag": @(NO)}];
    }
    // useCount
    NSString *useCount = @"0";
    if (self.pointDisamount.integerValue > 0) {
        useCount = self.pointDisamount;
    }
    [param addEntriesFromDictionary:@{@"useCount":useCount}];

    // canUseCount
    NSString *canUseCount = @"";
    if ([_pointModel.canUseCount jdcd_validateString]) {
        canUseCount = [_pointModel.canUseCount copy];
    }
    [param addEntriesFromDictionary:@{@"canUseCount":canUseCount}];
    // totalCount
    NSString *totalCount = @"";
    if ([_pointModel.totalCount jdcd_validateString]) {
        totalCount = [_pointModel.totalCount copy];
    }
    [param addEntriesFromDictionary:@{@"totalCount":totalCount}];
    return [NSDictionary dictionaryWithDictionary:param];
}


# pragma mark - net work
//调接口把积分换算成金额
- (void)requestPointWithParam:(NSString *)point complete:(void(^)(NSDictionary * _Nullable data, NSError *  _Nullable error))completeBlock {
                NSMutableDictionary *param = [NSMutableDictionary dictionary];
                
//                if (source == 1) {
//                    // 商详
//                    [param addEntriesFromDictionary:@{@"immediatelyBuy":@(YES), @"verticalTag":@"cn_ybxt_b2c"}];
//
//                } else {
                    [param addEntriesFromDictionary:@{@"verticalTag":@"cn_ybxt_b2c"}];
//                }
                
//                NSMutableDictionary *orderStrMap = [NSMutableDictionary dictionary];
//                [orderStrMap addEntriesFromDictionary:orderStrParam];
                //拼团固定参数处理
//                if (ISGroupBuy) {
//                    [param setObject:@(YES) forKey:@"ptFlag"];
//                }
//                //预售场景下入参
//                if (ISPresell) {
//                    [param setObject:@(YES) forKey:@"isPresale"];
//                }
                
                [param setObject:point ? : @"" forKey:@"integral"];
//                [param addEntriesFromDictionary:@{@"orderStr":orderStrMap}];
                
                [SettlementNetService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm path:@"" function:@"api.integral.integralConvert" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
                    if (error){
                        completeBlock(nil, error);
                    } else {
                        NSString *resultCode = (NSString *)[responseObject objectForKey:@"resultCode"];
                        NSString *code = (NSString *)[responseObject objectForKey:@"code"];
                        NSString *message = (NSString *)[responseObject objectForKey:@"message"];
                        NSNumber *success = (NSNumber *)[responseObject objectForKey:@"success"];

                        if (success.boolValue) {
                            // 正常
                            completeBlock(responseObject, nil);
                        } else {
                            // 异常
                            NSString *errorCode = @"-999";
                            NSInteger codeInt = 0;
                            if ([code jdcd_validateString] && [code isEqualToString:@"3"]) {
                                // TODO code == 3 未登录
                                if ([message jdcd_validateString] == NO) {
                                    message = SettlementLan(@"setllement_not_logged_in") ;
                                }
                                errorCode = @"3";
                                codeInt = 3;
                            } else {
                                if ([resultCode jdcd_validateString]) {
                                    errorCode = resultCode;
                                }
                                if (![message jdcd_validateString]) {
                                    message = SettlementLan(@"setllement_unknown_error") ;
                                }
                            }
                            NSError *resultError = [NSError errorWithDomain:@"JDISVSettlementLogicErrorDomain" code:codeInt userInfo:@{@"JDISVSettlementErrorResultCodeKey": errorCode, NSLocalizedDescriptionKey:message}];
                            completeBlock(nil,resultError);
                        }
                    }
                    
                }];
            }
@end
