//
//  JDISVSettlementPointViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointViewModel.h"

#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import "JDISVSettlementPointItemViewModel.h"
#import "JDISVSettlementPointTableHeaderViewModel.h"

#import "JDISVSettlementAmountFloorPointModel.h"
#import "JDISVSettlementAmountFloorPointItemModel.h"

#import "NSAttributedString+JDISVSettlementAmount.h"
#import "UIImage+JDISVSettlement.h"

static const CGFloat kJDISVSettlementPointViewModelTitleViewHeight = 55.f;
static const CGFloat kJDISVSettlementPointViewModelDetailInfoViewHeight = 44.f;
static const CGFloat kJDISVSettlementPointViewModelBottomViewHeight = 50.f;

@interface JDISVSettlementPointViewModel()
@property (nonatomic, strong) JDISVSettlementAmountFloorPointModel *pointModel;
@property (nonatomic, copy) NSArray *cellViewModels;
@property (nonatomic, assign) NSInteger rawSelectedIndex; /**< -1标识未选中 */
@property (nonatomic, assign) NSInteger selectIndex; /**< -1标识未选中 */
@end

@implementation JDISVSettlementPointViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        _selectIndex = -1;
        _rawSelectedIndex = -1;
    }
    return self;
}

#pragma mark - Data

- (void)updateViewModelWith:(id)data type:(NSInteger)type {
    
    [self updateViewModelCommonInfo];
    
    if (type == JDISVSettlementPointViewModelUpdateTypeInit) {
        if ([data isKindOfClass:JDISVSettlementAmountFloorPointModel.class]) {
            self.pointModel = (JDISVSettlementAmountFloorPointModel *)data;
            self.headerViewModel = [[JDISVSettlementPointTableHeaderViewModel alloc] init];
            // 判断是否为空页面
            if (_pointModel.pointItems.count > 0) {
                
                // 有积分
                self.isEmptyView = NO;
                self.bottomViewHeight = kJDISVSettlementPointViewModelBottomViewHeight;
                self.detailInfoViewHeight = kJDISVSettlementPointViewModelDetailInfoViewHeight;
                // 积分数标题
                self.pointCountTitleAttribuedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_points_list_sum：")  colorKey:@"#C7" fontKey:@"#T7"];
                self.pointCountTitleLabelWidth = [self.pointCountTitleAttribuedString ka_settlement_amount_widthOfAttributedStringHeight:20.f fontKey:@"#T7"];
                // 积分总数
                NSString *canUsePoint = @"";
                if ([_pointModel.canUseCount jdcd_validateString]) {
                    canUsePoint = [_pointModel.canUseCount copy];
                }
                self.pointCountAttributedString = [[NSAttributedString alloc] initWithString:canUsePoint attributes:@{
                    NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                    NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightBold]
                }];
                if ([canUsePoint jdcd_validateString]) {
                    self.pointCountLabelWidth = ceil([canUsePoint jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightBold] maxNumberOfLines:1].width);
                } else {
                    self.pointCountLabelWidth = 0;
                }
                
                // 更新扣减信息
                [self updateDiscountDetailInfoWith: _pointModel];
                
                
                // 使用说明
                [self.headerViewModel updateViewModelWith:_pointModel.useRuleMsg];
                
                // 积分条目信息
                NSMutableArray *itemViewModels = [NSMutableArray array];
                for (JDISVSettlementAmountFloorPointItemModel *itemModel in _pointModel.pointItems) {
                    if (itemModel.selected) {
                        self.selectIndex = [_pointModel.pointItems indexOfObject:itemModel];
                        self.rawSelectedIndex = _selectIndex;
                    }
                    JDISVSettlementPointItemViewModel *itemViewModel = [[JDISVSettlementPointItemViewModel alloc] init];
                    [itemViewModel updateWithData:itemModel forType:JDISVSettlementPointViewModelUpdateTypeInit];
                    [itemViewModels addObject:itemViewModel];
                }
                
                if (_pointModel.useRuleTitle && _pointModel.useRuleTitle.count > 0) {
                    self.useRuleTitle = [[_pointModel.useRuleTitle firstObject] copy];
                } else {
                    self.useRuleTitle = @"";
                }
                
                if (_pointModel.useRuleContent && _pointModel.useRuleContent.count > 0) {
                    self.useRuleContent = [[_pointModel.useRuleContent firstObject] copy];
                } else {
                    self.useRuleContent = @"";
                }
                
                self.cellViewModels = [NSArray arrayWithArray:itemViewModels];
                
                self.sectionViewModels = @[self.cellViewModels];
                
            } else {
                // 无积分
                [self updateViewModelEmptyView];
                self.useRuleContent = @"";
                self.useRuleTitle = @"";
            }
        }
    } else if (type == JDISVSettlementPointViewModelUpdateTypeClickPointItem) {
        NSNumber *clickIndex = (NSNumber *)data;
        [self updateViewModelWithClickPointItemIndex:[clickIndex unsignedIntegerValue]];
        
    }
    
}

- (void)updateViewModelCommonInfo {
    
    self.sheetTitleAtrributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_points_list_title")  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];
    self.closeButtonImg = [UIImage jdisvSettlement_imageNamed:@"jdka_settlement_float_close"];
    self.useInfoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_points_list_use_desc")  colorKey:@"#C5" fontKey:@"#T9"];
}

/// 更新抵扣区详细区域信息
- (void)updateDiscountDetailInfoWith:(JDISVSettlementAmountFloorPointModel *)model {
    JDISVSettlementAmountFloorPointModel *selectedItemModel;
    for (JDISVSettlementAmountFloorPointItemModel *itemModel in model.pointItems) {
        if (itemModel.selected) {
            selectedItemModel = itemModel;
            break;
        }
    }
    
    // 更新顶部扣减信息区域
    [self updateDiscountInfoUsing:selectedItemModel];
    
}


/// 选择积分抵扣区域信息
/// @param selectedModel 选中的条目模型
- (void)updateDiscountInfoUsing:(JDISVSettlementAmountFloorPointItemModel * _Nullable)selectedModel {
    if (selectedModel) {
        // 选择了积分
        self.dicountTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_points_list_discount_p1")  colorKey:@"#C7" fontKey:@"#T9"];
        self.dicountTitleLabelWidth = [self.dicountTitleAttributedString ka_settlement_amount_widthOfAttributedStringHeight:14.f fontKey:@"#T9"];
        // 金额
        NSString *discount = @"";
        if ([selectedModel.pointDiscount jdcd_validateString]) {
            discount = [selectedModel.pointDiscount copy];
        }
        
        NSMutableAttributedString *discountAmountAttrText = [[NSMutableAttributedString alloc] init];
        [discountAmountAttrText KA_renderWithPriceStr:discount type:KAPriceTypeP5 colorType:@"#C9"];
        CGFloat discountAmountWidth = ceil([discountAmountAttrText.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].width);
        NSString *hasDiscountText = SettlementLan(@"checkout_points_list_discount_p2") ;
        NSMutableAttributedString *discountAmountTmpAttrText = [[NSMutableAttributedString alloc] initWithString:hasDiscountText attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]
        }];
        CGFloat hasDiscountTextWidth = ceil([hasDiscountText jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].width);
        
        [discountAmountTmpAttrText appendAttributedString:[discountAmountAttrText copy]];
        self.dicountInfoAttributedString = [discountAmountTmpAttrText copy];
        self.discountInfoLabelWidth = discountAmountWidth + hasDiscountTextWidth + 2.f;
      
        self.dicountTitleLabelTrailing = 2.f + _discountInfoLabelWidth + 18.f;
    } else {
        // 未选择积分: 本单暂不使用积分 T9 C7
        self.dicountTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_points_not_use")  colorKey:@"#C7" fontKey:@"#T9"];
        self.dicountTitleLabelWidth = [self.dicountTitleAttributedString ka_settlement_amount_widthOfAttributedStringHeight:14.f fontKey:@"#T9"];
        self.dicountTitleLabelTrailing = 18.f;
        self.discountInfoLabelWidth = 0;
    }
}

- (void)updateViewModelWithClickPointItemIndex:(NSUInteger)index {
    JDISVSettlementAmountFloorPointItemModel *selectedModel;
    if (index < _cellViewModels.count) {
        JDISVSettlementPointItemViewModel *itemViewModel = [self.cellViewModels objectAtIndex:index];
        if(itemViewModel && itemViewModel.selected) {
            itemViewModel.selected = NO;
            self.selectIndex = -1;
        } else {
            for (JDISVSettlementPointItemViewModel *itemViewModel in self.cellViewModels) {
                itemViewModel.selected = NO;
            }
            itemViewModel.selected = YES;
            selectedModel = itemViewModel.itemModel;
            self.selectIndex = index;
        }
    }
    [self updateDiscountInfoUsing:selectedModel];
}

// 空页面更新
- (void)updateViewModelEmptyView {
    self.isEmptyView = YES;
    self.cellViewModels = [NSArray array];
    self.sectionViewModels = [NSArray array];
    self.bottomViewHeight = 0;
    self.detailInfoViewHeight = 0;
    self.pointCountLabelWidth = 0;
    self.pointCountLabelWidth = 0;
    self.emptyViewContent = SettlementLan(@"checkout_points_list_empty_tips") ;
    self.discountInfoLabelWidth = 0;
    self.pointCountLabelWidth = 0;
    
    // 使用说明
    [self.headerViewModel updateViewModelWith:nil];
}

- (CGFloat)emptyViewHeight {
    return CGRectGetHeight([[UIScreen mainScreen] bounds]) * 0.8 - _detailInfoViewHeight - _bottomViewHeight - kJDISVSettlementPointViewModelTitleViewHeight;
}

#pragma mark - 请求更新
- (BOOL)needRequstUpdateUsePoint {
    return _rawSelectedIndex != _selectIndex;
}

- (NSDictionary *)usingPointPayParam {
    NSMutableDictionary *param = [NSMutableDictionary dictionary];

    // usedFlag
    if (_selectIndex == -1) {
        [param addEntriesFromDictionary:@{@"usedFlag": @(NO)}];
    } else {
        [param addEntriesFromDictionary:@{@"usedFlag": @(YES)}];
    }
    // useCount
    NSString *useCount = @"0";
    if (_selectIndex != -1 && _selectIndex < self.cellViewModels.count) {
        JDISVSettlementPointItemViewModel *itemViewModel = [self.cellViewModels objectAtIndex:_selectIndex];
        if ([itemViewModel.usePoint jdcd_validateString]) {
            useCount = [itemViewModel.usePoint copy];
        }
    }
    [param addEntriesFromDictionary:@{@"useCount":useCount}];
    // canUseCount
    NSString *canUseCount = @"";
    if ([_pointModel.canUseCount jdcd_validateString]) {
        canUseCount = [_pointModel.canUseCount copy];
    }
    [param addEntriesFromDictionary:@{@"canUseCount":canUseCount}];
    // totalCount
    NSString *totalCount = @"";
    if ([_pointModel.totalPoint jdcd_validateString]) {
        totalCount = [_pointModel.totalPoint copy];
    }
    [param addEntriesFromDictionary:@{@"totalCount":totalCount}];
    return [NSDictionary dictionaryWithDictionary:param];
}
@end
