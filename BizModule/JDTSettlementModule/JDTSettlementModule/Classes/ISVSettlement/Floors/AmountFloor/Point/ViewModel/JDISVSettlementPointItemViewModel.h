//
//  JDISVSettlementPointItemViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementAmountFloorPointItemModel;

typedef NS_ENUM(NSUInteger, JDISVSettlementPointItemViewModelUpdateType) {
    JDISVSettlementPointItemViewModelUpdateTypeInit = 0, /**< 初始化 */
    
};

@interface JDISVSettlementPointItemViewModel : JDISVSettlementAmountFloorBaseCellViewModel
@property (nonatomic, copy) NSAttributedString *discountInfoAttributedString;
@property (nonatomic) BOOL selected;
@property (nonatomic, copy) NSString *discount;
@property (nonatomic, copy) NSString *usePoint;

@property (nonatomic, strong) JDISVSettlementAmountFloorPointItemModel *itemModel;
@end

NS_ASSUME_NONNULL_END
