//
//  JDISVSettlementPointTableHeaderViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointTableHeaderViewModel.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <NSAttributedString+JDISVSettlementAmount.h>

@implementation JDISVSettlementPointTableHeaderViewModel
- (void)updateViewModelWith:(NSString * _Nullable)contentInfo {
    self.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R30"];
    if (contentInfo && [contentInfo jdcd_validateString]) {
        self.infoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:contentInfo colorKey:@"#C12" fontKey:@"#T9"];
        CGFloat labelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f * 2 - 12.f * 2;
        self.infoLabelHeight = [_infoAttributedString ka_settlement_amount_heightOfAttributedStringWidth:labelWidth maxNumberOfLines:0 fontKey:@"#T9"];
        
        self.contentViewHeight = _infoLabelHeight + 5.f * 2;
        self.headerViewHeight = _contentViewHeight + 9.f; /**< top:0 bottom:9 */
        self.contentBgColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12" alpha:0.07f];
    } else {
        self.headerViewHeight = 0;
        self.infoLabelHeight = 0;
        self.contentViewHeight = 0;
    }
}
@end
