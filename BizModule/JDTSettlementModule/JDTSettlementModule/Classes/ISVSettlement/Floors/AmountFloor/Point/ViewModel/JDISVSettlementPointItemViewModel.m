//
//  JDISVSettlementPointItemViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointItemViewModel.h"

#import "JDISVSettlementAmountFloorPointItemModel.h"

@interface JDISVSettlementPointItemViewModel()
@end

@implementation JDISVSettlementPointItemViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementPointItemCell";
        self.cellIdentifier = @"JDISVSettlementPointItemCell";
        self.height = 50.f;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if (type == JDISVSettlementPointItemViewModelUpdateTypeInit) {
        self.itemModel = (JDISVSettlementAmountFloorPointItemModel *)data;
        self.discount = [_itemModel.pointDiscount copy];
        self.usePoint = [_itemModel.usePointCount copy];
        self.selected = _itemModel.selected;
        NSString *usePointText = @"";
        if ([_usePoint jdcd_validateString]) {
            usePointText = [NSString stringWithFormat:SettlementLan(@"setllement_deduction") , _usePoint];
        }
        NSMutableAttributedString *discountAmountAttrText;
        if ([_discount jdcd_validateString]) {
            discountAmountAttrText = [[NSMutableAttributedString alloc] init];
            [discountAmountAttrText KA_renderWithPriceStr:_discount type:KAPriceTypeP4 colorType:@"#C9"];
        }
        
        NSMutableAttributedString *infoTmpAttributedString = [[NSMutableAttributedString alloc] initWithString:usePointText attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [infoTmpAttributedString appendAttributedString:[discountAmountAttrText copy]];
        
        self.discountInfoAttributedString = [infoTmpAttributedString copy];
    }
}
@end
