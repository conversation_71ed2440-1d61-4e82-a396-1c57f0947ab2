//
//  JDISVSettlementPointTableHeaderView.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointTableHeaderView.h"

#import "JDISVSettlementPointTableHeaderViewModel.h"

@interface JDISVSettlementPointTableHeaderView()
@property (weak, nonatomic) IBOutlet UIView *contentView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *contentViewHeight;
@property (weak, nonatomic) IBOutlet UILabel *infoLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *infoLabelHeight;
@end

@implementation JDISVSettlementPointTableHeaderView

- (void)updateWith:(JDISVSettlementPointTableHeaderViewModel *)headerViewModel {
    self.backgroundColor = [UIColor clearColor];
    self.contentView.backgroundColor = headerViewModel.contentBgColor;
    self.contentView.layer.masksToBounds = YES;
    self.contentView.layer.cornerRadius = headerViewModel.cornerRadius;
    self.contentViewHeight.constant = headerViewModel.contentViewHeight;
    
    self.infoLabel.attributedText = headerViewModel.infoAttributedString;
    self.infoLabelHeight.constant = headerViewModel.infoLabelHeight;
}

@end
