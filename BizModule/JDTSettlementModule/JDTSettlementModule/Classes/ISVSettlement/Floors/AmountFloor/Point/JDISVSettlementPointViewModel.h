//
//  JDISVSettlementPointViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorBaseViewModel.h"

NS_ASSUME_NONNULL_BEGIN


@class JDISVSettlementPointTableHeaderViewModel;

typedef NS_ENUM(NSUInteger, JDISVSettlementPointViewModelUpdateType) {
    JDISVSettlementPointViewModelUpdateTypeInit = 0, /**< 初始化 */
    JDISVSettlementPointViewModelUpdateTypeClickPointItem, /**< 点击积分条目 */
};

@interface JDISVSettlementPointViewModel : JDISVSettlementAmountFloorBaseViewModel
@property (nonatomic, copy) NSAttributedString *sheetTitleAtrributedString; /**< 标题 */
@property (nonatomic, strong) UIImage *closeButtonImg;
@property (nonatomic, copy) NSAttributedString *useInfoAttributedString; /**< 使用说明 */

// 顶部使用信息提示
@property (nonatomic, strong) JDISVSettlementPointTableHeaderViewModel *headerViewModel; /**< 顶部使用信息提示 */

@property (nonatomic, assign) CGFloat detailInfoViewHeight;

@property (nonatomic, copy) NSAttributedString *pointCountTitleAttribuedString;
@property (nonatomic, assign) CGFloat pointCountTitleLabelWidth;

@property (nonatomic, copy) NSAttributedString *pointCountAttributedString;
@property (nonatomic, assign) CGFloat pointCountLabelWidth;

@property (nonatomic, copy) NSAttributedString *dicountTitleAttributedString;
@property (nonatomic, assign) CGFloat dicountTitleLabelWidth;
@property (nonatomic, assign) CGFloat dicountTitleLabelTrailing;

@property (nonatomic, copy) NSAttributedString *dicountInfoAttributedString;
@property (nonatomic, assign) CGFloat discountInfoLabelWidth;

@property (nonatomic, copy) NSString *useRuleTitle;
@property (nonatomic, copy) NSString *useRuleContent;


@property (nonatomic, assign) CGFloat bottomViewHeight; /**< 底部高度 */

// 属性判断
@property (nonatomic) BOOL isEmptyView;
@property (nonatomic, copy) NSString *emptyViewContent;


- (void)updateViewModelWith:(id)data type:(NSInteger)type;

- (CGFloat)emptyViewHeight;

// 是否请求更新
- (BOOL)needRequstUpdateUsePoint;
// 获取请求参数
- (NSDictionary *)usingPointPayParam;
@end

NS_ASSUME_NONNULL_END
