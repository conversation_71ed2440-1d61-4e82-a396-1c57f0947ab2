//
//  JDISVSettlementPointController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointController.h"

#import <JDISVDZNEmptyDataSetModule/JDISVDZNEmptyDataSetModule-umbrella.h>

#import "JDISVSettlementPointViewModel.h"

#import "JDISVSettlementAmountFloorBaseViewModel.h"
#import "JDISVSettlementPointTableHeaderView.h"
#import "JDISVSettlementPointTableHeaderViewModel.h"

#import "NSBundle+JDISVSettlement.h"


@interface JDISVSettlementPointController () <DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>
// TopView
@property (weak, nonatomic) IBOutlet UIView *contentTitleView;
@property (weak, nonatomic) IBOutlet UILabel *sheetTitleLabel;
@property (weak, nonatomic) IBOutlet UIButton *closeButton;
@property (weak, nonatomic) IBOutlet UILabel *useInfoLabel;

// DetailInfoView
@property (weak, nonatomic) IBOutlet UIView *detailInfoView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *detailInfoViewHeight;

@property (weak, nonatomic) IBOutlet UILabel *pointCountTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pointCountTitleLabelWidth;
@property (weak, nonatomic) IBOutlet UILabel *pointCountLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pointCountLabelWidth;

@property (weak, nonatomic) IBOutlet UILabel *discountTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *discountTitleLabelWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *discountTitleLabelTrailing;

@property (weak, nonatomic) IBOutlet UILabel *discountInfoLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *discountInfoLabelWidth;

// TableView
@property (weak, nonatomic) IBOutlet UITableView *tableView;

// BottomView
@property (weak, nonatomic) IBOutlet UIView *bottomView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomViewHeight;
@property (weak, nonatomic) IBOutlet UIButton *confirmButton;

// ViewModel
@property (nonatomic, strong) JDISVSettlementPointViewModel *pointViewModel;
@end

@implementation JDISVSettlementPointController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self reloadView];

}

# pragma mark - Data
- (void)updateViewModelWith:(id)data {
    // 初始化:在ViewDidLoad之前
    [self.pointViewModel updateViewModelWith:data type:JDISVSettlementPointViewModelUpdateTypeInit];
}

# pragma mark - UI

- (void)reloadView {
    [self updateControllerSubViews];
    [self.tableView reloadData];
}

- (void)updateControllerSubViews {
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.detailInfoView.backgroundColor = [UIColor clearColor];
    self.contentTitleView.backgroundColor = [UIColor clearColor];
    self.tableView.backgroundColor = [UIColor clearColor];
    self.bottomView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1-d"];
    
    self.tableView.emptyDataSetSource = self;
    self.tableView.emptyDataSetDelegate = self;
    
    [self.closeButton setImage:_pointViewModel.closeButtonImg forState:UIControlStateNormal];
    self.sheetTitleLabel.attributedText = _pointViewModel.sheetTitleAtrributedString;
    self.useInfoLabel.attributedText = _pointViewModel.useInfoAttributedString;
    [self.useInfoLabel jd_addTapAction:@selector(showUseRuleAlert) withTarget:self];
    self.useInfoLabel.userInteractionEnabled = YES;
    
    self.detailInfoViewHeight.constant = _pointViewModel.detailInfoViewHeight;
    // 积分标题
    self.pointCountTitleLabel.attributedText = _pointViewModel.pointCountTitleAttribuedString;
    self.pointCountTitleLabelWidth.constant = _pointViewModel.pointCountTitleLabelWidth;
    // 积分数
    self.pointCountLabel.attributedText = _pointViewModel.pointCountAttributedString;
    self.pointCountLabelWidth.constant = _pointViewModel.pointCountLabelWidth;
    // 抵扣金额标题
    self.discountTitleLabel.attributedText = _pointViewModel.dicountTitleAttributedString;
    self.discountTitleLabelWidth.constant = _pointViewModel.dicountTitleLabelWidth;
    self.discountTitleLabelTrailing.constant = _pointViewModel.dicountTitleLabelTrailing;
    // 抵扣金额
    self.discountInfoLabel.attributedText = _pointViewModel.dicountInfoAttributedString;
    self.discountInfoLabelWidth.constant = _pointViewModel.discountInfoLabelWidth;
    
    self.bottomViewHeight.constant = _pointViewModel.bottomViewHeight;
    self.bottomView.hidden = (_pointViewModel.bottomViewHeight == 0);
}


# pragma mark - Action
- (void)clickCloseButton {
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)clickConfirmButton {
    if ([self.pointViewModel needRequstUpdateUsePoint] && self.confirmCallback) {
        self.confirmCallback([self.pointViewModel usingPointPayParam]);
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}

/// 选中积分
/// @param index 选中序号
- (void)selectedPointIndexOf:(NSUInteger)index {
    [self.pointViewModel updateViewModelWith:@(index) type:JDISVSettlementPointViewModelUpdateTypeClickPointItem];
    [self reloadView];
}

- (void)showUseRuleAlert {
    [KAAlert alert].config
    .renderW4(_pointViewModel.useRuleTitle, _pointViewModel.useRuleContent)
    .addFillAction(SettlementLan(@"setllement_i_known") , ^{
        // 收回
    })
    .alertShow();
}

# pragma mark - Delegate
- (void)customFloatControllerUI {
    [self.closeButton addTarget:self action:@selector(clickCloseButton) forControlEvents:UIControlEventTouchUpInside];
    [self.confirmButton addTarget:self action:@selector(clickConfirmButton) forControlEvents:UIControlEventTouchUpInside];
    [self.confirmButton setTitle:SettlementLan(@"checkout_confirm") forState:UIControlStateNormal];
    [self.confirmButton setTitle:SettlementLan(@"checkout_confirm") forState:UIControlStateHighlighted];
    [self.confirmButton setTitle:SettlementLan(@"checkout_confirm") forState:UIControlStateDisabled];
    [self.confirmButton renderB1];
}

- (UIView *)floatContentTableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    JDISVSettlementPointTableHeaderView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), CGFLOAT_MIN)];
    if (section == 0) {
        headerView = [[[NSBundle jdisvSettlement_bundle] loadNibNamed:@"JDISVSettlementPointTableHeaderView" owner:nil options:nil] firstObject];
        [headerView setFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), _pointViewModel.headerViewModel.headerViewHeight)];
        [headerView updateWith:_pointViewModel.headerViewModel];
    }
    return headerView;
}

- (void)clickCellIndexOf:(NSIndexPath *)indexPath {
    [self selectedPointIndexOf:indexPath.row];
}

- (CGFloat)floatContentTableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return _pointViewModel.headerViewModel.headerViewHeight;
    } else {
        return CGFLOAT_MIN;
    }
}
# pragma mark - DataSource
- (UITableView *)floatControllerTableView {
    return self.tableView;
}

- (JDISVSettlementAmountFloorBaseViewModel *)floatControllerViewModel {
    if (_pointViewModel == nil) {
        _pointViewModel = [[JDISVSettlementAmountFloorBaseViewModel alloc] init];
    }
    return _pointViewModel;
}
- (NSBundle *)floatControllerModuleBundle {
    return [NSBundle jdisvSettlement_bundle];
}

- (JDISVSettlementPointViewModel *)pointViewModel {
    if (_pointViewModel == nil) {
        _pointViewModel = [[JDISVSettlementPointViewModel alloc] init];
    }
    return _pointViewModel;
}

#pragma mark - DZNEmptyDataSet
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView {
    if (_pointViewModel && _pointViewModel.isEmptyView) {
        return YES;
    } else {
        return NO;
    }
}

- (UIView *)customViewForEmptyDataSet:(UIScrollView *)scrollView {
    KAEmptyView *emptyView = [[KAEmptyView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), [self.pointViewModel emptyViewHeight]) type:KAEmptyViewTypeNotAction];
    NSLayoutConstraint *heightConstraint = [NSLayoutConstraint constraintWithItem:emptyView attribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:nil attribute:NSLayoutAttributeNotAnAttribute multiplier:1 constant:self.tableView.frame.size.height];
    [emptyView addConstraint:heightConstraint];
    emptyView.decrible = _pointViewModel.emptyViewContent;
    emptyView.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:JDISVImageTypeNoDataScore];
    return emptyView;
}
@end
