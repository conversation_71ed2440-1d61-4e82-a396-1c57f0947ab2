//
//  JDISVSettlementPointItemCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementPointItemCell.h"

#import "JDISVSettlementPointItemViewModel.h"

@interface JDISVSettlementPointItemCell()
@property (weak, nonatomic) IBOutlet UILabel *discountInfoLabel;
@property (weak, nonatomic) IBOutlet UIButton *radioButton;

@property (nonatomic, strong) JDISVSettlementPointItemViewModel *itemViewModel;
@end

@implementation JDISVSettlementPointItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.radioButton.enabled = YES;
    self.radioButton.userInteractionEnabled = NO;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementPointItemViewModel.class]) {
        self.itemViewModel = (JDISVSettlementPointItemViewModel *)viewModel;
        self.discountInfoLabel.attributedText = _itemViewModel.discountInfoAttributedString;
        self.radioButton.jdisv_selected_B7 = _itemViewModel.selected;
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
}

@end
