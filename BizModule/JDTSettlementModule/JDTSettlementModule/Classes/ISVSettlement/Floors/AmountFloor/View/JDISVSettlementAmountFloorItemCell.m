//
//  JDISVSettlementAmountFloorItemCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorItemCell.h"

#import "JDISVSettlementAmountFloorItemCellViewModel.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>


@interface JDISVSettlementAmountFloorItemCell()
@property (weak, nonatomic) IBOutlet UILabel *amountTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *amountInfoLabel;

@property (nonatomic, strong) JDISVSettlementAmountFloorItemCellViewModel *itemViewModel;
@property (nonatomic,strong) UIImageView *iconImageView;
@end

@implementation JDISVSettlementAmountFloorItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.amountInfoLabel.hidden = YES;
    self.amountTitleLabel.hidden = YES;
    
    self.iconImageView = [[UIImageView alloc]init];
    self.iconImageView.userInteractionEnabled = YES;
//    tips
    
    UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_TIPS imageSize:CGSizeMake(14, 14) color:[[UIColor alloc] initWithRed:0.6 green:0.6 blue:0.6 alpha:1]];
    self.iconImageView.image = image;
    
    [self.iconImageView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(taoAction)]];
    
    [self.contentView addSubview:_iconImageView];
    
    [_iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.amountTitleLabel);
        make.width.equalTo(@14);
        make.height.equalTo(@14);
        make.leading.equalTo(self.amountTitleLabel.mas_trailing).offset(7);
    }];
    [_iconImageView setHidden:YES];
}

- (void)taoAction{
//    JDCDISVAction *action = [[JDCDISVAction alloc]initWithType:@"JDISVLastMoneyDetailAction" broadcastAction:YES];
//    [self isv_sendAction:action];
    [self.delegate sendNext:@{@"cell_identifier":@"JDISVSettlementAmountEndPaymentPresaleFloorItemCell"}];
}
- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementAmountFloorItemCellViewModel.class]) {
        self.itemViewModel = (JDISVSettlementAmountFloorItemCellViewModel *)viewModel;
        if (_itemViewModel.amountTitleAttributedString) {
            self.amountTitleLabel.hidden = NO;
            self.amountTitleLabel.attributedText = _itemViewModel.amountTitleAttributedString;
        } else {
            self.amountTitleLabel.hidden = YES;
        }
        
        if (_itemViewModel.amountAttribtuedString) {
            self.amountInfoLabel.hidden = NO;
            self.amountInfoLabel.attributedText = _itemViewModel.amountAttribtuedString;
        } else {
            self.amountInfoLabel.hidden = YES;
        }
        
        if (ISPresell && ISFirstMoenyFlag == false) {
            self.amountTitleLabel.text = SettlementLan(@"checkout_presale_end_price_label") ;
            [_iconImageView setHidden:NO];
        }else{
            [_iconImageView setHidden:YES];
        }
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    self.amountInfoLabel.hidden = YES;
    self.amountTitleLabel.hidden = YES;
}

@end
