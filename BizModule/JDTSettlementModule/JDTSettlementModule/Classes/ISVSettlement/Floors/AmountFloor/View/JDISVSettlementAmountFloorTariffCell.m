//
//  JDISVSettlementAmountFloorTariffCell.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/12.
//

#import "JDISVSettlementAmountFloorTariffCell.h"
#import "JDISVSettlementAmountFloorTariffCellViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@interface JDISVSettlementAmountFloorTariffCell ()

@property (strong,nonatomic) UILabel *cusTitleLable;//标题
@property (strong,nonatomic) UILabel *cusValueLabel;//value
@property (strong,nonatomic) UIImageView *tipIcon;//提示icon

@property (strong,nonatomic) JDISVSettlementAmountFloorTariffCellViewModel *viewModel;

@end

@implementation JDISVSettlementAmountFloorTariffCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.backgroundColor = [UIColor clearColor];
        [self.contentView addSubview:self.cusTitleLable];
        [self.contentView addSubview:self.cusValueLabel];
        self.contentView.clipsToBounds = YES;
        
        self.tipIcon = [[UIImageView alloc] init];
        self.tipIcon.userInteractionEnabled = YES;
        self.tipIcon.contentMode = UIViewContentModeCenter;
        _tipIcon.image = [UIImage ka_iconWithName:JDIF_ICON_INFO imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        [self.contentView addSubview:self.tipIcon];
        
        [self.cusTitleLable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(18);
//            make.width.mas_lessThanOrEqualTo(90);
        }];
        [self.tipIcon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.contentView);
            make.trailing.mas_equalTo(self.contentView).offset(-14);
            make.width.mas_equalTo(20);
            make.height.mas_equalTo(16);
        }];
        [self.cusValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.contentView);
            make.trailing.equalTo(self.tipIcon.mas_leading);
        }];
        
        self.cusValueLabel.userInteractionEnabled = YES;
        
        [self.tipIcon jd_addTapAction:@selector(clickInfoImageView) withTarget:self];
        self.delegate = [[RACSubject alloc] init];
    }
    return self;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel{
    if ([viewModel isKindOfClass:[JDISVSettlementAmountFloorTariffCellViewModel class]]){
        self.viewModel = viewModel;
        self.cusTitleLable.attributedText = self.viewModel.tariffTitleAttributedString;
        self.cusValueLabel.attributedText = self.viewModel.tariffLabelAttributedString;
        self.tipIcon.hidden = !self.viewModel.isShowRuleTip;
    }
}

- (void)clickInfoImageView {
    // 提示
    if ([_viewModel.ruleTip jdcd_validateString]){
        NSDictionary *contentInfo = @{@"title": @"", @"content": _viewModel.ruleTip};
        NSDictionary *singalMessage =  @{ @"cell_identifier": @"JDISVSettlementAmountFloorTariffCell",
                                          @"action_type": @"show_info_tariff",
                                          @"data": contentInfo};
        [self.delegate sendNext:singalMessage];
    }
}

#pragma mark - <getter>

- (UILabel *)cusTitleLable{
    if(!_cusTitleLable) {
        _cusTitleLable = [[UILabel alloc] init];
    }
    return _cusTitleLable;
}


- (UILabel *)cusValueLabel{
    if(!_cusValueLabel) {
        _cusValueLabel = [[UILabel alloc] init];
    }
    return _cusValueLabel;
}


@end
