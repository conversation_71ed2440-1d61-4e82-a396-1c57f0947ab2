//
//  JDISVSettlementAmountFloorDiscountCell.m
//  JDISVSettlementModule
//
//  Created by ext.zhangchen41 on 2024/9/27.
//

#import "JDISVSettlementAmountFloorDiscountCell.h"
#import "JDISVSettlementAmountFloorDiscountCellViewModel.h"

@interface JDISVSettlementAmountFloorDiscountCell ()
@property (weak, nonatomic) IBOutlet UILabel *freightAmountTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *freightAmountLabel;
@property (nonatomic, strong) JDISVSettlementAmountFloorDiscountCellViewModel *viewModel;
@end

@implementation JDISVSettlementAmountFloorDiscountCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementAmountFloorDiscountCellViewModel.class]) {
        self.viewModel = (JDISVSettlementAmountFloorDiscountCellViewModel *)viewModel;
        
        if (_viewModel.amountTitleAttributedString) {
            self.freightAmountTitleLabel.attributedText = _viewModel.amountTitleAttributedString;
            self.freightAmountTitleLabel.hidden = NO;
        } else {
            self.freightAmountTitleLabel.hidden = YES;
        }
        
        if (_viewModel.amountAttributedString) {
            self.freightAmountLabel.attributedText = _viewModel.amountAttributedString;
        } else {
            self.freightAmountLabel.hidden = YES;
        }
    }
}

@end
