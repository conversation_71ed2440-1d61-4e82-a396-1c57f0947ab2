//
//  JDISVSettlementAmountFloorFreightCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorFreightCell.h"
#import "JDISVSettlementAmountFloorFreightCellViewModel.h"

@interface JDISVSettlementAmountFloorFreightCell()
@property (weak, nonatomic) IBOutlet UILabel *freightAmountTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *freightAmountLabel;


@property (nonatomic, strong) JDISVSettlementAmountFloorFreightCellViewModel *viewModel;
@end

@implementation JDISVSettlementAmountFloorFreightCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementAmountFloorFreightCellViewModel.class]) {
        self.viewModel = (JDISVSettlementAmountFloorFreightCellViewModel *)viewModel;
        if (_viewModel.freightTitleAttributedString) {
            self.freightAmountTitleLabel.attributedText = _viewModel.freightTitleAttributedString;
            self.freightAmountTitleLabel.hidden = NO;
        } else {
            self.freightAmountTitleLabel.hidden = YES;
        }
        
        if (_viewModel.freightAmountAttributedString) {
            self.freightAmountLabel.attributedText = _viewModel.freightAmountAttributedString;
        } else {
            self.freightAmountLabel.hidden = YES;
        }
    }
}

@end
