//
//  JDISVSettlementAmountFloorPointCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorInvoiceCell.h"

#import "JDISVSettlementAmountFloorInvoiceCellViewModel.h"

@interface JDISVSettlementAmountFloorInvoiceCell()
@property (weak, nonatomic) IBOutlet UILabel *pointTitleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pointTitleLabelWidth;
@property (weak, nonatomic) IBOutlet UILabel *pointCountLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pointCountLabelWidth;
@property (weak, nonatomic) IBOutlet UILabel *pointInfoLabel;
//@property (weak, nonatomic) IBOutlet NSLayoutConstraint *pointInfoLabelWidth;
@property (weak, nonatomic) IBOutlet UIImageView *infoImageView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *infoImageWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *infoImageHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *infoImageTrailing;

@property (nonatomic, strong) JDISVSettlementAmountFloorInvoiceCellViewModel *viewModel;
@end

@implementation JDISVSettlementAmountFloorInvoiceCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.pointInfoLabel.userInteractionEnabled = YES;
    self.infoImageView.userInteractionEnabled = YES;
    [self.pointInfoLabel jd_addTapAction:@selector(clickInfoImageView) withTarget:self];
    [self.infoImageView jd_addTapAction:@selector(clickInfoImageView) withTarget:self];
    self.delegate = [[RACSubject alloc] init];
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementAmountFloorInvoiceCellViewModel.class]) {
        self.viewModel = (JDISVSettlementAmountFloorInvoiceCellViewModel *)viewModel;
        if (_viewModel.pointTitleAttributedString) {
            self.pointTitleLabel.attributedText = _viewModel.pointTitleAttributedString;
            self.pointTitleLabelWidth.constant = _viewModel.pointTitleLabelWidth;
            self.pointTitleLabel.hidden = NO;
        } else {
            self.pointTitleLabelWidth.constant = 0;
            self.pointTitleLabel.hidden = YES;
        }
        
        if (_viewModel.pointCountAttributedString) {
            self.pointCountLabel.hidden = NO;
            self.pointCountLabel.attributedText = _viewModel.pointCountAttributedString;
            self.pointCountLabelWidth.constant = _viewModel.pointCountLabelWidth;
        } else {
            self.pointCountLabel.hidden = YES;
            self.pointCountLabelWidth.constant = 0;
        }
        
        self.infoImageView.contentMode = UIViewContentModeCenter;
        self.infoImageView.image = _viewModel.infoImage;
        self.infoImageWidth.constant = _viewModel.imageSize.width;
        self.infoImageHeight.constant = _viewModel.imageSize.height;
        self.infoImageTrailing.constant = _viewModel.imageTrailing;
        
        if (_viewModel.pointInfoAttributedString) {
            self.pointInfoLabel.hidden = NO;
            self.pointInfoLabel.attributedText = _viewModel.pointInfoAttributedString;
//            self.pointInfoLabelWidth.constant = _viewModel.pointInfoLabelWidth;
        } else {
            self.pointInfoLabel.hidden = YES;
//            self.pointInfoLabelWidth.constant = 0;
        }
    }
    
}

#pragma mark - Action
- (void)clickInfoImageView {
        // 打开发票h5页面
            NSDictionary *singalMessage =  @{ @"cell_identifier": @"JDISVSettlementAmountFloorInvoiceCell",
                             @"action_type": @"show_select_invoice_page"};
            [self.delegate sendNext:singalMessage];
    
}

@end
