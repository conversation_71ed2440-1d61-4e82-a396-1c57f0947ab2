//
//  JDISVSumMoneyTableViewCell.m
//  JDISVSettlementModule
//
//  Created by ext.songjian6 on 2022/8/16.
//

#import "JDISVSumMoneyTableViewCell.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeCornerRadius.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVSumMoneyCellViewModel.h"

@interface JDISVSumMoneyTableViewCell()
@property (nonatomic,strong) UILabel *sumMoneyLabel;
@property (nonatomic,strong) UIView *lineView;
@end

@implementation JDISVSumMoneyTableViewCell

-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        
        self.backgroundColor = UIColor.clearColor;
        self.contentView.backgroundColor = UIColor.clearColor;
        
        UILabel *sumMoneyLabel = [[UILabel alloc]init];
        self.sumMoneyLabel = sumMoneyLabel;
        [self.contentView addSubview:sumMoneyLabel];
        sumMoneyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        sumMoneyLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightRegular];
    
        UIView *lineView = [[UIView alloc]init];
        self.lineView = lineView;
        [self.contentView addSubview:lineView];
        lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_top).offset(14);
            make.width.equalTo(self.contentView);
            make.height.equalTo(@1);
        }];
        
        [self.sumMoneyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.contentView.mas_trailing).offset(-37/2);
            make.top.equalTo(lineView.mas_bottom).offset(18);
        }];
    }
    return self;
}

-(void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel{
    
//    CGFloat moneyValue = ((JDISVSumMoneyCellViewModel *)viewModel).amount.floatValue;
//    NSString *sumMoney = [NSString stringWithFormat:(@"%@%@ %.2f"),SettlementLan(@"checkout_pre_sale_sum_label"),[NSString getJDCDPriceTag] ,moneyValue];
//
//    NSString *first = [NSString stringWithFormat:@"%@ %.2f",[NSString getJDCDPriceTag] ,moneyValue];
//    NSString *sec = [NSString stringWithFormat:@"%.2f",[NSString getJDCDPriceTag] ,moneyValue];
//    NSRange rang = [sumMoney rangeOfString:first];
//    NSRange rang2 = [sumMoney rangeOfString:sec];
//    NSArray *moneyAry = [sumMoney componentsSeparatedByString:@"."];
//    NSString *firstMoney = moneyAry[0];
//    NSString *lastMoney = moneyAry[1];
//
//    NSString *largeNumber = [firstMoney componentsSeparatedByString:@":"][1];
//    NSMutableAttributedString *sumMoneyAttStr = [[NSMutableAttributedString alloc]initWithString:sumMoney];
//
//    [sumMoneyAttStr addAttributes:@{NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]} range:rang];
//    [sumMoneyAttStr addAttributes:@{NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightRegular]} range:rang2];
//    self.sumMoneyLabel.attributedText = sumMoneyAttStr;
    

    NSString *amount = ((JDISVSumMoneyCellViewModel *)viewModel).amount;
    NSString *sumMoney = SettlementLan(@"checkout_pre_sale_sum_label");
    NSMutableAttributedString *sumMoneyAttStr = [[NSMutableAttributedString alloc]initWithString:sumMoney];
    NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
    [priceAttributedString KA_renderWithPriceStr:amount ? : @"" type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
    [sumMoneyAttStr appendAttributedString:priceAttributedString];
    self.sumMoneyLabel.attributedText = sumMoneyAttStr;
    
}
@end
