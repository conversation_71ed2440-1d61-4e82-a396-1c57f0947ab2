//
//  JDISVSettlementAmountFloorCouponCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorCouponCell.h"

#import "JDISVSettlementAmountFloorCouponCellViewModel.h"


@interface JDISVSettlementAmountFloorCouponCell()
@property (weak, nonatomic) IBOutlet UILabel *couponTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *couponInfoLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightArrowImgView;
@property (weak, nonatomic) IBOutlet UIImageView *availableTagView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *availableTagViewWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *arrowRightWidth;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *infoTrailing;

@property (nonatomic, strong) JDISVSettlementAmountFloorCouponCellViewModel *viewModel;
@end

@implementation JDISVSettlementAmountFloorCouponCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backgroundColor = [UIColor clearColor];
    self.availableTagView.hidden = YES;
    self.availableTagViewWidth.constant = 0;
    self.rightArrowImgView.contentMode = UIViewContentModeCenter;
    
    self.rightArrowImgView.userInteractionEnabled = YES;
    self.couponInfoLabel.userInteractionEnabled = YES;
    
    [self.rightArrowImgView jd_addTapAction:@selector(clickUseCouponAction) withTarget:self];
    [self.couponInfoLabel jd_addTapAction:@selector(clickUseCouponAction) withTarget:self];
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementAmountFloorCouponCellViewModel.class]) {
        self.viewModel = (JDISVSettlementAmountFloorCouponCellViewModel *)viewModel;
        
        self.rightArrowImgView.image = _viewModel.rightArrowImg;
        self.infoTrailing.constant = _viewModel.infoTrailing;
        if (_viewModel.avalableTagImg) {
            self.availableTagView.hidden = NO;
            self.availableTagViewWidth.constant = _viewModel.availableTagViewWidth;
            self.availableTagView.image = _viewModel.avalableTagImg;
        } else {
            self.availableTagView.hidden = YES;
            self.availableTagViewWidth.constant = 0;
        }
        
        self.couponTitleLabel.attributedText = _viewModel.couponTitleAttributedString;
        self.couponInfoLabel.attributedText = _viewModel.couponInfoLabelAttributedString;
        
    }
}

- (void)prepareForReuse {
    [super prepareForReuse];
    
    self.availableTagView.hidden = YES;
    self.availableTagViewWidth.constant = 0;
}

#pragma mark - Action
- (void)clickUseCouponAction {
    if (self.viewModel.userActionStatus) {
        [self.delegate sendNext:@{@"cell_identifier": _viewModel.cellIdentifier ? : @"", @"action_type":@"show_select_coupon_page"}];
    }
}

@end
