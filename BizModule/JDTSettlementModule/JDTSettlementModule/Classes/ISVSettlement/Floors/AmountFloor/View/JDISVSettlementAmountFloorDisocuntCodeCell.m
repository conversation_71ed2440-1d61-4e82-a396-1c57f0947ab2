//
//  JDISVSettlementAmountDisocuntCodeCell.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/3/20.
//

#import "JDISVSettlementAmountFloorDisocuntCodeCell.h"
#import "JDISVSettlementAmountFloorCouponCellViewModel.h"
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "JDISVSettlementAmountFloorDiscountCodeCellViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVSettlementAmountFloorDisocuntCodeCell ()

@property (strong,nonatomic) UILabel *cusTitleLable;//标题
@property (strong,nonatomic) UILabel *cusValueLabel;//value
@property (strong,nonatomic) UIImageView *arrowIcon;//箭头

@property (strong,nonatomic) JDISVSettlementAmountFloorDiscountCodeCellViewModel *viewModel;

@end

@implementation JDISVSettlementAmountFloorDisocuntCodeCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.backgroundColor = [UIColor clearColor];
        [self.contentView addSubview:self.cusTitleLable];
        [self.contentView addSubview:self.cusValueLabel];
        [self.contentView addSubview:self.arrowIcon];
        self.contentView.clipsToBounds = YES;
        
        [self.cusTitleLable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView).mas_offset(9);
            make.leading.mas_equalTo(self.contentView).mas_offset(18);
//            make.width.mas_greaterThanOrEqualTo(90);
        }];
        
        [self.arrowIcon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.cusTitleLable);
            make.trailing.mas_equalTo(self.contentView).offset(-18);
            make.width.mas_offset(12);
        }];
        
        [self.cusValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView).mas_offset(9);
            make.trailing.mas_equalTo(self.arrowIcon.mas_leading).mas_offset(-3);
            make.leading.mas_equalTo(self.contentView).mas_offset(118.f);
        }];
        
        
        
        self.cusValueLabel.userInteractionEnabled = YES;
        self.arrowIcon.userInteractionEnabled = YES;
        
        [self.cusValueLabel jd_addTapAction:@selector(clickUseDiscountCodeAction) withTarget:self];
        [self.arrowIcon jd_addTapAction:@selector(clickUseDiscountCodeAction) withTarget:self];
    }
    return self;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel{
    if ([viewModel isKindOfClass:[JDISVSettlementAmountFloorDiscountCodeCellViewModel class]]){
        self.viewModel = viewModel;
        self.cusTitleLable.attributedText = self.viewModel.discountCodeTitleAttributedString;
        self.cusValueLabel.attributedText = self.viewModel.discountCodeLabelAttributedString;
        self.arrowIcon.hidden = !self.viewModel.arrowIconStatus;
    }
}

#pragma mark - Action
- (void)clickUseDiscountCodeAction {
    if (self.viewModel.userActionStatus) {
        [self.delegate sendNext:@{@"cell_identifier": _viewModel.cellIdentifier ? : @"", @"action_type":@"show_select_discount_code_page"}];
    }
}

#pragma mark - <getter>

- (UILabel *)cusTitleLable{
    if(!_cusTitleLable) {
        _cusTitleLable = [[UILabel alloc] init];
    }
    return _cusTitleLable;
}


//- (UILabel *)cusValueLabel{
//    if(!_cusValueLabel) {
//        _cusValueLabel = [[UILabel alloc] init];
//        _cusTitleLable.numberOfLines = 0;
//    }
//    return _cusValueLabel;
//}

- (UILabel *)cusValueLabel{
    if (!_cusValueLabel){
        _cusValueLabel = [[UILabel alloc] init];
        _cusValueLabel.numberOfLines = 0;
        _cusValueLabel.textAlignment = NSTextAlignmentRight;
    }
    return _cusValueLabel;
}

- (UIImageView *)arrowIcon{
    if(!_arrowIcon){
        _arrowIcon = [[UIImageView alloc] init];
        _arrowIcon.image = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    }
    return _arrowIcon;
}

@end
