//
//  JDISVSettlementAmountFloorCODCell.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/6/12.
//

#import "JDISVSettlementAmountFloorCODCell.h"
#import "JDISVSettlementAmountFloorCODCellViewModel.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@interface JDISVSettlementAmountFloorCODCell ()

@property (strong,nonatomic) UILabel *cusTitleLable;//标题
@property (strong,nonatomic) UILabel *cusValueLabel;//value

@property (strong,nonatomic) JDISVSettlementAmountFloorCODCellViewModel *viewModel;

@end

@implementation JDISVSettlementAmountFloorCODCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self){
        self.backgroundColor = [UIColor clearColor];
        [self.contentView addSubview:self.cusTitleLable];
        [self.contentView addSubview:self.cusValueLabel];
        self.contentView.clipsToBounds = YES;
        
        [self.cusTitleLable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.mas_equalTo(self.contentView);
            make.leading.mas_equalTo(self.contentView).mas_offset(18);
//            make.width.mas_lessThanOrEqualTo(90);
        }];
        
        [self.cusValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.contentView);
            make.trailing.mas_equalTo(self.contentView).offset(-34);
        }];
        
        self.cusValueLabel.userInteractionEnabled = YES;
    }
    return self;
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel{
    if ([viewModel isKindOfClass:[JDISVSettlementAmountFloorCODCellViewModel class]]){
        self.viewModel = viewModel;
        self.cusTitleLable.attributedText = self.viewModel.codTitleAttributedString;
        self.cusValueLabel.attributedText = self.viewModel.codLabelAttributedString;
    }
}

#pragma mark - <getter>

- (UILabel *)cusTitleLable{
    if(!_cusTitleLable) {
        _cusTitleLable = [[UILabel alloc] init];
    }
    return _cusTitleLable;
}


- (UILabel *)cusValueLabel{
    if(!_cusValueLabel) {
        _cusValueLabel = [[UILabel alloc] init];
    }
    return _cusValueLabel;
}


@end
