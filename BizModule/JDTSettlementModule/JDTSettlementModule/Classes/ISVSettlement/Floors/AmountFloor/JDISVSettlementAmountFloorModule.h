//
//  JDISVSettlementAmountFloorModule.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
#import "CHECKPriceAndDiscountCellModel.h"
#import "CHECKCouponModel.h"

NS_ASSUME_NONNULL_BEGIN

@class RACSignal, JDISVFloorCommonModel;

@interface JDISVSettlementAmountFloorModule : NSObject<JDISVFloorModuleProtocol>

@property (nonatomic, copy) NSArray <CHECKPriceAndDiscountCellModel *> *cellDataArr;

@property (nonatomic, copy) NSArray *sectionViewModels;
@property (nonatomic, assign) NSInteger sourceType;

@property (nonatomic, strong) JDISVFloorCommonModel *commonModel;

//预售付尾款调结算中台接口下发的优惠券数据，点击优惠券cell后会传入到优惠券VC里处理
@property (nonatomic, copy) NSArray *couponVos;
/// 优惠券列表
@property (nonatomic, strong) CHECKCouponModel *couponModel;

// 使用积分信号
- (RACSignal *)loadUsePointDataSignalParamOf:(NSDictionary *)param;
@end

NS_ASSUME_NONNULL_END
