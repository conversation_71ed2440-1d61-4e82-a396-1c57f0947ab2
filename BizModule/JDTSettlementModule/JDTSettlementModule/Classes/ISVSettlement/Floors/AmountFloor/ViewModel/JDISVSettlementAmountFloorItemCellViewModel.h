//
//  JDISVSettlementAmountFloorItemCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementAmountFloorItemCellViewModelType) {
    JDISVSettlementAmountFloorItemCellViewModelTypeTotal = 0, /**< 总金额 */
    JDISVSettlementAmountFloorItemCellViewModelTypeDiscount /**< 立减 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementAmountFloorItemCellViewModelUpdateType) {
    JDISVSettlementAmountFloorItemCellViewModelUpdateTypeMainInterface = 0, /**< 主接口 */
};

@interface JDISVSettlementAmountFloorItemCellViewModel : JDISVSettlementAmountFloorBaseCellViewModel
@property (nonatomic, assign) JDISVSettlementAmountFloorItemCellViewModelType cellType;
@property (nonatomic, copy, nullable) NSAttributedString *amountTitleAttributedString;
@property (nonatomic, copy, nullable) NSAttributedString *amountAttribtuedString;
@end

NS_ASSUME_NONNULL_END
