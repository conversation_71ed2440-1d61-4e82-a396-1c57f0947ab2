//
//  JDISVSettlementAmountFloorItemCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorItemCellViewModel.h"

#import "JDISVSettlementAmountFloorItemModel.h"

#import "NSAttributedString+JDISVSettlementAmount.h"

@interface JDISVSettlementAmountFloorItemCellViewModel()
@end

@implementation JDISVSettlementAmountFloorItemCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorItemCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorItemCell";
        self.height = 0;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if (type == JDISVSettlementAmountFloorItemCellViewModelUpdateTypeMainInterface) {
        if (![data isKindOfClass:JDISVSettlementAmountFloorItemModel.class]) {
            return;
        }
        JDISVSettlementAmountFloorItemModel *itemModel = (JDISVSettlementAmountFloorItemModel *)data;
        if ([itemModel.amountTitle jdcd_validateString]) {
            self.amountTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:itemModel.amountTitle colorKey:@"#C7" fontKey:@"#T7"];
        } else {
            self.amountTitleAttributedString = nil;
        }
        
        self.cellType = [itemModel.floorType integerValue];
        
        NSMutableAttributedString *price = [[NSMutableAttributedString alloc] initWithString:@""];
        if (_cellType == JDISVSettlementAmountFloorItemCellViewModelTypeTotal) {
            // 总金额 ￥1389.00 #222222
            
            if ([itemModel.amountInfo jdcd_validateString]) {
                [price KA_renderWithPriceStr:itemModel.amountInfo type:KAPriceTypeP4 colorType:@"#C7"];
                self.amountAttribtuedString = [price copy];
            } else if ([itemModel.amount jdcd_validateString]) {
                [price KA_renderWithPriceStr:itemModel.amount type:KAPriceTypeP4 colorType:@"#C7"];
                self.amountAttribtuedString = [price copy];
            } else {
                self.amountAttribtuedString = nil;
            }
            
            self.height = 38.f;
        } else if (_cellType == JDISVSettlementAmountFloorItemCellViewModelTypeDiscount) {
            // 立减 - ￥36.00 #EE323A
            
            if ([itemModel.amountInfo jdcd_validateString]) {
                
                [price KA_renderWithPriceStr:itemModel.amountInfo type:KAPriceTypeP4 colorType:@"#C9"];
                
                NSString *operatorStr = @"";
                if ([itemModel.operatorSymbol jdcd_validateString]) {
                    operatorStr = [itemModel.operatorSymbol copy];
                }
                
                NSMutableAttributedString *discountAttributedString = [[NSMutableAttributedString alloc] initWithString:operatorStr attributes:@{
                    NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"],
                    NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
                }];
                [discountAttributedString appendAttributedString:[price copy]];
                self.amountAttribtuedString = [discountAttributedString copy];
            } else {
                self.amountAttribtuedString = nil;
            }
            if ([itemModel.amount integerValue] == 0) {
                self.height = 0.f;
                self.amountAttribtuedString = nil;
                self.amountTitleAttributedString = nil;
            } else {
                self.height = 38.f;
            }
            
        } else {
            self.height = 0;
            self.amountAttribtuedString = nil;
            self.amountTitleAttributedString = nil;
        }
    }
    
}
@end
