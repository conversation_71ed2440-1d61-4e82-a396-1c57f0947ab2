//
//  JDISVSettlementAmountFloorDiscountCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.zhangchen41 on 2024/9/27.
//

#import "JDISVSettlementAmountFloorDiscountCellViewModel.h"
#import "NSAttributedString+JDISVSettlementAmount.h"

@implementation JDISVSettlementAmountFloorDiscountCellViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorDiscountCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorDiscountCell";
        self.height = 38.f;
        self.amountTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"settlement_title_shipping_fee_discount") colorKey:@"#C7" fontKey:@"#T7"];
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementAmountFloorDiscountModel.class]) {
        self.model = (JDISVSettlementAmountFloorDiscountModel *)data;
        if ([_model.freightFullDiscountAmountLabel jdcd_validateString]) {
            self.amountTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:_model.freightFullDiscountAmountLabel colorKey:@"#C7" fontKey:@"#T7"];
        } else {
            self.amountTitleAttributedString = nil;
        }
        
        NSString *amount = @"";
        if ([_model.freightFullDiscountAmount jdcd_validateString]) {
            amount = [NSString stringWithFormat:@"%@", _model.freightFullDiscountAmount];
        }
        NSMutableAttributedString *amountPrice = [[NSMutableAttributedString alloc] init];
        [amountPrice KA_renderWithPriceStr:amount type:KAPriceTypeP4 colorType:@"#11"];
        
        NSString *operatorStr = @"-";
        NSMutableAttributedString *operatorAttr = [[NSMutableAttributedString alloc] init];
        [operatorAttr KA_renderWithPriceStr:amount type:KAPriceTypeP4 colorType:@"#11"];
        
        NSMutableAttributedString *freightTmpAttributedString = [[NSMutableAttributedString alloc] initWithString:operatorStr attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C11"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [freightTmpAttributedString appendAttributedString:amountPrice];
        
        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:freightTmpAttributedString.string attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C11"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        self.amountAttributedString = [attributedString copy];
    }
}

@end
