//
//  JDISVSettlementAmountFloorRoundCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, JDISVSettlementAmountFloorRoundCellType) {
    JDISVSettlementAmountFloorRoundCellTypeR1 = 0, /**< 左上、右上圆角 */
    JDISVSettlementAmountFloorRoundCellTypeR2 /**< 左下、右下圆角 */
};
@interface JDISVSettlementAmountFloorRoundCellViewModel : JDISVSettlementAmountFloorBaseCellViewModel
@property (nonatomic, assign) JDISVSettlementAmountFloorRoundCellType cellType;

@property (nonatomic, assign) CGFloat radius;
@property (nonatomic, assign) CGFloat margin;
- (instancetype)initWithCellType:(JDISVSettlementAmountFloorRoundCellType)cellType;
@end

NS_ASSUME_NONNULL_END
