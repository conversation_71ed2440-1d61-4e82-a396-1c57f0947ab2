//
//  JDISVSumMoneyCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.songjian6 on 2022/8/16.
//

#import "JDISVSumMoneyCellViewModel.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

//#import "JDISVSettlementBottomFloor.h"

#import "JDISVSettlementBottomFloorModel.h"
#import "JDISVSettlementBottomFloorAmountItemModel.h"
#import "JDISVSettlementBottomFloorMainModel.h"
#import "JDISVSettlementOrderSkuModel.h"
#import "JDISVSettlementOrderModel.h"

#import "JDISVSettlementOrderStockOutSkuViewModel.h"

#import "NSAttributedString+JDISVSettlementBottom.h"
#import "NSBundle+JDISVSettlement.h"

static NSString * const kJDISVSettmentAmountBottomFloorPrefix = @"defaultProductAmountFloor";

@interface JDISVSumMoneyCellViewModel()
@property (nonatomic, strong) JDISVSettlementBottomFloorAmountItemModel *amountModel;

@end

@implementation JDISVSumMoneyCellViewModel
-(instancetype)init{
    self = super.init;
    if (self) {
        self.cellName = @"JDISVSumMoneyTableViewCell";
        self.cellIdentifier = @"JDISVSumMoneyTableViewCell";
        self.height = 18*3 + 10;
    }
    return self;;
}

-(void)updateWithData:(id)data forType:(NSInteger)type{
    NSDictionary *resultInfo = data[@"data"] ? : @{};
    NSArray *floorsArray = [resultInfo objectForKey:@"floors"];
    
    // 转楼层模型
    NSMutableArray *floorsModels = [NSMutableArray array];
    for (NSDictionary *floorData in floorsArray) {
        JDISVSettlementBottomFloorModel *floorModel = [JDISVSettlementBottomFloorModel yy_modelWithDictionary:floorData];
        [floorsModels addObject:floorModel];
    }
    
    [self updateViewModelWith:floorsModels];
}

- (void)updateViewModelWith:(NSArray *)floorDatas {
    if (floorDatas == nil) {
        self.amountModel = nil;
    }
    for (JDISVSettlementBottomFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:kJDISVSettmentAmountBottomFloorPrefix]) {
            JDISVSettlementBottomFloorAmountItemModel *amountItemModel = [JDISVSettlementBottomFloorAmountItemModel yy_modelWithDictionary:floorData.info];
            if ([amountItemModel.floorType isEqualToString:@"2"]) {
                // type:2 应付金额
                self.amountModel = amountItemModel;
                self.amount = amountItemModel.amount ? : @"" ;
                break;
            }
        }
    }
    
//    if (_amountModel) {
//        NSString *amount = @"";
//        if ([_amountModel.amount jdcd_validateString]) {
//            self.amount = [_amountModel.amount copy];
//        }
//        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
//        [priceAttributedString KA_renderWithPriceStr:amount type:KAPriceTypeP2 color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"]];
//        self.amountAttributedString = [priceAttributedString copy];
//    }
//    else {
//        self.height = 0;
//        self.amountAttributedString = nil;
//    }
}
@end
