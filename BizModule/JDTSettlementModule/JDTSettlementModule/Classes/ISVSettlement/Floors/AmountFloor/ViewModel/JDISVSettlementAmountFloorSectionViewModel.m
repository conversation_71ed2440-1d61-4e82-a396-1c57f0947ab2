//
//  JDISVSettlementAmountFloorSectionViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorSectionViewModel.h"

#import "JDISVSettlementAmountFloorItemCellViewModel.h"
#import "JDISVSettlementAmountFloorFreightCellViewModel.h"
#import "JDISVSettlementAmountFloorCouponCellViewModel.h"
#import "JDISVSettlementAmountFloorPointCellViewModel.h"
#import "JDISVSettlementAmountFloorInvoiceCellViewModel.h"
#import "JDISVSettlementAmountFloorRoundCellViewModel.h"
#import "JDISVSettlementAmountFloorDiscountCodeCellViewModel.h"
#import "JDISVSettlementAmoutFloorDiscountCodeModel.h"

#import "JDISVSettlementAmountFloorModel.h"
#import "JDISVSettlementAmountFloorItemModel.h"
#import "JDISVSettlementAmountFloorFreightModel.h"
#import "JDISVSettlementAmountFloorDiscountModel.h"
#import "JDISVSettlementAmountFloorCouponModel.h"
#import "JDISVSettlementAmountFloorPointModel.h"
//#import "JDISVSettlementAmountFloorInvoiceModel.h"
#import "JDISVSettlementAmountFloorPointItemModel.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>
#import "JDISVSumMoneyCellViewModel.h"
#import "JDISVSettlementAmountFloorTariffCellViewModel.h"
#import "JDISVSettlementAmountFloorCODCellViewModel.h"
#import "JDISVSettlementAmountFloorDiscountCellViewModel.h"
#import "JDISVSettlementAmoutFloorDefaultTaxModel.h"

static NSString * const kJDISVSettlementAmountFloorSectionItemFloorPrefix = @"defaultProductAmountFloor";
static NSString * const kJDISVSettlementAmountFloorSectionFreightFloorPrefix = @"totalFreightFloor";
static NSString * const kJDISVSettlementAmountFloorSectionDiscountFloorPrefix = @"discountAmountFloor";
static NSString * const kJDISVSettlementAmountFloorSectionCouponFloorPrefix = @"homePageFloorCouponFloor";
static NSString * const kJDISVSettlementAmountFloorSectionPointFloorPrefix = @"defaultPointsFloor";
static NSString * const kJDISVSettlementAmountFloorSectionInvoiceFloorPrefix = @"defaultInvoiceFloor";//发票
static NSString * const kJDISVSettlementAmountFloorSectiondDfaultTaxFloorPrefix = @"defaultTaxFloor";//COD,VAT
static NSString * const kJDISVSettlementAmountFloorSectionPointFloorItemPartNameSpace = @"cn_ybxt_b2c-FLR#balance.points-P#detailPart";

@interface JDISVSettlementAmountFloorSectionViewModel ()

@end

@implementation JDISVSettlementAmountFloorSectionViewModel
- (void)updateSectionWithUUIDKey:(NSString *)uuidKey
                       floorKeys:(NSArray *)floorKeys
                    allFloorData:(NSArray *)floorDatas
                    allOriginData:(NSDictionary *)allOriginData
                     commonModel:(JDISVFloorCommonModel *)commonModel
                      featureExt:(NSDictionary *)featureExt {
    
    // 商品金额Model
    JDISVSettlementAmountFloorItemModel *totalAmountModel;
    // 运费金额Model
    JDISVSettlementAmountFloorFreightModel *freightAmountModel;
    // 立减金额Model
    JDISVSettlementAmountFloorItemModel *discountAmountModel;

    
    // 优惠券金额Model
    JDISVSettlementAmountFloorCouponModel *couponAmountModel;
    // 优惠码金额Model
    JDISVSettlementAmoutFloorDiscountCodeModel *couponDiscountCodeModel;
    // 积分金额Model
    JDISVSettlementAmountFloorPointModel *pointAmountModel;
    // 免运费model
    JDISVSettlementAmountFloorDiscountModel *disCountModel;
    // 发票Model
    JDISVSettlementAmountFloorInvoiceModel *invoiceAmountModel;
    // 合计金额model
    JDISVSumMoneyCellViewModel *sumMoneyModel;
    
    NSMutableArray *defaultTaxModels = [NSMutableArray array];
    
    for (JDISVSettlementAmountFloorModel *floorData in floorDatas) {
        if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectionItemFloorPrefix]) {
            JDISVSettlementAmountFloorItemModel *itemAmountModel = [JDISVSettlementAmountFloorItemModel yy_modelWithDictionary:floorData.info];
            if ([itemAmountModel.floorType isEqualToString:@"0"]) {
                // 商品金额
                totalAmountModel = itemAmountModel;
                totalAmountModel.amountTitle =
                SettlementLan(@"checkout_summary_price_label") ;
            } else if ([itemAmountModel.floorType isEqualToString:@"1"]) {
                // 立减金额
                discountAmountModel = itemAmountModel;
                discountAmountModel.amountTitle = SettlementLan(@"checkout_summary_reduce_label") ;
            } else {
                continue;
            }
        } else if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectionFreightFloorPrefix]) {
            // 运费金额
            freightAmountModel = [JDISVSettlementAmountFloorFreightModel yy_modelWithDictionary:floorData.info];
        } 
        else if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectionDiscountFloorPrefix]) {
            // 免运费金额
            disCountModel = [JDISVSettlementAmountFloorDiscountModel yy_modelWithDictionary:floorData.info];
        } 
        else if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectionCouponFloorPrefix]) {
            // 优惠券金额
            couponAmountModel = [JDISVSettlementAmountFloorCouponModel yy_modelWithDictionary:floorData.info];
            // 优惠码金额
            couponDiscountCodeModel = [JDISVSettlementAmoutFloorDiscountCodeModel yy_modelWithDictionary:floorData.info];
        } else if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectionPointFloorPrefix]) {
            pointAmountModel = [JDISVSettlementAmountFloorPointModel yy_modelWithDictionary:floorData.info];
            NSMutableArray *pointItemModels = [NSMutableArray array];
           
            for (JDISVSettlementAmountFloorPartListItemModel *partModel in floorData.partList) {
                if ([partModel.type.nameSpace isEqualToString:kJDISVSettlementAmountFloorSectionPointFloorItemPartNameSpace]) {
                    JDISVSettlementAmountFloorPointItemModel *pointItemModel = [JDISVSettlementAmountFloorPointItemModel yy_modelWithDictionary:partModel.info];
                    [pointItemModels addObject:pointItemModel];
                }
                if (pointItemModels.count > 0) {
                    pointAmountModel.pointItems = [NSArray arrayWithArray:pointItemModels];
                } else {
                    pointAmountModel.pointItems = nil;
                }
            }
            
        }else if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectiondDfaultTaxFloorPrefix]){//税费 VAT COD手续费
            [defaultTaxModels removeAllObjects];
            for (JDISVSettlementAmountFloorPartListItemModel *info in floorData.partList) {
                JDISVSettlementAmoutFloorDefaultTaxModel *model = [JDISVSettlementAmoutFloorDefaultTaxModel yy_modelWithDictionary:info.info];
                if ([model.taxAmount jdcd_validateString] && [model.taxAmount floatValue] >= 0){
                    [defaultTaxModels addObject:model];
                }
            }
        }
//        else if ([floorData.uuid containsString:kJDISVSettlementAmountFloorSectionInvoiceFloorPrefix]) {
//            invoiceAmountModel = [JDISVSettlementAmountFloorInvoiceModel yy_modelWithDictionary:floorData.info];
//        }
    }
    NSMutableArray *amountSetions = [NSMutableArray array];
    
    JDISVSettlementAmountFloorRoundCellViewModel *topViewModel = [[JDISVSettlementAmountFloorRoundCellViewModel alloc] initWithCellType:JDISVSettlementAmountFloorRoundCellTypeR1];
    [amountSetions addObject:topViewModel];
    
    /* -------------商品金额------------*/
    JDISVSettlementAmountFloorItemCellViewModel *totalAmountViewModel = [[JDISVSettlementAmountFloorItemCellViewModel alloc] init];
    [totalAmountViewModel updateWithData:totalAmountModel forType:JDISVSettlementAmountFloorItemCellViewModelUpdateTypeMainInterface];
    [amountSetions addObject:totalAmountViewModel];
    /* --------------运费--------------*/
    if (freightAmountModel) {
        //    if([freightAmountModel.totalFreightMoney floatValue] != 0){
        JDISVSettlementAmountFloorFreightCellViewModel *freightAmountViewModel = [[JDISVSettlementAmountFloorFreightCellViewModel alloc] init];
        [freightAmountViewModel updateWithData:freightAmountModel forType:0];
        //    if (freightAmountModel){//运费数据存在 == 0展示，运费数据不存在，不展示
        [amountSetions addObject:freightAmountViewModel];
        //    }
        //    }
    }
    
    //TODO: KSA二期功能，判断是否有关税，是否有COD费用，展示
    for (JDISVSettlementAmoutFloorDefaultTaxModel *model in defaultTaxModels) {
        if ([model.type isEqualToString:@"1"] && [model.taxAmount jdcd_validateString] && [model.taxAmount floatValue] > 0){//cod
            /* --------------KSA-COD--------------*/
            JDISVSettlementAmountFloorCODCellViewModel *codViewModel = [JDISVSettlementAmountFloorCODCellViewModel new];
            [codViewModel updateWithData:model forType:1];
            [amountSetions addObject:codViewModel];
        }else if ([model.type isEqualToString:@"2"]){//vat
            /* --------------KSA关税--------------*/
            JDISVSettlementAmountFloorTariffCellViewModel *tariffViewModel = [[JDISVSettlementAmountFloorTariffCellViewModel alloc] init];
            tariffViewModel.amount = totalAmountModel.amount;
            [tariffViewModel updateWithData:model forType:1];
            [amountSetions addObject:tariffViewModel];
        }
    }
    
    /* --------------立减--------------*/
    if (discountAmountModel) {
        JDISVSettlementAmountFloorItemCellViewModel *discountAmountViewModel = [[JDISVSettlementAmountFloorItemCellViewModel alloc] init];
        [discountAmountViewModel updateWithData:discountAmountModel forType:JDISVSettlementAmountFloorItemCellViewModelUpdateTypeMainInterface];
        [amountSetions addObject:discountAmountViewModel];
    }
    /* -------------优惠券--------------*/
    if (!ISFirstMoenyFlag){
        JDISVSettlementAmountFloorCouponCellViewModel *couponAmountViewModel = [[JDISVSettlementAmountFloorCouponCellViewModel alloc] init];
        [couponAmountViewModel updateWithData:couponAmountModel ? couponAmountModel : [JDISVSettlementAmountFloorCouponModel new] featureExt:featureExt ];
        [amountSetions addObject:couponAmountViewModel];
    }
    
    if (!ISPresell && !ISFirstMoenyFlag){//判断商品是否为预售结算页(预售定金、和预售尾款)
        /* -------------优惠码--------------*/
        JDISVSettlementAmountFloorDiscountCodeCellViewModel *discountCodeAmountViewModel = [[JDISVSettlementAmountFloorDiscountCodeCellViewModel alloc] init];
        [discountCodeAmountViewModel updateWithData:couponDiscountCodeModel ? couponDiscountCodeModel : [JDISVSettlementAmoutFloorDiscountCodeModel new]  featureExt:featureExt ];
        [amountSetions addObject:discountCodeAmountViewModel];
    }
    
    /* --------------积分--------------*/
    if (!SettlementNetService.isVistor){
        JDISVSettlementAmountFloorPointCellViewModel *pointAmountViewModel = [[JDISVSettlementAmountFloorPointCellViewModel alloc] init];
        [pointAmountViewModel updateWithData:pointAmountModel forType:JDISVSettlementAmountFloorPointCellViewModelUpdateTypeMainInterface];
        if (!(ISPresell && ISFirstMoenyFlag == false)) {
            //非付尾款，才能使用积分
            [amountSetions addObject:pointAmountViewModel];
        }
    }
    
    /* --------------免运费--------------*/
    if (disCountModel) {
        // 有值才展示
        if([disCountModel.freightFullDiscountAmount floatValue] != 0){
            JDISVSettlementAmountFloorDiscountCellViewModel *disCountViewModel = [[JDISVSettlementAmountFloorDiscountCellViewModel alloc] init];
            [disCountViewModel updateWithData:disCountModel forType:0];
            [amountSetions addObject:disCountViewModel];
        }
    }
    
    /* --------------发票--------------*/
    BOOL invoiceFeatureFlag = ([featureExt objectForKey:@"invoiceFeature"] && [[featureExt objectForKey:@"invoiceFeature"] isEqualToNumber:@1]) ? YES : NO;
    if (invoiceFeatureFlag) {
        JDISVSettlementAmountFloorInvoiceCellViewModel *invoiceAmountViewModel = [[JDISVSettlementAmountFloorInvoiceCellViewModel alloc] init];
        NSDictionary *otherParams = allOriginData[@"data"][@"otherParams"];
        [invoiceAmountViewModel updateWithData:invoiceAmountModel originInvoiceData:otherParams[@"originInvoiceListData"] commonModel:commonModel];
        [amountSetions addObject:invoiceAmountViewModel];
    }
    
    //付尾款 合计
    if (ISPresell && ISFirstMoenyFlag == false) {
        sumMoneyModel = [[JDISVSumMoneyCellViewModel alloc]init];
        [sumMoneyModel updateWithData:allOriginData forType:0];
        [amountSetions addObject:sumMoneyModel];
    }
    
    JDISVSettlementAmountFloorRoundCellViewModel *bottomViewModel = [[JDISVSettlementAmountFloorRoundCellViewModel alloc] initWithCellType:JDISVSettlementAmountFloorRoundCellTypeR2];
    [amountSetions addObject:bottomViewModel];
    
    self.floorViewModels = [NSArray arrayWithArray:amountSetions];
}
@end
