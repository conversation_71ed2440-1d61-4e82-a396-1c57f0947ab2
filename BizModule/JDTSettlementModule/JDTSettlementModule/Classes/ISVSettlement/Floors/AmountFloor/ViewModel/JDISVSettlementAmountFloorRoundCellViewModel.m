//
//  JDISVSettlementAmountFloorRoundCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorRoundCellViewModel.h"

@implementation JDISVSettlementAmountFloorRoundCellViewModel
- (instancetype)initWithCellType:(JDISVSettlementAmountFloorRoundCellType)cellType
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorRoundCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorRoundCell";
        self.height = 12.f;
        _cellType = cellType;
       
        self.margin = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
        if (_cellType == JDISVSettlementAmountFloorRoundCellTypeR1) {
            _radius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"];
        } else {
            _radius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R2"];
        }
    }
    return self;
}
@end
