//
//  JDISVSettlementAmountFloorDiscountCodeCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/3/20.
//

#import "JDISVSettlementAmountFloorDiscountCodeCellViewModel.h"
#import "NSAttributedString+JDISVSettlementAmount.h"
#import "JDISVSettlementPub.h"
#import "JDISVSettlementAmoutFloorDiscountCodeModel.h"
#import "JDISVSettlementAmountFloorNetService.h"

typedef NS_ENUM(NSUInteger, JDISVSettlementAmountFloorDiscountCodeCellUploadType) {
    JDISVSettlementAmountFloorDiscountCodeCellUploadTypeNormal = 0, /**< 展示去选择 */
    JDISVSettlementAmountFloorDiscountCodeCellUploadTypeUnselected = 1,/**< 展示不可选择>*/
    JDISVSettlementAmountFloorDiscountCodeCellUploadTypeSelected = 2,/**<展示已选择的优惠码优惠的金额>*/
};

@interface JDISVSettlementAmountFloorDiscountCodeCellViewModel ()
@property (nonatomic, strong) JDISVSettlementAmoutFloorDiscountCodeModel *discountCodeModel;

@end

@implementation JDISVSettlementAmountFloorDiscountCodeCellViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.registerType = JDISVSettlementAmountFloorBaseCellViewModelRegisterTypeClass;
        self.cellName = @"JDISVSettlementAmountFloorDisocuntCodeCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorDisocuntCodeCell";
        self.height = 38.f;
        self.discountCodeTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"settlement_discount_code_title") colorKey:@"#C7" fontKey:@"#T7"];
        //默认展示为去选择
        [self uploadCellStatus:JDISVSettlementAmountFloorDiscountCodeCellUploadTypeNormal discount:@""];
        self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCantUse;
    }
    return self;
}

- (void)updateWithData:(id)data featureExt:(NSDictionary *)featureExt{
    if ([data isKindOfClass:JDISVSettlementAmoutFloorDiscountCodeModel.class]) {
        self.discountCodeModel = (JDISVSettlementAmoutFloorDiscountCodeModel *)data;
        if (_discountCodeModel.couponDiscountBasic) {//优惠码模块是否有返回值
            if (_discountCodeModel.couponCodeFlag && _discountCodeModel.couponCodeDiscount.floatValue> 0.01) {//已经选择了优惠码，展示优惠价格
                [self uploadCellStatus:JDISVSettlementAmountFloorDiscountCodeCellUploadTypeSelected discount:_discountCodeModel.couponCodeDiscount];
                self.codeUserStatus = JDISVSettlementCouponCodeUseStatusUsed;
                //缓存当前订单使用的优惠码信息
                [JDISVSettlementAmountFloorNetService sharedService].submitUseCouponCode = _discountCodeModel.couponCode;
            } else {//返回现实选择了优惠券
                if (_discountCodeModel.isUsedCoupon && [_discountCodeModel.discountAmount jdcd_validateString]) {//确定选择了优惠券，展示不可同选，不可点击
                    [self uploadCellStatus:JDISVSettlementAmountFloorDiscountCodeCellUploadTypeUnselected discount:@""];
                    self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCantUse;
                }else{//未确定是选择了优惠券，展示去选择
                    [self uploadCellStatus:JDISVSettlementAmountFloorDiscountCodeCellUploadTypeNormal discount:@""];
                    if (KASettlementAmountFloorNetService.isCheckoutPageFirstLoadData == 1){//自动使用逻辑，只有第一次进入时，未使用优惠券的情况下自动使用
                        self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCanUse;
                    }else{
                        self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCantUse;
                    }
//                    self.codeUserStatus =  ? JDISVSettlementCouponCodeUseStatusCanUse : JDISVSettlementCouponCodeUseStatusCantUse;
                }
            }
        }else{
            //容错判断 是否有存在的必要，需要正式数据确定
            if (_discountCodeModel.isUsedCoupon && [_discountCodeModel.discountAmount jdcd_validateString]) {//确定选择了优惠券，展示不可同选，不可点击
                [self uploadCellStatus:JDISVSettlementAmountFloorDiscountCodeCellUploadTypeUnselected discount:@""];
                self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCantUse;
            }else{//未确定是选择了优惠券，展示去选择
                [self uploadCellStatus:JDISVSettlementAmountFloorDiscountCodeCellUploadTypeNormal discount:@""];
                if (KASettlementAmountFloorNetService.isCheckoutPageFirstLoadData == 1){//自动使用逻辑，只有第一次进入时，且使用优惠券的情况下自动使用
                    self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCanUse;
                }else{
                    self.codeUserStatus = JDISVSettlementCouponCodeUseStatusCantUse;
                }
            }
        }
        
        CGSize titleSize = [self.discountCodeTitleAttributedString.string jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(300.f, MAXFLOAT) lineBreakMode:NSLineBreakByWordWrapping];
        CGSize valueSize = [self.discountCodeLabelAttributedString.string jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36 - titleSize.width - 25.f, MAXFLOAT) lineBreakMode:NSLineBreakByWordWrapping];
        self.height = MAX(titleSize.height, valueSize.height) + 18.f;
    } else {
        self.height = 0;
    }
}


/// 配置Cell展示
/// - Parameters:
///   - type: 展示类型
///   - discountStr:  优惠金额（type ==JDISVSettlementAmountFloorDiscountCodeCellUploadTypeSelected 有效）
- (void)uploadCellStatus:(JDISVSettlementAmountFloorDiscountCodeCellUploadType)type discount:(NSString *)discountStr{
    if (type == JDISVSettlementAmountFloorDiscountCodeCellUploadTypeNormal) {
        // 展示文字： 去选择
        // 箭头： 显示
        // 事件： 响应
        self.discountCodeLabelAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_coupon_empty") colorKey:@"#C7" fontKey:@"#T7"];
        self.arrowIconStatus = YES;
        self.userActionStatus = YES;
    }else if (type == JDISVSettlementAmountFloorDiscountCodeCellUploadTypeUnselected) {
        // 展示文字： 不可与优惠券同时使用
        // 箭头： 不展示
        // 事件： 不相应
        self.discountCodeLabelAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"settlement_discount_code_unselected") colorKey:@"#C5" fontKey:@"#T7"];
        self.arrowIconStatus = NO;
        self.userActionStatus = NO;
    }else if (type == JDISVSettlementAmountFloorDiscountCodeCellUploadTypeSelected){
        // 展示文字： 优惠金额
        // 箭头： 展示
        // 事件： 响应
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
        [priceAttributedString KA_renderWithPriceStr:discountStr type:KAPriceTypeP4 colorType:@"#C9"];
        NSMutableAttributedString *discountAttributedString = [[NSMutableAttributedString alloc] initWithString:@"-" attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [discountAttributedString appendAttributedString:[priceAttributedString copy]];
        self.discountCodeLabelAttributedString = [discountAttributedString copy];
        self.arrowIconStatus = YES;
        self.userActionStatus = YES;
    }
}

@end
