//
//  JDISVSettlementAmountFloorSectionViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorBaseSectionViewModel.h"

@class JDISVFloorCommonModel;

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementAmountFloorSectionViewModel : JDISVSettlementAmountFloorBaseSectionViewModel
/// 配置子楼层ViewModel
/// @param uuidKey Key UUId
/// @param floorKeys 楼层Key
/// @param floorDatas 楼层数据
/// @param featureExt 扩展字段
- (void)updateSectionWithUUIDKey:(NSString *)uuidKey
                       floorKeys:(NSArray *)floorKeys
                    allFloorData:(NSArray *)floorDatas
                   allOriginData:(NSDictionary *)allOriginData
                     commonModel:(JDISVFloorCommonModel *)commonModel
                      featureExt:(NSDictionary *)featureExt;

/// 是否立即使用缓存优惠码
@property (assign,nonatomic) BOOL autoUseCasheCouponCode;
@end

NS_ASSUME_NONNULL_END
