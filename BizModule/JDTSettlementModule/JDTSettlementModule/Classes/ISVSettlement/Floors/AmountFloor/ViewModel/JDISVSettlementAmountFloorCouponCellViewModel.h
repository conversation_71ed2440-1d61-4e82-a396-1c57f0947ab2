//
//  JDISVSettlementAmountFloorCouponCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementAmountFloorCouponModelUpdateType) {
    JDISVSettlementAmountFloorCouponModelUpdateTypeMain = 0, /**< 主接口 */
};

@interface JDISVSettlementAmountFloorCouponCellViewModel : JDISVSettlementAmountFloorBaseCellViewModel
@property (nonatomic, copy) NSAttributedString *couponTitleAttributedString;
@property (nonatomic, copy) NSAttributedString *couponInfoLabelAttributedString;
@property (nonatomic, strong) UIImage *rightArrowImg;
@property (nonatomic, strong) UIImage *avalableTagImg;
@property (nonatomic, assign) CGFloat availableTagViewWidth;
@property (nonatomic, assign) BOOL userActionStatus;//是否响应用户的点击事件 默认YES
@property (nonatomic, assign) CGFloat infoTrailing;

- (void)updateWithData:(id)data featureExt:(NSDictionary *)featureExt;
@end

NS_ASSUME_NONNULL_END
