//
//  JDISVSettlementAmountFloorTariffCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/6/12.
//

#import "JDISVSettlementAmountFloorTariffCellViewModel.h"
#import "NSAttributedString+JDISVSettlementAmount.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

@implementation JDISVSettlementAmountFloorTariffCellViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.registerType = JDISVSettlementAmountFloorBaseCellViewModelRegisterTypeClass;
        self.cellName = @"JDISVSettlementAmountFloorTariffCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorTariffCell";
        self.height = 38.f;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type{
    
    if (data && [data isKindOfClass:[JDISVSettlementAmoutFloorDefaultTaxModel class]]){
        JDISVSettlementAmoutFloorDefaultTaxModel *model = data;
        self.tariffTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"settlement_tariff_title") colorKey:@"#C7" fontKey:@"#T7"];
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
        if([model.taxAmount isEqualToString:@"0"]){
            NSString *priceTag = [PlatformService getPriceTag];
            priceAttributedString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ 0", priceTag] attributes:@{
                NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
                NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7" weight:UIFontWeightMedium]}];
        }else{
            [priceAttributedString KA_renderWithPriceStr:[NSString stringWithFormat:@"%@%@",[NSString getJDCDPriceTag],model.taxAmount] type:KAPriceTypeP4 colorType:@"#C7"];
        }
        NSMutableAttributedString *discountAttributedString = [[NSMutableAttributedString alloc] initWithString:@"+" attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [discountAttributedString appendAttributedString:[priceAttributedString copy]];
        self.tariffLabelAttributedString = [discountAttributedString copy];
        
        self.ruleTip = model.ruleTip;
        
        if (ISPresell && !ISFirstMoenyFlag) {
            //预售 付尾款
            if (model.taxAmount.doubleValue > 0) {
                self.isShowRuleTip = YES;
            } else {
                //不展示关税提示语
                self.isShowRuleTip = NO;
            }
        } else if (self.amount.doubleValue/1.15 < 1000 || ![model.ruleTip jdcd_validateString]){
            //不展示关税提示语
            self.isShowRuleTip = NO;
        } else {
            self.isShowRuleTip = YES;
        }
    }else{
        self.height = 0;
    }
}


@end
