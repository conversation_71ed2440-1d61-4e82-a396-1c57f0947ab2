//
//  JDISVSettlementAmountFloorCODCellViewModel.m
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu<PERSON> on 2023/6/12.
//

#import "JDISVSettlementAmountFloorCODCellViewModel.h"
#import "NSAttributedString+JDISVSettlementAmount.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@implementation JDISVSettlementAmountFloorCODCellViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.registerType = JDISVSettlementAmountFloorBaseCellViewModelRegisterTypeClass;
        self.cellName = @"JDISVSettlementAmountFloorCODCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorCODCell";
        self.height = 38.f;
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type{
    if (data && [data isKindOfClass:[JDISVSettlementAmoutFloorDefaultTaxModel class]]){
        JDISVSettlementAmoutFloorDefaultTaxModel *model = data;
        self.codTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"settlement_cod_title") colorKey:@"#C7" fontKey:@"#T7"];
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
        [priceAttributedString KA_renderWithPriceStr:[NSString stringWithFormat:@"%@%@",[NSString getJDCDPriceTag],model.taxAmount] type:KAPriceTypeP4 colorType:@"#C7"];
        
        NSMutableAttributedString *discountAttributedString = [[NSMutableAttributedString alloc] initWithString:@"+" attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [discountAttributedString appendAttributedString:[priceAttributedString copy]];
        self.codLabelAttributedString = [discountAttributedString copy];
    }else{
        self.height = 0;
    }
}


@end
