//
//  JDISVSettlementAmountFloorCouponCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorCouponCellViewModel.h"
#import "JDISVSettlementAmountFloorCouponModel.h"

#import "NSAttributedString+JDISVSettlementAmount.h"
#import "JDISVSettlementPub.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@interface JDISVSettlementAmountFloorCouponCellViewModel()
@property (nonatomic, strong) JDISVSettlementAmountFloorCouponModel *couponModel;
@end

@implementation JDISVSettlementAmountFloorCouponCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorCouponCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorCouponCell";
        self.height = 38.f;
        self.infoTrailing = 34;
        self.couponTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_coupon_list_title") colorKey:@"#C7" fontKey:@"#T7"];
        self.userActionStatus = YES;
    }
    return self;
}

- (void)updateWithData:(id)data featureExt:(NSDictionary *)featureExt {
    
    // 开关配置
    BOOL couponFeatureFlag = ([featureExt objectForKey:@"couponFeature"] && [[featureExt objectForKey:@"couponFeature"] isEqualToNumber:@1]) ? YES : NO;
    if (couponFeatureFlag == NO) {
        self.height = 0;
        return;
    }
    
    
    if ([data isKindOfClass:JDISVSettlementAmountFloorCouponModel.class]) {
        self.couponModel = (JDISVSettlementAmountFloorCouponModel *)data;
        if ([_couponModel.availableNum integerValue] > 0 && _couponModel.isUsedCoupon) {
            // 有可用优惠券 且没有选择
            NSString *canUseMsg = [NSString stringWithFormat:SettlementLan(@"checkout_coupon_available"), [_couponModel.availableNum integerValue]];
            self.availableTagViewWidth = ceil([canUseMsg jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 18.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"] maxNumberOfLines:1].width) + 4.f*2;
            self.avalableTagImg = [self availableTagWithInfo:canUseMsg Frame:CGRectMake(0, 0, _availableTagViewWidth, 18.f)];
        } else {
            self.avalableTagImg = nil;
            self.availableTagViewWidth = 0;
        }
        if(_couponModel.couponCodeDiscount.floatValue > 0 ){
            self.couponInfoLabelAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"settlement_coupon_unselected") colorKey:@"#C5" fontKey:@"#T7"];
            self.rightArrowImg = nil;
            self.userActionStatus = NO;
            self.infoTrailing = 18;
        }else{
            self.rightArrowImg = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
            self.infoTrailing = 34;
            if (_couponModel.isUsedCoupon && [_couponModel.discountAmount jdcd_validateString]) {
                NSString *discountStr = @"0.00";
                if ([_couponModel.discountAmount jdcd_validateString]) {
                    discountStr = [_couponModel.discountAmount copy];
                }
                
                NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
                [priceAttributedString KA_renderWithPriceStr:discountStr type:KAPriceTypeP4 colorType:@"#C9"];
                
                NSMutableAttributedString *discountAttributedString = [[NSMutableAttributedString alloc] initWithString:@"-" attributes:@{
                    NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"],
                    NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
                }];
                [discountAttributedString appendAttributedString:[priceAttributedString copy]];
                
                self.couponInfoLabelAttributedString = [discountAttributedString copy];
            } else {
                // 去选择
                self.couponInfoLabelAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_coupon_empty") colorKey:@"#C7" fontKey:@"#T7"];
            }
        }
        CGSize titleSize = [self.couponTitleAttributedString.string jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(300.f, MAXFLOAT) lineBreakMode:NSLineBreakByWordWrapping];
        CGSize valueSize = [self.couponInfoLabelAttributedString.string jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake([UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36 - titleSize.width - 55.f, MAXFLOAT) lineBreakMode:NSLineBreakByWordWrapping];
        self.height = MAX(titleSize.height, valueSize.height) + 18.f;
    } else {
        self.height = 0;
    }
}


# pragma mark - Private
- (UIImage *)availableTagWithInfo:(NSString *)info Frame:(CGRect)frame {
    UIButton *defaultTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [defaultTagBtn setFrame:frame];
    [defaultTagBtn setTitle:info forState:UIControlStateNormal];
    [defaultTagBtn renderL3WithCornerRadius:2.f];
    
    return [self getImageFromView:defaultTagBtn];
}


- (UIImage *)getImageFromView:(UIView *)view {
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, [UIScreen mainScreen].scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}
@end
