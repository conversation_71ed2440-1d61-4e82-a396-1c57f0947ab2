//
//  JDISVSettlementAmountFloorDiscountCodeCellViewModel.h
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/3/20.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

/**<自动使用逻辑
 A：缓存中没有优惠码：不自动使用优惠码
 B：缓存中有优惠码：
    B.1.第一次进入没有自动使用优惠券：则自动使用优惠码，使用了一次优惠码后，后续不在自动使用优惠码
    B.2.第一次进入时，自动使用了优惠券：取消优惠券时，不自动使用优惠码
 >*/
typedef NS_ENUM(NSInteger,JDISVSettlementCouponCodeUseStatus) {
    JDISVSettlementCouponCodeUseStatusUsed = 1,
    JDISVSettlementCouponCodeUseStatusCantUse = 2,
    JDISVSettlementCouponCodeUseStatusCanUse = 3,
};

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementAmountFloorDiscountCodeCellViewModel : JDISVSettlementAmountFloorBaseCellViewModel

@property (nonatomic, copy) NSAttributedString *discountCodeTitleAttributedString;//标题文本
@property (nonatomic, copy) NSAttributedString *discountCodeLabelAttributedString;//值文本
@property (nonatomic, assign) BOOL arrowIconStatus;//箭头图片展示状态 YES展示 NO 隐藏
@property (nonatomic, assign) BOOL userActionStatus;//是否响应用户的点击事件 默认YES

- (void)updateWithData:(id)data featureExt:(NSDictionary *)featureExt;


//优惠码使用状态  1：已使用优惠码 2:已使用优惠券，优惠码不可用 3，可使用优惠码
@property (assign,nonatomic) JDISVSettlementCouponCodeUseStatus codeUserStatus;

@end

NS_ASSUME_NONNULL_END
