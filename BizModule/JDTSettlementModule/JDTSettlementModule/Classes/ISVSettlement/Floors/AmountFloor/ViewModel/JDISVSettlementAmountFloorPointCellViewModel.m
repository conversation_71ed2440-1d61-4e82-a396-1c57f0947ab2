//
//  JDISVSettlementAmountFloorPointCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorPointCellViewModel.h"

#import "JDISVSettlementAmountFloorPointItemModel.h"
#import "JDISVSettlementAmountFloorPointModel.h"

#import "NSAttributedString+JDISVSettlementAmount.h"

//#define Rate 10

@interface JDISVSettlementAmountFloorPointCellViewModel ()

@property (assign,nonatomic) CGFloat pointCountLabelHeight;
@property (assign,nonatomic) CGFloat pointTitleLabelHeight;

@end

@implementation JDISVSettlementAmountFloorPointCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorPointCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorPointCell";
        self.height = 38.f;
        
        NSString *title = SettlementLan(@"checkout_points_label") ;
        if (ISPresell == 1) {
            title = SettlementLan(@"checkout_points_list_title") ;
        }
        self.pointTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:title colorKey:@"#C7" fontKey:@"#T7"];
        self.pointTitleLabelWidth = ceil([_pointTitleAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
        self.pointTitleLabelHeight = ceil([_pointTitleAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].height);
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if (type == JDISVSettlementAmountFloorPointCellViewModelUpdateTypeMainInterface) {
        // 主接口更新
        if ([data isKindOfClass:JDISVSettlementAmountFloorPointModel.class]) {
            self.model = (JDISVSettlementAmountFloorPointModel *)data;
            [self updateMainInterfaceWith:_model];
        }
    }
    
}

- (void)updateMainInterfaceWith:(JDISVSettlementAmountFloorPointModel *)model {
    // 是否展示积分
    [self updateMainInterfacePointsCountInfoWith:model];
    // 右侧提示图标
    [self updateMainInterfacePointInfoImageWith:model];
    // info信息
    [self updateMainInterfacePointsTipsInfoWith:model];
    
}

// 更新积分相关信息
- (void)updateMainInterfacePointsCountInfoWith:(JDISVSettlementAmountFloorPointModel *)model {
    if (model.totalCount.integerValue >= 0) {
        // 展示积分数
//
        NSString *totalCount = [NSString stringWithFormat:@"%ld",model.totalCount.integerValue];
        self.pointCountAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:[NSString stringWithFormat:SettlementLan(@"checkout_points_with_points") , totalCount] colorKey:@"#C5" fontKey:@"#T7"];
        
        self.pointCountLabelWidth = ceil([_pointCountAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
        self.pointCountLabelHeight = ceil([_pointCountAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].height);
    } else {
        // 不展示积分数
        self.pointCountAttributedString = nil;
        self.pointCountLabelWidth = 0;
        self.pointCountLabelHeight = 0;
    }
    
}

// 更新右侧图标相关信息
- (void)updateMainInterfacePointInfoImageWith:(JDISVSettlementAmountFloorPointModel *)model {
    // 是否可以使用积分
    if (model.canUseCount.integerValue > 0) {
        // 可以使用积分
        self.infoType = JDISVSettlementAmountFloorPointCellViewModelInfoActionSelectPoints;
        
        self.imageSize = CGSizeMake(4.f + 12.f + 4.f, 20.f);
        self.imageTrailing = 18.f - 4.f;
        self.infoImage = [UIImage ka_iconWithName:JDIF_ICON_SYSTEM_EXPLAIN imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
        
    } else {
        // 不可以使用积分
        
        self.infoType = JDISVSettlementAmountFloorPointCellViewModelInfoActionShowInfo;
        
        self.imageSize = CGSizeMake(7.f + 13.f + 7.f, 20.f);
        self.imageTrailing = 18.f - 7.f;
        self.infoImage = [UIImage ka_iconWithName:JDIF_ICON_SYSTEM_EXPLAIN imageSize:CGSizeMake(15.f, 15.f)  color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    }
    
    if (model.useRuleTitle && model.useRuleTitle.count > 0) {
        self.usePointRuleTitle = [model.useRuleTitle.firstObject copy];
    } else {
        self.usePointRuleTitle = @"";
    }
    if (model.useRuleContent && model.useRuleContent.count > 0) {
        self.usePointRuleContent = [model.useRuleContent.firstObject copy];
    } else {
        self.usePointRuleContent = @"";
    }
}

// 更新提示信息
- (void)updateMainInterfacePointsTipsInfoWith:(JDISVSettlementAmountFloorPointModel *)model {
    // 是否可以使用积分
    if (model.canUseCount.integerValue > 0) {
        // 积分可以用
        [self updateMainInterfacePointsTipsCanUseInfoWith:model];
            
    } else {
        // 积分不可用
        [self updateMainInterfacePointsTipsDisableInfoWith:model];
        
    }
    
//    self.pointCountLabelWidth
    if ([self.pointInfoAttributedString.string jdcd_validateString]) {
        CGFloat maxWidth = [UIScreen mainScreen].bounds.size.width - ([[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"] * 2.f) - 36.f - 20.f - ((MAX(self.pointTitleLabelWidth, self.pointCountLabelWidth)) < 90.f ? 90.f : (MAX(self.pointTitleLabelWidth, self.pointCountLabelWidth))) - 4 - 10.f;
        CGSize pointInfoSize = [_pointInfoAttributedString.string jdcd_sizeWithContainer:CGSizeMake(maxWidth, MAXFLOAT) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:0];
        self.pointInfoLabelWidth = ceil(pointInfoSize.width);
//        if (pointInfoSize.height > 20.f){
//            self.height = pointInfoSize.height + 18.f;
//        }
        
        self.height = MAX(pointInfoSize.height > 20.f ? pointInfoSize.height + 18.f : 20.f + 18.f, self.pointCountLabelHeight + self.pointTitleLabelHeight + 18.f);
        
    } else {
        self.pointInfoLabelWidth = 0;
        self.height = self.pointCountLabelHeight + self.pointTitleLabelHeight + 18.f;
    }
}

// 更新积分可用提示信息
- (void)updateMainInterfacePointsTipsCanUseInfoWith:(JDISVSettlementAmountFloorPointModel *)model {
    // 可以使用积分
    if (model.usedFlag) {
        // 使用过积分显示-￥XXX
        
        NSString *discountStr = @"0.00";
        if ([model.discount jdcd_validateString]) {
            discountStr = [model.discount copy];
        }
        
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] init];
        [priceAttributedString KA_renderWithPriceStr:discountStr type:KAPriceTypeP4 colorType:@"#C9"];
        
        NSMutableAttributedString *discountAttributedString = [[NSMutableAttributedString alloc] initWithString:@"-" attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [discountAttributedString appendAttributedString:[priceAttributedString copy]];
        self.pointInfoAttributedString = [discountAttributedString copy];
    } else {
        // 未用过积分显示 - 去使用
        
//        if ([model.showMsg jdcd_validateString]) {
        self.pointInfoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:model.showMsg ? : SettlementLan(@"checkout_points_default_choose")  colorKey:@"#C7" fontKey:@"#T7"];
//        } else {
//            self.pointInfoAttributedString = nil;
//        }
    }
}

// 更新积分不可用提示信息
- (void)updateMainInterfacePointsTipsDisableInfoWith:(JDISVSettlementAmountFloorPointModel *)model {
//    if ([model.showMsg jdcd_validateString]) {
    self.pointInfoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:model.showMsg ? : SettlementLan(@"checkout_points_forbid")  colorKey:@"#C5" fontKey:@"#T7"];
        self.pointInfoLabelWidth = ceil([_pointInfoAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
//    } else {
//        self.pointInfoAttributedString = nil;
//        self.pointInfoLabelWidth = 0;
//    }
}
@end
