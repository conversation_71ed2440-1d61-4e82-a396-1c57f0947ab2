//
//  JDISVSettlementAmountFloorFreightCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorFreightCellViewModel.h"

#import "NSAttributedString+JDISVSettlementAmount.h"

@interface JDISVSettlementAmountFloorFreightCellViewModel()

@end

@implementation JDISVSettlementAmountFloorFreightCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorFreightCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorFreightCell";
        self.height = 38.f;
        self.freightTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_summary_freight_label") colorKey:@"#C7" fontKey:@"#T7"];
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if ([data isKindOfClass:JDISVSettlementAmountFloorFreightModel.class]) {
        self.model = (JDISVSettlementAmountFloorFreightModel *)data;
//        if ([_model.freightLabel jdcd_validateString]) {
//            self.freightTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:_model.freightLabel colorKey:@"#C7" fontKey:@"#T7"];
//        } else {
//            self.freightTitleAttributedString = nil;
//        }
        NSString *freight = @"";
        if ([_model.originalTotalFreight jdcd_validateString]) {
            freight = [NSString stringWithFormat:@"%@", _model.originalTotalFreight];
        } 
        else if ([_model.totalFreight jdcd_validateString]) {
            freight = [NSString stringWithFormat:@"%@", _model.totalFreight];
        }
        else if ([_model.totalFreightMoney jdcd_validateString]) {
            freight = [NSString stringWithFormat:@"%@", _model.totalFreightMoney];
        }
        
        NSMutableAttributedString *freightPrice = [[NSMutableAttributedString alloc] init];
        [freightPrice KA_renderWithPriceStr:freight type:KAPriceTypeP4 colorType:@"#C7"];
        
        NSString *operatorStr = @"+";
        NSMutableAttributedString *freightTmpAttributedString = [[NSMutableAttributedString alloc] initWithString:operatorStr attributes:@{
            NSForegroundColorAttributeName : [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"],
            NSFontAttributeName : [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"]
        }];
        [freightTmpAttributedString appendAttributedString:[freightPrice copy]];
        self.freightAmountAttributedString = [freightTmpAttributedString copy];
    }
}
@end
