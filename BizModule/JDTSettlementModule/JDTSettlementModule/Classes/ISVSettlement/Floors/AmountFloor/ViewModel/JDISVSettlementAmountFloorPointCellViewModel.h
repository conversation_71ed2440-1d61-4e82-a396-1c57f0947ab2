//
//  JDISVSettlementAmountFloorPointCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementAmountFloorPointCellViewModelUpdateType) {
    JDISVSettlementAmountFloorPointCellViewModelUpdateTypeMainInterface = 0, /**< 主接口更新 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementAmountFloorPointCellViewModelInfoAction) {
    JDISVSettlementAmountFloorPointCellViewModelInfoActionSelectPoints = 0, /**< 选择积分 */
    JDISVSettlementAmountFloorPointCellViewModelInfoActionShowInfo /**< 展示积分不可用信息 */
};

@class JDISVSettlementAmountFloorPointModel;

@interface JDISVSettlementAmountFloorPointCellViewModel : JDISVSettlementAmountFloorBaseCellViewModel
@property (nonatomic, copy) NSAttributedString *pointTitleAttributedString;
@property (nonatomic, assign) CGFloat pointTitleLabelWidth;

@property (nonatomic, copy, nullable) NSAttributedString *pointCountAttributedString;
@property (nonatomic, assign) CGFloat pointCountLabelWidth;

@property (nonatomic, copy) NSAttributedString *pointInfoAttributedString;
@property (nonatomic, assign) CGFloat pointInfoLabelWidth;

@property (nonatomic, strong) UIImage *infoImage;
@property (nonatomic, assign) CGSize imageSize;
@property (nonatomic, assign) CGFloat imageTrailing;

@property (nonatomic, copy) NSString *usePointRuleTitle;
@property (nonatomic, copy) NSString *usePointRuleContent;

@property (nonatomic, assign) JDISVSettlementAmountFloorPointCellViewModelInfoAction infoType;

@property (nonatomic, strong) JDISVSettlementAmountFloorPointModel *model;
@end

NS_ASSUME_NONNULL_END
