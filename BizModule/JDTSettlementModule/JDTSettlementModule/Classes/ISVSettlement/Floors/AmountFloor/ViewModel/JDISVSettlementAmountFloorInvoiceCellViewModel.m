//
//  JDISVSettlementAmountFloorPointCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorInvoiceCellViewModel.h"

#import "JDISVSettlementAmountFloorPointItemModel.h"
#import "JDISVSettlementAmountFloorPointModel.h"
//#import "JDISVSettlementAmountFloorInvoiceModel.h"
#import "NSAttributedString+JDISVSettlementAmount.h"
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>

@interface JDISVSettlementAmountFloorInvoiceCellViewModel ()

@end

@implementation JDISVSettlementAmountFloorInvoiceCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cellName = @"JDISVSettlementAmountFloorInvoiceCell";
        self.cellIdentifier = @"JDISVSettlementAmountFloorInvoiceCell";
        self.height = 38.f;
        
        NSString *title = SettlementLan(@"checkout_invoice_label") ;
        
        self.pointTitleAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:title colorKey:@"#C7" fontKey:@"#T7"];
        self.pointTitleLabelWidth = ceil([_pointTitleAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
    }
    return self;
}

- (void)updateWithData:(id)data originInvoiceData:(NSArray *)originInvoiceData commonModel:(JDISVFloorCommonModel *)commonModel{
    
    self.imageSize = CGSizeMake(4.f + 12.f + 4.f, 20.f);
    self.imageTrailing = 18.f - 4.f;
    self.infoImage = [UIImage ka_iconWithName:JDIF_ICON_ARROW_RIGHT_SMALL imageSize:CGSizeMake(12.f, 12.f) color:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"]];
    
    NSString *showMsg = SettlementLan(@"checkout_invoice_type_personal") ;
    if (originInvoiceData && [originInvoiceData isKindOfClass:[NSArray class]]) {
        for (NSDictionary *invoiceDic in originInvoiceData){
            if (invoiceDic && [invoiceDic isKindOfClass:[NSDictionary class]]) {
                NSNumber *isSelected = (NSNumber *)invoiceDic[@"isSelected"];
                if (isSelected.boolValue) {
                    //找到选中的发票
                    if (invoiceDic[@"invoiceTitleType"]) {
                        NSInteger invoiceTitleType =((NSNumber *)invoiceDic[@"invoiceTitleType"]).integerValue;
                        if (invoiceTitleType == 0) {
                            showMsg = SettlementLan(@"checkout_invoice_type_personal") ;
                        }else if (invoiceTitleType == 1) {
                            showMsg = SettlementLan(@"checkout_invoice_type_company") ;
                        }
                        [commonModel.commonData setObject:invoiceDic forKey:@"selectedInvoiceData"];
                    }
                    break;
                }
            }
        }
    }
    
    self.pointInfoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:showMsg colorKey:@"#C7" fontKey:@"#T7"];
    self.pointInfoLabelWidth = ceil([_pointInfoAttributedString.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, 20.f) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] maxNumberOfLines:1].width);
}


@end
