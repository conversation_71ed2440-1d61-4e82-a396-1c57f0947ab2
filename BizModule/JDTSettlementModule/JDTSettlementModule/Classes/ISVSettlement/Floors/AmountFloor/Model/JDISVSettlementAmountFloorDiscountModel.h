//
//  JDISVSettlementAmountFloorDiscountModel.h
//  JDISVSettlementModule
//
//  Created by ext.zhangchen41 on 2024/9/28.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementAmountFloorDiscountModel : NSObject

/*
 模型名称:免运费金额模型
 模型命名空间:core.trade-FLR#balance.freight-M#discountAmountFloor
 模型uuid:discountAmountFloor
 */

//if (data[@"freightFullDiscountAmount"]) {//免运费楼层
//    NSDictionary *floorDic = @{};
//    NSString *uuid = @"discountAmountFloor";
//    floorDic = @{
//        @"info" : @{
//          @"C-M#discountAmountFloor&core" : @{
//            @"freightFullDiscountAmountLabel" : SettlementLan(@"settlement_title_shipping_fee_discount"),
//            @"freightFullDiscountAmount" : data[@"freightFullDiscountAmount"] ? : @""
//        },
//        @"uuid" : uuid
//      }
//    };
//    [floors addObject:floorDic];
//    [rootUUID addObject:uuid];
//}

// C-M#discountAmountFloor&core
@property (nonatomic, copy) NSString *freightFullDiscountAmountLabel;
@property (nonatomic, copy) NSString *freightFullDiscountAmount;
@property (nonatomic, copy) NSString *originalTotalFreight;
@property (nonatomic, copy) NSString *discountFreight;

@end

NS_ASSUME_NONNULL_END
