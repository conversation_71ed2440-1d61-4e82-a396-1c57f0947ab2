//
//  JDISVSettlementAmountFloorItemModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:通用金额模型
 模型命名空间:core.trade-FLR#balance.productAmount-M#defaultProductAmountFloor
 模型uuid:defaultProductAmountFloor
 */

@interface JDISVSettlementAmountFloorItemModel : NSObject
// C-M#productAmountFloor&basic
@property (nonatomic, copy) NSString *amount; /**< amount|factAmount(type==2) : 金额 */
@property (nonatomic, copy) NSString *amountInfo; /**< amountStr : 展示金额 */
@property (nonatomic, copy) NSString *floorType; /**< 楼层类型: 0:商品金额 1:立减 2:应付金额 */
@property (nonatomic, copy) NSString *amountTitle; /**< label : 金额标题 */
@property (nonatomic, copy) NSString *operatorSymbol; /**< operator:加减符号 */
@end

NS_ASSUME_NONNULL_END
