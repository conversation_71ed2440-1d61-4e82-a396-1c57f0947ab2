//
//  JDISVSettlementAmoutFloorDiscountCodeModel.h
//  JDISVSettlementModule
//
//  Created by ext.chenhongyu12 on 2023/3/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementAmoutFloorDiscountCodeModel : NSObject

//内部Key VCYBB2C-D#couponCode#basic
@property (strong,nonatomic) NSString *couponCode;//优惠码Code
@property (assign,nonatomic) BOOL couponCodeFlag;//#true使用了优惠码，false使用了优惠券
@property (strong,nonatomic) NSString *batchId;
@property (strong,nonatomic) NSString *activityId;
@property (strong,nonatomic) NSString * couponLimitInfo;
@property (assign,nonatomic) NSInteger couponType;
@property (assign,nonatomic) NSInteger couponStyle;
@property (strong,nonatomic) NSString* discount;
@property (strong,nonatomic) NSString* timeBegin;
@property (strong,nonatomic) NSString* timeEnd;
@property (assign,nonatomic) BOOL selected ;
//内部key C-M#homePageFloorCouponFloor&money
@property (strong,nonatomic) NSString *couponCodeDiscount;//优惠码优惠金额

@property (strong,nonatomic) NSDictionary *couponDiscountBasic;//用作判断是否使用了优惠码

@property (nonatomic) BOOL isUsedCoupon; /**< isUsedCoupon:是否使用了券 */
@property (nonatomic, copy) NSString *discountAmount; /**< discountAmount:优惠金额（运费+优惠券) */



@end

NS_ASSUME_NONNULL_END
