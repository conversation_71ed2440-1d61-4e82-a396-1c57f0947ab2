//
//  JDISVSettlementAmountFloorFreightModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:运费金额模型
 模型命名空间:core.trade-FLR#balance.freight-M#totalFreightFloor
 模型uuid:totalFreightFloor
 */

@interface JDISVSettlementAmountFloorFreightModel : NSObject
// C-M#totalFreightFloor&core
@property (nonatomic, copy) NSString *totalFreight;
@property (nonatomic, copy) NSString *freightLabel;
@property (nonatomic, copy) NSString *totalFreightMoney;
@property (nonatomic, copy) NSString *originalTotalFreight;
@property (nonatomic, copy) NSString *discountFreight;
@end

NS_ASSUME_NONNULL_END
