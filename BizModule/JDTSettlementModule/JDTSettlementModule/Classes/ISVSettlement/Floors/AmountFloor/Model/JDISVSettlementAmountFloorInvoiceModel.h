//
//  JDISVSettlementAmountFloorPointModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:积分模型
 模型命名空间:core.trade-FLR#balance.points-M#defaultPointsFloor
 模型uuid:defaultPointsFloor
 */
@interface JDISVSettlementAmountFloorInvoiceModel : NSObject


@property (nonatomic, copy) NSString *showMsg; /**< showMsg:展示信息(本单不能使用积分) */


//"id": 2,
//"invoiceType": 1,
//"invoiceTitleType": 1,
//"invoiceContent": "23",
//"taxationNum": "123",
//"invoiceTitleName": "tes2t",
//"userPin": "zhoukang6",
//"isSelected": 1
//@property (nonatomic, copy) NSString *invoiceId;
//@property (nonatomic, copy) NSString *invoiceType;
//@property (nonatomic, copy) NSString *invoiceTitleType;
//@property (nonatomic, copy) NSString *invoiceContent;
//@property (nonatomic, copy) NSString *taxationNum;
//@property (nonatomic, copy) NSString *invoiceTitleName;
//@property (nonatomic, copy) NSString *userPin;
//@property (nonatomic, assign) BOOL isSelected;


@end

NS_ASSUME_NONNULL_END
