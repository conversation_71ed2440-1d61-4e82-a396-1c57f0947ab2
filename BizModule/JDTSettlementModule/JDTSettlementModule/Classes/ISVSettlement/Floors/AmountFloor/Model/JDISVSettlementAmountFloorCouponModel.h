//
//  JDISVSettlementAmountFloorCouponModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JDISVSettlementAmountFloorCouponModel : NSObject

/*
 模型名称:优惠券金额模型
 模型命名空间:core.trade-FLR#balance.coupon-M#homePageFloorCouponFloor
 模型uuid:homePageFloorCouponFloor
 */
// 内部Key:C-M#homePageFloorCouponFloor&sign

@property (nonatomic, copy) NSString *useCouponMsg; /**< couponSign:优惠券标记文案(已选推荐组合) */
// 内部Key:C-M#homePageFloorCouponFloor&estimate
@property (nonatomic) BOOL isUsedCoupon; /**< isUsedCoupon:是否使用了券 */
@property (nonatomic) BOOL useBestCoupon; /**< useBestCoupon:智能选券-宜宾暂不支持 */
// 内部Key:C-M#homePageFloorCouponFloor&number
@property (nonatomic, strong) NSNumber *selectedCouponNum; /**< selectedCouponNum:用户已选优惠券数量 */
@property (nonatomic, strong) NSNumber *availableNum; /**< availableNum:可用优惠券数量 */
@property (nonatomic, strong) NSNumber *usedNum; /**< usedNum:当前优惠券使用数量 */
// 内部Key:C-M#homePageFloorCouponFloor&money
@property (nonatomic, copy) NSString *couponDiscount; /**< couponDiscount:优惠券优惠金额 */
@property (nonatomic, copy) NSString *discountAmount; /**< discountAmount:优惠金额（运费+优惠券) */
// 内部Key:VCYBB2C-D#couponCode#basic
@property (assign,nonatomic) BOOL couponCodeFlag;//#true使用了优惠码，false使用了优惠券
@property (strong,nonatomic) NSDictionary *couponDiscountBasic;//用作判断是否使用了优惠码

@property (nonatomic, strong) NSNumber *couponCodeDiscount;
@end

NS_ASSUME_NONNULL_END
