//
//  JDISVSettlementAmountFloorPointModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:积分模型
 模型命名空间:core.trade-FLR#balance.points-M#defaultPointsFloor
 模型uuid:defaultPointsFloor
 */
@interface JDISVSettlementAmountFloorPointModel : NSObject
// 内部Key:C-M#abstractPointsFloor&flag
@property (nonatomic) BOOL usedFlag; /**< usedFlag:本单是否使用过京豆 */
@property (nonatomic) BOOL showFlag; /**< showFlag:是否显示京豆 */
// 内部Key:C-M#abstractPointsFloor&useRule
@property (nonatomic, copy) NSArray<NSString *> *useRuleContent; /**< useRuleContent:使用规则内容文案 */
@property (nonatomic, copy) NSArray<NSString *> *useRuleTitle; /**< useRuleTitle:使用规则标题 */
// 内部Key:VCYBB2C-D#pointsDetail&msg
@property (nonatomic, copy) NSString *totalPoint; /**< total:总积分 -- 浮层 */
@property (nonatomic, copy) NSString *useRuleMsg; /**< useRuleMsg:使用规则信息(积分抵扣金额不能超过总价的50%)  */
//@property (nonatomic) BOOL canUsePoint; /**< canUsePoint:是否可以使用积分 */
@property (nonatomic, copy) NSString *showMsg; /**< showMsg:展示信息(本单不能使用积分) */
@property (nonatomic, copy) NSString *useLowLimit;/**< useLowLimit:最低使用积分限制 */
@property (nonatomic, copy) NSString *useHighLimit;/**< useHighLimit:最低使用积分限制 */
// C-M#abstractPointsFloor&count
@property (nonatomic, copy) NSString *canUseCount; /**< canUseCount:可以使用的数量 */
@property (nonatomic, copy) NSString *rate; /**< rate:积分折算比例 */
@property (nonatomic, copy) NSString *minUseUnit; /**< minUseUnit:最小使用的京豆数量单位（只在京豆新规则下） */
@property (nonatomic, copy) NSString *discount; /**< discount:减免金额 */
@property (nonatomic, copy) NSString *useCount; /**< useCount:使用数量 */
@property (nonatomic, copy) NSString *totalCount; /**< totalCount:总数量 -- 楼层 */
// C-D#msgDecorator&basic
@property (nonatomic, copy) NSString *showTotalMsg; /**< showTotalMsg: 总展示信息(共0积分，满1000积分可用) */
@property (nonatomic, copy) NSString *mostCanUsedMsg; /**< mostCanUsedMsg:最多使用展示信息(本单最多支持使用0积分) */
@property (nonatomic, copy) NSString *totalMsg; /**< totalMsg:总积分信息(共0积分|共5000京豆) */
@property (nonatomic, copy) NSString *disCountMsg; /**< disCountMsg:减免信息(抵¥30.00) */
@property (nonatomic, copy) NSString *canUseMsg; /**< canUseMsg:可使用信息(可用3000京豆，抵¥30.00可用3000京豆) */

// PartList
@property (nonatomic, copy) NSArray *pointItems;
@end

NS_ASSUME_NONNULL_END
