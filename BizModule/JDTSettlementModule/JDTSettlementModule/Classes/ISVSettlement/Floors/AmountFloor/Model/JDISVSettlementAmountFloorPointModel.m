//
//  JDISVSettlementAmountFloorPointModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import "JDISVSettlementAmountFloorPointModel.h"

@implementation JDISVSettlementAmountFloorPointModel
+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
        @"usedFlag": @"C-M#abstractPointsFloor&flag.usedFlag",
        @"showFlag": @"C-M#abstractPointsFloor&flag.showFlag",
        @"useRuleContent": @"C-M#abstractPointsFloor&useRule.useRuleContent",
        @"useRuleTitle": @"C-M#abstractPointsFloor&useRule.useRuleTitle",
        @"totalPoint": @"VCYBB2C-D#pointsDetail&msg.total",
        @"useRuleMsg": @"VCYBB2C-D#pointsDetail&msg.useRuleMsg",
//        @"canUsePoint": @"VCYBB2C-D#pointsDetail&msg.canUsePoint",
        @"showMsg": @"VCYBB2C-D#pointsDetail&msg.showMsg",
        @"useHighLimit": @"VCYBB2C-D#pointsDetail&msg.useHighLimit",
        @"useLowLimit": @"VCYBB2C-D#pointsDetail&msg.useLowLimit",
        @"canUseCount": @"C-M#abstractPointsFloor&count.canUseCount",
        @"rate": @"C-M#abstractPointsFloor&count.rate",
        @"minUseUnit": @"C-M#abstractPointsFloor&count.minUseUnit",
        @"discount": @"C-M#abstractPointsFloor&count.discount",
        @"useCount": @"C-M#abstractPointsFloor&count.useCount",
        @"totalCount": @"C-M#abstractPointsFloor&count.totalCount",
        @"showTotalMsg": @"C-D#msgDecorator&basic.showTotalMsg",
        @"mostCanUsedMsg": @"C-D#msgDecorator&basic.mostCanUsedMsg",
        @"totalMsg": @"C-D#msgDecorator&basic.totalMsg",
        @"disCountMsg": @"C-D#msgDecorator&basic.disCountMsg",
        @"canUseMsg": @"C-D#msgDecorator&basic.canUseMsg"
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"useRuleContent": @"NSString",
        @"useRuleTitle": @"NSString"
             
    };
}
@end
