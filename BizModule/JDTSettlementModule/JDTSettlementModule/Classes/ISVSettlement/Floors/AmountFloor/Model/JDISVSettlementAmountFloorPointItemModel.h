//
//  JDISVSettlementAmountFloorPointItemModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/*
 模型名称:积分条目模型(浮层使用)
 模型命名空间:cn_ybxt_b2c-FLR#balance.points-P#detailPart
 层级:defaultPointsFloor->PartList
 */
@interface JDISVSettlementAmountFloorPointItemModel : NSObject
// 内部Key:VCYBB2C-P#detailPart&basic
@property (nonatomic, copy) NSString *usePointCount; /**< partUsePointCount:使用积分总数 */
@property (nonatomic, copy) NSString *showMsg; /**< partShowMsg:展示文案 */
@property (nonatomic) BOOL selected; /**< partSelectFlag:选中标识 */
@property (nonatomic, copy) NSString *pointDiscount; /**< partPointDiscount:积分抵扣金额 */
@end

NS_ASSUME_NONNULL_END
