//
//  JDISVSettlementCouponController.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementCouponViewModel;
@class JDISVSettlementCouponItemCellViewModel;
@class RACSubject;

typedef void(^JDISVSettlementCouponControllerComfirmCallback)(void);
typedef void(^JDISVSettlementCouponControllerCloseCallback)(NSDictionary *param);

@interface JDISVSettlementCouponController : UIViewController
@property (nonatomic, copy) JDISVSettlementCouponControllerCloseCallback confirmCallback; /**< 点击确认按钮 浮层消失回调 */
@property (nonatomic, copy) JDISVSettlementCouponControllerCloseCallback refreshMainPageCallback; /**< 刷新结算页主接口回调*/
@property (nonatomic, copy) JDISVSettlementCouponControllerCloseCallback closeCallback;  /**< 点击关闭 浮层消失回调 */
@property (nonatomic, copy) JDISVSettlementCouponViewModel *viewModel;

@property (nonatomic, strong) RACSubject *delegate;
- (void)updateWithSource:(NSUInteger)source;
- (void)presaleEndPaymentUpdateWithCouponVos:(NSArray *)couponVos;//预售付尾款选券
- (void)reloadPresaleEndPaymentUpdateWithCouponVos:(NSArray *)couponVos;//刷新预售付尾款选券

- (void)updateTableView;
- (void)showError:(NSError *)error;
@end

NS_ASSUME_NONNULL_END
