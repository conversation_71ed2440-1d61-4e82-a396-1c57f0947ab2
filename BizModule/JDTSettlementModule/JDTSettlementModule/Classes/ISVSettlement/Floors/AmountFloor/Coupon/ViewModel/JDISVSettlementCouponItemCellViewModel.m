//
//  JDISVSettlementCouponItemCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementCouponItemCellViewModel.h"

#import "JDISVSettlementCouponItemModel.h"

#import "NSAttributedString+JDISVSettlementAmount.h"

@interface JDISVSettlementCouponItemCellViewModel()
@property (nonatomic, strong) JDISVSettlementCouponItemModel *couponModel;
@end

@implementation JDISVSettlementCouponItemCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        
        self.height = 0;
        self.cellName = @"JDISVSettlementCouponItemCell";
        self.cellIdentifier = @"JDISVSettlementCouponItemCell";
        
    }
    return self;
}

- (void)updateWithData:(id)data forType:(NSInteger)type {
    if (type == JDISVSettlementCouponItemCellViewModelUpdateTypeList) {
        if ([data isKindOfClass:JDISVSettlementCouponItemModel.class]) {
            self.couponModel = (JDISVSettlementCouponItemModel *)data;
            
            self.couponId = [_couponModel.couponId copy];
            self.couponKey = [_couponModel.couponKey copy];
            self.isReadOnly = _couponModel.isReadOnly;
            self.canUse = _couponModel.canUsed;
            if (_couponModel.canUsed) {
                self.couponType = JDISVSettlementCouponItemCellViewModelCouponTypeAvailable;
                if (_couponModel.isReadOnly) {
                    self.showRadioButton = NO;
                }else{
                    self.showRadioButton = YES;
                }
                self.selected = _couponModel.selected;
            } else {
                self.couponType = JDISVSettlementCouponItemCellViewModelCouponTypeUnAvailable;
                self.showRadioButton = NO;
            }
            
            // 左上角标识 500-1消费券，消费券307-1，优惠券限制类型：0-全品类 1-限品类 2-限店铺(店铺券)
            NSString *couponTypeTagText = @"";//左上角tag文字
            NSString *couponTextColorStr = @"#C10-a";//券面文字颜色
            NSString *tagTextColorStr = @"#C1";//标签文字颜色
            NSInteger state = 2;//这里传2，结算 可用和不可用优惠券UI是一样的，但不可用优惠券隐藏勾选框
            JDISVCouponTagModel *tag = [JDISVCouponTagModel createWithCouponType:_couponModel.couponType.integerValue couponKind:_couponModel.couponLimitType.integerValue userLabel:_couponModel.userLabel state:state batchId:nil couponId:nil shopId:nil venderId:nil shopName:nil];
            couponTypeTagText = tag.tagName ? : @"";
//            couponTextColorStr = tag.textColorStr ? : @"#C10-a";
//            tagTextColorStr = tag.tagTextColorStr ? : @"#C1";
//            @"#C10-a" @"#C10"
            BOOL isStoreCoupon = _couponModel.couponLimitType.integerValue == 2 || _couponModel.couponLimitType.integerValue == 3;
            couponTextColorStr = isStoreCoupon ? @"#C10" : @"#C10-a";
            tagTextColorStr = couponTextColorStr;
            tag.backgroundColorStr = couponTextColorStr;
            tag.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:couponTextColorStr alpha:0.05];
            tag.tagBackGroundAlpha = 0.1;
            tag.tagBackGroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:couponTextColorStr alpha:0.1];
            tag.tagTextColor = [[JDISVThemeColor sharedInstance] colorWithKey:couponTextColorStr alpha:1.0];
            
            self.couponTypeAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:couponTypeTagText colorKey:tagTextColorStr fontKey:@"#T9" weight:tag.tagFontWeight];
            
            self.couponTypeTagBgColor = tag.tagBackGroundColor;
            self.couponBgColor = tag.backgroundColor;
            self.couponTagWidth = tag.tagWidth;
            self.lineBgColor = tag.tagTextColor;
            
            NSString *discount = @"0";
            if ([_couponModel.discountStr jdcd_validateString]) {
                discount = [_couponModel.discountStr copy];
            }else if ([_couponModel.discount jdcd_validateString]) {
                discount = [_couponModel.discount copy];
            }
            NSMutableAttributedString *amountTmpAttrText = [[NSMutableAttributedString alloc] init];
            NSInteger symbolCount = 1;
            if ([discount containsString:@"."]) {
                symbolCount = 2;
            }
            [amountTmpAttrText KA_renderWithPriceStr:discount type:KAPriceTypeP3 color:[[JDISVThemeColor sharedInstance] colorWithKey:couponTextColorStr]];
            
            if (![discount containsString:@"."]) {
                [amountTmpAttrText trimmingRightZero];
            }
            
            self.discountAmountAttributedString = [amountTmpAttrText copy];
            self.discountAmountHeight = [self sizewithAttributedString:amountTmpAttrText].height;
            
            // quota
            if (_couponModel.couponType.integerValue == 0) {
                self.quotaAttributedString = [[NSAttributedString alloc] initWithString:@""];
                self.quotaLabelHeight = 0;
            }else{
                if ([_couponModel.quota jdcd_validateString] && [_couponModel.quotaStr jdcd_validateString]) {
                    NSMutableAttributedString *tempQuotaAttrText = [[NSMutableAttributedString alloc] ka_settlement_amount_initWithString:_couponModel.quotaStr colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightMedium];
                    self.quotaAttributedString = [tempQuotaAttrText copy];
                    self.quotaLabelHeight = [self sizewithAttributedString:tempQuotaAttrText].height;
                } else if ([_couponModel.quota jdcd_validateString]) {
                    NSMutableAttributedString *tempQuotaAttrText = [[NSMutableAttributedString alloc] ka_settlement_amount_initWithString:[NSString stringWithFormat:SettlementLan(@"checkout_coupon_use_limit"),_couponModel.quota] colorKey:@"#C7" fontKey:@"#T6" weight:UIFontWeightMedium];
                    self.quotaAttributedString = [tempQuotaAttrText copy];
                    self.quotaLabelHeight = [self sizewithAttributedString:tempQuotaAttrText].height;
                }
                else {
                    self.quotaAttributedString = [[NSAttributedString alloc] initWithString:@""];
                    self.quotaLabelHeight = 0;
                }
            }
            
            NSString *couponLimitInfo = @"";
            if ([_couponModel.couponLimitInfo jdcd_validateString]) {
                couponLimitInfo = [_couponModel.couponLimitInfo copy];
            }
            self.useScopeAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:couponLimitInfo colorKey:@"#C7" fontKey:@"#T9"];
            CGFloat userScopeLabelWidth = CGRectGetWidth([[UIScreen mainScreen] bounds]) - 18.f*2 - 101.f - 62.f;
            self.useScopeLabelHeight = [self sizewithAttributedString:self.useScopeAttributedString].height;

            NSString *beginTime = @"";
            NSString *endTime = @"";
            if ([_couponModel.timeBegin jdcd_validateString]) {
                if ([self dateFromString:_couponModel.timeBegin]) {
                    beginTime = [self dateStringFrom:[self dateFromString:_couponModel.timeBegin]];
                } else {
                    beginTime = [self dateStringFrom:[self dateFromTimeString:_couponModel.timeBegin]];
                }
            }
            if ([_couponModel.timeEnd jdcd_validateString]) {
                if ([self dateFromString:_couponModel.timeEnd]) {
                    endTime = [self dateStringFrom:[self dateFromString:_couponModel.timeEnd]];
                } else {
                    endTime = [self dateStringFrom:[self dateFromTimeString:_couponModel.timeEnd]];
                }
            }
            
            NSString *limitTime = [NSString stringWithFormat:@"%@-%@", beginTime, endTime];
            self.timeLimitAttributedString = [[NSAttributedString alloc] initWithString:limitTime attributes:@{
                NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C6"],
                NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightLight]
            }];
            self.timeLimitHeight = [self sizewithAttributedString:self.timeLimitAttributedString].height;
            
            
            self.height = 12.f + self.discountAmountHeight + 3.f + self.quotaLabelHeight + self.useScopeLabelHeight + 8.f + self.timeLimitHeight + 12.f;
            
            NSString *belowMsg = @"";
            if ([_couponModel.belowMsg jdcd_validateString]) {
                belowMsg = _couponModel.belowMsg;
            } else if ([_couponModel.limitDesc jdcd_validateString]) {
                belowMsg = _couponModel.limitDesc;
            }
            if ([belowMsg jdcd_validateString]) {
                self.couponLimitInfoAttributedString = [[NSAttributedString alloc] ka_settlement_amount_initWithString:belowMsg colorKey:couponTextColorStr fontKey:@"#T9"];
                self.couponLimitInfoLabelHeight = [self.couponLimitInfoAttributedString ka_settlement_amount_heightOfAttributedStringWidth: CGRectGetWidth([[UIScreen mainScreen] bounds])-(12.f*2 + 18.f*2) maxNumberOfLines:0 fontKey:@"#T9"];
                self.bottomViewHeight = 6 + _couponLimitInfoLabelHeight + 6;
                self.bottomBgColor = [[JDISVThemeColor sharedInstance] colorWithKey:couponTextColorStr alpha:0.1];
            } else {
                self.bottomViewHeight = 0;
                self.couponLimitInfoLabelHeight = 0;
                self.couponLimitInfoAttributedString = [[NSAttributedString alloc] init];
            }
            self.height = self.height + self.bottomViewHeight - (self.bottomViewHeight > 0 ? 7 : 0);
        } else {
            self.height = 0;
            self.couponType = JDISVSettlementCouponItemCellViewModelCouponTypeTypeError;
        }
    }
    
}

- (NSString *)dateStringFrom:(NSDate *)date {
    NSDateFormatter *format = [[NSDateFormatter alloc] init];
    format.dateFormat = @"yyyy.MM.dd";
    return [format stringFromDate:date];
}

- (NSDate *)dateFromString:(NSString *)dateString {
    NSDateFormatter *format = [[NSDateFormatter alloc] init];
    if ([dateString containsString:@"/"]) {
        format.dateFormat = @"yyyy/MM/dd HH:mm:ss";
    } else if ([dateString containsString:@"-"]) {
        format.dateFormat = @"yyyy-MM-dd HH:mm:ss";
    } else if ([dateString containsString:@"."]) {
        format.dateFormat = @"yyyy.MM.dd HH:mm:ss";
    } else{
        return nil;
    }
    
    return [format dateFromString:dateString];
    
}

- (NSDate *)dateFromTimeString:(NSString *)dateString{
    NSTimeInterval intervel = [dateString doubleValue]/1000;
    NSDate *endDate = [NSDate dateWithTimeIntervalSince1970:intervel];
    return endDate;
}

- (CGSize)sizewithAttributedString:(NSAttributedString *)attributedString{
    CGFloat maxwidth = [UIScreen mainScreen].bounds.size.width - 36.f - 12.f - 92 - 18;
    return [attributedString boundingRectWithSize:CGSizeMake(maxwidth, MAXFLOAT) options:NSStringDrawingUsesFontLeading | NSStringDrawingUsesLineFragmentOrigin context:nil].size;
}


@end
