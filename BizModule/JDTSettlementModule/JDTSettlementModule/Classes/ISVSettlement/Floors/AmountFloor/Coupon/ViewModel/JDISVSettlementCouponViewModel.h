//
//  JDISVSettlementCouponViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JDISVSettlementCouponTitleCellViewModel;
@class JDISVSettlementCouponViewModel;
@class JDISVSettlementAmountFloorMainModel;
@class RACSignal;

typedef NS_ENUM(NSInteger, JDISVSettlementCouponViewModelUpdateType) {
    JDISVSettlementCouponViewModelUpdateTypeRequestCouponList = 0, /**< 打开请求列表 */
    JDISVSettlementCouponViewModelUpdateTypeUseCoupon, /**< 使用 */
    JDISVSettlementCouponViewModelUpdateTypeChangeTab /**< 切换Tab */
};

@interface JDISVSettlementCouponViewModel : NSObject
@property (nonatomic, assign) NSUInteger source; /**< 购物车:0 商详1 */

@property (nonatomic, assign) NSUInteger selectedIndex; /**< 0:可用优惠券 1:不可用优惠券 */

@property (nonatomic, assign) BOOL isPresaleEndPayment; /**< 是否预售付尾款 */
@property (nonatomic, strong) NSArray *couponVos;
@property (nonatomic, strong) NSMutableArray *couponIds;

// title相关
@property (nonatomic, strong) JDISVSettlementCouponTitleCellViewModel *availableTitleViewModel;
@property (nonatomic, strong) JDISVSettlementCouponTitleCellViewModel *unAvailableTitleViewModel;
//
@property (nonatomic, copy) NSArray *availableCouponItemViewModels;
@property (nonatomic, copy) NSArray *unAvailableCouponItemViewModels;

@property (nonatomic, strong) JDISVSettlementCouponViewModel *availableVoucherViewModel;
@property (nonatomic, strong) JDISVSettlementCouponViewModel *unavailableVoucherViewModel;
@property (nonatomic, assign) CGFloat contentViewHeight;

- (void)updateWithModelWith:(id)data typeOf:(JDISVSettlementCouponViewModelUpdateType)type;

- (void)adaptCouponVosData:(NSArray *)couponVos;
#pragma mark - 信号
// 请求优惠券列表
- (RACSignal *)signalOfLoadCouponsInfo;
// 使用优惠券
- (RACSignal *)signalOfUseCoupon:(NSDictionary *)couponInfo;
- (NSDictionary * _Nullable)resetAllCouponParam;
- (CGSize)titleCellSize;
- (CGFloat)contentViewHeight;
- (CGFloat)indicatorCenterPoint;
@end

NS_ASSUME_NONNULL_END
