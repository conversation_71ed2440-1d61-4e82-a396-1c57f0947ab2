//
//  JDISVSettlementCouponTitleCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, JDISVSettlementCouponTitleCellViewModelType) {
    JDISVSettlementCouponTitleCellAvailable = 0, /**< 可用优惠券 */
    JDISVSettlementCouponTitleCellUnavailable /**< 不可用优惠券 */
};

@interface JDISVSettlementCouponTitleCellViewModel : NSObject
@property (nonatomic, strong) NSAttributedString *titleAttrText;


/// 更新优惠券信息
/// @param count 优惠券数量
/// @param selected 是否已选择
/// @param type 0:可用优惠券 1:不可用优惠券
- (void)updateViewModelWithVoucherCount:(NSUInteger)count selectedStatus:(BOOL)selected forType:(NSInteger)type;
@end

NS_ASSUME_NONNULL_END
