//
//  JDISVSettlementCouponItemCell.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementCouponItemCell.h"
#import "JDISVSettlementCouponItemCellViewModel.h"

#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@interface JDISVSettlementCouponItemCell()
@property (weak, nonatomic) IBOutlet UIView *couponContentView;
@property (weak, nonatomic) IBOutlet UIView *leftSemicircleView;
@property (weak, nonatomic) IBOutlet UIView *rightSemicircleView;

// 左上角标识
@property (weak, nonatomic) IBOutlet UILabel *couponTypeLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *couponTypeLabelWidth;

//抵扣金额
@property (weak, nonatomic) IBOutlet UILabel *discountAmountLabel;

// 券面信息
@property (weak, nonatomic) IBOutlet UILabel *quotaLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *quotaLabelHeight;
@property (weak, nonatomic) IBOutlet UILabel *useScopeLable;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *useScopeLableHeight;
@property (weak, nonatomic) IBOutlet UILabel *timeLimitLabel;
@property (weak, nonatomic) IBOutlet UIButton *selectedRadio;

@property (weak, nonatomic) IBOutlet UIView *lineView;

// 底部Limit信息
@property (weak, nonatomic) IBOutlet UIView *bottomView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomViewHeight;
@property (weak, nonatomic) IBOutlet UILabel *couponLimitInfoLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *couponLimitInfoLabelHeight;
@property (weak, nonatomic) IBOutlet UIView *bottomColorBgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *marginTop;


@property (nonatomic, strong) JDISVSettlementCouponItemCellViewModel *itemViewModel;


@property (nonatomic, strong) RACDisposable *buttonDispose;
@end

@implementation JDISVSettlementCouponItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.couponContentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10-a" alpha:0.07f];
    self.leftSemicircleView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.rightSemicircleView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.leftSemicircleView.layer.masksToBounds = YES;
    self.rightSemicircleView.layer.masksToBounds = YES;
    self.leftSemicircleView.layer.cornerRadius = 7.f;
    self.rightSemicircleView.layer.cornerRadius = 7.f;
    
    self.backgroundColor = [UIColor clearColor];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.contentView.layer.masksToBounds = YES;
    self.contentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R90"];
    self.couponContentView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R90"];
    self.couponContentView.layer.borderWidth = 0.5;
    self.leftSemicircleView.layer.borderWidth = 0.5;
    self.rightSemicircleView.layer.borderWidth = 0.5;
    self.bottomView.clipsToBounds = YES;
    self.bottomView.layer.cornerRadius = [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R90"];
    if (@available(iOS 11.0, *)) {
        self.bottomView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
    } else {
        // Fallback on earlier versions
    }
    
}

- (void)updateCellWithViewModel:(__kindof JDISVSettlementAmountFloorBaseCellViewModel *)viewModel {
    if ([viewModel isKindOfClass:JDISVSettlementCouponItemCellViewModel.class]) {
        self.itemViewModel = (JDISVSettlementCouponItemCellViewModel *)viewModel;
        self.couponContentView.backgroundColor = self.itemViewModel.couponBgColor;
//        if (_itemViewModel.showRadioButton) {
//            self.selectedRadio.hidden = NO;
//            self.selectedRadio.jdisv_selected_B7 = _itemViewModel.selected;
//        } else {
//            self.selectedRadio.hidden = YES;
//        }
        //更改交互逻辑
        
        if(_itemViewModel.canUse){
            self.selectedRadio.jdisv_selected_B7 = _itemViewModel.selected;
            self.selectedRadio.hidden = NO;
        }else{
            self.selectedRadio.hidden = YES;
        }
        
        self.couponContentView.layer.borderColor = [_itemViewModel.couponTypeTagBgColor colorWithAlphaComponent:0.1].CGColor;
        self.leftSemicircleView.layer.borderColor = [_itemViewModel.couponTypeTagBgColor colorWithAlphaComponent:0.1].CGColor;;
        self.rightSemicircleView.layer.borderColor = [_itemViewModel.couponTypeTagBgColor colorWithAlphaComponent:0.1].CGColor;;
//        self.lineView.backgroundColor = _itemViewModel.lineBgColor;
        
        self.couponTypeLabel.backgroundColor = _itemViewModel.couponTypeTagBgColor;
        self.couponTypeLabel.layer.masksToBounds = YES;
        self.couponTypeLabelWidth.constant = _itemViewModel.couponTagWidth;
        
        BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
        [self.couponTypeLabel jdcd_addRoundedCorners:isRTL ? UIRectCornerBottomLeft : UIRectCornerBottomRight withRadii:CGSizeMake([[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R91"], [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R91"]) viewRect:CGRectMake(0, 0, _itemViewModel.couponTagWidth, 20.f)];
        self.couponTypeLabel.attributedText = _itemViewModel.couponTypeAttributedString;
        
        // 券面信息
        self.discountAmountLabel.attributedText = _itemViewModel.discountAmountAttributedString;
        self.quotaLabel.attributedText = _itemViewModel.quotaAttributedString;
        self.quotaLabelHeight.constant = _itemViewModel.quotaLabelHeight;
        self.useScopeLable.attributedText = _itemViewModel.useScopeAttributedString;
        self.useScopeLableHeight.constant = _itemViewModel.useScopeLabelHeight;
        self.timeLimitLabel.attributedText = _itemViewModel.timeLimitAttributedString;
        
        if (_itemViewModel.bottomViewHeight != 0) {
            self.bottomViewHeight.constant = _itemViewModel.bottomViewHeight;
            self.bottomView.hidden = NO;
            self.bottomColorBgView.backgroundColor = _itemViewModel.bottomBgColor;
            
            self.couponLimitInfoLabelHeight.constant = _itemViewModel.couponLimitInfoLabelHeight;
            self.couponLimitInfoLabel.hidden = NO;
            self.couponLimitInfoLabel.attributedText = _itemViewModel.couponLimitInfoAttributedString;
            self.marginTop.constant = -7;
            
        } else {
            self.bottomView.hidden = YES;
            self.bottomViewHeight.constant = 0;
            self.couponLimitInfoLabel.hidden = YES;
            self.couponLimitInfoLabelHeight.constant = 0;
            self.marginTop.constant = 0;
        }
        
        [self.buttonDispose dispose];
        @weakify(self)
        self.buttonDispose = [[self.selectedRadio rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
            @strongify(self)
            [self tapSelectedRadioButton];
        }];
        [self drawLineOfDashByCAShapeLayer:self.lineView lineLength:3.0 lineSpacing:4.0 lineColor:_itemViewModel.lineBgColor lineDirection:NO];
    }
}


- (void)tapSelectedRadioButton {
    if (!_itemViewModel.canUse) return;
    
    NSMutableDictionary *paramDic = [[NSMutableDictionary alloc] init];
    NSString *couponId = @"";
    if ([_itemViewModel.couponId jdcd_validateString]) {
        couponId = [_itemViewModel.couponId copy];
    }
    [paramDic addEntriesFromDictionary:@{@"id": couponId}];
    
    NSString *couponKey = @"";
    if ([_itemViewModel.couponKey jdcd_validateString]) {
        couponKey = [_itemViewModel.couponKey copy];
    }
    [paramDic addEntriesFromDictionary:@{@"key": couponKey}];
    if (_itemViewModel.selected) {
        // 取消选中
        [paramDic addEntriesFromDictionary:@{@"selected": @(NO)}];
    } else {
        // 选中
        [paramDic addEntriesFromDictionary:@{@"selected": @(YES)}];
    }
    [paramDic addEntriesFromDictionary:@{@"useBestCoupon": @(NO)}];

    [self.delegate sendNext: @{
        @"cell_identifier": @"JDISVSettlementCouponItemCell",
        @"action_type": @"use_or_cancel_coupon",
        @"data": [paramDic copy]
    }];
    
}


/**
*  通过 CAShapeLayer 方式绘制虚线
*
*  param lineView:       需要绘制成虚线的view
*  param lineLength:     虚线的宽度
*  param lineSpacing:    虚线的间距
*  param lineColor:      虚线的颜色
*  param lineDirection   虚线的方向  YES 为水平方向， NO 为垂直方向
**/
- (void)drawLineOfDashByCAShapeLayer:(UIView *)lineView lineLength:(int)lineLength lineSpacing:(int)lineSpacing lineColor:(UIColor *)lineColor lineDirection:(BOOL)isHorizonal {

    CAShapeLayer *shapeLayer = nil;
    for (id layer in lineView.layer.sublayers) {
        if ([layer isKindOfClass:CAShapeLayer.class]){
            shapeLayer = layer;
            break;
        }
    }
    
    if (!shapeLayer){
        shapeLayer = [CAShapeLayer layer];
        //  把绘制好的虚线添加上来
        [lineView.layer addSublayer:shapeLayer];
    }
   [shapeLayer setBounds:lineView.bounds];

   if (isHorizonal) {

       [shapeLayer setPosition:CGPointMake(CGRectGetWidth(lineView.frame) / 2, CGRectGetHeight(lineView.frame))];

   } else{
       [shapeLayer setPosition:CGPointMake(CGRectGetWidth(lineView.frame) / 2, CGRectGetHeight(lineView.frame)/2)];
   }

   [shapeLayer setFillColor:[UIColor clearColor].CGColor];
   //  设置虚线颜色为blackColor
   [shapeLayer setStrokeColor:lineColor.CGColor];
   //  设置虚线宽度
   if (isHorizonal) {
       [shapeLayer setLineWidth:CGRectGetHeight(lineView.frame)];
   } else {

       [shapeLayer setLineWidth:CGRectGetWidth(lineView.frame)];
   }
   [shapeLayer setLineJoin:kCALineJoinBevel];
   //  设置线宽，线间距
   [shapeLayer setLineDashPattern:[NSArray arrayWithObjects:[NSNumber numberWithInt:lineLength], [NSNumber numberWithInt:lineSpacing], nil]];
   //  设置路径
   CGMutablePathRef path = CGPathCreateMutable();
   CGPathMoveToPoint(path, NULL, 0, 0);

   if (isHorizonal) {
       CGPathAddLineToPoint(path, NULL,CGRectGetWidth(lineView.frame), 0);
   } else {
       CGPathAddLineToPoint(path, NULL, 0, _itemViewModel.height-7);
   }

   [shapeLayer setPath:path];
   CGPathRelease(path);
   
}



@end
