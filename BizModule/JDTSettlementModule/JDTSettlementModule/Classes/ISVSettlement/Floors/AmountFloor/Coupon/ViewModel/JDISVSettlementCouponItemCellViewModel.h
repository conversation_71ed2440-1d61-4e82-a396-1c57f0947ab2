//
//  JDISVSettlementCouponItemCellViewModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementAmountFloorBaseCellViewModel.h"

NS_ASSUME_NONNULL_BEGIN

/**
 优惠券浮层 券 ViewModel
 */

typedef NS_ENUM(NSUInteger, JDISVSettlementCouponItemCellViewModelCouponType) {
    JDISVSettlementCouponItemCellViewModelCouponTypeAvailable = 0, /**< 可用 */
    JDISVSettlementCouponItemCellViewModelCouponTypeUnAvailable, /**< 不可用 */
    JDISVSettlementCouponItemCellViewModelCouponTypeTypeError /**< 信息错误 */
};

typedef NS_ENUM(NSUInteger, JDISVSettlementCouponItemCellViewModelUpdateType) {
    JDISVSettlementCouponItemCellViewModelUpdateTypeList = 0, /**< 优惠券列表更新 */
};

@interface JDISVSettlementCouponItemCellViewModel : JDISVSettlementAmountFloorBaseCellViewModel
@property (nonatomic, assign) JDISVSettlementCouponItemCellViewModelCouponType couponType;

/**< 入参使用 */
@property (nonatomic, copy) NSString *couponId;
@property (nonatomic, copy) NSString *couponKey;
@property (nonatomic) BOOL canUse; /**< 是否可用 */
@property (nonatomic) BOOL isReadOnly; /**< 是否可选 */
@property (nonatomic) BOOL selected; /**< 是否选中 */
@property (nonatomic) BOOL showRadioButton; /**< 是否选择按钮 */

//券背景颜色
@property (nonatomic, strong) UIColor *couponBgColor;
// 左上角标识
@property (nonatomic, strong) UIColor *couponTypeTagBgColor;
@property (nonatomic, copy) NSAttributedString *couponTypeAttributedString;
@property (nonatomic, assign) CGFloat couponTagWidth;

//抵扣金额
@property (nonatomic, copy) NSAttributedString *discountAmountAttributedString;
@property (nonatomic, assign) CGFloat discountAmountHeight;

// 券面信息
@property (nonatomic, copy) NSAttributedString *quotaAttributedString;
@property (nonatomic, assign) CGFloat quotaLabelHeight;
@property (nonatomic, copy) NSAttributedString *useScopeAttributedString;
@property (nonatomic, assign) CGFloat useScopeLabelHeight;
@property (nonatomic, copy) NSAttributedString *timeLimitAttributedString;
@property (nonatomic, assign) CGFloat timeLimitHeight;
@property (nonatomic, copy) UIColor *lineBgColor;

@property (nonatomic, assign) CGFloat bottomViewHeight;
@property (nonatomic, assign) CGFloat couponLimitInfoLabelHeight;
@property (nonatomic, strong) UIColor *bottomBgColor;
@property (nonatomic, copy) NSAttributedString *couponLimitInfoAttributedString;


- (CGSize)sizewithAttributedString:(NSAttributedString *)attributedString;
@end

NS_ASSUME_NONNULL_END
