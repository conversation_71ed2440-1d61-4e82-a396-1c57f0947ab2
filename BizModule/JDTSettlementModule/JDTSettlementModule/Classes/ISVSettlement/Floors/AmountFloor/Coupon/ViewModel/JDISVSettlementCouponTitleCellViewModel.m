//
//  JDISVSettlementCouponTitleCellViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementCouponTitleCellViewModel.h"

#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@implementation JDISVSettlementCouponTitleCellViewModel
- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (void)updateViewModelWithVoucherCount:(NSUInteger)count selectedStatus:(BOOL)selected forType:(NSInteger)type {
    NSString *title = @"";
    if (type == 0) {
        // 可用优惠券
        title = SettlementLan(@"checkout_coupon_list_available") ;
    } else {
        // 不可用优惠券
        title = SettlementLan(@"checkout_coupon_list_unavailable") ;
    }
    
    [self updateViewModelSelectedStatue:selected title:title count:count];
}


- (void)updateViewModelSelectedStatue:(BOOL)selected title:(NSString *)title count:(NSUInteger)count {
    NSString *countStr = [NSString stringWithFormat:@"(%lu)",count];
    if (selected) {
        NSMutableAttributedString *titleAttributedString = [[NSMutableAttributedString alloc] initWithString:title attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        NSAttributedString *countAttribitedString = [[NSAttributedString alloc] initWithString:countStr attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T6" weight:UIFontWeightMedium],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
   
        [titleAttributedString appendAttributedString:countAttribitedString];
        self.titleAttrText = [titleAttributedString copy];
    } else {
        NSMutableAttributedString *titleAttributedString = [[NSMutableAttributedString alloc] initWithString:title attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        NSAttributedString *countAttribitedString = [[NSAttributedString alloc] initWithString:countStr attributes:@{
            NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
            NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
        }];
        [titleAttributedString appendAttributedString:countAttribitedString];
        self.titleAttrText = [titleAttributedString copy];
    }
    
}
@end
