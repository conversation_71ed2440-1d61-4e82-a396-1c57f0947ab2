//
//  JDISVSettlementCouponViewModel.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementCouponViewModel.h"

#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import "JDISVSettlementCouponTitleCellViewModel.h"
#import "JDISVSettlementCouponItemCellViewModel.h"

#import "JDISVSettlementAmountFloorMainModel.h"
#import "JDISVSettlementAmountFloorModel.h"
#import "JDISVSettlementCouponItemModel.h"

#import "JDISVSettlementAmountFloorNetService.h"

static const CGFloat kJDISVSettlementCouponTitleViewHeight = 55.f;
static const CGFloat kJDISVSettlementCouponCategoryViewHeight = 44.f;
static const CGFloat kJDISVSettlementCouponBottomViewHeight = 50.f;

static NSString * const kJDISVSettlementCouponSingleFloorPrefixKey = @"couponSingleFloor";

@interface JDISVSettlementCouponViewModel()
@property (nonatomic, copy) NSString *selectedVoucherId;
@property (nonatomic, assign) NSUInteger availableCouponCounts;
@property (nonatomic, assign) NSUInteger unAvailableCouponCounts;

@property (nonatomic, strong, nullable) NSArray *rawCoupons;
@end

@implementation JDISVSettlementCouponViewModel
- (instancetype)init {
    self = [super init];
    if (self) {
        // Title
        _selectedIndex = 0;
        _availableCouponCounts = 0;
        _unAvailableCouponCounts = 0;
        
        self.availableTitleViewModel = [[JDISVSettlementCouponTitleCellViewModel alloc] init];
        self.unAvailableTitleViewModel = [[JDISVSettlementCouponTitleCellViewModel alloc] init];
        
        [self updateCategoryTitleViewModel];

    }
    return self;
}

- (void)updateWithModelWith:(id)data typeOf:(JDISVSettlementCouponViewModelUpdateType)type {
    if (type == JDISVSettlementCouponViewModelUpdateTypeRequestCouponList) {
        // 记录原始数据
        self.rawCoupons = [self recordRawCoupons:[self itemModelsFromRawData:data]];
        // 处理model
        [self updateCouponItemViewModelsWith:[self itemModelsFromRawData:data]];
        [self updateCategoryTitleViewModel];
    } else if (type == JDISVSettlementCouponViewModelUpdateTypeUseCoupon) {
        // 处理model
        [self updateCouponItemViewModelsWith:[self itemModelsFromRawData:data]];
        [self updateCategoryTitleViewModel];
    } else if (type == JDISVSettlementCouponViewModelUpdateTypeChangeTab) {
        NSNumber *selectedIndex = (NSNumber *)data;
        self.selectedIndex = [selectedIndex integerValue];
        //
        [self updateCategoryTitleViewModel];
    }
}

- (NSArray * _Nullable)itemModelsFromRawData:(id)data {
    if ([data isKindOfClass:JDISVSettlementAmountFloorMainModel.class]) {
        JDISVSettlementAmountFloorMainModel *model = (JDISVSettlementAmountFloorMainModel *)data;
        NSMutableArray *itemModels = [NSMutableArray array];
        for (JDISVSettlementAmountFloorModel *floorData in model.floors) {
            if ([floorData.uuid containsString:kJDISVSettlementCouponSingleFloorPrefixKey]) {
                JDISVSettlementCouponItemModel *itemModel = [JDISVSettlementCouponItemModel yy_modelWithDictionary:floorData.info];
                [itemModels addObject:itemModel];
            }
        }
        return [NSArray arrayWithArray:itemModels];
    } else {
        return nil;
    }
}

/// 记录原始数据 - 入参回传
/// @param itemModels 原始数据
- (NSArray * __nullable)recordRawCoupons:(NSArray * _Nullable)itemModels {
    NSMutableArray *rawCoupons = [NSMutableArray array];
    for (JDISVSettlementCouponItemModel *itemModel in itemModels) {
        NSMutableDictionary *couponInfo = [NSMutableDictionary dictionary];
        // couponType
        if (itemModel.couponType) {
            [couponInfo addEntriesFromDictionary:@{@"couponType": itemModel.couponType}];
        }
        
        // selected
        [couponInfo addEntriesFromDictionary:@{@"selected": @(itemModel.selected)}];
        
        // couponStyle
        if (itemModel.couponStyle) {
            [couponInfo addEntriesFromDictionary:@{@"couponStyle": itemModel.couponStyle}];
        }
        
        // platform
        if (itemModel.couponPlatform) {
            [couponInfo addEntriesFromDictionary:@{@"platform": itemModel.couponPlatform}];
        }
        
        // key
        if (itemModel.couponKey && [itemModel.couponKey jdcd_validateString]) {
            [couponInfo addEntriesFromDictionary:@{@"key": itemModel.couponKey}];
        }
        
        // id
        if (itemModel.couponId && [itemModel.couponId jdcd_validateString]) {
            [couponInfo addEntriesFromDictionary:@{@"id": itemModel.couponId}];
        }
        
        if (couponInfo.allKeys.count > 0) {
            [rawCoupons addObject:couponInfo];
        }
    }
    if (rawCoupons.count > 0) {
        return [NSArray arrayWithArray:rawCoupons];
    } else {
        return nil;
    }
}

- (void)updateCategoryTitleViewModel {
    [self.availableTitleViewModel updateViewModelWithVoucherCount:_availableCouponCounts selectedStatus:_selectedIndex == 0 forType:JDISVSettlementCouponTitleCellAvailable];
    [self.unAvailableTitleViewModel updateViewModelWithVoucherCount:_unAvailableCouponCounts selectedStatus:_selectedIndex == 1 forType:JDISVSettlementCouponTitleCellUnavailable];
}



- (void)updateCouponItemViewModelsWith:(NSArray * _Nullable)itemModels {
    if (itemModels && itemModels.count > 0) {
        NSMutableArray *availableItemViewModels = [NSMutableArray array];
        NSMutableArray *unAvailableItemViewModels = [NSMutableArray array];
        for (JDISVSettlementCouponItemModel *itemModel in itemModels) {
            JDISVSettlementCouponItemCellViewModel *itemViewModel = [[JDISVSettlementCouponItemCellViewModel alloc] init];
            [itemViewModel updateWithData:itemModel forType:JDISVSettlementCouponItemCellViewModelUpdateTypeList];
            if (itemViewModel.couponType == JDISVSettlementCouponTitleCellAvailable) {
                [availableItemViewModels addObject:itemViewModel];
            } else {
                [unAvailableItemViewModels addObject:itemViewModel];
            }
        }
        self.availableCouponItemViewModels = [NSArray arrayWithArray:availableItemViewModels];
        self.unAvailableCouponItemViewModels = [NSArray arrayWithArray:unAvailableItemViewModels];
        self.availableCouponCounts = _availableCouponItemViewModels.count;
        self.unAvailableCouponCounts = _unAvailableCouponItemViewModels.count;
    } else {
        self.availableCouponItemViewModels = [NSArray array];
        self.unAvailableCouponItemViewModels = [NSArray array];
        
        self.availableCouponCounts = 0;
        self.unAvailableCouponCounts = 0;
    }
}


#pragma mark - 把结算中台下发的优惠券数据转换成阿波罗接口的格式
- (void)adaptCouponVosData:(NSArray *)couponVos{
    NSMutableArray *floors = @[].mutableCopy;
    if (couponVos && [couponVos isKindOfClass:NSArray.class]) {
        for (int i = 0; i < couponVos.count; i++) {
            NSDictionary *couponVoData = couponVos[i];
            NSString *uuid = [NSString stringWithFormat:@"couponSingleFloor_%d",i];
            NSDictionary *floorDic = @{
                @"info" : @{
                  @"C-M#couponSingleFloor&core" : @{
                    @"batchId" : couponVoData[@"batchId"] ? : @"",
                    @"canUsed" : couponVoData[@"available"] ? : @(0),
//                    "classify" : "1",
                      @"id" : couponVoData[@"id"] ? :@"",
                    @"key" : couponVoData[@"tradeKey"] ? :@"",
                    @"readOnly" : couponVoData[@"readOnly"] ? :@"",
                    @"selected" : couponVoData[@"selected"] ? :@(0),
                  },
                  @"C-M#couponSingleFloor&discount" : @{
                    @"discount" : couponVoData[@"discount"] ? :@"",
                    @"curUseDiscount" : couponVoData[@"curUseDiscount"] ? :@"",
//                    "discountStr" : "¥2",
//                    "discountStrType" : 0
                  },
                  @"C-M#couponSingleFloor&limitUseInfo" : @{
                    @"limitDesc" : couponVoData[@"limitDesc"] ? :@"",
                    @"couponLimitInfo" : couponVoData[@"limitTips"] ? :@"",
                    @"couponLimitType" : couponVoData[@"couponKind"] ? :@"",
//                    "couponPlatform" : 0,
                    @"couponStyle" : couponVoData[@"couponStyle"] ? :@"",
                    @"couponType" : couponVoData[@"couponType"] ? :@"",
//                    "venderId" : 0
                  },
                  @"C-M#couponSingleFloor&time" : @{
                    @"timeBegin" : couponVoData[@"beginTime"] ? :@"",
                    @"timeEnd" : couponVoData[@"endTime"] ? :@"",
                  },
//                  "VCYBB2C-D#basicMsg&basicMsg" : {
//                    "couponMsg" : "",
//                    "name" : "全品类东券",
//                    "scope" : "全品类"
//                  },
                  @"VCYBB2C-D#flowLable&flowLable" : @{
                    @"userLabel" : couponVoData[@"userLabel"] ? :@"",
                  },
                  @"VCYBB2C-D#money&money" : @{
//                    "highDiscount" : "0.00",
                    @"quota" : couponVoData[@"quota"] ? :@"",
//                    "quotaStr" : "满10元可用"
                  }
                },
                @"uuid" : uuid
              };
            
            [floors addObject:floorDic];
        }
    }
    
//    model.floors = floors;
    JDISVSettlementAmountFloorMainModel *model = [JDISVSettlementAmountFloorMainModel yy_modelWithDictionary:@{@"floors":floors}];
    [self updateWithModelWith:model typeOf:JDISVSettlementCouponViewModelUpdateTypeRequestCouponList];
}

# pragma mark - 请求参数
- (NSDictionary * _Nullable)resetAllCouponParam {
    if (_rawCoupons && _rawCoupons.count > 0) {
        return @{@"theCoupons": _rawCoupons};
    } else {
        return nil;
    }
}

# pragma mark - 信号
- (RACSignal *)signalOfLoadCouponsInfo {
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSDictionary *orderStrParam = @{@"useBestCoupon": @(NO)};
        [KASettlementAmountFloorNetService requestCouponListWithParam:orderStrParam sourceFrom:_source complete:^(JDISVSettlementAmountFloorMainModel * _Nonnull model, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [self updateWithModelWith:model typeOf:JDISVSettlementCouponViewModelUpdateTypeRequestCouponList];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (RACSignal *)signalOfUseCoupon:(NSDictionary *)couponInfo {
    return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [KASettlementAmountFloorNetService requestUseOrCancelCouponWithParam:couponInfo sourceFrom:_source complete:^(JDISVSettlementAmountFloorMainModel * _Nonnull model, NSError * _Nullable error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [self updateWithModelWith:model typeOf:JDISVSettlementCouponViewModelUpdateTypeUseCoupon];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

# pragma mark - 界面信息

- (CGFloat)contentViewHeight {
    CGFloat bottomHeight = kJDISVSettlementCouponBottomViewHeight;
    if (@available(iOS 11.0, *)) {
        CGFloat bottom = [UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom;
        if (bottom > 0) {
            bottomHeight = bottomHeight + bottom;
        }
    }
    
    CGFloat contentScrollViewHeight = CGRectGetHeight([[UIScreen mainScreen] bounds]) * 0.8f - bottomHeight - kJDISVSettlementCouponTitleViewHeight - kJDISVSettlementCouponCategoryViewHeight;
    
    return ceil(contentScrollViewHeight);
}

- (CGSize)titleCellSize {
    CGFloat titleCellWidth = (CGRectGetWidth([[UIScreen mainScreen] bounds]) / 2);
    return CGSizeMake(titleCellWidth, kJDISVSettlementCouponCategoryViewHeight);
}

- (CGFloat)indicatorCenterPoint {
    CGFloat titleCellWidth = (CGRectGetWidth([[UIScreen mainScreen] bounds]) / 2);
    if (_selectedIndex == 0) {
        return titleCellWidth/2;
    } else {
        return titleCellWidth/2 + titleCellWidth;
    }
}
@end
