//
//  JDISVSettlementCouponController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementCouponController.h"

#import <JDISVDZNEmptyDataSetModule/JDISVDZNEmptyDataSetModule-umbrella.h>

#import "JDISVSettlementCouponTitleCell.h"
#import "JDISVSettlementAmountFloorBaseCell.h"

#import "JDISVSettlementCouponViewModel.h"
#import "JDISVSettlementCouponItemCellViewModel.h"
#import "JDISVSettlementCouponTitleCellViewModel.h"
#import "JDISVSettlementAmountFloorBaseCellViewModel.h"
#import <JDISVMasonryModule/Masonry.h>
#import "UIImage+JDISVSettlement.h"
#import "NSBundle+JDISVSettlement.h"
#import "NSAttributedString+JDISVSettlementAmount.h"


@interface JDISVSettlementCouponController () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout, UITableViewDelegate, UITableViewDataSource, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>

@property (weak, nonatomic) IBOutlet UIView *couponTopTitleView;
@property (weak, nonatomic) IBOutlet UILabel *controllerTitleLabel;
@property (weak, nonatomic) IBOutlet UIButton *closeButton;

@property (weak, nonatomic) IBOutlet UIView *categoryView;
@property (weak, nonatomic) IBOutlet UICollectionView *titleCollectionView;
@property (weak, nonatomic) IBOutlet UIView *indicatorView;
@property (weak, nonatomic) IBOutlet UIImageView *indicator;

@property (weak, nonatomic) IBOutlet UIScrollView *contentScrollView;
@property (weak, nonatomic) IBOutlet UIView *bottomView;
@property (weak, nonatomic) IBOutlet UIButton *confirmButton;

@property (nonatomic, strong) UITableView *availableTableView;
@property (nonatomic, strong) UITableView *unAvailableTableView;
@end

@implementation JDISVSettlementCouponController
- (instancetype)initWithCoder:(NSCoder *)coder
{
    self = [super initWithCoder:coder];
    if (self) {
        self.indicator.hidden = YES;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    [self loadCouponData];
}

- (void)viewDidAppear:(BOOL)animated {
    
    [super viewDidAppear:animated];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];

    self.contentScrollView.contentSize = CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) * 2, [self.viewModel contentViewHeight]);
}


- (void)updateWithSource:(NSUInteger)source{
    self.viewModel.source = source;
}

- (void)presaleEndPaymentUpdateWithCouponVos:(NSArray *)couponVos{
    self.viewModel.isPresaleEndPayment = YES;//预售付尾款标识
    self.viewModel.couponVos = couponVos;
}

- (void)reloadPresaleEndPaymentUpdateWithCouponVos:(NSArray *)couponVos{
    self.viewModel.isPresaleEndPayment = YES;//预售付尾款标识
    self.viewModel.couponVos = couponVos;
    [self loadCouponData];
}

#pragma mark - UI

- (void)setupUI {
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    self.couponTopTitleView.backgroundColor = [UIColor clearColor];
    self.categoryView.backgroundColor = [UIColor clearColor];
    self.indicatorView.backgroundColor = [UIColor clearColor];
    self.bottomView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
//    [self.closeButton setImage:[UIImage jdisvSettlement_imageNamed:@"jdka_settlement_float_close"] forState:UIControlStateNormal];
//    self.cancleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
    UIImage *image = [UIImage ka_iconWithName:JDIF_ICON_CLOSE_SMALL imageSize:CGSizeMake(9, 9) color:color];
    [self.closeButton setImage:image forState:UIControlStateNormal];
    [self.closeButton addTarget:self action:@selector(clickCloseButton) forControlEvents:UIControlEventTouchUpInside];
    self.closeButton.layer.cornerRadius = 9;
    self.closeButton.layer.masksToBounds = YES;
    self.closeButton.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(18);
        make.width.height.mas_equalTo(18);
        make.trailing.mas_equalTo(-18);
    }];
    
    self.controllerTitleLabel.attributedText = [[NSAttributedString alloc] ka_settlement_amount_initWithString:SettlementLan(@"checkout_scale")  colorKey:@"#C7" fontKey:@"#T5" weight:UIFontWeightMedium];

    // indicator
    self.indicator.layer.masksToBounds = YES;
    self.indicator.layer.cornerRadius = 1.5f;
    self.indicator.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
    self.indicator.hidden = YES;
    //RTL 重置坐标
    BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    if (isRTL) {
        CGRect rect = self.indicator.frame;
        rect.origin.x = [UIScreen mainScreen].bounds.size.width - self.indicator.frame.origin.x - self.indicator.frame.size.width;;
        self.indicator.frame = rect;
    }
    [self scrollIndicatorToPoint:[self.viewModel indicatorCenterPoint]];

    if (@available(iOS 11.0, *)) {
        self.contentScrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
  
    // Collection
    self.titleCollectionView.delegate = self;
    self.titleCollectionView.dataSource = self;
    self.titleCollectionView.backgroundColor = [UIColor clearColor];
    
    [self.titleCollectionView registerClass:NSClassFromString(@"UICollectionViewCell") forCellWithReuseIdentifier:@"UICollectionViewCell"];
    [self.titleCollectionView registerNib:[UINib nibWithNibName:@"JDISVSettlementCouponTitleCell" bundle:[NSBundle jdisvSettlement_bundle]] forCellWithReuseIdentifier:@"JDISVSettlementCouponTitleCell"];
    
    self.contentScrollView.contentSize = CGSizeMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) * 2, [self.viewModel contentViewHeight]);
    
    // TableView
    self.availableTableView = [[UITableView alloc] initWithFrame:CGRectMake(18.f, 0, CGRectGetWidth([[UIScreen mainScreen] bounds])-18.f*2, [self.viewModel contentViewHeight]) style:UITableViewStylePlain];
    self.unAvailableTableView = [[UITableView alloc] initWithFrame:CGRectMake(CGRectGetWidth([[UIScreen mainScreen] bounds])+18.f, 0, CGRectGetWidth([[UIScreen mainScreen] bounds])-18.f*2, [self.viewModel contentViewHeight]) style:UITableViewStylePlain];
    self.availableTableView.showsHorizontalScrollIndicator = NO;
    self.availableTableView.showsVerticalScrollIndicator = NO;
    self.unAvailableTableView.showsHorizontalScrollIndicator = NO;
    self.unAvailableTableView.showsVerticalScrollIndicator = NO;
    [self setupTableView:_availableTableView];
    [self setupTableView:_unAvailableTableView];
    [self.contentScrollView addSubview:_availableTableView];
    [self.contentScrollView addSubview:_unAvailableTableView];
    
    // BottomView
    [self.confirmButton addTarget:self action:@selector(clickConfirmButton) forControlEvents:UIControlEventTouchUpInside];
    [self.confirmButton setTitle:SettlementLan(@"settlement_discount_code_detainment_commit") forState:UIControlStateNormal];
    [self.confirmButton setTitle:SettlementLan(@"settlement_discount_code_detainment_commit") forState:UIControlStateHighlighted];
    [self.confirmButton setTitle:SettlementLan(@"settlement_discount_code_detainment_commit") forState:UIControlStateDisabled];
    [self.confirmButton renderB1];
    
    [self reloadView];
}

- (void)setupTableView:(UITableView *)tableView {
    
    if (@available(iOS 11.0, *)) {
        tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    tableView.delegate = self;
    tableView.dataSource = self;
    tableView.emptyDataSetSource = self;
    tableView.emptyDataSetDelegate = self;
    
    tableView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    
    tableView.estimatedRowHeight = 0;
    tableView.estimatedSectionHeaderHeight = 0;
    tableView.estimatedSectionFooterHeight = 0;
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    // Group样式的TableView Header和Footer会有空白
    tableView.tableHeaderView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 0.1f)];
    
}

- (void)reloadView {
    [self.availableTableView reloadData];
    [self.unAvailableTableView reloadData];
    [self.titleCollectionView reloadData];
}

# pragma mark - Data

- (void)loadCouponData {
    if (self.viewModel.isPresaleEndPayment) {
        /**< 预售付尾款结算中台接口下发的优惠券数据 */
        [self.viewModel adaptCouponVosData:self.viewModel.couponVos];
        self.viewModel.couponIds = @[].mutableCopy;
        for (JDISVSettlementCouponItemCellViewModel *cellmodel in _viewModel.availableCouponItemViewModels){
            if (cellmodel.selected) {
                if ([cellmodel.couponId jdcd_validateString]) {
                    [self.viewModel.couponIds addObject:cellmodel.couponId];
                }
            }
        }
        
        [self reloadView];
        return;
    }
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel signalOfLoadCouponsInfo] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    } completed:^{
        [PlatformService dismissInView:self.view];
        [self reloadView];
    }];
}

# pragma mark - Action

- (void)processCellActionWithParames:(NSDictionary *)param {
    NSDictionary *data = [param objectForKey:@"data"];
    NSString *actionType = [param objectForKey:@"action_type"];
    NSString *cellIdf = [param objectForKey:@"cell_identifier"];
    if ([cellIdf isEqualToString:@"JDISVSettlementCouponItemCell"] && [actionType isEqualToString:@"use_or_cancel_coupon"]) {
        // 勾选或者取消勾选优惠券
        [self useOrCancelCouponWith:data];
    }
}

- (void)clickCellIndexOf:(NSIndexPath *)indexPath {
    
}



// 勾选后者取消勾选优惠券
- (void)useOrCancelCouponWith:(NSDictionary *)couponInfo {
    if (self.viewModel.isPresaleEndPayment) {
        //    [paramDic addEntriesFromDictionary:@{@"useBestCoupon": @(NO)}];
//        NSMutableDictionary *backDic = @{}.mutableCopy;
        
        NSString *couponId = couponInfo[@"id"];
//        NSString *couponKey = couponInfo[@"couponKey"];
        NSNumber *selected = couponInfo[@"selected"];
//        NSString *discount;
//        JDISVSettlementCouponItemCellViewModel *currentcellmodel;
//        for (NSString *couponid in self.viewModel.couponIds) {
//            for (JDISVSettlementCouponItemCellViewModel *cellmodel in _viewModel.availableCouponItemViewModels){
//                if ([cellmodel.couponId isEqualToString:couponid]) {
//                    currentcellmodel = cellmodel;
//                }
//            }
//        }
//        if (selected.boolValue) {
//            if (self.viewModel.couponIds.count > 0) {
//                if (currentcellmodel.couponModel.isReadOnly == NO) {
//
//                }
//            } else {
//                if ([couponId jdcd_validateString] && ![self.viewModel.couponIds containsObject:couponId]) {
//                    [self.viewModel.couponIds addObject:couponId];
//                }
//                currentcellmodel.selected = selected;
//            }
//        }else{
//            if ([self.viewModel.couponIds containsObject:couponId]) {
//                [self.viewModel.couponIds removeObject:couponId];
//            }
//            currentcellmodel.selected = selected;
//        }
//        [self reloadView];

        if (selected.boolValue) {
            self.refreshMainPageCallback(@{@"couponIds":@[couponId ? : @""]});
        }else{
            self.refreshMainPageCallback(@{});
        }
        return;
    }
    
    
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel signalOfUseCoupon:couponInfo] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    } completed:^{
        [PlatformService dismissInView:self.view];
        [self reloadView];
    }];
}

- (void)changeSelectedIndex:(NSUInteger)index {
    if (index != _viewModel.selectedIndex) {
        [self.viewModel updateWithModelWith:@(index) typeOf:JDISVSettlementCouponViewModelUpdateTypeChangeTab];
        [self scrollIndicatorToPoint:[self.viewModel indicatorCenterPoint]];
        [self.contentScrollView setContentOffset:CGPointMake(CGRectGetWidth([[UIScreen mainScreen] bounds]) * _viewModel.selectedIndex, 0) animated:NO];
        [self reloadView];
    }
}

- (void)clickCloseButton {
    if (self.viewModel.isPresaleEndPayment) {
        NSDictionary *backDic = @{@"couponIds":self.viewModel.couponIds ? : @[] };
        if (self.confirmCallback) {
            self.confirmCallback(backDic);
        }
    }else{
        if (self.closeCallback && _viewModel.resetAllCouponParam) {
            self.closeCallback(_viewModel.resetAllCouponParam);
        }
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)clickConfirmButton {
    if (self.viewModel.isPresaleEndPayment) {
        NSDictionary *backDic = @{@"couponIds":self.viewModel.couponIds ? : @[] };
        if (self.confirmCallback) {
            self.confirmCallback(backDic);
        }
    }else{
        if (self.confirmCallback) {
            self.confirmCallback(nil);
        }
    }
    
    [self dismissViewControllerAnimated:YES completion:nil];
}


# pragma mark - Animation

/// 滚动Indicator
/// @param centerPoint Indicator目标中心点
- (void)scrollIndicatorToPoint:(CGFloat)centerPoint {
    CGFloat tx = centerPoint - self.indicator.center.x;
    //RTL
    BOOL isRTL = [UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft;
    if (isRTL) {
        tx = ([UIScreen mainScreen].bounds.size.width - centerPoint) - self.indicator.center.x;
    }
    __weak typeof(self) weakSelf = self;
    [UIView animateWithDuration:.3f animations:^{
        __strong typeof(weakSelf) strongSelf = weakSelf;
        strongSelf.indicator.transform = CGAffineTransformMakeTranslation(tx, 0);
    } completion:^(BOOL finished) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        strongSelf.indicator.hidden = NO;
    }];
}

#pragma mark - CollectionView
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    
    return 2;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
 
    return [_viewModel titleCellSize];
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    
    return 0;
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, 0, 0, 0);
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementCouponTitleCell *cell = nil;
    cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"JDISVSettlementCouponTitleCell" forIndexPath:indexPath];
    JDISVSettlementCouponTitleCellViewModel *titleViewModel;
    if (indexPath.row == 0) {
        titleViewModel = _viewModel.availableTitleViewModel;
    } else {
        titleViewModel = _viewModel.unAvailableTitleViewModel;
    }
    if ([cell respondsToSelector: @selector(updateWith:)]) {
        [cell performSelector:@selector(updateWith:) withObject:titleViewModel];
    }

    if (cell == nil) {
        cell = [UICollectionViewCell new];
    }
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementCouponTitleCellViewModel *titleViewModel;
    if (indexPath.row == 0) {
        titleViewModel = _viewModel.availableTitleViewModel;
    } else {
        titleViewModel = _viewModel.unAvailableTitleViewModel;
    }
    if ([cell respondsToSelector: @selector(updateWith:)]) {
        [cell performSelector:@selector(updateWith:) withObject:titleViewModel];
    }
    
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
   
    [self changeSelectedIndex:indexPath.row];
}

#pragma mark - TableView
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (tableView == _availableTableView) {
        if (_viewModel.availableCouponItemViewModels && _viewModel.availableCouponItemViewModels.count > 0) {
            return  _viewModel.availableCouponItemViewModels.count;
        } else {
            return 0;
        }
    } else {
        if (_viewModel.unAvailableCouponItemViewModels && _viewModel.unAvailableCouponItemViewModels.count > 0) {
            return _viewModel.unAvailableCouponItemViewModels.count;
        } else {
            return 0;
        }
    }
    
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (tableView == _availableTableView) {
        if (_viewModel.availableCouponItemViewModels && _viewModel.availableCouponItemViewModels.count > 0) {
            return  1;
        } else {
            return 0;
        }
    } else {
        if (_viewModel.unAvailableCouponItemViewModels && _viewModel.unAvailableCouponItemViewModels.count > 0) {
            return 1;
        } else {
            return 0;
        }
    }
}


- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 12.f;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, CGRectGetWidth([[UIScreen mainScreen] bounds]), 12.f)];
    headerView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    return headerView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel;
    if (tableView == _availableTableView) {
        // 可用
        if (_viewModel.availableCouponItemViewModels && indexPath.section < _viewModel.availableCouponItemViewModels.count) {
            itemViewModel = [_viewModel.availableCouponItemViewModels objectAtIndex:indexPath.section];
        }
    } else {
        // 不可用
        if (_viewModel.unAvailableCouponItemViewModels && indexPath.section < _viewModel.unAvailableCouponItemViewModels.count) {
            itemViewModel = [_viewModel.unAvailableCouponItemViewModels objectAtIndex:indexPath.section];
        }
    }
    if (itemViewModel) {
        return itemViewModel.height;
    } else {
        return 0;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementAmountFloorBaseCell *cell;
    JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel;
    if (tableView == _availableTableView) {
        // 可用
        if (_viewModel.availableCouponItemViewModels && indexPath.section < _viewModel.availableCouponItemViewModels.count) {
            itemViewModel = [_viewModel.availableCouponItemViewModels objectAtIndex:indexPath.section];
        }
    } else {
        // 不可用
        if (_viewModel.unAvailableCouponItemViewModels && indexPath.section < _viewModel.unAvailableCouponItemViewModels.count) {
            itemViewModel = [_viewModel.unAvailableCouponItemViewModels objectAtIndex:indexPath.section];
        }
    }
    
    if (itemViewModel) {
        [tableView registerNib:[UINib nibWithNibName:itemViewModel.cellName bundle:[NSBundle jdisvSettlement_bundle]] forCellReuseIdentifier: itemViewModel.cellIdentifier];
        cell = [tableView dequeueReusableCellWithIdentifier:itemViewModel.cellIdentifier forIndexPath:indexPath];
    }
    
    if (cell == nil) {
        [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    if (cell.delegate && [cell.delegate isKindOfClass:RACSubject.class]) {
        @weakify(self)
        [[cell.delegate takeUntil:cell.rac_prepareForReuseSignal] subscribeNext:^(NSDictionary *parames) {
            @strongify(self)
            [self processCellActionWithParames:parames];
        }];
    }
    return  cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    JDISVSettlementAmountFloorBaseCellViewModel *itemViewModel;
    if (tableView == _availableTableView) {
        // 可用
        if (_viewModel.availableCouponItemViewModels && indexPath.section < _viewModel.availableCouponItemViewModels.count) {
            itemViewModel = [_viewModel.availableCouponItemViewModels objectAtIndex:indexPath.section];
        }
    } else {
        // 不可用
        if (_viewModel.unAvailableCouponItemViewModels && indexPath.section < _viewModel.unAvailableCouponItemViewModels.count) {
            itemViewModel = [_viewModel.unAvailableCouponItemViewModels objectAtIndex:indexPath.section];
        }
    }
    if (itemViewModel && [cell respondsToSelector:@selector(updateCellWithViewModel:)]) {
        [cell performSelector:@selector(updateCellWithViewModel:) withObject:itemViewModel];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    [self clickCellIndexOf:indexPath];
}

#pragma mark - Show && Dismiss

- (void)closeAction:(id)sender {

}

- (RACSubject *)delegate {
    if (_delegate == nil) {
        _delegate = [[RACSubject alloc] init];
    }
    return _delegate;
}


#pragma mark - DZNEmptyDataSet
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView {
    if (scrollView == _availableTableView) {
        return !(self.viewModel.availableCouponItemViewModels.count > 0);
    } else {
        return !(self.viewModel.unAvailableCouponItemViewModels.count > 0);
    }
 
}

- (UIView *)customViewForEmptyDataSet:(UIScrollView *)scrollView {
    KAEmptyView *emptyView = [[KAEmptyView alloc] initWithFrame:CGRectMake(18.f, 0, CGRectGetWidth([[UIScreen mainScreen] bounds])-18.f*2, [self.viewModel contentViewHeight]) type:KAEmptyViewTypeNotAction];
    
    NSLayoutConstraint *heightConstraint = [NSLayoutConstraint constraintWithItem:emptyView attribute:NSLayoutAttributeHeight relatedBy:NSLayoutRelationEqual toItem:nil attribute:NSLayoutAttributeNotAnAttribute multiplier:1 constant:self.availableTableView.frame.size.height];
    [emptyView addConstraint:heightConstraint];
    
    if (scrollView == _availableTableView) {
        emptyView.decrible = SettlementLan(@"checkout_coupon_list_available_empty") ;
    } else {
        emptyView.decrible = SettlementLan(@"checkout_coupon_list_unavailable_empty") ;
    }
    emptyView.coverImage = [JDISV_RESOURCE_MANAGER imageWithImageType:JDISVImageTypeNoDataCoupon];
    return emptyView;
}

#pragma mark - Lazy Load
- (JDISVSettlementCouponViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[JDISVSettlementCouponViewModel alloc] init];
    }
    return _viewModel;
}
@end
