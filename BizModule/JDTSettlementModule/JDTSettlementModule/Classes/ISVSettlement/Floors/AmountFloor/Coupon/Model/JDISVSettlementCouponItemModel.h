//
//  JDISVSettlementCouponItemModel.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 模型名称:优惠券Item模型
 模型命名空间:core.trade-FLR#balance.coupon-M#couponSingleFloor
 模型uuid:couponSingleFloor
 */
// 内部Key:C-M#couponSingleFloor&discount

@interface JDISVSettlementCouponItemModel : NSObject
// 券面上文案类型：0、金额 1、单档折扣 2、多档折扣
@property (nonatomic, strong) NSNumber *discountStrType; /**< discountStrType:券面上文案类型 */
@property (nonatomic, copy) NSString *discountStr; /**< discountStr:面值文案¥28.99 */
/*
 *  运费样例：
 *     "discount": "可抵扣6.0000元"。
 *  京券样例:
 *      "discount": "28.99",
 */
@property (nonatomic, copy) NSString *discount; /**< discount:面值（有一些前端格式处理逻辑） */
@property (nonatomic, copy) NSString *maxDiscount; /**< maxdiscount:面值 */

// 内部Key:VCYBB2C-D#basicMsg&basicMsg
@property (nonatomic, copy) NSString *scope; /**< scope:优惠券使用范围描述文案 */
@property (nonatomic, copy) NSString *name; /**< name:限制可用的name */
@property (nonatomic, copy) NSString *couponMsg; /**< couponMsg:优惠券可配文案? */
@property (nonatomic, copy) NSString *belowMsg; /**< 优惠券不可用限制描述 - 展示 */

// 内部Key:C-M#couponSingleFloor&time
@property (nonatomic, copy) NSString *timeEnd; /**< timeEnd:优惠劵过期时间 */
@property (nonatomic, copy) NSString *timeBegin; /**< timeBegin:优惠劵发放时间 */

// 内部Key:VCYBB2C-D#money&money
@property (nonatomic, copy) NSString *quota; /**< quota:定额 3 */
@property (nonatomic, copy) NSString *highDiscount; /**< highDiscount:最高减免0.00 */
@property (nonatomic, copy) NSString *quotaStr; /**< quotaStr:满3元可用 */
// 内部Key:C-M#couponSingleFloor&limitUseInfo
@property (nonatomic, strong) NSNumber *couponPlatform; /**< couponPlatform:优惠券平台属性:1 限平台，0 全平台 入参 */
@property (nonatomic, copy) NSString *descCode; /**< descCode:优惠券不可用原因描述码 */
@property (nonatomic, strong) NSNumber *couponType; /**< couponType:优惠券类型 入参 */
@property (nonatomic, strong) NSNumber *venderId; /**< venderId:限店铺券店铺id */

@property (nonatomic, strong) NSNumber *couponStyle; /**< couponStyle:优惠券样式 入参 */

//优惠券类型 userLabel，消费券：307-1，会员券：500-1
@property (nonatomic, strong) NSString *userLabel;
//0-全品类，1-限品类，2-限店铺，3-店铺限商品券
//0,1 platform 2,3 shop
@property (nonatomic, strong) NSNumber *couponLimitType; /**< couponLimitType:优惠券限制类型 */
@property (nonatomic, copy) NSString *couponLimitInfo; /**< couponLimitInfo:券使用限制信息 */

@property (nonatomic, copy) NSString *limitDesc; /**< limitDesc:*/

//couponcode info
@property (nonatomic, copy) NSString *activityId;
@property (nonatomic, copy) NSString *codeBatchId;
@property (nonatomic, copy) NSString *couponCode;
@property (nonatomic, assign) BOOL couponCodeFlag;

// 内部Key:C-M#couponSingleFloor&core
// 1可用 2、3、4不可用券
@property (nonatomic, copy) NSString *classify; /**< classify:分类 1-可用 2-未到使用时间 3-可凑单 4-其他 */
@property (nonatomic) BOOL canUsed; /**< canUsed:当前优惠券能否使用 */
@property (nonatomic) BOOL isReadOnly; /**< readOnly:是否置灰（可用列表） */
@property (nonatomic, copy) NSString *couponId; /**< id:优惠券编号 入参 */
@property (nonatomic, copy) NSString *batchId; /**< batchId:批次Id */
@property (nonatomic, copy) NSString *couponKey; /**< key:优惠券KEY 入参 */
@property (nonatomic) BOOL selected; /**< selected:是否选中 */

@property (nonatomic,assign) NSInteger orderIndex;
@property (nonatomic,assign) BOOL isConflict;
@property (nonatomic,assign) BOOL isQuery;
@property (nonatomic,assign) BOOL canUseCode;

@property (nonatomic,assign) NSInteger couponCodeDiscount;
@end

NS_ASSUME_NONNULL_END
