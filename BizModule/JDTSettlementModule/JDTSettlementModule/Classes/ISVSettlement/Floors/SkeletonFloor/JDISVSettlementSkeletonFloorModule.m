//
//  JDISVSettlementSkeletonFloorModule.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementSkeletonFloorModule.h"

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>

#import "JDISVSettlementSkeletonFloor.h"

JDISVRegisterFloorModule(KaCheckSkeletonFloor, JDISVSettlementSkeletonFloorModule)

@implementation JDISVSettlementSkeletonFloorModule
- (UIView *)floorView {
    UIView* v = [[JDISVSettlementSkeletonFloor alloc] init];
//    v.backgroundColor = [UIColor redColor];
    return v;
}

- (CGFloat)floorHeight {
    return [UIScreen mainScreen].bounds.size.height - [self isv_pd_navigationBar_safeAreaInsets].top - 44.f;
}

- (JDISVFloorType)floorType{
    return JDISVFloorTypePreloadingFloor;
}

- (CGFloat)floorTopMarge {
    CGFloat statusBarHeight = [self isv_pd_navigationBar_safeAreaInsets].top;
    return statusBarHeight + 44;
}

- (void)updateData:(NSDictionary *)fullDic commond:(JDISVFloorCommonModel *)commonModel {
}

#pragma mark - private
- (UIEdgeInsets)isv_pd_navigationBar_safeAreaInsets {
    if (@available(iOS 11.0, *)) {
        UIEdgeInsets safeAreaInsets = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
        if (safeAreaInsets.bottom > 0) {
            return safeAreaInsets;
        }
        return UIEdgeInsetsMake(20, 0, 0, 0);
    }
    return UIEdgeInsetsMake(20, 0, 0, 0);
}
@end
