//
//  JDISVSettlementSkeletonFloor.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementSkeletonFloor.h"

#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

@implementation JDISVSettlementSkeletonFloor
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        UIImageView *imageView = [[UIImageView alloc] initWithImage:[JDISV_RESOURCE_MANAGER imageWithImageType:JDISVImageTypeSkeletonSettlement]];
        imageView.contentMode = UIViewContentModeScaleToFill;
        
        [self addSubview:imageView];
        
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(self);
        }];
    }
    return self;
}

- (void)floorDidLoad:(nonnull id<JDISVFloorModuleProtocol>)floorModel {
    
}


@end
