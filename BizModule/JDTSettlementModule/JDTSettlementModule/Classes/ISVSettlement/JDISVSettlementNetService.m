//
//  JDISVSettlementNetService.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/14.
//

#import "JDISVSettlementNetService.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

@implementation JDISVSettlementNetService
+ (instancetype)sharedService {
    static JDISVSettlementNetService * _sharedService = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedService = [[JDISVSettlementNetService alloc] init];
    });
    
    return _sharedService;
}

- (NSURLSessionDataTask * _Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                     requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                                      path:(NSString *)urlPath
                                  function:(NSString * _Nonnull)functionId
                                    version:(NSString * _Nonnull)version
                                parameters:(NSDictionary * _Nullable)parameters
                                  complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock{
    return [self request:requestType requestSerializerType:requestSerializerType paramterType:JDISVColorGateParameterTypeDefault path:urlPath function:functionId version:version parameters:[parameters copy] complete:completeBlock];
}

- (NSURLSessionDataTask * _Nonnull)request:(JDCDHTTPSessionRequestType)requestType
                     requestSerializerType:(JDCDURLRequestSerializerType)requestSerializerType
                              paramterType:(JDISVColorGateParameterType)paramterType
                                      path:(NSString *)urlPath
                                  function:(NSString * _Nonnull)functionId
                                    version:(NSString * _Nonnull)version
                                parameters:(NSDictionary * _Nullable)parameters
                                  complete:(nullable void (^)(JDCDURLTask * _Nonnull urlTask, id _Nullable responseObject, NSError * _Nullable error))completeBlock{
    NSMutableDictionary *mutableParams = parameters.mutableCopy;
    
    //访客结算：添加入参, 所有接口都要这些入参
    if (SettlementNetService.isVistor) {
//        [mutableParams setObject:@"true" forKey:@"visitor"];
        NSDictionary *orderStr = [parameters objectForKey:@"orderStr"] ? : @{};
        
        NSMutableDictionary *mutableOrderStr = orderStr.mutableCopy;
        if ([mutableOrderStr objectForKey:@"useBestCoupon"]) {
            [mutableOrderStr setObject:@(NO) forKey:@"useBestCoupon"];//访客结算不使用智能选券
        }
        [mutableOrderStr setObject:[PlatformService getUUID] ? : @"" forKey:@"visitorUuid"];//购物车访客uuid
        [mutableOrderStr addEntriesFromDictionary:self.vistorParams];
        [mutableParams addEntriesFromDictionary:@{@"orderStr":mutableOrderStr}];
        [mutableParams setObject: @"true" forKey:@"mergeCart"];
    }
    
    parameters = [mutableParams copy];
    
    return [[JDISVPlatformService sharedService] request:requestType requestSerializerType:requestSerializerType paramterType:paramterType path:urlPath function:functionId version:version parameters:[parameters copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        if (completeBlock) {
            completeBlock(urlTask, responseObject, error);
        }
    }];
}

@end
