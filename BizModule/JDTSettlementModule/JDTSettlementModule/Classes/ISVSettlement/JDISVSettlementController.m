//
//  JDISVSettlementController.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2022/3/7.
//

#import "JDISVSettlementController.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>
#import <JDISVIQKeyboardManagerModule/JDISVIQKeyboardManagerModule-umbrella.h>

#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVYYModelModule/JDISVYYModelModule-umbrella.h>

#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>

#import "JDISVSettlementViewModel.h"
#import "JDISVSettlementTracker.h"
#import "JDISVSettlementAmountFloorNetService.h"
@import JDTCommonToolModule;

@interface JDISVSettlementController ()<JDCDISVActionDelegate>
@property (nonatomic, assign) JDISVSettlementSourceType sourceType;
@property (nonatomic, strong) NSDictionary *routerParams;
@property (nonatomic, strong) JDISVFloorListView *listView;
@property (nonatomic, strong) JDISVSettlementViewModel *viewModel;

@property (nonatomic, strong) UIViewController *guestLoginVC;
@property (nonatomic, strong) UIViewController *guestAddressVC;
@end

@implementation JDISVSettlementController

- (instancetype)initWithSourceType:(NSInteger)sourceType params:(NSDictionary *)param
{
    self = [super init];
    if (self) {
        _sourceType = sourceType;
        _routerParams = param;
        KASettlementAmountFloorNetService.isCheckoutPageFirstLoadData = 0;//重置进入次数
//          NSDictionary *commonParam = @{@"pin": [[JDISVPlatformService sharedService] getUserPin],
//                                        @"client": @"pc",
//                                        @"build": @"1",
//                                        @"clientVersion": @"1.0.0",
//                                        @"lang":@"zh_CN",
//                                        @"area": [self getDefaultAddressAreadId]
//          };
//
//          NSDictionary *commonBodyParam = @{@"businessTag": @"cn_ybxt_b2c",
//                                            @"buld":@"405",
//                                            @"verticalTag": @"cn_ybxt_b2c"
//          };
//          [[JDISVPlatformService sharedService] setNetWorkB2CCommomParam:commonParam andCommonBodyParam:commonBodyParam];
        
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:YES animated:YES];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    self.navigationController.interactivePopGestureRecognizer.delegate = (id)self;
    
    // IQKeyBoard
    [IQKeyboardManager sharedManager].enable = YES;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = YES;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    [self setUpUI];
    
//    [self loadInvoiceListData];//进入主页面获取一下发票列表接口

    //访客标识，结算VC生命周期只初始化一次
//    SettlementNetService.isVistor = ![PlatformService getUserIsLogin];
    
//    if (SettlementNetService.isVistor) {
//        [self goToGuestLogin];
//    }else{
        [self loadData];
//    }

    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(getSkuid:) name:@"shopInfosMTA" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(autoUseCouponCode:) name:@"JDISVSettlementAutoUseCouponCode" object:nil];
}

#pragma mark - 访客相关
- (void)goToGuestLogin{
    @weakify(self)
    UIViewController *vc  = [JDRouter openURL:@"router://KSALoginModule/guestLoginViewController" arg:nil error:nil completion:^(id  _Nullable object) {
        NSLog(@"%@",object);
        NSDictionary *loginDic = object;
        NSNumber *backClick = [loginDic objectForKey:@"backClick"];
        @strongify(self)
        if (backClick.boolValue) {
            [self.navigationController popViewControllerAnimated:YES];
        } else {
            NSString *email = [loginDic objectForKey:@"email"];
            NSString *mobile = [loginDic objectForKey:@"mobile"];
            if ([email jdcd_validateString]) {
                SettlementNetService.vistorParams = @{@"email":email};
            } else if ([mobile jdcd_validateString]) {
                SettlementNetService.vistorParams = @{@"mobile":mobile};
            }
            
            //WT_TODO
            [self.guestAddressVC.view removeFromSuperview];
            [self.guestAddressVC removeFromParentViewController];
            self.guestAddressVC = nil;
            [self.guestLoginVC.view removeFromSuperview];
            [self.guestLoginVC removeFromParentViewController];
            self.guestLoginVC = nil;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                @strongify(self)
                [self loadData];
            });
        }
    }];
    if (vc){
        self.guestLoginVC = vc;
        vc.view.frame = CGRectMake(0, 0, self.view.bounds.size.width, self.view.bounds.size.height);
        [self addChildViewController:vc];
        [self.view addSubview:vc.view];
    }
}

- (void)goToGuestAddress{
    @weakify(self)
    UIViewController *vc  = [JDRouter openURL:@"router://KSAAddressModule/guestAddAddressViewController" arg:nil error:nil completion:^(id  _Nullable object) {
        @strongify(self)
        NSDictionary *addressDic = object;
        NSNumber *backClick = [addressDic objectForKey:@"backClick"];
        if (backClick.boolValue) {
            [self.navigationController popViewControllerAnimated:YES];
        } else {
            [self.guestAddressVC.view removeFromSuperview];
            [self.guestAddressVC removeFromParentViewController];
            self.guestAddressVC = nil;
            [self.guestLoginVC.view removeFromSuperview];
            [self.guestLoginVC removeFromParentViewController];
            self.guestLoginVC = nil;
            NSMutableDictionary *tempDic = SettlementNetService.vistorParams.mutableCopy;
            [tempDic addEntriesFromDictionary:addressDic];
            SettlementNetService.vistorParams = tempDic.copy;
            //访客结算不需要走地址接口校验逻辑，这里直接调结算主接口
            [self loadMainDataWithAddressId:nil];

        }
    }];
    if (vc) {
        self.guestAddressVC = vc;
        vc.view.frame = CGRectMake(0, 0,self.view.bounds.size.width,self.view.bounds.size.height);
        [self addChildViewController:vc];
        [self.view addSubview:vc.view];
    }
}

- (void)goToGuestModifyAddress{
    NSMutableDictionary *arg = [[NSMutableDictionary alloc]init];
    [arg setObject:@(YES) forKey:@"isModifyAddress"];
    if (SettlementNetService.vistorParams) {
        [arg setObject:SettlementNetService.vistorParams forKey:@"addressInfo"];
    }
    @weakify(self)
    UIViewController *vc  = [JDRouter openURL:@"router://KSAAddressModule/guestAddAddressViewController" arg:arg error:nil completion:^(id  _Nullable object) {
        @strongify(self)
        NSDictionary *addressDic = object;
        NSMutableDictionary *tempDic = SettlementNetService.vistorParams.mutableCopy;
        [tempDic addEntriesFromDictionary:addressDic];
        SettlementNetService.vistorParams = tempDic.copy;
        //访客修改地址，访客结算不需要走地址接口校验逻辑，这里直接调结算主接口
        [self loadMainDataWithAddressId:nil];
        
    }];
    if (vc) {
        //访客修改地址，底部弹出
        UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController:vc];
        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:nav presentingViewController:self];
        presentationVC.backgroundColor = [UIColor clearColor];
        presentationVC.type = KAFloatLayerTypeCustom;
        presentationVC.contentHeight = self.view.bounds.size.height;
        presentationVC.insets = UIEdgeInsetsZero;
//            presentationVC.animationDirection = KAFloatLayerDirectionRigthToLeft;
        nav.transitioningDelegate = presentationVC;
        [self presentViewController:nav animated:YES completion:nil];
    }
}

- (void)getSkuid:(NSNotification *)fication{
    //埋点
    NSArray *shopInfos = fication.object;
    if (shopInfos) {
        NSDictionary *mtaParam = @{@"page_id":@"SettleAccounts_OrderNew",
                                 @"shopinfo":shopInfos};
        [JDISVSettlementTracker PV:@"SettleAccounts_OrderNew"
                            param:mtaParam];
    }
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"shopInfosMTA" object:nil];
}

- (void)autoUseCouponCode:(NSNotification *)notification{
    self.viewModel.useBestCoupon = NO;
    [self.viewModel processFloorData:[notification.object jdcd_getDicElementForKey:@"value"]];
    NSMutableArray *floorGroups = [self.viewModel.floorGroups mutableCopy];
    [self.listView configData:floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
    [self.listView reloadData];
}

- (void)showFirstMoneyAction{
    [KAAlert alert].config
    .renderW3a(SettlementLan(@"checkout_dialog_pre_sale_title"), NSTextAlignmentCenter)
    .ksa_addLineAction(SettlementLan(@"checkout_dialog_pre_sale_negative") , ^{
        // 不做任何事
        NSLog(SettlementLan(@"checkout_dialog_pre_sale_negative") );
    })
    .ksa_addFillAction(SettlementLan(@"checkout_dialog_pre_sale_positive") , ^{
        NSLog(SettlementLan(@"checkout_dialog_pre_sale_positive") );
    })
    .alertShow();
}

- (void)setUpUI {
    self.listView.backgroundColor =  [UIColor clearColor];
    
    CGFloat w3 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"];
    self.listView.listInsets = UIEdgeInsetsMake(0, w3, 0, w3);
    
    [self.view addSubview:self.listView];
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.view);
    }];
    
    NSArray *preloadingData = [self.viewModel preloadingFloorData];
    [self.listView configData:preloadingData allFloorOriginData:nil];
    [self.listView reloadData];
}

// 获取默认地址 areadId
- (NSString *)getDefaultAddressAreadId {
    
    JDTAddressItemModel *addressModel = [PlatformService getDefaultAddress];
    if(addressModel.addressId == 0)
        return nil;
    return @(addressModel.addressId).stringValue;
}

- (void)loadData {
    @weakify(self);
    [[[self.viewModel loadAddressListSignal] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSArray <JDTAddressItemModel *> * _Nullable x) {
        // 本地缓存是否有详细地址
        NSArray *addressList = (NSArray *)x;
        if ([[PlatformService getDefaultAddress].addressDetail jdcd_validateString]) {
            // 选中缓存中的详细地址
            NSString *ID = [self getDefaultAddressAreadId];
            if (ID.length == 0) {
                // 选中第一条地址
                JDTAddressItemModel *firstAddress = [addressList firstObject];
                NSString *addressId = [@(firstAddress.addressId) stringValue];
                [self loadMainDataWithAddressId:addressId];
            } else {
                [self loadMainDataWithAddressId:ID];
            }
        } else {
            // 是否有级联地址
            if ([PlatformService getDefaultAddress]) {
                JDTAddressItemModel *matchAddress;
                for (JDTAddressItemModel *address in addressList) {
                    if ([PlatformService getDefaultAddress].addressId == address.addressId) {
                        matchAddress = address;
                        break;
                    }
                }
                if (matchAddress && [matchAddress.addressDetail jdcd_validateString]) {
                    // 缓存的级联地址 有匹配地址列表的详细地址
                    // 选中默认地址 并且弹框提示是否更换地址
                    NSString *switchAddressURL = [NSString stringWithFormat:@"router://%@/switchAddress", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
                    [JDRouter openURL:switchAddressURL arg:nil error:nil completion:^(NSDictionary *object) {
                        //
                        NSString *type = [object objectForKey:@"type"];
                        if ([type isEqualToString:@"create"]) {
                            // 新建
                            [self addAddressAction];
                        } else if ([type isEqualToString:@"select"]) {
                            // 选择
                            NSNumber *addressId = [object objectForKey:@"address"];
                            NSString *addressListURL = [NSString stringWithFormat:@"router://%@/addressList", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
                            [JDRouter openURL:addressListURL arg:nil error:nil completion:^(id  _Nullable object) {
                                @strongify(self)
                                NSArray *addressList = (NSArray *)[object objectForKey:@"addressList"];
                                if (addressList && addressList.count > 0) {
                                    JDTAddressItemModel *targetAddress;
                                    for (JDTAddressItemModel *addressModel in addressList) {
                                        if (targetAddress.addressId == addressId.integerValue) {
                                            targetAddress = addressModel;
                                            break;
                                        }
                                    }
                                    if (targetAddress) {
                                        [self loadSaveAddress:targetAddress];
                                    }
                                }
                            }];
                        } else {
                            // error
                            NSError *error = [object objectForKey:@"errorInfo"];
                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
                            [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
                            [self.listView reloadData];
                        }
                    }];
                } else {
                    // 并且弹框提示是否新建地址
                    [KAAlert alert].config
                    .renderW3(SettlementLan(@"checkout_dialog_no_address_content") )
                    .addLineAction(SettlementLan(@"checkout_exit_dialog_cancel") , ^{
                        // 不做任何事
                    })
                    .addFillAction(SettlementLan(@"setllement_new_built") , ^{
                        [self addAddressAction];
                    })
                    .alertShow();
                }
                // 选中默认地址
                NSString *addressId = @([PlatformService getDefaultAddress].addressId).stringValue;
                [self loadMainDataWithAddressId:addressId];
            } else {
                // 选中第一条地址
                JDTAddressItemModel *firstAddress = [addressList firstObject];
                NSString *addressId = [@(firstAddress.addressId) stringValue];
                [self loadMainDataWithAddressId:addressId];
            }
        }
    } error:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
        [KAAlert alert].config
        .renderW3(SettlementLan(@"setllement_address_empty") )
        .addLineAction(SettlementLan(@"checkout_exit_dialog_cancel") , ^{
            // 不做任何事
        })
        .addFillAction(SettlementLan(@"checkout_no_address_positive") , ^{
            [self addAddressAction];
        })
        .alertShow();
    }];
}

// MARK:请求主接口
- (void)loadMainDataWithAddressId:(NSString *)addressId{
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[[self.viewModel loadMainDataSignalWithAddressId:addressId]  takeUntil:self.rac_willDeallocSignal] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        
        [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
    } completed:^{
        @strongify(self)
        [PlatformService dismissInView:self.view];
        NSMutableArray *floorGroups = [self.viewModel.floorGroups mutableCopy];
        [self.listView configData:floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
    }];
}


- (void)loadInvoiceListData{
    @weakify(self)
//    [PlatformService showLoadingInView:self.view];
    [[self.viewModel queryInvoiceListSignal] subscribeError:^(NSError * _Nullable error) {
//        @strongify(self)
//        [PlatformService dismissInView:self.view];
//        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
    } completed:^{
        @strongify(self)
//        [PlatformService dismissInView:self.view];
        if (self.viewModel.allFloorOriginData.allKeys.count > 0) {
            //发票数据插入列表数据后，不需刷主接口，只刷列表
            [self.viewModel processFloorData:self.viewModel.allFloorOriginData];
            NSMutableArray *floorGroups = [self.viewModel.floorGroups mutableCopy];
            [self.listView configData:floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
            [self.listView reloadData];
        }
    }];
}

// MARK:保存地址
- (void)loadSaveAddress:(JDTAddressItemModel *)address {
    @weakify(self)
    [PlatformService showLoadingInView:self.view];
    [[self.viewModel loadSaveConsigneeAddressSignalWithId:[@(address.addressId) stringValue]] subscribeError:^(NSError * _Nullable error) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        } completed:^{
            @strongify(self)
            [PlatformService dismissInView:self.view];
            [PlatformService setDefaultAddress:address];
            [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
            [self.listView reloadData];
        }];
    
}

// MARK:新增地址
- (void)addAddressAction {
    @weakify(self)
    NSString *addAddressViewControllerURL = [NSString stringWithFormat:@"router://%@/addAddressViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
    [JDRouter openURL:addAddressViewControllerURL arg:nil error:nil completion:^(NSDictionary *object) {
            // code
            UIViewController *vc = [object objectForKey:@"viewController"];
            if (vc) {
                [self.navigationController pushViewController:vc animated:YES];
            }
            NSNumber *addrId = [object objectForKey:@"info"];
            if (addrId) {
                NSString *addressListURL = [NSString stringWithFormat:@"router://%@/addressList", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
                [JDRouter openURL:addressListURL arg:nil error:nil completion:^(id  _Nullable object) {
                    @strongify(self)
                    NSArray *addressList = (NSArray *)[object objectForKey:@"addressList"];
                    if (addressList && addressList.count > 0) {
                        JDTAddressItemModel *targetAddress;
                        for (JDTAddressItemModel *addressModel in addressList) {
                            if (addressModel.addressId == addrId.integerValue) {
                                targetAddress = addressModel;
                                break;
                            }
                        }
                        if (targetAddress) {
                            [PlatformService setDefaultAddress:targetAddress];
                            if (self.viewModel.noAnyAddress) {
                                //无地址新建地址后，调用结算主接口刷新页面
                                self.viewModel.noAnyAddress = NO;
                                [self loadData];
                            }else{
                                [self loadSaveAddress:targetAddress];
                            }
                        }
                    }
                }];
            }
        }];
}

// MARK:切换地址
- (void)changeAddressWithSelectedId:(NSNumber *)addressId {
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];

    [arg addEntriesFromDictionary:@{ @"canSelect": @1 }];

    if (addressId) {
        [arg addEntriesFromDictionary:@{ @"addressId": addressId }];
    }
    [arg addEntriesFromDictionary:@{ @"backVC": self }];
    
    NSString *addressListViewControllerURL = [NSString stringWithFormat:@"router://%@/addressListViewController", [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeAddress)]];
    [JDRouter openURL:addressListViewControllerURL arg:[arg copy] error:nil completion:^(NSDictionary *object) {
        UIViewController *vc = [object objectForKey:@"viewController"];
        if (vc) {
            [self.navigationController pushViewController:vc animated:YES];
        }
        JDTAddressItemModel *selectedAddress = [object objectForKey:@"info"];
        if (selectedAddress) {
            // code
            JDTAddressItemModel *addrObj = nil;
            if([selectedAddress isKindOfClass:[NSDictionary class]] || [selectedAddress isKindOfClass:[NSMutableDictionary class]]){
                NSDictionary *addrDic = (NSDictionary *)selectedAddress;
                addrObj = [JDTAddressItemModel yy_modelWithDictionary:addrDic];
                self.selectBillingAddressId = [addrDic objectForKey:@"billingAddrId"];
                self.viewModel.billingAdrressId = [addrDic objectForKey:@"billingAddrId"];
                [self.listView reloadData];
            } else {
                addrObj = selectedAddress;
            }
            [PlatformService setDefaultAddress:addrObj];
            [self loadSaveAddress:addrObj];
        }
    }];
}

#pragma mark - JDCDISVActionDelegate
- (void)action:(JDCDISVAction *)action {
    if ([action.actionType isEqualToString:@"JDISVAddressFloorChangeAddressCompleteAction"]) {
        // 更改地址完成刷新list
        NSDictionary *data = action.value;
        [self.viewModel processFloorData:data];
        [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
    } else if ([action.actionType isEqualToString:@"JDISVShopFloorSaveShipmentCompleteAction"]) {
        // 更改物流方式后刷新list
        NSDictionary *data = action.value;
        [self.viewModel processFloorData:data];
        [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorOpenPointPageUsePointRefresh"]) {
        // 使用积分后刷新页面
        NSDictionary *data = action.value;
        [self.viewModel processFloorData:data];
        [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorSelectInvoiceRequestAndRefresh"]) {
        // 选择发票后调获取发票列表接口，并刷新页面
        [self loadInvoiceListData];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorPresaleEndPaymentUseCouponRefresh"]) {
        // //预售付尾款，选择优惠券，更新数据后并刷新页面
        self.viewModel.useBestCoupon = NO;//用户操作优惠券浮层后，不再使用智能选券
        NSArray *couponIds = (NSArray *)action.value;
        self.viewModel.couponIds = couponIds;
        [self loadMainDataWithAddressId:nil];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorUseCouponRequestAndRefresh"]) {
        // 使用优惠券后请求数据并刷新页面
        self.viewModel.useBestCoupon = NO;//用户操作优惠券浮层后，不再使用智能选券
        NSString *addressId = [(NSDictionary *)action.value objectForKey:@"addressId"];
        NSString *mobile = [(NSDictionary *)action.value objectForKey:@"mobile"];
        [self loadMainDataWithAddressId:addressId];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorResetCoupons"]) {
        // 重置优惠券 并刷新
        NSDictionary *param = action.value;
        @weakify(self)
        [PlatformService showLoadingInView:self.view];
        [[self.viewModel loadResetAllCouponsParamOf:param] subscribeError:^(NSError * _Nullable error) {
            @strongify(self)
            [PlatformService dismissInView:self.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:error.localizedDescription];
        } completed:^{
            @strongify(self)
            [PlatformService dismissInView:self.view];
            [self.listView configData:self.viewModel.floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
            [self.listView reloadData];
        }];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementBottomFloorAddressfloorClickChangeAddressAction"]) {
         // 点击地址楼层 - 修改地址
         NSString *addressId = action.value;
         NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
         [self changeAddressWithSelectedId:[numberFormatter numberFromString:addressId]];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementAddressFloorClickVistorModifyAddressAction"]) {
        // 地址楼层 - 访客修改地址
        [self goToGuestModifyAddress];
   } else if ([action.actionType isEqualToString:@"JDISVSettlementBottomFloorSubmitActionChangeAddressAction"]) {
        // 提交订单 无货 - 修改地址
        NSString *addressId = [(NSDictionary *)action.value objectForKey:@"addressId"];
        NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
        [self changeAddressWithSelectedId:[numberFormatter numberFromString:addressId]];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementBottomFloorSubmitActionRefreshAfterRemoveStockoutAction"]) {
        // 移除无货刷新
        NSString *addressId = [(NSDictionary *)action.value objectForKey:@"addressId"];
        NSString *mobile = [(NSDictionary *)action.value objectForKey:@"mobile"];
        [self loadMainDataWithAddressId:addressId];
    } else if ([action.actionType isEqualToString:@"JDISVSettlementEmptyFloorRequestAction"]) {
        NSNumber *refreshType = (NSNumber *)action.value;
        if ([refreshType integerValue] == 1) {
            // 添加地址
            [self addAddressAction];
        } else {
            [self loadData];
            
        }
    } else if ([action.actionType isEqualToString:kJDCDISVFloorScrollAction]) {
//        [[IQKeyboardManager sharedManager] resignFirstResponder];
    } else if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]) {
        [[IQKeyboardManager sharedManager] resignFirstResponder];
    }else if([action.actionType isEqualToString:@"JDISVSettlementFirstMoneyInfoFloorMoudle"]){
        self.isAgree = !self.isAgree;
        [[NSNotificationCenter defaultCenter] postNotificationName:@"changeSwitchMode" object:@(self.isAgree)];
    }else if([action.actionType isEqualToString:@"JDISVSettlementBottomConfirmAction"]){
        [self showFirstMoneyAction];
    }else if ([action.actionType isEqualToString:@"JDISVSettlementAmountFloorResetDiscountCodeExchang"]){//优惠码兑换/删除成功
        //使用优惠码后，请求求数据并刷新页面
        self.viewModel.useBestCoupon = NO;//使用了优惠码后，取消自动选券逻辑
        [self.viewModel processFloorData:action.value];
        NSMutableArray *floorGroups = [self.viewModel.floorGroups mutableCopy];
        [self.listView configData:floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
        [self.listView reloadData];
    }else if ([action.actionType isEqualToString:@"JDISVPaymentTypeChangedAction"]){//更改支付方式
        @weakify(self)
        BOOL isshopCart = self.viewModel.source == JDISVSettlementSourceTypeCart ? NO : YES;
        [PlatformService showLoadingInView:self.view];
        [[self.viewModel changedPaymentType:@{@"orderStr":action.value,@"immediatelyBuy":@(isshopCart),@"isPresale":@(ISPresell),@"ptFlag":@(ISGroupBuy)}] subscribeNext:^(id  _Nullable x) {
            @strongify(self)
            [PlatformService dismissInView:self.view];
            [self.viewModel processFloorData:x];
            NSMutableArray *floorGroups = [self.viewModel.floorGroups mutableCopy];
            [self.listView configData:floorGroups allFloorOriginData:self.viewModel.allFloorOriginData];
            [self.listView reloadData];
        } error:^(NSError * _Nullable error) {
            @strongify(self)
            [PlatformService dismissInView:self.view];
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage showTime:2.0 message:error.description];
        }];
        
    }
}

#pragma mark - getter
-(JDISVFloorListView *)listView {
    if (!_listView) {
        _listView = [[JDISVFloorListView alloc] initWithFrame:CGRectZero ceiling:NO formPageId:@"KaCheckPageId"];
        _listView.controller = self;
        _listView.actionDelegate = self;
    }
    
    return _listView;
}

- (JDISVSettlementViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[JDISVSettlementViewModel alloc] initWithSource:_sourceType];
        _viewModel.routerParams = _routerParams;
        _viewModel.useBestCoupon = YES;//默认智能选券
        _viewModel.isLoc = self.isLoc;
    }
    
    return _viewModel;
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


@end
