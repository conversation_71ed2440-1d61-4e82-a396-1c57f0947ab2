//
//  JDISVSettlementProtocolHandler.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2021/12/28.
//

#import "JDISVSettlementProtocolHandler.h"
#import <JDBRouterModule/JDRouter.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@JDProtocolHandlerRegister(jump_settlement,JDISVSettlementProtocolHandler);

/*
openapp.xxkaapp://virtual?params={"category":"jump","des":"settlement","sourceType":"0"}
openapp.xxkaapp://virtual?params={"category":"jump","des":"settlement","sourceType":"1", "skuId":"XXX", "skuCount":"XXX"}
openapp.xxkaapp://virtual?params={"category":"jump","des":"settlement","sourceType":"2","shareBuyId":"XXX", "activityId":"XXX"}
// sourceType: 0-购物车 1-商详/砍价/非购物车进入 2-拼团结算页
// 如果sourceType=1时，必须传skuCount和skuId参数
// 如果sourceType=2时，必须传入shareBuyId="738947p3749237"和activityId="738947"
*/

@implementation JDISVSettlementProtocolHandler
- (BOOL)handleProtocolWithWebView:(id)webView completion:(JDProtocolHandleFinishedBlock)completion {
    [super handleProtocolWithWebView:webView completion:completion];
    NSDictionary *params = self.model.params;
    
    NSString *sourceType = @"";
    if ([[params objectForKey:@"sourceType"] isKindOfClass:NSNumber.class]) {
        sourceType = [[params objectForKey:@"sourceType"] stringValue];
    } else if ([[params objectForKey:@"sourceType"] isKindOfClass:NSString.class]) {
        sourceType = [params objectForKey:@"sourceType"];
    } else {
        return NO;
    }
    NSString *skuId = @"";
    if ([[params objectForKey:@"skuId"] isKindOfClass:NSNumber.class]) {
        skuId = [[params objectForKey:@"skuId"] stringValue];
    } else if ([[params objectForKey:@"skuId"] isKindOfClass:NSString.class]) {
        skuId = [params objectForKey:@"skuId"];
    } else {
        return NO;
    }
    NSString *skuCount = @"";
    if ([[params objectForKey:@"skuCount"] isKindOfClass:NSNumber.class]) {
        skuCount = [[params objectForKey:@"skuCount"] stringValue];
    } else if ([[params objectForKey:@"skuCount"] isKindOfClass:NSString.class]) {
        skuCount = [params objectForKey:@"skuCount"];
    } else {
        return NO;
    }
    
    NSMutableDictionary *arg = [NSMutableDictionary dictionary];
    if ([sourceType integerValue] == 0) {
        [arg addEntriesFromDictionary: @{@"sourceType":@(0)}];
    } else if ([sourceType integerValue] == 1){
        [arg addEntriesFromDictionary: @{@"sourceType":@(1)}];
        if ([skuId jdcd_validateString]) {
            [arg addEntriesFromDictionary: @{@"skuId":skuId}];
        }
        
        if ([skuCount jdcd_validateString]) {
            [arg addEntriesFromDictionary: @{@"skuCount":skuCount}];
        }
    } else if ([sourceType integerValue] == 2){//拼团结算页
        [arg addEntriesFromDictionary: @{@"sourceType":@(1)}];//按从商详进结算处理
        if ([skuId jdcd_validateString]) {
            [arg addEntriesFromDictionary: @{@"skuId":skuId}];
        }
        
        if ([skuCount jdcd_validateString]) {
            [arg addEntriesFromDictionary: @{@"skuCount":skuCount}];
        }
        //eg: shareBuyId="738947p3749237"和activityId="738947"
        NSString *shareBuyId = [params objectForKey:@"shareBuyId"];
        if ([shareBuyId jdcd_validateString]) {
            [arg addEntriesFromDictionary: @{@"taskId":shareBuyId}];
            [arg addEntriesFromDictionary: @{@"ptFlag":@(YES)}];
        }
    }
    
    @weakify(self)
    [PlatformService showLoadingInView: [PlatformService getTopViewController].view];
    [JDRouter openURL:@"router://JDISVSettlementModule/settlementController" arg:arg error:nil completion:^(id  _Nullable object) {
        @strongify(self)
        [PlatformService dismissInView:[PlatformService getTopViewController].view];
        NSNumber *code = [object objectForKey:@"code"];
        if ([code integerValue] == 0) {
            // 成功
            UIViewController *vc = [object objectForKey:@"result"];
            if (vc) {
                [[JDISVSettlementProtocolHandler navigator] pushViewController:vc animated:YES];
            }
        } else {
            // 失败
            NSString *message = [object objectForKey:@"result"];
            if ([message jdcd_validateString]) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:message];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:SettlementLan(@"checkout_group_buy_network_error") ];
            };
        }
    }];
    
    return YES;
}

+ (UINavigationController *)navigator {
    
    UIViewController *resultController = [UIApplication
                                                sharedApplication].delegate.window.rootViewController;
    if([resultController isKindOfClass:UITabBarController.class]){
        UITabBarController *tabController = (UITabBarController*) resultController;
        return tabController.selectedViewController;
    } else if ([resultController isKindOfClass:UINavigationController.class]) {
        return (UINavigationController *)resultController;
    }
    return nil;
}
@end
