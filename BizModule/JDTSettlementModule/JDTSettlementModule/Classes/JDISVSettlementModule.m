//
//  JDISVSettlementModule.m
//
//
// 组件输出类, 可引入JDRouter组件, 进行组件间通信

#import <Foundation/Foundation.h>
#import "JDISVSettlementModule.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import "JDISVSettlementController.h"
#import "JDISVSettlementViewModel.h"
#import "JDISVSettlementOrderSuccessController.h"
#import "NSBundle+JDISVSettlement.h"

@implementation JDISVSettlementModule

/// 打开结算页
JDROUTER_EXTERN_METHOD(JDISVSettlementModule, settlementController, arg, callback) {
    NSNumber *sourceFrom = [arg objectForKey:@"sourceType"];
   __block NSString *skuId = @"";
    __block NSString *skuCount = @"";
    NSNumber *isLocNum = [arg objectForKey:@"isLoc"];
    BOOL isLoc = isLocNum.boolValue;
//    if(!PlatformService.getUserIsLogin){
//        NSString* loginModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeLogin ];
//        NSString* routerStr= [NSString stringWithFormat:@"router://%@/excuteAfterLogin",loginModule];
//        [JDRouter openURL:routerStr
//                      arg:nil
//                    error:nil
//               completion:^(NSNumber* success){
//            if(success.boolValue){
//                [self routerHandle_JDISVSettlementModule_settlementController:arg callback:callback];
//            }
//        }
//        ];
//        return nil;
//    }
    
    if ([sourceFrom integerValue] == 0) {
        // 购物车进入
        // 离散化结算页
        JDISVSettlementController *settlementController = [[JDISVSettlementController alloc] initWithSourceType:[sourceFrom integerValue] params:arg];
        settlementController.isLoc = isLoc;
        NSDictionary *result = @{@"code":@(0),
                                 @"result":settlementController};
        callback(result);
        
    }
    else {
        // 非购物车进入
        // 反选全部商品
        
        skuId = (NSString *)[arg objectForKey:@"skuId"];
        skuCount = (NSString *)[arg objectForKey:@"skuCount"];
        
        
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        [params setObject:skuId ? skuId : @"" forKey:@"skuId"];
        [params setObject:@(1) forKey:@"itemType"];
        if ([skuCount jdcd_validateString]) {
            [params setObject:@([skuCount integerValue]) forKey:@"num"];
        } else {
            [params setObject:@(1) forKey:@"num"];
        }
        if (arg[@"isNormalBuyNow"]){
            NSString* storeId = [arg jdcd_getStringElementForKey:@"pickUpStoreId"];
            if(storeId.length){
                [params setObject:@"73" forKey:@"shipmentType"];
                [params setObject:storeId forKey:@"pickUpStoreId"];
            }else{
                [params setObject:@"66" forKey:@"shipmentType"];
            }
        }
        
        NSMutableDictionary *argcart = @{@"operations":@{@"products":@[params]}, @"refer":@(7)}.mutableCopy;
        
        NSNumber *isPresale = arg[@"isPresale"];
        NSNumber *ptFlag = arg[@"ptFlag"];
        if (isPresale.boolValue) {
            [argcart setObject:@(1) forKey:@"scene"];
        } else if (ptFlag.boolValue) {
            [argcart setObject:@(2) forKey:@"scene"];
        }
        
        if (isPresale.boolValue){
            //预售不需要反选
            NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShoppingCart];
            [JDRouter openURL:[NSString stringWithFormat:@"router://%@/addCart", module] arg:argcart error:nil completion:^(id  _Nullable object) {
                if ([object isKindOfClass:NSDictionary.class] && [object[@"code"] isEqualToString:@"0"]) {
                    // 加车成功，再打开结算页
                    // 购物车进入
                    // 离散化结算页
                    //商详的拼团参数
                    JDISVSettlementController *settlementController = [[JDISVSettlementController alloc] initWithSourceType:[sourceFrom integerValue] params:arg];
                    settlementController.isLoc = isLoc;
                    NSDictionary *result = @{@"code":@(0),
                                             @"result":settlementController};
                    callback(result);
                    
                } else {
                    //
                    NSString *message = SettlementLan(@"checkout_group_buy_network_error") ;
                    if ([[object objectForKey:@"message"] jdcd_validateString]) {
                        message = [object objectForKey:@"message"];
                    }
                    NSDictionary *result = @{@"code":@(-1),
                                             @"result":message};
                    callback(result);
                }
            }];
            return nil;
        }
        
        // 反选成功，再进行加车
        NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShoppingCart];
        [JDRouter openURL:[NSString stringWithFormat:@"router://%@/unSelectAll", module] arg:nil error:nil completion:^(id  _Nullable object) {
            if ([object isKindOfClass:NSDictionary.class] && [object[@"code"] isEqualToString:@"0"]) {
                
                if (arg[@"isNormalBuyNow"]) {
                    [argcart setObject:arg[@"isNormalBuyNow"] forKey:@"isNormalBuyNow"];
                }
                [JDRouter openURL:[NSString stringWithFormat:@"router://%@/addCart", module] arg:argcart error:nil completion:^(id  _Nullable object) {
                    if ([object isKindOfClass:NSDictionary.class] && [object[@"code"] isEqualToString:@"0"]) {
                        // 加车成功，再打开结算页
                        // 购物车进入
                        // 离散化结算页
                        //商详的拼团参数
                        JDISVSettlementController *settlementController = [[JDISVSettlementController alloc] initWithSourceType:[sourceFrom integerValue] params:arg];
                        settlementController.isLoc = isLoc;
                        NSDictionary *result = @{@"code":@(0),
                                                 @"result":settlementController};
                        callback(result);
                        
                    } else {
                        //
                        NSString *message = SettlementLan(@"checkout_group_buy_network_error") ;
                        if ([[object objectForKey:@"message"] jdcd_validateString]) {
                            message = [object objectForKey:@"message"];
                        }
                        NSDictionary *result = @{@"code":@(-1),
                                                 @"result":message};
                        callback(result);
                    }
                }];
            } else {
                // 反选失败
                NSString *message = SettlementLan(@"checkout_group_buy_network_error") ;
                if ([[object objectForKey:@"message"] jdcd_validateString]) {
                    message = [object objectForKey:@"message"];
                }
                NSDictionary *result = @{@"code":@(-1),
                                         @"result":message};
                callback(result);
            }
        }];
    }

    
    return nil;
}
 

//打开0元单支付成功页
JDROUTER_EXTERN_METHOD(JDISVSettlementModule, openZeroPaySuccessController, arg, callback) {
    NSString *orderId = arg[@"orderId"] ? : @"";
    JDISVSettlementOrderSuccessController *orderSuccessController = [[JDISVSettlementOrderSuccessController alloc] initWithNibName:@"JDISVSettlementOrderSuccessController" bundle:[NSBundle jdisvSettlement_bundle]];
    [orderSuccessController updateWithOrderId:orderId];
        
    return orderSuccessController;
}
@end
 
