//
//  NSBundle+JDISVSettlement.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2021/10/28.
//

#import "NSBundle+JDISVSettlement.h"

@implementation NSBundle (JDISVSettlement)
+ (NSBundle *)jdisvSettlement_bundle {
    NSBundle *bundle = [NSBundle jdisvSettlementBundleWithBundleName: @"JDTSettlementModule" class: NSClassFromString(@"JDTSettlementModule")];
    return bundle;
}

+ (NSBundle *)jdisvSettlementBundleWithBundleName:(NSString *)name class:(Class)aClass {
    NSBundle *baseBundle = [NSBundle bundleForClass:aClass];
    NSURL *url = [baseBundle URLForResource:name withExtension:@"bundle"];
    
    if (url) {
        NSBundle *bdl = [NSBundle bundleWithURL:url];
        
        return bdl;
    }
    
    return [NSBundle mainBundle];
}
@end
