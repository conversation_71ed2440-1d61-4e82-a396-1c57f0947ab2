//
//  NSAttributedString+JDISVSettlement.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2021/11/11.
//

#import "NSAttributedString+JDISVSettlement.h"
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>

@implementation NSAttributedString (JDISVSettlement)
- (instancetype)ka_settlementInitWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey {
    return [[NSAttributedString alloc] initWithString:str attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:fontKey],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:colorKey]
    }];
}
- (instancetype)ka_settlementInitWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight{
    return [[NSAttributedString alloc] initWithString:str attributes:@{
        NSFontAttributeName: [[JDISVThemeFont sharedInstance] fontWithKey:fontKey weight:weight],
        NSForegroundColorAttributeName: [[JDISVThemeColor sharedInstance] colorWithKey:colorKey]
    }];
}

- (CGFloat)ka_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, height) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey] maxNumberOfLines:1].width);
}

- (CGFloat)ka_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(CGFLOAT_MAX, height) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey weight:weight] maxNumberOfLines:1].width);
}

- (CGFloat)ka_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey] maxNumberOfLines:line].height);
}

- (CGFloat)ka_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight {
    return ceil([self.string jdcd_sizeWithContainer:CGSizeMake(width, CGFLOAT_MAX) textFont:[[JDISVThemeFont sharedInstance] fontWithKey:fontKey weight:weight] maxNumberOfLines:line].height);
}
@end
