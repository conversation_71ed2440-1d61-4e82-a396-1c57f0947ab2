//
//  JDISVSettolementTraker.m
//  JDISVSettlementModule
//
//  Created by cdwutao3 on 2022/6/10.
//

#import "JDISVSettlementTracker.h"
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

@implementation JDISVSettlementTracker
+(void)PV:(NSString*)pageName
    param:(NSDictionary*)param{
    if(pageName.length == 0)
        return;
    NSMutableDictionary* trackParam = [@{@"PV":pageName} mutableCopy];
    if(param){
        trackParam[@"arg"] = param;
    }
    
    
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
    NSString *url = [NSString stringWithFormat:@"router://%@/PV", moduleName];
    [JDRouter openURL:url arg:trackParam error:nil completion:nil];
}

+(void)Event:(NSString*)eventId
       param:(NSDictionary*)param{
    if(eventId.length == 0)
        return;
    NSMutableDictionary* trackParam = [@{@"Event":eventId} mutableCopy];
    if(param){
        trackParam[@"arg"] = param;
    }
    
    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
    NSString *url = [NSString stringWithFormat:@"router://%@/Event", moduleName];
    [JDRouter openURL:url arg:trackParam error:nil completion:nil];
}
@end
