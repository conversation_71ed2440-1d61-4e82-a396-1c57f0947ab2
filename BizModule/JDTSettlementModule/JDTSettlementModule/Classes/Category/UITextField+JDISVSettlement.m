//
//  UITextField+JDISVSettlement.m
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2021/11/11.
//

#import "UITextField+JDISVSettlement.h"

#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVReactiveObjCModule/NSObject+RACDescription.h>

@implementation UITextField (JDISVSettlement)
- (RACSignal *)ka_settlement_inputTextSignal {
    @weakify(self);
    return [[[[[[RACSignal
                 defer:^RACSignal * _Nonnull{
                     @strongify(self);
                     return [RACSignal return:self];
                 }]
                concat:[self rac_signalForControlEvents:UIControlEventAllEditingEvents]]
               filter:^BOOL(UITextField *x) {
                   if (!x.markedTextRange) {
                       return YES;
                   } else {
                       return NO;
                   }
               }]
              map:^(UITextField *x) {
                  return x.text;
              }]
             takeUntil:self.rac_willDeallocSignal]
            setNameWithFormat:@"%@ -rac_inputTextSignal", RACDescription(self)];
}
@end
