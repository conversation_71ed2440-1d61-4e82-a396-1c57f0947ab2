//
//  NSAttributedString+JDISVSettlement.h
//  JDISVSettlementModule
//
//  Created by 张令浩 on 2021/11/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSAttributedString (JDISVSettlement)
- (instancetype)ka_settlementInitWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey;
- (instancetype)ka_settlementInitWithString:(nonnull NSString *)str colorKey:(nonnull NSString *)colorKey fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight;

- (CGFloat)ka_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey;
- (CGFloat)ka_widthOfAttributedStringHeight:(CGFloat)height fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight;

- (CGFloat)ka_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey;
- (CGFloat)ka_heightOfAttributedStringWidth:(CGFloat)width maxNumberOfLines:(NSInteger)line fontKey:(nonnull NSString *)fontKey weight:(UIFontWeight)weight;
@end

NS_ASSUME_NONNULL_END
