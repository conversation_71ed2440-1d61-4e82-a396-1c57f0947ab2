//
//  JDISVSettlementPub.h
//  Pods
//
//  Created by ext.chenhongyu12 on 2023/3/16.
//

#ifndef JDISVSettlementPub_h
#define JDISVSettlementPub_h

#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVImageModule/JDISVImageModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

@import JDISVCategoryModule;

#import "NSBundle+JDISVSettlement.h"
#import "UIImage+JDISVSettlement.h"
#import "NSAttributedString+JDISVSettlement.h"
#import "JDISVSettlementAmountFloorNetService.h"
#import "JDISVSettlementNetService.h"

static inline void KASettlementLogHelper(NSString * _Nullable info, NSString * _Nullable func, NSString * _Nonnull file){
    NSDate *date = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss zzz"];
    NSString *dateVaule = [dateFormatter stringFromDate:date];
    NSString *infoValue = @"";
    if ([info jdcd_validateString]) {
        infoValue = [info copy];
    }
    NSString *funcValue = @"";
    if ([func jdcd_validateString]) {
        funcValue = [func copy];
    }
    NSDictionary *paramDict = @{
        @"date": dateVaule,
        @"info": infoValue,
        @"func": funcValue,
        @"file": file,
        @"module": @"JDISVSettlementModule"
    };
    [JDRouter openURL:@"router://JDISVLogModule/saveLog" arg:paramDict error:nil completion:nil];
}

#define KASETTLEMENT_SCREEN_WIDTH     CGRectGetWidth([[UIScreen mainScreen] bounds])
#define KASETTLEMENT_SCREEN_HEIGHT    CGRectGetHeight([[UIScreen mainScreen] bounds])

#define KASETTLEMENT_CONTENT_FLOOR_SPACE_W2 [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W2"]
#define KASETTLEMENT_CONTENT_MARGIN_W3 [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#W3"]


#define KASETTLEMENT_FLOOR_TOP_RADIUS_R1 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R1"]
#define KASETTLEMENT_FLOOR_BOTTOM_RADIUS_R2 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R2"]
#define KASETTLEMENT_FLOAT_TOP_RADIUS_R30 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R30"]
#define KASETTLEMENT_ALERT_RADIUS_R40 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R40"]
#define KASETTLEMENT_BUTTON_RADIUS_R51 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R51"]
#define KASETTLEMENT_TAG_RADIUS_R60 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R60"]
#define KASETTLEMENT_PRODUCT_RADIUS_R75 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R75"]
#define KASETTLEMENT_COUPON_RADIUS_R90 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R90"]
#define KASETTLEMENT_COUPON_TAG_RADIUS_R91 [[JDISVThemeCornerRadius sharedInstance] cornerRadiusWithKey:@"#R91"]

#define ISPresell ([JDISVSettlementAmountFloorNetService sharedService].ISPresellCapacity)
#define ISFirstMoenyFlag ( [JDISVSettlementAmountFloorNetService sharedService].ISFirstMoenyCapacity)

#define ISGroupBuy ( [JDISVSettlementAmountFloorNetService sharedService].ISGroupBuyCapacity)

#define SettlementLan(key) \
[NSString localizedStringKey:key fromTable:@"settle" inBundle:@"JDTSettlementModule"]

#endif /* JDISVSettlementPub_h */
