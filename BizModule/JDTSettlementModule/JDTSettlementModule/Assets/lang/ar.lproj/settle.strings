/* 
  settle.strings
  JDISVSettlementModule

  Created by ext.chenhongyu12 on 2023/3/16.
  Copyright © 2023 张令浩. All rights reserved.
*/

"checkout_dialog_pre_sale_negative" = "لا";
"checkout_invoice_label" = "فاتورة";
"checkout_coupon_list_title" = "قسيمة";
"checkout_auto_coupon_title" = "هناك كوبونات متاحة لم يتم تحديدها. هل أنت متأكد من أنك تريد الاستمرار في تقديم الطلب؟";
"checkout_coupon_use_limit" = "متاح لأكثر من %@ ريال";
"checkout_front_money" = "العربون";
"checkout_express_label" = "توصيل";
"checkout_common_network_error" = "فشل طلب الشبكة";
"checkout_result_go_home" = "الرجوع للصفحة الرئيسية";
"checkout_shipment_list_empty_tips" = "عذرا، لا توجد متاجر فعلية متوفرة في المنطقة أو أن المتاجر الفعلية غير متوفرة. حدد التوصيل السريع";
"checkout_result_go_order" = "عرض الطلب";
"checkout_auto_coupon_left_btn" = "العودة إلى التحديد";
"checkout_coupon_list_use_points_failed" = "فشل تحديد النقاط";
"checkout_pre_sale_payed" = "مدفوع";
"checkout_points_list_use_desc" = "تعليمات";
"checkout_shipment_select_stores" = "الرجاء اختيار المتجر أولا";
"checkout_dialog_pre_sale_positive" = "موافقة وتقديم طلب";
"checkout_not_use_points" = "لاتستخدم";
"checkout_shipment_list_title" = "اختر العنوان";
"checkout_pay_online" = "الدفع عبر الإنترنت";
"checkout_confirm" = "تأكد";
"checkout_final_money_label" = "الدفعة النهائية:";
"checkout_pre_sale_sum_label" = "المجموع: ";
"checkout_front_money_label" = "الوديعة المدفوعة:";
"checkout_no_address_positive" = "عنوان جديد";
"checkout_presale_end_price_label" = "الدفعة النهائية لمنتجات ما قبل البيع";
"checkout_stores_bar_title" = "اختر متجرا";
"checkout_total_prefix" = "المبلغ المستحق: ";
"checkout_shipment_please_select_store" = "يرجى تحديد متجر الجمع الذاتي";
"checkout_store_label" = "متجر";
"checkout_message_label" = "رسالة";
"checkout_submit" = "ادفع الآن";
"checkout_select_payment" = "اختر طريقة الدفع";
"checkout_all_out_stock_title" = "جميع المنتجات على صفحة التسوية غير متوفرة في المخزون";
"checkout_switch_address_failed" = "فشل تبديل العناوين";
"checkout_pre_sale_dialog_confirm" = "مفهوم";
"checkout_points_not_use" = "هذا الطلب لا يستخدم النقاط حاليا";
"checkout_pre_sale_agree_tips" = "العربون غير قابل للاسترداد لسلع البيع المسبق!";
"checkout_address_default" = "افتراضي";
"checkout_points_list_sum" = "نقاط الحساب: ";
"checkout_auto_coupon_right_btn" = "استمر في تقديم الطلبات";
"checkout_some_out_stock_negative" = "تغيير عنوان الشحن";
"checkout_summary_freight_label" = "رسوم الشحن";
"checkout_pre_sale_stage_2" = "المرحلة 2: الدفعة النهائية";
"checkout_shipment_title" = "طريق التوصيل";
"checkout_pre_sale_stage_1" = "المرحلة 1: الوديعة";
"checkout_pre_sale_stage_new_1" = "عربون";
"checkout_pre_sale_stage_new_2" = "الرصيد";
"checkout_pre_sale_agree_label" = "الموافقة على دفع عربون";
"checkout_no_price" = "لا يوجد أسعار حاليا";
"checkout_dialog_pre_sale_title" = "العربون غير قابل للاسترداد لسلع البيع المسبق. موافقة ومتابعة الطلب";
"checkout_pre_sale_end_period" = "%@ ~ %@ دفع الدفعة النهائية";
"checkout_stores_click_open_stores" = "انقر لفتح قائمة المتاجر";
"checkout_coupon_available" = "%ld متوفرة";
"checkout_dialog_no_address_title" = "لا يوجد حاليا عنوان شحن";
"checkout_switch_address_title" = "تغيير العنوان";
"checkout_points_use_suffix" = "نقطة, خصم";
"checkout_points_forbid" = "لا يتوافق مع قواعد الاستخدام";
"checkout_some_out_stock_title" = "المنتجات التالية غير متوفرة في المخزون";
"checkout_product_list_count" = "المجموع %@";
"checkout_points_list_empty_tips" = "ليس لديك حاليا نقاط متاحة لاستخدامها";
"checkout_coupon_selected_failed" = "فشل اختيار الكوبونات";
"checkout_use_points" = "استخدم";
"checkout_points_list_title" = "النقاط";
"checkout_use_points_hint" = "يمكن استخدام %@ - %@ نقطة لهذا الطلب";
"checkout_switch_shipment_failed" = "فشل تبديل طريقة التوصيل";
"checkout_points_list_discount_p1" = "هذا الطلب ";
"checkout_stores_list_business_time_label" = "ساعات العمل";
"checkout_points_list_discount_p2" = "تم خصم ";
"checkout_stores_list_business_time" = "ساعات العمل %@";
"checkout_coupon_list_available_empty" = "لا توجد كوبونات متاحة";
"checkout_stores_list_address" = "العنوان: %@";
"checkout_dialog_no_address_content" = "لقد حددت عنوانا جديدا أثناء عملية تصفح المنتج. هل ترغب في إنشاء عنوان شحن جديد";
"checkout_switch_address_positive" = "تأكيد التبديل";
"checkout_result_submit_order_success" = "تم تقديم الطلبات بنجاح";
"checkout_result_submit_order_pay_success" = "الدفع بنجاح";
"checkout_stores_no_product" = "غير متوفر حاليا";
"checkout_all_out_stock_positive" = "اعادة";
"checkout_exit_dialog_cancel" = "الغي";
"checkout_points_empty" = "لا توجد نقاط متاحة";
"checkout_points_with_points" = "(النقاط الحالية %@)";
"checkout_coupon_list_unavailable_empty" = "لا يوجد حاليا كوبونات غير متوفرة";
"checkout_shop_no_product_label" = "إنتهى من المخزن";
"checkout_summary_reduce_label" = "تخفيض الترويج";
"checkout_stores_list_distance" = "مسافة %@";
"checkout_final_payment_number" = "رقم الهاتف المحمول للإشعار بالرصيد";
"checkout_pre_sale_remark" = "تصريحات النظام";
"checkout_coupon_empty" = "اختار";
"checkout_pre_sale_sum_tips" = "يمكن استخدام أي قسائم متوفرة أثناء دفع الرصيد";
"checkout_points_label" = "النقاط";
"checkout_points_default_choose" = "استخدم";
"checkout_coupon_list_available" = "الكوبونات المتوفرة ";
"checkout_leave_msg_hint" = "اختياري. يرجى التأكيد مع البائع";
"checkout_dialog_no_address_positive" = "أضف عنوانا";
"checkout_join_buy_back" = "الى تفاصيل المنتج";
"checkout_points_list_use_title" = "تعليمات لاستخدام النقاط";
"checkout_product_list_total" = "المجموع %@";
"checkout_switch_address_tip" = "لقد قمت بتبديل العناوين أثناء تصفح المنتجات، يرجى اختيار ما إذا كنت تريد مطابقة عنوان موجود أو إنشاء عنوان جديد.";
"checkout_some_out_stock_positive" = "إزالة المنتجات غير المتوفرة في المخزون";
"checkout_pre_sale_disocunt_prefix" = "القيمة القابلة للخصم لوديعة منتج واحد هي ريال %.2f";
"checkout_summary_price_label" = "السعر";
"checkout_layout_virtual_main" = "مجموعة السلع";
"checkout_pre_sale_dialog_title" = "تفاصيل الدفعة النهائية";
"checkout_stores_list_Tips" = "بعد الدفع الناجح، سيتم إرسال رمز الاستلام إلى رقم الهاتف في عنوان الشحن. إذا كنت بحاجة إلى تعديل رقم الهاتف، فيرجى الانتقال إلى صفحة قائمة العناوين لتعديله";
"checkout_stores_list_distance_address" = "المسافة إلى عنوان الشحن:";
"checkout_address_all" = "جميع";
"checkout_product_list_title" = "قائمة المنتجات";
"checkout_title_bar_title" = "أكمل نموذج الطلب";
"checkout_shipment_select_other_store" = "اختر متجرا آخر";
"checkout_coupon_reset_failed" = "فشل إعادة تعيين الكوبونات";
"checkout_address_pre_sale_tips" = "لا يمكن تعديل معلومات العنوان بعد دفع العربون";
"checkout_group_buy_network_error" = "النظام غير طبيعي، يرجى إعادة المحاولة لاحقا";
"checkout_stores_list_recent" = "أخيرا";
"checkout_points_use" = "استخدم";
"checkout_some_out_stock_positive_add" = "العودة إلى عربة التسوق";
"checkout_coupon_list_unavailable" = "الكوبونات غير المتوفرة ";
"checkout_scale" = "الخصم";
"checkout_coupon_code_title" = "رمز الكوبون";
"checkout_coupon_code_hint" = "لا يمكن استخدامها مع الكوبونات";
"checkout_coupon_code_exit_title" = "بمجرد الخروج من الصفحة، ستفقد المعلومات التي ملأتها. هل أنت متأكد من أنك تريد الخروج؟";
"checkout_coupon_code_exit_confirm" = "متأكد";
"checkout_use_coupon_code_success" = "'قدمت بنجاح'";
"checkout_use_coupon_code_fail" = "فشل التقديم!";
"checkout_coupon_disable" = "لا يمكن استخدامها مع رموز الخصم";
"checkout_coupon_code_const_tips" = "استمتع بتوفير إضافي باستخدام كود الخصم. كل كود متاح حتى نفاد الكمية";
"checkout_coupon_code_hint" = "يرجى ملء كود الخصم";
"settlement_discount_code_fromat_error" = "تنسيق كود الخصم غير صحيح";
"settlement_dispcunt_code_delete_code_title" = "هل أنت متأكد من أنك تريد حذف كود الخصم؟";
"settlement_discount_code_delete_success" = "نجاح الحذف";
"settlement_discount_code_delete_failed" = "فشل الحذف، يرجى إعادة المحاولة لاحقا";
"setllement_new_built" = "انشاء";
"setllement_address_empty" = "ليس لديك عنوان شحن حتى الآن، اذهب وقم بإعداده بسرعة";
"setllement_not_logged_in" = "المستخدم لم يسجل الدخول";
"setllement_unknown_error" = "خطأ غير معروف، يرجى المحاولة مرة أخرى";
"setllement_i_known" = "موافقة";
"setllement_discount_code_error_tip" = "يدعم إدخال 15 رقما أو حرفا فقط";
"setllement_deduction" = "تستهلك %@ نقاط، خصم";
"setllement_changed_address" = "تغيير عنوان الشحن";
"setllement_delete_back" = "حذف العودة";
"setllement_opation_error" = "فشل التشغيل";
"setllement_reload_failed" = "فشل تحميل الصفحة";
"setllement_repty" = "حاول مرة أخرى";
"setllement_pay_type_illustrate" = "وصف طريقة الدفع";
//"setllement_pay_type_illustrate_des" = "يمكنك اختيار طريقة الدفع "تحويل الشركات" في أمين الصندوق على PC";
"setllement_date_formatter" = "HH:mm اليوم dd شهر MM";
"setllement_address_title" = "عنوان";
"checkout_stores_list_business_time_1" = "ساعات العمل:";
"setllement_plase_choose" = "اختر";
"setllement_store_out_of_stock" = "المتجر غير متوفر مؤقتًا";
"setllement_exchanged_purchase" = "الشراء";
"setllement_joinGroupBuy" = "الشراء المشارك";
"checkout_invoice_type_personal" = "إلكتروني (تفاصيل المنتج - شخصي)";
"checkout_invoice_type_company" = "إلكتروني (تفاصيل المنتج - شركة)";
"checkout_coupon_use_limit" = "متاح للحد الأدنى لإنفاق ريال  %@";
"checkout_result_order_id" = "رقم الطلبات: %@\n يرجى انتظار الشحن من قبل التاجر";
"checkout_pre_sale_end_period" = "%@ ~ %@ دفع الدفعة النهائية";
"checkout_coupon_available" = "%ld متاحة";
"checkout_product_list_count" = "المجموع %@";
"checkout_points_list_discount_p2" = "مخفض";
"checkout_stores_list_business_time" = "ساعات العمل %@";
"checkout_points_with_points" = "(النقاط الحالية %@)";
"checkout_stores_list_distance" = "المسافة %@";
"checkout_pre_sale_disocunt_prefix" = "القيمة القابلة للخصم لوديعة منتج واحد هي ريال %.2f";
"settlement_discount_code_title" = "كود الخصم";
"settlement_discount_code_tip" = "استمتع بتوفير إضافي باستخدام كود الخصم. كل كود متاح حتى نفاد الكمية";
"settlement_discount_code_placeholder" = "أدخل رمز القسيمة";
"settlement_discount_code_detainment_desc" = "بمجرد الخروج من الصفحة، ستفقد المعلومات التي ملأتها. هل أنت متأكد من أنك تريد الخروج؟";
"settlement_discount_code_detainment_commit" = "تم";
"settlement_discount_code_success" = "قدمت بنجاح'";
"settlement_discount_code_failed" = "فشل التقديم!";
"settlement_discount_code_unselected" = "لا يمكن استخدامها مع الكوبونات";
"settlement_coupon_unselected" = "لا يمكن استخدامها مع رموز الخصم";


"checkout_settlment_gift" = "هبات";
"checkout_settlment_qty" = "الكمية %@";

//630
"checkout_serving_tray_title_1" = "المنتجات التالية لها متطلبات شراء دنيا ومحدودة";
"checkout_serving_tray_title_2" = "جميع منتجات التسوية لها حد أدنى من متطلبات الشراء";
"checkout_return_to_shopping_cart" = "العودة إلى عربة التسوق";
"checkout_remove_goods" = "ازالة المنتج";
"checkout_minimum_purchase" = "حد أدنى للشراء %@";
"checkout_limited_to_pieces" = "كمية شراء محدودة %@";
"checkout_settlment_step" = "عنوان الشحن";
"checkout_settlment_step_3" = "تفاصيل المنتج";
"checkout_settlment_payment_title" = "الدفع";
"checkout_selttment_payment_error1" = " لا يتم دعم الدفع نقدا عند الاستلام عندما يكون مبلغ الطلب أكبر من 500.";
"checkout_selttment_payment_error2" =  "الدفع عند الاستلام غير مدعوم";
"settlement_tariff_title" = "رسوم الاستيراد";
"settlement_cod_title" =  "رسوم خدمة الدفع عند الاستلام";
"checkout_payment_type_error" = "فاتورة صفر ريال لا تدعم الدفع عند الاستلام";

"checkout_payment_online" = "الدفع عبر الإنترنت";
"checkout_payment_COD" = "الدفع نقداً أو بالبطاقة عند الاستلام ";

"checkout_payment_exchanged_to_online_pay" = "نظرا لرسوم خدمة الدفع عند الاستلام، واستخدام / إلغاء استخدام الأصول الافتراضية، وما إلى ذلك، فإن إجمالي مبلغ الطلب أكبر من 500 ولا يمكن استخدام الدفع عند الاستلام  لدفعه، سننتقل إلى الدفع عبر الإنترنت نيابة عنك.
";

"settlement_title_shipping_fee_discount" = "تخفيض رسوم الشحن";
"checkout_query_title" = "إدخال";
"settlement_title_shipping_fee_discount" = "تخفيض رسوم الشحن";
"checkout_query_title" = "تطبيق";
"checkout_use_referrals" = "اختيار تلقائي";
"checkout_Total_deduction" = "إجمالي الخصم";
"checkout_coupon_titletip" = "تعليمات استخدام رمز القسيمة";
"checkout_coupon_detailtip" = "قواعد الاستخدام: \
1. لا يدعم الطلب الواحد أكثر من رمز قسيمة منصة واحدة.
2. تدعم المنتجات المتعددة من تجار مختلفين في الطلب الواحد استخدام رموز قسيمة متعددة، ولكن لا يمكن للتاجر الواحد استخدام أكثر من رمز قسيمة واحد. \
3. لا تدعم رموز القسيمة خصم الشحن. \
4. لا يدعم الدفع الزائد استرداد الفرق. \
5. يمكن استخدام 20 رمز قسيمة كحد أقصى لطلب واحد.";
"checkout_coupon_auto_select" = "رمز القسيمة موجود بالفعل وتم اختياره لك تلقائيًا!";
"checkout_coupon_maxinum" = "يمكن ادخال 20 كوبون كحد أقصى";
"checkout_coupon_maxinum_use" = "يدعم ما يصل إلى 20 رمز قسيمة!";
"checkout_coupon_del_ok" = "تم الحذف بنجاح، ويمكن إدخاله مرة أخرى بعد الحذف";
"checkout_coupon_sure_select" = "لا يمكن دمج رمز القسيمة الذي قمت باختياره حديثًا مع الرمز الذي قمت باختياره بالفعل. هل أنت متأكد من أنك تريد تطبيق رمز القسيمة الجديد الذي قمت باختياره؟";
"checkout_coupon_cancel" = "الغي";
"checkout_coupon_query_unuse" = "رمز القسيمة الحالي غير متوفر";
