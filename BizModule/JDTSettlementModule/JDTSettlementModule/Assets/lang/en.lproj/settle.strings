/* 
  settle.strings
  JDISVSettlementModule

  Created by ext.chenhongyu12 on 2023/3/16.
  Copyright © 2023 张令浩. All rights reserved.
*/

"checkout_dialog_pre_sale_negative" = "No";
"checkout_invoice_label" = "Invoice";
"checkout_coupon_list_title" = "Voucher";
"checkout_auto_coupon_title" = "There are available vouchers for choosing. Are you sure you want to continue ordering?";
"checkout_coupon_use_limit" = "Available upon min. spend SAR %@";
"checkout_front_money" = "Deposit";
"checkout_express_label" = "Distribution";
"checkout_common_network_error" = "Network request failed";
"checkout_result_go_home" = "Back to Home Page";
"checkout_shipment_list_empty_tips" = "Sorry, no physical stores are available in the region or the physical stores are out of stock. Select express delivery instead";
"checkout_result_go_order" = "View Order";
"checkout_auto_coupon_left_btn" = "Back to select";
"checkout_coupon_list_use_points_failed" = "Failed to select points";
"checkout_pre_sale_payed" = "Paid";
"checkout_points_list_use_desc" = "Operation instructions";
"checkout_shipment_select_stores" = "Please select a physical store first";
"checkout_dialog_pre_sale_positive" = "Agree and place an order";
"checkout_not_use_points" = "Do not apply";
"checkout_shipment_list_title" = "Select address";
"checkout_pay_online" = "Online payment";
"checkout_confirm" = "OK";
"checkout_final_money_label" = "Balance: ";
"checkout_pre_sale_sum_label" = "Total: ";
"checkout_front_money_label" = "Deposit paid: ";
"checkout_no_address_positive" = "Newly address";
"checkout_presale_end_price_label" = "Balance of presale product";
"checkout_stores_bar_title" = "Select physical store";
"checkout_total_prefix" = "Payable: ";
"checkout_shipment_please_select_store" = "Please select the physical store for pickup";
"checkout_store_label" = "Store";
"checkout_message_label" = "Message";
"checkout_submit" = "Pay Now";
"checkout_select_payment" = "Select payment";
"checkout_all_out_stock_title" = "All the products on the checkout page are out of stock";
"checkout_switch_address_failed" = "Failed to switch address";
"checkout_pre_sale_dialog_confirm" = "I Know";
"checkout_points_not_use" = "Do not use points for this order";
"checkout_pre_sale_agree_tips" = "Deposit is not refundable for pre-sale products!";
"checkout_address_default" = "Default";
"checkout_points_list_sum" = "Account points:  ";
"checkout_auto_coupon_right_btn" = "Continue ordering";
"checkout_some_out_stock_negative" = "Change delivery address";
"checkout_summary_freight_label" = "Shipping Fee";
"checkout_pre_sale_stage_2" = "Stage 2: Balance";
"checkout_shipment_title" = "Shipping Method";
"checkout_pre_sale_stage_1" = "Stage 1: Deposit";
"checkout_pre_sale_stage_new_1" = "Deposit";
"checkout_pre_sale_stage_new_2" = "Balance";
"checkout_pre_sale_agree_label" = "Agree to pay a deposit";
"checkout_no_price" = "No offer";
"checkout_dialog_pre_sale_title" = "Deposit is not refundable for pre-sale products. Agree and continue ordering";
"checkout_pre_sale_end_period" = "%@ ~ %@ pay balance";
"checkout_stores_click_open_stores" = "Click to open the physical store list";
"checkout_coupon_available" = "%ld vouchers available";
"checkout_dialog_no_address_title" = "No delivery address";
"checkout_switch_address_title" = "Switch address";
"checkout_points_use_suffix" = "Points Deduction";
"checkout_points_forbid" = "This order is not applicable";
"checkout_some_out_stock_title" = "The following products are out of stock";
"checkout_product_list_count" = "%@ items in total";
"checkout_points_list_empty_tips" = "No point available";
"checkout_coupon_selected_failed" = "Voucher selection failed";
"checkout_use_points" = "Apply";
"checkout_points_list_title" = "Points Deduction";
"checkout_switch_shipment_failed" = "Failed to switch distribution mode";
"checkout_points_list_discount_p1" = "This order ";
"checkout_stores_list_business_time_label" = "Business hours";
"checkout_points_list_discount_p2" = " reduced";
"checkout_stores_list_business_time" = "Business hours %@";
"checkout_coupon_list_available_empty" = "Not Available voucher";
"checkout_stores_list_address" = "Address: %@";
"checkout_dialog_no_address_content" = "You have selected a new address when browsing products. Do you want to create a new delivery address";
"checkout_switch_address_positive" = "Do you want to switch";
"checkout_result_submit_order_success" = "Order submitted successfully";
"checkout_result_submit_order_pay_success" = "Payment Succeeded";
"checkout_stores_no_product" = "Temporarily out of stock";
"checkout_all_out_stock_positive" = "Back";
"checkout_exit_dialog_cancel" = "Cancel";
"checkout_exit_dialog_Sure" = "Sure";
"checkout_points_empty" = "No available points";
"checkout_points_with_points" = "(Current points %@)";
"checkout_coupon_list_unavailable_empty" = "No unavailable vouchers";
"checkout_shop_no_product_label" = "Sold out";
"checkout_summary_reduce_label" = "Promotion Discount";
"checkout_stores_list_distance" = "Distance %@";
"checkout_final_payment_number" = "Mobile Phone Number for Balance Notification";
"checkout_pre_sale_remark" = "Order remarks";
"checkout_coupon_empty" = "Select";
"checkout_pre_sale_sum_tips" = "Any vouchers available can be redeemed during balance payment";
"checkout_points_label" = "Point";
"checkout_points_default_choose" = "Apply";
"checkout_coupon_list_available" = "Available Vouchers ";
"checkout_leave_msg_hint" = "Optional. Please confirm with the seller";
"checkout_dialog_no_address_positive" = "New address";
"checkout_join_buy_back" = "Back";
"checkout_points_list_use_title" = "Points instructions";
"checkout_product_list_total" = "%@ items in total";
"checkout_switch_address_tip" = "You have changed the address when browsing the product. Please select whether to match the existing address or create a new address.";
"checkout_some_out_stock_positive" = "Remove products that are out of stock";
"checkout_pre_sale_disocunt_prefix" = "Deposit can reduce SAR %.2f per item";
"checkout_summary_price_label" =  "Products";
"checkout_layout_virtual_main" = "Bundle";
"checkout_pre_sale_dialog_title" = "Balance details";
"checkout_stores_list_Tips" = "After successful payment, the pickup code will be sent to the mobile phone number in the delivery address. If you need to modify your mobile phone number, please modify it on address list page";
"checkout_stores_list_distance_address" = "Distance to delivery address:";
"checkout_address_all" = "All";
"checkout_product_list_title" = "Product list";
"checkout_title_bar_title" = "Fill Out the Order";
"checkout_shipment_select_other_store" = "Select other physical stores";
"checkout_coupon_reset_failed" = "Failed to reset voucher";
"checkout_address_pre_sale_tips" = "The address information cannot be modified after the deposit is paid";
"checkout_group_buy_network_error" = "System exception, please try again later";
"checkout_stores_list_recent" = "Recent";
"checkout_points_use" = "Apply";
"checkout_some_out_stock_positive_add" = "Back to shopping cart";
"checkout_coupon_list_unavailable" = "Unavailable Vouchers";
"checkout_scale" = "Discount";
"checkout_coupon_code_title" = "Coupon Code";
"checkout_coupon_code_hint" = "Cannot be used in combination with vouchers";
"checkout_coupon_code_exit_title" = "Once you exit from the page, the information you have entered will be lost. Are you sure to exit?";
"checkout_coupon_code_exit_confirm" = "Confirm";
"checkout_use_coupon_code_success" = "Submitted successfully";
"checkout_use_coupon_code_fail" = "Submission failed ";
"checkout_coupon_disable" = "Cannot be used in combination with coupon code";
"checkout_coupon_code_const_tips" = "Enjoy extra savings by using a code. Codes are limited, so grab yours while you can";
"checkout_coupon_code_hint" = "Please fill in coupon code";
"settlement_discount_code_fromat_error" = "Incorrect format of coupon code";
"settlement_dispcunt_code_delete_code_title" = "Are you sure you want to delete the coupon code?";
"settlement_discount_code_delete_success" = "Deletion succeeded";
"settlement_discount_code_delete_failed" = "Deletion failed, please try again later";
"setllement_new_built" = "Create";
"setllement_address_empty" = "You don't have a delivery address yet, go to set it up";
"setllement_not_logged_in" = "User not logged in";
"setllement_unknown_error" = "Unknown error, please try again";
"setllement_i_known" = "OK";
"setllement_discount_code_error_tip" = "You can enter 15-digit numbers or letters";
"setllement_deduction" = "Consume % @ points, reduce";
"setllement_changed_address" = "Change delivery address";
"setllement_delete_back" = "Delete and back";
"setllement_opation_error" = "Operation failed";
"setllement_reload_failed" = "Page loading failed";
"setllement_repty" = "Retry ";
"setllement_pay_type_illustrate" = "Description on payment method";
"setllement_pay_type_illustrate_des" = "You can choose the payment method of “Transfer to Corporate Account” at the PC cashier";
"setllement_date_formatter" = "HH:mm  MM/dd";
"setllement_address_title" = "URL";
"checkout_stores_list_business_time_1" = "Business hours:";
"setllement_plase_choose" = "Please select";
"setllement_store_out_of_stock" = "The physical store is temporarily out of stock";
"setllement_exchanged_purchase" = "Bargain-buy";
"setllement_joinGroupBuy" = "Sharebuy";
"checkout_invoice_type_personal" = "Electronic (Product details - Personal)";
"checkout_invoice_type_company" = "Electronic (Product details - Organization)";
"checkout_coupon_use_limit" = "Available upon purchase of SAR %@";
"checkout_result_order_id" = "Order No.: %@\n Please wait for the shipment by merchant";
"checkout_pre_sale_end_period" = "%@ ~ %@ Pay balance";
"checkout_coupon_available" = "%ld vouchers available";
"checkout_product_list_count" = "%@ items in total";
"checkout_use_points_hint" = "%@-%@ points can be used for this order";
"checkout_points_list_discount_p2" = "Reduced";
"checkout_stores_list_business_time" = "Business hours %@";
"checkout_points_with_points" = "(Current points %@)";
"checkout_stores_list_distance" = "Distance %@";
"checkout_pre_sale_disocunt_prefix" = "Deposit can reduce SAR %.2f per item";


"settlement_discount_code_title" = "Coupon code";
"settlement_discount_code_tip" = "Enjoy extra savings by using a code. Codes are limited, so grab yours while you can";
"settlement_discount_code_placeholder" = "Enter coupon code";
"settlement_discount_code_detainment_desc" = "Once you exit from the page, the information you have entered will be lost. Are you sure to exit?";
"settlement_discount_code_detainment_commit" = "OK";
"settlement_discount_code_success" = "Submitted successfully";
"settlement_discount_code_failed" = "Submission failed";
"settlement_discount_code_unselected" = "Cannot be redeemed in combination with vouchers";
"settlement_coupon_unselected" = "Cannot be used in combination with coupon code";


"checkout_settlment_gift" = "Gift";
"checkout_settlment_qty" = "Qty %@";


//630
"checkout_serving_tray_title_1" = "The following products are subject to minimum purchase requirements and purchase restrictions";
"checkout_serving_tray_title_2" = "All settlement products have minimum purchase requirements";
"checkout_return_to_shopping_cart" = "Back to shopping cart";
"checkout_remove_goods" = "Remove product";
"checkout_minimum_purchase" = "At least %@ products must be purchased";
"checkout_limited_to_pieces" = "A maximum of %@ pieces can be purchased";
"checkout_settlment_step" = "Shipping Address";
"checkout_settlment_step_3" = "Product details";
"checkout_settlment_payment_title" = "Payment";
"checkout_selttment_payment_error1" = "Cash on delivery is not available for orders with an amount greater than SAR 500.";
"checkout_selttment_payment_error2" = "Cash on delivery is not available";
"settlement_tariff_title" = "Import Fee";
"settlement_cod_title" = "Service fee of cash on delivery";
"checkout_payment_type_error" = "Cash on delivery payment is not applicable to SAR 0 order";

"checkout_payment_online" = "Online payment";
"checkout_payment_COD" = "Cash / Card on delivery";

"checkout_payment_exchanged_to_online_pay" = "Due to cash on delivery service fee, the use/cancelation of virtual assets and other factors, the total amount of the order is greater than 500 and cannot be paid by cod; it will be switched to online payment.";
"checkout_query_title" = "Apply";

"settlement_title_shipping_fee_discount" = "Shipping Fee Discount";

"checkout_use_referrals" = "Use Referrals";

"checkout_use_referrals" = "Apply Recommended";

"checkout_Total_deduction" = "Total Deduction:";
"checkout_coupon_titletip" = "Coupon Code Instructions";
"checkout_coupon_detailtip" = "Usage rules: \
        1、Different types (no threshold, full reduction, full discount) cannot be stacked. \
        2、Exceeding the platform code limit, the number of sheets cannot be stacked. \
        3、Exceeding the total limit, the number of sheets cannot be stacked. \
        4、The set code cannot overlap with other codes according to the rule. \
        5、Other restriction rules.";
"checkout_coupon_auto_select" = "This discount code already exists and has been automatically selected for you!";
"checkout_coupon_maxinum" = "A maximum of 20 coupon can be entered";
"checkout_coupon_maxinum_use" = "Supports up to 20 coupon codes!";
"checkout_coupon_del_ok" = "Deleted successfully, can be entered again after deletion";
"checkout_coupon_sure_select" = "The coupon code you new selected cannot be combined with the already selected one. Are you sure you want to apply new selected coupon code?";
"checkout_coupon_cancel" = "Cancel";
"checkout_coupon_query_unuse" = "The current coupon code is not available";

