/* 
  settle.strings
  JDISVSettlementModule

  Created by ext.chenhongyu12 on 2023/3/16.
  Copyright © 2023 张令浩. All rights reserved.
*/

"settlement_discount_code_title" = "优惠码";
"settlement_discount_code_tip" = "优惠码数量有限，以支付时间为准，先到先得";
"settlement_discount_code_placeholder" = "请填写优惠码";
"settlement_discount_code_detainment_desc" = "一旦退出页面，所填写的内容将会丢失，请确认是否退出？";
"settlement_discount_code_detainment_commit" = "确认";
"settlement_discount_code_success" = "提交成功";
"settlement_discount_code_failed" = "提交失败";
"settlement_discount_code_unselected" = "不可与优惠券同时使用";
"settlement_coupon_unselected" = "不可与优惠码同时使用";

"checkout_confirm" = "确定";
"settlement_discount_code_delete_success" = "删除成功";
"settlement_discount_code_delete_failed" = "删除失败，请稍后重试";
"settlement_dispcunt_code_delete_code_title" = "确认要删除该优惠码?";
"checkout_coupon_empty" = "去选择";
"checkout_exit_dialog_cancel" = "取消";
"settlement_discount_code_fromat_error" = "优惠码格式错误";
"checkout_group_buy_network_error" = "系统异常，请稍后重试";
"checkout_dialog_pre_sale_title" = "预售商品定金不支持退款，\n同意后可继续下单";
"checkout_dialog_pre_sale_negative" = "我再想想";
"checkout_dialog_pre_sale_positive" = "同意并下单";
"checkout_dialog_no_address_content" = "您在浏览商品过程中选择了新的地址，是否新建一个收货地址";
"setllement_new_built" = "新建";
"setllement_address_empty" = "您还没有收货地址,快去设置吧";
"checkout_no_address_positive" = "新建地址";
"setllement_not_logged_in" = "用户未登录";
"setllement_unknown_error" = "未知错误,请重试";
"checkout_presale_end_price_label" = "预售商品尾款";
"checkout_summary_freight_label" = "运费";
"checkout_address_pre_sale_tips" = "支付定金后，地址信息不可修改";
"checkout_address_default" = "默认";
"setllement_i_known" = "知道了";
"checkout_coupon_list_available_empty" = "暂无可用优惠券";
"checkout_coupon_list_unavailable_empty" = "暂无不可用优惠券";
"checkout_coupon_use_limit" = "满%@元可用";
"checkout_coupon_list_available" = "可用优惠券";
"checkout_coupon_list_unavailable" = "不可用优惠券";
"setllement_discount_code_error_tip" = "仅支持输入15位数字或字母";
"checkout_points_list_title" = "积分抵扣";
"checkout_points_use" = "使用";
"checkout_points_use_suffix" = "积分，抵扣";
"checkout_not_use_points" = "暂不使用";
"checkout_use_points" = "确定使用";
"checkout_use_points_hint" = "本单可使用%@-%@个积分哦";
"checkout_points_list_sum" = "账户积分数：";
"checkout_points_list_use_desc" = "使用说明";
"checkout_points_list_discount_p1" = "本单";
"checkout_points_list_discount_p2" = "已减";
"checkout_points_not_use" = "本单暂不使用积分";
"checkout_points_list_empty_tips" = "您暂无可以使用的积分";
"setllement_deduction" = "消耗%@积分，抵扣";
"checkout_coupon_list_title" = "优惠券";
"checkout_coupon_available" = "%ld张可用";
"checkout_invoice_label" = "发票";
"checkout_invoice_type_personal" = "电子（商品明细-个人）";
"checkout_invoice_type_company" = "电子（商品明细-单位）";
"checkout_points_label" = "积分";
"checkout_points_with_points" = "(当前积分%@)";
"checkout_points_default_choose" = "去使用";
"checkout_points_forbid" = "本单不符合使用规则";
"checkout_summary_reduce_label" = "促销立减";
"checkout_submit" = "提交订单";
"checkout_pay_online" = "在线支付";
"checkout_join_buy_back" = "返回商品详情";
"checkout_all_out_stock_title" = "结算页商品全部无货";
"setllement_changed_address" = "更换收货地址";
"setllement__delete_back" = "删除返回";
"checkout_some_out_stock_title" = "以下商品无货";
"checkout_some_out_stock_positive_add" = "返回购物车";
"checkout_some_out_stock_negative" = "更改收货地址";
"checkout_some_out_stock_positive" = "移除无货商品";
"checkout_pre_sale_dialog_confirm" = "我知道了";
"checkout_total_prefix" = "应付   ";
"checkout_pre_sale_sum_label" = "合计: ";
"setllement_opation_error" = "操作失败";
"checkout_shop_no_product_label" = "无货";
"checkout_result_submit_order_success" = "提交订单成功";
"checkout_result_submit_order_pay_success" = "支付成功";
"checkout_result_order_id" = "订单号：%@\n请等待商家发货";
"checkout_result_go_home" = "返回首页";
"checkout_result_go_order" = "查看订单";
"setllement_reload_failed" = "页面加载失败";
"setllement_repty" = "重试";
"checkout_dialog_no_address_title" = "暂无收货地址";
"checkout_title_bar_title" = "填写订单";
"setllement_pay_type_illustrate" = "支付方式说明";
"setllement_pay_type_illustrate_des" = "可在PC收银台选择“对公转账” 支付方式";
"checkout_final_payment_number" = "尾款通知手机号";
"checkout_pre_sale_stage_1" = "阶段1:   定金";
"checkout_pre_sale_stage_2" = "阶段2：尾款";
"checkout_pre_sale_stage_new_1" = "定金";
"checkout_pre_sale_stage_new_2" = "尾款";
"checkout_pre_sale_payed" = "已付";
"checkout_pre_sale_disocunt_prefix" = "单件定金可抵值 ¥%.2f";
"setllement_date_formatter" = "MM月dd日 HH:mm";
"checkout_pre_sale_end_period" = "%@ ~ %@ 支付尾款";
"checkout_pre_sale_agree_label" = "同意支付定金";
"checkout_pre_sale_agree_tips" = "预售商品，定金不退哦";
"checkout_pre_sale_sum_tips" = "如有可用券，可在尾款时使用";
"setllement_address_title" = "地址";
"checkout_stores_bar_title" = "选择门店";
"checkout_shipment_title" = "配送方式";
"checkout_stores_list_business_time_1" = "营业时间：";
"checkout_stores_list_address" = "地址：";
"checkout_stores_list_distance_address" = "距离收货地址：";
"checkout_stores_no_product" = "暂时无货";
"checkout_stores_list_recent" = "最近";
"setllement_plase_choose" = "请选择";
"checkout_shipment_list_empty_tips" = "抱歉，该地区暂无门店或门店缺货\n可以尝试选择快递配送";
"checkout_shipment_list_title" = "选择地址";
"checkout_stores_click_open_stores" = "点击打开门店列表";
"checkout_stores_list_distance" = "距离%@";
"checkout_stores_list_business_time_label" = "营业时间";
"checkout_stores_list_Tips" = "支付成功后提货码将发送至收货地址中的手机号，如需修改手机号，请至地址列表页修改";
"setllement_store_out_of_stock" = "该门店暂时无货";
"checkout_shipment_please_select_store" = "请选择自取的门店";
"checkout_shipment_select_other_store" = "选择其它门店";
"checkout_product_list_title" = "商品清单";
"checkout_layout_virtual_main" = "组套";
"setllement_exchanged_purchase" = "换购";
"checkout_store_label" = "门店";
"checkout_pre_sale_dialog_title" = "尾款详情";
"checkout_final_money_label" = "尾款：";
"checkout_front_money_label" = "已付定金：";
"checkout_no_price" = "暂无报价";
"setllement_joinGroupBuy" = "拼团";
"checkout_product_list_count" = "共%@件";
"checkout_message_label" = "留言";
"checkout_leave_msg_hint" = "选填，请先和商家沟通确认";
"checkout_express_label" = "配送";
"checkout_stores_list_business_time" = "营业时间 %@";
"checkout_scale" = "优惠";

"checkout_settlment_gift" = "赠品";
"checkout_settlment_qty" = "数量 %@";


//6-30
"checkout_settlment_step" = "收货地址";
"checkout_settlment_step_3" = "商品详情";
"checkout_settlment_payment_title" = "付款";

"checkout_selttment_payment_error1" = "订单金额大于500不支持货到付款。";
"checkout_selttment_payment_error2" = "不支持货到付款";
"checkout_serving_tray_title_1" = "以下商品有起购限购要求";
"checkout_serving_tray_title_2" = "所有结算产品都有最低购买要求";
"checkout_return_to_shopping_cart" = "返回购物车";
"checkout_remove_goods" = "移除商品";
"checkout_minimum_purchase" = "%@件起购";
"checkout_limited_to_pieces" = "限购%@件";
"settlement_tariff_title" = "关税";
"settlement_cod_title" = "货到付款服务费";
"checkout_payment_type_error" = "零元单不支持COD支付";

"checkout_payment_online" = "在线支付";
"checkout_payment_COD" = "货到付款";
"checkout_payment_exchanged_to_online_pay" = "由于cod服务费，虚拟资产使用/取消使用等，订单总金额大于500，不可使用cod支付，将为您切换为在线支付。";
"checkout_query_title" = "查询";

"settlement_title_shipping_fee_discount" = "运费折扣";
