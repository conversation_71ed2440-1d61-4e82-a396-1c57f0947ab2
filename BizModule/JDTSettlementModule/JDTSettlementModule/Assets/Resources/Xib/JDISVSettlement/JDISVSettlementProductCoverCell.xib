<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="JDISVSettlementProductCoverCell" id="gTV-IL-0wX" customClass="JDISVSettlementProductCoverCell">
            <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nNi-Do-wM1">
                        <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="GE8-aZ-WNc">
                                <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gd2-gt-1Ee">
                                <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="TOg-35-yAp">
                                <rect key="frame" x="7" y="7" width="56" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="56" id="0Qr-o5-Td1"/>
                                    <constraint firstAttribute="height" constant="56" id="zAA-xM-Ula"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BOr-Zd-7ZD">
                                <rect key="frame" x="20" y="26" width="30" height="18"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="18" id="9Xx-jp-QTf"/>
                                    <constraint firstAttribute="width" constant="30" id="aw7-ae-5eE"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="gd2-gt-1Ee" secondAttribute="trailing" id="258-6t-gcF"/>
                            <constraint firstAttribute="trailing" secondItem="GE8-aZ-WNc" secondAttribute="trailing" id="MLO-7P-J1e"/>
                            <constraint firstItem="TOg-35-yAp" firstAttribute="centerY" secondItem="nNi-Do-wM1" secondAttribute="centerY" id="NtO-Zd-uMx"/>
                            <constraint firstItem="GE8-aZ-WNc" firstAttribute="top" secondItem="nNi-Do-wM1" secondAttribute="top" id="Qiu-gY-ghv"/>
                            <constraint firstItem="GE8-aZ-WNc" firstAttribute="leading" secondItem="nNi-Do-wM1" secondAttribute="leading" id="UPz-HW-ezq"/>
                            <constraint firstItem="gd2-gt-1Ee" firstAttribute="leading" secondItem="nNi-Do-wM1" secondAttribute="leading" id="bup-6G-S9a"/>
                            <constraint firstItem="BOr-Zd-7ZD" firstAttribute="centerY" secondItem="nNi-Do-wM1" secondAttribute="centerY" id="dvY-ci-HSe"/>
                            <constraint firstAttribute="bottom" secondItem="GE8-aZ-WNc" secondAttribute="bottom" id="hJ3-cv-HwU"/>
                            <constraint firstItem="BOr-Zd-7ZD" firstAttribute="centerX" secondItem="nNi-Do-wM1" secondAttribute="centerX" id="k6W-zh-9DP"/>
                            <constraint firstItem="TOg-35-yAp" firstAttribute="centerX" secondItem="nNi-Do-wM1" secondAttribute="centerX" id="lqM-Ue-sy1"/>
                            <constraint firstAttribute="bottom" secondItem="gd2-gt-1Ee" secondAttribute="bottom" id="usJ-az-Uqa"/>
                            <constraint firstItem="gd2-gt-1Ee" firstAttribute="top" secondItem="nNi-Do-wM1" secondAttribute="top" id="xE8-Zc-gec"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="nNi-Do-wM1" secondAttribute="trailing" id="a4N-KI-Xe4"/>
                <constraint firstItem="nNi-Do-wM1" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="h9s-6y-gJT"/>
                <constraint firstAttribute="bottom" secondItem="nNi-Do-wM1" secondAttribute="bottom" id="lF9-uY-pPI"/>
                <constraint firstItem="nNi-Do-wM1" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="uv0-C4-2Bl"/>
            </constraints>
            <connections>
                <outlet property="coverContentView" destination="nNi-Do-wM1" id="Jxo-1Z-cu7"/>
                <outlet property="maskView" destination="gd2-gt-1Ee" id="PuZ-EU-U3R"/>
                <outlet property="skuCoverImageView" destination="GE8-aZ-WNc" id="zhk-at-2mR"/>
                <outlet property="soldOutImageView" destination="TOg-35-yAp" id="CcX-Lr-ceP"/>
                <outlet property="stockOutLabel" destination="BOr-Zd-7ZD" id="dVr-Ka-ESL"/>
            </connections>
            <point key="canvasLocation" x="140.57971014492756" y="153.34821428571428"/>
        </collectionViewCell>
    </objects>
</document>
