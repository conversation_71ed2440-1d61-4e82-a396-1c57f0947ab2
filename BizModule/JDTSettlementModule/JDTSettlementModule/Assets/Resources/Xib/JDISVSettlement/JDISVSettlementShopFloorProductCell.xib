<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShopFloorProductCell" rowHeight="108" id="KGk-i7-Jjw" customClass="JDISVSettlementShopFloorProductCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="108"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="108"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gLy-Jl-C7T">
                        <rect key="frame" x="18" y="9" width="90" height="90"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="90" id="El4-XS-nti"/>
                            <constraint firstAttribute="width" constant="90" id="tm0-Yc-IYz"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="g2F-RU-nBP">
                        <rect key="frame" x="18" y="9" width="90" height="90"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="A6J-Mn-GgV">
                        <rect key="frame" x="120" y="9" width="182" height="20.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="35S-XR-d2Z">
                        <rect key="frame" x="120" y="35.5" width="182" height="14"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="14" id="l2g-sf-F0L"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="￥XXXXXXXXXXXXXX" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EQz-X1-zkt" customClass="KAPriceLabel">
                        <rect key="frame" x="120" y="61.5" width="151" height="18"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="JG7-vO-hxy"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="X1" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jma-xa-LTJ">
                        <rect key="frame" x="283" y="63.5" width="19" height="14"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="14" id="SoW-4v-pMr"/>
                            <constraint firstAttribute="width" constant="19" id="ei2-48-goH"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TTt-8y-WFt">
                        <rect key="frame" x="33" y="24" width="60" height="60"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eRv-MS-7sl">
                                <rect key="frame" x="10" y="15" width="40" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="AuJ-kM-tet"/>
                                    <constraint firstAttribute="height" constant="30" id="XK2-br-OSJ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="eRv-MS-7sl" firstAttribute="centerX" secondItem="TTt-8y-WFt" secondAttribute="centerX" id="3qJ-ai-VUS"/>
                            <constraint firstAttribute="height" constant="60" id="FEt-dy-6q4"/>
                            <constraint firstAttribute="width" constant="60" id="rgT-hT-G08"/>
                            <constraint firstItem="eRv-MS-7sl" firstAttribute="centerY" secondItem="TTt-8y-WFt" secondAttribute="centerY" id="t0J-mz-v8s"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VQq-8A-tfw">
                        <rect key="frame" x="120" y="85.5" width="41.5" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="35S-XR-d2Z" firstAttribute="leading" secondItem="gLy-Jl-C7T" secondAttribute="trailing" constant="12" id="08f-pB-i1N"/>
                    <constraint firstItem="A6J-Mn-GgV" firstAttribute="leading" secondItem="gLy-Jl-C7T" secondAttribute="trailing" constant="12" id="2YW-xe-b4A"/>
                    <constraint firstAttribute="trailing" secondItem="A6J-Mn-GgV" secondAttribute="trailing" constant="18" id="2gT-Ak-3xb"/>
                    <constraint firstItem="Jma-xa-LTJ" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="EQz-X1-zkt" secondAttribute="trailing" constant="12" id="6K0-C3-uup"/>
                    <constraint firstItem="g2F-RU-nBP" firstAttribute="trailing" secondItem="gLy-Jl-C7T" secondAttribute="trailing" id="97S-wf-f4w"/>
                    <constraint firstItem="g2F-RU-nBP" firstAttribute="leading" secondItem="gLy-Jl-C7T" secondAttribute="leading" id="Fde-hb-OMT"/>
                    <constraint firstItem="EQz-X1-zkt" firstAttribute="top" secondItem="35S-XR-d2Z" secondAttribute="bottom" constant="12" id="JMq-8C-lSS"/>
                    <constraint firstItem="VQq-8A-tfw" firstAttribute="top" secondItem="EQz-X1-zkt" secondAttribute="bottom" constant="6" id="NGQ-yU-4e5"/>
                    <constraint firstItem="g2F-RU-nBP" firstAttribute="top" secondItem="gLy-Jl-C7T" secondAttribute="top" id="P9h-o3-Dfp"/>
                    <constraint firstAttribute="bottom" secondItem="gLy-Jl-C7T" secondAttribute="bottom" constant="9" id="ShC-Uh-rVH"/>
                    <constraint firstItem="TTt-8y-WFt" firstAttribute="centerY" secondItem="gLy-Jl-C7T" secondAttribute="centerY" id="Wlk-7r-rqL"/>
                    <constraint firstItem="VQq-8A-tfw" firstAttribute="leading" secondItem="gLy-Jl-C7T" secondAttribute="trailing" constant="12" id="bKf-hv-SxT"/>
                    <constraint firstItem="A6J-Mn-GgV" firstAttribute="top" secondItem="g2F-RU-nBP" secondAttribute="top" id="cSL-Oc-Ba9"/>
                    <constraint firstItem="EQz-X1-zkt" firstAttribute="leading" secondItem="gLy-Jl-C7T" secondAttribute="trailing" constant="12" id="e6b-am-Taj"/>
                    <constraint firstItem="Jma-xa-LTJ" firstAttribute="centerY" secondItem="EQz-X1-zkt" secondAttribute="centerY" id="fU7-eC-Gmk"/>
                    <constraint firstItem="gLy-Jl-C7T" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="goE-4e-GEp"/>
                    <constraint firstItem="g2F-RU-nBP" firstAttribute="bottom" secondItem="gLy-Jl-C7T" secondAttribute="bottom" id="hJA-oD-TNs"/>
                    <constraint firstItem="35S-XR-d2Z" firstAttribute="top" secondItem="A6J-Mn-GgV" secondAttribute="bottom" constant="6" id="kkO-k0-5h1"/>
                    <constraint firstAttribute="trailing" secondItem="Jma-xa-LTJ" secondAttribute="trailing" constant="18" id="oyd-YI-YAN"/>
                    <constraint firstAttribute="trailing" secondItem="35S-XR-d2Z" secondAttribute="trailing" constant="18" id="rff-tu-hPe"/>
                    <constraint firstItem="gLy-Jl-C7T" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="9" id="ux1-7v-PrU"/>
                    <constraint firstItem="TTt-8y-WFt" firstAttribute="centerX" secondItem="gLy-Jl-C7T" secondAttribute="centerX" id="vOw-6w-jQs"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="countLabel" destination="Jma-xa-LTJ" id="x4N-4w-K6c"/>
                <outlet property="countLabelWidth" destination="ei2-48-goH" id="tHz-aU-uml"/>
                <outlet property="coverImageView" destination="gLy-Jl-C7T" id="roR-7b-yLn"/>
                <outlet property="covermaskView" destination="g2F-RU-nBP" id="dLh-L5-P2a"/>
                <outlet property="priceLabel" destination="EQz-X1-zkt" id="K6p-Qt-XNj"/>
                <outlet property="productNameLabel" destination="A6J-Mn-GgV" id="QFI-bw-CLl"/>
                <outlet property="productPropertyLabel" destination="35S-XR-d2Z" id="LHO-4n-9aI"/>
                <outlet property="promiseLabel" destination="VQq-8A-tfw" id="mxA-V6-yVl"/>
                <outlet property="stockOutLabel" destination="eRv-MS-7sl" id="veo-sK-OF5"/>
                <outlet property="stockOutView" destination="TTt-8y-WFt" id="Qog-w5-U2J"/>
            </connections>
            <point key="canvasLocation" x="-123.18840579710145" y="135.9375"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
