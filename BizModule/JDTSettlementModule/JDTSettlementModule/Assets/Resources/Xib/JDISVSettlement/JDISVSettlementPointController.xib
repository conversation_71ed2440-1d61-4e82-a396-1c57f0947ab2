<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="JDISVSettlementPointController">
            <connections>
                <outlet property="bottomView" destination="Fmh-jw-PM0" id="PZM-fJ-Ziw"/>
                <outlet property="bottomViewHeight" destination="gSL-v6-E3u" id="MVS-fJ-dVG"/>
                <outlet property="closeButton" destination="NFi-T4-eOG" id="i3L-mh-pM1"/>
                <outlet property="confirmButton" destination="am4-gR-338" id="aNg-qw-gsi"/>
                <outlet property="contentTitleView" destination="Jvw-3y-eFf" id="h6M-Rr-Nlh"/>
                <outlet property="detailInfoView" destination="AiQ-L0-nA9" id="gJy-V7-kqc"/>
                <outlet property="detailInfoViewHeight" destination="vZz-Ew-YYK" id="Qsi-mK-zrE"/>
                <outlet property="discountInfoLabel" destination="umt-te-SqL" id="r4R-46-Mux"/>
                <outlet property="discountInfoLabelWidth" destination="5rR-Y8-ZNW" id="5RY-TF-S6B"/>
                <outlet property="discountTitleLabel" destination="dxW-p4-PlB" id="9jM-Su-qoR"/>
                <outlet property="discountTitleLabelTrailing" destination="gMp-BS-TX3" id="S1B-F6-mVu"/>
                <outlet property="discountTitleLabelWidth" destination="gqM-gm-DlK" id="upZ-Um-FhO"/>
                <outlet property="pointCountLabel" destination="JDZ-7n-6Cq" id="r2X-6H-rb6"/>
                <outlet property="pointCountLabelWidth" destination="fLz-AU-8Xm" id="MRV-2F-PY0"/>
                <outlet property="pointCountTitleLabel" destination="cXF-pn-PId" id="OWD-5d-KB1"/>
                <outlet property="pointCountTitleLabelWidth" destination="Wvv-uD-lQl" id="pd0-dC-wZo"/>
                <outlet property="sheetTitleLabel" destination="RZ1-Ix-v50" id="Y4R-jP-Eht"/>
                <outlet property="tableView" destination="bhn-xL-puZ" id="YV9-ry-b9b"/>
                <outlet property="useInfoLabel" destination="8Fr-SD-bQx" id="qqg-2H-Nat"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AiQ-L0-nA9">
                    <rect key="frame" x="0.0" y="55" width="414" height="44"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="账户积分数：" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cXF-pn-PId">
                            <rect key="frame" x="18" y="12" width="104" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="Uc9-1o-e6R"/>
                                <constraint firstAttribute="width" constant="104" id="Wvv-uD-lQl"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JDZ-7n-6Cq">
                            <rect key="frame" x="122" y="12" width="42" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="3Md-bQ-hJ3"/>
                                <constraint firstAttribute="width" constant="42" id="fLz-AU-8Xm"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="umt-te-SqL">
                            <rect key="frame" x="354" y="10" width="42" height="20"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="42" id="5rR-Y8-ZNW"/>
                                <constraint firstAttribute="height" constant="20" id="Btj-Vm-PNc"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dxW-p4-PlB">
                            <rect key="frame" x="287" y="12" width="42" height="20"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="42" id="gqM-gm-DlK"/>
                                <constraint firstAttribute="height" constant="20" id="wIZ-q0-KKq"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="JDZ-7n-6Cq" firstAttribute="leading" secondItem="cXF-pn-PId" secondAttribute="trailing" id="Cwt-Sr-cF5"/>
                        <constraint firstItem="cXF-pn-PId" firstAttribute="leading" secondItem="AiQ-L0-nA9" secondAttribute="leading" constant="18" id="Im0-Un-LpK"/>
                        <constraint firstItem="umt-te-SqL" firstAttribute="centerY" secondItem="dxW-p4-PlB" secondAttribute="centerY" constant="-2" id="U5j-MU-VAY"/>
                        <constraint firstItem="JDZ-7n-6Cq" firstAttribute="centerY" secondItem="cXF-pn-PId" secondAttribute="centerY" id="apR-gt-nZl"/>
                        <constraint firstAttribute="trailing" secondItem="dxW-p4-PlB" secondAttribute="trailing" constant="85" id="gMp-BS-TX3"/>
                        <constraint firstItem="cXF-pn-PId" firstAttribute="top" secondItem="AiQ-L0-nA9" secondAttribute="top" constant="12" id="raN-Xw-73y"/>
                        <constraint firstItem="dxW-p4-PlB" firstAttribute="centerY" secondItem="cXF-pn-PId" secondAttribute="centerY" id="v6v-dC-yn6"/>
                        <constraint firstAttribute="height" constant="44" id="vZz-Ew-YYK"/>
                        <constraint firstAttribute="trailing" secondItem="umt-te-SqL" secondAttribute="trailing" constant="18" id="xQT-9l-fwb"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Fmh-jw-PM0">
                    <rect key="frame" x="0.0" y="812" width="414" height="50"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="am4-gR-338">
                            <rect key="frame" x="18" y="5" width="378" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="lHB-rE-TYm"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="am4-gR-338" firstAttribute="leading" secondItem="Fmh-jw-PM0" secondAttribute="leading" constant="18" id="e4Q-bV-W1W"/>
                        <constraint firstAttribute="height" constant="50" id="gSL-v6-E3u"/>
                        <constraint firstAttribute="trailing" secondItem="am4-gR-338" secondAttribute="trailing" constant="18" id="sFy-dt-zOv"/>
                        <constraint firstItem="am4-gR-338" firstAttribute="centerY" secondItem="Fmh-jw-PM0" secondAttribute="centerY" id="ySf-1w-k7Z"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="grouped" separatorStyle="none" rowHeight="44" sectionHeaderHeight="18" sectionFooterHeight="18" translatesAutoresizingMaskIntoConstraints="NO" id="bhn-xL-puZ">
                    <rect key="frame" x="0.0" y="99" width="414" height="713"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </tableView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Jvw-3y-eFf">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="55"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="积分抵扣" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RZ1-Ix-v50">
                            <rect key="frame" x="172.5" y="16.5" width="69.5" height="22"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="22" id="3yh-x1-yNa"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NFi-T4-eOG">
                            <rect key="frame" x="360" y="0.0" width="48" height="55"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="48" id="f3k-BN-bOh"/>
                            </constraints>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="使用说明" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Fr-SD-bQx">
                            <rect key="frame" x="310.5" y="19.5" width="49.5" height="16"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="16" id="TAE-Pu-tBK"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="55" id="9id-yd-lE7"/>
                        <constraint firstAttribute="bottom" secondItem="NFi-T4-eOG" secondAttribute="bottom" id="HFv-0R-hAS"/>
                        <constraint firstItem="NFi-T4-eOG" firstAttribute="leading" secondItem="8Fr-SD-bQx" secondAttribute="trailing" id="LLx-4x-9HY"/>
                        <constraint firstItem="RZ1-Ix-v50" firstAttribute="centerY" secondItem="Jvw-3y-eFf" secondAttribute="centerY" id="R1K-05-ZrP"/>
                        <constraint firstAttribute="trailing" secondItem="NFi-T4-eOG" secondAttribute="trailing" constant="6" id="cLj-bR-dLx"/>
                        <constraint firstItem="8Fr-SD-bQx" firstAttribute="centerY" secondItem="RZ1-Ix-v50" secondAttribute="centerY" id="dgB-Ng-i6L"/>
                        <constraint firstItem="NFi-T4-eOG" firstAttribute="top" secondItem="Jvw-3y-eFf" secondAttribute="top" id="svB-oh-8Oh"/>
                        <constraint firstItem="RZ1-Ix-v50" firstAttribute="centerX" secondItem="Jvw-3y-eFf" secondAttribute="centerX" id="usq-eT-IEZ"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="Fmh-jw-PM0" firstAttribute="top" secondItem="bhn-xL-puZ" secondAttribute="bottom" id="2U7-Oo-qxG"/>
                <constraint firstItem="AiQ-L0-nA9" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="4mD-Be-FQo"/>
                <constraint firstItem="bhn-xL-puZ" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="64Y-E9-lQH"/>
                <constraint firstItem="Fmh-jw-PM0" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="AMn-bj-nKt"/>
                <constraint firstItem="Jvw-3y-eFf" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="Ag7-ma-U5H"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="bottom" secondItem="Fmh-jw-PM0" secondAttribute="bottom" id="Avx-wo-yxf"/>
                <constraint firstItem="Jvw-3y-eFf" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="Bvj-hS-Mb3"/>
                <constraint firstItem="AiQ-L0-nA9" firstAttribute="top" secondItem="Jvw-3y-eFf" secondAttribute="bottom" id="DWG-4X-ejL"/>
                <constraint firstItem="bhn-xL-puZ" firstAttribute="top" secondItem="AiQ-L0-nA9" secondAttribute="bottom" id="Prs-W7-Y9P"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="bhn-xL-puZ" secondAttribute="trailing" id="bJd-xG-ihz"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="AiQ-L0-nA9" secondAttribute="trailing" id="dzn-2x-Mc1"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="Fmh-jw-PM0" secondAttribute="trailing" id="fvZ-hp-cAW"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="Jvw-3y-eFf" secondAttribute="trailing" id="paH-Yc-Sa6"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-54" y="67"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
