<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShipmentUnselectStoreCell" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementShipmentUnselectStoreCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="71"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="71"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bez-LM-XPI">
                        <rect key="frame" x="18" y="12" width="256" height="18"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="GEg-Th-fF7"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xpv-Ig-hhL">
                        <rect key="frame" x="18" y="36" width="256" height="23"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="23" id="tAb-E2-7af"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="DmF-my-9SD">
                        <rect key="frame" x="290" y="29.5" width="12" height="12"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="12" id="96P-lV-E7X"/>
                            <constraint firstAttribute="width" constant="12" id="kBx-YK-cmX"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="bez-LM-XPI" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="58s-vp-AgH"/>
                    <constraint firstItem="DmF-my-9SD" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="C9z-o3-pft"/>
                    <constraint firstItem="xpv-Ig-hhL" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="HzS-jb-DQ8"/>
                    <constraint firstAttribute="trailing" secondItem="DmF-my-9SD" secondAttribute="trailing" constant="18" id="MbK-jq-PkD"/>
                    <constraint firstAttribute="trailing" secondItem="bez-LM-XPI" secondAttribute="trailing" constant="46" id="bhE-cr-QCy"/>
                    <constraint firstItem="bez-LM-XPI" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="eac-jY-H2B"/>
                    <constraint firstItem="xpv-Ig-hhL" firstAttribute="top" secondItem="bez-LM-XPI" secondAttribute="bottom" constant="6" id="jkN-GM-4Nw"/>
                    <constraint firstAttribute="trailing" secondItem="xpv-Ig-hhL" secondAttribute="trailing" constant="46" id="pm5-Ud-TYe"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="rightArrow" destination="DmF-my-9SD" id="CyJ-OA-zHG"/>
                <outlet property="selectOtherStoreLabel" destination="xpv-Ig-hhL" id="Nzp-6q-QaI"/>
                <outlet property="tipLabel" destination="bez-LM-XPI" id="nlR-6D-mEE"/>
            </connections>
            <point key="canvasLocation" x="-335" y="139"/>
        </tableViewCell>
    </objects>
</document>
