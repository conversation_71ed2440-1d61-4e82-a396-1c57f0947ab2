<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementAmountFloorDiscountCell" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementAmountFloorDiscountCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="38"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="38"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z5d-1k-MnC">
                        <rect key="frame" x="18" y="6" width="41.5" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="nxE-r0-Boa"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9LF-ul-ZH4">
                        <rect key="frame" x="244" y="8.5" width="42" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="UAR-HM-vSs"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="Z5d-1k-MnC" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="3dk-f2-t5y"/>
                    <constraint firstItem="Z5d-1k-MnC" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="Bvi-x5-PRb"/>
                    <constraint firstItem="9LF-ul-ZH4" firstAttribute="centerY" secondItem="Z5d-1k-MnC" secondAttribute="centerY" id="Y1U-sz-PRz"/>
                    <constraint firstAttribute="trailing" secondItem="9LF-ul-ZH4" secondAttribute="trailing" constant="34" id="bD3-Fa-ffU"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="freightAmountLabel" destination="9LF-ul-ZH4" id="uS2-J4-uJ8"/>
                <outlet property="freightAmountTitleLabel" destination="Z5d-1k-MnC" id="l6F-Z4-JsV"/>
            </connections>
            <point key="canvasLocation" x="19" y="139"/>
        </tableViewCell>
    </objects>
</document>
