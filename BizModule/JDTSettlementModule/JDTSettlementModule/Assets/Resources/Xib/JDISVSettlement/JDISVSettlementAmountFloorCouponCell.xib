<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementAmountFloorCouponCell" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementAmountFloorCouponCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="38"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="38"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8ug-NN-1SX">
                        <rect key="frame" x="18" y="9" width="41.5" height="20.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="x6W-sO-kpS">
                        <rect key="frame" x="244.5" y="9" width="41.5" height="20.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="RGV-Bb-3IC">
                        <rect key="frame" x="286" y="7.5" width="20" height="24"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="1Kp-ph-GXZ"/>
                            <constraint firstAttribute="height" constant="24" id="31y-qP-UOS"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="zIV-MK-Ygr">
                        <rect key="frame" x="63.5" y="10.5" width="40" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="40" id="IZe-oa-OfL"/>
                            <constraint firstAttribute="height" constant="18" id="QeL-Fc-r6T"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="8ug-NN-1SX" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="9" id="6NU-bM-yBP"/>
                    <constraint firstItem="RGV-Bb-3IC" firstAttribute="centerY" secondItem="8ug-NN-1SX" secondAttribute="centerY" id="93a-bg-AEh"/>
                    <constraint firstItem="x6W-sO-kpS" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="zIV-MK-Ygr" secondAttribute="trailing" constant="10" id="EKZ-72-9oV"/>
                    <constraint firstItem="x6W-sO-kpS" firstAttribute="firstBaseline" secondItem="8ug-NN-1SX" secondAttribute="firstBaseline" id="JsG-WB-5Et"/>
                    <constraint firstItem="zIV-MK-Ygr" firstAttribute="centerY" secondItem="8ug-NN-1SX" secondAttribute="centerY" id="ZlB-t4-IIG"/>
                    <constraint firstAttribute="trailing" secondItem="x6W-sO-kpS" secondAttribute="trailing" constant="34" id="gXt-rV-J5Q"/>
                    <constraint firstItem="zIV-MK-Ygr" firstAttribute="leading" secondItem="8ug-NN-1SX" secondAttribute="trailing" constant="4" id="lbk-IL-EvA"/>
                    <constraint firstItem="8ug-NN-1SX" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="vIT-aX-RIg"/>
                    <constraint firstItem="RGV-Bb-3IC" firstAttribute="leading" secondItem="x6W-sO-kpS" secondAttribute="trailing" id="zfs-Df-H7D"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="arrowRightWidth" destination="1Kp-ph-GXZ" id="U8p-do-I5a"/>
                <outlet property="availableTagView" destination="zIV-MK-Ygr" id="nRc-er-fEO"/>
                <outlet property="availableTagViewWidth" destination="IZe-oa-OfL" id="JQ0-KX-DOx"/>
                <outlet property="couponInfoLabel" destination="x6W-sO-kpS" id="Hlu-rX-Pse"/>
                <outlet property="couponTitleLabel" destination="8ug-NN-1SX" id="cmg-Bf-dCJ"/>
                <outlet property="infoTrailing" destination="gXt-rV-J5Q" id="pI1-dq-AtR"/>
                <outlet property="rightArrowImgView" destination="RGV-Bb-3IC" id="qUa-vR-TFA"/>
            </connections>
            <point key="canvasLocation" x="-54" y="48"/>
        </tableViewCell>
    </objects>
</document>
