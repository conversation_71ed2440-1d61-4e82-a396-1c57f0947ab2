<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShipmentSelectStoreCell" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementShipmentSelectStoreCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="234"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="234"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="250" text="hfkdhfdhkfjhkhkfdhfkjdh" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2Kn-tJ-2jC">
                        <rect key="frame" x="18" y="12" width="186" height="23"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="23" id="RD5-O8-gUc"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="trK-IQ-LP7">
                        <rect key="frame" x="210" y="14.5" width="32" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="32" id="3gt-1G-x9V"/>
                            <constraint firstAttribute="height" constant="18" id="Naa-H9-iaW"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="p5d-P5-Nkd">
                        <rect key="frame" x="260" y="13.5" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="9nP-ba-oGC"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Ihk-or-Mu3">
                        <rect key="frame" x="18" y="51" width="12" height="12"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="12" id="dDc-7s-48c"/>
                            <constraint firstAttribute="width" constant="12" id="foz-gT-ReU"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="B1s-16-Dwq">
                        <rect key="frame" x="18" y="77" width="12" height="12"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="12" id="3B9-fv-tvl"/>
                            <constraint firstAttribute="height" constant="12" id="fmk-le-Gz6"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XHP-3W-EzR">
                        <rect key="frame" x="34" y="47" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="htS-Ca-HnZ"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Q1P-2I-Hz4">
                        <rect key="frame" x="34" y="74" width="41.5" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="XbP-fB-GJa"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hNM-bA-gyG">
                        <rect key="frame" x="272" y="74" width="48" height="12"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="48" id="Sx9-Ix-joB"/>
                            <constraint firstAttribute="height" constant="12" id="cax-mG-tJz"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bwo-z0-Mfn">
                        <rect key="frame" x="18" y="112" width="284" height="57"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Mg-a9-pxv">
                                <rect key="frame" x="12" y="12" width="260" height="33"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="0Mg-a9-pxv" firstAttribute="top" secondItem="bwo-z0-Mfn" secondAttribute="top" constant="12" id="BoM-w3-zH7"/>
                            <constraint firstAttribute="height" constant="57" id="K6O-vZ-rE0"/>
                            <constraint firstAttribute="bottom" secondItem="0Mg-a9-pxv" secondAttribute="bottom" constant="12" id="QGs-he-eoI"/>
                            <constraint firstAttribute="trailing" secondItem="0Mg-a9-pxv" secondAttribute="trailing" constant="12" id="dTJ-7X-Mbt"/>
                            <constraint firstItem="0Mg-a9-pxv" firstAttribute="leading" secondItem="bwo-z0-Mfn" secondAttribute="leading" constant="12" id="k5c-4I-Ne0"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jyJ-tA-kK0">
                        <rect key="frame" x="82" y="47" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="xOq-Sc-9cA"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="2Kn-tJ-2jC" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="07r-MQ-j7o"/>
                    <constraint firstItem="p5d-P5-Nkd" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="2Kn-tJ-2jC" secondAttribute="trailing" constant="6" id="6B6-sE-gcS"/>
                    <constraint firstItem="XHP-3W-EzR" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="34" id="Bum-vK-qrn"/>
                    <constraint firstItem="trK-IQ-LP7" firstAttribute="centerY" secondItem="2Kn-tJ-2jC" secondAttribute="centerY" id="Crz-ac-pyf"/>
                    <constraint firstItem="bwo-z0-Mfn" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="EXg-Hf-5I2"/>
                    <constraint firstItem="B1s-16-Dwq" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="EZc-dH-OEe"/>
                    <constraint firstItem="XHP-3W-EzR" firstAttribute="top" secondItem="Ihk-or-Mu3" secondAttribute="top" constant="-4" id="Gkf-Mb-ggv"/>
                    <constraint firstAttribute="trailing" secondItem="p5d-P5-Nkd" secondAttribute="trailing" constant="18" id="L8S-wb-ix2"/>
                    <constraint firstItem="jyJ-tA-kK0" firstAttribute="centerY" secondItem="XHP-3W-EzR" secondAttribute="centerY" id="P2X-55-TJB"/>
                    <constraint firstItem="B1s-16-Dwq" firstAttribute="top" secondItem="Ihk-or-Mu3" secondAttribute="bottom" constant="14" id="P8q-jS-fZl"/>
                    <constraint firstAttribute="trailing" secondItem="hNM-bA-gyG" secondAttribute="trailing" id="RcV-ri-YrS"/>
                    <constraint firstAttribute="trailing" secondItem="bwo-z0-Mfn" secondAttribute="trailing" constant="18" id="WBN-JG-Drg"/>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Q1P-2I-Hz4" secondAttribute="trailing" constant="85" id="Xdn-mr-XGf"/>
                    <constraint firstItem="hNM-bA-gyG" firstAttribute="top" secondItem="Q1P-2I-Hz4" secondAttribute="top" id="Xfl-56-JAp"/>
                    <constraint firstItem="bwo-z0-Mfn" firstAttribute="top" secondItem="Q1P-2I-Hz4" secondAttribute="bottom" constant="18" id="dBb-6C-S5m"/>
                    <constraint firstItem="Q1P-2I-Hz4" firstAttribute="top" secondItem="B1s-16-Dwq" secondAttribute="top" constant="-3" id="itD-ah-61r"/>
                    <constraint firstItem="jyJ-tA-kK0" firstAttribute="leading" secondItem="XHP-3W-EzR" secondAttribute="trailing" constant="6" id="l90-LI-UK6"/>
                    <constraint firstItem="Ihk-or-Mu3" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="m7P-je-cE5"/>
                    <constraint firstItem="p5d-P5-Nkd" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="trK-IQ-LP7" secondAttribute="trailing" constant="1" id="pG5-aL-Iwf"/>
                    <constraint firstItem="Ihk-or-Mu3" firstAttribute="top" secondItem="2Kn-tJ-2jC" secondAttribute="bottom" constant="16" id="q1A-pJ-htx"/>
                    <constraint firstItem="trK-IQ-LP7" firstAttribute="leading" secondItem="2Kn-tJ-2jC" secondAttribute="trailing" constant="6" id="qx3-F6-aNW"/>
                    <constraint firstItem="p5d-P5-Nkd" firstAttribute="centerY" secondItem="2Kn-tJ-2jC" secondAttribute="centerY" id="tdj-sh-39c"/>
                    <constraint firstItem="Q1P-2I-Hz4" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="34" id="w8N-7Y-f4F"/>
                    <constraint firstItem="2Kn-tJ-2jC" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="yam-3S-Pxl"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="addressIcon" destination="B1s-16-Dwq" id="Ped-s3-1bT"/>
                <outlet property="addressLabel" destination="Q1P-2I-Hz4" id="rNt-rr-HuP"/>
                <outlet property="addressLabelHeight" destination="XbP-fB-GJa" id="R0x-Am-pzG"/>
                <outlet property="bussinessTimeIcon" destination="Ihk-or-Mu3" id="exB-gH-EqB"/>
                <outlet property="bussinessTimeLabel" destination="jyJ-tA-kK0" id="3d3-Ei-6ut"/>
                <outlet property="bussinessTimeTitleLabel" destination="XHP-3W-EzR" id="hsJ-9F-5dq"/>
                <outlet property="distanceLabel" destination="p5d-P5-Nkd" id="1oz-gV-6DU"/>
                <outlet property="recentMarkImgView" destination="trK-IQ-LP7" id="hx1-gq-AxD"/>
                <outlet property="recentMarkImgViewWidth" destination="3gt-1G-x9V" id="6aJ-kn-7YF"/>
                <outlet property="rightArrowImgView" destination="hNM-bA-gyG" id="Oup-Zy-Bme"/>
                <outlet property="storeNameLabel" destination="2Kn-tJ-2jC" id="sQu-Pm-Dg1"/>
                <outlet property="storeName_distanceLabel_space" destination="6B6-sE-gcS" id="D4M-14-kCk"/>
                <outlet property="tipBgView" destination="bwo-z0-Mfn" id="wLK-a8-FmZ"/>
                <outlet property="tipBgViewHeight" destination="K6O-vZ-rE0" id="4Xg-Q7-qhi"/>
                <outlet property="tipsLabel" destination="0Mg-a9-pxv" id="ojN-JB-OaS"/>
            </connections>
            <point key="canvasLocation" x="-54" y="139"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
