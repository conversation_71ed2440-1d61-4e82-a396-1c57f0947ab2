<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="19" id="KGk-i7-Jjw" customClass="JDISVSettlementCouponItemCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="136"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="136"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="p6r-1p-c48">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="103"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="1v8-Fj-32z">
                                <rect key="frame" x="58.5" y="12.5" width="41.5" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="41.5" id="axL-y0-ZjR"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="满¥900000元可用" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="B7g-43-nRr">
                                <rect key="frame" x="12" y="35.5" width="141.5" height="20.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="全品类商品可用" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="A70-Kr-E3J">
                                <rect key="frame" x="12" y="56" width="121.5" height="20.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2021.08.01-2021.08.31" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4DC-ij-6Fl">
                                <rect key="frame" x="12" y="84.5" width="129" height="14.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="04Y-vl-bd5">
                                <rect key="frame" x="12" y="12" width="41.5" height="20.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DPJ-ZP-ZRe">
                                <rect key="frame" x="264" y="41.5" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="20" id="HK7-F6-Xfv"/>
                                    <constraint firstAttribute="height" constant="20" id="x0d-2J-zip"/>
                                </constraints>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="4DC-ij-6Fl" firstAttribute="leading" secondItem="04Y-vl-bd5" secondAttribute="leading" id="5am-fC-K6W"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="4DC-ij-6Fl" secondAttribute="trailing" constant="110" id="EU7-Th-pEW"/>
                            <constraint firstItem="4DC-ij-6Fl" firstAttribute="top" secondItem="A70-Kr-E3J" secondAttribute="bottom" constant="8" id="F55-GU-Vo0"/>
                            <constraint firstItem="04Y-vl-bd5" firstAttribute="top" secondItem="p6r-1p-c48" secondAttribute="top" constant="12" id="I17-9N-3Fr"/>
                            <constraint firstItem="B7g-43-nRr" firstAttribute="top" secondItem="1v8-Fj-32z" secondAttribute="bottom" constant="3" id="JwD-US-kfq"/>
                            <constraint firstItem="DPJ-ZP-ZRe" firstAttribute="centerY" secondItem="p6r-1p-c48" secondAttribute="centerY" id="KVe-iC-yWE"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="A70-Kr-E3J" secondAttribute="trailing" constant="110" id="MVQ-lQ-WGD"/>
                            <constraint firstAttribute="trailing" secondItem="DPJ-ZP-ZRe" secondAttribute="trailing" constant="36" id="Rq7-Mk-Opa"/>
                            <constraint firstItem="B7g-43-nRr" firstAttribute="leading" secondItem="04Y-vl-bd5" secondAttribute="leading" id="SXl-yA-Nbh"/>
                            <constraint firstItem="A70-Kr-E3J" firstAttribute="leading" secondItem="04Y-vl-bd5" secondAttribute="leading" id="bCj-te-mDo"/>
                            <constraint firstItem="1v8-Fj-32z" firstAttribute="centerY" secondItem="04Y-vl-bd5" secondAttribute="centerY" id="bUD-OJ-gzZ"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="B7g-43-nRr" secondAttribute="trailing" constant="110" id="dN1-rb-2gh"/>
                            <constraint firstItem="A70-Kr-E3J" firstAttribute="top" secondItem="B7g-43-nRr" secondAttribute="bottom" id="e7r-Bs-S5n"/>
                            <constraint firstItem="04Y-vl-bd5" firstAttribute="leading" secondItem="p6r-1p-c48" secondAttribute="leading" constant="12" id="fgn-an-8W9"/>
                            <constraint firstItem="1v8-Fj-32z" firstAttribute="leading" secondItem="04Y-vl-bd5" secondAttribute="trailing" constant="5" id="qsx-kv-bAK"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4lR-h8-Bcx">
                        <rect key="frame" x="223" y="0.0" width="1" height="136"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="1" id="wqe-fx-RBs"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cdk-Sw-qbY">
                        <rect key="frame" x="216.5" y="-7" width="14" height="14"/>
                        <color key="backgroundColor" systemColor="linkColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="14" id="54l-rq-mYB"/>
                            <constraint firstAttribute="width" constant="14" id="Ymy-ED-sHs"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sX1-My-JBa">
                        <rect key="frame" x="0.5" y="103" width="319" height="32.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mdC-xC-n3C">
                                <rect key="frame" x="0.0" y="0.0" width="319" height="32.5"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FcB-Ix-Whf">
                                <rect key="frame" x="12" y="6" width="295" height="20.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="FcB-Ix-Whf" secondAttribute="bottom" constant="6" id="0Ql-ZK-cva"/>
                            <constraint firstItem="mdC-xC-n3C" firstAttribute="top" secondItem="sX1-My-JBa" secondAttribute="top" id="1gG-bV-LT8"/>
                            <constraint firstAttribute="height" constant="32.5" id="4Dq-m8-Qdb"/>
                            <constraint firstItem="FcB-Ix-Whf" firstAttribute="top" secondItem="sX1-My-JBa" secondAttribute="top" constant="6" id="8lf-ck-h4d"/>
                            <constraint firstAttribute="trailing" secondItem="FcB-Ix-Whf" secondAttribute="trailing" constant="12" id="Ecv-CH-6Iv"/>
                            <constraint firstItem="FcB-Ix-Whf" firstAttribute="leading" secondItem="sX1-My-JBa" secondAttribute="leading" constant="12" id="NPG-hb-mRW"/>
                            <constraint firstAttribute="bottom" secondItem="mdC-xC-n3C" secondAttribute="bottom" id="ZEh-Ee-pIY"/>
                            <constraint firstItem="mdC-xC-n3C" firstAttribute="leading" secondItem="sX1-My-JBa" secondAttribute="leading" id="cEc-Te-LhP"/>
                            <constraint firstAttribute="trailing" secondItem="mdC-xC-n3C" secondAttribute="trailing" id="vJ1-7x-4gF"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="f1B-VA-qeK">
                        <rect key="frame" x="216.5" y="129" width="14" height="14"/>
                        <color key="backgroundColor" systemColor="linkColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="14" id="0OD-HQ-QnQ"/>
                            <constraint firstAttribute="width" constant="14" id="MlA-Js-gjA"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="sX1-My-JBa" secondAttribute="trailing" constant="0.5" id="93E-o8-DRh"/>
                    <constraint firstItem="f1B-VA-qeK" firstAttribute="centerX" secondItem="Cdk-Sw-qbY" secondAttribute="centerX" id="C3d-Fg-uKe"/>
                    <constraint firstItem="f1B-VA-qeK" firstAttribute="bottom" secondItem="4lR-h8-Bcx" secondAttribute="bottom" constant="7" id="D52-bf-L5B"/>
                    <constraint firstItem="4lR-h8-Bcx" firstAttribute="centerX" secondItem="Cdk-Sw-qbY" secondAttribute="centerX" id="FY8-CD-JTP"/>
                    <constraint firstAttribute="bottom" secondItem="4lR-h8-Bcx" secondAttribute="bottom" id="Jwy-vV-2Q9"/>
                    <constraint firstItem="p6r-1p-c48" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Mkm-eR-W9i"/>
                    <constraint firstItem="sX1-My-JBa" firstAttribute="top" secondItem="p6r-1p-c48" secondAttribute="bottom" id="NvX-zw-TSt"/>
                    <constraint firstItem="sX1-My-JBa" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="0.5" id="aj1-4I-nhA"/>
                    <constraint firstAttribute="trailing" secondItem="4lR-h8-Bcx" secondAttribute="trailing" constant="96" id="hS4-PS-P6r"/>
                    <constraint firstAttribute="bottom" secondItem="sX1-My-JBa" secondAttribute="bottom" constant="0.5" id="jcZ-jC-Ukk"/>
                    <constraint firstAttribute="trailing" secondItem="p6r-1p-c48" secondAttribute="trailing" id="sHw-c9-UUi"/>
                    <constraint firstItem="p6r-1p-c48" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="tyx-GB-l6n"/>
                    <constraint firstItem="Cdk-Sw-qbY" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="-7" id="v4K-cg-ZvJ"/>
                    <constraint firstItem="4lR-h8-Bcx" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="wWJ-yt-p2f"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="bottomColorBgView" destination="mdC-xC-n3C" id="OhY-U7-ZoP"/>
                <outlet property="bottomView" destination="sX1-My-JBa" id="OyF-tx-05T"/>
                <outlet property="bottomViewHeight" destination="4Dq-m8-Qdb" id="AQb-c7-YV5"/>
                <outlet property="couponContentView" destination="p6r-1p-c48" id="Wzq-Bd-KTK"/>
                <outlet property="couponLimitInfoLabel" destination="FcB-Ix-Whf" id="VYF-nO-CPg"/>
                <outlet property="couponTypeLabel" destination="1v8-Fj-32z" id="v2H-3y-bfw"/>
                <outlet property="couponTypeLabelWidth" destination="axL-y0-ZjR" id="4d5-Yn-6Xk"/>
                <outlet property="discountAmountLabel" destination="04Y-vl-bd5" id="Auf-Fn-Dsb"/>
                <outlet property="leftSemicircleView" destination="Cdk-Sw-qbY" id="g8W-W0-PRg"/>
                <outlet property="lineView" destination="4lR-h8-Bcx" id="YnZ-iC-NGO"/>
                <outlet property="marginTop" destination="NvX-zw-TSt" id="T2S-rY-eOg"/>
                <outlet property="quotaLabel" destination="B7g-43-nRr" id="aCT-Ue-aWg"/>
                <outlet property="rightSemicircleView" destination="f1B-VA-qeK" id="Ybr-UK-BzF"/>
                <outlet property="selectedRadio" destination="DPJ-ZP-ZRe" id="iky-yb-kqR"/>
                <outlet property="timeLimitLabel" destination="4DC-ij-6Fl" id="mq9-va-Lne"/>
                <outlet property="useScopeLable" destination="A70-Kr-E3J" id="ZQw-eC-ZbW"/>
            </connections>
            <point key="canvasLocation" x="18.840579710144929" y="37.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="linkColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
