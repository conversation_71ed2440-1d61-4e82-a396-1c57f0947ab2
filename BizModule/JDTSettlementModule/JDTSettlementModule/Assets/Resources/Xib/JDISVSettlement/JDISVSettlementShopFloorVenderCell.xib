<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShopFloorVenderCell" id="KGk-i7-Jjw" customClass="JDISVSettlementShopFloorVenderCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="i8l-W1-GZ5">
                        <rect key="frame" x="18" y="0.0" width="284" height="44"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Q4d-m8-SmO">
                        <rect key="frame" x="18" y="12" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="MWs-GL-lHw"/>
                            <constraint firstAttribute="width" constant="20" id="iKB-B0-PDe"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="五粮液专卖旗舰店" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WHv-jT-Bnz">
                        <rect key="frame" x="43" y="14" width="139" height="16"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16" id="dfW-mQ-jkZ"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="WHv-jT-Bnz" firstAttribute="centerY" secondItem="Q4d-m8-SmO" secondAttribute="centerY" id="5EY-uy-hJp"/>
                    <constraint firstItem="i8l-W1-GZ5" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="EZt-MN-45T"/>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="WHv-jT-Bnz" secondAttribute="trailing" constant="18" id="Ehc-zQ-d2U"/>
                    <constraint firstItem="WHv-jT-Bnz" firstAttribute="leading" secondItem="Q4d-m8-SmO" secondAttribute="trailing" constant="5" id="HsW-9K-Gu6"/>
                    <constraint firstAttribute="trailing" secondItem="i8l-W1-GZ5" secondAttribute="trailing" constant="18" id="YxX-U8-nWg"/>
                    <constraint firstItem="Q4d-m8-SmO" firstAttribute="centerY" secondItem="i8l-W1-GZ5" secondAttribute="centerY" id="iSC-wg-3WU"/>
                    <constraint firstItem="i8l-W1-GZ5" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="kOL-ge-gUP"/>
                    <constraint firstItem="Q4d-m8-SmO" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="urh-MD-bie"/>
                    <constraint firstAttribute="bottom" secondItem="i8l-W1-GZ5" secondAttribute="bottom" id="ynZ-Ta-F0D"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="KSABGView" destination="i8l-W1-GZ5" id="Axq-Wr-hMy"/>
                <outlet property="bottomConstraint" destination="ynZ-Ta-F0D" id="xCj-tn-aai"/>
                <outlet property="iconLeading" destination="urh-MD-bie" id="rMl-h4-a7p"/>
                <outlet property="topConstraint" destination="EZt-MN-45T" id="Isa-CW-kfG"/>
                <outlet property="venderIconImgView" destination="Q4d-m8-SmO" id="WB2-GU-mkS"/>
                <outlet property="venderNameLabel" destination="WHv-jT-Bnz" id="8ed-r8-TDR"/>
            </connections>
            <point key="canvasLocation" x="-122" y="139"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
