<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementPointItemCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="50"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="50"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="消耗5000积分，抵扣" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DGu-aA-RN9">
                        <rect key="frame" x="18" y="15" width="162.5" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="FHh-eV-wTx"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RFA-wU-RUP">
                        <rect key="frame" x="282" y="15" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="Wcw-YB-4S0"/>
                            <constraint firstAttribute="height" constant="20" id="rn8-bL-zkf"/>
                        </constraints>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="DGu-aA-RN9" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="4ZA-jq-txM"/>
                    <constraint firstAttribute="trailing" secondItem="RFA-wU-RUP" secondAttribute="trailing" constant="18" id="Kct-Pg-3gl"/>
                    <constraint firstItem="DGu-aA-RN9" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="LEM-M2-PK9"/>
                    <constraint firstItem="RFA-wU-RUP" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="hSs-Y2-88g"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="discountInfoLabel" destination="DGu-aA-RN9" id="sVP-JF-nNg"/>
                <outlet property="radioButton" destination="RFA-wU-RUP" id="cQe-hM-UNN"/>
            </connections>
            <point key="canvasLocation" x="-333" y="48"/>
        </tableViewCell>
    </objects>
</document>
