<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="JDISVSettlementShopFloorStepCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xce-Kh-28L">
                        <rect key="frame" x="18" y="0.0" width="42" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rCi-5M-mjf">
                        <rect key="frame" x="66" y="0.0" width="42" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="xce-Kh-28L" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="5GP-hi-kao"/>
                    <constraint firstItem="xce-Kh-28L" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="NBv-qE-nap"/>
                    <constraint firstItem="rCi-5M-mjf" firstAttribute="leading" secondItem="xce-Kh-28L" secondAttribute="trailing" constant="6" id="RwJ-9M-c15"/>
                    <constraint firstItem="rCi-5M-mjf" firstAttribute="centerY" secondItem="xce-Kh-28L" secondAttribute="centerY" id="Spg-bK-iwE"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="Fni-3Q-67v"/>
            <connections>
                <outlet property="stepNumber" destination="xce-Kh-28L" id="6Db-ym-Kcu"/>
                <outlet property="stepTitle" destination="rCi-5M-mjf" id="WnG-6U-l3Z"/>
            </connections>
            <point key="canvasLocation" x="-6" y="20"/>
        </tableViewCell>
    </objects>
</document>
