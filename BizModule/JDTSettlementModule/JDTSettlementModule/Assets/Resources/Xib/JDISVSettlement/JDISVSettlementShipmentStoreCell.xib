<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShipmentStoreCell" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementShipmentStoreCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="127"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="127"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="U0r-mQ-Y6D">
                        <rect key="frame" x="50" y="12" width="36" height="23"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="23" id="b7f-q4-Bal"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="mCi-WA-1u2">
                        <rect key="frame" x="92" y="14.5" width="28" height="18"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="JLS-qY-hJY"/>
                            <constraint firstAttribute="width" constant="28" id="WDX-gc-Jgk"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5UW-Lh-dqt">
                        <rect key="frame" x="260" y="13.5" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="42" id="bCQ-2v-9Ph"/>
                            <constraint firstAttribute="height" constant="20" id="x1G-My-BMZ"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="QWT-ug-iZz">
                        <rect key="frame" x="50" y="51" width="12" height="12"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="12" id="aPf-Xu-ceK"/>
                            <constraint firstAttribute="height" constant="12" id="fwe-hb-SBu"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bb5-Nc-n8j">
                        <rect key="frame" x="66" y="47" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="xcC-j1-s4f"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HIq-gw-wfF">
                        <rect key="frame" x="114" y="47" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="kWl-68-RWh"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="MfL-Hl-xzK">
                        <rect key="frame" x="50" y="77" width="12" height="12"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="12" id="3Kz-8o-Zhw"/>
                            <constraint firstAttribute="width" constant="12" id="WtG-Ze-17b"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Lp-L9-n3e">
                        <rect key="frame" x="66" y="73" width="41.5" height="20.5"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20.5" id="Xnw-u4-dek"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N4H-Bc-dSl">
                        <rect key="frame" x="66" y="99.5" width="42" height="17"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="17" id="uVz-yg-qdS"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" userInteractionEnabled="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mbP-pd-uhf">
                        <rect key="frame" x="18" y="53.5" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="l6u-8H-LKS"/>
                            <constraint firstAttribute="width" constant="20" id="qt0-mG-kUP"/>
                        </constraints>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="5UW-Lh-dqt" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="U0r-mQ-Y6D" secondAttribute="trailing" constant="6" id="2Qe-AA-Pcs"/>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="6Lp-L9-n3e" secondAttribute="trailing" constant="18" id="32A-HW-2xT"/>
                    <constraint firstItem="6Lp-L9-n3e" firstAttribute="leading" secondItem="MfL-Hl-xzK" secondAttribute="trailing" constant="4" id="5jx-S8-mCg"/>
                    <constraint firstItem="mbP-pd-uhf" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Dv1-sB-H8d"/>
                    <constraint firstItem="mCi-WA-1u2" firstAttribute="centerY" secondItem="U0r-mQ-Y6D" secondAttribute="centerY" id="EAB-Do-KI3"/>
                    <constraint firstItem="HIq-gw-wfF" firstAttribute="centerY" secondItem="QWT-ug-iZz" secondAttribute="centerY" id="JaN-yN-3Hd"/>
                    <constraint firstItem="Bb5-Nc-n8j" firstAttribute="top" secondItem="U0r-mQ-Y6D" secondAttribute="bottom" constant="12" id="JuG-kA-sbX"/>
                    <constraint firstItem="MfL-Hl-xzK" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="50" id="Sjb-Fq-2Cu"/>
                    <constraint firstItem="mCi-WA-1u2" firstAttribute="leading" secondItem="U0r-mQ-Y6D" secondAttribute="trailing" constant="6" id="UoI-eO-1l8"/>
                    <constraint firstItem="mbP-pd-uhf" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="WV7-a7-o3z"/>
                    <constraint firstItem="N4H-Bc-dSl" firstAttribute="top" secondItem="6Lp-L9-n3e" secondAttribute="bottom" constant="6" id="WcF-bZ-3dy"/>
                    <constraint firstItem="6Lp-L9-n3e" firstAttribute="top" secondItem="Bb5-Nc-n8j" secondAttribute="bottom" constant="6" id="XrB-fq-f85"/>
                    <constraint firstItem="HIq-gw-wfF" firstAttribute="leading" secondItem="Bb5-Nc-n8j" secondAttribute="trailing" constant="6" id="aRR-i8-wDv"/>
                    <constraint firstItem="5UW-Lh-dqt" firstAttribute="centerY" secondItem="U0r-mQ-Y6D" secondAttribute="centerY" id="biV-7e-QMk"/>
                    <constraint firstItem="U0r-mQ-Y6D" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="c0g-kR-hRb"/>
                    <constraint firstItem="N4H-Bc-dSl" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="66" id="cul-5v-ODB"/>
                    <constraint firstItem="Bb5-Nc-n8j" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="66" id="dtP-vS-6tS"/>
                    <constraint firstItem="Bb5-Nc-n8j" firstAttribute="centerY" secondItem="QWT-ug-iZz" secondAttribute="centerY" id="pHG-5g-zhn"/>
                    <constraint firstAttribute="trailing" secondItem="5UW-Lh-dqt" secondAttribute="trailing" constant="18" id="s5d-pv-QvJ"/>
                    <constraint firstItem="QWT-ug-iZz" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="50" id="sdw-Zr-Dma"/>
                    <constraint firstItem="U0r-mQ-Y6D" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="50" id="tRC-IO-KLy"/>
                    <constraint firstItem="MfL-Hl-xzK" firstAttribute="top" secondItem="QWT-ug-iZz" secondAttribute="bottom" constant="14" id="yT2-2Y-sLc"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="bussinessTimeIconImgView" destination="QWT-ug-iZz" id="lnT-jT-aZc"/>
                <outlet property="bussinessTimeLabel" destination="HIq-gw-wfF" id="ALX-rN-pMn"/>
                <outlet property="bussinessTimeTitleLabel" destination="Bb5-Nc-n8j" id="fkg-uF-NAJ"/>
                <outlet property="checkRadio" destination="mbP-pd-uhf" id="Q8h-ce-N8R"/>
                <outlet property="distanceLabel" destination="5UW-Lh-dqt" id="vTQ-Qv-mbI"/>
                <outlet property="distanceLabelWidth" destination="bCQ-2v-9Ph" id="yZ7-V9-qnu"/>
                <outlet property="locationIconImgView" destination="MfL-Hl-xzK" id="e8A-dt-cJq"/>
                <outlet property="locationLabel" destination="6Lp-L9-n3e" id="PFj-h6-y05"/>
                <outlet property="locationLabelHeight" destination="Xnw-u4-dek" id="N2Z-Xf-wur"/>
                <outlet property="recentRemarkImgView" destination="mCi-WA-1u2" id="2Gi-jy-dCc"/>
                <outlet property="stockoutTipLabel" destination="N4H-Bc-dSl" id="OGS-k7-JNu"/>
                <outlet property="storeNameLabel" destination="U0r-mQ-Y6D" id="nhj-ft-W8Z"/>
                <outlet property="storeName_distanceLabel_space" destination="2Qe-AA-Pcs" id="Sjr-mf-WAr"/>
            </connections>
            <point key="canvasLocation" x="-54" y="139"/>
        </tableViewCell>
    </objects>
</document>
