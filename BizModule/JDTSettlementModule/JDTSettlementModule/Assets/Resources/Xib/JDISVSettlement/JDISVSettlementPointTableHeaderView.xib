<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JDISVSettlementPointTableHeaderView">
            <rect key="frame" x="0.0" y="0.0" width="414" height="36"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="t6p-gg-ExR">
                    <rect key="frame" x="18" y="0.0" width="378" height="27"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MuM-4c-7Ng">
                            <rect key="frame" x="12" y="5" width="41.5" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="BCs-TS-1oB"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="MuM-4c-7Ng" firstAttribute="leading" secondItem="t6p-gg-ExR" secondAttribute="leading" constant="12" id="5py-HH-7Wd"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="MuM-4c-7Ng" secondAttribute="trailing" constant="12" id="7jL-Cf-fy7"/>
                        <constraint firstAttribute="height" constant="27" id="FCP-Ag-eBK"/>
                        <constraint firstItem="MuM-4c-7Ng" firstAttribute="centerY" secondItem="t6p-gg-ExR" secondAttribute="centerY" id="pUq-Q0-AjI"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="t6p-gg-ExR" secondAttribute="trailing" constant="18" id="ThK-P0-uxR"/>
                <constraint firstItem="t6p-gg-ExR" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="18" id="UjP-eA-aPG"/>
                <constraint firstItem="t6p-gg-ExR" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="hg2-SF-mGM"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="contentView" destination="t6p-gg-ExR" id="7nX-jK-omp"/>
                <outlet property="contentViewHeight" destination="FCP-Ag-eBK" id="bNW-x5-j4W"/>
                <outlet property="infoLabel" destination="MuM-4c-7Ng" id="nqb-EO-PGJ"/>
                <outlet property="infoLabelHeight" destination="BCs-TS-1oB" id="8rD-T9-oUL"/>
            </connections>
            <point key="canvasLocation" x="19" y="48"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
