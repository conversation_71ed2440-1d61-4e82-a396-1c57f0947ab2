<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementOrderStockOutSkuCell" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementOrderStockOutSkuCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="82"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="82"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="jzD-ka-lEW">
                        <rect key="frame" x="16" y="6" width="70" height="70"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="70" id="6Ff-zt-a37"/>
                            <constraint firstAttribute="height" constant="70" id="nqV-uo-XRQ"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="4Le-Nl-Et8">
                        <rect key="frame" x="16" y="6" width="70" height="70"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IR5-Vc-yW1">
                        <rect key="frame" x="98" y="12" width="222" height="20.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OCf-Ww-0bc">
                        <rect key="frame" x="98" y="56" width="42" height="17"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="17" id="UdH-HC-mac"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OxF-ca-iRc">
                        <rect key="frame" x="21" y="11" width="60" height="60"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7AE-Ap-8nT">
                                <rect key="frame" x="5" y="20" width="50" height="20.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="50" id="fqR-JM-JF6"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="7AE-Ap-8nT" firstAttribute="centerY" secondItem="OxF-ca-iRc" secondAttribute="centerY" id="7Bg-BT-PSr"/>
                            <constraint firstAttribute="height" constant="60" id="9pE-2x-nyH"/>
                            <constraint firstItem="7AE-Ap-8nT" firstAttribute="centerX" secondItem="OxF-ca-iRc" secondAttribute="centerX" id="vuK-Bn-4cw"/>
                            <constraint firstAttribute="width" constant="60" id="xTq-Jt-pzd"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="4Le-Nl-Et8" firstAttribute="leading" secondItem="jzD-ka-lEW" secondAttribute="leading" id="DfC-6c-Nxs"/>
                    <constraint firstItem="OxF-ca-iRc" firstAttribute="centerY" secondItem="jzD-ka-lEW" secondAttribute="centerY" id="Ldb-hV-7Re"/>
                    <constraint firstItem="4Le-Nl-Et8" firstAttribute="bottom" secondItem="jzD-ka-lEW" secondAttribute="bottom" id="N7r-5s-Bbj"/>
                    <constraint firstItem="IR5-Vc-yW1" firstAttribute="top" secondItem="jzD-ka-lEW" secondAttribute="top" constant="6" id="Ot0-0f-TTW"/>
                    <constraint firstAttribute="trailing" secondItem="IR5-Vc-yW1" secondAttribute="trailing" id="S6u-sO-M06"/>
                    <constraint firstItem="OxF-ca-iRc" firstAttribute="centerX" secondItem="jzD-ka-lEW" secondAttribute="centerX" id="eBc-uS-KxC"/>
                    <constraint firstItem="4Le-Nl-Et8" firstAttribute="trailing" secondItem="jzD-ka-lEW" secondAttribute="trailing" id="iVR-sH-lYX"/>
                    <constraint firstItem="4Le-Nl-Et8" firstAttribute="top" secondItem="jzD-ka-lEW" secondAttribute="top" id="k50-Ki-N53"/>
                    <constraint firstItem="jzD-ka-lEW" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="ovT-hR-F2X"/>
                    <constraint firstItem="jzD-ka-lEW" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="16" id="pQd-6k-z78"/>
                    <constraint firstItem="OCf-Ww-0bc" firstAttribute="bottom" secondItem="jzD-ka-lEW" secondAttribute="bottom" constant="-3" id="t6c-tq-cSB"/>
                    <constraint firstItem="IR5-Vc-yW1" firstAttribute="leading" secondItem="jzD-ka-lEW" secondAttribute="trailing" constant="12" id="wA2-Lb-SSt"/>
                    <constraint firstItem="OCf-Ww-0bc" firstAttribute="leading" secondItem="jzD-ka-lEW" secondAttribute="trailing" constant="12" id="x5K-Sb-MSv"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="numLabel" destination="OCf-Ww-0bc" id="ObK-rW-ljy"/>
                <outlet property="skuImageMaskView" destination="4Le-Nl-Et8" id="Lcz-Ii-KIO"/>
                <outlet property="skuImageView" destination="jzD-ka-lEW" id="GU0-PA-nHi"/>
                <outlet property="skuLabel" destination="IR5-Vc-yW1" id="ngY-eD-GBa"/>
                <outlet property="stockoutLabel" destination="7AE-Ap-8nT" id="Zpw-OW-Hik"/>
                <outlet property="stockoutMaskView" destination="OxF-ca-iRc" id="BYg-Pd-VF2"/>
            </connections>
            <point key="canvasLocation" x="-54" y="68"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
