<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Settlement Product Sheet Controller-->
        <scene sceneID="Qsu-yl-I2n">
            <objects>
                <viewController storyboardIdentifier="JDISVSettlementProductSheetController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="eEe-Yc-TtR" customClass="JDISVSettlementProductSheetController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="apu-eA-PTk">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="dTk-yI-rJh">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nNB-wW-7TR">
                                <rect key="frame" x="0.0" y="396" width="414" height="500"/>
                                <subviews>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="44" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="VXU-xR-FGR">
                                        <rect key="frame" x="0.0" y="55" width="414" height="445"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementProductSheetItemCell" rowHeight="125" id="Lkm-vo-e1g" customClass="JDISVSettlementProductSheetItemCell">
                                                <rect key="frame" x="0.0" y="50" width="414" height="125"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Lkm-vo-e1g" id="Ilt-Vf-Ei4">
                                                    <rect key="frame" x="0.0" y="0.0" width="414" height="125"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="4DL-wb-5oS">
                                                            <rect key="frame" x="18" y="12" width="90" height="90"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="90" id="fxW-OQ-Yky"/>
                                                                <constraint firstAttribute="width" constant="90" id="mnk-LM-TVs"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="美孚一号 原装进口 0W-40 润滑油 全合成机油 SN 级 1L " textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xy9-LD-CAz">
                                                            <rect key="frame" x="120" y="12" width="276" height="33.5"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="￥200" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0NF-N6-sko">
                                                            <rect key="frame" x="120" y="63.5" width="48" height="20.5"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="X1" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="onT-eF-hYw">
                                                            <rect key="frame" x="382" y="67" width="14" height="14"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="门店" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="n8R-F6-smC">
                                                            <rect key="frame" x="120" y="87.5" width="25" height="14.5"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="25" id="zkh-B1-Twt"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="12"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="北京捷亚泰中实汽车销售有限公司" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e6x-yg-L7n">
                                                            <rect key="frame" x="149" y="87.5" width="184" height="14.5"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="csf-tq-nGQ">
                                                            <rect key="frame" x="108" y="124.5" width="306" height="0.5"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="0.5" id="nIw-5n-9Fl"/>
                                                            </constraints>
                                                        </imageView>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstItem="4DL-wb-5oS" firstAttribute="leading" secondItem="Ilt-Vf-Ei4" secondAttribute="leading" constant="18" id="AA2-Ku-Kox"/>
                                                        <constraint firstItem="onT-eF-hYw" firstAttribute="centerY" secondItem="0NF-N6-sko" secondAttribute="centerY" id="ELc-b3-DaP"/>
                                                        <constraint firstItem="e6x-yg-L7n" firstAttribute="leading" secondItem="n8R-F6-smC" secondAttribute="trailing" constant="4" id="EgL-IO-Dlt"/>
                                                        <constraint firstItem="n8R-F6-smC" firstAttribute="leading" secondItem="xy9-LD-CAz" secondAttribute="leading" id="FoK-ow-4x8"/>
                                                        <constraint firstItem="xy9-LD-CAz" firstAttribute="top" secondItem="Ilt-Vf-Ei4" secondAttribute="top" constant="12" id="Tnp-04-tBa"/>
                                                        <constraint firstItem="4DL-wb-5oS" firstAttribute="top" secondItem="Ilt-Vf-Ei4" secondAttribute="top" constant="12" id="UYl-BT-Vqt"/>
                                                        <constraint firstItem="0NF-N6-sko" firstAttribute="top" secondItem="xy9-LD-CAz" secondAttribute="bottom" constant="18" id="V8K-fl-oST"/>
                                                        <constraint firstItem="e6x-yg-L7n" firstAttribute="centerY" secondItem="n8R-F6-smC" secondAttribute="centerY" id="WiN-PO-HXJ"/>
                                                        <constraint firstItem="csf-tq-nGQ" firstAttribute="leading" secondItem="Ilt-Vf-Ei4" secondAttribute="leading" constant="108" id="YII-Jn-4MO"/>
                                                        <constraint firstItem="xy9-LD-CAz" firstAttribute="leading" secondItem="4DL-wb-5oS" secondAttribute="trailing" constant="12" id="ZPN-90-C5o"/>
                                                        <constraint firstItem="n8R-F6-smC" firstAttribute="top" secondItem="0NF-N6-sko" secondAttribute="bottom" constant="3.5" id="e6i-jZ-l8W"/>
                                                        <constraint firstAttribute="trailing" secondItem="csf-tq-nGQ" secondAttribute="trailing" id="eMm-d3-4GX"/>
                                                        <constraint firstAttribute="trailing" secondItem="xy9-LD-CAz" secondAttribute="trailing" constant="18" id="h6q-4l-Dds"/>
                                                        <constraint firstAttribute="bottom" secondItem="csf-tq-nGQ" secondAttribute="bottom" id="i3s-pI-EmU"/>
                                                        <constraint firstItem="0NF-N6-sko" firstAttribute="leading" secondItem="xy9-LD-CAz" secondAttribute="leading" id="qtm-VP-J3Z"/>
                                                        <constraint firstAttribute="trailingMargin" relation="greaterThanOrEqual" secondItem="e6x-yg-L7n" secondAttribute="trailing" constant="18" id="vnR-oQ-WRd"/>
                                                        <constraint firstItem="onT-eF-hYw" firstAttribute="trailing" secondItem="xy9-LD-CAz" secondAttribute="trailing" id="xeg-2s-ga1"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <connections>
                                                    <outlet property="separatorLine" destination="csf-tq-nGQ" id="7ab-lv-aeK"/>
                                                    <outlet property="skuCountLabel" destination="onT-eF-hYw" id="1aS-Q8-Yn7"/>
                                                    <outlet property="skuImageView" destination="4DL-wb-5oS" id="ijR-Z2-8Iz"/>
                                                    <outlet property="skuPriceLabel" destination="0NF-N6-sko" id="NR5-fh-Wi8"/>
                                                    <outlet property="skuShopLabel" destination="e6x-yg-L7n" id="rg6-A2-E0f"/>
                                                    <outlet property="skuShopTitleLabel" destination="n8R-F6-smC" id="7CC-65-xhq"/>
                                                    <outlet property="skuTitleLabel" destination="xy9-LD-CAz" id="mAz-Zn-abg"/>
                                                </connections>
                                            </tableViewCell>
                                        </prototypes>
                                    </tableView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="商品清单" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xrk-yp-HQM">
                                        <rect key="frame" x="170.5" y="18.5" width="73.5" height="21"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="18"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g5m-s8-8yt">
                                        <rect key="frame" x="378" y="18" width="18" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="18" id="RIq-Jh-oaT"/>
                                            <constraint firstAttribute="width" constant="18" id="yGr-rU-xII"/>
                                        </constraints>
                                        <connections>
                                            <action selector="closeAction:" destination="eEe-Yc-TtR" eventType="touchUpInside" id="hwa-k1-6Mp"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="共5件" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hQf-il-KMX">
                                        <rect key="frame" x="332" y="22" width="32" height="14"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="14" id="r7A-Du-pEd"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Xrk-yp-HQM" firstAttribute="top" secondItem="nNB-wW-7TR" secondAttribute="top" constant="18.5" id="6nJ-ls-WAj"/>
                                    <constraint firstItem="VXU-xR-FGR" firstAttribute="top" secondItem="nNB-wW-7TR" secondAttribute="top" constant="55" id="Kn2-KT-QSj"/>
                                    <constraint firstItem="Xrk-yp-HQM" firstAttribute="centerX" secondItem="nNB-wW-7TR" secondAttribute="centerX" id="Qyo-Zf-mSh"/>
                                    <constraint firstAttribute="height" constant="500" id="Wrb-QT-ylM"/>
                                    <constraint firstItem="g5m-s8-8yt" firstAttribute="top" secondItem="nNB-wW-7TR" secondAttribute="top" constant="18" id="ZOf-db-ORu"/>
                                    <constraint firstItem="hQf-il-KMX" firstAttribute="centerY" secondItem="Xrk-yp-HQM" secondAttribute="centerY" id="dtt-Ar-3XN"/>
                                    <constraint firstAttribute="trailing" secondItem="hQf-il-KMX" secondAttribute="trailing" constant="50" id="eRv-fu-nAp"/>
                                    <constraint firstAttribute="trailing" secondItem="g5m-s8-8yt" secondAttribute="trailing" constant="18" id="mE0-QN-FwT"/>
                                    <constraint firstAttribute="bottom" secondItem="VXU-xR-FGR" secondAttribute="bottom" id="oYA-N0-nId"/>
                                    <constraint firstAttribute="trailing" secondItem="VXU-xR-FGR" secondAttribute="trailing" id="qbL-td-Zw0"/>
                                    <constraint firstItem="VXU-xR-FGR" firstAttribute="leading" secondItem="nNB-wW-7TR" secondAttribute="leading" id="uIV-Aj-Vzt"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hdO-bl-Tb3"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="dTk-yI-rJh" secondAttribute="bottom" id="70R-mA-3rh"/>
                            <constraint firstItem="dTk-yI-rJh" firstAttribute="trailing" secondItem="hdO-bl-Tb3" secondAttribute="trailing" id="Drj-vp-6lJ"/>
                            <constraint firstItem="nNB-wW-7TR" firstAttribute="trailing" secondItem="hdO-bl-Tb3" secondAttribute="trailing" id="FAx-Ts-dlt"/>
                            <constraint firstItem="nNB-wW-7TR" firstAttribute="leading" secondItem="hdO-bl-Tb3" secondAttribute="leading" id="GQF-6l-CUP"/>
                            <constraint firstItem="dTk-yI-rJh" firstAttribute="top" secondItem="apu-eA-PTk" secondAttribute="top" id="YoG-Q0-66f"/>
                            <constraint firstAttribute="bottom" secondItem="nNB-wW-7TR" secondAttribute="bottom" id="Zcj-9b-wQU"/>
                            <constraint firstItem="dTk-yI-rJh" firstAttribute="leading" secondItem="hdO-bl-Tb3" secondAttribute="leading" id="psm-Xt-4cQ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="closeButton" destination="g5m-s8-8yt" id="09j-uA-YKu"/>
                        <outlet property="contentView" destination="nNB-wW-7TR" id="fd4-h6-1ff"/>
                        <outlet property="contentViewBottom" destination="Zcj-9b-wQU" id="VeV-dk-WfN"/>
                        <outlet property="contentViewHeight" destination="Wrb-QT-ylM" id="OiE-f7-dub"/>
                        <outlet property="maskView" destination="dTk-yI-rJh" id="Tcy-Zs-kqi"/>
                        <outlet property="tableView" destination="VXU-xR-FGR" id="gZZ-v9-S9e"/>
                        <outlet property="titleLabel" destination="Xrk-yp-HQM" id="Ner-AP-DVR"/>
                        <outlet property="totalCountLabel" destination="hQf-il-KMX" id="EGR-LA-lUX"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ng9-iH-WW3" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-494.20289855072468" y="792.85714285714278"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
