<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="JDISVSettlementCouponController">
            <connections>
                <outlet property="bottomView" destination="eXz-G3-03T" id="CKt-H0-Uw9"/>
                <outlet property="categoryView" destination="W0D-KJ-UvJ" id="Z3V-k8-i9b"/>
                <outlet property="closeButton" destination="5fv-Uw-f5l" id="9kr-QD-sVE"/>
                <outlet property="confirmButton" destination="OCE-dW-aOE" id="jbz-F4-qsR"/>
                <outlet property="contentScrollView" destination="xf7-nf-k5i" id="ewl-Te-gL6"/>
                <outlet property="controllerTitleLabel" destination="p9m-g1-W6a" id="399-6X-mdk"/>
                <outlet property="couponTopTitleView" destination="OPe-63-j8O" id="Z6e-z5-dkm"/>
                <outlet property="indicator" destination="CMR-jT-PV5" id="Kun-kb-dLJ"/>
                <outlet property="indicatorView" destination="EeO-d5-nLL" id="Dso-vd-5p9"/>
                <outlet property="titleCollectionView" destination="DLk-F7-5fG" id="jh4-ar-Ddz"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OPe-63-j8O">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="55"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="优惠" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="p9m-g1-W6a">
                            <rect key="frame" x="189.5" y="18.5" width="35" height="18"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="18" id="ama-i7-sK0"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5fv-Uw-f5l">
                            <rect key="frame" x="354" y="0.0" width="60" height="55"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="60" id="xoE-08-Ihp"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="55" id="6MR-V9-cAm"/>
                        <constraint firstItem="5fv-Uw-f5l" firstAttribute="top" secondItem="OPe-63-j8O" secondAttribute="top" id="8FV-Db-VeX"/>
                        <constraint firstAttribute="trailing" secondItem="5fv-Uw-f5l" secondAttribute="trailing" id="8Re-CO-RjG"/>
                        <constraint firstAttribute="bottom" secondItem="5fv-Uw-f5l" secondAttribute="bottom" id="AML-c9-hAz"/>
                        <constraint firstItem="p9m-g1-W6a" firstAttribute="centerX" secondItem="OPe-63-j8O" secondAttribute="centerX" id="fnY-z4-0J3"/>
                        <constraint firstItem="p9m-g1-W6a" firstAttribute="centerY" secondItem="OPe-63-j8O" secondAttribute="centerY" id="gUt-Hg-KeS"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W0D-KJ-UvJ">
                    <rect key="frame" x="0.0" y="55" width="414" height="44"/>
                    <subviews>
                        <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="DLk-F7-5fG">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="ICM-vV-ibz">
                                <size key="itemSize" width="128" height="128"/>
                                <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                            </collectionViewFlowLayout>
                        </collectionView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EeO-d5-nLL">
                            <rect key="frame" x="0.0" y="34" width="414" height="3"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="CMR-jT-PV5">
                                    <rect key="frame" x="18" y="0.0" width="18" height="3"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="18" id="pa7-ET-gGM"/>
                                    </constraints>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstItem="CMR-jT-PV5" firstAttribute="leading" secondItem="EeO-d5-nLL" secondAttribute="leading" constant="18" id="9Hd-mH-Jmv"/>
                                <constraint firstAttribute="bottom" secondItem="CMR-jT-PV5" secondAttribute="bottom" id="TK2-a6-dEh"/>
                                <constraint firstItem="CMR-jT-PV5" firstAttribute="top" secondItem="EeO-d5-nLL" secondAttribute="top" id="Xeq-Hw-R3d"/>
                                <constraint firstAttribute="height" constant="3" id="wFw-yv-MLj"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="EeO-d5-nLL" secondAttribute="trailing" id="24M-Qf-Bst"/>
                        <constraint firstAttribute="trailing" secondItem="DLk-F7-5fG" secondAttribute="trailing" id="2xP-gU-20C"/>
                        <constraint firstItem="EeO-d5-nLL" firstAttribute="leading" secondItem="W0D-KJ-UvJ" secondAttribute="leading" id="52T-dI-ftP"/>
                        <constraint firstItem="EeO-d5-nLL" firstAttribute="top" secondItem="W0D-KJ-UvJ" secondAttribute="top" constant="34" id="8f3-PP-m5P"/>
                        <constraint firstAttribute="height" constant="44" id="LAo-OT-i6b"/>
                        <constraint firstItem="DLk-F7-5fG" firstAttribute="top" secondItem="W0D-KJ-UvJ" secondAttribute="top" id="LQQ-0O-NSW"/>
                        <constraint firstItem="DLk-F7-5fG" firstAttribute="leading" secondItem="W0D-KJ-UvJ" secondAttribute="leading" id="tja-ba-bUM"/>
                        <constraint firstAttribute="bottom" secondItem="DLk-F7-5fG" secondAttribute="bottom" id="zYW-Lm-tDa"/>
                    </constraints>
                </view>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" directionalLockEnabled="YES" scrollEnabled="NO" pagingEnabled="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xf7-nf-k5i">
                    <rect key="frame" x="0.0" y="99" width="414" height="714"/>
                </scrollView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eXz-G3-03T">
                    <rect key="frame" x="0.0" y="812" width="414" height="50"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OCE-dW-aOE">
                            <rect key="frame" x="18" y="5" width="378" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="QIG-On-63L"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="OCE-dW-aOE" firstAttribute="leading" secondItem="eXz-G3-03T" secondAttribute="leading" constant="18" id="C8H-uj-dtq"/>
                        <constraint firstAttribute="height" constant="50" id="Drg-2f-Z6u"/>
                        <constraint firstItem="OCE-dW-aOE" firstAttribute="centerY" secondItem="eXz-G3-03T" secondAttribute="centerY" id="Odm-nX-QSY"/>
                        <constraint firstAttribute="trailing" secondItem="OCE-dW-aOE" secondAttribute="trailing" constant="18" id="oHB-gl-UQY"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="DPD-VR-bi9"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="OPe-63-j8O" firstAttribute="leading" secondItem="DPD-VR-bi9" secondAttribute="leading" id="2cF-w2-DhD"/>
                <constraint firstItem="DPD-VR-bi9" firstAttribute="trailing" secondItem="eXz-G3-03T" secondAttribute="trailing" id="3T8-aS-qQe"/>
                <constraint firstItem="DPD-VR-bi9" firstAttribute="trailing" secondItem="W0D-KJ-UvJ" secondAttribute="trailing" id="53k-sp-bqk"/>
                <constraint firstItem="DPD-VR-bi9" firstAttribute="bottom" secondItem="eXz-G3-03T" secondAttribute="bottom" id="7wL-oD-uQ2"/>
                <constraint firstItem="xf7-nf-k5i" firstAttribute="top" secondItem="W0D-KJ-UvJ" secondAttribute="bottom" id="KU4-uQ-lzn"/>
                <constraint firstItem="W0D-KJ-UvJ" firstAttribute="leading" secondItem="DPD-VR-bi9" secondAttribute="leading" id="W1Z-l6-eKZ"/>
                <constraint firstItem="eXz-G3-03T" firstAttribute="top" secondItem="xf7-nf-k5i" secondAttribute="bottom" constant="-1" id="XBy-KA-KHg"/>
                <constraint firstItem="DPD-VR-bi9" firstAttribute="trailing" secondItem="OPe-63-j8O" secondAttribute="trailing" id="ZmA-Jk-1RA"/>
                <constraint firstItem="W0D-KJ-UvJ" firstAttribute="top" secondItem="OPe-63-j8O" secondAttribute="bottom" id="b68-tW-T76"/>
                <constraint firstItem="DPD-VR-bi9" firstAttribute="trailing" secondItem="xf7-nf-k5i" secondAttribute="trailing" id="h7m-a0-qka"/>
                <constraint firstItem="eXz-G3-03T" firstAttribute="leading" secondItem="DPD-VR-bi9" secondAttribute="leading" id="iIe-8x-xvS"/>
                <constraint firstItem="xf7-nf-k5i" firstAttribute="leading" secondItem="DPD-VR-bi9" secondAttribute="leading" id="mI3-X5-fFz"/>
                <constraint firstItem="OPe-63-j8O" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="obZ-at-tB1"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="-54" y="48"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
