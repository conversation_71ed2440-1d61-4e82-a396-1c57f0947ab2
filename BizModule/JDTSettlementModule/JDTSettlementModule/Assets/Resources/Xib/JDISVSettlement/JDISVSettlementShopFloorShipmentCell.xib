<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShopFloorShipmentCell" rowHeight="88" id="KGk-i7-Jjw" customClass="JDISVSettlementShopFloorShipmentCell">
            <rect key="frame" x="0.0" y="0.0" width="448" height="174"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="448" height="174"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="22I-Px-Jxa">
                        <rect key="frame" x="18" y="12" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="B8e-Sw-PHj"/>
                            <constraint firstAttribute="width" constant="42" id="bER-Td-ok4"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gNW-vn-gee">
                        <rect key="frame" x="64" y="13" width="80" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="80" id="E0Y-Vf-MLk"/>
                            <constraint firstAttribute="height" constant="18" id="P1G-Py-HqR"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wK3-qH-Rj3">
                        <rect key="frame" x="144" y="12" width="292" height="44"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Ryw-jl-DyT">
                                <rect key="frame" x="268" y="0.0" width="24" height="20"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="M3r-GL-vhl"/>
                                    <constraint firstAttribute="width" constant="24" id="VHO-M0-c3K"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eFe-P3-KsA">
                                <rect key="frame" x="0.0" y="24" width="268" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="YeJ-KM-kQN"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="agv-UI-kIR">
                                <rect key="frame" x="0.0" y="0.0" width="268" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="RIQ-72-qCT"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <constraints>
                            <constraint firstItem="agv-UI-kIR" firstAttribute="leading" secondItem="wK3-qH-Rj3" secondAttribute="leading" id="91S-np-j8m"/>
                            <constraint firstAttribute="bottom" secondItem="eFe-P3-KsA" secondAttribute="bottom" id="9ua-Ea-1Yy"/>
                            <constraint firstItem="eFe-P3-KsA" firstAttribute="trailing" secondItem="agv-UI-kIR" secondAttribute="trailing" id="CAl-g6-GRz"/>
                            <constraint firstItem="eFe-P3-KsA" firstAttribute="leading" secondItem="wK3-qH-Rj3" secondAttribute="leading" id="HCT-Rl-eRQ"/>
                            <constraint firstItem="Ryw-jl-DyT" firstAttribute="leading" secondItem="agv-UI-kIR" secondAttribute="trailing" id="Hg2-YY-hKe"/>
                            <constraint firstAttribute="trailing" secondItem="Ryw-jl-DyT" secondAttribute="trailing" id="Ten-5I-tNQ"/>
                            <constraint firstItem="Ryw-jl-DyT" firstAttribute="top" secondItem="wK3-qH-Rj3" secondAttribute="top" id="W2H-Zk-WYC"/>
                            <constraint firstItem="eFe-P3-KsA" firstAttribute="top" secondItem="agv-UI-kIR" secondAttribute="bottom" constant="4" id="dQf-Kb-q93"/>
                            <constraint firstItem="agv-UI-kIR" firstAttribute="top" secondItem="wK3-qH-Rj3" secondAttribute="top" id="lek-i0-aGP"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="we4-TC-TS4">
                        <rect key="frame" x="0.0" y="40" width="448" height="109.5"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pick up Address" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P89-Sv-Fkh">
                                <rect key="frame" x="18" y="0.0" width="123" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="R22-Hq-ZYE"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Q4d-nb-aYI">
                                <rect key="frame" x="418" y="4" width="12" height="12"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="12" id="RHO-y2-FVa"/>
                                    <constraint firstAttribute="height" constant="12" id="Vaw-hh-t8M"/>
                                </constraints>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Eea-bB-OPr">
                                <rect key="frame" x="18" y="24" width="412" height="85.5"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hcO-6A-bfl">
                                        <rect key="frame" x="8" y="8.5" width="20" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="0l8-dT-uqR"/>
                                            <constraint firstAttribute="width" constant="20" id="95t-kH-7ag"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="hfdkjhfkjd浩丰科技电" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="f1Q-8r-5ty">
                                        <rect key="frame" x="32" y="8" width="372" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" horizontalCompressionResistancePriority="749" text="汇顶科技水电费开发" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QW7-Jl-xKd">
                                        <rect key="frame" x="32" y="28.5" width="372" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="hfkjdhfjkdh" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yqR-lB-BTS">
                                        <rect key="frame" x="32" y="57" width="372" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="23b-ec-lHD">
                                        <rect key="frame" x="8" y="57.5" width="20" height="20"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="f1Q-8r-5ty" firstAttribute="top" secondItem="Eea-bB-OPr" secondAttribute="top" constant="8" id="3pE-wS-pSJ"/>
                                    <constraint firstItem="hcO-6A-bfl" firstAttribute="leading" secondItem="Eea-bB-OPr" secondAttribute="leading" constant="8" id="7nl-6E-71o"/>
                                    <constraint firstItem="23b-ec-lHD" firstAttribute="centerX" secondItem="hcO-6A-bfl" secondAttribute="centerX" id="DJw-Kl-jLD"/>
                                    <constraint firstItem="yqR-lB-BTS" firstAttribute="trailing" secondItem="f1Q-8r-5ty" secondAttribute="trailing" id="IAV-4E-Mhs"/>
                                    <constraint firstItem="23b-ec-lHD" firstAttribute="width" secondItem="hcO-6A-bfl" secondAttribute="width" id="LP1-72-CUT"/>
                                    <constraint firstItem="yqR-lB-BTS" firstAttribute="top" secondItem="QW7-Jl-xKd" secondAttribute="bottom" constant="8" id="Rrg-fI-R5a"/>
                                    <constraint firstItem="hcO-6A-bfl" firstAttribute="centerY" secondItem="f1Q-8r-5ty" secondAttribute="centerY" id="SAV-Sq-DHv"/>
                                    <constraint firstItem="yqR-lB-BTS" firstAttribute="centerY" secondItem="23b-ec-lHD" secondAttribute="centerY" id="Wqg-zG-Xb8"/>
                                    <constraint firstAttribute="bottom" secondItem="yqR-lB-BTS" secondAttribute="bottom" constant="8" id="YWS-BF-rdh"/>
                                    <constraint firstItem="f1Q-8r-5ty" firstAttribute="leading" secondItem="hcO-6A-bfl" secondAttribute="trailing" constant="4" id="YdJ-VM-Gil"/>
                                    <constraint firstItem="QW7-Jl-xKd" firstAttribute="trailing" secondItem="f1Q-8r-5ty" secondAttribute="trailing" id="ezN-ue-0Lq"/>
                                    <constraint firstAttribute="trailing" secondItem="f1Q-8r-5ty" secondAttribute="trailing" constant="8" id="gDE-9a-IqD"/>
                                    <constraint firstItem="yqR-lB-BTS" firstAttribute="leading" secondItem="f1Q-8r-5ty" secondAttribute="leading" id="ged-eO-Wwv"/>
                                    <constraint firstItem="23b-ec-lHD" firstAttribute="height" secondItem="hcO-6A-bfl" secondAttribute="height" id="p84-rh-eAp"/>
                                    <constraint firstItem="QW7-Jl-xKd" firstAttribute="top" secondItem="f1Q-8r-5ty" secondAttribute="bottom" id="wK6-ci-uzA"/>
                                    <constraint firstItem="QW7-Jl-xKd" firstAttribute="leading" secondItem="f1Q-8r-5ty" secondAttribute="leading" id="xcD-YD-xuV"/>
                                </constraints>
                            </view>
                        </subviews>
                        <constraints>
                            <constraint firstItem="Eea-bB-OPr" firstAttribute="top" secondItem="P89-Sv-Fkh" secondAttribute="bottom" constant="4" id="Aze-6s-Rdd"/>
                            <constraint firstItem="P89-Sv-Fkh" firstAttribute="leading" secondItem="we4-TC-TS4" secondAttribute="leading" constant="18" id="NwI-3Y-Lp0"/>
                            <constraint firstAttribute="trailing" secondItem="Q4d-nb-aYI" secondAttribute="trailing" constant="18" id="UXa-I0-zgn"/>
                            <constraint firstItem="Eea-bB-OPr" firstAttribute="leading" secondItem="we4-TC-TS4" secondAttribute="leading" constant="18" id="ep1-1k-y9R"/>
                            <constraint firstItem="P89-Sv-Fkh" firstAttribute="top" secondItem="we4-TC-TS4" secondAttribute="top" id="hOI-2m-ef2"/>
                            <constraint firstAttribute="bottom" secondItem="Eea-bB-OPr" secondAttribute="bottom" id="jah-UB-ilO"/>
                            <constraint firstAttribute="trailing" secondItem="Eea-bB-OPr" secondAttribute="trailing" constant="18" id="kLM-c4-rnY"/>
                            <constraint firstItem="Q4d-nb-aYI" firstAttribute="centerY" secondItem="P89-Sv-Fkh" secondAttribute="centerY" id="xF4-Of-GAV"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ESO-N5-EBC">
                        <rect key="frame" x="18" y="173.5" width="412" height="0.5"/>
                        <color key="backgroundColor" systemColor="labelColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="0.5" id="icf-az-ah3"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="22I-Px-Jxa" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="20M-ry-QOQ"/>
                    <constraint firstItem="wK3-qH-Rj3" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="144" id="6VK-ek-Cdt"/>
                    <constraint firstItem="gNW-vn-gee" firstAttribute="centerY" secondItem="22I-Px-Jxa" secondAttribute="centerY" id="6cR-y3-8mf"/>
                    <constraint firstAttribute="bottom" secondItem="ESO-N5-EBC" secondAttribute="bottom" id="8xA-mt-fGy"/>
                    <constraint firstItem="22I-Px-Jxa" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="ACk-fx-7S4"/>
                    <constraint firstAttribute="trailing" secondItem="we4-TC-TS4" secondAttribute="trailing" id="FGo-1S-AlC"/>
                    <constraint firstItem="we4-TC-TS4" firstAttribute="top" secondItem="22I-Px-Jxa" secondAttribute="bottom" constant="8" id="Jpr-s3-J7N"/>
                    <constraint firstItem="we4-TC-TS4" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="OND-9M-1nD"/>
                    <constraint firstAttribute="trailing" secondItem="wK3-qH-Rj3" secondAttribute="trailing" constant="12" id="Qnh-8u-2VG"/>
                    <constraint firstItem="wK3-qH-Rj3" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="TMp-yW-CtS"/>
                    <constraint firstItem="ESO-N5-EBC" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="TbQ-bg-XcT"/>
                    <constraint firstItem="gNW-vn-gee" firstAttribute="leading" secondItem="22I-Px-Jxa" secondAttribute="trailing" constant="4" id="WTr-ky-zrV"/>
                    <constraint firstAttribute="trailing" secondItem="ESO-N5-EBC" secondAttribute="trailing" constant="18" id="wK6-aB-Zhh"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="KSAInfoBg" destination="wK3-qH-Rj3" id="LIB-Gh-mul"/>
                <outlet property="KSAInfoBgLeading" destination="6VK-ek-Cdt" id="tYB-XI-joC"/>
                <outlet property="KSAInfoBgTop" destination="TMp-yW-CtS" id="dNI-RR-d8m"/>
                <outlet property="LOCStoreContentView" destination="we4-TC-TS4" id="Ziy-xf-95L"/>
                <outlet property="bottomLine" destination="ESO-N5-EBC" id="Sqv-9W-cOk"/>
                <outlet property="businessTimeIcon" destination="hcO-6A-bfl" id="MVh-jU-dvB"/>
                <outlet property="businessTimeLabel" destination="f1Q-8r-5ty" id="qXR-7m-Fls"/>
                <outlet property="businessTipLabel" destination="QW7-Jl-xKd" id="GKk-PX-sPX"/>
                <outlet property="picUpAddressLabel" destination="P89-Sv-Fkh" id="5XO-bd-CgH"/>
                <outlet property="pickUpAddressContentView" destination="Eea-bB-OPr" id="hmW-Qs-m7M"/>
                <outlet property="pickUpaddressArrow" destination="Q4d-nb-aYI" id="t6U-oe-CZa"/>
                <outlet property="promiseLabel" destination="eFe-P3-KsA" id="Xg2-KJ-glV"/>
                <outlet property="promiseLabelHeight" destination="YeJ-KM-kQN" id="i2B-KK-6dd"/>
                <outlet property="rightArrowImageView" destination="Ryw-jl-DyT" id="uyw-hk-1ji"/>
                <outlet property="shipmentFloorTitleLabel" destination="22I-Px-Jxa" id="WAv-Zv-22O"/>
                <outlet property="shipmentFloorTitleLabelWidth" destination="bER-Td-ok4" id="eza-SO-OaA"/>
                <outlet property="shipmentTypeLabel" destination="agv-UI-kIR" id="Mzi-LF-ADi"/>
                <outlet property="storeAddresIcon" destination="23b-ec-lHD" id="H2y-M4-TOB"/>
                <outlet property="storeAddresLabel" destination="yqR-lB-BTS" id="6xe-RW-GQr"/>
                <outlet property="storePickTag" destination="gNW-vn-gee" id="8cu-fT-fnz"/>
                <outlet property="storePickTagWidth" destination="E0Y-Vf-MLk" id="mDz-nd-oKi"/>
            </connections>
            <point key="canvasLocation" x="-175.36231884057972" y="153.34821428571428"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
