<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="DWG-5F-KJi" customClass="JDISVSettlementOnlinePayFloor">
            <rect key="frame" x="0.0" y="0.0" width="414" height="56"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="在线支付" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ujY-gI-gLc">
                    <rect key="frame" x="18" y="18" width="57.5" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="mLf-12-fu1"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="agy-CO-yjp">
                    <rect key="frame" x="81.5" y="23" width="10" height="10"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="10" id="Ee8-QN-4tu"/>
                        <constraint firstAttribute="height" constant="10" id="mcD-vJ-MDI"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="LVc-Fk-61b"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="ujY-gI-gLc" firstAttribute="leading" secondItem="DWG-5F-KJi" secondAttribute="leading" constant="18" id="0yU-RO-dm1"/>
                <constraint firstItem="agy-CO-yjp" firstAttribute="leading" secondItem="ujY-gI-gLc" secondAttribute="trailing" constant="6" id="6r9-kB-O1x"/>
                <constraint firstItem="agy-CO-yjp" firstAttribute="centerY" secondItem="DWG-5F-KJi" secondAttribute="centerY" id="V2t-bK-kQR"/>
                <constraint firstItem="ujY-gI-gLc" firstAttribute="centerY" secondItem="DWG-5F-KJi" secondAttribute="centerY" id="q8m-Ab-iLW"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="infoBtn" destination="agy-CO-yjp" id="hKD-3t-YVv"/>
                <outlet property="payTitleLabel" destination="ujY-gI-gLc" id="MHM-OS-q5a"/>
            </connections>
            <point key="canvasLocation" x="548" y="-19"/>
        </view>
    </objects>
</document>
