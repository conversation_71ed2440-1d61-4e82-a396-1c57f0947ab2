<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JDISVSettlementBottomFloor">
            <rect key="frame" x="0.0" y="0.0" width="414" height="50"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="v5c-RI-2R6">
                    <rect key="frame" x="18" y="21" width="42" height="14"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="14" id="CWl-IO-yYq"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HUh-fr-zmL">
                    <rect key="frame" x="65" y="13" width="42" height="24"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24" id="AxH-WZ-3Iv"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="AE4-HX-EaT">
                    <rect key="frame" x="286" y="5" width="110" height="40"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="40" id="Hbs-nz-DNJ"/>
                        <constraint firstAttribute="width" constant="110" id="vvs-uU-5aq"/>
                    </constraints>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="v5c-RI-2R6" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="18" id="3VE-b5-c9V"/>
                <constraint firstItem="HUh-fr-zmL" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="13" id="Hey-a5-Zpv"/>
                <constraint firstItem="AE4-HX-EaT" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="HUh-fr-zmL" secondAttribute="trailing" constant="12" id="Ofw-zQ-GB5"/>
                <constraint firstItem="HUh-fr-zmL" firstAttribute="leading" secondItem="v5c-RI-2R6" secondAttribute="trailing" constant="5" id="WyC-Ac-KEm"/>
                <constraint firstItem="v5c-RI-2R6" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="21" id="YFw-Wn-kfK"/>
                <constraint firstAttribute="trailing" secondItem="AE4-HX-EaT" secondAttribute="trailing" constant="18" id="c7b-Wx-dXG"/>
                <constraint firstItem="AE4-HX-EaT" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="5" id="ueo-7Y-y9H"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="amountLabel" destination="HUh-fr-zmL" id="ZRh-71-Z48"/>
                <outlet property="amountTitleLabel" destination="v5c-RI-2R6" id="0pH-jx-DmG"/>
                <outlet property="buttonWidth" destination="vvs-uU-5aq" id="fNh-SG-nqC"/>
                <outlet property="submitButton" destination="AE4-HX-EaT" id="vMS-IL-4yS"/>
            </connections>
            <point key="canvasLocation" x="-54" y="139"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
