<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="44" id="KGk-i7-Jjw" customClass="JDISVSettlementAddressFloor">
            <rect key="frame" x="0.0" y="0.0" width="320" height="151"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="151"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sK1-6j-ZXs">
                        <rect key="frame" x="18" y="18" width="41.5" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="hBf-wT-pZI"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LSF-PE-Vpc">
                        <rect key="frame" x="18" y="44" width="41.5" height="25"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="25" id="5v5-Z9-5Bp"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="AsU-E0-9qt">
                        <rect key="frame" x="290" y="44" width="25" height="25"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="25" id="cPM-Nh-t2Z"/>
                            <constraint firstAttribute="width" constant="25" id="yw9-zg-M06"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nWH-Gg-jy6">
                        <rect key="frame" x="18" y="75" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="6YF-DX-FPg"/>
                            <constraint firstAttribute="width" constant="42" id="kxT-je-A7e"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kut-uU-E7H">
                        <rect key="frame" x="66" y="77" width="41.5" height="16.5"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="16.5" id="UwH-Io-gKL"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KZW-oz-RuO">
                        <rect key="frame" x="18" y="101" width="284" height="29"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hsn-Cj-h76">
                                <rect key="frame" x="12" y="4" width="260" height="21"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="21" id="wbZ-aF-YT5"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Hsn-Cj-h76" firstAttribute="leading" secondItem="KZW-oz-RuO" secondAttribute="leading" constant="12" id="1cV-cT-Udc"/>
                            <constraint firstAttribute="trailing" secondItem="Hsn-Cj-h76" secondAttribute="trailing" constant="12" id="5DS-6o-yVn"/>
                            <constraint firstItem="Hsn-Cj-h76" firstAttribute="centerY" secondItem="KZW-oz-RuO" secondAttribute="centerY" id="a45-4W-OAX"/>
                            <constraint firstAttribute="height" constant="29" id="erI-FO-4tU"/>
                        </constraints>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="3qA-Dx-Sgl">
                        <rect key="frame" x="0.0" y="148" width="320" height="3"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="3" id="Ktz-qQ-LgS"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="sK1-6j-ZXs" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="4H9-Ni-Zrk"/>
                    <constraint firstItem="nWH-Gg-jy6" firstAttribute="top" secondItem="LSF-PE-Vpc" secondAttribute="bottom" constant="6" id="5BG-mU-k9E"/>
                    <constraint firstItem="sK1-6j-ZXs" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="18" id="K3M-6m-nzM"/>
                    <constraint firstAttribute="trailing" secondItem="AsU-E0-9qt" secondAttribute="trailing" constant="5" id="LUu-jM-3Hd"/>
                    <constraint firstAttribute="bottom" secondItem="3qA-Dx-Sgl" secondAttribute="bottom" id="M9a-mf-yTQ"/>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Kut-uU-E7H" secondAttribute="trailing" constant="18" id="PPJ-py-r9W"/>
                    <constraint firstItem="KZW-oz-RuO" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="TSc-Rc-lab"/>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="sK1-6j-ZXs" secondAttribute="trailing" constant="30" id="U5j-yq-JNi"/>
                    <constraint firstItem="LSF-PE-Vpc" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="VGp-yC-mOf"/>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="LSF-PE-Vpc" secondAttribute="trailing" constant="36" id="VlD-Ek-dc0"/>
                    <constraint firstItem="Kut-uU-E7H" firstAttribute="centerY" secondItem="nWH-Gg-jy6" secondAttribute="centerY" id="am1-MG-Cco"/>
                    <constraint firstItem="3qA-Dx-Sgl" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="bvL-W6-Zrk"/>
                    <constraint firstItem="nWH-Gg-jy6" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="eR6-pc-Rsv"/>
                    <constraint firstItem="LSF-PE-Vpc" firstAttribute="top" secondItem="sK1-6j-ZXs" secondAttribute="bottom" constant="6" id="qXY-2u-BBw"/>
                    <constraint firstItem="AsU-E0-9qt" firstAttribute="centerY" secondItem="LSF-PE-Vpc" secondAttribute="centerY" id="rwK-1E-3tm"/>
                    <constraint firstItem="Kut-uU-E7H" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="66" id="tmU-Vn-t1a"/>
                    <constraint firstItem="KZW-oz-RuO" firstAttribute="top" secondItem="nWH-Gg-jy6" secondAttribute="bottom" constant="6" id="uEX-yz-Nfk"/>
                    <constraint firstAttribute="trailing" secondItem="KZW-oz-RuO" secondAttribute="trailing" constant="18" id="xVx-6b-5G7"/>
                    <constraint firstAttribute="trailing" secondItem="3qA-Dx-Sgl" secondAttribute="trailing" id="z6h-K3-GWO"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="areaAddressLabel" destination="sK1-6j-ZXs" id="JhI-s1-n2N"/>
                <outlet property="areaAddressLabelHeight" destination="hBf-wT-pZI" id="sjc-Bf-54Z"/>
                <outlet property="detailAddressLabel" destination="LSF-PE-Vpc" id="YiJ-Qw-BAK"/>
                <outlet property="detailAddressLabelHeight" destination="5v5-Z9-5Bp" id="inX-hd-99l"/>
                <outlet property="mobileLabel" destination="Kut-uU-E7H" id="BtA-1c-uBr"/>
                <outlet property="mobileLabelLeading" destination="tmU-Vn-t1a" id="OE6-Hc-xLi"/>
                <outlet property="nameLabel" destination="nWH-Gg-jy6" id="MzV-ty-wpN"/>
                <outlet property="nameLabelWidth" destination="kxT-je-A7e" id="d5t-mG-E5M"/>
                <outlet property="rightArrow" destination="AsU-E0-9qt" id="YYc-Tp-xod"/>
                <outlet property="separatorLine" destination="3qA-Dx-Sgl" id="3tH-iV-D02"/>
                <outlet property="tipBgLabel" destination="Hsn-Cj-h76" id="R3C-bo-Lxh"/>
                <outlet property="tipBgLabelHeight" destination="wbZ-aF-YT5" id="HUh-y5-eyW"/>
                <outlet property="tipBgView" destination="KZW-oz-RuO" id="ERu-4h-3HA"/>
                <outlet property="tipBgViewHeight" destination="erI-FO-4tU" id="7mF-kT-AtD"/>
            </connections>
            <point key="canvasLocation" x="57" y="139"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
