<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="JDISVSettlementShipmentChangeStoreMapCard">
            <rect key="frame" x="0.0" y="0.0" width="414" height="118"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aiy-iy-l5O">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="75.5"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lvv-Mz-wwx">
                            <rect key="frame" x="12" y="10" width="42" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="2J4-TE-S70"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ytv-PR-Xo3">
                            <rect key="frame" x="12" y="31.5" width="41.5" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="aHn-Sd-61X"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l7g-cW-ly1">
                            <rect key="frame" x="12" y="48" width="41.5" height="21"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="21" id="zyC-RB-da3"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="l7g-cW-ly1" firstAttribute="leading" secondItem="aiy-iy-l5O" secondAttribute="leading" constant="12" id="2UG-d9-0oc"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="lvv-Mz-wwx" secondAttribute="trailing" constant="12" id="8Oq-Wd-Lvv"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="l7g-cW-ly1" secondAttribute="trailing" constant="12" id="Eet-Py-WLt"/>
                        <constraint firstItem="lvv-Mz-wwx" firstAttribute="top" secondItem="aiy-iy-l5O" secondAttribute="top" constant="10" id="QEw-o1-bB7"/>
                        <constraint firstItem="Ytv-PR-Xo3" firstAttribute="leading" secondItem="aiy-iy-l5O" secondAttribute="leading" constant="12" id="Zsf-e5-mnb"/>
                        <constraint firstItem="lvv-Mz-wwx" firstAttribute="leading" secondItem="aiy-iy-l5O" secondAttribute="leading" constant="12" id="hea-hc-c9z"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Ytv-PR-Xo3" secondAttribute="trailing" constant="12" id="imY-Q0-xBk"/>
                        <constraint firstAttribute="height" constant="75.5" id="uMC-EG-MgZ"/>
                        <constraint firstItem="l7g-cW-ly1" firstAttribute="top" secondItem="Ytv-PR-Xo3" secondAttribute="bottom" constant="2.5" id="ykg-4O-e1M"/>
                        <constraint firstItem="Ytv-PR-Xo3" firstAttribute="top" secondItem="lvv-Mz-wwx" secondAttribute="bottom" constant="4.5" id="ymY-1e-oSc"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MWb-aJ-2EO">
                    <rect key="frame" x="0.0" y="75.5" width="414" height="42.5"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="brf-1i-4se">
                            <rect key="frame" x="12" y="4.5" width="28" height="18"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="18" id="8wj-bh-kQ0"/>
                                <constraint firstAttribute="width" constant="28" id="onQ-6X-UJ4"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Gqe-nL-NLX">
                            <rect key="frame" x="44" y="6" width="42" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="jXC-k0-MIB"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YkP-CD-efV">
                            <rect key="frame" x="354" y="3" width="42" height="20.5"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20.5" id="9xa-fK-A2B"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6Dy-fv-YYM">
                            <rect key="frame" x="387" y="5.5" width="14" height="14"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="14" id="yFK-F8-eT3"/>
                                <constraint firstAttribute="height" constant="14" id="zwx-vv-vy9"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="Gqe-nL-NLX" firstAttribute="top" secondItem="MWb-aJ-2EO" secondAttribute="top" constant="6" id="8vq-Rr-fWD"/>
                        <constraint firstItem="6Dy-fv-YYM" firstAttribute="top" secondItem="MWb-aJ-2EO" secondAttribute="top" constant="5.5" id="CwC-1n-x1L"/>
                        <constraint firstAttribute="trailing" secondItem="6Dy-fv-YYM" secondAttribute="trailing" constant="13" id="M7c-yQ-Evr"/>
                        <constraint firstItem="Gqe-nL-NLX" firstAttribute="leading" secondItem="MWb-aJ-2EO" secondAttribute="leading" constant="44" id="WqT-ve-ntn"/>
                        <constraint firstItem="YkP-CD-efV" firstAttribute="centerY" secondItem="Gqe-nL-NLX" secondAttribute="centerY" id="Yq2-dp-Vcj"/>
                        <constraint firstItem="brf-1i-4se" firstAttribute="top" secondItem="MWb-aJ-2EO" secondAttribute="top" constant="4.5" id="ar2-xx-r8Z"/>
                        <constraint firstAttribute="trailing" secondItem="YkP-CD-efV" secondAttribute="trailing" constant="18" id="k6u-h3-wPk"/>
                        <constraint firstItem="brf-1i-4se" firstAttribute="leading" secondItem="MWb-aJ-2EO" secondAttribute="leading" constant="12" id="utZ-kS-O8t"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="MWb-aJ-2EO" secondAttribute="bottom" id="3TH-NX-1qP"/>
                <constraint firstAttribute="trailing" secondItem="aiy-iy-l5O" secondAttribute="trailing" id="JWE-uh-Ft7"/>
                <constraint firstItem="aiy-iy-l5O" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="Zl9-9x-V94"/>
                <constraint firstItem="aiy-iy-l5O" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="d5N-dk-WaW"/>
                <constraint firstItem="MWb-aJ-2EO" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="ec0-a7-tbh"/>
                <constraint firstItem="MWb-aJ-2EO" firstAttribute="top" secondItem="aiy-iy-l5O" secondAttribute="bottom" id="i1c-1y-Fn0"/>
                <constraint firstAttribute="trailing" secondItem="MWb-aJ-2EO" secondAttribute="trailing" id="obI-W1-JhE"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="addressLabel" destination="l7g-cW-ly1" id="JYS-76-wT5"/>
                <outlet property="addressLabelHeight" destination="zyC-RB-da3" id="M8U-pj-H2H"/>
                <outlet property="bottomView" destination="MWb-aJ-2EO" id="hN4-SC-bVU"/>
                <outlet property="bussinessTimeLabel" destination="Ytv-PR-Xo3" id="r2l-f1-qfn"/>
                <outlet property="checkButton" destination="6Dy-fv-YYM" id="QW9-a3-oS7"/>
                <outlet property="distanceLabel" destination="Gqe-nL-NLX" id="e5e-1d-2sj"/>
                <outlet property="distanceLabelLeading" destination="WqT-ve-ntn" id="oHq-KK-9L5"/>
                <outlet property="recentTagImgView" destination="brf-1i-4se" id="WB7-eG-3uJ"/>
                <outlet property="stockoutLabel" destination="YkP-CD-efV" id="Rjf-fG-Gtv"/>
                <outlet property="storeNameLabel" destination="lvv-Mz-wwx" id="2ad-RB-mEe"/>
                <outlet property="topView" destination="aiy-iy-l5O" id="G4q-bS-aQM"/>
                <outlet property="topViewHeight" destination="uMC-EG-MgZ" id="aS7-zg-TbQ"/>
            </connections>
            <point key="canvasLocation" x="-54" y="58"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
