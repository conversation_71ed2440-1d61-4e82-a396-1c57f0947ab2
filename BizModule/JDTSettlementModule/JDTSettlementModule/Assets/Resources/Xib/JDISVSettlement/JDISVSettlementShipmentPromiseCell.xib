<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShipmentPromiseCell" rowHeight="42" id="KGk-i7-Jjw" customClass="JDISVSettlementShipmentPromiseCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="42"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="42"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="paz-BY-QCa">
                        <rect key="frame" x="18" y="12" width="284" height="18"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="Tso-hr-aBh"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="paz-BY-QCa" secondAttribute="trailing" constant="18" id="2Fh-Bv-G5u"/>
                    <constraint firstItem="paz-BY-QCa" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="Q5d-Qi-Y2h"/>
                    <constraint firstItem="paz-BY-QCa" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="Wya-VZ-CUt"/>
                    <constraint firstItem="paz-BY-QCa" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="wR1-vY-bAn"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="promiseLabel" destination="paz-BY-QCa" id="vDp-xh-pt3"/>
                <outlet property="promiseLabelHeight" destination="Tso-hr-aBh" id="tGF-Lb-tNK"/>
            </connections>
            <point key="canvasLocation" x="-54" y="139"/>
        </tableViewCell>
    </objects>
</document>
