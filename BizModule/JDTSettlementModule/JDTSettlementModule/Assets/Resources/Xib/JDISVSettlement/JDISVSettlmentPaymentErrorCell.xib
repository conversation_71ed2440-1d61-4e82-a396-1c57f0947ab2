<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="85" id="KGk-i7-Jjw" customClass="JDISVSettlmentPaymentErrorCell">
            <rect key="frame" x="0.0" y="0.0" width="365" height="85"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" ambiguous="YES" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="365" height="85"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="kK5-tF-3Na">
                        <rect key="frame" x="12" y="0.0" width="70" height="70"/>
                        <color key="backgroundColor" systemColor="systemGray5Color"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="70" id="r4b-9U-hQX"/>
                            <constraint firstAttribute="width" constant="70" id="rYu-Ub-Cn3"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="LANCOME Pure Whitening Essence Cream Light Moist Repair Water Cream..." textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uCO-ji-Kbr">
                        <rect key="frame" x="94" y="6" width="259" height="40.666666666666664"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Qty 1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vj3-l7-92F">
                        <rect key="frame" x="94" y="41" width="39.333333333333343" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="kK5-tF-3Na" secondAttribute="bottom" constant="12" id="7qh-Ok-yWX"/>
                    <constraint firstItem="kK5-tF-3Na" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="12" id="Ghp-AX-a1M"/>
                    <constraint firstAttribute="trailing" secondItem="uCO-ji-Kbr" secondAttribute="trailing" constant="12" id="Vs1-9h-AxY"/>
                    <constraint firstItem="kK5-tF-3Na" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="b9y-kw-FCz"/>
                    <constraint firstItem="uCO-ji-Kbr" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="jqD-bH-Dni"/>
                    <constraint firstItem="Vj3-l7-92F" firstAttribute="leading" secondItem="kK5-tF-3Na" secondAttribute="trailing" constant="12" id="pCF-oh-CuO"/>
                    <constraint firstItem="uCO-ji-Kbr" firstAttribute="leading" secondItem="kK5-tF-3Na" secondAttribute="trailing" constant="12" id="tmS-tm-DA2"/>
                    <constraint firstAttribute="bottom" secondItem="Vj3-l7-92F" secondAttribute="bottom" constant="15" id="ypK-q5-5t1"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="productImg" destination="kK5-tF-3Na" id="5fq-6c-Zot"/>
                <outlet property="productname" destination="uCO-ji-Kbr" id="iue-o0-db4"/>
                <outlet property="qtyLabel" destination="Vj3-l7-92F" id="HzB-68-gap"/>
            </connections>
            <point key="canvasLocation" x="173.2824427480916" y="34.154929577464792"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemGray5Color">
            <color red="0.89803921568627454" green="0.89803921568627454" blue="0.91764705882352937" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
