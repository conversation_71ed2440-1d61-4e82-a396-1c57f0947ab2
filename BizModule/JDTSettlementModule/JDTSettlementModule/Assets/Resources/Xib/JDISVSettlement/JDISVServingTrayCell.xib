<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="82" id="KGk-i7-Jjw" customClass="JDISVServingTrayCell">
            <rect key="frame" x="0.0" y="0.0" width="467" height="82"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="467" height="82"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="0ve-Su-PB5">
                        <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                        <color key="backgroundColor" systemColor="systemGrayColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="70" id="GO7-bK-xyH"/>
                            <constraint firstAttribute="width" constant="70" id="fh1-eY-JFV"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="settlement_sold_out" translatesAutoresizingMaskIntoConstraints="NO" id="mDv-rd-M9N">
                        <rect key="frame" x="5" y="5" width="60" height="60"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="60" id="Rje-1F-8cD"/>
                            <constraint firstAttribute="height" constant="60" id="dXf-Nb-knv"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="&lt;MASLayoutConstraint:0x600001d37780 UITableView:0x7f962d80e000.height " textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZA1-cr-kPR">
                        <rect key="frame" x="82" y="6" width="385" height="40.666666666666664"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hSk-EH-IPh">
                        <rect key="frame" x="82" y="46.666666666666664" width="41.333333333333343" height="20.999999999999993"/>
                        <constraints>
                            <constraint firstAttribute="width" relation="lessThanOrEqual" constant="50" id="2V4-92-VQO"/>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" id="pEH-J6-waF"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s0O-iW-LJS">
                        <rect key="frame" x="129.33333333333334" y="46.666666666666664" width="41.333333333333343" height="20.333333333333336"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="s0O-iW-LJS" secondAttribute="trailing" id="07H-aT-sb7"/>
                    <constraint firstItem="mDv-rd-M9N" firstAttribute="centerY" secondItem="0ve-Su-PB5" secondAttribute="centerY" id="6h1-CV-8VS"/>
                    <constraint firstItem="0ve-Su-PB5" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="81a-ZZ-0JU"/>
                    <constraint firstItem="hSk-EH-IPh" firstAttribute="leading" secondItem="0ve-Su-PB5" secondAttribute="trailing" constant="12" id="AGE-Qx-nMc"/>
                    <constraint firstItem="ZA1-cr-kPR" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="J5Q-5J-hQE"/>
                    <constraint firstAttribute="trailing" secondItem="ZA1-cr-kPR" secondAttribute="trailing" id="Lzs-fc-Vfg"/>
                    <constraint firstAttribute="bottom" secondItem="s0O-iW-LJS" secondAttribute="bottom" constant="15" id="Mz8-Gz-coq"/>
                    <constraint firstItem="s0O-iW-LJS" firstAttribute="leading" secondItem="hSk-EH-IPh" secondAttribute="trailing" constant="6" id="Ttp-Om-CIZ"/>
                    <constraint firstItem="0ve-Su-PB5" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="jn5-4N-9k5"/>
                    <constraint firstItem="s0O-iW-LJS" firstAttribute="top" secondItem="hSk-EH-IPh" secondAttribute="top" id="po4-oO-Qa6"/>
                    <constraint firstItem="mDv-rd-M9N" firstAttribute="centerX" secondItem="0ve-Su-PB5" secondAttribute="centerX" id="pts-wx-7uk"/>
                    <constraint firstItem="ZA1-cr-kPR" firstAttribute="leading" secondItem="0ve-Su-PB5" secondAttribute="trailing" constant="12" id="qEe-d1-5xq"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="numberLabel" destination="hSk-EH-IPh" id="zRJ-yr-Au6"/>
                <outlet property="productImg" destination="0ve-Su-PB5" id="gE1-8F-fB7"/>
                <outlet property="skuName" destination="ZA1-cr-kPR" id="Yon-Do-nKJ"/>
                <outlet property="soldOutImg" destination="mDv-rd-M9N" id="PWS-1w-53y"/>
                <outlet property="tipLabel" destination="s0O-iW-LJS" id="2bz-DW-q3R"/>
            </connections>
            <point key="canvasLocation" x="167.17557251908397" y="32.394366197183103"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="settlement_sold_out" width="120" height="120"/>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
