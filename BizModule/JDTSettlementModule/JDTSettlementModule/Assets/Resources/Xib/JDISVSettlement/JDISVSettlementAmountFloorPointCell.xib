<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementAmountFloorPointCell" rowHeight="126" id="KGk-i7-Jjw" customClass="JDISVSettlementAmountFloorPointCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="60"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="积分" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="urF-BJ-FRd">
                        <rect key="frame" x="18" y="9" width="35" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="35" id="OXZ-6l-L1i"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="1vy-39-gN5">
                        <rect key="frame" x="286" y="22" width="20" height="16"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="Im8-PD-0u5"/>
                            <constraint firstAttribute="height" constant="16" id="ikd-2p-0QI"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="033-iq-yfV">
                        <rect key="frame" x="244" y="20" width="42" height="20.5"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="42" id="G0b-NS-cfP"/>
                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="b3a-ir-Epm"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3rL-j8-AbD">
                        <rect key="frame" x="18" y="29" width="42" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="42" id="mwI-pn-IuP"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="1vy-39-gN5" firstAttribute="leading" secondItem="033-iq-yfV" secondAttribute="trailing" id="19Q-S2-evp"/>
                    <constraint firstItem="033-iq-yfV" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Ih2-9b-syP"/>
                    <constraint firstItem="3rL-j8-AbD" firstAttribute="top" secondItem="urF-BJ-FRd" secondAttribute="bottom" id="LxT-Mx-cf1"/>
                    <constraint firstItem="urF-BJ-FRd" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="Sxy-jE-JJq"/>
                    <constraint firstAttribute="trailing" secondItem="1vy-39-gN5" secondAttribute="trailing" constant="14" id="Vib-do-eZh"/>
                    <constraint firstItem="urF-BJ-FRd" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="9" id="ebW-rP-MbE"/>
                    <constraint firstItem="3rL-j8-AbD" firstAttribute="leading" secondItem="urF-BJ-FRd" secondAttribute="leading" id="icp-8R-tHt"/>
                    <constraint firstItem="1vy-39-gN5" firstAttribute="centerY" secondItem="033-iq-yfV" secondAttribute="centerY" id="zPb-5K-BYR"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="infoImageHeight" destination="ikd-2p-0QI" id="xkp-D9-ZTy"/>
                <outlet property="infoImageTrailing" destination="Vib-do-eZh" id="0kw-26-orZ"/>
                <outlet property="infoImageView" destination="1vy-39-gN5" id="cEH-Ua-4tT"/>
                <outlet property="infoImageWidth" destination="Im8-PD-0u5" id="zcV-y8-hGe"/>
                <outlet property="pointCountLabel" destination="3rL-j8-AbD" id="RF5-2P-qRR"/>
                <outlet property="pointCountLabelWidth" destination="mwI-pn-IuP" id="wTY-n7-X42"/>
                <outlet property="pointInfoLabel" destination="033-iq-yfV" id="Zkn-P0-uAR"/>
                <outlet property="pointInfoLabelWidth" destination="G0b-NS-cfP" id="cC4-MW-7Qu"/>
                <outlet property="pointTitleLabel" destination="urF-BJ-FRd" id="loC-VK-3ON"/>
                <outlet property="pointTitleLabelWidth" destination="OXZ-6l-L1i" id="S6Y-Fp-mgL"/>
            </connections>
            <point key="canvasLocation" x="18.840579710144929" y="170.08928571428569"/>
        </tableViewCell>
    </objects>
</document>
