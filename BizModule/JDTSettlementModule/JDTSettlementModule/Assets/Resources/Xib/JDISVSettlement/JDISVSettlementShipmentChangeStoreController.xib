<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="JDISVSettlementShipmentChangeStoreController">
            <connections>
                <outlet property="addressLabel" destination="ezc-ZD-Jue" id="Fbq-zq-4DW"/>
                <outlet property="contentTitleLabel" destination="Obd-W9-yo1" id="qND-N7-suf"/>
                <outlet property="contentTitleView" destination="2bS-H5-HIO" id="Plh-u7-qTG"/>
                <outlet property="contentView" destination="Y5g-xr-cWp" id="igf-Lp-whL"/>
                <outlet property="contentViewBottom" destination="t70-yA-uFU" id="BQO-tM-LkI"/>
                <outlet property="contentViewHeight" destination="hPI-kd-lZZ" id="53o-iS-PuB"/>
                <outlet property="mapLogo" destination="jgG-fx-dbu" id="Rds-ol-O95"/>
                <outlet property="rightArrowView" destination="YY1-gb-uvC" id="KWw-9U-b3J"/>
                <outlet property="tableView" destination="V8M-2v-kQA" id="x4q-9j-9Zl"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="jgG-fx-dbu">
                    <rect key="frame" x="10" y="430" width="85" height="24"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="85" id="Ui0-EN-bra"/>
                        <constraint firstAttribute="height" constant="24" id="zvl-ha-rOx"/>
                    </constraints>
                </imageView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y5g-xr-cWp" customClass="JDISVSettlementShipmentChangeStoreContentView">
                    <rect key="frame" x="0.0" y="466" width="414" height="430"/>
                    <subviews>
                        <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="V8M-2v-kQA">
                            <rect key="frame" x="0.0" y="55" width="414" height="375"/>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        </tableView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2bS-H5-HIO">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="55"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Obd-W9-yo1">
                                    <rect key="frame" x="18" y="15" width="41.5" height="25"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="25" id="MRO-CK-P5W"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ezc-ZD-Jue">
                                    <rect key="frame" x="338" y="17" width="42" height="21"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="YY1-gb-uvC">
                                    <rect key="frame" x="380" y="0.0" width="20" height="55"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="20" id="ZFZ-QE-ksq"/>
                                    </constraints>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="YY1-gb-uvC" secondAttribute="trailing" constant="14" id="1uW-5Q-6xi"/>
                                <constraint firstItem="Obd-W9-yo1" firstAttribute="leading" secondItem="2bS-H5-HIO" secondAttribute="leading" constant="18" id="9K5-OI-Znf"/>
                                <constraint firstItem="Obd-W9-yo1" firstAttribute="centerY" secondItem="ezc-ZD-Jue" secondAttribute="centerY" id="Hj6-aW-gCX"/>
                                <constraint firstItem="YY1-gb-uvC" firstAttribute="top" secondItem="2bS-H5-HIO" secondAttribute="top" id="IXe-hQ-phd"/>
                                <constraint firstAttribute="height" constant="55" id="UaO-35-Dcd"/>
                                <constraint firstItem="YY1-gb-uvC" firstAttribute="leading" secondItem="ezc-ZD-Jue" secondAttribute="trailing" id="VJX-7e-rub"/>
                                <constraint firstItem="ezc-ZD-Jue" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="Obd-W9-yo1" secondAttribute="trailing" constant="18" id="f4H-eK-DW7"/>
                                <constraint firstAttribute="bottom" secondItem="YY1-gb-uvC" secondAttribute="bottom" id="l0N-kk-IWT"/>
                                <constraint firstItem="ezc-ZD-Jue" firstAttribute="centerY" secondItem="YY1-gb-uvC" secondAttribute="centerY" id="rU9-TF-I6f"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="V8M-2v-kQA" secondAttribute="trailing" id="8g6-vM-S1g"/>
                        <constraint firstItem="V8M-2v-kQA" firstAttribute="leading" secondItem="Y5g-xr-cWp" secondAttribute="leading" id="EVy-Ki-N1R"/>
                        <constraint firstItem="V8M-2v-kQA" firstAttribute="leading" secondItem="Y5g-xr-cWp" secondAttribute="leading" id="dnp-vB-6Un"/>
                        <constraint firstItem="2bS-H5-HIO" firstAttribute="leading" secondItem="Y5g-xr-cWp" secondAttribute="leading" id="eAR-Ax-qx1"/>
                        <constraint firstAttribute="height" constant="430" id="hPI-kd-lZZ"/>
                        <constraint firstAttribute="trailing" secondItem="2bS-H5-HIO" secondAttribute="trailing" id="jHP-of-Skv"/>
                        <constraint firstItem="V8M-2v-kQA" firstAttribute="top" secondItem="2bS-H5-HIO" secondAttribute="bottom" id="jQd-hb-1EE"/>
                        <constraint firstAttribute="trailing" secondItem="V8M-2v-kQA" secondAttribute="trailing" id="qO5-8j-Dcm"/>
                        <constraint firstItem="2bS-H5-HIO" firstAttribute="top" secondItem="Y5g-xr-cWp" secondAttribute="top" id="tER-8F-594"/>
                        <constraint firstAttribute="bottom" secondItem="V8M-2v-kQA" secondAttribute="bottom" id="uFq-Pg-7nC"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="Y5g-xr-cWp" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="0SN-gl-ULQ"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="Y5g-xr-cWp" secondAttribute="trailing" id="6FY-Z9-NxL"/>
                <constraint firstItem="jgG-fx-dbu" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" constant="10" id="dnV-dw-7Dt"/>
                <constraint firstItem="2bS-H5-HIO" firstAttribute="top" secondItem="jgG-fx-dbu" secondAttribute="bottom" constant="12" id="f3o-QW-rEG"/>
                <constraint firstAttribute="bottom" secondItem="Y5g-xr-cWp" secondAttribute="bottom" id="t70-yA-uFU"/>
            </constraints>
            <point key="canvasLocation" x="15.942028985507248" y="55.580357142857139"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
