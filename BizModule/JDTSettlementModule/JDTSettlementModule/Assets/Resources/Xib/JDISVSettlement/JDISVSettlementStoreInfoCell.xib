<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementStoreInfoCell" rowHeight="149" id="KGk-i7-Jjw" customClass="JDISVSettlementStoreInfoCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="149"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="149"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rpm-7d-JFB">
                        <rect key="frame" x="12" y="0.0" width="296" height="149"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="4J8-Ek-Q13">
                                <rect key="frame" x="12" y="20" width="200" height="109"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="0.0" id="rCJ-5V-42y">
                                    <size key="itemSize" width="70" height="70"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                            </collectionView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="unM-mP-Wyz">
                                <rect key="frame" x="212" y="39.5" width="84" height="70"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="OtJ-y6-Uwg">
                                        <rect key="frame" x="60" y="25" width="24" height="20"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="22z-KE-Iqd"/>
                                            <constraint firstAttribute="width" constant="24" id="rww-vj-vVe"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="共5件" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NAT-zM-2BB">
                                        <rect key="frame" x="22" y="27" width="32" height="16"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="16" id="TFM-0y-TBH"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="OtJ-y6-Uwg" firstAttribute="centerY" secondItem="unM-mP-Wyz" secondAttribute="centerY" id="Pme-de-UaA"/>
                                    <constraint firstAttribute="trailing" secondItem="OtJ-y6-Uwg" secondAttribute="trailing" id="Pxv-em-VGH"/>
                                    <constraint firstItem="OtJ-y6-Uwg" firstAttribute="leading" secondItem="NAT-zM-2BB" secondAttribute="trailing" constant="6" id="TAY-B3-UIF"/>
                                    <constraint firstAttribute="height" constant="70" id="Y7h-T8-j7e"/>
                                    <constraint firstItem="NAT-zM-2BB" firstAttribute="centerY" secondItem="unM-mP-Wyz" secondAttribute="centerY" id="mDr-cT-eVP"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="unM-mP-Wyz" firstAttribute="centerY" secondItem="4J8-Ek-Q13" secondAttribute="centerY" id="8wr-Sa-fk6"/>
                            <constraint firstAttribute="trailing" secondItem="unM-mP-Wyz" secondAttribute="trailing" id="M4x-0Y-uBp"/>
                            <constraint firstItem="4J8-Ek-Q13" firstAttribute="leading" secondItem="rpm-7d-JFB" secondAttribute="leading" constant="12" id="bF1-vO-dgS"/>
                            <constraint firstItem="4J8-Ek-Q13" firstAttribute="top" secondItem="rpm-7d-JFB" secondAttribute="top" constant="20" symbolic="YES" id="bQG-ZY-G2Q"/>
                            <constraint firstAttribute="trailing" secondItem="4J8-Ek-Q13" secondAttribute="trailing" constant="84" id="lgA-j6-WK9"/>
                            <constraint firstItem="unM-mP-Wyz" firstAttribute="leading" secondItem="4J8-Ek-Q13" secondAttribute="trailing" id="qMF-XV-fX7"/>
                            <constraint firstAttribute="bottom" secondItem="4J8-Ek-Q13" secondAttribute="bottom" constant="20" symbolic="YES" id="xio-cD-oac"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="rpm-7d-JFB" secondAttribute="bottom" id="7jh-4f-C8C"/>
                    <constraint firstAttribute="trailing" secondItem="rpm-7d-JFB" secondAttribute="trailing" constant="12" id="T6B-2I-SRw"/>
                    <constraint firstItem="rpm-7d-JFB" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="12" id="Vya-B1-8Ys"/>
                    <constraint firstItem="rpm-7d-JFB" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="ZSH-OV-JiJ"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="collectionView" destination="4J8-Ek-Q13" id="2jG-rG-Tor"/>
                <outlet property="collectionViewTrailing" destination="lgA-j6-WK9" id="6Hz-LS-TEn"/>
                <outlet property="rightArrowImageView" destination="OtJ-y6-Uwg" id="KGf-cj-YYX"/>
                <outlet property="skuCountView" destination="unM-mP-Wyz" id="Mtx-kK-RbW"/>
                <outlet property="storeInfoContentView" destination="rpm-7d-JFB" id="GgK-Hu-Wlf"/>
                <outlet property="totalCountLabel" destination="NAT-zM-2BB" id="dnF-Jh-lH7"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="153.01339285714286"/>
        </tableViewCell>
    </objects>
</document>
