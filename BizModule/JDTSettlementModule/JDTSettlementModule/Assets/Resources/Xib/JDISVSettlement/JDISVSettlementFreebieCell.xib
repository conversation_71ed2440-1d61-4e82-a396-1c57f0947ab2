<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementFreebieCell" rowHeight="305" id="KGk-i7-Jjw" customClass="JDISVSettlementFreebieCell">
            <rect key="frame" x="0.0" y="0.0" width="389" height="206"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="389" height="206"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="749" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gsO-TS-oZj">
                        <rect key="frame" x="158" y="12" width="213" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="cbo-qo-ras">
                        <rect key="frame" x="36" y="12" width="72" height="72"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="72" id="Lsx-pd-VH4"/>
                            <constraint firstAttribute="width" constant="72" id="owN-Kc-s5r"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sfw-Ba-VNb">
                        <rect key="frame" x="120" y="12" width="34" height="18"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="x68-TL-PiB">
                                <rect key="frame" x="3.5" y="0.0" width="27" height="18"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="18" id="0A9-Ot-aT1"/>
                            <constraint firstAttribute="trailing" secondItem="x68-TL-PiB" secondAttribute="trailing" constant="3.5" id="7V1-we-0C6"/>
                            <constraint firstItem="x68-TL-PiB" firstAttribute="leading" secondItem="sfw-Ba-VNb" secondAttribute="leading" constant="3.5" id="SBa-CJ-Vf5"/>
                            <constraint firstAttribute="bottom" secondItem="x68-TL-PiB" secondAttribute="bottom" id="jBx-8o-hcg"/>
                            <constraint firstItem="x68-TL-PiB" firstAttribute="top" secondItem="sfw-Ba-VNb" secondAttribute="top" id="qdy-hE-9JA"/>
                            <constraint firstAttribute="width" constant="34" id="y5I-ft-meh"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="x4" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2ND-vp-snK">
                        <rect key="frame" x="357" y="67.5" width="14" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8sO-KO-5k1">
                        <rect key="frame" x="44" y="20" width="56" height="56"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="k2z-XO-aQr">
                                <rect key="frame" x="0.0" y="0.0" width="56" height="56"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="k2z-XO-aQr" firstAttribute="leading" secondItem="8sO-KO-5k1" secondAttribute="leading" id="C0e-JI-Teo"/>
                            <constraint firstAttribute="bottom" secondItem="k2z-XO-aQr" secondAttribute="bottom" id="DfS-vf-IBA"/>
                            <constraint firstAttribute="width" constant="56" id="ZEM-BW-UbY"/>
                            <constraint firstAttribute="height" constant="56" id="agF-Wi-h2b"/>
                            <constraint firstItem="k2z-XO-aQr" firstAttribute="top" secondItem="8sO-KO-5k1" secondAttribute="top" id="dPL-mZ-gAz"/>
                            <constraint firstAttribute="trailing" secondItem="k2z-XO-aQr" secondAttribute="trailing" id="vtQ-91-jxc"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="cbo-qo-ras" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="36" id="3cc-Kx-7b8"/>
                    <constraint firstItem="sfw-Ba-VNb" firstAttribute="leading" secondItem="cbo-qo-ras" secondAttribute="trailing" constant="12" id="6xW-tE-V18"/>
                    <constraint firstItem="gsO-TS-oZj" firstAttribute="top" secondItem="cbo-qo-ras" secondAttribute="top" id="786-mq-vg1"/>
                    <constraint firstAttribute="trailing" secondItem="gsO-TS-oZj" secondAttribute="trailing" constant="18" id="APl-rE-jeI"/>
                    <constraint firstItem="sfw-Ba-VNb" firstAttribute="top" secondItem="cbo-qo-ras" secondAttribute="top" id="B7t-1g-SVi"/>
                    <constraint firstItem="2ND-vp-snK" firstAttribute="bottom" secondItem="cbo-qo-ras" secondAttribute="bottom" constant="-2" id="CQM-cx-1LM"/>
                    <constraint firstItem="8sO-KO-5k1" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="20" id="HkZ-vd-Rgz"/>
                    <constraint firstItem="2ND-vp-snK" firstAttribute="trailing" secondItem="gsO-TS-oZj" secondAttribute="trailing" id="cWX-di-eAW"/>
                    <constraint firstItem="gsO-TS-oZj" firstAttribute="leading" secondItem="sfw-Ba-VNb" secondAttribute="trailing" constant="4" id="nzc-eb-PBF"/>
                    <constraint firstItem="8sO-KO-5k1" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="44" id="vhh-WM-ISx"/>
                    <constraint firstItem="cbo-qo-ras" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="yg8-ma-K3I"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="freeCount" destination="2ND-vp-snK" id="OnU-ji-KDV"/>
                <outlet property="freeProductName" destination="gsO-TS-oZj" id="gI6-Zi-PmJ"/>
                <outlet property="freeTitle" destination="x68-TL-PiB" id="yh7-gu-b45"/>
                <outlet property="proImg" destination="cbo-qo-ras" id="q5S-Cu-m1e"/>
                <outlet property="stockLabel" destination="k2z-XO-aQr" id="tAK-Br-eNE"/>
                <outlet property="stockView" destination="8sO-KO-5k1" id="EjH-o3-Jsm"/>
                <outlet property="tagBgImg" destination="sfw-Ba-VNb" id="Bsn-NM-IO5"/>
                <outlet property="tagWidth" destination="y5I-ft-meh" id="rtX-ej-eXf"/>
            </connections>
            <point key="canvasLocation" x="187.68115942028987" y="204.91071428571428"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
