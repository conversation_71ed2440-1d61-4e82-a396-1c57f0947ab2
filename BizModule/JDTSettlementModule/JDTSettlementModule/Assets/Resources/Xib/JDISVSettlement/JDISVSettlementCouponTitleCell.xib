<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="JDISVSettlementCouponTitleCell">
            <rect key="frame" x="0.0" y="0.0" width="166" height="43"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="166" height="43"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rxc-yD-JYR">
                        <rect key="frame" x="0.0" y="11" width="166" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstItem="Rxc-yD-JYR" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" id="68M-S1-uZe"/>
                <constraint firstItem="Rxc-yD-JYR" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Qbf-9y-kGE"/>
                <constraint firstAttribute="trailing" secondItem="Rxc-yD-JYR" secondAttribute="trailing" id="fsR-cY-wRI"/>
                <constraint firstItem="Rxc-yD-JYR" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="iVE-6L-b4B"/>
            </constraints>
            <connections>
                <outlet property="couponTitleLabel" destination="Rxc-yD-JYR" id="XZR-44-OeE"/>
            </connections>
            <point key="canvasLocation" x="19" y="48"/>
        </collectionViewCell>
    </objects>
</document>
