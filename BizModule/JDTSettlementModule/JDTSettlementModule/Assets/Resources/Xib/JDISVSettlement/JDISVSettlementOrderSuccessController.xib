<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="JDISVSettlementOrderSuccessController">
            <connections>
                <outlet property="backToHomeButton" destination="c9I-OZ-f08" id="uL1-Cb-nYa"/>
                <outlet property="checkOrderListButton" destination="lBd-8K-3Fh" id="H3k-PP-D5f"/>
                <outlet property="submitSuccessInfoLabel" destination="djT-w8-dXL" id="Xwd-A7-tCV"/>
                <outlet property="submitSuccessTitleLabel" destination="aAi-Xk-fK9" id="Bwf-e3-Ma1"/>
                <outlet property="successImgView" destination="Bxs-4E-swd" id="Rn4-Iz-0dT"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="M7y-o5-Fyi">
                    <rect key="frame" x="47" y="100" width="320" height="140"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aAi-Xk-fK9">
                            <rect key="frame" x="154.5" y="3.5" width="41.5" height="23"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="23" id="0p4-lH-lex"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Bxs-4E-swd">
                            <rect key="frame" x="118.5" y="0.0" width="30" height="30"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="30" id="8SX-9a-2aX"/>
                                <constraint firstAttribute="width" constant="30" id="rzb-Et-lGx"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="c9I-OZ-f08">
                            <rect key="frame" x="0.0" y="110" width="154" height="30"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="154" id="vYr-qW-Ucb"/>
                                <constraint firstAttribute="height" constant="30" id="waP-3c-lqL"/>
                            </constraints>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="lBd-8K-3Fh">
                            <rect key="frame" x="166" y="110" width="154" height="30"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="154" id="JR7-GR-f4t"/>
                                <constraint firstAttribute="height" constant="30" id="cVA-63-iGW"/>
                            </constraints>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="djT-w8-dXL">
                            <rect key="frame" x="139.5" y="48.5" width="41.5" height="34"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="34" id="j5d-WS-keN"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="Bxs-4E-swd" firstAttribute="top" secondItem="M7y-o5-Fyi" secondAttribute="top" id="8qd-yI-DtY"/>
                        <constraint firstItem="djT-w8-dXL" firstAttribute="top" secondItem="aAi-Xk-fK9" secondAttribute="bottom" constant="22" id="MfN-Kl-tl0"/>
                        <constraint firstItem="aAi-Xk-fK9" firstAttribute="centerX" secondItem="M7y-o5-Fyi" secondAttribute="centerX" constant="15" id="Ojd-It-HE0"/>
                        <constraint firstItem="c9I-OZ-f08" firstAttribute="leading" secondItem="M7y-o5-Fyi" secondAttribute="leading" id="WQH-qt-irT"/>
                        <constraint firstAttribute="bottom" secondItem="lBd-8K-3Fh" secondAttribute="bottom" id="aKc-3P-ES8"/>
                        <constraint firstAttribute="trailing" secondItem="lBd-8K-3Fh" secondAttribute="trailing" id="hOf-Em-GVQ"/>
                        <constraint firstItem="aAi-Xk-fK9" firstAttribute="centerY" secondItem="Bxs-4E-swd" secondAttribute="centerY" id="hcZ-8P-DY8"/>
                        <constraint firstAttribute="bottom" secondItem="c9I-OZ-f08" secondAttribute="bottom" id="lsw-TU-utw"/>
                        <constraint firstItem="aAi-Xk-fK9" firstAttribute="leading" secondItem="Bxs-4E-swd" secondAttribute="trailing" constant="6" id="neC-bj-q8W"/>
                        <constraint firstAttribute="height" constant="140" id="sqe-tz-hXN"/>
                        <constraint firstAttribute="width" constant="320" id="tmh-2V-hLd"/>
                        <constraint firstItem="djT-w8-dXL" firstAttribute="centerX" secondItem="M7y-o5-Fyi" secondAttribute="centerX" id="vmk-9Y-e6Z"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="M7y-o5-Fyi" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="18v-z8-cOx"/>
                <constraint firstItem="M7y-o5-Fyi" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="100" id="Bsx-TU-cRX"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="141" y="65"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
