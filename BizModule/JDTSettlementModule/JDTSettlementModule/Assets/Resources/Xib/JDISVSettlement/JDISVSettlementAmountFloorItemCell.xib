<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementAmountFloorItemCell" rowHeight="38" id="KGk-i7-Jjw" customClass="JDISVSettlementAmountFloorItemCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="38"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="38"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l42-Gr-Bs8">
                        <rect key="frame" x="18" y="6" width="41.5" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="hKw-Qz-EJ6"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="piS-wl-EUy">
                        <rect key="frame" x="244" y="8.5" width="42" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="B6M-pp-uLQ"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <constraints>
                    <constraint firstItem="l42-Gr-Bs8" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="2Dy-SQ-b4t"/>
                    <constraint firstItem="piS-wl-EUy" firstAttribute="centerY" secondItem="l42-Gr-Bs8" secondAttribute="centerY" id="Mmx-u3-M3J"/>
                    <constraint firstItem="l42-Gr-Bs8" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="6" id="STH-pr-tC4"/>
                    <constraint firstAttribute="trailing" secondItem="piS-wl-EUy" secondAttribute="trailing" constant="34" id="aLo-yC-TX7"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="amountInfoLabel" destination="piS-wl-EUy" id="vLe-aW-Vyr"/>
                <outlet property="amountTitleLabel" destination="l42-Gr-Bs8" id="bFE-xd-JFa"/>
            </connections>
            <point key="canvasLocation" x="19" y="139"/>
        </tableViewCell>
    </objects>
</document>
