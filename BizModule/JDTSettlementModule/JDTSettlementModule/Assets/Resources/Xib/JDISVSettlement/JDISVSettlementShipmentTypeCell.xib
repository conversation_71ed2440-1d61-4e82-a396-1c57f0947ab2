<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="JDISVSettlementShipmentTypeCell" id="gTV-IL-0wX" customClass="JDISVSettlementShipmentTypeCell">
            <rect key="frame" x="0.0" y="0.0" width="295" height="50"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="295" height="50"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2Mn-3n-fjW" customClass="KALabel">
                        <rect key="frame" x="0.0" y="12" width="295" height="26"/>
                    </button>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="2Mn-3n-fjW" secondAttribute="bottom" constant="12" id="0c5-pH-pc1"/>
                <constraint firstAttribute="trailing" secondItem="2Mn-3n-fjW" secondAttribute="trailing" id="BFe-WL-Usy"/>
                <constraint firstItem="2Mn-3n-fjW" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="12" id="mqu-G4-2jB"/>
                <constraint firstItem="2Mn-3n-fjW" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="qRT-V9-T5B"/>
            </constraints>
            <connections>
                <outlet property="shipmentTypeButton" destination="2Mn-3n-fjW" id="lmm-xh-lFc"/>
            </connections>
            <point key="canvasLocation" x="17" y="139"/>
        </collectionViewCell>
    </objects>
</document>
