<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="JDISVSettlementShipmentController">
            <connections>
                <outlet property="bottomView" destination="OiN-G8-LVq" id="SPB-Q3-sDX"/>
                <outlet property="bottomViewBottomSpace" destination="USr-ZH-D1Z" id="Nra-KL-yQL"/>
                <outlet property="bottomViewHeight" destination="ViE-el-yQw" id="OGu-cs-LcW"/>
                <outlet property="closeButton" destination="VBV-zy-bzJ" id="hCe-f8-5AR"/>
                <outlet property="confirmButton" destination="mos-6w-DNn" id="xjS-jH-FvJ"/>
                <outlet property="shipmentCollectionView" destination="qdx-Jg-sEE" id="ivf-F0-1oD"/>
                <outlet property="shipmentTitleLabel" destination="Atd-a2-NDw" id="RBP-HM-KAX"/>
                <outlet property="shipmentTypeView" destination="QWl-hq-snF" id="F8H-hw-fww"/>
                <outlet property="shipmentTypeViewHeight" destination="eq5-po-1Sl" id="KxH-RA-ZS6"/>
                <outlet property="tableView" destination="qsp-aG-H3o" id="Y9u-o7-LHk"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AaB-up-jgK">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="63"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Atd-a2-NDw">
                            <rect key="frame" x="186.5" y="19" width="41.5" height="25"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="25" id="R25-vx-idN"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VBV-zy-bzJ">
                            <rect key="frame" x="360" y="0.0" width="48" height="63"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="48" id="EBe-mG-UuC"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="Atd-a2-NDw" firstAttribute="centerY" secondItem="AaB-up-jgK" secondAttribute="centerY" id="438-TO-XgK"/>
                        <constraint firstItem="Atd-a2-NDw" firstAttribute="centerX" secondItem="AaB-up-jgK" secondAttribute="centerX" id="9hZ-Ei-zlc"/>
                        <constraint firstAttribute="bottom" secondItem="VBV-zy-bzJ" secondAttribute="bottom" id="EBk-mq-Eys"/>
                        <constraint firstAttribute="height" constant="63" id="PHv-xO-w6t"/>
                        <constraint firstAttribute="trailing" secondItem="VBV-zy-bzJ" secondAttribute="trailing" constant="6" id="QgU-PK-i0N"/>
                        <constraint firstItem="VBV-zy-bzJ" firstAttribute="top" secondItem="AaB-up-jgK" secondAttribute="top" id="pWk-d0-k0i"/>
                    </constraints>
                </view>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OiN-G8-LVq">
                    <rect key="frame" x="0.0" y="846" width="414" height="50"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mos-6w-DNn">
                            <rect key="frame" x="18" y="5" width="378" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="Yxp-GU-vUo"/>
                            </constraints>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="mos-6w-DNn" firstAttribute="leading" secondItem="OiN-G8-LVq" secondAttribute="leading" constant="18" id="8kG-hF-vth"/>
                        <constraint firstAttribute="trailing" secondItem="mos-6w-DNn" secondAttribute="trailing" constant="18" id="Pnf-hT-XJY"/>
                        <constraint firstAttribute="height" constant="50" id="ViE-el-yQw"/>
                        <constraint firstItem="mos-6w-DNn" firstAttribute="centerY" secondItem="OiN-G8-LVq" secondAttribute="centerY" id="cYQ-7t-1jL"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="qsp-aG-H3o">
                    <rect key="frame" x="0.0" y="117" width="414" height="729"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </tableView>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QWl-hq-snF">
                    <rect key="frame" x="0.0" y="63" width="414" height="54"/>
                    <subviews>
                        <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" directionalLockEnabled="YES" scrollEnabled="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="qdx-Jg-sEE">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="54"/>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="4Ra-N8-uT8">
                                <size key="itemSize" width="128" height="128"/>
                                <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                            </collectionViewFlowLayout>
                        </collectionView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="qdx-Jg-sEE" firstAttribute="top" secondItem="QWl-hq-snF" secondAttribute="top" id="CPU-3X-Q7M"/>
                        <constraint firstAttribute="bottom" secondItem="qdx-Jg-sEE" secondAttribute="bottom" id="MlW-f7-pFI"/>
                        <constraint firstAttribute="height" constant="54" id="eq5-po-1Sl"/>
                        <constraint firstAttribute="trailing" secondItem="qdx-Jg-sEE" secondAttribute="trailing" id="kY6-Nw-KNR"/>
                        <constraint firstItem="qdx-Jg-sEE" firstAttribute="leading" secondItem="QWl-hq-snF" secondAttribute="leading" id="l24-os-Fh4"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="qsp-aG-H3o" secondAttribute="trailing" id="6Wu-13-kje"/>
                <constraint firstItem="QWl-hq-snF" firstAttribute="trailing" secondItem="AaB-up-jgK" secondAttribute="trailing" id="Ald-Oi-L1J"/>
                <constraint firstItem="qsp-aG-H3o" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="DFH-uA-sgl"/>
                <constraint firstItem="QWl-hq-snF" firstAttribute="top" secondItem="AaB-up-jgK" secondAttribute="bottom" id="IuF-4X-geR"/>
                <constraint firstAttribute="trailing" secondItem="OiN-G8-LVq" secondAttribute="trailing" id="Lpn-jm-cSy"/>
                <constraint firstItem="AaB-up-jgK" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="RUv-hd-Hkz"/>
                <constraint firstItem="QWl-hq-snF" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="TM8-vY-6nM"/>
                <constraint firstItem="qsp-aG-H3o" firstAttribute="top" secondItem="QWl-hq-snF" secondAttribute="bottom" id="UR7-zJ-nf7"/>
                <constraint firstAttribute="bottom" secondItem="OiN-G8-LVq" secondAttribute="bottom" id="USr-ZH-D1Z"/>
                <constraint firstItem="AaB-up-jgK" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="jIq-wY-58i"/>
                <constraint firstItem="OiN-G8-LVq" firstAttribute="top" secondItem="qsp-aG-H3o" secondAttribute="bottom" id="sE2-wG-eFk"/>
                <constraint firstItem="OiN-G8-LVq" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="uiT-kr-OVk"/>
                <constraint firstAttribute="trailing" secondItem="AaB-up-jgK" secondAttribute="trailing" id="xi6-GE-5Fe"/>
            </constraints>
            <point key="canvasLocation" x="-54" y="56"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
