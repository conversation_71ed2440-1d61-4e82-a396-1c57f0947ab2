<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="2304" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="JDISVSettlementShopFloorRemarkCell" rowHeight="56" id="KGk-i7-Jjw" customClass="JDISVSettlementShopFloorRemarkCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="56"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="56"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="留言" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Aaw-VC-fRL">
                        <rect key="frame" x="18" y="12" width="29" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="hn2-wL-1jy"/>
                            <constraint firstAttribute="width" constant="29" id="tnV-5l-vuW"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" clearButtonMode="whileEditing" translatesAutoresizingMaskIntoConstraints="NO" id="h7l-Sc-283" customClass="JDISVSettlementShopFloorRemarkCellTextFiled">
                        <rect key="frame" x="18" y="12" width="284" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="TUa-vV-wKu"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <textInputTraits key="textInputTraits"/>
                    </textField>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0Cc-27-s5A">
                        <rect key="frame" x="18" y="49.5" width="284" height="0.5"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="0.5" id="g0Z-Xj-2VT"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="h7l-Sc-283" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="4DM-uj-PMS"/>
                    <constraint firstItem="h7l-Sc-283" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="GNr-a2-Tc8"/>
                    <constraint firstItem="Aaw-VC-fRL" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="12" id="LeU-Tz-afC"/>
                    <constraint firstItem="Aaw-VC-fRL" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="NWI-7s-OEb"/>
                    <constraint firstAttribute="bottom" secondItem="0Cc-27-s5A" secondAttribute="bottom" constant="6" id="YfY-qG-HCc"/>
                    <constraint firstAttribute="trailing" secondItem="0Cc-27-s5A" secondAttribute="trailing" constant="18" id="tHy-y2-6Bd"/>
                    <constraint firstAttribute="trailing" secondItem="h7l-Sc-283" secondAttribute="trailing" constant="18" id="wyj-cE-Hp2"/>
                    <constraint firstItem="0Cc-27-s5A" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="yN5-mR-bfv"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="bottomLine" destination="0Cc-27-s5A" id="wQ9-7r-a2a"/>
                <outlet property="remarkTF" destination="h7l-Sc-283" id="Ha7-RH-Zdk"/>
                <outlet property="remarkTFHeight" destination="TUa-vV-wKu" id="lrB-Ft-5pG"/>
                <outlet property="remarkTFLeading" destination="GNr-a2-Tc8" id="174-dq-6eb"/>
                <outlet property="remarkTFTop" destination="4DM-uj-PMS" id="yr8-pC-faG"/>
                <outlet property="remarkTFTrailing" destination="wyj-cE-Hp2" id="X6d-Ci-Ltj"/>
                <outlet property="remarkTitleLabel" destination="Aaw-VC-fRL" id="eMh-bV-X5P"/>
                <outlet property="remarkTitleLabelWidth" destination="tnV-5l-vuW" id="t8P-7N-LXl"/>
            </connections>
            <point key="canvasLocation" x="24.637681159420293" y="143.30357142857142"/>
        </tableViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
