# JDTSettlementModule

[![CI Status](https://img.shields.io/travis/lvchenzhu.1/JDTSettlementModule.svg?style=flat)](https://travis-ci.org/lvchenzhu.1/JDTSettlementModule)
[![Version](https://img.shields.io/cocoapods/v/JDTSettlementModule.svg?style=flat)](https://cocoapods.org/pods/JDTSettlementModule)
[![License](https://img.shields.io/cocoapods/l/JDTSettlementModule.svg?style=flat)](https://cocoapods.org/pods/JDTSettlementModule)
[![Platform](https://img.shields.io/cocoapods/p/JDTSettlementModule.svg?style=flat)](https://cocoapods.org/pods/JDTSettlementModule)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

JDTSettlementModule is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'JDTSettlementModule'
```

## Author

lvchenzhu.1, <EMAIL>

## License

JDTSettlementModule is available under the MIT license. See the LICENSE file for more info.
