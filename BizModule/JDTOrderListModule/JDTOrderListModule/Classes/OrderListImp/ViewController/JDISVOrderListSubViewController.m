//
//  JDISVOrderListSubViewController.m
//  JDISVOrderListSDKModule
//
//  Created by gongyang2 on 2021/10/15.
//

#import "JDISVOrderListSubViewController.h"

#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "JDISVOrderListMacro.h"

#import "JDISVOrderListCardTableViewCell.h"
#import "JDISVOrderListEmptyTableViewCell.h"
#import "JDISVOrderListCardViewModel.h"
#import "JDISVOrderListSubViewModel.h"
#import "JDISVOrderListCellViewModel.h"
#import "JDISVCommonTools.h"
#import "NSString+orderList_urlencode.h"
#import <JDISVYYModelModule/YYModel.h>
@import JDTInfrastructureModule;

@interface JDISVOrderListSubViewController ()

@property (nonatomic, assign) BOOL needRefresh;
@property (nonatomic, strong) NSTimer *timer;

@end

@implementation JDISVOrderListSubViewController

- (void)dealloc{
    [self.timer invalidate];
    self.timer = nil;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    [self registerTableViewCellWithClassName:[JDISVOrderListEmptyTableViewCell class] forCellReuseIdentifier:kEmptyCell];
    [self registerTableViewCellWithClassName:[JDISVOrderListCardTableViewCell class] forCellReuseIdentifier:kCardCell];
    __weak typeof(self) weakSelf = self;
    self.timer = [NSTimer timerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        __strong typeof(self) strongSelf = weakSelf;
        [strongSelf scheduleTimer];
    }];
    [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
}
- (void)scheduleTimer{

    [[NSNotificationCenter defaultCenter] postNotificationName:ScheduleTimer object:nil];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    if (self.needRefresh) {
        self.needRefresh = NO;
        JDISVOrderListSubViewModel *viewModel = (id)self.viewModel;
        
        if (viewModel.originDataArray.count > 20) {
            return;
        }
        
        @weakify(self)
        [viewModel refreshAllDataWithBlock:^{
            @strongify(self)
            [self.tableView reloadData];
        }];
    }
}

- (void)processOrderDelegateActionWithParames:(JDISVOrderListSDKCellActionParam *)parames {
    [super processOrderDelegateActionWithParames:parames];
    
    @weakify(self)
    if ([parames.cellIdentifier isEqualToString:kCardCell]) {
        NSDictionary *info = parames.actionParams;
        NSString *action = info[@"action"];
        JDISVOrderListCellViewModel *cellvm = info[@"vm"];
//        JDISVOrderListCardViewModel *cardVM = cellvm.model;
        JDISVOrderListSubViewModel *viewModel = (id)self.viewModel;
        @weakify(viewModel)
        if ([action isEqualToString:@"tap"]) {
            NSUInteger row = [self.viewModel.cellViewModels indexOfObject:cellvm];
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:0];
            [self clickOrderAction:indexPath];
        } else if ([action isEqualToString:@"shop"]) {
            // 跳转店铺
//            NSString *shopId = cellvm.order.shopInfo.shopId;
            NSString *shopId = cellvm.orderListItem.shopId;
            if (![shopId isKindOfClass:NSString.class] || shopId.length == 0) {
                return;
            }
//            NSString *shopName = cellvm.order.shopInfo.shopName;
            NSString *shopName = cellvm.orderListItem.shopName;
            NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeShop];
            [JDRouter openURL:[NSString stringWithFormat:@"router://%@/shopController", module] arg:@{@"pageCode": @"1", @"shopId": shopId ?: @"", @"shopName":shopName ?: @""} error:nil completion:^(UIViewController *vc) {
                vc.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:vc animated:YES];
            }];
        } else if ([action isEqualToString:@"actionButton"]) {
            // 按钮
            JDISVOrderListCardButtonModel *buttonModel = info[@"model"];
            switch (buttonModel.buttonModel.showLabelId) {
                case ORDERListItemButtonTypeToPay: {
                    self.needRefresh = YES;
//                    NSString *orderId = cellvm.order.orderBaseInfo.orderId;
                    NSString *orderId = cellvm.orderListItem.orderId;
                    if (![orderId isKindOfClass:NSString.class] || orderId.length == 0) {
                        [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderListL(@"ka_order_button_pay_request_failed")];
                        return;
                    }
                    // TODO:Juice  这里接口调用改为:order/pay?apiCode=b2c.cbff.order.pay
                    NSMutableDictionary *param = [NSMutableDictionary dictionary];
                    [param setObject:orderId forKey:@"orderId"];
                    [param setObject:@(3) forKey:@"sourceType"];//获取H5支付链接传3
                    @weakify(self)
                    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"" function:@"getPayUrl" version:@"1.0" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
                        @strongify(self)
                        if (error) {
                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderListL(@"ka_order_button_pay_request_failed")];
                            NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
                            NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
                            [JDRouter openURL:url arg:@{@"name":@"GetPaymentUrlError"} error:nil completion:nil];//获取支付链接失败埋点
                        } else {
                            NSNumber *success = [responseObject objectForKey:@"success"];
                            if (success.boolValue) {
                                NSDictionary *dataDic = [responseObject objectForKey:@"value"];
                                NSString *jsonstr = [dataDic objectForKey:@"url"];
                                NSDictionary *jsonDic = [JDISVOrderListSubViewController dictionaryWithJsonString:jsonstr];
                                NSString *payUrl = [jsonDic objectForKey:@"httpUrl"];
                                if ([payUrl jdcd_validateString]) {
                                    //跳转收银台
                                    NSDictionary* param = @{@"text_url":payUrl};
                                    NSString* WebModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeWebView ];
                                    NSString* routerStr = [NSString stringWithFormat:@"router://%@/getWebViewController",WebModule];
                                    [JDRouter openURL:routerStr arg:param error:nil completion:^(UIViewController *obj){
                                        obj.hidesBottomBarWhenPushed = YES;
                                        [self.navigationController pushViewController:obj animated:YES];
                                    }];
                                }
                            } else {
                                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderListL(@"ka_order_button_pay_request_failed")];
                                NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeTracker];
                                NSString *url = [NSString stringWithFormat:@"router://%@/PerformanceEvent", moduleName];
                                [JDRouter openURL:url arg:@{@"name":@"GetPaymentUrlError"} error:nil completion:nil];//获取支付链接失败埋点
                            }
                        }
                    }];
                    break;
                }
                case ORDERListItemButtonTypeQueryLogistics: {
                    [self queryLogisticsWithOrderId:cellvm.orderListItem.orderId];
                    break;
                }
                case ORDERListItemButtonTypeBuyAgain: {
                    self.needRefresh = YES;
                    NSString *shoppingCart = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeShoppingCart)];
                    // 反选
                    NSString *unselect = [NSString stringWithFormat:@"router://%@/unSelectAll", shoppingCart];
                    [JDRouter openURL:unselect arg:nil error:nil completion:nil];
                    // 加车
                    NSString *addCart = [NSString stringWithFormat:@"router://%@/addCart", shoppingCart];
                    NSMutableArray *items = [NSMutableArray array];
                    for (ORDERListItemSkuDetailModel *sku in cellvm.orderListItem.orderItem) {
                        if (!sku.skuId) {
                            continue;
                        }
                        NSUInteger count = sku.num.integerValue;
                        if (count < 1) {
                            count = 1;
                        }
                        NSMutableDictionary *temp = [NSMutableDictionary dictionary];
                        [temp setObject:sku.skuId forKey:@"skuId"];
                        [temp setObject:@(count) forKey:@"num"];
                        [temp setObject:@(1) forKey:@"itemType"];
                        [items addObject:[temp copy]];
                    }
                    if (items.count == 0) {
                        return;
                    }
                    [JDRouter openURL:addCart arg:@{@"operations": @{@"products": [items copy]}} error:nil completion:^(NSDictionary * _Nullable object) {
                        @strongify(self)
                        NSString *code = [object objectForKey:@"code"];
                        if ([code isEqual:@"0"]) {
                            // 跳转购物车
                            NSString *show = [NSString stringWithFormat:@"router://%@/shoppingCartController", shoppingCart];
                            UIViewController *vc = [JDRouter openURL:show arg:nil error:nil completion:nil];
                            vc.hidesBottomBarWhenPushed = YES;
                            [self.navigationController pushViewController:vc animated:YES];
                        } else {
                            NSString *msg = [object objectForKey:@"message"];
                            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:msg ?: OrderListL(@"ka_order_button_buy_again_failed")];
                        }
                    }];
                    break;
                }
                case ORDERListItemButtonTypeCancelOrder: {
                    
                    break;
                }
                case ORDERListItemButtonTypeApplyAfterSale: {
                    
                    break;
                }
                case ORDERListItemButtonTypeDeleteOrder: {
                    
                    break;
                }
                case ORDERListItemButtonTypeConfirmReceipt: {
                    [KAAlert alert].config.renderW3(OrderListL(@"isv_order_comfirm_receipt_des")).ka_addAction(OrderListL(@"ka_order_dialog_btn_cancel"), ^(UIButton * _Nonnull button) {
                        [button renderB5];
                    }, ^{
                        // 取消
                    })
                    .ka_addAction(OrderListL(@"isv_order_confirm_receipt"), ^(UIButton * _Nonnull button) {
                        [button renderB3];
                    }, ^{
                        // 删除
                        NSString *orderId = cellvm.orderListItem.orderId;
                        @strongify(viewModel)
                        [viewModel comfirmOrderInfoWithOrderId:orderId block:^(BOOL result) {
                            if (result) {
                                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:OrderListL(@"isv_order_comfirm_receipt_end")];
                                @strongify(viewModel)
                                [viewModel refreshAllDataWithBlock:^{
                                    @strongify(self)
                                    [self.tableView reloadData];
                                }];
                            } else {
                                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderListL(@"ka_order_button_ensure_receive_request_failed")];
                            }
                        }];
                        
                    }).jdcd_show();
                    break;
                }
                default:
                    break;
            }
        }
    }
}

- (void)queryLogisticsWithOrderId:(NSString *)orderId {
    NSDictionary *testParams = @{
        @"orderId": orderId
    };
    [[OOPNetworkManager sharedManager] POST:@"order/shipment?apiCode=b2c.cbff.order.shipment" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            // TODO:Juice 补充多语言文案
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:@"获取物流信息失败"];
        } else {
            NSArray <ORDERListItemShipmentOrderModel *> *shipmentArr = [NSArray yy_modelArrayWithClass:[ORDERListItemShipmentOrderModel class] json:responseObject[@"data"]];
            NSArray *orderUMS = shipmentArr.firstObject.orderUms;
            UIViewController *vc = [JDRouter openURL:@"router://JDISVOrderListSDKModule/getOrderTrackViewController" arg:@{@"trackDataList": orderUMS} error:nil completion:nil];
            KAFloatLayerPresentationController *floatVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:vc presentingViewController:self];
            floatVC.contentHeight = 0.68 * [[UIScreen mainScreen] bounds].size.height;
            vc.transitioningDelegate = floatVC;
            [self presentViewController:vc animated:YES completion:nil];
        }
    }];
}

- (void)openCheckoutPageWithOrderId:(NSString *)orderId{
    //预售付尾款打开结算
    NSDictionary *arg = @{@"orderId": orderId? : @"",@"presaleEndPayment":@(1)};//预售付尾款打开结算
    [PlatformService showLoadingInView:self.view];
    @weakify(self)
    [JDRouter openURL:@"router://JDISVSettlementModule/settlementController" arg:arg error:nil completion:^(id  _Nullable object) {
        @strongify(self)
        [PlatformService dismissInView:self.view];
        NSNumber *code = [object objectForKey:@"code"];
        if ([code integerValue] == 0) {
            // 成功
            UIViewController *vc = [object objectForKey:@"result"];
            if (vc) {
                vc.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:vc animated:YES];
            }
        } else {
            // 失败
            NSString *message = [object objectForKey:@"result"];
            if ([message jdcd_validateString]) {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:message];
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:OrderListL(@"ka_order_submit_error")];
            }
        }
    }];
}

//确认延迟收货
- (void)delayReceiveWithOrderId:(NSString *)orderId{
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param setObject:orderId?:@"" forKey:@"orderId"];
    [param setObject:@(0) forKey:@"operatingPlatform"];//端口标识（0：C端，1：B端）
    @weakify(self)
    [PlatformService request:(JDCDHTTPSessionRequestTypePost) requestSerializerType:(JDCDURLRequestSerializerTypeForm) paramterType:(JDISVColorGateParameterTypeDefault) path:@"" function:@"record_save_record_color_3.0" version:@"" parameters:[param copy] complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        @strongify(self)
        if (error) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderListL(@"ka_order_submit_error")];
        } else {
            NSNumber *code = [responseObject objectForKey:@"code"];
            NSNumber *success = [responseObject objectForKey:@"success"];
            NSString *message = [responseObject objectForKey:@"message"];
            if (success.boolValue) {
                NSString *dataStr = [responseObject objectForKey:@"data"];
                if ([dataStr jdcd_validateString]) {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFinish message:dataStr];
                    JDISVOrderListSubViewModel *viewModel = (id)self.viewModel;
                    @weakify(self)
                    [viewModel refreshAllDataWithBlock:^{
                        @strongify(self)
                        [self.tableView reloadData];
                    }];
                }else{
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:OrderListL(@"ka_order_submit_error")];
                }
            } else {
                [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:[message jdcd_validateString] ? message : OrderListL(@"ka_order_button_pay_request_failed")];
            }
        }
    }];
}

#pragma mark - action

- (void)clickOrderAction:(NSIndexPath *)indexPath {
    // cell 点击
    @try {
        JDISVOrderListCellViewModel *cellvm = self.viewModel.cellViewModels[indexPath.row];
        if ([cellvm isKindOfClass:JDISVOrderListCellViewModel.class]) {
            NSString *module = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:(JDISVModuleTypeOrderList)];
            NSString *orderId = cellvm.orderListItem.orderId;
            NSDictionary *arg = @{@"orderId":orderId, @"sourceFrom": module ?: @""};
            UIViewController *vc = [JDRouter openURL:@"router://JDISVOrderDetailSDKModule/getOrderDetailController" arg:arg error:nil completion:nil];
             vc.hidesBottomBarWhenPushed = YES;
            [self.navigationController pushViewController:vc animated:YES];
            
            self.needRefresh = YES;
        }
    } @catch (NSException *exception) {
        
    }
    
}

#pragma mark - Tool Func
+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString
{
    if (jsonString == nil) {
        return nil;
    }

    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err) {
        NSLog(@"json err：%@",err);
        return nil;
    }
    return dic;
}


@end
