# ORDERTrackController 使用说明

## 功能描述
`ORDERTrackController` 是一个用于展示订单物流轨迹的视图控制器，以时间轴的形式展示物流信息。

## 主要特性
- 时间轴样式的物流轨迹展示
- 最新状态使用橙色圆点高亮显示
- 支持多行文本内容展示
- 自动计算Cell高度
- 清晰的时间和内容分离显示

## 数据模型
使用 `ORDERListItemUMSModel` 作为数据源：
```objc
@interface ORDERListItemUMSModel : NSObject
@property (nonatomic, copy) NSString *content;        // 物流内容描述
@property (nonatomic, copy) NSString *msgTime;        // 物流时间
@property (nonatomic, copy) NSString *operatorName;   // 操作人
@property (nonatomic, assign) NSInteger nodeFlag;     // 节点标识
@end
```

## 使用方法

### 1. 基本使用
```objc
#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

// 创建物流轨迹数据
NSMutableArray *trackDataList = [NSMutableArray array];

// 添加物流信息（按时间倒序，最新的在前面）
ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
track1.content = @"您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达";
track1.msgTime = @"2025-06-20 21:14:06";
[trackDataList addObject:track1];

ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
track2.content = @"您的订单已送达";
track2.msgTime = @"2025-06-20 21:14:06";
[trackDataList addObject:track2];

ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
track3.content = @"您的订单配送中";
track3.msgTime = @"2025-06-18 21:14:06";
[trackDataList addObject:track3];

// 创建控制器
ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:trackDataList];

// 推送显示
[self.navigationController pushViewController:trackController animated:YES];
```

### 2. 从订单数据中提取物流信息
```objc
// 假设从订单详情中获取物流数据
ORDERListItemModel *orderModel = [self getOrderDetail];
NSArray<ORDERListItemShipmentOrderModel *> *shipmentOrders = orderModel.shipmentOrder;

if (shipmentOrders.count > 0) {
    ORDERListItemShipmentOrderModel *shipment = shipmentOrders.firstObject;
    NSArray<ORDERListItemUMSModel *> *trackList = shipment.orderUms;
    
    // 创建物流轨迹控制器
    ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:trackList];
    [self.navigationController pushViewController:trackController animated:YES];
}
```

## 界面说明
- **时间轴圆点**: 第一个（最新）为橙色，其他为灰色
- **连接线**: 灰色竖线连接各个节点
- **内容文字**: 第一个为粗体黑色，其他为普通灰色
- **时间显示**: 小号灰色字体显示在内容下方
- **自适应高度**: 根据内容长度自动调整Cell高度

## 自定义扩展
如需自定义样式，可以修改 `ORDERTrackCell` 中的颜色和字体设置。
