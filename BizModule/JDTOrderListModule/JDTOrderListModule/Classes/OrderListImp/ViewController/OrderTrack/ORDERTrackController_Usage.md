# ORDERTrackController 使用说明

## 功能描述
`ORDERTrackController` 是一个用于展示订单物流轨迹的视图控制器，支持一单多包裹的物流信息展示，以时间轴的形式展示物流信息。

## 主要特性
- **多包裹支持**: 支持一单多包裹，可点击切换查看不同包裹的物流轨迹
- **时间轴样式**: 物流轨迹以时间轴形式展示
- **状态高亮**: 最新状态使用橙色圆点高亮显示
- **多行文本**: 支持多行文本内容展示
- **自适应高度**: 自动计算Cell高度
- **包裹信息**: 显示承运商和运单号，支持一键复制运单号
- **切换动画**: 包裹切换时有平滑的动画效果

## 数据模型
使用 `ORDERListItemShipmentOrderModel` 作为包裹数据源：
```objc
@interface ORDERListItemShipmentOrderModel : NSObject
@property (nonatomic, strong) NSNumber *shipmentId;           // 包裹ID
@property (nonatomic, copy) NSString *orderId;                // 订单ID
@property (nonatomic, strong) NSNumber *carrierId;            // 承运商ID
@property (nonatomic, copy) NSString *carrierName;            // 承运商名称
@property (nonatomic, copy) NSString *waybillNum;             // 运单号
@property (nonatomic, strong) NSNumber *skuNum;               // 商品总数量
@property (nonatomic, assign) NSInteger state;                // 包裹状态
@property (nonatomic, copy) NSArray<ORDERListItemUMSModel *> *orderUms; // 物流轨迹
@end
```

每个包裹的物流轨迹使用 `ORDERListItemUMSModel`：
```objc
@interface ORDERListItemUMSModel : NSObject
@property (nonatomic, copy) NSString *content;        // 物流内容描述
@property (nonatomic, copy) NSString *msgTime;        // 物流时间
@property (nonatomic, copy) NSString *operatorName;   // 操作人
@property (nonatomic, assign) NSInteger nodeFlag;     // 节点标识
@end
```

## 使用方法

### 1. 单包裹使用
```objc
#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

// 创建单个包裹的物流轨迹数据
ORDERListItemShipmentOrderModel *shipment = [[ORDERListItemShipmentOrderModel alloc] init];
shipment.carrierName = @"中通快递";
shipment.waybillNum = @"ZT1234567899765";

// 创建物流轨迹
NSMutableArray *trackList = [NSMutableArray array];
ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
track1.content = @"您的订单已送达";
track1.msgTime = @"2025-06-20 21:14:06";
[trackList addObject:track1];

ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
track2.content = @"您的订单配送中";
track2.msgTime = @"2025-06-18 21:14:06";
[trackList addObject:track2];

shipment.orderUms = trackList;

// 创建控制器
ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:@[shipment]];

// 推送显示
[self.navigationController pushViewController:trackController animated:YES];
```

### 2. 多包裹使用
```objc
// 创建多个包裹的物流轨迹数据
NSMutableArray *shipmentList = [NSMutableArray array];

// 包裹1
ORDERListItemShipmentOrderModel *shipment1 = [[ORDERListItemShipmentOrderModel alloc] init];
shipment1.carrierName = @"中通快递";
shipment1.waybillNum = @"ZT1234567899765";
shipment1.orderUms = [self createTrackDataForPackage1];
[shipmentList addObject:shipment1];

// 包裹2
ORDERListItemShipmentOrderModel *shipment2 = [[ORDERListItemShipmentOrderModel alloc] init];
shipment2.carrierName = @"京东快递";
shipment2.waybillNum = @"JD9876543210123";
shipment2.orderUms = [self createTrackDataForPackage2];
[shipmentList addObject:shipment2];

// 创建控制器（会自动显示包裹切换按钮）
ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:shipmentList];
[self.navigationController pushViewController:trackController animated:YES];
```

### 3. 从订单数据中提取物流信息
```objc
// 从订单详情中获取所有包裹的物流数据
ORDERListItemModel *orderModel = [self getOrderDetail];
NSArray<ORDERListItemShipmentOrderModel *> *shipmentOrders = orderModel.shipmentOrder;

if (shipmentOrders.count > 0) {
    // 直接传递所有包裹信息，控制器会自动处理单包裹或多包裹显示
    ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:shipmentOrders];
    [self.navigationController pushViewController:trackController animated:YES];
} else {
    NSLog(@"暂无物流信息");
}
```

## 界面说明

### 多包裹切换区域
- **包裹按钮**: 当有多个包裹时，顶部显示包裹切换按钮（包裹1、包裹2...）
- **选中状态**: 当前选中的包裹按钮为橙色背景，白色文字
- **未选中状态**: 未选中的包裹按钮为透明背景，黑色文字

### 包裹信息区域
- **承运商**: 显示当前包裹的承运商名称
- **运单号**: 显示当前包裹的运单号
- **复制按钮**: 点击可复制运单号到剪贴板

### 物流轨迹区域
- **时间轴圆点**: 第一个（最新）为橙色，其他为灰色
- **连接线**: 灰色竖线连接各个节点
- **内容文字**: 第一个为粗体黑色，其他为普通灰色
- **时间显示**: 小号灰色字体显示在内容下方
- **自适应高度**: 根据内容长度自动调整Cell高度

## 交互说明
- **包裹切换**: 点击不同的包裹按钮可切换查看对应包裹的物流轨迹
- **运单号复制**: 点击复制按钮可将当前包裹的运单号复制到剪贴板
- **滚动查看**: 物流轨迹支持上下滚动查看完整信息

## 自定义扩展
如需自定义样式，可以修改以下文件：
- `ORDERTrackCell`: 修改时间轴样式、颜色和字体设置
- `ORDERTrackController`: 修改包裹切换按钮样式和布局
