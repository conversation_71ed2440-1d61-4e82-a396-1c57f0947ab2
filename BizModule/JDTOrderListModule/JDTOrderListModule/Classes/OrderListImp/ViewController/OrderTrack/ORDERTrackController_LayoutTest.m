//
//  ORDERTrackController_LayoutTest.m
//  JDTOrderListModule
//
//  Created by AI Assistant on 2025-06-25.
//

#import <Foundation/Foundation.h>
#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

@interface ORDERTrackController_LayoutTest : NSObject

@end

@implementation ORDERTrackController_LayoutTest

// 测试布局效果的方法
+ (ORDERTrackController *)createTestController {
    // 创建测试数据
    NSMutableArray *shipmentList = [NSMutableArray array];
    
    // 包裹1
    ORDERListItemShipmentOrderModel *shipment1 = [[ORDERListItemShipmentOrderModel alloc] init];
    shipment1.carrierName = @"中通快递";
    shipment1.waybillNum = @"ZT1234567899765";
    shipment1.orderUms = [self createTestTrackData1];
    [shipmentList addObject:shipment1];
    
    // 包裹2
    ORDERListItemShipmentOrderModel *shipment2 = [[ORDERListItemShipmentOrderModel alloc] init];
    shipment2.carrierName = @"京东快递";
    shipment2.waybillNum = @"JD9876543210123";
    shipment2.orderUms = [self createTestTrackData2];
    [shipmentList addObject:shipment2];
    
    // 创建控制器
    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:shipmentList];
    controller.title = @"物流轨迹";
    
    return controller;
}

// 创建包裹1的测试数据
+ (NSArray<ORDERListItemUMSModel *> *)createTestTrackData1 {
    NSMutableArray *trackList = [NSMutableArray array];
    
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单已送达，感谢您的耐心等待";
    track1.msgTime = @"2025-06-25 14:30:00";
    track1.operatorName = @"配送员";
    track1.nodeFlag = 1;
    [trackList addObject:track1];
    
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单正在配送中";
    track2.msgTime = @"2025-06-25 10:20:00";
    track2.operatorName = @"配送员";
    track2.nodeFlag = 2;
    [trackList addObject:track2];
    
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单已从仓库发出";
    track3.msgTime = @"2025-06-24 18:45:00";
    track3.operatorName = @"仓库";
    track3.nodeFlag = 3;
    [trackList addObject:track3];
    
    return trackList;
}

// 创建包裹2的测试数据
+ (NSArray<ORDERListItemUMSModel *> *)createTestTrackData2 {
    NSMutableArray *trackList = [NSMutableArray array];
    
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单正在配送中，预计今日送达";
    track1.msgTime = @"2025-06-25 09:15:00";
    track1.operatorName = @"配送员";
    track1.nodeFlag = 1;
    [trackList addObject:track1];
    
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单已到达配送站点";
    track2.msgTime = @"2025-06-24 22:30:00";
    track2.operatorName = @"京东快递";
    track2.nodeFlag = 2;
    [trackList addObject:track2];
    
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单已发货";
    track3.msgTime = @"2025-06-24 16:20:00";
    track3.operatorName = @"仓库";
    track3.nodeFlag = 3;
    [trackList addObject:track3];
    
    return trackList;
}

// 在某个视图控制器中调用此方法来测试布局
+ (void)showTestControllerInViewController:(UIViewController *)parentViewController {
    ORDERTrackController *testController = [self createTestController];
    [parentViewController.navigationController pushViewController:testController animated:YES];
}

@end

/*
 使用方法：
 
 // 在任何视图控制器中调用
 [ORDERTrackController_LayoutTest showTestControllerInViewController:self];
 
 // 或者直接创建控制器
 ORDERTrackController *controller = [ORDERTrackController_LayoutTest createTestController];
 [self.navigationController pushViewController:controller animated:YES];
 
 */
