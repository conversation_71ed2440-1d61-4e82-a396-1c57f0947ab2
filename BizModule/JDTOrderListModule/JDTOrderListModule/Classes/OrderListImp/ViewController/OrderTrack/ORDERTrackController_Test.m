//
//  ORDERTrackController_Test.m
//  JDTOrderListModule
//
//  测试 ORDERTrackController 的实现
//

#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

@interface ORDERTrackControllerTest : NSObject
@end

@implementation ORDERTrackControllerTest

// 测试基本功能
+ (void)testBasicFunctionality {
    // 创建测试数据
    NSArray<ORDERListItemUMSModel *> *testData = [self createTestData];
    
    // 创建控制器
    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:testData];
    
    // 验证数据设置
    NSAssert(controller.trackDataList.count == testData.count, @"数据设置失败");
    NSAssert([controller.trackDataList.firstObject.content isEqualToString:@"您的订单已送达"], @"数据内容不匹配");
    
    NSLog(@"✅ 基本功能测试通过");
}

// 测试空数据处理
+ (void)testEmptyData {
    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:@[]];
    NSAssert(controller.trackDataList.count == 0, @"空数据处理失败");
    
    NSLog(@"✅ 空数据测试通过");
}

// 测试单条数据
+ (void)testSingleData {
    ORDERListItemUMSModel *singleModel = [[ORDERListItemUMSModel alloc] init];
    singleModel.content = @"订单已确认";
    singleModel.msgTime = @"2025-06-24 10:00:00";
    
    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:@[singleModel]];
    NSAssert(controller.trackDataList.count == 1, @"单条数据处理失败");
    
    NSLog(@"✅ 单条数据测试通过");
}

// 创建测试数据
+ (NSArray<ORDERListItemUMSModel *> *)createTestData {
    NSMutableArray *trackList = [NSMutableArray array];
    
    // 测试数据1：长文本
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达";
    track1.msgTime = @"2025-06-20 21:14:06";
    track1.operatorName = @"配送员";
    [trackList addObject:track1];
    
    // 测试数据2：普通文本
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单已送达";
    track2.msgTime = @"2025-06-20 21:14:06";
    track2.operatorName = @"配送员";
    [trackList addObject:track2];
    
    // 测试数据3：配送中
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单配送中";
    track3.msgTime = @"2025-06-18 21:14:06";
    track3.operatorName = @"配送员";
    [trackList addObject:track3];
    
    // 测试数据4：拣货完成
    ORDERListItemUMSModel *track4 = [[ORDERListItemUMSModel alloc] init];
    track4.content = @"您的订单已拣货完成，将由新建承运商0414为您配送，运单号为";
    track4.msgTime = @"2025-06-16 21:14:06";
    track4.operatorName = @"仓库";
    [trackList addObject:track4];
    
    // 测试数据5：订单确认
    ORDERListItemUMSModel *track5 = [[ORDERListItemUMSModel alloc] init];
    track5.content = @"订单已确认，等待发货";
    track5.msgTime = @"2025-06-16 15:11:18";
    track5.operatorName = @"系统";
    [trackList addObject:track5];
    
    return trackList;
}

// 运行所有测试
+ (void)runAllTests {
    NSLog(@"🚀 开始运行 ORDERTrackController 测试...");
    
    [self testBasicFunctionality];
    [self testEmptyData];
    [self testSingleData];
    
    NSLog(@"🎉 所有测试通过！");
}

@end

// 在需要的地方调用测试
// [ORDERTrackControllerTest runAllTests];
