//
//  ORDERTrackController_Test.m
//  JDTOrderListModule
//
//  测试 ORDERTrackController 的实现
//

#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

@interface ORDERTrackControllerTest : NSObject
@end

@implementation ORDERTrackControllerTest

// 测试基本功能
+ (void)testBasicFunctionality {
    // 创建测试数据
    NSArray<ORDERListItemShipmentOrderModel *> *testData = [self createTestShipmentData];

    // 创建控制器
    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:testData];

    // 验证数据设置
    NSAssert(controller.trackDataList.count == testData.count, @"数据设置失败");
    NSAssert([controller.trackDataList.firstObject.carrierName isEqualToString:@"中通快递"], @"数据内容不匹配");

    NSLog(@"✅ 基本功能测试通过");
}

// 测试空数据处理
+ (void)testEmptyData {
    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:@[]];
    NSAssert(controller.trackDataList.count == 0, @"空数据处理失败");
    
    NSLog(@"✅ 空数据测试通过");
}

// 测试单包裹数据
+ (void)testSinglePackageData {
    ORDERListItemShipmentOrderModel *singlePackage = [[ORDERListItemShipmentOrderModel alloc] init];
    singlePackage.carrierName = @"京东快递";
    singlePackage.waybillNum = @"JD123456789";
    singlePackage.orderUms = [self createSingleTrackData];

    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:@[singlePackage]];
    NSAssert(controller.trackDataList.count == 1, @"单包裹数据处理失败");

    NSLog(@"✅ 单包裹数据测试通过");
}

// 测试多包裹数据
+ (void)testMultiplePackageData {
    NSArray<ORDERListItemShipmentOrderModel *> *multiplePackages = [self createTestShipmentData];

    ORDERTrackController *controller = [[ORDERTrackController alloc] initWithTrackDataList:multiplePackages];
    NSAssert(controller.trackDataList.count == 2, @"多包裹数据处理失败");
    NSAssert(controller.currentPackageIndex == 0, @"默认包裹索引错误");

    NSLog(@"✅ 多包裹数据测试通过");
}

// 创建测试包裹数据
+ (NSArray<ORDERListItemShipmentOrderModel *> *)createTestShipmentData {
    NSMutableArray *shipmentList = [NSMutableArray array];

    // 包裹1
    ORDERListItemShipmentOrderModel *shipment1 = [[ORDERListItemShipmentOrderModel alloc] init];
    shipment1.shipmentId = @1;
    shipment1.carrierName = @"中通快递";
    shipment1.waybillNum = @"ZT1234567899765";
    shipment1.orderUms = [self createTestTrackData];
    [shipmentList addObject:shipment1];

    // 包裹2
    ORDERListItemShipmentOrderModel *shipment2 = [[ORDERListItemShipmentOrderModel alloc] init];
    shipment2.shipmentId = @2;
    shipment2.carrierName = @"京东快递";
    shipment2.waybillNum = @"JD9876543210123";
    shipment2.orderUms = [self createTestTrackData];
    [shipmentList addObject:shipment2];

    return shipmentList;
}

// 创建单个物流轨迹数据
+ (NSArray<ORDERListItemUMSModel *> *)createSingleTrackData {
    ORDERListItemUMSModel *track = [[ORDERListItemUMSModel alloc] init];
    track.content = @"订单已确认";
    track.msgTime = @"2025-06-24 10:00:00";
    track.operatorName = @"系统";
    return @[track];
}

// 创建测试物流轨迹数据
+ (NSArray<ORDERListItemUMSModel *> *)createTestTrackData {
    NSMutableArray *trackList = [NSMutableArray array];
    
    // 测试数据1：长文本
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达";
    track1.msgTime = @"2025-06-20 21:14:06";
    track1.operatorName = @"配送员";
    [trackList addObject:track1];
    
    // 测试数据2：普通文本
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单已送达";
    track2.msgTime = @"2025-06-20 21:14:06";
    track2.operatorName = @"配送员";
    [trackList addObject:track2];
    
    // 测试数据3：配送中
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单配送中";
    track3.msgTime = @"2025-06-18 21:14:06";
    track3.operatorName = @"配送员";
    [trackList addObject:track3];
    
    // 测试数据4：拣货完成
    ORDERListItemUMSModel *track4 = [[ORDERListItemUMSModel alloc] init];
    track4.content = @"您的订单已拣货完成，将由新建承运商0414为您配送，运单号为";
    track4.msgTime = @"2025-06-16 21:14:06";
    track4.operatorName = @"仓库";
    [trackList addObject:track4];
    
    // 测试数据5：订单确认
    ORDERListItemUMSModel *track5 = [[ORDERListItemUMSModel alloc] init];
    track5.content = @"订单已确认，等待发货";
    track5.msgTime = @"2025-06-16 15:11:18";
    track5.operatorName = @"系统";
    [trackList addObject:track5];
    
    return trackList;
}

// 运行所有测试
+ (void)runAllTests {
    NSLog(@"🚀 开始运行 ORDERTrackController 多包裹功能测试...");

    [self testBasicFunctionality];
    [self testEmptyData];
    [self testSinglePackageData];
    [self testMultiplePackageData];

    NSLog(@"🎉 所有测试通过！");
}

@end

// 在需要的地方调用测试
// [ORDERTrackControllerTest runAllTests];
