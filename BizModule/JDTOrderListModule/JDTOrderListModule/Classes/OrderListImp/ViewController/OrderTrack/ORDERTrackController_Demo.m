//
//  ORDERTrackController_Demo.m
//  JDTOrderListModule
//
//  演示如何使用 ORDERTrackController
//

#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

@interface SomeOrderListController : UIViewController
@end

@implementation SomeOrderListController

// 示例：用户点击查看物流轨迹按钮
- (void)viewTrackButtonClicked {
    // 创建模拟的物流轨迹数据（实际项目中从网络获取）
    NSArray<ORDERListItemUMSModel *> *trackDataList = [self createMockTrackData];
    
    // 创建物流轨迹控制器
    ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:trackDataList];
    
    // 推送显示
    [self.navigationController pushViewController:trackController animated:YES];
}

// 创建模拟的物流轨迹数据
- (NSArray<ORDERListItemUMSModel *> *)createMockTrackData {
    NSMutableArray *trackList = [NSMutableArray array];
    
    // 最新状态
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达您的订单已送达";
    track1.msgTime = @"2025-06-20 21:14:06";
    track1.operatorName = @"配送员";
    track1.nodeFlag = 1;
    [trackList addObject:track1];
    
    // 第二个状态
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单已送达";
    track2.msgTime = @"2025-06-20 21:14:06";
    track2.operatorName = @"配送员";
    track2.nodeFlag = 2;
    [trackList addObject:track2];
    
    // 第三个状态
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单已送达";
    track3.msgTime = @"2025-06-20 21:14:06";
    track3.operatorName = @"配送员";
    track3.nodeFlag = 3;
    [trackList addObject:track3];
    
    // 配送中
    ORDERListItemUMSModel *track4 = [[ORDERListItemUMSModel alloc] init];
    track4.content = @"您的订单配送中";
    track4.msgTime = @"2025-06-18 21:14:06";
    track4.operatorName = @"配送员";
    track4.nodeFlag = 4;
    [trackList addObject:track4];
    
    // 拣货完成
    ORDERListItemUMSModel *track5 = [[ORDERListItemUMSModel alloc] init];
    track5.content = @"您的订单已拣货完成，将由新建承运商0414为您配送，运单号为";
    track5.msgTime = @"2025-06-16 21:14:06";
    track5.operatorName = @"仓库";
    track5.nodeFlag = 5;
    [trackList addObject:track5];
    
    // 订单确认
    ORDERListItemUMSModel *track6 = [[ORDERListItemUMSModel alloc] init];
    track6.content = @"订单已确认，等待发货";
    track6.msgTime = @"2025-06-16 15:11:18";
    track6.operatorName = @"系统";
    track6.nodeFlag = 6;
    [trackList addObject:track6];
    
    return trackList;
}

// 从真实订单数据中获取物流信息的示例
- (void)showTrackFromOrderModel:(ORDERListItemModel *)orderModel {
    if (orderModel.shipmentOrder.count > 0) {
        ORDERListItemShipmentOrderModel *shipment = orderModel.shipmentOrder.firstObject;
        NSArray<ORDERListItemUMSModel *> *trackList = shipment.orderUms;
        
        if (trackList.count > 0) {
            ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:trackList];
            [self.navigationController pushViewController:trackController animated:YES];
        } else {
            // 显示暂无物流信息
            NSLog(@"暂无物流信息");
        }
    }
}

@end
