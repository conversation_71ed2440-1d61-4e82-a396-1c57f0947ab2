//
//  ORDERTrackController_Demo.m
//  JDTOrderListModule
//
//  演示如何使用 ORDERTrackController
//

#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>

@interface SomeOrderListController : UIViewController
@end

@implementation SomeOrderListController

// 示例：用户点击查看物流轨迹按钮
- (void)viewTrackButtonClicked {
    // 创建模拟的多包裹物流轨迹数据（实际项目中从网络获取）
    NSArray<ORDERListItemShipmentOrderModel *> *shipmentDataList = [self createMockShipmentData];

    // 创建物流轨迹控制器
    ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:shipmentDataList];

    // 推送显示
    [self.navigationController pushViewController:trackController animated:YES];
}

// 创建模拟的多包裹物流轨迹数据
- (NSArray<ORDERListItemShipmentOrderModel *> *)createMockShipmentData {
    NSMutableArray *shipmentList = [NSMutableArray array];

    // 包裹1
    ORDERListItemShipmentOrderModel *shipment1 = [[ORDERListItemShipmentOrderModel alloc] init];
    shipment1.shipmentId = @1;
    shipment1.carrierName = @"中通快递";
    shipment1.waybillNum = @"ZT1234567899765";
    shipment1.orderUms = [self createMockTrackDataForPackage1];
    [shipmentList addObject:shipment1];

    // 包裹2
    ORDERListItemShipmentOrderModel *shipment2 = [[ORDERListItemShipmentOrderModel alloc] init];
    shipment2.shipmentId = @2;
    shipment2.carrierName = @"京东快递";
    shipment2.waybillNum = @"JD9876543210123";
    shipment2.orderUms = [self createMockTrackDataForPackage2];
    [shipmentList addObject:shipment2];

    return shipmentList;
}

// 创建包裹1的物流轨迹数据
- (NSArray<ORDERListItemUMSModel *> *)createMockTrackDataForPackage1 {
    NSMutableArray *trackList = [NSMutableArray array];

    // 最新状态
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单已送达，感谢您的耐心等待";
    track1.msgTime = @"2025-06-20 21:14:06";
    track1.operatorName = @"配送员";
    track1.nodeFlag = 1;
    [trackList addObject:track1];

    // 配送中
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单配送中";
    track2.msgTime = @"2025-06-20 15:30:00";
    track2.operatorName = @"配送员";
    track2.nodeFlag = 2;
    [trackList addObject:track2];

    // 拣货完成
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单已拣货完成，将由中通快递为您配送";
    track3.msgTime = @"2025-06-19 21:14:06";
    track3.operatorName = @"仓库";
    track3.nodeFlag = 3;
    [trackList addObject:track3];

    // 订单确认
    ORDERListItemUMSModel *track4 = [[ORDERListItemUMSModel alloc] init];
    track4.content = @"订单已确认，等待发货";
    track4.msgTime = @"2025-06-18 15:11:18";
    track4.operatorName = @"系统";
    track4.nodeFlag = 4;
    [trackList addObject:track4];

    return trackList;
}

// 创建包裹2的物流轨迹数据
- (NSArray<ORDERListItemUMSModel *> *)createMockTrackDataForPackage2 {
    NSMutableArray *trackList = [NSMutableArray array];

    // 配送中
    ORDERListItemUMSModel *track1 = [[ORDERListItemUMSModel alloc] init];
    track1.content = @"您的订单正在配送中，预计今日送达";
    track1.msgTime = @"2025-06-21 09:30:00";
    track1.operatorName = @"配送员";
    track1.nodeFlag = 1;
    [trackList addObject:track1];

    // 运输中
    ORDERListItemUMSModel *track2 = [[ORDERListItemUMSModel alloc] init];
    track2.content = @"您的订单正在运输中";
    track2.msgTime = @"2025-06-20 18:20:00";
    track2.operatorName = @"京东快递";
    track2.nodeFlag = 2;
    [trackList addObject:track2];

    // 已发货
    ORDERListItemUMSModel *track3 = [[ORDERListItemUMSModel alloc] init];
    track3.content = @"您的订单已发货，将由京东快递为您配送";
    track3.msgTime = @"2025-06-19 14:30:00";
    track3.operatorName = @"仓库";
    track3.nodeFlag = 3;
    [trackList addObject:track3];

    return trackList;
}

// 从真实订单数据中获取物流信息的示例
- (void)showTrackFromOrderModel:(ORDERListItemModel *)orderModel {
    NSArray<ORDERListItemShipmentOrderModel *> *shipmentOrders = orderModel.shipmentOrder;

    if (shipmentOrders.count > 0) {
        // 直接传递所有包裹信息，控制器内部会处理多包裹切换
        ORDERTrackController *trackController = [[ORDERTrackController alloc] initWithTrackDataList:shipmentOrders];
        [self.navigationController pushViewController:trackController animated:YES];
    } else {
        // 显示暂无物流信息
        NSLog(@"暂无物流信息");
    }
}

@end
