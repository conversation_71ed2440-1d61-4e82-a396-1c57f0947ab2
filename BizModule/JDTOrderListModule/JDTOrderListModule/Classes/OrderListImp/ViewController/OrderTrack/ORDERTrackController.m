//
//  ORDERTrackController.m
//  JDTOrderListModule
//
//  Created by lvchenzhu.1 on 2025/6/24.
//

#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "ORDERTrackCell.h"

@interface ORDERTrackController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UILabel *carrierNameLabel;

@property (nonatomic, strong) UILabel *shipNumLabel;

@property (nonatomic, strong) UIButton *copyBtn;

@property (nonatomic, strong) UITableView *tableView;

@end

@implementation ORDERTrackController

- (instancetype)initWithTrackDataList:(NSArray<ORDERListItemShipmentOrderModel *> *)trackDataList {
    self = [super init];
    if (self) {
        self.trackDataList = trackDataList;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupNavigationBar];
}

- (void)setupNavigationBar {
    self.title = @"物流轨迹";
    self.navigationController.navigationBar.backgroundColor = [UIColor whiteColor];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];

    [self.view addSubview:self.copyBtn];
    [self.copyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.view).offset(-18);
        make.size.mas_equalTo(CGSizeMake(48, 26));
        make.top.equalTo(self.view);
    }];
    
    [self.view addSubview:self.carrierNameLabel];
    [self.carrierNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view).offset(18);
        make.height.equalTo(@22);
    }];
    
    [self.view addSubview:self.shipNumLabel];
    [self.shipNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(18);
        make.height.equalTo(@22);
        make.top.equalTo(self.carrierNameLabel.mas_bottom);
    }];
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    [self.tableView registerClass:[ORDERTrackCell class] forCellReuseIdentifier:@"ORDERTrackCell"];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.trackDataList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ORDERTrackCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ORDERTrackCell" forIndexPath:indexPath];

    ORDERListItemUMSModel *model = self.trackDataList[indexPath.row];
    BOOL isFirst = (indexPath.row == 0);
    BOOL isLast = (indexPath.row == self.trackDataList.count - 1);

    [cell configWithModel:model isFirst:isFirst isLast:isLast];

    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 根据内容动态计算高度，这里先返回固定高度
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 80;
}

#pragma mark - Getters
- (UILabel *)carrierNameLabel {
    if (!_carrierNameLabel) {
        _carrierNameLabel = [[UILabel alloc] init];
        _carrierNameLabel.font = [UIFont systemFontOfSize:16];
        _carrierNameLabel.textColor = [UIColor blackColor];
    }
    return _carrierNameLabel;
}

- (UILabel *)shipNumLabel {
    if (!_shipNumLabel) {
        _shipNumLabel = [[UILabel alloc] init];
        _shipNumLabel.font = [UIFont systemFontOfSize:16];
        _shipNumLabel.textColor = [UIColor blackColor];
    }
    return _shipNumLabel;
}

- (UIButton *)copyBtn {
    if (!_copyBtn) {
        _copyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_copyBtn setTitle:@"复制" forState:UIControlStateNormal];
        [_copyBtn setTitleColor:[UIColor blueColor] forState:UIControlStateNormal];
        _copyBtn.titleLabel.font = [UIFont systemFontOfSize:16];
    }
    return _copyBtn;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = YES;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 80;
    }
    return _tableView;
}

@end
