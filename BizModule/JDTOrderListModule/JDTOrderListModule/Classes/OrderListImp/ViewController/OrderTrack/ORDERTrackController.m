//
//  ORDERTrackController.m
//  JDTOrderListModule
//
//  Created by lvchenzhu.1 on 2025/6/24.
//

#import "ORDERTrackController.h"
#import <JDTCommonToolModule/ORDERListModel.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import "ORDERTrackCell.h"

@interface ORDERTrackController () <UITableViewDelegate, UITableViewDataSource>

/// 包裹切换按钮容器
@property (nonatomic, strong) UIView *packageSwitchContainer;
/// 包裹切换按钮数组
@property (nonatomic, strong) NSMutableArray<UIButton *> *packageButtons;
/// 当前选中的包裹索引
@property (nonatomic, assign) NSInteger currentPackageIndex;
/// 承运商标签
@property (nonatomic, strong) UILabel *carrierNameLabel;
/// 运单号标签
@property (nonatomic, strong) UILabel *shipNumLabel;
/// 复制按钮
@property (nonatomic, strong) UIButton *copyBtn;
/// 表格视图
@property (nonatomic, strong) UITableView *tableView;

@end

@implementation ORDERTrackController

- (instancetype)initWithTrackDataList:(NSArray<ORDERListItemShipmentOrderModel *> *)trackDataList {
    self = [super init];
    if (self) {
        self.trackDataList = trackDataList;
        self.currentPackageIndex = 0; // 默认选中第一个包裹
        self.packageButtons = [NSMutableArray array];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupNavigationBar];
    [self updatePackageInfo];
}

- (void)setupNavigationBar {
    self.title = @"物流轨迹";
    self.navigationController.navigationBar.backgroundColor = [UIColor whiteColor];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];

    // 设置包裹切换按钮容器（如果有多个包裹）
    if (self.trackDataList.count > 1) {
        [self setupPackageSwitchContainer];
    }

    // 设置包裹信息区域
    [self setupPackageInfoArea];

    // 设置表格视图
    [self setupTableView];
}

- (void)setupPackageSwitchContainer {
    self.packageSwitchContainer = [[UIView alloc] init];
//    self.packageSwitchContainer.backgroundColor = [UIColor colorWithRed:0.95 green:0.95 blue:0.95 alpha:1.0];
    self.packageSwitchContainer.backgroundColor = [UIColor redColor];
    [self.view addSubview:self.packageSwitchContainer];

    [self.packageSwitchContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        if (@available(iOS 11.0, *)) {
            make.top.equalTo(self.view.mas_safeAreaLayoutGuide);
        } else {
            make.top.equalTo(self.view).offset(64);
        }
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(50);
    }];

    // 创建包裹切换按钮
    [self createPackageButtons];
}

- (void)createPackageButtons {
    [self.packageButtons removeAllObjects];

    for (NSInteger i = 0; i < self.trackDataList.count; i++) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button setTitle:[NSString stringWithFormat:@"包裹%ld", (long)(i + 1)] forState:UIControlStateNormal];
        [button setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateSelected];
        [button setBackgroundColor:[UIColor clearColor]];
        button.titleLabel.font = [UIFont systemFontOfSize:14];
        button.layer.cornerRadius = 15;
        button.layer.masksToBounds = YES;
        button.tag = i;
        [button addTarget:self action:@selector(packageButtonTapped:) forControlEvents:UIControlEventTouchUpInside];

        [self.packageSwitchContainer addSubview:button];
        [self.packageButtons addObject:button];
    }

    // 设置按钮布局
    [self layoutPackageButtons];

    // 默认选中第一个
    [self selectPackageAtIndex:0];
}

- (void)layoutPackageButtons {
    CGFloat buttonWidth = 60;
    CGFloat spacing = 10;
    CGFloat totalWidth = self.packageButtons.count * buttonWidth + (self.packageButtons.count - 1) * spacing;
    CGFloat startX = (self.view.frame.size.width - totalWidth) / 2;

    for (NSInteger i = 0; i < self.packageButtons.count; i++) {
        UIButton *button = self.packageButtons[i];
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.packageSwitchContainer);
            make.width.mas_equalTo(buttonWidth);
            make.height.mas_equalTo(30);
            if (i == 0) {
                make.centerX.equalTo(self.packageSwitchContainer).offset(-totalWidth/2 + buttonWidth/2);
            } else {
                make.left.equalTo(self.packageButtons[i-1].mas_right).offset(spacing);
            }
        }];
    }
}

- (void)setupPackageInfoArea {
    // 承运商标签
    [self.view addSubview:self.carrierNameLabel];

    // 运单号标签
    [self.view addSubview:self.shipNumLabel];

    // 复制按钮
    [self.view addSubview:self.copyBtn];
    [self.copyBtn addTarget:self action:@selector(copyButtonTapped) forControlEvents:UIControlEventTouchUpInside];

    // 设置约束
    UIView *topView = self.packageSwitchContainer ?: self.view;
    CGFloat topOffset = self.packageSwitchContainer ? 0 : (self.view.frame.size.height > 0 ? 100 : 100);

    [self.carrierNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(topView.mas_bottom).offset(topOffset);
//        if (self.packageSwitchContainer) {
//            make.top.equalTo(self.packageSwitchContainer.mas_bottom).offset(20);
//        } else {
//            if (@available(iOS 11.0, *)) {
//                make.top.equalTo(self.view.mas_safeAreaLayoutGuide).offset(20);
//            } else {
//                make.top.equalTo(self.view).offset(84);
//            }
//        }
        make.left.equalTo(self.view).offset(18);
        make.height.mas_equalTo(22);
    }];

    [self.shipNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(18);
        make.height.mas_equalTo(22);
        make.top.equalTo(self.carrierNameLabel.mas_bottom).offset(8);
    }];

    [self.copyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.view).offset(-18);
        make.size.mas_equalTo(CGSizeMake(48, 26));
        make.centerY.equalTo(self.shipNumLabel);
    }];
}

- (void)setupTableView {
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.shipNumLabel.mas_bottom).offset(20);
        make.left.right.bottom.equalTo(self.view);
    }];
    [self.tableView registerClass:[ORDERTrackCell class] forCellReuseIdentifier:@"ORDERTrackCell"];
}

#pragma mark - Actions

- (void)packageButtonTapped:(UIButton *)sender {
    [self selectPackageAtIndex:sender.tag];
}

- (void)selectPackageAtIndex:(NSInteger)index {
    if (index < 0 || index >= self.trackDataList.count) return;

    self.currentPackageIndex = index;

    // 更新按钮状态
    for (NSInteger i = 0; i < self.packageButtons.count; i++) {
        UIButton *button = self.packageButtons[i];
        button.selected = (i == index);
        if (button.selected) {
            [button setBackgroundColor:[UIColor colorWithRed:1.0 green:0.5 blue:0.0 alpha:1.0]];
        } else {
            [button setBackgroundColor:[UIColor clearColor]];
        }
    }

    // 更新包裹信息和表格数据
    [self updatePackageInfo];
    [self.tableView reloadData];
}

- (void)copyButtonTapped {
    if (self.currentPackageIndex < self.trackDataList.count) {
        ORDERListItemShipmentOrderModel *currentPackage = self.trackDataList[self.currentPackageIndex];
        NSString *waybillNum = currentPackage.waybillNum ?: @"";

        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = waybillNum;

        // 显示复制成功提示（这里可以根据项目中的提示组件进行调整）
        NSLog(@"运单号已复制: %@", waybillNum);
    }
}

- (void)updatePackageInfo {
    if (self.currentPackageIndex < self.trackDataList.count) {
        ORDERListItemShipmentOrderModel *currentPackage = self.trackDataList[self.currentPackageIndex];
        self.carrierNameLabel.text = currentPackage.carrierName ?: @"";
        self.shipNumLabel.text = currentPackage.waybillNum ?: @"";
    }
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.currentPackageIndex < self.trackDataList.count) {
        ORDERListItemShipmentOrderModel *currentPackage = self.trackDataList[self.currentPackageIndex];
        return currentPackage.orderUms.count;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    ORDERTrackCell *cell = [tableView dequeueReusableCellWithIdentifier:@"ORDERTrackCell" forIndexPath:indexPath];

    if (self.currentPackageIndex < self.trackDataList.count) {
        ORDERListItemShipmentOrderModel *currentPackage = self.trackDataList[self.currentPackageIndex];
        NSArray<ORDERListItemUMSModel *> *trackList = currentPackage.orderUms;

        if (indexPath.row < trackList.count) {
            ORDERListItemUMSModel *model = trackList[indexPath.row];
            BOOL isFirst = (indexPath.row == 0);
            BOOL isLast = (indexPath.row == trackList.count - 1);

            [cell configWithModel:model isFirst:isFirst isLast:isLast];
        }
    }

    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 根据内容动态计算高度，这里先返回固定高度
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 80;
}

#pragma mark - Getters
- (UILabel *)carrierNameLabel {
    if (!_carrierNameLabel) {
        _carrierNameLabel = [[UILabel alloc] init];
        _carrierNameLabel.font = [UIFont systemFontOfSize:16];
        _carrierNameLabel.textColor = [UIColor blackColor];
    }
    return _carrierNameLabel;
}

- (UILabel *)shipNumLabel {
    if (!_shipNumLabel) {
        _shipNumLabel = [[UILabel alloc] init];
        _shipNumLabel.font = [UIFont systemFontOfSize:16];
        _shipNumLabel.textColor = [UIColor blackColor];
    }
    return _shipNumLabel;
}

- (UIButton *)copyBtn {
    if (!_copyBtn) {
        _copyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_copyBtn setTitle:@"复制" forState:UIControlStateNormal];
        [_copyBtn setTitleColor:[UIColor colorWithRed:0.4 green:0.4 blue:0.4 alpha:1.0] forState:UIControlStateNormal];
        _copyBtn.titleLabel.font = [UIFont systemFontOfSize:14];
        _copyBtn.layer.borderWidth = 1.0;
        _copyBtn.layer.borderColor = [UIColor colorWithRed:0.8 green:0.8 blue:0.8 alpha:1.0].CGColor;
        _copyBtn.layer.cornerRadius = 4;
        _copyBtn.backgroundColor = [UIColor whiteColor];
    }
    return _copyBtn;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.showsVerticalScrollIndicator = YES;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 80;

        // 设置内容边距
        if (@available(iOS 11.0, *)) {
            _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
    }
    return _tableView;
}

@end
