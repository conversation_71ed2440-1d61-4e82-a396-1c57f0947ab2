//
//  ORDERTrackCell.m
//  JDTOrderListModule
//
//  Created by lvchenzhu.1 on 2025/6/24.
//

#import "ORDERTrackCell.h"
#import <JDTCommonToolModule/ORDERListModel.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>

@interface ORDERTrackCell ()

/// 时间轴圆点
@property (nonatomic, strong) UIView *timelineDot;
/// 时间轴线条（上）
@property (nonatomic, strong) UIView *timelineTopLine;
/// 时间轴线条（下）
@property (nonatomic, strong) UIView *timelineBottomLine;
/// 内容标签
@property (nonatomic, strong) UILabel *contentLabel;
/// 时间标签
@property (nonatomic, strong) UILabel *timeLabel;

@end

@implementation ORDERTrackCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor whiteColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    // 时间轴上线条
    self.timelineTopLine = [[UIView alloc] init];
    self.timelineTopLine.backgroundColor = [UIColor lightGrayColor];
    [self.contentView addSubview:self.timelineTopLine];

    // 时间轴圆点
    self.timelineDot = [[UIView alloc] init];
    self.timelineDot.backgroundColor = [UIColor lightGrayColor];
    self.timelineDot.layer.cornerRadius = 4;
    self.timelineDot.layer.masksToBounds = YES;
    [self.contentView addSubview:self.timelineDot];

    // 时间轴下线条
    self.timelineBottomLine = [[UIView alloc] init];
    self.timelineBottomLine.backgroundColor = [UIColor lightGrayColor];
    [self.contentView addSubview:self.timelineBottomLine];

    // 内容标签
    self.contentLabel = [[UILabel alloc] init];
    self.contentLabel.font = [UIFont systemFontOfSize:15];
    self.contentLabel.textColor = [UIColor blackColor];
    self.contentLabel.numberOfLines = 0;
    self.contentLabel.lineBreakMode = NSLineBreakByWordWrapping;
    [self.contentView addSubview:self.contentLabel];

    // 时间标签
    self.timeLabel = [[UILabel alloc] init];
    self.timeLabel.font = [UIFont systemFontOfSize:13];
    self.timeLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    [self.contentView addSubview:self.timeLabel];

    [self setupConstraints];
}

- (void)setupConstraints {
    // 时间轴上线条
    [self.timelineTopLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.timelineDot);
        make.top.equalTo(self.contentView);
        make.bottom.equalTo(self.timelineDot.mas_top);
        make.width.mas_equalTo(2);
    }];

    // 时间轴圆点
    [self.timelineDot mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(24);
        make.top.equalTo(self.contentView).offset(18);
        make.size.mas_equalTo(CGSizeMake(8, 8));
    }];

    // 时间轴下线条
    [self.timelineBottomLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.timelineDot);
        make.top.equalTo(self.timelineDot.mas_bottom);
        make.bottom.equalTo(self.contentView);
        make.width.mas_equalTo(2);
    }];

    // 内容标签
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.timelineDot.mas_right).offset(16);
        make.top.equalTo(self.contentView).offset(12);
        make.right.equalTo(self.contentView).offset(-20);
    }];

    // 时间标签
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentLabel);
        make.top.equalTo(self.contentLabel.mas_bottom).offset(8);
        make.right.equalTo(self.contentView).offset(-20);
        make.bottom.equalTo(self.contentView).offset(-15);
    }];
}

- (void)configWithModel:(ORDERListItemUMSModel *)model isFirst:(BOOL)isFirst isLast:(BOOL)isLast {
    // 设置内容
    self.contentLabel.text = model.content ?: @"";
    self.timeLabel.text = model.msgTime ?: @"";

    // 设置时间轴样式
    if (isFirst) {
        // 第一个（最新的）使用橙色圆点，加粗文字
        self.timelineDot.backgroundColor = [UIColor colorWithRed:1.0 green:0.5 blue:0.0 alpha:1.0]; // 橙色
        self.contentLabel.font = [UIFont boldSystemFontOfSize:15];
        self.contentLabel.textColor = [UIColor blackColor];
        self.timelineTopLine.hidden = YES;
    } else {
        // 其他使用灰色圆点，普通文字
        self.timelineDot.backgroundColor = [UIColor colorWithRed:0.8 green:0.8 blue:0.8 alpha:1.0];
        self.contentLabel.font = [UIFont systemFontOfSize:15];
        self.contentLabel.textColor = [UIColor colorWithRed:0.3 green:0.3 blue:0.3 alpha:1.0];
        self.timelineTopLine.hidden = NO;
    }

    // 设置时间轴线条颜色
    UIColor *lineColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0];
    self.timelineTopLine.backgroundColor = lineColor;
    self.timelineBottomLine.backgroundColor = lineColor;

    // 最后一个隐藏下方线条
    if (isLast) {
        self.timelineBottomLine.hidden = YES;
    } else {
        self.timelineBottomLine.hidden = NO;
    }
}

@end
