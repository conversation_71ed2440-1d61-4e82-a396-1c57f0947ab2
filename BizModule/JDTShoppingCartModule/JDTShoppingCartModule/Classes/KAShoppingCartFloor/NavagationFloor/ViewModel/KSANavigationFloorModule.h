//
//  KSANavigationFloorModule.h
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by cdwutao3 on 2023/5/25.
//

#import <Foundation/Foundation.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN

@interface KSANavigationFloorModule : NSObject<JDISVFloorModuleProtocol>

@property (nonatomic, assign) BOOL optionEnable;

@property (nonatomic, assign, getter=isEdit) BOOL edit;

@property (nonatomic, assign)BOOL isNodata;

@property (nonatomic, copy, readonly) NSArray <JDTAddressItemModel *> *addressList;

- (void)editStatusChange;

- (void)resetEditStatus;

@end

NS_ASSUME_NONNULL_END
