//
//  KSANavigationFloorModule.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by cdwutao3 on 2023/5/25.
//

#import "KSANavigationFloorModule.h"
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
#import <JDISVYYModelModule/YYModel.h>

#import "JDISVShoppingCartTool.h"
#import "KSAShoppingCartNavigationFloor.h"

JDISVRegisterFloorModule(KSACartNavBarFloor, KSANavigationFloorModule)

@interface KSANavigationFloorModule ()

@property (nonatomic, weak) JDISVFloorCommonModel *commonModel;

@property (nonatomic, copy) NSArray <JDTAddressItemModel *> *addressList;

@end


@implementation KSANavigationFloorModule


- (UIView *)floorView {
    KSAShoppingCartNavigationFloor *navigationBar = [[KSAShoppingCartNavigationFloor alloc] init];
    return navigationBar;
}

- (CGFloat)floorHeight {
//    CGFloat height = [UIWindow isv_shoppingCart_navigationHeight] + 50.;
    CGFloat height = [UIWindow isv_shoppingCart_navigationHeight];
//    没有数据不展示地址
    if(!self.isNodata){
        height +=44;
    }
    return height;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeTopFixFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    
    self.commonModel = commonModel;
    
    self.addressList = data[@"data"][@"addressList"];
    
    BOOL edit = [self.commonModel.commonData[@"edit"] boolValue];
    self.edit = edit;
    
    BOOL optionEnable = YES;
    if (data[@"data"][@"optionEnable"]){
        optionEnable = [data[@"data"][@"optionEnable"] boolValue];
    }else{
        optionEnable = YES;
    }
    NSDictionary* ext = [data jdcd_getDicElementForKey:@"ext"];
    NSNumber* isNoData =[ext jdcd_getNumberElementForKey:@"Nodata"];
    self.isNodata = isNoData.boolValue;
    
    self.optionEnable = optionEnable;
}

- (void)editStatusChange {
    BOOL edit = [self.commonModel.commonData[@"edit"] boolValue];
    edit = !edit;
    self.edit = edit;
    
    self.commonModel.commonData[@"editModeSkus"] = [NSMutableArray array];
    self.commonModel.commonData[@"editModeGroups"] = [NSMutableArray array];
    
    [self.commonModel.commonData setObject:@(edit) forKey:@"edit"];
    [self.commonModel commonDataChange];
}

- (void)resetEditStatus {
    self.edit = NO;
    [self.commonModel.commonData setObject:@(NO) forKey:@"edit"];
    [self.commonModel commonDataChange];
}
@end
