//
//  KSAShoppingCartNavigationFloor.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by cdwutao3 on 2023/5/25.
//

#import "KSAShoppingCartNavigationFloor.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/KANavigationBar.h>
#import <JDISVKAUIKitModule/KANavigationBarDecorator.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVKAIconFontModule/UIImageView+KATheme.h>
#import <JDISVKAUIKitModule/KAFloatLayerPresentationController.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>

#import "KSANavigationFloorModule.h"
#import "JDISVShoppingCartTool.h"
#import "JDISVShoppingCarPub.h"
#import "CARTSwitchAddressController.h"

JDCDISVActionType const KSAShopCartNavigationBarBackAction = @"KSAShopCartNavigationBarBackAction";
JDCDISVActionType const KSAShopCartNavigationBarAddressAction = @"KSAShopCartNavigationBarAddressAction";
JDCDISVActionType const KSAShoppingCartEditProductAction = @"KSAShoppingCartEditProductAction";

@interface KSAShoppingCartNavigationFloor ()

@property (nonatomic, strong) KSANavigationFloorModule *viewModel;

@property (nonatomic, strong) KANavigationBar *navigationBar;

@property (nonatomic, strong) UIView *addressView;
@property (nonatomic, strong) UIView *addressBackgroudView;

@property (nonatomic, strong) UIImageView *addressImageView;

@property (nonatomic, strong) UILabel *addressLabel;
@property (nonatomic, strong) UILabel *addressLabelSub;

@property (nonatomic, strong) UIButton *editItem;

@property (nonatomic, strong) UIView *globalSearchView;

@end

@implementation KSAShoppingCartNavigationFloor


- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        [self setUpUI];
    }
    return self;
}

- (void)setUpUI {
    [self addSubview:self.navigationBar];
    [self.navigationBar mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.edges.mas_equalTo(self);
    }];
    CGFloat statusHeight = [UIWindow isv_shoppingCart_safeAreaInsets].top;
    [self.navigationBar.navigationItem mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.navigationBar).mas_offset(statusHeight);
        make.leading.trailing.mas_equalTo(self.navigationBar);
        make.leading.height.mas_equalTo(44);
    }];
    
    
    self.navigationBar
        .decorator
        .addBackground(^(KANavigationBarBackgrounItem * _Nonnull item) {
            item.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        }).title(ShopingCartL(@"ka_cart_shopping_cart") ,NSTextAlignmentCenter)
        .render();
    
    
    //    [self leftBarButtonItemWithShowBack:YES];
    
    [JDRouter openURL:[NSString stringWithFormat:@"router://%@/KSAGlobalSearchView",@"KSAGlobalSearchModule"] arg:nil error:nil completion:^(UIView * view) {
        if(view){
            self.globalSearchView = view;
            [self.navigationBar addSubview:view];
            [view mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.navigationBar);
                make.trailing.mas_equalTo(self.navigationBar);
                make.bottom.mas_equalTo(self.navigationBar).offset(-45);
                make.height.mas_equalTo(50.);
            }];
        }
    }];
    
    self.editItem = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.navigationBar addSubview:self.editItem];
    [self.editItem mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(44);
        make.trailing.mas_equalTo(self.navigationBar).mas_offset(-12);
        make.bottom.mas_equalTo(self.navigationBar);
    }];
    self.editItem.titleLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C10-j");
    self.editItem.titleLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    UIColor * C7 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    [self.editItem setTitleColor:C7 forState:UIControlStateNormal];
    [self.editItem addTarget:self action:@selector(tapEdit) forControlEvents:UIControlEventTouchUpInside];
    
    [self.addressView addSubview:self.addressBackgroudView];
    
    [self.addressBackgroudView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.mas_equalTo(self.addressView);
        make.trailing.mas_equalTo(self.addressView.mas_trailing).mas_lessThanOrEqualTo(0);
    }];
    
    
    [self.addressBackgroudView addSubview:self.addressImageView];
    [self.addressBackgroudView addSubview:self.addressLabel];
    [self.addressBackgroudView addSubview:self.addressLabelSub];
    [self.addressImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(12);
        make.width.height.mas_equalTo(12);
        make.centerY.mas_equalTo(self.addressView.mas_centerY);
    }];
    
    [self.addressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.addressImageView.mas_trailing).mas_offset(6);
        make.centerY.mas_equalTo(self.addressView.mas_centerY);
        //        make.trailing.mas_equalTo(self.addressBackgroudView.mas_trailing).mas_offset(-12);
    }];
    
    [self.addressLabelSub mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.addressLabel.mas_trailing);
        make.centerY.mas_equalTo(self.addressView.mas_centerY);
        make.trailing.mas_equalTo(self.addressBackgroudView.mas_trailing).mas_offset(-5);
    }];
    
    self.navigationBar.navigationItem.titleViewLayoutType = KANavigationBarTitleViewLayoutTypeAuto;
    
    [self.navigationBar addSubview:self.addressView];
    [self.addressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(44);
        make.bottom.mas_equalTo(self.navigationBar.mas_bottom);
        make.leading.mas_equalTo(self.navigationBar).mas_offset(6);
        make.trailing.mas_equalTo(self.editItem.mas_leading).mas_offset(-5);
    }];
    
    NSString* editItemTitle = self.viewModel.isEdit ? ShopingCartL(@"ka_cart_edit_done") : ShopingCartL(@"ka_cart_edit");
    [self updateAddressConstraints:editItemTitle];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(addressDidSelected)];
    [self.addressView addGestureRecognizer:tap];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginSuccess) name:ISVLoginedNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginOut) name:ISVLogoutNotification object:nil];
    
    
}

- (void)updateAddressConstraints:(NSString *)editItemTitle{
    
    CGSize size =  [editItemTitle jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(200, 200)];
    [self.addressView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(44);
        make.bottom.mas_equalTo(self.navigationBar.mas_bottom);
        make.leading.mas_equalTo(self.navigationBar).mas_offset(6);
        make.trailing.mas_equalTo(self.navigationBar).mas_offset(-(size.width+17));
    }];
    
}

-(void)tapEdit{
    NSString* title = [self.editItem titleForState:UIControlStateNormal];
    
    NSString* changeTitle = [title isEqualToString:ShopingCartL(@"ka_cart_edit")] ? ShopingCartL(@"ka_cart_edit_done") : ShopingCartL(@"ka_cart_edit");
    [self.editItem setTitle:changeTitle forState:UIControlStateNormal];
    
    [self updateAddressConstraints:changeTitle];
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:KSAShoppingCartEditProductAction];
    [self isv_sendAction:action];
}

- (void)loginSuccess {
    [self.viewModel resetEditStatus];
}

- (void)loginOut {
    [self.viewModel resetEditStatus];
}

- (void)leftBarButtonItemWithShowBack:(BOOL)showBack {
    KANavigationBarButtonStandardItem *backItem = [[KANavigationBarButtonStandardItem alloc] initWithFrame:CGRectMake(0, 0, 24, 24) type:KANavigationBarButtonItemTypeBack];
    backItem.action = ^(KANavigationBarButtonStandardItem * _Nonnull sender) {
        JDCDISVAction *action = [JDCDISVAction actionWithType:KSAShopCartNavigationBarBackAction];
        [self isv_sendAction:action];
    };
    if (showBack) {
        self.navigationBar.navigationItem.leftBarButtonItems = @[backItem];
    }else{
        
    }
}

- (void)addressDidSelected {
    if(self.viewModel.edit)
        return;
    JDCDISVAction *action = [JDCDISVAction actionWithType:KSAShopCartNavigationBarAddressAction];
    [self isv_sendAction:action];
}

- (void)viewWillAppear {
    [self.viewModel resetEditStatus];
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    BOOL isTop = [self isTopController];
    [self leftBarButtonItemWithShowBack:!isTop];
    
    self.viewModel = floorModel;
    
    NSString * addressStr = [JDISVShoppingCartTool addressDecs];
    //分成2个字段去显示 避免阿语翻转问题
    NSString * firstStr = @"";
    NSString * secondStr = @"";
    NSString * prefixStr = [NSString stringWithFormat:@"%@:",ShopingCartL(@"ka_cart_title_address")];
    
    if([addressStr containsString:prefixStr]){
        firstStr = prefixStr;
        secondStr = [addressStr stringByReplacingOccurrencesOfString:prefixStr withString:@""];
    }else{
        firstStr = addressStr;
    }
    self.addressLabel.text = firstStr;
    self.addressLabelSub.text = secondStr;
    
    CGSize firstStrSize =  [firstStr jdcd_getStringSize:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"] constraintsSize:CGSizeMake(200, 200)];
    
    [self.addressLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.addressImageView.mas_trailing).mas_offset(6);
        make.centerY.mas_equalTo(self.addressView.mas_centerY);
        make.width.mas_equalTo(firstStrSize.width+1);
    }];
    [self.addressLabelSub mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.addressLabel.mas_trailing);
        make.centerY.mas_equalTo(self.addressView.mas_centerY);
        make.trailing.mas_equalTo(self.addressBackgroudView.mas_trailing).mas_offset(-5);
    }];
    
    [self.editItem setTitle:self.viewModel.isEdit ? ShopingCartL(@"ka_cart_edit_done") : ShopingCartL(@"ka_cart_edit") forState:UIControlStateNormal];
    BOOL showBtn = NO;
    if(self.viewModel.optionEnable){
        showBtn =YES;
    }
    if(showBtn){
        self.editItem.hidden = NO;
        self.addressView.hidden = NO;
        [self.globalSearchView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.navigationBar);
            make.trailing.mas_equalTo(self.navigationBar);
            make.bottom.mas_equalTo(self.navigationBar).offset(-45);
            make.height.mas_equalTo(50.);
        }];
    }else{
        self.editItem.hidden = YES;
        self.addressView.hidden = YES;
        [self.globalSearchView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.navigationBar);
            make.trailing.mas_equalTo(self.navigationBar);
            make.bottom.mas_equalTo(self.navigationBar).offset(0);
            make.height.mas_equalTo(50.);
        }];
    }
}

#pragma mark - JDCDISVActionTransferProtocol
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:KSAShopCartNavigationBarBackAction]) {
        [controller.navigationController popViewControllerAnimated:YES];
        return YES;
    }else if ([action.actionType isEqualToString:KSAShopCartNavigationBarAddressAction]){
//        NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeAddress];
//        NSString *router = [NSString stringWithFormat:@"router://%@/cascaderAddressViewController",moduleName];
//        
//        NSMutableDictionary *params = [NSMutableDictionary dictionary];
//        JDTAddressItemModel *address = [PlatformService getDefaultAddress];
//        if (params) {
//            [params setObject:address forKey:@"address"];
//        }
//        UIViewController *cascaderController  = [JDRouter openURL:router arg:params error:nil completion:^(JDTAddressItemModel *object) {
//            [PlatformService setDefaultAddress:object];
//            //重新请求数据
//            JDCDISVAction *action = [JDCDISVAction actionWithType:kJDCDISVFloorReloadDataAction];
//            [self isv_sendAction:action];
//        }];
        
        CARTSwitchAddressController *switchAddrController = [[CARTSwitchAddressController alloc] init];
        switchAddrController.addressList = self.viewModel.addressList;
        
        KAFloatLayerPresentationController *delegate = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:switchAddrController presentingViewController:controller];
        delegate.contentHeight = UIScreen.mainScreen.bounds.size.height-90;
        delegate.defultHeader.titleLabel.text = ShopingCartL(@"ka_cart_title_address");
        switchAddrController.transitioningDelegate = delegate;
        [controller presentViewController:switchAddrController animated:YES completion:nil];
        return YES;
    }else if([action.actionType isEqualToString:KSAShoppingCartEditProductAction]){
        [self.viewModel editStatusChange];
        if(!self.viewModel.edit){
            JDCDISVAction *action = [JDCDISVAction actionWithType:kJDCDISVFloorReloadDataAction];
            [self isv_sendAction:action];
        }else{
            JDCDISVAction *action = [JDCDISVAction actionWithType:kJDCDISVFloorUpdateViewAction];
            [self isv_sendAction:action];
        }
        return YES;
    }
    return NO;
}

#pragma mark - private
- (BOOL)isTopController {
//    BOOL result = NO;
//    if ([self currentController].navigationController.viewControllers.count == 1) {
//        result = YES;
//    }
//    return result;
    UIViewController* vc = [self currentController];
    if(vc.navigationController.viewControllers.count == 1){
        return YES;
    }
    if([vc.navigationController.viewControllers[0] isEqual:vc]){
        return YES;
    }
    return NO;
}

- (nullable UIViewController *)currentController {
    UIResponder *next = self.nextResponder;
    do {
        if ([next isKindOfClass:UIViewController.class]) {
            return (UIViewController *)next;
        }
        next = next.nextResponder;
    } while (next != nil);
    return nil;
}

#pragma mark - getter
- (KANavigationBar *)navigationBar {
    if (!_navigationBar) {
        _navigationBar = [[KANavigationBar alloc] initWithFrame:CGRectZero];
    }
    return _navigationBar;
}

- (UIView *)addressView {
    if (!_addressView) {
        _addressView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 100, 24)];
        _addressView.layer.cornerRadius = 12;
        _addressView.layer.masksToBounds = YES;
        _addressView.backgroundColor = UIColor.clearColor;
    }
    return _addressView;
}
- (UIView *)addressBackgroudView {
    if (!_addressBackgroudView) {
        _addressBackgroudView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 100, 24)];
        _addressBackgroudView.layer.cornerRadius = 12;
        _addressBackgroudView.layer.masksToBounds = YES;
        _addressBackgroudView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1" alpha:0.2];
        
    }
    return _addressBackgroudView;
}

- (UIImageView *)addressImageView {
    if (!_addressImageView) {
        _addressImageView = [[UIImageView alloc] init];
        _addressImageView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C10-j", JDIF_ICON_LOCATION_LINE_SMALL, CGSizeMake(12, 12));
    }
    return _addressImageView;
}

- (UILabel *)addressLabel {
    if (!_addressLabel) {
        _addressLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _addressLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _addressLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _addressLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _addressLabel;
}

- (UILabel *)addressLabelSub {
    if (!_addressLabelSub) {
        _addressLabelSub = [[UILabel alloc] initWithFrame:CGRectZero];
        _addressLabelSub.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _addressLabelSub.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _addressLabelSub.textAlignment = NSTextAlignmentLeft;
    }
    return _addressLabelSub;
}
@end
