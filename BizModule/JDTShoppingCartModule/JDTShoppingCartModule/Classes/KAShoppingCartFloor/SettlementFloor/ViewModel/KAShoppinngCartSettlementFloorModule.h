//
//  KAShoppinngCartSettlementFloorModule.h
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/23.
//  Copyright © 2021 罗静. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

@protocol JDISVFloorViewProtocol;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, KAShoppingCartSettlementBarSelectedStatus) {
    KAShoppingCartSettlementBarSelectedStatusUnSelected, ///< 未选中
    KAShoppingCartSettlementBarSelectedStatusSelected, ///< 选中
    KAShoppingCartSettlementBarSelectedStatusDisenable, ///< 没有选中能力
};

@interface KAShoppinngCartSettlementFloorModule : NSObject<JDISVFloorModuleProtocol>

@property (weak,nonatomic) NSObject<JDISVFloorViewProtocol>* cell;

@property (nonatomic, assign) KAShoppingCartSettlementBarSelectedStatus selectedStatus;

@property (nonatomic, copy) NSString *price;

@property (nonatomic, copy) NSString *discount;

@property (nonatomic, strong) NSArray *productParams;
@property (nonatomic, strong) NSArray *promotionParams;
@property (nonatomic, assign) NSInteger cheapBuyNum;
@property (nonatomic, strong) NSArray* groupBySkus;

@property (nonatomic, assign, getter=isEdit) BOOL edit;

//@property (nonatomic, assign) BOOL featureJHGroup;

@property (nonatomic, strong) NSArray* details;

@property (nonatomic,assign) NSInteger kindNum;
@property (nonatomic,assign) NSInteger selectKindNum;
@property (nonatomic,assign) NSInteger productNum;
@property (nonatomic,assign) NSInteger selectProductNum;

//-(NSInteger)getShowNum;

- (RACSignal*)jhGroupCanBuy;

- (JDCDISVAction *)selectAction;

- (void)userSelectAll:(NSString*)selected;

- (JDCDISVAction *)deleteAction;

- (JDCDISVAction *)collectionAction;

@end

NS_ASSUME_NONNULL_END
