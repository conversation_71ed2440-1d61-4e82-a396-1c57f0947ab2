//
//  KSAUICheckOutBottomBar.m
//  JDISVShoppingCartSDKModule
//
//  Created by cdwutao3 on 2023/8/30.
//

#import "KSAUICheckOutBottomBar.h"

#import "KSAUICheckOutBottomBar.h"
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>

#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/UIButton+JDISVTheme.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVReactiveObjCModule/JDISVReactiveObjCModule-umbrella.h>

#import <JDISVMasonryModule/Masonry.h>

#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>

@interface KSAUICheckOutBottomBar ()

@property (strong, nonatomic) UIButton *selectAllBtn;
@property (strong, nonatomic) UILabel *selectAllLabel;

@property (strong, nonatomic) UILabel *labelTotal;
//@property (strong, nonatomic) UILabel *labelDetailInfo;
//@property (strong, nonatomic) UILabel *labelDetail;
//@property (strong, nonatomic) UIImageView* detailArr;

@property (strong, nonatomic) UIButton *settleBtn;

//@property(strong,nonatomic) UIButton* couponBtn;
@property (nonatomic, strong) UIView *lineView;
@end

@implementation KSAUICheckOutBottomBar

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self configUI];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self configUI];
    }
    return self;
}

- (void)configUI {
    
    UIButton *B7Button1 = [[UIButton alloc] initWithFrame:CGRectMake(12, 15, 20, 20)];
    [B7Button1 renderB7];
    B7Button1.enabled = YES;
    B7Button1.jdisv_selected_B7 = @(0);
    [B7Button1 addTarget:self action:@selector(touchUpInsideAction1) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:B7Button1];
    self.selectAllBtn = B7Button1;
    [self.selectAllBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(12);
        make.top.mas_equalTo(15);
        make.height.width.mas_equalTo(20);
    }];
    
    UILabel *selectTipLabel = [[UILabel alloc] initWithFrame:CGRectMake(38, 17, 30, 18)];
    selectTipLabel.text = ShopingCartL(@"ka_cart_selected_all");
    selectTipLabel.textAlignment = NSTextAlignmentLeft;
    selectTipLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    selectTipLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    [self addSubview:selectTipLabel];
    self.selectAllLabel = selectTipLabel;
    
    CGSize size = [selectTipLabel.text jdcd_getStringSize:selectTipLabel.font  constraintsSize:CGSizeMake(100, 28)];
    [self.selectAllLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(17);
        make.leading.mas_equalTo(38);
        make.width.mas_equalTo(size.width+1);
    }];
    
    [self.selectAllLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.selectAllLabel setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.selectAllLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.selectAllBtn.mas_trailing).mas_offset(4);
        make.top.mas_equalTo(17);
        make.height.mas_equalTo(18);
    }];
    
    UIButton *B3Button2 = [[UIButton alloc] initWithFrame:CGRectMake(self.bounds.size.width - 110 - 12, 5, 110, 40)];
    [B3Button2 renderB2];
    [self configBtn:B3Button2 title:ShopingCartL(@"ka_cart_to_settle")];
    B3Button2.enabled = YES;
    [B3Button2 addTarget:self action:@selector(touchUpInsideAction2) forControlEvents:UIControlEventTouchUpInside];
    self.settleBtn = B3Button2;
    
    UILabel *label1 = [[UILabel alloc] initWithFrame:CGRectMake(12 + 20 + 5, 9, self.bounds.size.width - 12 - 20 - 5 - 5 - 110 - 12, 18)];
    
    label1.textAlignment = NSTextAlignmentRight;
    if (@available(iOS 8.2, *)) {
        label1.font = [UIFont systemFontOfSize:18.f weight:UIFontWeightBold];
    } else {
        label1.font = [UIFont systemFontOfSize:18.f];
    }
    [self addSubview:label1];
    self.labelTotal = label1;
    
    [self.labelTotal mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.selectAllLabel.mas_trailing).mas_offset(5);
        make.top.mas_equalTo(9);
        make.trailing.mas_offset(-12);
        make.height.mas_equalTo(18);
    }];
    [self.labelTotal setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.labelTotal setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    
    
//    self.labelDetail = [[UILabel alloc] initWithFrame:CGRectZero];
//    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
//    UIFont *discontFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
//    self.labelDetail.font = discontFont;
//    self.labelDetail.textColor = color;
//    self.labelDetail.text = ShopingCartL(@"ka_cart_checkout_detail");
//    [self addSubview:self.labelDetail];
//    [self.labelDetail mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.mas_equalTo(28);
//        make.trailing.mas_offset(-12-13);
//        make.height.mas_equalTo(16);
//    }];
//    [self.labelDetail setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
//    [self.labelDetail setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
//    UIColor* c12 = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C12"];
//    UIImage* arrImg = [UIImage ka_iconWithName:JDIF_ICON_ARROW_DOWN_SMALL imageSize:CGSizeMake(10, 10) color:c12];
//    self.detailArr = [[UIImageView alloc] initWithImage:arrImg];
//    [self addSubview:self.detailArr];
//    [self.detailArr mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.trailing.mas_offset(-12);
//        make.centerY.mas_equalTo(self.labelDetail).mas_offset(2);
//        make.width.mas_equalTo(10);
//        make.height.mas_equalTo(10);
//    }];
    
    [self addSubview:self.lineView];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.mas_equalTo(0);
        make.height.mas_equalTo(1);
    }];
    
//    [self addSubview:self.couponBtn];
//    [self.couponBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.leading.mas_equalTo(self.labelDetail.mas_leading);
//        make.trailing.mas_equalTo(self.detailArr.mas_trailing);
//        make.height.mas_equalTo(self.labelDetail.mas_height);
//        make.centerY.mas_equalTo(self.labelDetail.mas_centerY);
//    }];
    
//    UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectMake(12 + 20 + 5, 28, self.bounds.size.width - 12 - 20 - 5 - 5 - 110 - 12, 16)];
//    
//    label2.textAlignment = NSTextAlignmentRight;
//    if (@available(iOS 8.2, *)) {
//        label2.font = [UIFont systemFontOfSize:12.f weight:UIFontWeightRegular];
//    } else {
//        // Fallback on earlier versions
//    }
//    [self addSubview:label2];
//    self.labelDetailInfo = label2;
//    [self.labelDetailInfo mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.leading.mas_equalTo(self.selectAllLabel.mas_trailing).mas_offset(5);
//        make.top.mas_equalTo(28);
//        make.trailing.mas_equalTo(self.labelDetail.mas_leading).mas_offset(-3);
//        make.height.mas_equalTo(16);
//    }];
}

- (void)configSelectTipLabel:(NSAttributedString *)text {
    self.selectAllLabel.attributedText = text;
}

- (void)configLabel1:(NSAttributedString *)text {
    // Original code
    // self.labelTotal.attributedText = text;
    
    // New code
    @try {
        // 确保 UI 操作在主线程中执行 / Ensure that UI operations are performed on the main thread.
        dispatch_async(dispatch_get_main_queue(), ^{
            // 使用 strong 引用确保 self.labelTotal 不会被释放 / Use strong references to ensure that self.labelTotal will not be deallocated.
            __strong UILabel *strongLabelTotal = self.labelTotal;
            if (strongLabelTotal) {
                // 使用 copy 确保 text 不会被释放 / Use copy to ensure that text will not be deallocated.
                strongLabelTotal.attributedText = [text copy];
            }
        });
    } @catch (NSException *exception) {
        NSLog(@"Exception in configLabel1: %@", exception);
    }
}

//- (void)configLabel2:(NSAttributedString *)text {
//    self.labelDetailInfo.attributedText = text;
//}
//- (void)configLabel2:(NSAttributedString *)text hideImg:(BOOL)hideImg{
//    //WT TODO
//    //    hideImg = NO;
//    self.labelDetailInfo.attributedText = text;
//    if(hideImg){
//        [self.labelDetailInfo mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.selectAllLabel.mas_trailing).mas_offset(5);
//            make.top.mas_equalTo(28);
//            make.trailing.mas_equalTo(self.settleBtn.mas_leading).mas_offset(-12);
//            make.height.mas_equalTo(16);
//        }];
//        self.labelDetailInfo.hidden = YES;
//        self.detailArr.hidden = YES;
//        self.labelDetail.hidden = YES;
//        self.couponBtn.hidden = YES;
//    }else{
//        self.labelDetailInfo.hidden = NO;
//        self.detailArr.hidden = NO;
//        self.labelDetail.hidden = NO;
//        self.couponBtn.hidden = NO;
//        [self.labelDetailInfo mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.selectAllLabel.mas_trailing).mas_offset(5);
//            make.top.mas_equalTo(28);
//            make.trailing.mas_equalTo(self.labelDetail.mas_leading).mas_offset(-3);
//            make.height.mas_equalTo(16);
//        }];
//    }
//    
//}
- (void)configButton:(NSString *)title {
    if(!self.settleBtn.superview){
        [self.superview addSubview:self.settleBtn];
    }
    [self configBtn:self.settleBtn title:title];
    //    CGSize size = [self.B3Button2 jdcd_getSizeOfWidth:200 height:40];
    //    CGFloat width = (size.width+24)<110?110:size.width+24;
    [self.settleBtn mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(40);
        //        make.top.mas_equalTo(5);
        make.leading.mas_offset(18);
        make.trailing.mas_offset(-18);
        make.bottom. mas_offset(-12);
    }];
}

- (void)configAllButtonCornerRadius:(CGFloat)cornerRadius {
    
    @weakify(self)
    [RACObserve(self.settleBtn,frame) subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        self.settleBtn.layer.cornerRadius = cornerRadius;
        self.settleBtn.layer.masksToBounds = YES;
    }];
}

- (void)touchUpInsideAction1 {
    if (self.button1Action) {
        self.button1Action();
    }
}

- (void)touchUpInsideAction2 {
    if (self.button2Action) {
        self.button2Action();
    }
}

- (void)tapCoupon{
    if(self.couponAction){
        self.couponAction();
    }
}

- (void)configBtn:(UIButton *)button title:(NSString *)title {
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitle:title forState:UIControlStateHighlighted];
    [button setTitle:title forState:UIControlStateDisabled];
}

- (void)configSelectState:(KACheckOutBottomBarSelectType)state {
    
    switch (state) {
        case KACheckOutBottomBarSelected:
        {
            self.selectAllBtn.enabled = YES;
            self.selectAllBtn.jdisv_selected_B7 = YES;
        }
            break;
        case KACheckOutBottomBarNotSelected:
        {
            self.selectAllBtn.enabled = YES;
            self.selectAllBtn.jdisv_selected_B7 = NO;
        }
            break;
        case KACheckOutBottomBarDisable:
        {
            self.selectAllBtn.enabled = NO;
        }
            break;
        default:
        {
            self.selectAllBtn.enabled = NO;
        }
            break;
    }
}

- (void)hideLabel1:(BOOL)hide {
    if (hide) {
        self.labelTotal.hidden = YES;
    } else {
        self.labelTotal.hidden = NO;
    }
}

//- (void)hideLabel2:(BOOL)hide {
//    if (hide) {
//        self.labelDetailInfo.hidden = YES;
//    } else {
//        self.labelDetailInfo.hidden = NO;
//    }
//}

- (void)hideRightButton:(BOOL)hide {
    if (hide) {
        self.settleBtn.hidden = YES;
    } else {
        self.settleBtn.hidden = NO;
    }
}

//- (void)hideCoupon:(BOOL)hide{
//    if (hide) {
//        self.couponBtn.hidden = YES;
//    } else {
//        self.couponBtn.hidden = NO;
//    }
//}

//-(void)changeCouponModel:(BOOL)couponModel{
//    if(couponModel){
//        self.detailArr.hidden = NO;
//        self.labelDetail.hidden = NO;
//        self.couponBtn.hidden = NO;
//        self.labelDetailInfo.hidden = NO;
//        [self.labelDetailInfo mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.selectAllLabel.mas_trailing).mas_offset(5);
//            make.top.mas_equalTo(28);
//            make.trailing.mas_equalTo(self.labelDetail.mas_leading).mas_offset(-3);
//            make.height.mas_equalTo(16);
//        }];
//    }else{
//        [self.labelDetailInfo mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.leading.mas_equalTo(self.selectAllLabel.mas_trailing).mas_offset(5);
//            make.top.mas_equalTo(28);
//            make.trailing.mas_equalTo(self.settleBtn.mas_leading).mas_offset(-12);
//            make.height.mas_equalTo(16);
//        }];
//        self.detailArr.hidden = YES;
//        self.labelDetail.hidden = YES;
//        self.labelDetailInfo.hidden = YES;
//        self.couponBtn.hidden = YES;
//    }
//}


- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    }
    return _lineView;
}

//-(UIButton*)couponBtn{
//    if(!_couponBtn){
//        _couponBtn = [UIButton buttonWithType:UIButtonTypeCustom];
//        [_couponBtn addTarget:self action:@selector(tapCoupon) forControlEvents:UIControlEventTouchUpInside];
//    }
//    return _couponBtn;
//}
@end

