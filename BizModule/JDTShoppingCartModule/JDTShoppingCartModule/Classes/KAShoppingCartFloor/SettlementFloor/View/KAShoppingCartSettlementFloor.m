//
//  KAShoppingCartSettlementFloor.m
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/23.
//  Copyright © 2021 罗静. All rights reserved.
//

#import "KAShoppingCartSettlementFloor.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVKAUIKitModule/KACheckOutBottomBar.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/NSMutableAttributedString+KAPirce.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVKAUIKitModule/KAAlert.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDBRouterModule/JDRouter.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>

#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>

#import "KAShoppinngCartSettlementFloorModule.h"
#import "KAShoppingCartCouponDetailView.h"
#import "JDISVShoppingCartMainModel.h"
#import <JDISVCategoryModule/UIButton+extern.h>
#import "KSAUICheckOutBottomBar.h"

JDCDISVActionType const JDISVShoppingCartSettlementAction = @"JDISVShoppingCartSettlementAction";

@interface KAShoppingCartSettlementFloor ()

@property (nonatomic, strong) KAShoppinngCartSettlementFloorModule *viewModel;

@property (nonatomic, strong) KSAUICheckOutBottomBar *bottomBar;

@property (nonatomic, strong) UIButton *deleteButton;

@property (nonatomic, strong) UIButton *collectionButton;

@property (nonatomic, strong) RACDisposable *disposable;

@property (nonatomic, strong) UIButton *selectedMaskButton;

@property (nonatomic, strong) KAShoppingCartCouponDetailView* detailView;

//@property (nonatomic,strong) UIButton* discountDetailBtn;

@end

@implementation KAShoppingCartSettlementFloor

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        
        self.clipsToBounds = YES;
        
        self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
//        [self addSubview: self.discountDetailBtn];
        
        
        [self addSubview:self.bottomBar];
        [self.bottomBar mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(50);
        }];
        
        [self.bottomBar addSubview:self.deleteButton];
        CGSize deleteButton = [self.collectionButton jdcd_getSizeOfWidth:200 height:30];
        CGFloat deleteButtonWidth= MIN(deleteButton.width+16,80);
        [self.deleteButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(-12);
            make.height.mas_equalTo(30);
            make.width.mas_equalTo(deleteButtonWidth);
            make.centerY.mas_equalTo(self.bottomBar.mas_centerY);
        }];
        
//        [self.bottomBar addSubview:self.collectionButton];
        CGSize sizeCollection = [self.collectionButton jdcd_getSizeOfWidth:200 height:30];
        [self.collectionButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.deleteButton.mas_leading).mas_offset(-12);
            make.height.mas_equalTo(30);
            make.width.mas_equalTo(sizeCollection.width+16);
            make.centerY.mas_equalTo(self.bottomBar.mas_centerY);
        }];
        
        [self.bottomBar addSubview:self.selectedMaskButton];
        [self.selectedMaskButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.top.bottom.mas_equalTo(0);
            make.width.mas_equalTo(20 + 36);
        }];
        
        
    }
    return self;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    self.viewModel.cell = self;
    NSMutableAttributedString *totolPrice = [[NSMutableAttributedString alloc] init];
    
    UIFont *font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9" weight:UIFontWeightMedium];
    [totolPrice KA_renderWithPriceStr:self.viewModel.price type:KAPriceTypeP3 colorType:@"#C7"];
    UIColor* preColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
    
    NSAttributedString *pre = [[NSAttributedString alloc] initWithString:ShopingCartL(@"ka_cart_in_total") attributes:@{NSFontAttributeName:font,NSForegroundColorAttributeName:preColor}];
    [totolPrice insertAttributedString:pre atIndex:0];
    
    [self.bottomBar configLabel1:totolPrice];
    
    
//    NSMutableAttributedString *discountPrice = [[NSMutableAttributedString alloc] init];
//    
//    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C5"];
//    UIFont *discontFont = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
//    [discountPrice KA_renderWithPriceStr:self.viewModel.discount type:KAPriceTypeP5 colorType:@"#C5"];
//    [discountPrice trimmingRightZero];
//    NSAttributedString *discontPre = [[NSAttributedString alloc] initWithString:ShopingCartL(@"ka_cart_checkout_discount") attributes:@{NSFontAttributeName:discontFont,NSForegroundColorAttributeName:color}];
//    [discountPrice insertAttributedString:discontPre atIndex:0];
//    [self.bottomBar configLabel2:discountPrice];
    
    switch (self.viewModel.selectedStatus) {
        case KAShoppingCartSettlementBarSelectedStatusSelected:
        {
            [self.bottomBar configSelectState:KACheckOutBottomBarSelected];
        }
            break;
        case KAShoppingCartSettlementBarSelectedStatusDisenable:
        {
            [self.bottomBar configSelectState:KACheckOutBottomBarDisable];
        }
            break;
        default:
        {
            [self.bottomBar configSelectState:KACheckOutBottomBarNotSelected];
        }
            break;
    }
    
    if (self.disposable && self.disposable.isDisposed == NO) {
        [self.disposable dispose];
    }
    @weakify(self);
    self.disposable = [RACObserve(self.viewModel, edit) subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        BOOL edit = [x boolValue];
        self.deleteButton.hidden = !edit;
        self.collectionButton.hidden = !edit;
        
        [self.bottomBar hideLabel1:edit];
//        [self.bottomBar hideLabel2:edit];
        [self.bottomBar hideRightButton:edit];
//        if(edit){
//            [self.bottomBar hideCoupon:edit];
//        }
//        if(self.viewModel.details.count && [self.viewModel.discount floatValue]>0.01 && !edit){
//            [self.bottomBar hideCoupon:NO];
//        }
    }];
    NSInteger showNum = self.viewModel.selectProductNum;
    NSString* tip;

    tip = [NSString stringWithFormat:ShopingCartL(@"ka_cart_checkout"),@(showNum)];
    [self.bottomBar configButton:tip];
}

//-(BOOL)showCouponDetail{
//    if(self.viewModel.details.count && [self.viewModel.discount floatValue]>0.01){
//        [self.bottomBar changeCouponModel:YES];
//        return NO;
//    }else{
//        [self.bottomBar changeCouponModel:NO];
//    }
//    return YES;
//}

- (void)deleteButtonClicked {
    @weakify(self);
    if (self.viewModel.productParams.count) {
        
        NSString* showTip;
//        if([self.viewModel isAllVirtualSuit] && [self.viewModel getShowNum] == 1){
//            showTip = ShopingCartL(@"ka_cart_confirm_del_vir_suit");
//        }else{

            showTip = ShopingCartL(@"ka_cart_confirm_del");
//        }
        [KAAlert alert].config.renderW3a(showTip,NSTextAlignmentCenter).addLineAction(ShopingCartL(@"ka_cart_cancel"), ^{
            
        }).addFillAction(ShopingCartL(@"ka_cart_product_delete"), ^{
            JDCDISVAction *action = [self.viewModel deleteAction];
            action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
                
                if (error) {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_tips_delete_failed")];
                }
            };
            @strongify(self);
            [self isv_sendAction:action];
        }).jdcd_show();
    }else {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:ShopingCartL(@"ka_cart_no_sel_product")];
    }
}

- (void)collectionButtonClicked {
    @weakify(self);
//    if([self.viewModel isAllVirtualSuit]){
//        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:ShopingCartL(@"ka_cart_no_suit_no_remove_favorite")];
//        return;
//    }
    
    NSString* tipStr = ShopingCartL(@"ka_cart_confirm_remove_favorite");
    
    if (self.viewModel.productParams.count) {

        [KAAlert alert].config.renderW3a(tipStr,NSTextAlignmentCenter).addLineAction(ShopingCartL(@"ka_cart_cancel"), ^{

        }).addFillAction(ShopingCartL(@"ka_cart_confirm"), ^{
            @strongify(self);
            JDCDISVAction *action = [self.viewModel collectionAction];
            
            action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
                if (error) {
                    [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_no_suit_no_remove_favorite")];
                }
            };
            [self isv_sendAction:action];
        }).jdcd_show();
    }else {
        [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:ShopingCartL(@"ka_cart_no_sel_product")];
    }
    
}

#pragma mark - action
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    
    if ([action.actionType isEqualToString:JDISVShoppingCartSettlementAction]) {
        if (!self.viewModel.productParams.count) {
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:ShopingCartL(@"ka_cart_no_sel_product")];
            return YES;
        }
        [[self.viewModel jhGroupCanBuy] subscribeNext:^(id  _Nullable x) {
            NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeSettlement];
            NSString *router = [NSString stringWithFormat:@"router://%@/settlementController",moduleName];
            [JDRouter openURL:router arg:@{@"sourceType":@(0)} error:nil completion:^(id _Nullable object) {
                // 成功
                UIViewController *vc = [object objectForKey:@"result"];
                if (vc) {
                    vc.hidesBottomBarWhenPushed = YES;
                    [controller.navigationController pushViewController:vc animated:YES];
                }
            }];
        } error:^(NSError * _Nullable error) {
            [KAAlert alert].config
            .renderW4(ShopingCartL(@"ka_cart_user_group_buy_limit_title"), error.localizedDescription)
            .addLineAction(ShopingCartL(@"ka_cart_user_group_buy_limit_ok"), ^{
                NSLog(@"---- click ----");
            })
            .alertShow();
            
        }];
        return YES;
    }
    return NO;
}

- (void)selectedButtonClicked {
    if(self.detailView){
        return;
    }
    if(self.viewModel.selectedStatus == KAShoppingCartSettlementBarSelectedStatusDisenable){
        return;
    }
    
    JDCDISVAction *action = [self.viewModel selectAction];
    @weakify(self);
    NSDictionary* value = (NSDictionary*)action.value;
    action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
        if (error) {
            [KAToast alert].config.renderW1(ShopingCartL(@"ka_cart_tips_action_failed")).jdcd_show();
        }else{
            @strongify(self);
            [self.viewModel userSelectAll:value[@"userActionId"]];
        }
    };
    [self isv_sendAction:action];
}

-(void)discountDetailBtnClicked{
    if(self.detailView){
        return;
    }

    NSMutableArray* tmp  = [NSMutableArray array];
    if(self.viewModel.details.count>1){
        for(int i =0; i< self.viewModel.details.count-1; i++){
            couponDeailModel* detailmodel = [[couponDeailModel alloc] init];
            JDISVShoppingCartItemModel* model = self.viewModel.details[i];
            detailmodel.title = model.info[@"C-M#subtotalDetailItem&core"][@"name"];
            detailmodel.detail = model.info[@"C-M#subtotalDetailItem&core"][@"price"];
            [tmp addObject:detailmodel];
        }
    }
    
    couponDeailModel* totalModel = [[couponDeailModel alloc] init];
    JDISVShoppingCartItemModel* model = self.viewModel.details.lastObject;
    totalModel.title = model.info[@"C-M#subtotalDetailItem&core"][@"name"];
    totalModel.detail = model.info[@"C-M#subtotalDetailItem&core"][@"price"];
    
    totalModel.isTotal = YES;
    [tmp addObject:totalModel];
    CGRect superViewRect = self.superview.bounds;
    CGFloat selfHeight = self.frame.size.height;
    CGRect resultRect = CGRectMake(0, 0, superViewRect.size.width, superViewRect.size.height-selfHeight+10);
    
    KAShoppingCartCouponDetailView* detailView = [[KAShoppingCartCouponDetailView alloc] initWithFrame:resultRect];
    detailView.details = [tmp copy];
    [self.superview addSubview:detailView];
    [self.superview bringSubviewToFront:self];
    self.detailView = detailView;
    detailView.settleFloor = self;
}

-(void)closePromosionView{
    if(self.detailView){
        [self.detailView removeFromSuperview];
        self.detailView = nil;
    }
}

#pragma mark - private
- (BOOL)isTopController {
    BOOL result = NO;
    if ([self currentController].navigationController.viewControllers.count == 1) {
        result = YES;
    }
    return result;
}

- (nullable UIViewController *)currentController {
    UIResponder *next = self.nextResponder;
    do {
        if ([next isKindOfClass:UIViewController.class]) {
            return (UIViewController *)next;
        }
        next = next.nextResponder;
    } while (next != nil);
    return nil;
}

#pragma mark - getter
//- (KACheckOutBottomBar *)bottomBar {
//    if (!_bottomBar) {
//        _bottomBar = [[KACheckOutBottomBar alloc] init];
//        
//        @weakify(self);
//        _bottomBar.button1Action = ^{
//            @strongify(self);
//            if(self.detailView)
//                return;
//            
//            [self selectedButtonClicked];
//        };
//        
//        _bottomBar.button2Action = ^{
//            @strongify(self);
//            if(self.detailView){
//                [self.detailView removeFromSuperview];
//                self.detailView = nil;
//            }
//            JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVShoppingCartSettlementAction];
//            [self isv_sendAction:action];
//        };
//        _bottomBar.couponAction = ^{
//            @strongify(self);
//            [self discountDetailBtnClicked];
//        };
//    }
//    return _bottomBar;
//}

- (KSAUICheckOutBottomBar *)bottomBar {
    if (!_bottomBar) {
        _bottomBar = [[KSAUICheckOutBottomBar alloc] init];
        @weakify(self);
        _bottomBar.button1Action = ^{
            @strongify(self);
            if(self.detailView)
                return;
            
            [self selectedButtonClicked];
        };
        
        _bottomBar.button2Action = ^{
            @strongify(self);
            if(self.detailView){
                [self.detailView removeFromSuperview];
                self.detailView = nil;
            }
            JDCDISVAction *action = [JDCDISVAction actionWithType:JDISVShoppingCartSettlementAction];
            [self isv_sendAction:action];
        };
        _bottomBar.couponAction = ^{
            @strongify(self);
            [self discountDetailBtnClicked];
        };
    }
    return _bottomBar;
}

- (UIButton *)deleteButton {
    if (!_deleteButton) {
        _deleteButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_deleteButton setTitle:ShopingCartL(@"ka_cart_product_delete") forState:UIControlStateNormal];
        [_deleteButton addTarget:self action:@selector(deleteButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        CGFloat r51 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R51"];
        [_deleteButton renderB4WithCornerRadius:r51];
    }
    return _deleteButton;
}

- (UIButton *)collectionButton {
    if (!_collectionButton) {
        _collectionButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_collectionButton setTitle:ShopingCartL(@"ka_cart_remove_favorite") forState:UIControlStateNormal];
        [_collectionButton addTarget:self action:@selector(collectionButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        CGFloat r51 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R51"];
        [_collectionButton renderB4WithCornerRadius:r51];
    }
    return _collectionButton;
}

- (UIButton *)selectedMaskButton {
    if (!_selectedMaskButton) {
        _selectedMaskButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedMaskButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
    }
    return _selectedMaskButton;
}


- (void)dealloc {
    NSLog(@"-----");
}

@end
