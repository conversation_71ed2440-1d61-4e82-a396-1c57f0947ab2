//
//  KSAUICheckOutBottomBar.h
//  JDISVShoppingCartSDKModule
//
//  Created by cdwutao3 on 2023/8/30.
//

#import <UIKit/UIKit.h>

@import JDISVKAUIKitModule;

NS_ASSUME_NONNULL_BEGIN

@interface KSAUICheckOutBottomBar : KABaseBottomBar

/// 点击第一个按钮的点击事件回调
@property (copy, nonatomic) void(^button1Action)(void);
/// 点击第二个按钮的点击事件回调
@property (copy, nonatomic) void(^button2Action)(void);

@property (copy, nonatomic) void(^couponAction)(void);

/// 设置当前左侧选择按钮的状态（默认为未选中状态）
/// @param state 按钮状态
- (void)configSelectState:(KACheckOutBottomBarSelectType)state;

/// 是否隐藏第一个 Label Text（默认不隐藏）
/// @param hide 是否隐藏
- (void)hideLabel1:(BOOL)hide;

/// 是否隐藏第一个 Label Text（默认不隐藏）
/// @param hide 是否隐藏
- (void)hideLabel2:(BOOL)hide;

/// 是否隐藏右侧 Button（默认不隐藏）
/// @param hide 是否隐藏
- (void)hideRightButton:(BOOL)hide;


/// 是否隐藏右侧 Button（默认不隐藏）
/// @param hide 是否隐藏
- (void)hideCoupon:(BOOL)hide;

/// 更改选择按钮旁边的Tip Label Text
/// @param text 标题
- (void)configSelectTipLabel:(NSAttributedString *)text;

/// 更改第一个 Label Text
/// @param text 标题
- (void)configLabel1:(NSAttributedString *)text;

/// 更改第二个 Label Text
/// @param text 标题
- (void)configLabel2:(NSAttributedString *)text;

- (void)configLabel2:(NSAttributedString *)text hideImg:(BOOL)hideImg;

/// 更改右侧 Button Title
/// @param title 标题
- (void)configButton:(NSString *)title;

/// 自定义更改右侧 Button 圆角大小
/// @param cornerRadius 圆角大小
- (void)configAllButtonCornerRadius:(CGFloat)cornerRadius;

-(void)changeCouponModel:(BOOL)couponModel;
@end

NS_ASSUME_NONNULL_END
