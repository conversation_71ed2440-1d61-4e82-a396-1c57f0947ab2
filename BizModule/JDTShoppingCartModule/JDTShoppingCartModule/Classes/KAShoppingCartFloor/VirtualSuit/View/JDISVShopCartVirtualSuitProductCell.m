//
//  JDISVShopCartVirtualSuitProductCell.m
//  JDISVShoppingCartSDKModule
//
//  Created by cdwutao3 on 2022/11/19.
//

#import "JDISVShopCartVirtualSuitProductCell.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVKAUIKitModule/KAFloatLayerPresentationController.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDBRouterModule/JDRouter.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVKAIconFontModule/UIImageView+KATheme.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVKAUIKitModule/KALabel.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/KAPriceLabel.h>
#import "JDISVShoppingCarPub.h"

@interface JDISVShopCartVirtualSuitProductCell()

@property (nonatomic, strong) UIView *productView;

@property (nonatomic, strong) UIImageView *productImageView;

@property (nonatomic, strong) UIView *maskView;

@property (nonatomic, strong) UILabel *productNameLabel;

@property (nonatomic, strong) UILabel *suitDesc;

@property (nonatomic, strong) KALabel *labelSellOut;

@property (nonatomic, strong) UIView *propertyView;

@property (nonatomic, strong) UILabel *propertyLabel;

@end

@implementation JDISVShopCartVirtualSuitProductCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    [self setupUI];
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        [self setupUI];
    }
    return self;
}
-(void)setupUI{
    self.contentView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self.contentView addSubview:self.productView];
    [self.productView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(50);
        make.top.mas_equalTo(12);
        make.width.height.mas_equalTo(90);
    }];
    
    [self.contentView addSubview:self.productNameLabel];
    [self.productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productView).mas_offset(2);
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.trailing.mas_equalTo(self.contentView).mas_offset(-18);
    }];
    
    [self.contentView addSubview:self.suitDesc];
    [self.suitDesc mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.top.mas_equalTo(self.productView.mas_top).mas_offset(60);
    }];
    
    [self.productView addSubview:self.productImageView];
    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.productView);
    }];
    [self.productView addSubview:self.labelSellOut];
    [self.labelSellOut mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.mas_equalTo(50);
        make.center.mas_equalTo(self.productView);
    }];
    [self.productView addSubview:self.maskView];
    [self.maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.productView);
    }];
    
    {//property
        [self.contentView addSubview:self.propertyView];
        [self.propertyView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
            make.top.mas_equalTo(self.productNameLabel.mas_bottom).mas_offset(6);
            make.height.mas_equalTo(18);
            make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-18);
        }];
        
        [self.propertyView addSubview:self.propertyLabel];
        [self.propertyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(6);
            make.centerY.mas_equalTo(self.propertyView.mas_centerY);
            make.trailing.mas_equalTo(self.propertyView.mas_trailing).mas_offset(-6);
        }];
    }
}

-(void)configWithModel:(JDISVShopCartVirtualSuitProductModel*)model{
    NSString *imageUrl = [PlatformService getCompleteImageUrl:model.imgUrl moduleType:JDISVModuleTypeShoppingCart];
    UIImage* placeHoldImg = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
    [self.productImageView jdcd_setImage:imageUrl placeHolder:placeHoldImg contentMode:UIViewContentModeScaleAspectFit];
    self.productNameLabel.text = model.name;
    self.suitDesc.text = model.desc;
    
    self.labelSellOut.hidden = model.hasStock;
    
    if (model.propertyDic.count) {
        
        NSMutableArray *tempArray = [NSMutableArray array];
        for (NSString *key in model.propertyDic) {
            [tempArray addObject:[NSString stringWithFormat:@"%@",model.propertyDic[key]]];
        }
        NSString* propertyStr = [tempArray componentsJoinedByString:@";"];
        self.propertyLabel.text = propertyStr;
        self.propertyView.hidden = NO;
        self.propertyLabel.hidden = NO;
    }else {
        self.propertyView.hidden = YES;
        self.propertyLabel.hidden = YES;
    }
}

- (UIView *)productView {
    if (!_productView) {
        _productView = [[UIView alloc] init];
        CGFloat r75 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R75"];
        _productView.layer.cornerRadius = r75;
        _productView.layer.masksToBounds = YES;
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        _productView.backgroundColor = color;
    }
    return _productView;
}

- (UIImageView *)productImageView {
    
    if (!_productImageView) {
        UIImage* placeHoldImg = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
        _productImageView = [[UIImageView alloc] initWithImage:placeHoldImg];
    }
    return _productImageView;
}
- (UIView *)maskView {
    if (!_maskView) {
        _maskView = [[UIView alloc] init];
        _maskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02];
    }
    return _maskView;
}
-(UILabel*)productNameLabel{
    if(!_productNameLabel){
        _productNameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _productNameLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _productNameLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _productNameLabel.numberOfLines = 2;
        _productNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    }
    return _productNameLabel;
}

-(UILabel*)suitDesc{
    if(!_suitDesc){
        _suitDesc = [[UILabel alloc] initWithFrame:CGRectZero];
        _suitDesc.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _suitDesc.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
        _suitDesc.numberOfLines = 0;
        _suitDesc.lineBreakMode = NSLineBreakByTruncatingTail;
    }
    return _suitDesc;
}

- (KALabel *)labelSellOut {
    if (!_labelSellOut) {
        _labelSellOut = [KALabel kaLabelWithType:KALabelTypeL8 title:ShopingCartL(@"ka_cart_product_no_stock_short") cornerRadius:25];
        _labelSellOut.userInteractionEnabled = NO;
    }
    return _labelSellOut;
}

- (UIView *)propertyView {
    if (!_propertyView) {
        _propertyView = [[UIView alloc] init];
        _propertyView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C2");
        CGFloat r60 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R60"];
        _propertyView.layer.cornerRadius = r60;
        _propertyView.layer.masksToBounds = YES;
    }
    return _propertyView;
}

- (UILabel *)propertyLabel {
    if (!_propertyLabel) {
        _propertyLabel = [[UILabel alloc] init];
        _propertyLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
        _propertyLabel.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    }
    return _propertyLabel;
}

@end
