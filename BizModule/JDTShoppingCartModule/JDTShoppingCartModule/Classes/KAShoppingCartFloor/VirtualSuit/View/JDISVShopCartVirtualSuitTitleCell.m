//
//  JDISVShopCartVirtualSuitTitleCell.m
//  JDISVShoppingCartSDKModule
//
//  Created by cdwutao3 on 2022/11/19.
//

#import "JDISVShopCartVirtualSuitTitleCell.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVKAUIKitModule/KAFloatLayerPresentationController.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDBRouterModule/JDRouter.h>

#import <JDISVKAIconFontModule/UIImageView+KATheme.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import "JDISVShoppingCartVirtualSuitFloor.h"
#import "JDISVShopCartVirtualSuitTitleModel.h"
#import "JDISVShoppingCarPub.h"

@interface JDISVShopCartVirtualSuitTitleCell()
@property (nonatomic, strong) UIButton *selectedButton;
@property (nonatomic, strong) UIButton *selectedMaskButton;
@property (nonatomic, weak) JDISVShopCartVirtualSuitTitleModel* viewModel;
@property (nonatomic,strong) UIButton* vstag;
@property (nonatomic,strong) UILabel* labelName;
@end

@implementation JDISVShopCartVirtualSuitTitleCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    [self setupUI];
}
-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
//    
    [self.contentView addSubview:self.selectedButton];
    [self.selectedButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self).mas_equalTo(18);
        make.height.width.mas_equalTo(20);
        make.top.mas_equalTo(self).mas_equalTo(6);
    }];
    
    [self.contentView addSubview:self.vstag];
    [self.vstag mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentView).mas_offset(50);
        make.height.mas_equalTo(16);
        make.width.mas_equalTo(32);
        make.top.mas_equalTo(self.mas_top).mas_offset(7);
    }];

    [self.contentView addSubview:self.labelName];
    if (@available(iOS 8.2, *)) {
        self.labelName.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
    } else {
        self.labelName.font = [UIFont systemFontOfSize:12];
    }
    [self.labelName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentView).mas_offset(50);
        make.trailing.mas_equalTo(self.contentView.mas_trailing).offset(-18);
        make.top.mas_equalTo(self.mas_top).mas_offset(8);
    }];
    
    [self.contentView addSubview:self.selectedMaskButton];
    [self.selectedMaskButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.mas_equalTo(0);
        make.width.mas_equalTo(20 + 36);
    }];
}

-(void)configWithModel:(JDISVShopCartVirtualSuitTitleModel*)model{
    self.viewModel = model;
    model.cell = self;
    if(model.vsTag.length){
        [self.vstag setTitle:model.vsTag forState:UIControlStateNormal];
        self.vstag.hidden = NO;
//        _vstag.titleLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1-b"];
        [_vstag renderL4WithCornerRadius:4];
        [_vstag setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    }else{
        self.vstag.hidden = YES;
    }
    NSString* tagStr = model.vsTag;
    NSString* desc = model.text;
    NSMutableAttributedString* tmpStr = [[NSMutableAttributedString alloc] init];
    NSMutableAttributedString* txtStr = [[NSMutableAttributedString alloc] initWithString:desc];
    [tmpStr appendAttributedString:txtStr];
    NSMutableParagraphStyle * mParagraphStyle = [[NSMutableParagraphStyle  alloc] init];
    mParagraphStyle.lineSpacing = 5;                 // 行间距
    mParagraphStyle.minimumLineHeight = 14;
    mParagraphStyle.maximumLineHeight = 14;
    mParagraphStyle.lineBreakMode = NSLineBreakByTruncatingTail;
    mParagraphStyle.firstLineHeadIndent = 10*tagStr.length+10+6;
    [tmpStr addAttribute:NSParagraphStyleAttributeName
                           value:mParagraphStyle
                           range:NSMakeRange(0, tmpStr.length)];
    [tmpStr addAttribute:NSFontAttributeName
                           value:[UIFont systemFontOfSize:12]
                           range:NSMakeRange(0, tmpStr.length)];
    self.labelName.attributedText = [tmpStr copy];
    self.labelName.text = desc;
    
    self.selectedButton.jdisv_selected_B7 = model.active;
    self.selectedButton.enabled = model.enabelCheck;
    self.selectedMaskButton.enabled = model.enabelCheck;
}

-(void)selectedButtonClicked{
    [self.floor userTapSelect];
}

- (UIButton *)selectedButton {
    if (!_selectedButton) {
        _selectedButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        [_selectedButton renderB7];
    }
    return _selectedButton;
}

-(UIButton*)vstag{
    if(!_vstag){
        _vstag = [UIButton buttonWithType:UIButtonTypeCustom];
        [_vstag setTitle:ShopingCartL(@"group") forState:UIControlStateNormal];
        _vstag.titleLabel.font = [UIFont systemFontOfSize:10];
        [_vstag renderL4WithCornerRadius:4];
        _vstag.titleLabel.textColor = [UIColor whiteColor];
    }
    return _vstag;
}

- (UIButton *)selectedMaskButton {
    if (!_selectedMaskButton) {
        _selectedMaskButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedMaskButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
    }
    return _selectedMaskButton;
}
-(UILabel*)labelName{
    if(!_labelName){
        _labelName = [[UILabel alloc] initWithFrame:CGRectZero];
        _labelName.numberOfLines = 2;
        _labelName.lineBreakMode = NSLineBreakByTruncatingTail;
        _labelName.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        
    }
    return _labelName;
}
@end
