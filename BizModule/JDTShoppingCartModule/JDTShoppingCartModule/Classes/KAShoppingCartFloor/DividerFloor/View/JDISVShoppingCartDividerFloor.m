//
//  JDISVShoppingCartDividerFloor.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/9/28.
//

#import "JDISVShoppingCartDividerFloor.h"

#import <JDISVMasonryModule/Masonry.h>

#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>

#import <JDISVCategoryModule/UIView+JDCDCorners.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>

#import "JDISVShoppingCartDividerModule.h"

@interface JDISVShoppingCartDividerFloor ()

@property (nonatomic, strong) JDISVShoppingCartDividerModule *viewModel;

@property (nonatomic, strong) UIView *topView;

@property (nonatomic, strong) UIView *bottomView;


@end

@implementation JDISVShoppingCartDividerFloor

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.clipsToBounds = YES;
        

        [self.contentView addSubview:self.topView];
        [self.topView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(9);
        }];
        
        [self.contentView addSubview:self.bottomView];
        [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.leading.trailing.mas_equalTo(0);
            make.height.mas_equalTo(9);
        }];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat r1 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R1"];
    if(r1 != 0){
        [self.topView jdcd_addRoundedCorners:UIRectCornerBottomLeft | UIRectCornerBottomRight withRadii:CGSizeMake(r1, r1) viewRect:CGRectMake(0, -r1, self.contentView.frame.size.width, r1 * 2)];
    }

    CGFloat r2 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R2"];
    if(r1 != 0){
        [self.bottomView jdcd_addRoundedCorners:UIRectCornerTopLeft | UIRectCornerTopRight withRadii:CGSizeMake(r2, r2) viewRect:CGRectMake(0, 0, self.contentView.frame.size.width, r2 * 2)];
    }
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    self.viewModel = floorModel;
    
//    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C16"];
    self.contentView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    
    self.topView.hidden = !self.viewModel.showTop;
    self.bottomView.hidden = !self.viewModel.showBottom;
}

#pragma mark - getter
- (UIView *)topView {
    if (!_topView) {
        _topView = [[UIView alloc] initWithFrame:CGRectZero];
        _topView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    return _topView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[UIView alloc] initWithFrame:CGRectZero];
        _bottomView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    }
    return _bottomView;
}

@end
