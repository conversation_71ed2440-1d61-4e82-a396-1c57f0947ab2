//
//  KSAShoppingCartShopHeadFloor.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by cdwutao3 on 2023/6/5.
//

#import "KSAShoppingCartShopHeadFloor.h"

#import <JDISVMasonryModule/Masonry.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>

#import <JDISVPlatformModule/JDISVPlatformService.h>

#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/UIButton+KALabelRender.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVKAUIKitModule/KAFloatLayerPresentationController.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDBRouterModule/JDRouter.h>

#import <JDISVKAIconFontModule/UIImageView+KATheme.h>
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>

#import "KAShoppingCartShopHeadModule.h"
#import "JDISVShoppingCartMainModel.h"
#import "JDISVShoppingCartCouponController.h"
#import <JDISVCategoryModule/UIView+JDCDRTL.h>
#import "JDISVCategoryModule/NSString+JDCDExtend.h"

static JDCDISVActionType const KSAShoppingCartHeaderCouponClickAction = @"KSAShoppingCartHeaderCouponClickAction";
static JDCDISVActionType const KSAShoppingCartHeaderShopNameClickAction = @"KSAShoppingCartHeaderShopNameClickAction";


@interface KSAShoppingCartShopHeadFloor ()

@property (nonatomic, strong) KAShoppingCartShopHeadModule *viewModel;

@property (nonatomic, strong) UIButton *selectedButton;
@property (nonatomic, strong) UIButton *selectedMaskButton;

@property (nonatomic, strong) UIImageView *shopImageView;

@property (nonatomic, strong) UILabel *shopNameLabel;

@property (nonatomic, strong) UIImageView *moreImageView;

@property (nonatomic, strong) UIButton *couponButton;



@end

@implementation KSAShoppingCartShopHeadFloor

- (instancetype)initWithReuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithReuseIdentifier:reuseIdentifier]) {
        [self setUpUI];
    }
    return self;
}

- (void)setUpUI {
    self.contentView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    
    [self addSubview:self.selectedButton];
    [self.selectedButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(18);
        make.height.width.mas_equalTo(20);
        make.top.mas_equalTo(8);
    }];
    
    [self addSubview:self.selectedMaskButton];
    [self.selectedMaskButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.bottom.mas_equalTo(0);
        make.width.mas_equalTo(20 + 36);
    }];
    
    [self addSubview:self.shopImageView];
    [self.shopImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.selectedButton.mas_trailing).mas_offset(13);
        make.width.height.mas_equalTo(20);
        make.centerY.mas_equalTo(self.selectedButton.mas_centerY);
    }];
    
    [self addSubview:self.couponButton];
    
    [self.couponButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self.mas_trailing).mas_offset(-18);
        make.width.mas_equalTo(24);
        make.height.mas_equalTo(24);
        make.centerY.mas_equalTo(self.selectedButton.mas_centerY);
    }];
    
    [self addSubview:self.moreImageView];
    [self.moreImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(12);
        make.centerY.mas_equalTo(self.selectedButton.mas_centerY);
        make.trailing.mas_lessThanOrEqualTo(self.couponButton.mas_leading).mas_offset(-15);
    }];
    
    [self addSubview:self.shopNameLabel];
    [self.shopNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.shopImageView.mas_trailing).mas_offset(3);
        make.trailing.mas_equalTo(self.moreImageView.mas_leading).mas_offset(-2);
        make.centerY.mas_equalTo(self.selectedButton.mas_centerY);
    }];
    
    self.shopNameLabel.userInteractionEnabled = YES;
    self.moreImageView.userInteractionEnabled = YES;
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(shopNameClicked)];
    [self.shopNameLabel addGestureRecognizer:tap];
    
    UITapGestureRecognizer *tap2 = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(shopNameClicked)];
    [self.moreImageView addGestureRecognizer:tap2];
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    if(self.viewModel){
        self.viewModel.cell = nil;
        self.viewModel = nil;
    }
    self.viewModel = floorModel;
    self.viewModel.cell = self;
    if(self.viewModel.shopId.integerValue == -1){
        self.shopNameLabel.text = ShopingCartL(@"ka_cart_min_promotion");
    }else{
        self.shopNameLabel.text = self.viewModel.shopName;
    }

    [self hiddenCoupon:!self.viewModel.showCoupon];
    
    @weakify(self);
    [[RACObserve(self.viewModel, edit) takeUntil:self.rac_prepareForReuseSignal]  subscribeNext:^(id  _Nullable x) {
        BOOL edit = [x boolValue];
        @strongify(self);
        BOOL hidden = (edit || !self.viewModel.showCoupon);
        [self hiddenCoupon:hidden];
        self.moreImageView.hidden = edit;
    }] ;
    
    self.selectedButton.jdisv_selected_B7 = self.viewModel.selected;
    self.selectedButton.enabled = self.viewModel.selectedEnable;
    self.selectedMaskButton.enabled = self.viewModel.selectedEnable;
}

- (void)hiddenCoupon:(BOOL)hidden {
    self.couponButton.hidden = hidden;
    CGSize size = [self.couponButton.titleLabel.text jdcd_getStringSize:self.couponButton.titleLabel.font constraintsSize:CGSizeMake(150, 24)];
    CGFloat couponButtonWidth = !hidden ? size.width+8 : 0;
    CGFloat couponLeft = !hidden ? - 15 : 0;
    [self.couponButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(couponButtonWidth);
    }];
    [self.couponButton renderASCouponTag:ShopingCartL(@"ka_cart_coupon")];
    [self.moreImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_lessThanOrEqualTo(self.couponButton.mas_leading).mas_offset(couponLeft);
    }];
    
}

- (void)selectedButtonClicked {
    JDCDISVAction *action = [self.viewModel selectAction];
    NSDictionary* value = (NSDictionary*)action.value;
    action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
        if (error) {
            
            [PlatformService showDefaultToastWithIconType:ISVInstantTypeFail message:ShopingCartL(@"ka_cart_tips_action_failed")];
        }else{
            [self.viewModel userSelectAll:value[@"userActionId"]
                               needNotify:YES];
        }
    };
    [self isv_sendAction:action];
}

- (void)couponButtonClicked:(UIButton *)sender {
    JDCDISVAction *action = [JDCDISVAction actionWithType:KSAShoppingCartHeaderCouponClickAction];
    [self isv_sendAction:action];
}

- (void)shopNameClicked {
    if (self.viewModel.edit) {
        return;
    }
    JDCDISVAction *action = [JDCDISVAction actionWithType:KSAShoppingCartHeaderShopNameClickAction];
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:KSAShoppingCartHeaderCouponClickAction]) {
        
        JDISVShoppingCartCouponController *couponVC = [[JDISVShoppingCartCouponController alloc] init];
        
        KAFloatLayerPresentationController *presentationVC = [[KAFloatLayerPresentationController alloc] initWithPresentedViewController:couponVC presentingViewController:controller];
        presentationVC.defultHeader.titleLabel.text = ShopingCartL(@"ka_cart_coupon");
        presentationVC.defultHeader.titleLabel.textAlignment = NSTextAlignmentCenter;
        presentationVC.insets = UIEdgeInsetsMake(0, 0, 0, 0);
        couponVC.transitioningDelegate = presentationVC;
        
        couponVC.venderId = self.viewModel.venderId;
        couponVC.shopName = self.viewModel.shopName;
        couponVC.shopId = self.viewModel.shopId;
        
        couponVC.gather = ^(JDISVCouponTagModel* tag){
            [tag useCoupon:controller];
        };
        presentationVC.presentedView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
        [controller presentViewController:couponVC animated:YES completion:nil];
        
        return YES;
    }else if ([action.actionType isEqualToString:KSAShoppingCartHeaderShopNameClickAction]){
//        if(self.viewModel.shopId.intValue == -1){
//            JDISVShoppingCartItemModel* promotinItem = self.viewModel.currentShopModel.subList.firstObject;
//            NSString* pid = promotinItem.info[@"C-M#promotionItem&core"][@"promotionId"]?:@"";
//            if(pid.length == 0){
//                [PlatformService showDefaultToastWithIconType:ISVInstantTypeOnlyMessage message:ShopingCartL(@"ka_cart_an_error_occurred_in_the_activity")];
//                return YES;
//            }
////            NSNumber* type = promotinItem.info[@"HJM-D#price&normalSuit"][@"fullRefundType"];
////            if(type.intValue == 1){//满减-去凑单 同Android去除这个限制
//            NSString* loginModule = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeLogin ];
//            NSString* routerStr= [NSString stringWithFormat:@"router://%@/excuteAfterLogin",loginModule];
//            [JDRouter openURL:routerStr
//                          arg:nil
//                        error:nil
//                   completion:^(NSNumber* success){
//                if(success.boolValue){
//                    NSDictionary* param = @{@"promotionId":pid};
//                    
//                    NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeSearch];
//                    NSString *router = [NSString stringWithFormat:@"router://%@/searchResultController",moduleName];
//                    UIViewController *searchController = [JDRouter openURL:router arg:param error:nil completion:nil];
//                    if (searchController) {
//                        searchController.hidesBottomBarWhenPushed = YES;
//                        [controller.navigationController pushViewController:searchController animated:YES];
//                    }
//                }
//            }];
//        }else{
            NSString* shopModule = [JDISV_RESOURCE_MANAGER moduleNameWithModuleType:JDISVModuleTypeShop];
            NSString* routerStr = [NSString stringWithFormat:@"router://%@/shopController",shopModule];
            NSDictionary* param =  @{@"pageCode": @"",@"shopName":self.viewModel.shopName?:@"", @"shopId": self.viewModel.shopId};
            [JDRouter openURL:routerStr arg:param error:nil completion:^(UIViewController *vc) {
                [controller.navigationController pushViewController:vc animated:YES];
            }];
//        }
        return YES;
    }
    return NO;
}

#pragma mark - getter
- (UIButton *)selectedButton {
    if (!_selectedButton) {
        _selectedButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
        [_selectedButton renderB7];
    }
    return _selectedButton;
}

- (UIImageView *)shopImageView {
    if (!_shopImageView) {
        _shopImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _shopImageView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C7", JDIF_ICON_SHOP_LINE, CGSizeMake(18, 18));
    }
    return _shopImageView;
}

- (UILabel *)shopNameLabel {
    if (!_shopNameLabel) {
        _shopNameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        _shopNameLabel.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
        _shopNameLabel.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T7", UIFontWeightMedium);
    }
    return _shopNameLabel;
}

- (UIImageView *)moreImageView {
    if (!_moreImageView) {
        _moreImageView = [[UIImageView alloc] initWithFrame:CGRectZero];
        _moreImageView.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C5", JDIF_ICON_ARROW_RIGHT_SMALL, CGSizeMake(12, 12));
//        if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft) {
//            [_moreImageView JDCDRTL];
//        }
    }
    return _moreImageView;
}

- (UIButton *)couponButton {
    if (!_couponButton) {
        _couponButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_couponButton setTitle:ShopingCartL(@"ka_cart_coupon") forState:UIControlStateNormal];
        
//        CGFloat r52 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R52"];
        
//        [_couponButton renderB18WithCornerRadius:r52];
        [_couponButton addTarget:self action:@selector(couponButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _couponButton;
}

- (UIButton *)selectedMaskButton {
    if (!_selectedMaskButton) {
        _selectedMaskButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_selectedMaskButton addTarget:self action:@selector(selectedButtonClicked) forControlEvents:UIControlEventTouchUpInside];
    }
    return _selectedMaskButton;
}

@end
