//
//  JDISVShoppingCartProductAddbyModule.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by cdwutao3 on 2022/7/22.
//

#import "JDISVShoppingCartProductAddbyModel.h"
#import "JDISVShoppingCartProductAddbyFloor.h"
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import "JDISVShoppingCartMainModel.h"

JDISVRegisterFloorModule(kShopProductAddbyFloor, JDISVShoppingCartProductAddbyModel)

JDCDISVActionType const KAShoppingCartAddbyProductCollectionAction = @"KAShoppingCartAddbyProductCollectionAction";
JDCDISVActionType const KAShoppingCartAddbyProductDeleteAction = @"KAShoppingCartAddbyProductDeleteAction";

@interface JDISVShoppingCartProductAddbyModel()
@property(strong,nonatomic) JDISVShoppingCartItemModel* cartItem;
@property(strong,nonatomic) JDISVShoppingCartItemModel* parentItem;
@end

@implementation JDISVShoppingCartProductAddbyModel

- (Class)tableViewFloorClass {
    return JDISVShoppingCartProductAddbyFloor.class;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (CGFloat)floorHeight {
    if(self.shopId.length && self.shopName.length){
        return 70+24+30;
    }
    return 70+24;
}

- (BOOL)sideSlipEnable {
    return YES;
}

- (NSArray<JDCDISVAction *> *)sideSlipActions {
    //左滑效果
    JDCDISVAction *deleteAction = [JDCDISVAction actionWithType:KAShoppingCartAddbyProductDeleteAction];
    UIColor *deleteColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C13"] ? : [UIColor whiteColor];
    deleteAction.value = @{@"title":ShopingCartL(@"ka_cart_product_delete"),@"color":deleteColor};

//    JDCDISVAction *collectionAction = [JDCDISVAction actionWithType:KAShoppingCartAddbyProductCollectionAction];
//    UIColor *collectionColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C10"] ? : [UIColor whiteColor];
//    collectionAction.value = @{@"title":ShopingCartL(@"ka_cart_product_favorite"),@"color":collectionColor};
    
    NSArray *tempActions = @[deleteAction];
    return [tempActions copy];
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.cartItem = data[@"data"];
    self.parentItem = data[@"parentItem"];
    self.name = [NSString stringWithFormat:@"%@",self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"name"]];
    self.skuId = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"id"];
    self.shopName = @"";
    self.shopId = @"";
    self.price = self.cartItem.info[@"C-M#abstractAppendantItem&basic"][@"priceShow"];
    self.imgUrl = self.cartItem.info[@"C-M#abstractAppendantItem&img"][@"imgUrl"];
    self.number = @(1);
    NSNumber* stockNum = self.cartItem.info[@"HJM-M#cartProduct&stockInfo"][@"stockCode"];
    self.stockCode = [stockNum integerValue];
}

-(JDCDISVAction*)collectAction{
    NSMutableDictionary* prodDic = [NSMutableDictionary dictionary];
    NSMutableDictionary* promoDic = [NSMutableDictionary dictionary];
    NSString* prodUUID = NSUUID.UUID.UUIDString;
    NSString* promoUUID = NSUUID.UUID.UUIDString;
    
    prodDic[@"id"] = prodUUID;
    prodDic[@"parentId"] = promoUUID;
    prodDic[@"itemType"] =@(13);
    prodDic[@"num"] = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"num"]?:@(1);
    prodDic[@"skuId"] = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"id"]?:@"";
    
    promoDic[@"id"] = promoUUID;
    promoDic[@"promotionId"] = self.parentItem.info[@"C-M#promotionItem&core"][@"promotionId"];
    promoDic[@"itemType"] = @(3);
    promoDic[@"num"] = @"1";
    promoDic[@"childList"] = @[prodUUID];
    
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVShoppingCarAddbyCollectAction"];
    action.value = @{@"operations":@{@"products":@[prodDic],@"promotions":@[promoDic]}};
    return action;
}


-(JDCDISVAction*)deleteAction{
    NSMutableDictionary* prodDic = [NSMutableDictionary dictionary];
    NSMutableDictionary* promoDic = [NSMutableDictionary dictionary];
    NSString* prodUUID = NSUUID.UUID.UUIDString;
    NSString* promoUUID = NSUUID.UUID.UUIDString;
    
    prodDic[@"id"] = prodUUID;
    prodDic[@"parentId"] = promoUUID;
    prodDic[@"itemType"] =@(13);
    prodDic[@"num"] = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"num"]?:@(1);
    prodDic[@"skuId"] = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"id"]?:@"";
    
    promoDic[@"id"] = promoUUID;
    promoDic[@"promotionId"] = self.parentItem.info[@"C-M#promotionItem&core"][@"promotionId"];
    promoDic[@"itemType"] = @(3);
    promoDic[@"num"] = @"1";
    promoDic[@"childList"] = @[prodUUID];
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVShoppingCarAddbyDeleteAction"];
    action.value = @{@"operations":@{@"products":@[prodDic],@"promotions":@[promoDic]}};
    return action;
    
}
@end
