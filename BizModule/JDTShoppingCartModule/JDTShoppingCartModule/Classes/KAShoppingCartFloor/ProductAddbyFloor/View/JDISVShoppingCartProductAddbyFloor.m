//
//  JDISVShoppingCartProductAddbyFloor.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by cdwutao3 on 2022/7/22.
//

#import "JDISVShoppingCartProductAddbyFloor.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/KALabel.h>
#import <JDISVKAUIKitModule/UIButton+KAButton.h>
#import <JDISVKAUIKitModule/KAPriceLabel.h>
#import <JDISVKAUIKitModule/KAToast.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import "JDISVShoppingCartTool.h"
#import <JDBRouterModule/JDRouter.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVCategoryModule/NSString+JDCDExtend.h>
#import "JDISVShoppingCartProductAddbyModel.h"
@interface JDISVShoppingCartProductAddbyFloor()

@property (nonatomic, strong) UIView *productView;
@property (nonatomic, strong) UIImageView *productImageView;
@property (nonatomic, strong) UIView *maskView;

@property(strong,nonatomic) KALabel* labelAddbySig;
@property(strong,nonatomic) UILabel* labelName;
@property (nonatomic, strong) KAPriceLabel *priceLabel;
@property (nonatomic, strong) UILabel *labelNum;

@property (nonatomic, strong) UILabel *labelMD;
@property (nonatomic, strong) UILabel *labelShopName;

@property (nonatomic, strong) KALabel *labelSellOut;
@property (nonatomic, assign) CGFloat tagWidth;
@property(strong,nonatomic) JDISVShoppingCartProductAddbyModel* viewModel;

@end

@implementation JDISVShoppingCartProductAddbyFloor


- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUpUI];
    }
    return self;
}

- (void)setUpUI {
    self.contentView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    [self.contentView addSubview:self.productView];
    [self.productView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.mas_equalTo(70);
        make.top.mas_equalTo(self.contentView).mas_offset(16);
        make.leading.mas_equalTo(self.contentView).mas_offset(76);
    }];
    
    [self.productView addSubview:self.productImageView];
    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.productView);
    }];
    
    [self.productView addSubview:self.labelSellOut];
    [self.labelSellOut mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.mas_equalTo(50);
        make.center.mas_equalTo(self.productView);
    }];
    
    self.labelAddbySig = [KALabel kaLabelWithType:KALabelTypeL3 title:ShopingCartL(@"ka_cart_add_pay_tag")];
    CGSize size = [self.labelAddbySig.titleLabel.text jdcd_getStringSize:self.labelAddbySig.titleLabel.font constraintsSize:CGSizeMake(200, 18)];
    self.tagWidth = size.width+8;
    [self.contentView addSubview:self.labelAddbySig];
    [self.labelAddbySig mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(18);
        make.width.mas_equalTo(self.tagWidth);
        make.top.mas_equalTo(self.productView.mas_top);
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
    }];
    self.labelAddbySig.layer.cornerRadius = 2;
    self.labelAddbySig.layer.masksToBounds = YES;
    
    
    self.labelName = [[UILabel alloc] initWithFrame:CGRectZero];
    self.labelName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.labelName.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    self.labelName.numberOfLines = 2;
    self.labelName.lineBreakMode = NSLineBreakByTruncatingTail;
    [self.contentView addSubview:self.labelName];
    [self.labelName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productView.mas_top);
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.trailing.mas_equalTo(self.contentView).mas_offset(-18);
        make.height.mas_lessThanOrEqualTo(85);
    }];
    
    self.priceLabel = [[KAPriceLabel alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productView.mas_top).mas_offset(45);
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
    }];
    
    self.labelNum = [[UILabel alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.labelNum];
    [self.labelNum mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.priceLabel);
        make.trailing.mas_equalTo(self.contentView).mas_offset(-18);
    }];
    self.labelNum.text = @"x1";
    self.labelNum.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
    self.labelNum.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    
    self.labelMD = [[UILabel alloc] initWithFrame:CGRectZero];
    self.labelMD.text = ShopingCartL(@"ka_cart_product_shop");
    self.labelMD.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
    if (@available(iOS 8.2, *)) {
        self.labelMD.jdisv_fontPicker = JDISVFontPickerWithKeyAndWeight(@"#T9",UIFontWeightMedium);
    } else {
        self.labelMD.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    }
    [self.contentView addSubview:self.labelMD];
    [self.labelMD mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.productView.mas_trailing).mas_offset(12);
        make.top.mas_equalTo(self.priceLabel.mas_bottom).mas_offset(12);
    }];
    
    self.labelShopName = [[UILabel alloc] initWithFrame:CGRectZero];
    self.labelShopName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C6");
    self.labelShopName.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T9");
    [self.contentView addSubview:self.labelShopName];
    [self.labelShopName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.labelMD.mas_trailing).mas_offset(6);
        make.top.mas_equalTo(self.labelMD);
    }];
    self.labelShopName.numberOfLines = 1;
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel {
    
    self.viewModel = floorModel;
    
    NSString *imageUrl = [PlatformService getCompleteImageUrl:self.viewModel.imgUrl moduleType:JDISVModuleTypeShoppingCart];
    UIImage* placeHoldImg = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
    [self.productImageView jdcd_setImage:imageUrl placeHolder:placeHoldImg contentMode:UIViewContentModeScaleAspectFit];
    NSMutableParagraphStyle* style = [NSMutableParagraphStyle alloc];
    style.lineSpacing = 5;
    style.firstLineHeadIndent = self.tagWidth+4;
    style.alignment = NSTextAlignmentNatural;
    NSMutableAttributedString* nameAttr = [[NSMutableAttributedString alloc] initWithString:self.viewModel.name?:@""];
    [nameAttr addAttributes:@{NSParagraphStyleAttributeName:style,
                              NSFontAttributeName:[[JDISVThemeFont sharedInstance] fontWithKey:@"#T7"],
                              NSForegroundColorAttributeName:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"]
                            } range:NSMakeRange(0, self.viewModel.name.length)];
    self.labelName.attributedText = [nameAttr copy];
    self.labelName.lineBreakMode = NSLineBreakByTruncatingTail;
    NSString* price = [self.viewModel.price stringByReplacingOccurrencesOfString:@"¥" withString:@""];
    if(price.floatValue == 0){
        [self.priceLabel configNoPrice];
    }else{
        
        [self.priceLabel configTextWithPrice:price.floatValue middleLinePrice:0];
    }
    
    if(self.viewModel.shopName.length){
        self.labelShopName.text = self.viewModel.shopName;
        self.labelShopName.hidden = NO;
        self.labelMD.hidden = NO;
    }else{
        self.labelShopName.hidden = YES;
        self.labelMD.hidden = YES;
    }
    if(self.viewModel.stockCode == 0){
        self.labelSellOut.hidden = YES;
    }else{
        self.labelSellOut.hidden = NO;
    }
}

#pragma mark action
- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if([action.actionType isEqualToString:KAShoppingCartAddbyProductCollectionAction]){
        JDCDISVAction *action = [self.viewModel collectAction];
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            if (error) {
                [KAToast alert].config.renderW2Error(ShopingCartL(@"ka_cart_tips_delete_failed")).jdcd_show();
            }
        };
        [self isv_sendAction:action];
        return YES;
    }
    if([action.actionType isEqualToString:KAShoppingCartAddbyProductDeleteAction]){
        JDCDISVAction *action = [self.viewModel deleteAction];
        action.complete = ^(id  _Nullable obj, NSError * _Nullable error) {
            if (error) {
                [KAToast alert].config.renderW2Error(ShopingCartL(@"ka_cart_tips_delete_failed")).jdcd_show();
            }
        };
        [self isv_sendAction:action];
        return YES;
    }
    if ([action.actionType isEqualToString:kJDCDISVFloorDidSelected]){
        if (self.viewModel.isEdit) {
            //编辑态不能进商详
            return YES;
        }
        NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeProductDetail];
        NSString *router = [NSString stringWithFormat:@"router://%@/productDetailController",moduleName];
        router = @"router://JDISVProductDetailSDKModule/productDetailController";
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        if (self.viewModel.skuId) {
            [params setObject:self.viewModel.skuId forKey:@"skuId"];
        }
        UIViewController *productDetailVC = [JDRouter openURL:router arg:params error:nil completion:nil];
        if (productDetailVC) {
            productDetailVC.hidesBottomBarWhenPushed = YES;
            [controller.navigationController pushViewController:productDetailVC animated:YES];
        }
        return YES;
    }
    return NO;
}


#pragma mark getter
- (UIView *)productView {
    if (!_productView) {
        _productView = [[UIView alloc] init];
        CGFloat r75 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R75"];
        _productView.layer.cornerRadius = r75;
        _productView.layer.masksToBounds = YES;
        UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
        _productView.backgroundColor = color;
    }
    return _productView;
}

- (UIImageView *)productImageView {
    
    if (!_productImageView) {
        UIImage* placeHoldImg = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
        _productImageView = [[UIImageView alloc] initWithImage:placeHoldImg];
    }
    return _productImageView;
}

- (KALabel *)labelSellOut {
    if (!_labelSellOut) {
        _labelSellOut = [KALabel kaLabelWithType:KALabelTypeL8 title:ShopingCartL(@"ka_cart_product_no_stock_short") cornerRadius:25];
        _labelSellOut.userInteractionEnabled = NO;
    }
    return _labelSellOut;
}

@end
