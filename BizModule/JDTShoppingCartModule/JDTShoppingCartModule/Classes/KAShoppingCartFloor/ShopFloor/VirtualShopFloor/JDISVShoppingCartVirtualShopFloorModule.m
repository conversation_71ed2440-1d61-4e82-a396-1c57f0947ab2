//
//  JDISVShoppingCartVirtualShopFloorModule.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2022/3/8.
//

#import "JDISVShoppingCartVirtualShopFloorModule.h"
#import <JDISVYYModelModule/YYModel.h>
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import "JDISVShoppingCartMainModel.h"
#import <JDISVFloorRenderModule/JDISVFloorCommonModel.h>
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
#import "KAShoppingCartProductModel.h"
#import "CARTMainModel.h"

JDISVRegisterVirtualFloorModule(KaCartShopFloor, JDISVShoppingCartVirtualShopFloorModule)

@interface JDISVShoppingCartVirtualShopFloorModule()
@property (weak,nonatomic) JDISVFloorCommonModel* commonModel;
@end

@implementation JDISVShoppingCartVirtualShopFloorModule

-(void)addProductToshop:(JDISVShoppingCartItemModel*)productModel
                    ext:(NSDictionary*)ext
             parentItem:(JDISVShoppingCartItemModel*)parentItem
                forShop:(NSMutableArray*)shopList
           forAppendant:(NSMutableArray*)appendGroup
        forBuygiftGroup:(NSMutableArray*)buygiftGroup
               firstPos:(NSInteger*)firstPosPoint{
    if([productModel.type.nameSpace containsString:@"#cartView.appendant-M#promotionCheapBuyModel"]){
        NSMutableDictionary *addByProduct = [NSMutableDictionary dictionary];
        [addByProduct setObject:@"kShopProductAddbyFloor" forKey:@"floorId"];
        [addByProduct setObject:productModel forKey:@"data"];
        [addByProduct setObject:ext forKey:@"ext"];
        if(parentItem){
            [addByProduct setObject:parentItem forKey:@"parentItem"];
        }
        [appendGroup addObject:addByProduct];
        
    }else{
        NSMutableDictionary *product = [NSMutableDictionary dictionary];
        [product setObject:@"kProductFloor" forKey:@"floorId"];
        [product setObject:productModel.itemDict forKey:@"data"];
        [product setObject:ext forKey:@"ext"];
        if(parentItem){
            [product setObject:parentItem forKey:@"parentItem"];
        }
        [shopList addObject:product];
        [self isForeignProduct:productModel];
        {
            // 赠品
            BOOL isFirst = YES;
            for(JDISVShoppingCartItemModel* buygift in productModel.subList){
                NSMutableDictionary *giftProduct = [NSMutableDictionary dictionary];
                [giftProduct setObject:@"KaCartBuygiftFloor" forKey:@"floorId"];
                [giftProduct setObject:buygift forKey:@"data"];
                [giftProduct setObject:ext forKey:@"ext"];
                if(parentItem){
                    [giftProduct setObject:productModel forKey:@"parentItem"];
                }
                [giftProduct setObject:parentItem forKey:@"superParentItem"];
                if(isFirst){
                    giftProduct[@"isFirst"] = @(YES);
                    isFirst = NO;
                }
                [shopList addObject:giftProduct];
            }
            
        }
        if(*firstPosPoint == 0){
            *firstPosPoint = shopList.count;
        }
    }
}

-(void) decodeJDISVShoppingCartItemModel:(JDISVShoppingCartItemModel*)subItemModel
                                     ext:(NSDictionary*)ext
                             fartherItem:(JDISVShoppingCartItemModel*)parentItem
                                 forShop:(NSMutableArray*)shopList
                            forAppendant:(NSMutableArray*)appendGroup
                         forBuygiftGroup:(NSMutableArray*)buygiftGroup
                                firstPos:(NSInteger*)firstPosPoint{
    if ([subItemModel.type.nameSpace containsString:@"cartView.promotion-M#defaultPromotionItem"]) {
        for (JDISVShoppingCartItemModel *productModel in subItemModel.subList) {
            [self addProductToshop:productModel
                               ext:ext
                        parentItem:subItemModel
                           forShop:shopList
                      forAppendant:appendGroup
                   forBuygiftGroup:buygiftGroup
                          firstPos:firstPosPoint];
        }
    }else {
        [self addProductToshop:subItemModel
                           ext:ext
                    parentItem:parentItem
                       forShop:shopList
                  forAppendant:appendGroup
               forBuygiftGroup:buygiftGroup
                      firstPos:firstPosPoint];
    }
}

-(void)addVirturlSuit:(JDISVShoppingCartItemModel*) vsProduct
                 parentItem:(JDISVShoppingCartItemModel*)parentItem
                    forShop:(NSMutableArray*)shopList
                  ext:(NSDictionary*)ext{
    NSMutableDictionary *vs = [NSMutableDictionary dictionary];
    [vs setObject:@"kShopVirtualSuitFloor" forKey:@"floorId"];
    [vs setObject:vsProduct forKey:@"data"];
    [vs setObject:ext forKey:@"ext"];
    if(parentItem){
        [vs setObject:parentItem forKey:@"parentItem"];
    }
    [shopList addObject:vs];
}
-(void)isForeignProduct:(JDISVShoppingCartItemModel*) product{
    KAShoppingCartProductModel *infoModel = [KAShoppingCartProductModel yy_modelWithDictionary:product.info];
    if ([infoModel.cartProductExtInfo.enableCheck boolValue]) {
        if ([infoModel.productItemBasic.activeCheck boolValue]) {
            NSDictionary* extDic =[product.info jdcd_getDicElementForKey:@"RCB2C-D#productDecorator&extInfo"];
            NSArray* markSet = [extDic jdcd_getArrayElementForKey:@"productMarkSet"];
            if([markSet containsObject:@(36) ]){
                self.commonModel.commonData[@"hasForeignProduct"] = @(YES);
            }
        }
    }
}

- (NSArray<NSDictionary *> *)realFloorsWithData:(NSDictionary *)data ext:(NSDictionary *)ext commond:(JDISVFloorCommonModel *)commonModel {
    self.commonModel = commonModel;
    commonModel.commonData[@"hasForeignProduct"] = @(NO);
    NSDictionary *responseObject = data;
    if (nil == ext) {
        ext = @{};
    }
    
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    CARTMainModel *cartMainModel = [CARTMainModel yy_modelWithDictionary:responseObject];
    
    NSMutableArray *shopList = [NSMutableArray array];
    for (CARTMainVenderModel *venderModel in cartMainModel.venderList) {
        NSMutableDictionary *shopHeader = [NSMutableDictionary dictionary];
        [shopHeader setObject:@"kShopHeaderFloor" forKey:@"floorId"];
        [shopHeader setObject:venderModel forKey:@"data"];
//        [shopHeader setObject:ext forKey:@"ext"];
        [shopList addObject:shopHeader];
        for (CARTMainSkuItemModel *skuItem in venderModel.skuItems) {
            NSMutableDictionary *product = [NSMutableDictionary dictionary];
            [product setObject:@"kProductFloor" forKey:@"floorId"];
            [product setObject:skuItem forKey:@"data"];
//            [product setObject:ext forKey:@"ext"];
            [shopList addObject:product];
        }
        NSMutableDictionary *deliver = [NSMutableDictionary dictionary];
        [deliver setObject:@"kDeliverFloor" forKey:@"floorId"];
        [deliver setObject:@{@"showTop":@(1),@"showBottom":@(1)} forKey:@"data"];
//        [deliver setObject:ext forKey:@"ext"];
        [shopList addObject:deliver];
    }
    NSDictionary *dict = shopList.lastObject;
    if ([dict[@"floorId"] isEqualToString:@"kDeliverFloor"]) {
        [shopList removeLastObject];
    }
    return shopList;
    
    
//    for (JDISVShoppingCartItemModel *itemModel in mainModel.rootHierarchy.subList) {
//        NSString *nameSpace = @"core.trade-DM#cartView.group-M#defaultGroupItem";
//        if ([itemModel.type.nameSpace isEqualToString:nameSpace]) {
//            NSMutableDictionary *shopHeader = [NSMutableDictionary dictionary];
//            [shopHeader setObject:@"kShopHeaderFloor" forKey:@"floorId"];
//            [shopHeader setObject:itemModel.itemDict forKey:@"data"];
//            [shopHeader setObject:ext forKey:@"ext"];
//            [shopList addObject:shopHeader];
//            
//            for (JDISVShoppingCartItemModel *subItemModel in itemModel.subList) {
//                NSMutableArray* appandgroup = [NSMutableArray array];
//                NSMutableArray* buygiftgroup = [NSMutableArray array];
//                
//                if([subItemModel.type.nameSpace containsString:@"cartView.promotion-M#defaultPromotionItem"]){
//                    NSDictionary* suit =  subItemModel.info[@"HJM-D#virtualSuit&virtualSuit"];
//                    if(suit){
//                        [self addVirturlSuit:subItemModel parentItem:itemModel forShop:shopList ext:ext];
//                        continue;
//                    }
//                    NSMutableDictionary *shopAddbyHeader = [NSMutableDictionary dictionary];
//                    [shopAddbyHeader setObject:@"kShopAddbyFloor" forKey:@"floorId"];
//                    [shopAddbyHeader setObject:subItemModel forKey:@"data"];
//                    [shopAddbyHeader setObject:ext forKey:@"ext"];
//                    [shopAddbyHeader setObject:data forKey:@"originalDic"];
//                    [shopList addObject:shopAddbyHeader];
//                    NSInteger firstProductPos = 0;
//                    [self decodeJDISVShoppingCartItemModel:subItemModel
//                                                       ext:ext
//                                               fartherItem:itemModel
//                                                   forShop:shopList
//                                              forAppendant:appandgroup
//                                           forBuygiftGroup:buygiftgroup
//                                                  firstPos:&firstProductPos];
//                    if(appandgroup.count && firstProductPos){
//                        NSRange range = NSMakeRange(firstProductPos, [appandgroup count]);
//                        NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:range];
//                        [shopList insertObjects:appandgroup atIndexes:indexSet];
//                    }
//                    if(buygiftgroup.count && firstProductPos){
//                        NSRange range = NSMakeRange(firstProductPos, [buygiftgroup count]);
//                        NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:range];
//                        [shopList insertObjects:buygiftgroup atIndexes:indexSet];
//                    }
//                }else{
//                    NSInteger firstProductPos = 0;
//                    [self decodeJDISVShoppingCartItemModel:subItemModel
//                                                       ext:ext
//                                               fartherItem:itemModel
//                                                   forShop:shopList
//                                               forAppendant:appandgroup
//                                           forBuygiftGroup:buygiftgroup
//                                                  firstPos:&firstProductPos];
//                }
//            }
//            
//            NSMutableDictionary *deliver = [NSMutableDictionary dictionary];
//            [deliver setObject:@"kDeliverFloor" forKey:@"floorId"];
//            [deliver setObject:@{@"showTop":@(1),@"showBottom":@(1)} forKey:@"data"];
//            [deliver setObject:ext forKey:@"ext"];
//            [shopList addObject:deliver];
//        }
//    }
//    
//    NSDictionary *dict = shopList.lastObject;
//    if ([dict[@"floorId"] isEqualToString:@"kDeliverFloor"]) {
//        [shopList removeLastObject];
//    }
//    return shopList;
}

@end
