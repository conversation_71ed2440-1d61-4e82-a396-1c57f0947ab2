//
//  KAShoppingCartProductModule.h
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/23.
//  Copyright © 2021 罗静. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVFloorRenderModule/JDISVFloorModuleProtocol.h>
#import "CARTMainModel.h"

@class  KAShoppingCartProductModel;
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, KAShoppingCartProductSelectedStatus) {
    KAShoppingCartProductSelectedStatusUnSelected, ///< 未选中
    KAShoppingCartProductSelectedStatusSelected, ///< 选中
    KAShoppingCartProductSelectedStatusDisenable, ///< 没有选中能力
};

@class KAShoppingCartProductPartModel;
@class KAShoppingCartProductSlashModel;
@class JDISVShoppingCartItemModel;

@protocol JDISVFloorViewProtocol;

FOUNDATION_EXPORT JDCDISVActionType const KAShoppingCartProductCollectionAction;
FOUNDATION_EXPORT JDCDISVActionType const KAShoppingCartProductDeleteAction;

           
@interface KAShoppingCartProductModule : NSObject<JDISVFloorModuleProtocol>
@property (nonatomic,weak) id<JDISVFloorViewProtocol> cell;
@property (nonatomic, assign, getter=isEdit) BOOL edit;

@property (nonatomic, copy) NSString *skuId;

//@property (nonatomic, strong) NSNumber *itemType;

@property (nonatomic, assign) KAShoppingCartProductSelectedStatus selectedStatus;
@property (nonatomic, assign) KAShoppingCartProductSelectedStatus orignalSelectedStatus;

@property (nonatomic, assign) BOOL selected;
@property (nonatomic, assign) BOOL selectedEnable;

@property (nonatomic, copy) NSString *coverImageUrl;

@property (nonatomic, assign) BOOL inStock;

@property (nonatomic, copy) NSString *stockNum;
@property (nonatomic, assign) NSInteger stockCount;
@property (nonatomic, assign) BOOL showStockNum;

@property (nonatomic, copy) NSString *productName;

@property (nonatomic, copy) NSString *property;

@property (nonatomic, assign) CGFloat tagImageWidth;
@property (nonatomic, assign) CGFloat tagImageRight;

@property (nonatomic, copy) NSString *price;

@property (nonatomic, assign) NSInteger count;

@property (nonatomic, assign) NSInteger maxCount;

@property (nonatomic, assign) NSInteger minCount;

@property (nonatomic, copy) NSString *priceColor;


@property (nonatomic, assign) BOOL showProperty;
@property (nonatomic, assign) BOOL showMoreProperty;
//@property (nonatomic, assign) BOOL showPromotion;
//@property (nonatomic, assign) BOOL showSlash;

@property (nonatomic, assign) CGFloat priceTopMargin;
@property (nonatomic, assign) CGFloat secKilltopMargin;

@property (nonatomic,assign) CGFloat cellHeight;

@property (nonatomic,assign) BOOL isShip;//跨境

@property (nonatomic, assign) NSInteger secKill;//秒杀秒数
//@property (nonatomic, assign) BOOL preSale; //预售大于秒杀 大于砍价

@property (nonatomic, copy, nullable) NSString *slashPrice;

//@property (strong,nonatomic) KAShoppingCartProductModel* model;
@property (nonatomic, strong) CARTMainSkuItemModel *skuItem;
//@property (nonatomic, copy) NSString *udid;
//@property(weak,nonatomic) JDISVShoppingCartItemModel* parentItem;

@property (strong,nonatomic) NSString* limitInfoText;

- (void)editSaveToStore:(BOOL)save;

- (JDCDISVAction *)selectAction;

- (JDCDISVAction *)deleteAction;

- (JDCDISVAction *)collectionAction;

- (JDCDISVAction *)changeProductCountActionWithCount:(NSInteger)count;

- (JDCDISVAction *)changeProductActionWithSkuId:(NSString *)skuId;

//- (JDCDISVAction *)changePromotionActionWithNewId:(NSString *)newId
//                                      newItemType:(NSString*)newType
//                                            oldId:(NSString*)oldId
//                                      oldItemType:(NSString*)oldType;
@end

NS_ASSUME_NONNULL_END
