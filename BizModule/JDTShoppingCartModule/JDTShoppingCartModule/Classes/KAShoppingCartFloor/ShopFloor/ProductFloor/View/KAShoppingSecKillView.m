//
//  KAShoppingSecKillView.m
//  JDISVShoppingCartSDKModule
//
//  Created by cdwutao3 on 2022/7/22.
//

#import "KAShoppingSecKillView.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVKAUIKitModule/JDISVTimerView.h>
#import <JDISVKAUIKitModule/KSATimerView.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import "JDISVShoppingCartTool.h"
#import "JDISVShoppingCarPub.h"
#import <JDISVKAIconFontModule/UIImage+KAIconFont.h>

@interface KAShoppingSecKillView()
@property (strong,nonatomic) UILabel *dayLabel;
@property (strong,nonatomic) JDISVTimerView *timeView;
@property (strong,nonatomic) KSATimerView* ksaTimer;
@property (strong,nonatomic) UIImageView *imageView;

@end

@implementation KAShoppingSecKillView
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:CGRectZero];
    if(self){
        [self setupUI];
    }
    return self;
}

-(void)setupUI{
    self.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30" alpha:0.07];
    
    self.imageView = [[UIImageView alloc] initWithFrame:CGRectZero];
    UIImage*img = [UIImage isv_shoppingCart_imageWithName:@"ksa_secKill"];
    self.imageView.image = img;
    [self.imageView setTintColor:[[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"]];
    [self addSubview:self.imageView];
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self).mas_offset(5);
        make.centerY.mas_equalTo(self);
        make.width.mas_equalTo(12);
        make.height.mas_equalTo(12);
    }];
    self.dayLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    [self addSubview:self.dayLabel];
    [self.dayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.imageView.mas_trailing).mas_offset(4);
        make.centerY.mas_equalTo(self);
        make.height.mas_equalTo(self);
    }];
    self.dayLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
    self.dayLabel.font = [UIFont systemFontOfSize:10];
    
    
    if(KSAAPP){
        self.ksaTimer = [[KSATimerView alloc] initWithFrame:CGRectZero];
        [self addSubview:self.ksaTimer];
        self.ksaTimer.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30" alpha:0.15];
        self.ksaTimer.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
        self.ksaTimer.spaceColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C30"];
        
        //JH底色为白色
        self.ksaTimer.textBackgroundColor = UIColor.whiteColor;
        
        [self.ksaTimer mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.dayLabel.mas_trailing).mas_offset(4);
            make.height.mas_equalTo(14);
            make.width.mas_equalTo(63);
            make.centerY.mas_equalTo(self);
        }];
    }else{
        
        self.timeView = [[JDISVTimerView alloc] initWithFrame:CGRectZero itemCount:3];
        [self addSubview:self.timeView];
        self.timeView.borderColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9" alpha:0.15];
        //JH底色为白色
        self.timeView.textBackgroundColor = UIColor.whiteColor;
        
        [self.timeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.dayLabel.mas_trailing).mas_offset(4);
            make.height.mas_equalTo(14);
            make.width.mas_equalTo(63);
            make.centerY.mas_equalTo(self);
        }];
    }
    
    
}

-(void)updateLeaftSec:(NSInteger)sec{
    NSArray* texts = [self SecKillWithTime:sec];
    if(KSAAPP){
        self.dayLabel.text = texts[0];
        CGFloat width = 0;
        if(texts.count == 5){
            width = [self.ksaTimer updateDay:texts[1] hour:texts[2] min:texts[3] sec:texts[4] height:14];
        }else{
            width = [self.ksaTimer updateHour:texts[1] min:texts[2] sec:texts[3] height:14];
        }
        [self.ksaTimer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(width);
        }];
    }else{
        self.dayLabel.text = texts[0];
        [self.timeView updateHour:texts[1] min:texts[2] sec:texts[3]];
    }
}

-(NSArray*)SecKillWithTime:(NSInteger)time{
    NSInteger day =  time/60/60/24;
    time  -= (day*24*60*60);
    
    NSInteger hour =  time/60/60;
    time -= hour*60*60;
    
    NSInteger min =  time/60;
    time -= min*60;
    
    NSInteger sec =  time;
    NSString* dayStr;
    if(KSAAPP){
        NSArray* result;
        if(day>0){
            dayStr = ShopingCartL(@"ka_cart_soper_deal_end_title");
            NSString* daydesc = ShopingCartL(@"ka_cart_soper_deal_day");
            result = @[dayStr,
                       [NSString stringWithFormat:daydesc,day],
                       [NSString stringWithFormat:@"%02d",hour],
                       [NSString stringWithFormat:@"%02d",min],
                       [NSString stringWithFormat:@"%02d",sec]];
        }else{
            dayStr = ShopingCartL(@"ka_cart_soper_deal_end_title");
            result = @[dayStr,
                       [NSString stringWithFormat:@"%02d",hour],
                       [NSString stringWithFormat:@"%02d",min],
                       [NSString stringWithFormat:@"%02d",sec]];
        }
        return result;
    }else{
        if(day>0){
            dayStr = [NSString stringWithFormat:ShopingCartL(@"ka_cart_soper_deal_end_day"), @(day)];
        }else{
            dayStr = ShopingCartL(@"ka_cart_soper_deal_end_title");
        }
        return @[dayStr,
                 [NSString stringWithFormat:@"%02d",hour],
                 [NSString stringWithFormat:@"%02d",min],
                 [NSString stringWithFormat:@"%02d",sec]];
    }
    
   
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
