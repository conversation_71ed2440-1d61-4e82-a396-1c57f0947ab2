//
//  KAShoppingCartProductModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/10/18.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class KAShoppingCartProductModel;
@class KAShoppingCartProductBasicModel;
@class KAShoppingCartProductExtInfoModel;
@class KAShoppingCartProductStockInfoModel;
@class KAShoppingCartProductCoreModel;
@class KAShoppingCartProductInfoModel;
@class KAShoppingCartProductPartModel;
@class KAShoppingCartProductSlashCoreModel;
@class KAShoppingCartProductSlashModel;
@class KAShoppingCartProductSecKill;
@class  KAShoppingCartPromosion;

@interface KAShoppingCartProductResultModel : NSObject

@property (nonatomic, strong) NSDictionary *type;

@property (nonatomic, copy) NSString *uuid;

@property (nonatomic, strong) KAShoppingCartProductModel *info;

@property (nonatomic, strong) NSArray <KAShoppingCartProductPartModel *>*partList;

@end

@interface KAShoppingCartProductModel : NSObject

@property (nonatomic, strong) KAShoppingCartProductBasicModel *productItemBasic;

@property (nonatomic, strong) KAShoppingCartProductExtInfoModel *cartProductExtInfo;

@property (nonatomic, strong) KAShoppingCartProductStockInfoModel *cartProductStockInfo;

@property (nonatomic, strong) KAShoppingCartProductCoreModel *productItemCore;
///  砍价
@property (nonatomic, strong) KAShoppingCartProductSlashModel *slashModel;
///  满减
@property (nonatomic, strong) NSArray <KAShoppingCartPromosion*> *promosions;
///  秒杀
@property (nonatomic, strong) KAShoppingCartProductSecKill *secKillModel;

@end

@interface KAShoppingCartProductBasicModel : NSObject

@property (nonatomic, copy) NSString *skuName;

@property (nonatomic, copy) NSString *imgUrl;

@property (nonatomic, strong) NSNumber *thirdCId;

@property (nonatomic, copy) NSString *price;

@property (nonatomic, strong) NSNumber *firstCid;

@property (nonatomic, copy) NSString *num;

@property (nonatomic, strong) NSNumber *activeCheck;

@property (nonatomic, strong) NSNumber *secondCid;

@end

@interface KAShoppingCartProductExtInfoModel : NSObject

@property (nonatomic, copy) NSString *overseaPurchaseFlag;
@property (nonatomic, strong) NSNumber *enableCheck;
@property (nonatomic, strong) NSNumber *itemType;
@property (nonatomic, strong) NSNumber *provideServiceFlag;
@property (nonatomic, copy) NSString *discount;
@property (nonatomic, copy) NSString *weight;
@property (nonatomic, copy) NSString *rePrice;
@property (nonatomic, copy) NSString *supplierCode;
@property (nonatomic, copy) NSString *imgDomain;
@property (nonatomic, copy) NSString *maxBuyNum;
@property (nonatomic, copy) NSString *minBuyNum;
@property (nonatomic, copy) NSString *revertPrice;
@property (nonatomic, copy) NSString *priceShow;
@property (nonatomic, copy) NSString *addCartPrice;
@property (nonatomic, strong) NSDictionary *iconMap;
@property (nonatomic, copy) NSString *specialId;
@property (nonatomic, strong) NSDictionary *propertyTagMap;
@property (nonatomic, copy) NSString *brandId;
@property (nonatomic, strong) NSNumber *addCartTime;


@end

@interface KAShoppingCartProductStockInfoModel : NSObject

@property (nonatomic, strong) NSNumber *remainNum;
@property (nonatomic, strong) NSNumber *stockType;
@property (nonatomic, copy) NSString *stockCodeDesc;

/**
 0：有货

 1：无货

 2：预定
 */
@property (nonatomic, strong) NSNumber *stockCode;

@end

@interface KAShoppingCartProductCoreModel : NSObject

@property (nonatomic, copy) NSString *skuId;

@end

@interface KAShoppingCartProductPartModel : NSObject

@property (nonatomic, strong) KAShoppingCartProductInfoModel *type;

@property (nonatomic, strong) NSDictionary *info;

@end

@interface KAShoppingCartProductInfoModel : NSObject

@property (nonatomic, copy) NSString *nameSpace;

@property (nonatomic, copy) NSString *version;

@end

@interface KAShoppingCartProductSlashModel : NSObject

@property (nonatomic, strong) KAShoppingCartProductSlashCoreModel *slashCore;

@end

@interface KAShoppingCartProductSlashCoreModel : NSObject

@property (nonatomic, copy) NSString *ID;

@property (nonatomic, copy) NSString *priceType;

@property (nonatomic, copy) NSString *title;

@property (nonatomic, copy) NSString *price;

@property (nonatomic, copy) NSString *priceIcon;

@property (nonatomic, copy) NSString *priceColor;

@property (nonatomic, assign) NSInteger activeCheck;

@end

@interface KAShoppingCartPromosion : NSObject
@property (assign,nonatomic) NSInteger mfmzFullRefundType;
@property (nonatomic, copy) NSString *promoType;
@property (assign,nonatomic) BOOL activeCheck;
@property (nonatomic, copy) NSString *promotionId;
@property (nonatomic, copy) NSString *title;
                        
@end

@interface KAShoppingCartProductSecKill : NSObject
@property (nonatomic, assign) BOOL seckillTag;
@property (nonatomic, assign) NSInteger secKillEndRemainTime;

@end

NS_ASSUME_NONNULL_END
