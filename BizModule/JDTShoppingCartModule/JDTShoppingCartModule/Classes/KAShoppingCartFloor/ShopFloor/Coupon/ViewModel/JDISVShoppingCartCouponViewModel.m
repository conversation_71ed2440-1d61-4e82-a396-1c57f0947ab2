//
//  JDISVShoppingCartCouponViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/11/9.
//

#import "JDISVShoppingCartCouponViewModel.h"

#import "JDSIVShoppingCartCouponItemViewModel.h"

#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVYYModelModule/YYModel.h>

#import "JDISVShoppingCartTool.h"

#import "JDISVShoppingCartMainModel.h"

#import "JDISVShoppingCartCouponModel.h"
#import "CARTCouponModel.h"

@import JDTInfrastructureModule;

@implementation JDISVShoppingCartCouponViewModel

- (RACSignal *)loadShoppingCartCouponData {
    @weakify(self);
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self);
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        [params setObject:@"listCoupon" forKey:@"userActionId"];
        if([self.venderId intValue]){
            [params setObject:self.venderId forKey:@"venderId"];
        }
//        [PlatformService isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_executeAction" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self);
//            if (!error) {
//                [self progressCoupons:responseObject];
//                [subscriber sendNext:self.coupons];
//            }else {
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSDictionary *fixParams = @{
            @"commonParam": @{
                @"isReturnCart": @"1",
                @"addressId": @""
            },
            @"lbsParam": @{
                @"coordinateParam": @{
                    @"longitude": @(116.36685),
                    @"latitude": @(39.94031)
                },
                @"provinceId": @"110000",
                @"cityId": @"110100",
                @"districtId": @"110102",
                @"townId": @"110102003",
                @"fullAddress": @"",
                @"provinceName": @"北京市",
                @"cityName": @"北京市",
                @"districtName": @"西城区",
                @"townName": @"新街口街道",
                @"latitude": @(39.94031),
                @"longitude": @(116.36685)
            }
        };
        NSMutableDictionary *testParams = [NSMutableDictionary dictionaryWithDictionary:fixParams];
        testParams[@"shopId"] = self.shopId;
        
        [[OOPNetworkManager sharedManager] POST:@"cart/c/getCoupons?apiCode=b2c.cbff.cart.c.getCoupons" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                NSDictionary *data = responseObject[@"data"];
                [self progressCoupons2:data];
                [subscriber sendNext:self.coupons];
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
    return signal;
}

- (void)progressCoupons2:(NSDictionary *)responseObject {
    CARTCouponModel *model = [CARTCouponModel yy_modelWithDictionary:responseObject];
    
    NSMutableArray *tempRevices = [NSMutableArray array];
    NSMutableArray *tempNoRevices = [NSMutableArray array];
    
    for (JDTCouponItemModel *coupon in model.canObtainedList) {
        JDSIVShoppingCartCouponItemViewModel *itemViewModel = [[JDSIVShoppingCartCouponItemViewModel alloc] init];
        itemViewModel.shopId = self.shopId;
        itemViewModel.venderId = self.venderId;
        itemViewModel.originHasRevices = NO;
//        [itemViewModel updateWithModel:coupon];
        [tempNoRevices addObject:itemViewModel];
    }
    
    JDISVShoppingCartSectionViewModel *revicesViewModel = [[JDISVShoppingCartSectionViewModel alloc] init];
    revicesViewModel.title = ShopingCartL(@"ka_cart_coupon_not_receive_label");
    
    if (tempRevices.count) {
        revicesViewModel.headerHeight = 50;
    }else {
        revicesViewModel.headerHeight = CGFLOAT_MIN;
    }
    revicesViewModel.items = [tempRevices copy];
    
    for (JDTCouponItemModel *coupon in model.obtainedList) {
        JDSIVShoppingCartCouponItemViewModel *itemViewModel = [[JDSIVShoppingCartCouponItemViewModel alloc] init];
        itemViewModel.shopId = self.shopId;
        itemViewModel.venderId = self.venderId;
        itemViewModel.originHasRevices = YES;
//        [itemViewModel updateWithModel:coupon];
        [tempRevices addObject:itemViewModel];
    }
    
    JDISVShoppingCartSectionViewModel *noRevicesViewModel = [[JDISVShoppingCartSectionViewModel alloc] init];
    noRevicesViewModel.title = ShopingCartL(@"ka_cart_coupon_have_receive_label");
    if (tempNoRevices.count) {
        noRevicesViewModel.headerHeight = 50;
    }else {
        noRevicesViewModel.headerHeight = CGFLOAT_MIN;
    }
    noRevicesViewModel.items = [tempNoRevices copy];
    
    self.coupons = @[noRevicesViewModel, revicesViewModel];
}

- (void)progressCoupons:(NSDictionary *)responseObject {
    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
//
    NSMutableArray *tempRevices = [NSMutableArray array];
    NSMutableArray *tempNoRevices = [NSMutableArray array];
    NSMutableArray* tempCouponCodes = [NSMutableArray array];
    for (JDISVShoppingCartItemModel *itemModel in mainModel.rootHierarchy.subList) {
        JDSIVShoppingCartCouponItemViewModel *itemViewModel = [[JDSIVShoppingCartCouponItemViewModel alloc] init];

        JDISVShoppingCartCouponModel *couponModel = [JDISVShoppingCartCouponModel yy_modelWithDictionary:itemModel.info];

        itemViewModel.shopId = self.shopId;
        itemViewModel.venderId = self.venderId;
        if(couponModel.basicModel.couponStyle.intValue == 39 ){
            [itemViewModel updateWithModel:couponModel];
            [tempCouponCodes addObject:itemViewModel];
        }else if (couponModel.basicModel.hasReceive) {
            itemViewModel.originHasRevices = YES;
            [itemViewModel updateWithModel:couponModel];
            [tempRevices addObject:itemViewModel];
        }else {
            itemViewModel.originHasRevices = NO;
            [itemViewModel updateWithModel:couponModel];
            [tempNoRevices addObject:itemViewModel];
        }
    }
    
    JDISVShoppingCartSectionViewModel *revicesViewModel = [[JDISVShoppingCartSectionViewModel alloc] init];
    revicesViewModel.title = ShopingCartL(@"ka_cart_coupon_not_receive_label");
    
    if (tempRevices.count) {
        revicesViewModel.headerHeight = 50;
    }else {
        revicesViewModel.headerHeight = CGFLOAT_MIN;
    }
    revicesViewModel.items = [tempRevices copy];
    
    JDISVShoppingCartSectionViewModel *noRevicesViewModel = [[JDISVShoppingCartSectionViewModel alloc] init];
    noRevicesViewModel.title = ShopingCartL(@"ka_cart_coupon_have_receive_label");
    if (tempNoRevices.count) {
        noRevicesViewModel.headerHeight = 50;
    }else {
        noRevicesViewModel.headerHeight = CGFLOAT_MIN;
    }
    noRevicesViewModel.items = [tempNoRevices copy];
    
    JDISVShoppingCartSectionViewModel *couponCodeSec = [[JDISVShoppingCartSectionViewModel alloc] init];
    if(tempCouponCodes.count){
        couponCodeSec.headerHeight = 50;
    }else{
        couponCodeSec.headerHeight = CGFLOAT_MIN;
    }
    
    couponCodeSec.title = ShopingCartL(@"ka_cart_coupon_codes");
    couponCodeSec.items = [tempCouponCodes copy];
    self.coupons = @[noRevicesViewModel, revicesViewModel,couponCodeSec];
    
}

@end
