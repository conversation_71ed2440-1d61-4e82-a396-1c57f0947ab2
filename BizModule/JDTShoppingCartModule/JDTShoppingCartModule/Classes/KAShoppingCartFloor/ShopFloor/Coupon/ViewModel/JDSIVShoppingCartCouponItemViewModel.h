//
//  JDSIVShoppingCartCouponItemViewModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/11/9.
//

#import <UIKit/UIKit.h>
#import <JDISVPlatformModule/JDISVCouponTagModel.h>
@import JDTCommonToolModule.JDTCouponItemModel;

NS_ASSUME_NONNULL_BEGIN

@class JDISVShoppingCartCouponModel;
@class RACSignal;

@interface JDSIVShoppingCartCouponItemViewModel : NSObject

@property (strong,nonatomic) NSNumber* venderId;
@property (strong,nonatomic) NSNumber* shopId;
@property (strong,nonatomic) NSString* shopName;

@property (nonatomic, strong) NSNumber *couponId;
@property (nonatomic, strong) NSNumber *realCouponId;

@property (nonatomic, strong) NSNumber *batchId;

@property (nonatomic, strong) NSNumber *couponKind;
@property (nonatomic, strong) NSNumber *couponType;

@property (nonatomic, copy) NSString *activityId;

@property (nonatomic, copy) NSString *activityKey;

@property (nonatomic, assign) BOOL originHasRevices;
@property (nonatomic, assign) BOOL hasRevices;

@property (nonatomic, assign) CGFloat rowHeight;

@property (nonatomic, assign) CGFloat actionButtonWidth;

@property (nonatomic, assign) CGFloat titleLabelRight;

@property (nonatomic, assign) CGFloat price;

@property (nonatomic, copy) NSString *title;

@property (nonatomic, copy) NSString *decrible;
@property (nonatomic, assign) CGFloat decribleMaxSpace;

@property (nonatomic, copy) NSString *time;

@property (nonatomic, copy) NSString *actionTitle;

@property (nonatomic, copy) NSString *actionTitleColor;

@property (nonatomic, assign) BOOL showRevicesTag;

//优惠码
@property (nonatomic, copy) NSString* discountCode;
@property (nonatomic, copy) NSString* ticketHight;
@property (nonatomic, copy) NSString* ticketFull;
@property (nonatomic, assign) BOOL isTicket;

@property (nonatomic,strong) JDISVCouponTagModel* couponTag;
//领取tag
@property (nonatomic, copy) NSString *reviceTagColor;

@property (nonatomic, assign) BOOL infoEnable;

- (void)updateWithModel:(nullable JDISVShoppingCartCouponModel *)model;
- (void)updateWithModel2:(nullable JDTCouponItemModel *)model;

- (NSAttributedString *)infoAttributeWithImage:(UIImageView *)imageV color:(UIColor *)color;

- (RACSignal *)revicesCoupons;

- (void)changeRevicesStatus:(BOOL)hasReceive;

- (CGFloat)rowHeightWithWidth:(CGFloat)width space:(CGFloat)space font:(nonnull UIFont *)font;

@end

@interface JDISVShoppingCartSectionViewModel : NSObject

@property (nonatomic, strong) NSString *title;

@property (nonatomic, assign) CGFloat headerHeight;

@property (nonatomic, strong) NSArray *items;

@end

NS_ASSUME_NONNULL_END
