//
//  JDSIVShoppingCartCouponItemViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/11/9.
//

#import "JDSIVShoppingCartCouponItemViewModel.h"

#import <JDISVYYTextModule/YYText.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>

#import <JDISVCategoryModule/NSString+JDCDExtend.h>

#import "JDISVShoppingCartCouponModel.h"
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
#import "JDISVShoppingCartTool.h"
#import "CARTCouponModel.h"

@implementation JDSIVShoppingCartCouponItemViewModel

- (void)updateWithModel:(JDISVShoppingCartCouponModel *)model {
    
    self.rowHeight = 108;
    
    self.couponId = model.coreModel.Id;
    self.realCouponId = model.basicModel.couponId;
    
    self.activityId = model.activityModel.activityId;
    self.activityKey = model.activityModel.activityKey;
    self.couponKind = model.basicModel.couponKind;
    if(model.basicModel.couponStyle.intValue == 39){
        self.isTicket = YES;
        if(KSAAPP){
            self.discountCode = model.ksaBaseModel.discountCode;
            self.ticketHight = model.ksaDiscountModel.highDiscount;
            self.ticketFull = model.ksaDiscountModel.fullDiscount;
        }else{
            self.discountCode = model.basicModel.discountCode;
            self.ticketHight = model.discountModel.high;
        }
        self.couponTag =
        [JDISVCouponTagModel createCodeWithCouponType:model.basicModel.couponType.integerValue
                                           couponKind:model.basicModel.couponKind.integerValue
                                              batchId:self.couponId
                                             couponId:self.realCouponId
                                               shopId:self.shopId
                                             venderId:self.couponId
                                             shopName:self.shopName];
    }else{
        self.couponTag =
        [JDISVCouponTagModel createWithCouponType:model.basicModel.couponType.integerValue
                                       couponKind:model.basicModel.couponKind.integerValue
                                        userLabel:model.basicModel.userLabel
                                            state:2
                                          batchId:self.couponId
                                         couponId:self.realCouponId
                                           shopId:self.shopId
                                         venderId:self.couponId
                                         shopName:self.shopName];
    }
    
    //券状态
    [self changeRevicesStatus:model.basicModel.hasReceive];
    
    self.price = model.coreModel.discount.floatValue;
    
    self.title = [NSString stringWithFormat:ShopingCartL(@"ka_cart_quota"),model.basicModel.quota];
    
    self.decrible = model.coreModel.name;
    self.couponType = model.basicModel.couponType;
    
    self.time = [NSString stringWithFormat:@"%@-%@",model.basicModel.beginTime,model.basicModel.endTime];
    
}

- (void)updateWithModel2:(nullable JDTCouponItemModel *)model {
    self.rowHeight = 108;
    self.couponId = model.voucherId;
    self.realCouponId = model.voucherId;
    self.couponKind = @(model.scopeType);
    
    // TODO:Juice  后面优惠券模块再做
}

- (void)changeRevicesStatus:(BOOL)hasReceive {
    self.hasRevices = hasReceive;
    //券事件控制
    if (hasReceive) {
        self.actionButtonWidth = 67;
        self.titleLabelRight = 10;
        
        self.actionTitle = ShopingCartL(@"ka_cart_to_promotion");
        self.actionTitleColor = @"#C10-a";
    }else {
        self.actionButtonWidth = 80;
        self.titleLabelRight = 10;
        
        self.actionTitle = ShopingCartL(@"ka_cart_coupon_collect");
        self.actionTitleColor = @"#C1-b";
    }
    
    //优惠券状态
    if (self.originHasRevices == YES || hasReceive) {
        self.showRevicesTag = YES;
    }else {
        self.showRevicesTag = NO;
    }
    
    //描述信息frame
    if (hasReceive) {
        self.decribleMaxSpace = 118 + 85;
    }else {
        self.decribleMaxSpace = 118 + 98;
    }
}

- (CGFloat)rowHeightWithWidth:(CGFloat)width space:(CGFloat)space font:(nonnull UIFont *)font{
    
    CGFloat decWidht = width - 101 - space * 2 - self.actionButtonWidth - 12 - self.titleLabelRight - 18;
    CGSize size = [self.decrible jdcd_getStringSize:font constraintsSize:CGSizeMake(decWidht, 0)];
    CGFloat decHeight = ceilf(size.height);
    if (decHeight > 34) {
        decHeight = 34;
        self.infoEnable = YES;
    }else {
        self.infoEnable = NO;
    }
    CGFloat height = 18 * 2 + 22 + 17 + 2 + 2 + decHeight;
    return height;
}

- (NSAttributedString *)infoAttributeWithImage:(UIImageView *)imageV color:(nonnull UIColor *)color{
    NSMutableAttributedString *attString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@", @"..."] attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:12],NSForegroundColorAttributeName:color}];
    
    NSMutableAttributedString *info = [NSMutableAttributedString yy_attachmentStringWithContent:imageV contentMode:UIViewContentModeCenter attachmentSize:CGSizeMake(16, 16) alignToFont:[UIFont systemFontOfSize:12] alignment:YYTextVerticalAlignmentCenter];
    
    [attString appendAttributedString:info];
    
    return [attString copy];
}

- (RACSignal *)revicesCoupons {
    @weakify(self);
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self);
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        if (self.activityId) {
            [params setObject:self.activityId forKey:@"roleId"];
        }
        if (self.activityKey) {
            [params setObject:self.activityKey forKey:@"encryptedKey"];
        }
                
        [PlatformService request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeOverseas path:@"" function:@"se_user_coupon_get" version:@"" parameters:params complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
            @strongify(self);
            
            NSDictionary *data = nil;
            if (responseObject && [responseObject isKindOfClass:NSDictionary.class]) {
                NSInteger code = [responseObject[@"code"] integerValue];
                if (200 == code) {
                    data = responseObject[@"data"];
                    self.realCouponId = [data jdcd_getNumberElementForKey:@"couponId"];
                    self.couponId = [data jdcd_getNumberElementForKey:@"batchId"];
                     
                }else {
                    NSString *message = @"";
                    if (responseObject[@"message"]) {
                        message = responseObject[@"message"];
                    }
                    error = [NSError errorWithDomain:@"shoppingCart.error" code:code userInfo:@{NSLocalizedDescriptionKey:message}];
                }
            }
            
            if (!error) {
                [self changeRevicesStatus:YES];
                [subscriber sendNext:nil];
            }else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        return nil;
    }];
    return signal;
}

@end

@implementation JDISVShoppingCartSectionViewModel


@end
