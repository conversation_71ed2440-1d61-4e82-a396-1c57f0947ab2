//
//  KAShoppingCartPropertyViewModel.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/10/21.
//

#import "KAShoppingCartPropertyViewModel.h"

#import <JDISVYYModelModule/YYModel.h>

#import <JDISVPlatformModule/JDISVPlatformService.h>

#import "KAShoppingCartPropertyModel.h"

#import "JDISVShoppingCartTool.h"
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import "CARTPropertyModel.h"

@import JDTInfrastructureModule;

@implementation KAShoppingCartPropertyViewModel

- (RACSignal *)loadPropertyData {
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        if (self.skuId) {
            [params setObject:self.skuId forKey:@"skuId"];
        }
        
        JDTAddressItemModel *addressModel = [[JDISVPlatformService sharedService] getDefaultAddress];
        
        if (addressModel.longitude) {
            [params setObject:addressModel.longitude forKey:@"longitude"];
        }
        if (addressModel.latitude) {
            [params setObject:addressModel.latitude forKey:@"latitude"];
        }
        NSString *address = [JDISVShoppingCartTool addressIdsWithSpace:@"_"];
        if (address) {
            [params setObject:address forKey:@"areaId"];
        }
//        @weakify(self)
//        [PlatformService isv_shoppingCart_request:JDCDHTTPSessionRequestTypeGet function:@"generalMiniWareInfo" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
////            NSLog(@"%@",responseObject);
//            if (!error) {
//                @strongify(self)
//                [self progressDataWithResponseObject:responseObject];
//                [subscriber sendNext:nil];
//            }else {
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSDictionary *fixParams = @{
            @"provinceId": @"110000",
            @"provinceName": @"北京市",
            @"cityId": @"110100",
            @"cityName": @"北京市",
            @"districtId": @"110102",
            @"districtName": @"西城区",
            @"townId": @"110102003",
            @"townName": @"新街口街道",
            @"latitude": @(39.94031),
            @"longitude": @(116.36685)
        };
        NSMutableDictionary *testParams = [NSMutableDictionary dictionaryWithDictionary:fixParams];
        [testParams setObject:self.skuId forKey:@"skuId"];
        
        [[OOPNetworkManager sharedManager] POST:@"ware/generalMiniWareInfo?apiCode=b2c.cbff.ware.generalMiniWareInfo" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                NSDictionary *data = responseObject[@"data"];
                [self progressDataWithResponseObjectNew:data];
                [subscriber sendNext:nil];
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
                
        return nil;
    }];
    return signal;
}

- (void)progressDataWithResponseObjectNew:(NSDictionary *)responseObject {
    CARTPropertyModel *model = [CARTPropertyModel yy_modelWithDictionary:responseObject];
    
    self.cartProperty = model;
    
//    self.coverImageUrl = model.productImgModule.imageList.firstObject;
//    
//    self.price = model.priceModule.sellPrice.floatValue;
//    
//    if (model.priceModule.originalPrice) {
//        self.crossOffPrice = [[NSAttributedString alloc] initWithString:model.priceModule.originalPrice];
//    }
//    
//    NSMutableArray *tempSections = [NSMutableArray array];
//    
//    for (PDProductSpecItemModel *specItem in model.selectedModule.saleAttrList) {
//        KAShoppingCartPropertySectionViewModel *sectionModel = [[KAShoppingCartPropertySectionViewModel alloc] init];
//        [sectionModel updateWithSpecItem:specItem];
//        [tempSections addObject:sectionModel];
//    }
//    
//    self.sections = [tempSections copy];
}

//- (void)progressDataWithResponseObject:(NSDictionary *)responseObject {
//    KAShoppingCartPropertyModel *model = [KAShoppingCartPropertyModel yy_modelWithDictionary:responseObject];
//    
//    self.coverImageUrl = model.selectedModule.viewSelectedItemCore.imageUrl;
//    
//    self.price = [model.selectedModule.viewSelectedItemCore.mainPrice floatValue];
//    
//    if (model.selectedModule.viewSelectedItemCore.crossOffPrice) {
//        self.crossOffPrice = [[NSAttributedString alloc] initWithString:model.selectedModule.viewSelectedItemCore.crossOffPrice];
//    }
//    
//    NSMutableArray *tempSections = [NSMutableArray array];
//    
//    for (KAShoppingCartPropertySelectedDimensionModel *dimensionModel in model.selectedModule.colorSizePartList) {
//        KAShoppingCartPropertySectionViewModel *sectionModel = [[KAShoppingCartPropertySectionViewModel alloc] init];
//        [sectionModel updateWithModel:dimensionModel];
//        [tempSections addObject:sectionModel];
//    }
//    
//    self.sections = [tempSections copy];
//}

- (BOOL)sureButtonEnableWithSkuId:(NSString *)skuId allItemsSelected:(BOOL)allItemsSelected{
    
    for (KAShoppingCartPropertySectionViewModel *section in self.sections) {
        for (KAShoppingCartPropertyItemViewModel *item in section.items) {
            if (item.selectedItem && NO == item.inStock) {
                return NO;
            }
        }
    }
    if (NO == allItemsSelected) {
        return NO;
    }
    return YES;
}

- (BOOL)sureRequestWithSkuId:(NSString *)skuId {
    if ([skuId isEqualToString:self.skuId]) {
        return NO;
    }
    return YES;
}

- (NSString *)disenableTitle {
    for (KAShoppingCartPropertySectionViewModel *section in self.sections) {
        for (KAShoppingCartPropertyItemViewModel *item in section.items) {
            if (NO == item.inStock && item.selectedItem) {
                return ShopingCartL(@"ka_cart_product_no_stock");
            }
        }
    }
    return ShopingCartL(@"ka_cart_confirm");
}

@end

@implementation KAShoppingCartPropertySectionViewModel

- (void)updateWithSpecItem:(PDProductSpecItemModel *)specItem {
    self.title = specItem.dimTitle;
    
    NSMutableArray *tempItems = [NSMutableArray array];
    
    for (PDProductSpecItemDetailModel *itemDetail in specItem.selectedDimBtnPartVoList) {
        KAShoppingCartPropertyItemViewModel *itemModel = [[KAShoppingCartPropertyItemViewModel alloc] init];
        [itemModel updateWithSpecItemDetail:itemDetail];
        [tempItems addObject:itemModel];
    }
    self.items = [tempItems copy];
}

//- (void)updateWithModel:(KAShoppingCartPropertySelectedDimensionModel *)dimensionModel {
//    self.title = dimensionModel.dimension.dimTitle;
//    
//    NSMutableArray *tempItems = [NSMutableArray array];
//    
//    for (KAShoppingCartPropertySelectedDimensionButtonModel *buttonModel in dimensionModel.buttonPartList) {
//        KAShoppingCartPropertyItemViewModel *itemModel = [[KAShoppingCartPropertyItemViewModel alloc] init];
//        [itemModel updateWithModel:buttonModel];
//        
//        [tempItems addObject:itemModel];
//        
//    }
//    self.items = [tempItems copy];
//}

- (void)selectedItemModel:(KAShoppingCartPropertyItemViewModel *)itemModel {
    
    for (KAShoppingCartPropertyItemViewModel *tempItem in self.items) {
        if ([tempItem isEqual:itemModel]) {
            tempItem.selectedItem = YES;
        }else {
            tempItem.selectedItem = NO;
        }
    }
}

@end

@implementation KAShoppingCartPropertyItemViewModel

//- (void)updateWithModel:(KAShoppingCartPropertySelectedDimensionButtonModel *)buttonModel {
//    self.imageUrl = buttonModel.dimensionButtonCore.imgUrl;
//    self.inStock = buttonModel.dimensionButtonCore.stockState.integerValue == 33;
//    self.text = buttonModel.dimensionButtonCore.text;
//    self.skuList = buttonModel.dimensionButtonCore.skuList;
//}

- (void)updateWithSpecItemDetail:(PDProductSpecItemDetailModel *)specItemDetail {
    self.imageUrl = specItemDetail.imgUrl;
    self.inStock = (specItemDetail.stockState == StockStateHasStock);
    self.text = specItemDetail.text;
    self.skuList = specItemDetail.skuList;
}

- (CGSize)itemSizeWithFont:(UIFont *)font maxWidth:(CGFloat)maxWidth{
//    CGSize tempSize = [self.text sizeWithAttributes:@{NSFontAttributeName:font}];
//    CGFloat width = tempSize.width + 24;
//    if (width > maxWidth) {
//        width = maxWidth;
//    }
//    if (width < 60) {
//        width = 60;
    //    }
    //    return CGSizeMake(width, 42);
    maxWidth -= 24;
    CGSize size = [self.text jdcd_getStringSize:font constraintsSize:CGSizeMake(maxWidth, 100)];
    if(size.height > 20){
        self.isOneLine = NO;
    }else{
        self.isOneLine = YES;
    }
    size.height += 14;
    size.width += 24;
    
    return size;
}

- (void)updateStatusWithSelectStatus:(NSInteger)selectedStatus {
    if (selectedStatus == 1) {
        self.selectedItem = YES;
        self.showStockTag = !self.inStock;
        self.enable = YES;
    }else if(selectedStatus == 0) {
        self.selectedItem = NO;
        self.showStockTag = !self.inStock;
        self.enable = self.inStock;
    }else {
        self.selectedItem = NO;
        self.showStockTag = NO;
        self.enable = NO;
    }
}

@end
