//
//  KAShoppingCartPropertyViewModel.h
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by 罗静 on 2021/10/21.
//

#import <Foundation/Foundation.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import "CARTPropertyModel.h"

@import JDTCommonToolModule;

NS_ASSUME_NONNULL_BEGIN
@class KAShoppingCartPropertyItemViewModel;
@class KAShoppingCartPropertySectionViewModel;
@class KAShoppingCartPropertySelectedDimensionModel;
@class KAShoppingCartPropertySelectedDimensionButtonModel;

@interface KAShoppingCartPropertyViewModel : NSObject

@property (nonatomic, copy) NSString *coverImageUrl;

@property (nonatomic, assign) CGFloat price;

@property (nonatomic, copy) NSAttributedString *crossOffPrice;

@property (nonatomic, strong) NSArray <KAShoppingCartPropertySectionViewModel *>*sections;

@property (nonatomic, copy) NSString *skuId;

@property (nonatomic, strong) CARTPropertyModel *cartProperty;

- (RACSignal *)loadPropertyData;

- (BOOL)sureButtonEnableWithSkuId:(NSString *)skuId allItemsSelected:(BOOL)allItemsSelected;

- (BOOL)sureRequestWithSkuId:(NSString *)skuId;

- (NSString *)disenableTitle;

@end

@interface KAShoppingCartPropertySectionViewModel : NSObject

@property (nonatomic, copy) NSString *title;

@property (nonatomic, strong) NSArray <KAShoppingCartPropertyItemViewModel *>*items;

//- (void)updateWithModel:(KAShoppingCartPropertySelectedDimensionModel *)dimensionModel;

- (void)updateWithSpecItem:(PDProductSpecItemModel *)specItem;

- (void)selectedItemModel:(KAShoppingCartPropertyItemViewModel *)itemModel;

@end

@interface KAShoppingCartPropertyItemViewModel : NSObject

@property (nonatomic, strong) NSArray <NSString *>*skuList;

@property (nonatomic, copy) NSString *text;

@property (nonatomic, assign) BOOL isOneLine;

@property (nonatomic, assign) BOOL inStock;

@property (nonatomic, copy) NSString *imageUrl;

@property (nonatomic, assign) BOOL selectedItem;

@property (nonatomic, assign) BOOL showStockTag;

@property (nonatomic, assign) BOOL enable;

//- (void)updateWithModel:(KAShoppingCartPropertySelectedDimensionButtonModel *)buttonModel;

- (void)updateWithSpecItemDetail:(PDProductSpecItemDetailModel *)specItemDetail;

- (CGSize)itemSizeWithFont:(UIFont *)font maxWidth:(CGFloat)maxWidth;

- (void)updateStatusWithSelectStatus:(NSInteger)selectedStatus;

@end

NS_ASSUME_NONNULL_END
