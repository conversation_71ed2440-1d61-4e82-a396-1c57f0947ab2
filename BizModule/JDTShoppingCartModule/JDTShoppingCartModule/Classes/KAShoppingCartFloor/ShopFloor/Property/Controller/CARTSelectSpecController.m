//
//  CARTSelectSpecController.m
//  JDTShoppingCartModule
//
//  Created by lvchenzhu.1 on 2025/6/30.
//

#import "CARTSelectSpecController.h"
@import JDISVKAUIKitModule;

@interface CARTSelectSpecController ()
/// 商品图
@property (nonatomic, strong) UIImageView *imgView;
/// 商品名
@property (nonatomic, strong) UILabel *nameLabel;
/// 价格
@property (nonatomic, strong) KAPriceLabel *priceLabel;
/// 限购提示
@property (nonatomic, strong) UILabel *limitBuyLabel;
/// 规格选择
@property (nonatomic, strong) UICollectionView *collectionView;
/// 确定
@property (nonatomic, strong) UIButton *okBtn;

@end

@implementation CARTSelectSpecController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
}

#pragma mark - Getter and Setter
- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [[UIImageView alloc] init];
    }
    return _imgView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
    }
    return _nameLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
    }
    return _priceLabel;
}

- (UILabel *)limitBuyLabel {
    if (!_limitBuyLabel) {
        _limitBuyLabel = [[UILabel alloc] init];
    }
    return _limitBuyLabel;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        _collectionView = [[UICollectionView alloc] init];
    }
    return _collectionView;
}

- (UIButton *)okBtn {
    if (!_okBtn) {
        _okBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    }
    return _okBtn;
}

@end
