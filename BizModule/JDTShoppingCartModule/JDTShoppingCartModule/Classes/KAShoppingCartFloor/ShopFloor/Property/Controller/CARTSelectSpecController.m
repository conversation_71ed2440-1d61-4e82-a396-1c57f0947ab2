//
//  CARTSelectSpecController.m
//  JDTShoppingCartModule
//
//  Created by lvchenzhu.1 on 2025/6/30.
//

#import "CARTSelectSpecController.h"
#import <JDISVMasonryModule/Masonry.h>
#import <JDISVImageModule/JDISVImageModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import "KAShoppingCartPropertyViewModel.h"
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>

@import JDISVKAUIKitModule;
@import JDTCommonToolModule;

@interface CARTSelectSpecController () <UICollectionViewDelegateFlowLayout, UICollectionViewDelegate, UICollectionViewDataSource>
/// 商品图
@property (nonatomic, strong) UIImageView *imgView;
/// 商品名
@property (nonatomic, strong) UILabel *nameLabel;
/// 价格
@property (nonatomic, strong) KAPriceLabel *priceLabel;
/// 限购提示
@property (nonatomic, strong) UILabel *limitBuyLabel;
/// 规格选择
@property (nonatomic, strong) UICollectionView *collectionView;
/// 确定
@property (nonatomic, strong) UIButton *okBtn;
/// 已选择的规格 key: dimNumber, value: 选中的文本
@property (nonatomic, strong) NSMutableDictionary <NSNumber *, NSString *> *selectedSpecs;
/// 已选择的索引路径 key: dimNumber, value: indexPath
@property (nonatomic, strong) NSMutableDictionary <NSNumber *, NSIndexPath *> *selectedIndexpath;
/// 选中的SKU ID
@property (nonatomic, copy) NSString *selectedSkuId;

@property (nonatomic, strong) KAShoppingCartPropertyViewModel *viewModel;

@end

@implementation CARTSelectSpecController

- (void)viewDidLoad {
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor whiteColor];

    [self setupHeaderView];
    [self setupFooterView];
    [self setupSpecCollectionView];
    
    [self loadData];
    
    [self updateUIWithData];
}

- (void)setupHeaderView {
    [self.view addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(18);
        make.top.equalTo(self.view).offset(20);
        make.size.mas_equalTo(CGSizeMake(108, 108));
    }];

    [self.view addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imgView);
        make.right.equalTo(self.view).offset(-18);
        make.left.equalTo(self.imgView.mas_right).offset(12);
        make.height.mas_equalTo(42);
    }];

    [self.view addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.right.equalTo(self.view).offset(-18);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(21);
    }];

    [self.view addSubview:self.limitBuyLabel];
    [self.limitBuyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel);
        make.right.equalTo(self.view).offset(-18);
        make.top.equalTo(self.priceLabel.mas_bottom).offset(8);
        make.height.mas_equalTo(16);
    }];
}

- (void)setupSpecCollectionView {
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(self.imgView.mas_bottom).offset(24);
        make.bottom.equalTo(self.okBtn.mas_top).offset(-28);
    }];
}

- (void)setupFooterView {
    [self.view addSubview:self.okBtn];
    [self.okBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(18);
        make.right.equalTo(self.view).offset(-18);
        make.height.mas_equalTo(50);
        make.bottom.equalTo(self.view.mas_bottom).offset(-[UIWindow ka_uikit_safeAreaInsets].bottom - 20);
    }];
}

- (void)loadData {
    [PlatformService showLoadingInView:self.view];
    @weakify(self);
    [[self.viewModel loadPropertyData] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [PlatformService dismissInView:self.view];
        [self updateUIWithData];
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        [PlatformService dismissInView:self.view];
    }];
}

- (void)updateUIWithData {
    if (!self.viewModel.cartProperty) return;

    // 更新商品图片
    if (self.viewModel.cartProperty.productImgModule.imageList.count > 0) {
        NSString *imageUrl = self.viewModel.cartProperty.productImgModule.imageList.firstObject;
        [self.imgView jdcd_setImage:imageUrl placeHolder:[[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault] contentMode:UIViewContentModeScaleAspectFit];
    }

    // 更新商品名称
    self.nameLabel.text = self.viewModel.cartProperty.productBaseModule.skuName ?: @"";

    // 更新价格
    if (self.viewModel.cartProperty.priceModule.sellPrice) {
        [self.priceLabel configTextWithPrice:self.viewModel.cartProperty.priceModule.sellPrice.floatValue middleLinePrice:self.viewModel.cartProperty.priceModule.originalPrice.floatValue];
    }

    // 更新限购信息
    NSUInteger limitMinNum = self.viewModel.cartProperty.limitModule.limitMinNum;
    NSUInteger limitMaxNum = self.viewModel.cartProperty.limitModule.limitMaxNum;
    if (limitMinNum > 0) {
        if (limitMaxNum > 0) {
            self.limitBuyLabel.text = [NSString stringWithFormat:@"每笔订单%zd件起购，最多可买%zd件", limitMinNum, limitMaxNum];
        } else {
            self.limitBuyLabel.text = [NSString stringWithFormat:@"每笔订单%zd件起购", limitMinNum];
        }
    } else {
        if (limitMaxNum > 0) {
            self.limitBuyLabel.text = [NSString stringWithFormat:@"每笔订单最多购买%zd件商品", limitMaxNum];
        } else {
            self.limitBuyLabel.text = @"";
        }
    }

    // 如果有预设的SKU ID，自动选中对应的规格
    if (self.skuId.length > 0) {
        [self autoSelectSpecsForSkuId:self.skuId];
    }

    [self.collectionView reloadData];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return self.viewModel.cartProperty.selectedModule.saleAttrList.count;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.viewModel.cartProperty.selectedModule.saleAttrList[section].selectedDimBtnPartVoList.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    PDProductSpecItemModel *model = self.viewModel.cartProperty.selectedModule.saleAttrList[indexPath.section];
    PDProductSpecItemDetailModel *detailModel = model.selectedDimBtnPartVoList[indexPath.item];

    PDSpecOptionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"PDSpecOptionCell" forIndexPath:indexPath];
    cell.titleLabel.text = detailModel.text;

    BOOL isNoStock = (detailModel.stockState == StockStateNoStock);
    BOOL isUnavailable = ![self isOptionAvailable:detailModel forDimNumber:model.dimNumber];
    NSIndexPath *selectedIndexPath = self.selectedIndexpath[model.dimNumber];
    BOOL isSelected = (selectedIndexPath && selectedIndexPath.item == indexPath.item);

    if (isNoStock) {
        cell.state = isSelected ? PDSpecOptionCellStateNoStockSelected : PDSpecOptionCellStateNoStockNormal;
    } else if (isUnavailable) {
        cell.state = PDSpecOptionCellStateUnavailable;
    } else if (isSelected) {
        cell.state = PDSpecOptionCellStateSelected;
    } else {
        cell.state = PDSpecOptionCellStateNormal;
    }

    return cell;
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    if (kind == UICollectionElementKindSectionHeader) {
        PDSpecSectionHeaderView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"PDSpecSectionHeaderView" forIndexPath:indexPath];
        headerView.sectionLabel.text = self.viewModel.cartProperty.selectedModule.saleAttrList[indexPath.section].dimTitle;
        return headerView;
    }
    return nil;
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    NSString *text = self.viewModel.cartProperty.selectedModule.saleAttrList[indexPath.section].selectedDimBtnPartVoList[indexPath.item].text;
    CGSize size = [text sizeWithAttributes:@{NSFontAttributeName: [UIFont systemFontOfSize:12]}];
    return CGSizeMake(size.width + 30, 30);
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    return CGSizeMake(collectionView.frame.size.width, 40);
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(10, 18, 10, 18);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 12;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 12;
}

#pragma mark - Getter and Setter
- (UIImageView *)imgView {
    if (!_imgView) {
        _imgView = [[UIImageView alloc] init];
    }
    return _imgView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C7"];
        _nameLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T13"];
    }
    return _nameLabel;
}

- (KAPriceLabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[KAPriceLabel alloc] init];
    }
    return _priceLabel;
}

- (UILabel *)limitBuyLabel {
    if (!_limitBuyLabel) {
        _limitBuyLabel = [[UILabel alloc] init];
        _limitBuyLabel.textColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C9"];
        _limitBuyLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T9"];
    }
    return _limitBuyLabel;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;

        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor whiteColor];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsVerticalScrollIndicator = NO;

        [_collectionView registerClass:[PDSpecOptionCell class] forCellWithReuseIdentifier:@"PDSpecOptionCell"];
        [_collectionView registerClass:[PDSpecSectionHeaderView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"PDSpecSectionHeaderView"];
    }
    return _collectionView;
}

- (UIButton *)okBtn {
    if (!_okBtn) {
        _okBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_okBtn setTitle:@"确定" forState:UIControlStateNormal];
        [_okBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_okBtn jdisv_setGradientBackgroundColorPicker1:JDISVColorPickerWithKey(@"#C9-1") picker2:JDISVColorPickerWithKey(@"#C9-2") forState:UIControlStateNormal];
        _okBtn.layer.cornerRadius = 25;
        _okBtn.layer.masksToBounds = YES;
        _okBtn.titleLabel.font = [UIFont boldSystemFontOfSize:16];
        [_okBtn addTarget:self action:@selector(okButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _okBtn;
}

- (NSMutableDictionary<NSNumber *,NSString *> *)selectedSpecs {
    if (!_selectedSpecs) {
        _selectedSpecs = [NSMutableDictionary dictionary];
    }
    return _selectedSpecs;
}

- (NSMutableDictionary<NSNumber *,NSIndexPath *> *)selectedIndexpath {
    if (!_selectedIndexpath) {
        _selectedIndexpath = [NSMutableDictionary dictionary];
    }
    return _selectedIndexpath;
}

- (KAShoppingCartPropertyViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[KAShoppingCartPropertyViewModel alloc] init];
        _viewModel.skuId = self.skuId;
    }
    return _viewModel;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    PDProductSpecItemModel *model = self.viewModel.cartProperty.selectedModule.saleAttrList[indexPath.section];
    PDProductSpecItemDetailModel *detailModel = model.selectedDimBtnPartVoList[indexPath.item];

    PDSpecOptionCell *cell = (PDSpecOptionCell *)[collectionView cellForItemAtIndexPath:indexPath];

    switch (cell.state) {
        case PDSpecOptionCellStateUnavailable: {
            // 如果是不可选或无货状态，则不响应选择
            [collectionView deselectItemAtIndexPath:indexPath animated:NO];
            return;
        }
        case PDSpecOptionCellStateSelected:
        case PDSpecOptionCellStateNoStockSelected: {
            [self.selectedSpecs removeObjectForKey:model.dimNumber];
            [self.selectedIndexpath removeObjectForKey:model.dimNumber];
            break;
        }
        case PDSpecOptionCellStateNormal:
        case PDSpecOptionCellStateNoStockNormal: {
            self.selectedSpecs[model.dimNumber] = detailModel.text;
            self.selectedIndexpath[model.dimNumber] = indexPath;
            break;
        }
        default:
            break;
    }

    [self updateAvailableOptions];
    [self determineSelectedSku];
}

#pragma mark - Private Methods

- (void)updateAvailableOptions {
    [self.collectionView reloadData];
}

- (BOOL)isOptionAvailable:(PDProductSpecItemDetailModel *)model forDimNumber:(NSNumber *)number {
    // 如果没有选择任何规格，所有选项都可用
    if (self.selectedSpecs.count == 0) {
        return YES;
    }
    // 检查是否与已选规格兼容
    for (NSString *sku in model.skuList) {
        BOOL isCompatible = YES;

        // 检查该SKU是否与所有已选规格兼容
        for (NSNumber *selectedDimNumber in self.selectedSpecs.allKeys) {
            // 跳过当前正在检查的维度
            if ([selectedDimNumber isEqualToNumber:number]) {
                continue;
            }

            NSString *selectedValue = self.selectedSpecs[selectedDimNumber];
            BOOL foundMatch = NO;

            // 在所有规格中查找匹配的维度和值
            for (PDProductSpecItemModel *item in self.viewModel.cartProperty.selectedModule.saleAttrList) {
                if ([item.dimNumber isEqualToNumber:selectedDimNumber]) {
                    for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                        if ([detail.text isEqualToString:selectedValue]) {
                            // 检查SKU是否在该选项的SKU列表中
                            if ([detail.skuList containsObject:sku]) {
                                foundMatch = YES;
                                break;
                            }
                        }
                    }
                    break;
                }
            }

            if (!foundMatch) {
                isCompatible = NO;
                break;
            }
        }

        if (isCompatible) {
            return YES;
        }
    }
    return NO;
}

- (void)determineSelectedSku {
    // 如果所有规格都已选择，确定最终SKU
    if (self.selectedSpecs.count == self.viewModel.cartProperty.selectedModule.saleAttrList.count) {
        NSMutableSet *possibleSkus = nil;

        // 遍历所有已选规格，找出符合所有规格的SKU
        for (NSNumber *dimNumber in self.selectedSpecs.allKeys) {
            NSString *selectedValue = self.selectedSpecs[dimNumber];

            for (PDProductSpecItemModel *item in self.viewModel.cartProperty.selectedModule.saleAttrList) {
                if ([item.dimNumber isEqualToNumber:dimNumber]) {
                    for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                        if ([detail.text isEqualToString:selectedValue]) {
                            NSArray *skuList = detail.skuList;
                            NSMutableSet *skuSet = [NSMutableSet setWithArray:skuList];

                            if (possibleSkus == nil) {
                                possibleSkus = skuSet;
                            } else {
                                [possibleSkus intersectSet:skuSet];
                            }

                            break;
                        }
                    }
                }
            }
        }

        // 如果找到唯一的SKU，则设置为选中的SKU
        if (possibleSkus.count == 1) {
            self.selectedSkuId = [possibleSkus anyObject];
            NSLog(@"已选中SKU: %@", self.selectedSkuId);
        } else {
            self.selectedSkuId = nil;
        }
    } else {
        self.selectedSkuId = nil;
    }
}

- (void)autoSelectSpecsForSkuId:(NSString *)skuId {
    if (!skuId.length || !self.viewModel.cartProperty.selectedModule.saleAttrList.count) {
        return;
    }

    // 清空之前的选择
    [self.selectedSpecs removeAllObjects];
    [self.selectedIndexpath removeAllObjects];

    // 遍历所有规格维度，找到包含目标SKU的选项
    for (NSInteger section = 0; section < self.viewModel.cartProperty.selectedModule.saleAttrList.count; section++) {
        PDProductSpecItemModel *specItem = self.viewModel.cartProperty.selectedModule.saleAttrList[section];

        for (NSInteger item = 0; item < specItem.selectedDimBtnPartVoList.count; item++) {
            PDProductSpecItemDetailModel *detailModel = specItem.selectedDimBtnPartVoList[item];

            // 检查该选项的SKU列表是否包含目标SKU
            if ([detailModel.skuList containsObject:skuId]) {
                // 找到匹配的选项，记录选择
                self.selectedSpecs[specItem.dimNumber] = detailModel.text;
                self.selectedIndexpath[specItem.dimNumber] = [NSIndexPath indexPathForItem:item inSection:section];
                break; // 找到后跳出内层循环
            }
        }
    }

    // 验证选择的规格组合是否能唯一确定目标SKU
    [self validateAutoSelection:skuId];

    // 设置选中的SKU
    self.selectedSkuId = skuId;

    NSLog(@"自动选中规格完成，SKU: %@, 选中规格: %@", skuId, self.selectedSpecs);
}

- (void)validateAutoSelection:(NSString *)targetSkuId {
    // 验证当前选择的规格组合是否能唯一确定目标SKU
    NSMutableSet *possibleSkus = nil;

    for (NSNumber *dimNumber in self.selectedSpecs.allKeys) {
        NSString *selectedValue = self.selectedSpecs[dimNumber];

        for (PDProductSpecItemModel *item in self.viewModel.cartProperty.selectedModule.saleAttrList) {
            if ([item.dimNumber isEqualToNumber:dimNumber]) {
                for (PDProductSpecItemDetailModel *detail in item.selectedDimBtnPartVoList) {
                    if ([detail.text isEqualToString:selectedValue]) {
                        NSArray *skuList = detail.skuList;
                        NSMutableSet *skuSet = [NSMutableSet setWithArray:skuList];

                        if (possibleSkus == nil) {
                            possibleSkus = skuSet;
                        } else {
                            [possibleSkus intersectSet:skuSet];
                        }
                        break;
                    }
                }
                break;
            }
        }
    }

    // 检查是否能唯一确定目标SKU
    if (![possibleSkus containsObject:targetSkuId]) {
        NSLog(@"警告：自动选择的规格组合无法确定目标SKU: %@", targetSkuId);
        // 清空选择
        [self.selectedSpecs removeAllObjects];
        [self.selectedIndexpath removeAllObjects];
    } else if (possibleSkus.count > 1) {
        NSLog(@"警告：自动选择的规格组合对应多个SKU: %@，目标SKU: %@", possibleSkus, targetSkuId);
    }
}

#pragma mark - Debug Methods

- (void)debugPrintSpecData {
    NSLog(@"=== 规格数据调试信息 ===");
    for (NSInteger i = 0; i < self.viewModel.cartProperty.selectedModule.saleAttrList.count; i++) {
        PDProductSpecItemModel *specItem = self.viewModel.cartProperty.selectedModule.saleAttrList[i];
        NSLog(@"规格维度 %@: %@", specItem.dimNumber, specItem.dimTitle);

        for (NSInteger j = 0; j < specItem.selectedDimBtnPartVoList.count; j++) {
            PDProductSpecItemDetailModel *detail = specItem.selectedDimBtnPartVoList[j];
            NSLog(@"  选项 %zd: %@ (库存:%zd, SKU:%@)", j, detail.text, detail.stockState, detail.skuList);
        }
    }
    NSLog(@"当前选择: %@", self.selectedSpecs);
    NSLog(@"选中SKU: %@", self.selectedSkuId);
    NSLog(@"======================");
}

#pragma mark - Actions

- (void)okButtonTapped:(UIButton *)sender {
    if ([self.selectedSkuId isEqualToString:self.skuId]) {
        [self dismissViewControllerAnimated:YES completion:nil];
    } else {
        if (self.didClickOKBtnBlock) {
            self.didClickOKBtnBlock(self.selectedSkuId);
        }
    }
}

@end
