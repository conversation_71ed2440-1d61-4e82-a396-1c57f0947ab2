//
//  KAShoppingCartShopHeadModule.h
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/22.
//  Copyright © 2021 罗静. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import "CARTMainModel.h"
@class  JDISVShoppingCartItemModel;
@protocol  JDISVFloorViewProtocol;
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, KAShoppingCartShopSelectedStatus) {
    KAShoppingCartShopSelectedStatusUnSelected, ///< 未选中
    KAShoppingCartShopSelectedStatusSelected, ///< 选中
    KAShoppingCartShopSelectedStatusDisenable, ///< 没有选中能力
};


@interface KAShoppingCartShopHeadModule : NSObject<JDISVFloorModuleProtocol>
@property(nonatomic,weak) id<JDISVFloorViewProtocol> cell;
@property (nonatomic, copy) NSString *shopName;

@property (nonatomic, strong) NSNumber *shopId;
@property (nonatomic, strong) NSNumber *venderId;

@property (nonatomic, assign) KAShoppingCartShopSelectedStatus selectedStatus;
@property (nonatomic, assign) KAShoppingCartShopSelectedStatus originalSelectedStatus;

@property (nonatomic, assign) BOOL selected;
@property (nonatomic, assign) BOOL selectedEnable;

@property (nonatomic, assign) BOOL showCoupon;

@property (nonatomic, assign) BOOL inStock;

@property (nonatomic, strong) NSArray *productParams;
@property (nonatomic, strong) NSArray *promotionParams;
@property (nonatomic, strong) NSArray *couponParams;

@property (nonatomic, assign) BOOL edit;

//@property (nonatomic, strong) JDISVShoppingCartItemModel *currentShopModel;
@property (nonatomic, strong) CARTMainVenderModel *curVenderModel;

-(void)userSelectAll:(NSString*)selected
          needNotify:(BOOL)notify;
- (JDCDISVAction *)selectAction;

@end

NS_ASSUME_NONNULL_END
