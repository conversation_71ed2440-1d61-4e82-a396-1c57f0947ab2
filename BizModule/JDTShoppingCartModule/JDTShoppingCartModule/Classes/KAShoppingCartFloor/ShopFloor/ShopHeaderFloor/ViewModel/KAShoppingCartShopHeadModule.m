//
//  KAShoppingCartShopHeadModule.m
//  JDISVShoppingCartSDKModule_Example
//
//  Created by 罗静 on 2021/9/22.
//  Copyright © 2021 罗静. All rights reserved.
//

#import "KAShoppingCartShopHeadModule.h"

#import <JDISVYYModelModule/YYModel.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>

#import "KAShoppingCartShopHeaderModel.h"
#import "JDISVShoppingCartMainModel.h"

#import "KAShoppingCartShopHeadFloor.h"
#import "KSAShoppingCartShopHeadFloor.h"

#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import "KAShoppingCartProductModel.h"
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>

#import "CARTMainModel.h"

JDISVRegisterFloorModule(kShopHeaderFloor, KAShoppingCartShopHeadModule)

@interface KAShoppingCartShopHeadModule()
    @property (weak,nonatomic) JDISVFloorCommonModel* commonModel;
@end

@implementation KAShoppingCartShopHeadModule

- (Class)tableViewFloorClass {
    if(KSAAPP){
        return KSAShoppingCartShopHeadFloor.class;
    }
    return KAShoppingCartShopHeadFloor.class;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (CGFloat)floorHeight {
    return 36;
}

- (BOOL)isHeaderFooter {
    return YES;
}

- (BOOL)ceiling {
    return YES;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    @weakify(self)
   
    self.commonModel = commonModel;
    
//    NSDictionary *floorData = data[@"data"];
    CARTMainVenderModel *vender = data[@"data"];
    self.curVenderModel = vender;
//    NSDictionary *cartData = commonModel.allFloorOriginData;
    
//    KASHoppingCartShopHeaderResultModel *resultModel = [KASHoppingCartShopHeaderResultModel yy_modelWithDictionary:floorData];
    
//    KAShoppingCartShopHeaderModel *model = resultModel.info;
        
//    self.shopName = model.groupModelDescription.groupName;
//    self.venderId = model.groupModelVendor.vendorId;
//    self.shopId =  model.abstractGroupItemCore.groupId;
    self.shopName = vender.shopName;
    self.venderId = vender.vendorId;
    self.shopId = vender.shopId;
    
//    if (model.groupModelCheckbox.enableCheck) {
//        if (model.groupModelCheckbox.activeCheck) {
    if (vender.enableCheck.boolValue) {
        if (vender.activeCheck.boolValue) {
            self.selectedStatus = KAShoppingCartShopSelectedStatusSelected;
            self.selected = YES;
            self.selectedEnable = YES;
        }else {
            self.selectedStatus = KAShoppingCartShopSelectedStatusUnSelected;
            self.selected = NO;
            self.selectedEnable = YES;
        }
    }else {
        self.selectedStatus = KAShoppingCartShopSelectedStatusDisenable;
        self.selected = YES;
        self.selectedEnable = NO;
    }
    
    //数据下发是否显示优惠券入口
//    self.showCoupon = model.groupModelCoupon.hasCoupon;
    self.showCoupon = vender.hasCoupon;
    
    //配置开关是否显示优惠券入口，优先级更高
//    NSDictionary *configData = data[@"ext"];
//    BOOL couponFeature = [configData[@"couponFeature"] boolValue];
//    self.showCoupon = couponFeature;
        
    //操作参数
//    JDISVShoppingCartItemModel *currentShopModel = [self currentShopModelWithResponseObject:cartData uuid:resultModel.uuid];
//    self.currentShopModel = currentShopModel;
    
//    [self createParam:currentShopModel];
    [self createParam:vender];
//    self.couponParams = [self couponParamWithShopModel:currentShopModel];
    self.couponParams = [self couponParamWithShopModel:vender];
//    self.inStock = [self inStockWithShopModel:currentShopModel];
    self.inStock = [self inStockWithShopModel:vender];
    
    if (self.inStock == NO) {
        self.selectedStatus = KAShoppingCartShopSelectedStatusDisenable;
        self.selected = YES;
        self.selectedEnable = NO;
        self.showCoupon = NO;
    }

    self.originalSelectedStatus = self.selectedStatus;
    [commonModel commonDataDidChange:^(NSDictionary * _Nonnull commonData) {
        @strongify(self);
        BOOL edit = [commonData[@"edit"] boolValue];
        self.edit = edit;
        if(edit){
            [self editSeleted];
        }
    }];
    
}

-(BOOL)isSelectSubItem:(CARTMainSkuItemModel *)model
              editSkus:(NSArray*)skus{
//    if([model.type.nameSpace containsString:@"cartView.promotion-M"]){
//        NSDictionary* vsDic = [model.info jdcd_getDicElementForKey:@"HJM-D#virtualSuit&virtualSuit"];
//        if(vsDic.count){//virtualsuit
//            BOOL enableCheck = [[vsDic jdcd_getNumberElementForKey:@"enableCheck"] boolValue];
//            BOOL activeCheck = [[vsDic jdcd_getNumberElementForKey:@"activeCheck"] boolValue];
//            NSString* skuId = [vsDic jdcd_getStringElementForKey:@"vSkuId"];
//            
//            if( enableCheck) {
//                if (!activeCheck){
//                    return NO;
//                }
//            }
//            else{
//                if(![skus containsObject:skuId]){
//                    return NO;
//                }
//            }
//        }else{
//            
//            for(JDISVShoppingCartItemModel* subModel in model.subList){
//                if(![self isSelectSubItem:subModel editSkus:skus]){
//                    return NO;
//                }
//            }
//        }
//    }else{
//        KAShoppingCartProductResultModel *sku = [KAShoppingCartProductResultModel yy_modelWithDictionary:model.itemDict];
//        if( [sku.info.cartProductExtInfo.enableCheck boolValue]) {
//            if (![sku.info.productItemBasic.activeCheck boolValue]){
//                return NO;
//            }
//        }
    if (model.enableCheck.boolValue) {
        if (!model.activeCheck.boolValue) {
            return NO;
        }
    } else{
//            if(![skus containsObject:sku.info.productItemCore.skuId]){
            if(![skus containsObject:model.skuId]){
                return NO;
            }
        }
//    }
    return YES;
}

-(void)editSeleted{
    self.selectedEnable = YES;
    BOOL isAllSelect = YES;
    NSString* userSelect  = self.commonModel.commonData[@"userSelecteAll"];
    if(userSelect.length){
        if([userSelect isEqualToString:@"selectAll"]){
            [self userSelectAll:@"select" needNotify:NO];

        }else{
            [self userSelectAll:@"unselect" needNotify:NO];
        }
        return;
    }
    
    NSArray *selectSku =  [self.commonModel.commonData[@"editModeSkus"] copy];
//    for(JDISVShoppingCartItemModel* model in self.currentShopModel.subList){
//        BOOL isSelect =  [self isSelectSubItem:model editSkus:selectSku];
//        if(!isSelect){
//            isAllSelect = NO;
//            break;;
//        }
//    }
    for (CARTMainSkuItemModel *skuItem in self.curVenderModel.skuItems) {
        BOOL isSelect =  [self isSelectSubItem:skuItem editSkus:selectSku];
        if(!isSelect){
            isAllSelect = NO;
            break;;
        }
    }
    
    NSMutableArray* groups = self.commonModel.commonData[@"editModeGroups"];
//    NSString* groupId = self.currentShopModel.info[@"C-M#abstractGroupItem&core"][@"groupId"]?:@"";
    NSString* groupId = self.curVenderModel.vendorId.stringValue;
    if(isAllSelect){
        NSLog(@"shop selelct All");
        self.selected = YES;
        self.selectedStatus = KAShoppingCartShopSelectedStatusSelected;
        [groups addObject:groupId];
    }else{
        NSLog(@"shop selelct NoAll");
        self.selected = NO;
        self.selectedStatus = KAShoppingCartShopSelectedStatusUnSelected;
        [groups removeObject:groupId];
    }
    [self.cell floorDidLoad:self];
}

-(void)createParam:(CARTMainVenderModel*)model{
    NSMutableArray* prds = [NSMutableArray array];
    NSMutableArray* promotions = [NSMutableArray array];
    
//    for (JDISVShoppingCartItemModel *promotionModel in model.subList) {
//        if ([promotionModel.type.nameSpace containsString:@"cartView.promotion-M#defaultPromotionItem"]) {
//            NSString* promotionId = NSUUID.UUID.UUIDString;
//            NSMutableArray* promtionPrdis = [NSMutableArray array];
//            for(JDISVShoppingCartItemModel* prudoctModel in promotionModel.subList){
//                NSDictionary* prd = [self shopParamsWithShopModel:prudoctModel promotonId:promotionId pids:promtionPrdis];
//                if(prd){
//                    [prds addObject:prd];
//                }
//            }
//            NSDictionary* promotion  = [self shopPromotionParamsWithShopModel:promotionModel promotonId:promotionId pids:promtionPrdis];
//            if(promotion){
//                [promotions addObject:promotion];
//            }
//        }else{ //商品
//            NSDictionary* prd = [self shopParamsWithShopModel:promotionModel promotonId:@"" pids:nil];
//            if(prd){
//                [prds addObject:prd];
//            }
//        }
//    }
    
    for (CARTMainSkuItemModel *skuItem in model.skuItems) {
        NSDictionary* prd = [self shopParamsWithShopModel:skuItem promotonId:@"" pids:nil];
        if (prd) {
            [prds addObject:prd];
        }
    }
    
    
    
    self.productParams = prds;
    self.promotionParams = promotions;
}

-(NSDictionary*)shopPromotionParamsWithShopModel:(JDISVShoppingCartItemModel*)promotionModel
                                      promotonId:(NSString*)proID
                                            pids:(NSArray*)prds{
    
    if([promotionModel.type.nameSpace containsString:@"cartView.promotion-M"]){
        NSDictionary* vsDic = promotionModel.info[@"HJM-D#virtualSuit&virtualSuit"];
        if(vsDic){
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"id"] = proID;
            params[@"itemType"] = @(4);
            params[@"num"] = @(1);
            params[@"promotionId"] = vsDic[@"vSkuId"]?:@"";
            if(prds.count){
                params[@"childList"] = prds;
            }
            return params;
        }else{
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            params[@"id"] = proID;
            params[@"itemType"] = @(3);
            params[@"num"] = @(1);
            params[@"promotionId"] = promotionModel.info[@"C-M#promotionItem&core"][@"promotionId"]?:@"";
            if(prds.count){
                params[@"childList"] = prds;
            }
            return params;
        }
    }
    return nil;
}

- (NSDictionary *)shopParamsWithShopModel:(CARTMainSkuItemModel *)pModel
                               promotonId:(NSString*)promotionId
                                     pids:(NSMutableArray*)pids{
    
//    NSNumber* enableCheck = pModel.info[@"HJM-M#cartProduct&extInfo"][@"enableCheck"];
//    if(!enableCheck.boolValue){
//        return nil;
//    }
    if (!pModel.enableCheck) {
        return nil;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
//    KAShoppingCartHeaderTempProductModel *productModel = [KAShoppingCartHeaderTempProductModel yy_modelWithDictionary:pModel.info];
    NSString* uuid = NSUUID.UUID.UUIDString;
    params[@"id"] = uuid;
    if(pids){
        [pids addObject:uuid];
    }
    if(promotionId.length){
        params[@"parentId"] = promotionId;
    }
//    if (productModel.cartProductExtInfo.itemType) {
//        [params setObject:productModel.cartProductExtInfo.itemType forKey:@"itemType"];
//    }
//    if (productModel.productItemCore.skuId) {
//        [params setObject:productModel.productItemCore.skuId forKey:@"skuId"];
//    }
    if (pModel.skuId) {
        [params setObject:pModel.skuId forKey:@"skuId"];
    }
//    if (productModel.productItemBasic.num) {
//        [params setObject:productModel.productItemBasic.num forKey:@"num"];
//    }
    if (pModel.num) {
        [params setObject:pModel.num forKey:@"num"];
    }
    return params;
}

- (BOOL)inStockWithShopModel:(CARTMainVenderModel *)currentShopModel {
        
//    for (JDISVShoppingCartItemModel *promotionModel in currentShopModel.subList) {
//        
//        if (promotionModel.subList) {
//            for (JDISVShoppingCartItemModel *infoModel in promotionModel.subList) {
//                
//                KAShoppingCartHeaderTempProductModel *productModel = [KAShoppingCartHeaderTempProductModel yy_modelWithDictionary:infoModel.info];
//                
//                //0：有货 1：无货 2：预定
//                if (productModel.cartProductStockInfo.stockCode.integerValue == 1) {
//                    
//                }else {
//                    return YES;
//                }
//            }
//        }else {
//            
//            KAShoppingCartHeaderTempProductModel *productModel = [KAShoppingCartHeaderTempProductModel yy_modelWithDictionary:promotionModel.info];
//            //0：有货 1：无货 2：预定
//            if (productModel.cartProductStockInfo.stockCode.integerValue == 1) {
//                
//            }else {
//                return YES;
//            }
//            
//        }
//        
//    }
    
    for (CARTMainSkuItemModel *skuItem in currentShopModel.skuItems) {
        if (skuItem.stockStatus.integerValue == 1) {
            return YES;
        }
    }
    
    return NO;
}


- (NSArray *)couponParamWithShopModel:(CARTMainVenderModel *)currentShopModel {
    NSMutableArray *result = [NSMutableArray array];
    
//    for (JDISVShoppingCartItemModel *promotionModel in currentShopModel.subList) {
//        
//        NSMutableDictionary *params = [NSMutableDictionary dictionary];
//
//        if (promotionModel.subList) {
//            for (JDISVShoppingCartItemModel *infoModel in promotionModel.subList) {
//                
//                KAShoppingCartHeaderTempProductModel *productModel = [KAShoppingCartHeaderTempProductModel yy_modelWithDictionary:infoModel.info];
//            
//                if (productModel.productItemCore.skuId) {
//                    [params setObject:productModel.productItemCore.skuId forKey:@"skuId"];
//                }
//            }
//        }else {
//            
//            KAShoppingCartHeaderTempProductModel *productModel = [KAShoppingCartHeaderTempProductModel yy_modelWithDictionary:promotionModel.info];
//        
//            if (productModel.productItemCore.skuId) {
//                [params setObject:productModel.productItemCore.skuId forKey:@"skuId"];
//            }
//        }
//        
//        
//        [result addObject:params];
//    }
    for (CARTMainSkuItemModel *skuItem in currentShopModel.skuItems) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        if (skuItem.skuId.length) {
            [params setObject:skuItem.skuId forKey:@"skuId"];
        }
        [result addObject:params];
    }
    
    return [result copy];
}

- (JDISVShoppingCartItemModel *)currentShopModelWithResponseObject:(NSDictionary *)responseObject uuid:(NSString *)uuid{
    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    return [mainModel itemModelForKey:uuid];
}

- (void)loadFloorExtraDataSuccess:(void (^)(id _Nullable))success failure:(void (^)(NSError * _Nullable))failure {
    if (success) {
        success(nil);
    }
}

#pragma mark - action
- (JDCDISVAction *)selectAction {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    
    NSDictionary* param;
    if(self.promotionParams.count){
        param = @{@"products":self.productParams?:@"",
                  @"promotions":self.promotionParams?:@""
        };
    }else{
        param = @{@"products":self.productParams?:@""};
    }
    
    NSString *selectTag = @"selectAll";
    BOOL selected = YES;
    if (self.selectedStatus == KAShoppingCartShopSelectedStatusSelected) {
        selectTag = @"unselect";
        selected = NO;
    }else if(self.selectedStatus == KAShoppingCartShopSelectedStatusUnSelected){
        selectTag = @"select";
        selected = YES;
    }
    [result setObject:selectTag forKey:@"userActionId"];
    [result setObject:param forKey:@"operations"];
    
    JDCDISVAction *action = [JDCDISVAction actionWithType:@"JDISVShoppingCartSelectProductAction"];
    action.value = result;
    
    return action;
}

-(void)userSelectAll:(NSString*)selected
          needNotify:(BOOL)notify{
    BOOL edit = [self.commonModel.commonData[@"edit"] boolValue];
    if(!edit){
        return;
    }
    NSMutableArray *selectSkus =  self.commonModel.commonData[@"editModeSkus"];
    NSMutableArray* groups = self.commonModel.commonData[@"editModeGroups"];
//    for(JDISVShoppingCartItemModel* model in self.currentShopModel.subList){
//        NSMutableArray* subSkus = [NSMutableArray array];
//        [self getSubSku:model
//                   skus:subSkus];
//        if([selected isEqualToString:@"select"]){
//            [selectSkus addObjectsFromArray:subSkus];
//        }else{
//            [selectSkus removeObjectsInArray:subSkus];
//        }
//    }
    for(CARTMainSkuItemModel* model in self.curVenderModel.skuItems){
        NSMutableArray* subSkus = [NSMutableArray array];
        [self getSubSku:model
                   skus:subSkus];
        if([selected isEqualToString:@"select"]){
            [selectSkus addObjectsFromArray:subSkus];
        }else{
            [selectSkus removeObjectsInArray:subSkus];
        }
    }
    self.commonModel.commonData[@"editModeSkus"] = selectSkus;
    if(notify){
        [self.commonModel commonDataChange];
    }else{
//        NSString* groupId = self.currentShopModel.info[@"C-M#abstractGroupItem&core"][@"groupId"]?:@"";
        NSString* groupId = self.curVenderModel.vendorId.stringValue;
        if([selected isEqualToString:@"select"]){
            self.selectedStatus = KAShoppingCartShopSelectedStatusSelected;
            self.selected = YES;
            [groups addObject:groupId];
        }else{
            self.selectedStatus = KAShoppingCartShopSelectedStatusUnSelected;
            self.selected = NO;
            [groups removeObject:groupId];
        }
        [self.cell floorDidLoad:self];
    }
}

-(void)getSubSku:(CARTMainSkuItemModel*)model
            skus:(NSMutableArray*)skus{
//    if([model.type.nameSpace containsString:@"cartView.promotion-M"]){
//        NSDictionary* vsDic = [model.info jdcd_getDicElementForKey:@"HJM-D#virtualSuit&virtualSuit"];
//        if(vsDic.count){//virtualsuit
//            BOOL enableCheck = [[vsDic jdcd_getNumberElementForKey:@"enableCheck"] boolValue];
//            
//            NSString* skuId = [vsDic jdcd_getStringElementForKey:@"vSkuId"];
//            
//            if( !enableCheck) {
//                [skus addObject:skuId];
//            }
//        }else{
//            for(JDISVShoppingCartItemModel* subModel in model.subList){
//                [self getSubSku:subModel skus:skus];
//            }
//        }
//    }else{
//        KAShoppingCartProductResultModel *sku = [KAShoppingCartProductResultModel yy_modelWithDictionary:model.itemDict];
//        if( ![sku.info.cartProductExtInfo.enableCheck boolValue]) {
        if( ![model.enableCheck boolValue]) {
//            [skus addObject:sku.info.productItemCore.skuId];
            [skus addObject:model.skuId];
        }
//    }
}
@end
