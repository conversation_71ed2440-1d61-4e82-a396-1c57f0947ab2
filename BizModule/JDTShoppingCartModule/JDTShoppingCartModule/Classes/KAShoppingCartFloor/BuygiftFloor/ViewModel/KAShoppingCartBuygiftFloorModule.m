//
//  KAShoppingCartBuygiftFloorModule.m
//  JDISVAddressModule-JDISVAddressModule
//
//  Created by cdwutao3 on 2023/5/26.
//
#import <JDISVFloorRenderModule/JDISVFloorRegisterManager.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import "JDISVShoppingCartTool.h"
#import "KAShoppingCartBuygiftFloorModule.h"
#import "KAShoppingCartBuygiftFloor.h"
#import "JDISVShoppingCartMainModel.h"
#import <JDISVFloorRenderModule/JDCDISVAction.h>

JDISVRegisterFloorModule(KaCartBuygiftFloor, KAShoppingCartBuygiftFloorModule)

@interface KAShoppingCartBuygiftFloorModule()

@property(strong,nonatomic) JDISVShoppingCartItemModel* cartItem;
@property(strong,nonatomic) JDISVShoppingCartItemModel* parentItem;
@property(strong,nonatomic) JDISVShoppingCartItemModel* superParentItem;

@end


@implementation KAShoppingCartBuygiftFloorModule


- (Class)tableViewFloorClass {
    return KAShoppingCartBuygiftFloor.class;
}

- (CGFloat)floorHeight {
    return 28;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeScrollFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    self.cartItem = data[@"data"];
    self.parentItem = data[@"parentItem"];
    self.superParentItem  =  data[@"superParentItem"];
    self.name = [NSString stringWithFormat:@"%@",self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"name"]?:@""];
    self.skuId = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"id"];
    self.shopName = @"";
    self.shopId = @"";
    self.price = self.cartItem.info[@"C-M#abstractAppendantItem&basic"][@"priceShow"];
    self.imgUrl = self.cartItem.info[@"C-M#abstractAppendantItem&img"][@"imgUrl"];
    self.number = self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"num"]?:@(1);
    NSNumber* stockNum = self.cartItem.info[@"HJM-M#cartProduct&stockInfo"][@"stockCode"];
    
    self.hasStock = [stockNum integerValue] == 0;
    self.isFirst = data[@"isFirst"];
 
}


-(JDCDISVAction*)deleteAction{
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVShoppingCarGiftDeleteAction"];;
    if([self.superParentItem.type.nameSpace containsString:@"cartView.group-M#defaultGroupItem"]){
        NSString* prod1UUID = NSUUID.UUID.UUIDString;
        NSString* prod2UUID = NSUUID.UUID.UUIDString;
        NSDictionary* prod1Dic = @{
            @"id":prod1UUID,
            @"itemType":@(1),
            @"skuId" : self.parentItem.info[@"C-M#productItem&core"][@"skuId"]?:@"",
            @"num":self.parentItem.info[@"C-M#productItem&basic"][@"num"]?:@"1",
            @"childList":@[prod2UUID]
        };
        NSDictionary* prod2Dic = @{
            @"id":prod2UUID,
            @"itemType":@(13),
            @"skuId" : self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"id"]?:@"",
            @"num":self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"num"]?:@"1",
            @"parentId":prod1UUID
        };
        action.value = @{@"operations":@{@"products":@[prod1Dic,prod2Dic]}};
        
    }else{
        NSString* promotinUUID = NSUUID.UUID.UUIDString;
        NSString* prod1UUID = NSUUID.UUID.UUIDString;
        NSString* prod2UUID = NSUUID.UUID.UUIDString;
        NSDictionary* prod1Dic = @{
            @"id":prod1UUID,
            @"parentId":promotinUUID,
            @"itemType":@(1),
            @"skuId" : self.parentItem.info[@"C-M#productItem&core"][@"skuId"]?:@"",
            @"num":self.parentItem.info[@"C-M#productItem&basic"][@"num"]?:@"1",
            @"childList":@[prod2UUID]
        };
        NSDictionary* prod2Dic = @{
            @"id":prod2UUID,
            @"itemType":@(13),
            @"skuId" : self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"id"]?:@"",
            @"num":self.cartItem.info[@"C-M#abstractAppendantItem&core"][@"num"]?:@"1",
            @"parentId":prod1UUID
        };
        NSDictionary* promotionDic = @{@"id": promotinUUID,
                                       @"promotionId":self.superParentItem.info[@"C-M#promotionItem&core"][@"promotionId"],
                                       @"itemType": @(3),
                                       @"num": @"1",
                                       @"childList":@[prod1UUID]};
        
        action.value = @{@"operations":@{@"products":@[prod1Dic,prod2Dic],@"promotions":@[promotionDic]}};
    }
    
    return action;

}
@end
