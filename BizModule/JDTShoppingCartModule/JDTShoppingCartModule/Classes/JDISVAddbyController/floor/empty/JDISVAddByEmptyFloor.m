//
//  JDISVAddByEmptyFloor.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/3.
//

#import "JDISVAddByEmptyFloor.h"
#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>
//#import <JDISVUIKitModule/>
#import <JDISVKAUIKitModule/KAEmptyView.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import "JDISVShoppingCarPub.h"
#import "JDISVFloorRenderModule/JDCDISVAction.h"
#import "JDISVAddByEmptyModule.h"

@interface JDISVAddByEmptyFloor()
@property(strong,nonatomic)JDISVAddByEmptyModule* viewModule;
@end

@implementation JDISVAddByEmptyFloor
-(instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        [self setupUI];
    }
    return self;
}

-(void)setupUI{
    KAEmptyView* emptyView = [[KAEmptyView alloc] initWithFrame:CGRectZero type:KAEmptyViewTypeNotAction];
    emptyView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C1"];
    emptyView.coverImage = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypeNoDataSearchResult];
    
    emptyView.decrible = ShopingCartL(@"ka_cart_empty_exchange_data");
    [self addSubview:emptyView];
    [emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
}

-(void)showTextInpurt:(UIViewController*)vc{
        //提示框添加文本输入框
        UIAlertController* alert = [UIAlertController alertControllerWithTitle:@"test"
                                                                       message:@"SKU"
                                                                preferredStyle:UIAlertControllerStyleAlert];
        
        UIAlertAction* okAction = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault
                                                         handler:^(UIAlertAction * action) {
            //响应事件
            //得到文本信息
            for(UITextField *text in alert.textFields){
                //
//                wt_TestAddby_sku = text.text;
            }
            JDCDISVAction* action1 = [JDCDISVAction actionWithType:kJDCDISVFloorRequestAction];
            [self isv_sendAction:action1];
        }];
        UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel
                                                             handler:^(UIAlertAction * action) {
            //响应事件
            NSLog(@"action = %@", alert.textFields);
        }];
        [alert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
            textField.placeholder = @"login";
        }];
        
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [vc presentViewController:alert animated:YES completion:nil];
        
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"ksa_alert_input_addby"]){
        [self showTextInpurt:controller];
        return YES;
    }
    return NO;
}
@end
