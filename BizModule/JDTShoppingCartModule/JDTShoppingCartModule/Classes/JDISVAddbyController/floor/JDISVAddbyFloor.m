//
//  JDISVAddbyFloor.m
//  JDISVCategoryModule-JDISVCategoryModule
//
//  Created by cdwutao3 on 2022/7/25.
//

#import "JDISVAddbyFloor.h"
#import "JDISVAddbyModule.h"

#import <JDISVThemeModule/UIView+JDISVTheme.h>
#import <JDISVThemeModule/JDISVThemeColor.h>
#import <JDISVThemeModule/JDISVThemeFont.h>
#import <JDISVThemeModule/JDISVThemeSpace.h>
#import <JDISVThemeModule/UILabel+JDISVTheme.h>
#import <JDISVKAIconFontModule/UIImageView+KATheme.h>
#import <JDISVKAIconFontModule/JDISVKAIconFontModule-umbrella.h>
#import <JDISVKAUIKitModule/KAPriceLabel.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVImageModule/UIImageView+JDCDWebImage.h>
#import <JDISVFloorRenderModule/JDCDISVAction.h>
#import <JDISVCategoryModule/JDISVCategoryModule-umbrella.h>
#import <JDBRouterModule/JDBRouterModule-umbrella.h>
#import "JDISVShoppingCarPub.h"

JDCDISVActionType ISVShoppingCartAddyShopAction = @"ISVShoppingCartAddyShopAction";
JDCDISVActionType ISVShoppingCartAddySelectAction = @"ISVShoppingCartAddySelectAction";

@interface JDISVAddbyFloor()

@property(strong,nonatomic) JDISVAddbyModule* viewModel;

@property (strong,nonatomic) UIButton* btnSelect;
@property (strong,nonatomic) UIButton *selectMask;
@property (strong,nonatomic) UIImageView* imgView;
@property (strong,nonatomic) UIView* maskView;
@property (strong,nonatomic) UILabel* labelName;
@property (strong,nonatomic) KAPriceLabel* priceLabel;
@property (strong,nonatomic) UILabel* labelShopName;
@property (strong,nonatomic) UIImageView* labelShopNameTag;
@property (strong,nonatomic) UIButton* btnShop;
@property (strong,nonatomic) KALabel* noStock;

@end

@implementation JDISVAddbyFloor

-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        [self setupUI];
    }
    return self;
}

-(void)setupUI{
    self.imgView = [[UIImageView alloc] initWithFrame:CGRectZero];
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    self.contentView.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C1");
    
    [self.contentView addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self).mas_offset(50);
        make.top.mas_equalTo(self).mas_offset(18);
        make.height.width.mas_equalTo(70);
    }];
    CGFloat r75 = [[JDISVThemeSpace sharedInstance] spaceWithKey:@"#R75"];
    self.imgView.layer.cornerRadius = r75;
    self.imgView.layer.masksToBounds = YES;
    UIColor *color = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C2"];
    self.imgView.backgroundColor = color;
    
    self.noStock = [KALabel kaLabelWithType:KALabelTypeL8 title:ShopingCartL(@"ka_cart_product_no_stock_short") cornerRadius:25];
    [self.imgView addSubview:self.noStock];
    [self.noStock mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.imgView);
        make.width.height.mas_equalTo(50);
    }];
    
    self.maskView = [[UIView alloc] init];
    _maskView.backgroundColor = [[JDISVThemeColor sharedInstance] colorWithKey:@"#C8" alpha:0.02];
    [self.imgView addSubview:self.maskView];
    [self.maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.imgView);
    }];
    
    self.btnSelect = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.contentView addSubview:self.btnSelect];
    [self.btnSelect mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.mas_equalTo(16);
        make.leading.mas_equalTo(self).mas_offset(20);
        make.centerY.mas_equalTo(self.imgView);
    }];
    self.selectMask = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.contentView addSubview:self.selectMask];
    [self.selectMask mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(80);
        make.width.mas_equalTo(50);
        make.leading.mas_equalTo(self);
        make.centerY.mas_equalTo(self.btnSelect);
    }];
    [self.selectMask addTarget:self action:@selector(tapSelect) forControlEvents:UIControlEventTouchUpInside];
    
    self.labelName = [[UILabel alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.labelName];
    [self.labelName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.imgView);
        make.leading.mas_equalTo(self.imgView.mas_trailing).mas_offset(12);
        make.trailing.mas_equalTo(self.mas_trailing).mas_offset(-18);
    }];
    self.labelName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C7");
    self.labelName.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    self.labelName.numberOfLines = 2;
    
    self.priceLabel = [[KAPriceLabel alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.priceLabel];
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.labelName);
        make.top.mas_equalTo(self.imgView).mas_offset(50);
    }];
    
    self.labelShopName = [[UILabel alloc] initWithFrame:CGRectZero];
    [self.contentView addSubview:self.labelShopName];
    [self.labelShopName mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.priceLabel.mas_bottom).mas_offset(7);
        make.leading.mas_equalTo(self.priceLabel);
        make.trailing.mas_lessThanOrEqualTo(-24);
    }];
    self.labelShopName.jdisv_textColorPicker = JDISVColorPickerWithKey(@"#C5");
    self.labelShopName.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    
    self.labelShopNameTag = [[UIImageView alloc] initWithFrame:CGRectZero];
    self.labelShopNameTag.ka_iconImagePicker = KAIconImagePickerWithColorKeyAndUnicodeInSize(@"#C5", JDIF_ICON_ARROW_RIGHT_SMALL, CGSizeMake(12, 12));
    //    if ([UIView appearance].semanticContentAttribute == UISemanticContentAttributeForceRightToLeft) {
    //        [self.labelShopNameTag JDCDRTL];
    //    }
    [self.contentView addSubview:self.labelShopNameTag];
    [self.labelShopNameTag mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(12);
        make.leading.mas_equalTo(self.labelShopName.mas_trailing).mas_offset(5);
        make.centerY.mas_equalTo(self.labelShopName).mas_offset(2);
    }];
    self.btnShop = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.btnShop setTitle:@"" forState:UIControlStateNormal];
    [self.contentView addSubview:self.btnShop];
    [self.btnShop mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.labelShopName);
        make.trailing.mas_equalTo(self.labelShopNameTag);
        make.height.mas_equalTo(28);
        make.centerY.mas_equalTo(self.labelShopName);
    }];
    [self.btnShop addTarget:self action:@selector(tapShop) forControlEvents:UIControlEventTouchUpInside];
}

-(void)tapSelect{
    JDCDISVAction* action = [JDCDISVAction actionWithType:ISVShoppingCartAddySelectAction];
    [self isv_sendAction:action];
}

-(void)tapShop{
    JDCDISVAction* action = [JDCDISVAction actionWithType:ISVShoppingCartAddyShopAction];
    [self isv_sendAction:action];
}

- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    self.viewModel = floorModel;
    self.labelName.text = self.viewModel.name;
    NSString *imageUrl = [PlatformService getCompleteImageUrl:self.viewModel.imgUrl moduleType:JDISVModuleTypeShoppingCart];
    UIImage* placeHoldImg = [[JDISVResourceManager shareInstance] imageWithImageType:JDISVImageTypePlaceholderDefault];
    [self.imgView jdcd_setImage:imageUrl placeHolder:placeHoldImg contentMode:UIViewContentModeScaleAspectFit];
    self.labelShopName.text = self.viewModel.shopName;
    
    if(self.viewModel.price.floatValue == 0){
        [self.priceLabel configNoPrice];
    }else{
        [self.priceLabel configTextWithPrice:self.viewModel.price.floatValue middleLinePrice:self.viewModel.linePrice.floatValue];
    }
    //JH 无门店
    self.labelShopName.hidden = YES;
    self.labelShopNameTag.hidden  =YES;
    self.btnShop.hidden = YES;
    //    if(self.viewModel.hasShop){
    //        if(self.viewModel.shopName.length){
    //            self.labelShopName.text = self.viewModel.shopName;
    //        }else{
    //            self.labelShopName.text = ShopingCartL(@"ka_cart_select_store_tip");
    //        }
    //    }
    
    self.btnSelect.jdisv_selected_B7 = self.viewModel.prdSelected;
    self.noStock.hidden = self.viewModel.hasStock;
    if(!self.viewModel.hasStock){
        self.btnSelect.userInteractionEnabled = NO;
        self.selectMask.userInteractionEnabled = NO;
        NSBundle* bdl = [NSBundle jdcd_moduleWithBundleName:@"JDTShoppingCartModule" class:self.class];
        UIImage *img = [UIImage jdcd_imageWithName:@"isv_shopping_cart_no_select" bundle:bdl];
        [self.btnSelect setImage:img forState:UIControlStateNormal];
    }
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if([action.actionType isEqualToString:ISVShoppingCartAddySelectAction]){
        if(self.viewModel.prdSelected){
            [self.viewModel setProctedSeleted:NO];
        }else{
            [self.viewModel setProctedSeleted:YES];
        }
        self.btnSelect.jdisv_selected_B7 = self.viewModel.prdSelected;
        return YES;
    }
    else if([action.actionType isEqualToString:ISVShoppingCartAddyShopAction]){
        //        UIViewController *vc = [JDRouter openURL:@"router://JDISVProductDetailSDKModule/storeListViewController" arg:@{@"skuId": @"123", @"venderId":@"456"} error:nil completion:^(NSDictionary* result) {
        ////            NSDictionary* shopInfo = result[@"selectedStore"];
        //           
        //         }];
        //        [controller.navigationController pushViewController:vc animated:YES];
        return YES;
    }else if([action.actionType isEqualToString:kJDCDISVFloorDidSelected]){
        if(self.viewModel.isEdit)
            return YES;
        NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeProductDetail];
        NSString *router = [NSString stringWithFormat:@"router://%@/productDetailController",moduleName];
        
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        if (self.viewModel.sku) {
            [params setObject:self.viewModel.sku forKey:@"skuId"];
        }
        UIViewController *productDetailVC = [JDRouter openURL:router arg:params error:nil completion:nil];
        if (productDetailVC) {
            productDetailVC.hidesBottomBarWhenPushed = YES;
            [controller.navigationController pushViewController:productDetailVC animated:YES];
        }
        return YES;
    }
    return NO;
}

@end
