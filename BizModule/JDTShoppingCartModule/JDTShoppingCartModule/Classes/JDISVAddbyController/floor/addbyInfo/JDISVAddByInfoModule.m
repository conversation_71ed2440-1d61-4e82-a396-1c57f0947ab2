//
//  JDISVAddByInfoModule.m
//
//  Created by 吴滔 on 2022/3/7.
//

#import "JDISVAddbyInfoFloor.h"
#import "JDISVAddByInfoModule.h"
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import "JDISVShoppingCartAddbyViewModel.h"
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
JDISVRegisterFloorModule(KaAddByInfoFloor, JDISVAddByInfoModule)


@interface JDISVAddByInfoModule()
@property(weak,nonatomic) JDISVFloorCommonModel* commomData;
@property(weak,nonatomic) JDISVShoppingCartAddbyViewModel *addbyModel;
@end

@implementation JDISVAddByInfoModule
- (UIView *)floorView {
    UIView* v = [[JDISVAddByInfoFloor alloc] init];

    return v;
}

- (CGFloat)floorHeight {
    if(PlatformService.isIphoneX)
        return 80;
    return 50;
}

- (JDISVFloorType)floorType {
    return JDISVFloorTypeBottomFixFloor;
}

- (void)updateData:(NSDictionary *)data commond:(JDISVFloorCommonModel *)commonModel {
    NSDictionary* dataDic = [data jdcd_getDicElementForKey:@"data"];
    self.addbyModel = dataDic[@"addbyModel"];
    self.maxNum = 0;
    self.selectNum = 0;
    self.commomData = commonModel;
    @weakify(self)
    commonModel.commonData[@"selectedNum"] = @(self.addbyModel.selectedNum);
    [commonModel commonDataDidChange:^(NSDictionary * _Nonnull commonData) {
        @strongify(self);
        self.addbyModel.selectedNum = [commonData[@"selectedNum"] integerValue];
    }];
    
    RAC(self,maxNum) = RACObserve(self.addbyModel, MaxSelectNum);
    RAC(self,selectNum) = RACObserve(self.addbyModel, selectedNum);
    
    
};
@end
