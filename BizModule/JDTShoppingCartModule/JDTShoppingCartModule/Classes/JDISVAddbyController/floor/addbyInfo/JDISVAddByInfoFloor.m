//
//  JDISVAddrAddNewFloor.m
//  JDISVAddressModule
//
//  Created by 吴滔 on 2022/3/7.
//

#import "JDISVAddByInfoFloor.h"
#import "JDISVAddByInfoModule.h"
#import "JDISVShoppingAddbyController.h"

#import <JDISVUIKitModule/JDISVUIKitModule-umbrella.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVKAUIKitModule/JDISVKAUIKitModule-umbrella.h>
#import <JDISVMasonryModule/JDISVMasonryModule-umbrella.h>
#import <JDISVThemeModule/JDISVThemeModule-umbrella.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import "JDISVShoppingCarPub.h"
#import <JDISVPlatformModule/JDISVPlatformModule-umbrella.h>
@interface JDISVAddByInfoFloor()
@property (strong,nonatomic) UIButton* addButton;
@property (strong,nonatomic) UILabel* labelInfo;
@property (strong,nonatomic) JDISVAddByInfoModule* viewModule;
@end

@implementation JDISVAddByInfoFloor
-(instancetype)initWithFrame:(CGRect)frame{
   self = [super initWithFrame:frame];
    if(self){
        [self setupUI];
    }
    return self;
}

-(void)setupUI{
    self.addButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self addSubview:self.addButton];
    self.jdisv_backgroundColorPicker = JDISVColorPickerWithKey(@"#C2");
    [self.addButton configKAButtonTitle:ShopingCartL(@"ka_cart_confirm")];
//    [self.addButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    
    if (@available(iOS 8.2, *)) {
        self.addButton.titleLabel.font = [[JDISVThemeFont sharedInstance] fontWithKey:@"#T5" weight:UIFontWeightMedium];
    }
    [self.addButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).mas_offset(5);
        make.width.mas_equalTo(110);
        make.height.mas_equalTo(40);
        make.right.mas_equalTo(self).mas_offset(-12);
    }];
    [self.addButton addTarget:self action:@selector(tapOK) forControlEvents:UIControlEventTouchUpInside];
    [self.addButton renderB2WithCornerRadius:20];
    
    self.labelInfo = [[UILabel alloc] initWithFrame:CGRectZero];
    [self addSubview:self.labelInfo];
    self.labelInfo.jdisv_textColorPicker =  JDISVColorPickerWithKey(@"#C7");
    self.labelInfo.jdisv_fontPicker = JDISVFontPickerWithKey(@"#T7");
    [self.labelInfo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.addButton);
        make.leading.mas_equalTo(self).mas_offset(18);
    }];
}

-(void)tapOK{
    JDCDISVAction* action = [JDCDISVAction actionWithType:@"JDISVAddbyOK"];
    [self isv_sendAction:action];
}

- (BOOL)customReplyAction:(JDCDISVAction *)action controller:(UIViewController *)controller {
    if ([action.actionType isEqualToString:@"JDISVAddbyOK"]) {
        NSString *moduleName = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeLogin];
        NSString *url = [NSString stringWithFormat:@"router://%@/excuteAfterLogin", moduleName];
        return [JDRouter openURL:url arg:nil error:nil completion:^(NSNumber*  _Nullable suc) {
            if(suc.boolValue){
                JDISVShoppingAddbyController* vc = (JDISVShoppingAddbyController*)controller;
                [vc executeCallBack];
            }
        }];
        return YES;
    }
    return NO;
}


- (void)floorDidLoad:(id<JDISVFloorModuleProtocol>)floorModel{
    self.viewModule = floorModel;
    @weakify(self)
    [[RACObserve(self.viewModule, selectNum) distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        self.labelInfo.text = [NSString stringWithFormat:ShopingCartL(@"ka_cart_add_pay_num_tip"),@(self.viewModule.selectNum),@(self.viewModule.maxNum)];
    }];
    [[RACObserve(self.viewModule, maxNum) distinctUntilChanged] subscribeNext:^(id  _Nullable x) {
        @strongify(self)
        self.labelInfo.text = [NSString stringWithFormat:ShopingCartL(@"ka_cart_add_pay_num_tip"),@(self.viewModule.selectNum),@(self.viewModule.maxNum)];
    }];
}
@end
