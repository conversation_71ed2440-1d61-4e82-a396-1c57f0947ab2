//
//  CARTMainModel.h
//  JDTShoppingCartModule
//
//  Created by lvchenzhu.1 on 2025/5/28.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CARTMainSubTotalModel : NSObject

@property (nonatomic, strong) NSNumber *selectedProductNum;

@property (nonatomic, copy) NSString *productNum;

@property (nonatomic, strong) NSNumber *productKindNum;

@property (nonatomic, strong) NSNumber *enableCheck;

@property (nonatomic, strong) NSNumber *activeCheck;
// 这几个返回的都是 511.00 类型的，看下 Number 接收有没有问题
@property (nonatomic, strong) NSNumber *totalAmount;

@property (nonatomic, strong) NSNumber *totalDiscountAmount;

@property (nonatomic, strong) NSNumber *totalRealAmount;

@end

@interface CARTMainSubTotalDetailModel : NSObject

@property (nonatomic, strong) NSNumber *seq;

@property (nonatomic, copy) NSString *name;

@property (nonatomic, copy) NSString *price;

@end

@interface CARTMainProductSpecModel : NSObject

@property (nonatomic, copy) NSString *name;

@property (nonatomic, copy) NSString *value;

@end

@interface CARTMainPromotionModel : NSObject

@property (nonatomic, copy) NSString *promoId;

@property (nonatomic, copy) NSString *activityType;

@property (nonatomic, copy) NSString *activitySubType;

@property (nonatomic, strong) NSNumber *remainTime;

@end

@interface CARTMainSkuItemModel : NSObject

@property (nonatomic, copy) NSString *skuName;

@property (nonatomic, copy) NSString *imgUrl;

@property (nonatomic, strong) NSNumber *num;
/// 是否已经选中
@property (nonatomic, strong) NSNumber *activeCheck;
/// 是否支持选中
@property (nonatomic, strong) NSNumber *enableCheck;

@property (nonatomic, strong) NSNumber *originalPrice;

@property (nonatomic, strong) NSNumber *salePrice;

@property (nonatomic, copy) NSString *maxLimitNum;

@property (nonatomic, copy) NSString *minLimitNum;

@property (nonatomic, strong) NSNumber *maxBuyNum;

@property (nonatomic, strong) NSNumber *minBuyNum;

@property (nonatomic, strong) NSNumber *remainNum;
/// 1 有货 0 无货
@property (nonatomic, strong) NSNumber *stockStatus;

@property (nonatomic, copy) NSString *skuId;

@property (nonatomic, copy) NSString *id;

@property (nonatomic, copy) NSArray *productMarkSet;

@property (nonatomic, copy) NSArray <CARTMainProductSpecModel *> *productSpecs;

@property (nonatomic, copy) NSArray <CARTMainPromotionModel *> *promotionList;

@end

@interface CARTMainVenderModel : NSObject

@property (nonatomic, strong) NSNumber *enableCheck;

@property (nonatomic, strong) NSNumber *activeCheck;

@property (nonatomic, copy) NSString *venderName;

@property (nonatomic, strong) NSNumber *vendorId;

@property (nonatomic, copy) NSString *shopName;

@property (nonatomic, strong) NSNumber *shopId;

@property (nonatomic, copy) NSArray <CARTMainSkuItemModel *> *skuItems;

@property (nonatomic, assign) BOOL hasCoupon;

@end

@interface CARTMainModel : NSObject
/// 结算底部栏副数据信息
@property (nonatomic, strong) CARTMainSubTotalModel *subTotal;
/// 结算底部栏数据信息
@property (nonatomic, copy) NSArray <CARTMainSubTotalDetailModel *> *subTotalDetails;
///  商品列表
@property (nonatomic, copy) NSArray <CARTMainVenderModel *> *venderList;

@end

NS_ASSUME_NONNULL_END
