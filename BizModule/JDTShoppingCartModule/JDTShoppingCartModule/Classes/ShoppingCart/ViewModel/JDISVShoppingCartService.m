//
//  JDISVShoppingCartService.m
//  JDISVFloorRenderModule
//
//  Created by 罗静 on 2021/6/15.
//

#import "JDISVShoppingCartService.h"

#import <JDBRouterModule/JDRouter.h>

#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVYYModelModule/YYModel.h>

#import <JDISVPlatformModule/JDISVPlatformService.h>
#import <JDISVFloorRenderModule/JDISVFloorRenderModule-umbrella.h>
#import <JDISVFloorRenderModule/JDCDISVBaseFloorGroupModel.h>
#import <JDISVPlatformModule/JDISVResourceManager.h>
#import <JDBRouterModule/JDRouter.h>

#import "JDISVShoppingCartMainModel.h"

#import "JDISVShoppingCartTool.h"
#import "JDISVShoppingTracker.h"
#import "CARTMainModel.h"

@import JDTInfrastructureModule;

NSString * const kShoppingCartCountChangeNotificationName = @"ShoppingCartCountChangeNotificationName";

@interface JDISVShoppingCartService ()

@property (nonatomic, strong, readwrite) NSDictionary *responseObject;
@property (nonatomic, strong, readwrite) CARTMainModel *cartMainModel;

/** 当前商品数量 */
@property (nonatomic, strong)  NSNumber * count;
/// 临时模拟的固定参数
@property (nonatomic, strong) NSDictionary *fixParams;

@end

@implementation JDISVShoppingCartService

+ (instancetype)sharedInstance {
    static JDISVShoppingCartService *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[JDISVShoppingCartService alloc] init];
    });
    return instance;
    
}

- (instancetype)init {
    if (self = [super init]) {
        //监听商品数量改变
        [[[RACObserve(self, count)  distinctUntilChanged]
          deliverOnMainThread]
         subscribeNext:^(id  _Nullable x) {
            if (x) {
                [[NSNotificationCenter defaultCenter] postNotificationName:kShoppingCartCountChangeNotificationName object:nil userInfo:@{@"count":x}];
            }
        }];
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginSuccess) name:ISVLoginedNotification object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginOut) name:ISVLogoutNotification object:nil];
    }
    return self;
}

- (void)loginSuccess {
    @weakify(self);
    [[[JDISVShoppingCartService sharedInstance] getCartCount] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        self.count = x;
    } error:^(NSError * _Nullable error) {
        
    }];
}

- (void)loginOut {
    self.count = nil;
}

- (void)progressOriginData:(NSDictionary *)responseObject {
    self.responseObject = responseObject;
}

- (RACSignal *)getShoppingCart {
    
    __block NSDate* sendDate;
    __block NSDate* receiveDate;
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        [params setObject:@"getCart" forKey:@"userActionId"];
        sendDate = [NSDate date];
        //        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_getCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
        //            @strongify(self);
        //            receiveDate = [NSDate date];
        //            if (!error) {
        //                //宜宾 标品兼容
        //                NSNumber* code = responseObject[@"code"];
        //                NSNumber* rcode = responseObject[@"resultCode"];
        //                if(code && code.intValue == 0 && rcode && rcode.intValue == 0){
        //                    responseObject = responseObject[@"resultInfo"];
        //                }
        //                [self progressOriginData:responseObject];
        //                NSInteger count = [self cartCountWithRespostObject:responseObject];
        //                self.count = @(count);
        //                [subscriber sendNext:responseObject];
        //            }else {
        //                [subscriber sendError:error];
        //            }
        //        }];
        
        
        [[OOPNetworkManager sharedManager] POST:@"cart/c/get?apiCode=b2c.cbff.cart.c.get" parameters:self.fixParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    NSDictionary *data = responseObject[@"data"];
                    [self progressOriginData:data];
                    NSInteger count = [self cartCountWithRespostObject:data];
                    self.count = @(count);
                    [subscriber sendNext:data];
                }
            } else {
                [subscriber sendError:error];
            }
        }];
        
        return nil;
    }];
    return [[signal doNext:^(id  _Nullable x) {
        NSTimeInterval time = [receiveDate timeIntervalSinceDate:sendDate];
        [self sendFireBaseLoadCart:YES time:time];
    }]doError:^(NSError * _Nonnull error) {
        NSTimeInterval time = [receiveDate timeIntervalSinceDate:sendDate];
        [self sendFireBaseLoadCart:NO time:time ];
    }];
}

-(void)sendFireBaseLoadCart:(BOOL)isOk time:(NSTimeInterval)time{
    if(isOk){
        [JDISVShoppingTracker firPerformanceEvent:@"CartPageLoadPerf" param:@{@"time":@(time)}];
    }else{
        [JDISVShoppingTracker firPerformanceEvent:@"CartPageLoadPerf_err" param:@{@"time":@(time)}];
    }
}

- (RACSignal *)addProductToCartWithItems:(NSDictionary *)items {
    @weakify(self)
    __block NSDate* sendDate;
    __block NSDate* receiveDate;
    NSNumber* isBuyNow = items[@"isNormalBuyNow"];
    if(isBuyNow.boolValue){
        NSMutableDictionary* tempDic = [items mutableCopy];
        [tempDic removeObjectForKey:@"isNormalBuyNow"];
        items = [tempDic copy];
    }
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        [params setObject:@"addCart" forKey:@"userActionId"];
        
        if (nil == params[@"operations"]) {
            NSError *tempError = [NSError errorWithDomain:@"shoppingCart.error" code:-1 userInfo:@{NSLocalizedDescriptionKey:@""}];
            [subscriber sendError:tempError];
            return nil;
        }
        sendDate = [NSDate date];
//        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_addCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self)
//            receiveDate = [NSDate date];
//            if (!error) {
//                if (responseObject.allValues.count) {
//                    [self progressOriginData:responseObject];
//                    
//                    NSInteger count = [self cartCountWithRespostObject:responseObject];
//                    self.count = @(count);
//                    [subscriber sendNext:@(count)];
//                }else {
//                    NSError *tempError = [NSError errorWithDomain:@"shoppingCart.error" code:-1 userInfo:@{NSLocalizedDescriptionKey:ShopingCartL(@"ka_cart_adderr_normal")}];
//                    [subscriber sendError:tempError];
//                }
//            }else {
//                
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSMutableDictionary *testParams = [NSMutableDictionary dictionary];
        [testParams addEntriesFromDictionary:self.fixParams];
        
        NSArray *skuList = items[@"operations"][@"products"];
        NSMutableArray *newSkuList = [NSMutableArray array];
        for (NSDictionary *sku in skuList) {
            NSDictionary *newSku = @{
                @"skuId": sku[@"skuId"],
                @"skuNum": sku[@"num"]
            };
            [newSkuList addObject:newSku];
        }
        testParams[@"skuList"] = newSkuList;
        [[OOPNetworkManager sharedManager] POST:@"cart/c/add?apiCode=b2c.cbff.cart.c.add" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
//                    RACSignal *countSignal = [self forceGetCartCount:YES];
//                    [countSignal subscribeNext:^(id  _Nullable x) {
//                        [subscriber sendNext:x];
//                    } error:^(NSError * _Nullable error) {
//                        [subscriber sendError:error];
//                    }];
                    // 这里传了 count 后面没有用到，先传 0
                    [subscriber sendNext:@(0)];
                } else {
                    NSError *tempError = [NSError errorWithDomain:@"shoppingCart.error" code:-1 userInfo:@{NSLocalizedDescriptionKey:ShopingCartL(@"ka_cart_adderr_normal")}];
                    [subscriber sendError:tempError];
                }
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
    return [signal  doError:^(NSError * _Nonnull error) {
        NSTimeInterval time = [receiveDate timeIntervalSinceDate:sendDate];
        [self sendFireBaseAddCart:isBuyNow.boolValue time:time];
    }];
}

-(void)sendFireBaseAddCart:(BOOL)isBuyNow time:(NSTimeInterval)time{
    if(isBuyNow){
        //商详加埋点，购物车不加
//        [JDISVShoppingTracker firPerformanceEvent:@"BuyNowError" param:@{@"time":@(time)}];
    }else{
        [JDISVShoppingTracker firPerformanceEvent:@"AddToCartError" param:@{@"time":@(time)}];
    }
}

- (RACSignal *)removeProductWithItems:(NSDictionary *)items {
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        [params setObject:@"removeCart" forKey:@"userActionId"];
        
        
//        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self)
//            if (!error) {
//                [self progressOriginData:responseObject];
//
//                NSInteger count = [self cartCountWithRespostObject:responseObject];
//                self.count = @(count);
//                [subscriber sendNext:@(count)];
//            }else {
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSMutableDictionary *testParams = [NSMutableDictionary dictionary];
        [testParams addEntriesFromDictionary:self.fixParams];
        
        // 指定删除（如果是删除全部，将所有skuId传入即可）
        testParams[@"deleteType"] = @(1);
        
        NSArray *skuList = items[@"operations"][@"products"];
        NSMutableArray *newSkuList = [NSMutableArray array];
        for (NSDictionary *sku in skuList) {
            NSDictionary *newSku = @{
                @"skuId": sku[@"skuId"],
                @"skuNum": sku[@"num"]
            };
            [newSkuList addObject:newSku];
        }
        testParams[@"skuList"] = newSkuList;
        
        [[OOPNetworkManager sharedManager] POST:@"cart/c/remove?apiCode=b2c.cbff.cart.c.remove" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    NSDictionary *data = responseObject[@"data"];
                    [self progressOriginData:responseObject];
                    NSInteger count = [self cartCountWithRespostObject:data];
                    self.count = @(count);
                    [subscriber sendNext:@(count)];
                }
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
    return signal;
}
- (RACSignal *)removeGiftWithItems:(NSDictionary *)items{
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        [params setObject:@"removeGift" forKey:@"userActionId"];

        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
            @strongify(self)
            if (!error) {
                [self progressOriginData:responseObject];
                
                NSInteger count = [self cartCountWithRespostObject:responseObject];
                self.count = @(count);
                [subscriber sendNext:@(count)];
            }else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        
        return nil;
    }];
    return signal;
}

- (RACSignal *)removeAddbyProductWithItems:(NSDictionary *)items {
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        [params setObject:@"removeCart" forKey:@"userActionId"];
        
        
        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
            @strongify(self)
            if (!error) {
                [self progressOriginData:responseObject];

                NSInteger count = [self cartCountWithRespostObject:responseObject];
                self.count = @(count);
                [subscriber sendNext:@(count)];
            }else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        
        return nil;
    }];
    return signal;
}

- (RACSignal *)selectProductWithItems:(NSDictionary *)items {
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self);
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
//        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self)
//            if (!error) {
//                [self progressOriginData:responseObject];
//                
//                NSInteger count = [self cartCountWithRespostObject:responseObject];
//                self.count = @(count);
//                [subscriber sendNext:@(count)];
//            }else {
//                
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSMutableDictionary *testParams = [NSMutableDictionary dictionary];
        [testParams addEntriesFromDictionary:self.fixParams];
        
        NSString *action = items[@"userActionId"];
        if ([action isEqualToString:@"select"]) {
            testParams[@"type"] = @(1);
        } else if ([action isEqualToString:@"unselect"]) {
            testParams[@"type"] = @(2);
        } else if ([action isEqualToString:@"selectAll"]) {
            testParams[@"type"] = @(3);
        } else if ([action isEqualToString:@"unselectAll"]) {
            testParams[@"type"] = @(4);
        }
        NSArray *skuList = items[@"operations"][@"products"];
        NSMutableArray *newSkuList = [NSMutableArray array];
        for (NSDictionary *sku in skuList) {
            NSDictionary *newSku = @{
                @"skuId": sku[@"skuId"],
                @"skuNum": sku[@"num"]
            };
            [newSkuList addObject:newSku];
        }
        testParams[@"skuList"] = newSkuList;
        
        [[OOPNetworkManager sharedManager] POST:@"/cart/c/select?apiCode=b2c.cbff.cart.c.select" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                if ([responseObject[@"code"] isEqualToString:@"0"]) {
                    NSDictionary *data = responseObject[@"data"];
                    [self progressOriginData:data];
                    NSInteger count = [self cartCountWithRespostObject:data];
                    self.count = @(count);
                    [subscriber sendNext:data];
                }
            } else {
                [subscriber sendError:error];
            }
        }];
        
        return nil;
    }];
    return signal;
}

- (RACSignal *)changeProductCountWithItems:(NSDictionary *)items {
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        [params setObject:@"changeNum" forKey:@"userActionId"];

//        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self)
//            if (!error) {
//                [self progressOriginData:responseObject];
//                
//                NSInteger count = [self cartCountWithRespostObject:responseObject];
//                self.count = @(count);
//                [subscriber sendNext:@(count)];
//            }else {
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSMutableDictionary *testParams = [NSMutableDictionary dictionary];
        [testParams addEntriesFromDictionary:self.fixParams];
        
        NSArray *skuList = items[@"operations"][@"products"];
        NSMutableArray *newSkuList = [NSMutableArray array];
        for (NSDictionary *sku in skuList) {
            NSDictionary *newSku = @{
                @"skuId": sku[@"skuId"],
                @"skuNum": sku[@"num"]
            };
            [newSkuList addObject:newSku];
        }
        testParams[@"skuList"] = newSkuList;
        
        [[OOPNetworkManager sharedManager] POST:@"cart/c/changeItemNum?apiCode=b2c.cbff.cart.c.changeItemNum" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                NSDictionary *data = responseObject[@"data"];
                [self progressOriginData:data];
                NSInteger count = [self cartCountWithRespostObject:responseObject];
                self.count = @(count);
                [subscriber sendNext:@(count)];
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
    return signal;
}

/**
 * 收藏商品
 *
 * 接口：https://cf.jd.com/pages/viewpage.action?pageId=364742325
 */
- (RACSignal *)collectionProductWithItems:(NSDictionary *)items {
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        NSArray *tempProducts = items[@"operations"][@"products"];
        NSMutableArray *skus = [NSMutableArray array];
        for (NSDictionary *dict in tempProducts) {
            if (dict[@"skuId"]) {
                [skus addObject:dict[@"skuId"]];
            }
        }
        
        NSString *module = [[JDISVResourceManager shareInstance] moduleNameWithModuleType:JDISVModuleTypeFavorite];
        NSString *router = [NSString stringWithFormat:@"router://%@/focusProduct",module];
        
        [JDRouter openURL:router arg:@{@"skus":skus} error:nil completion:^(id  _Nullable object) {
            @strongify(self)
            if ([object isKindOfClass:NSNumber.class] && [object boolValue]) {
                [[self removeProductWithItems:items] subscribeNext:^(id  _Nullable x) {
                    [subscriber sendNext:x];
                    [subscriber sendCompleted];
                } error:^(NSError * _Nullable error) {
                    [subscriber sendNext:@(0)];
                    [subscriber sendCompleted];
                }];
            }else {
                NSError *error = [[NSError alloc] initWithDomain:@"" code:-1 userInfo:nil];
                [subscriber sendError:error];
                [subscriber sendCompleted];
            }
        }];
        
        
        return nil;
    }];
    return signal;
}

/**
 * 切换商品
 * 接口：https://cf.jd.com/pages/viewpage.action?pageId=422669422
 */
- (RACSignal *)changeProductWithItems:(NSDictionary *)items {
    @weakify(self)
    RACSignal *siganl = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        [params setObject:@"switchCart" forKey:@"userActionId"];
        
//        [PlatformService isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self)
//            if (!error) {
//                [self progressOriginData:responseObject];
//                
//                NSInteger count = [self cartCountWithRespostObject:responseObject];
//                self.count = @(count);
//                [subscriber sendNext:@(count)];
//            }else {
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        NSMutableDictionary *testParams = [NSMutableDictionary dictionary];
        [testParams addEntriesFromDictionary:self.fixParams];
        
        NSArray *skuList = items[@"operations"][@"products"];
        NSMutableArray *newSkuList = [NSMutableArray array];
        for (NSDictionary *sku in skuList) {
            NSDictionary *newSku = @{
                @"skuId": sku[@"skuId"],
                @"skuNum": sku[@"num"],
                @"targetSkuId": sku[@"targetSkuId"]
            };
            [newSkuList addObject:newSku];
        }
        testParams[@"skuList"] = newSkuList;
        
        [[OOPNetworkManager sharedManager] POST:@"cart/c/switchSku?apiCode=b2c.cbff.cart.c.switchSku" parameters:testParams headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                NSDictionary *data = responseObject[@"data"];
                [self progressOriginData:data];
                NSInteger count = [self cartCountWithRespostObject:responseObject];
                self.count = @(count);
                [subscriber sendNext:@(count)];
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
    return siganl;
}

/**
 * 切换促销
 * 接口：https://cf.jd.com/pages/viewpage.action?pageId=605887976
 */
-(RACSignal*)changePromotion:(NSDictionary*)items{
    RACSignal *siganl = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        NSString *address = [JDISVShoppingCartTool addressIds];
        if (address) {
            [params setObject:address forKey:@"area"];
        }
        if (items) {
            [params addEntriesFromDictionary:items];
        }
        
        [params setObject:@"changePromotion" forKey:@"userActionId"];
        @weakify(self)
        [PlatformService isv_shoppingCart_request:JDCDHTTPSessionRequestTypePost function:@"se_operateCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
            @strongify(self)
            if (!error) {
                [self progressOriginData:responseObject];
                
                NSInteger count = [self cartCountWithRespostObject:responseObject];
                self.count = @(count);
                [subscriber sendNext:@(count)];
            }else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        return nil;
    }];
    return siganl;
}

- (RACSignal *)forceGetCartCount:(BOOL)isForce{
    @weakify(self)
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        @strongify(self)
        
        //非强制刷新直接返回
        if(!isForce){
            if (self.count) {
                [subscriber sendNext:self.count];
                return nil;
            }
        }
        
        //登录未有值
//        NSMutableDictionary *params = [NSMutableDictionary dictionary];
//        
//        NSString *address = [JDISVShoppingCartTool addressIds];
//        if (address) {
//            [params setObject:address forKey:@"area"];
//        }
//        [params setObject:@"getCart" forKey:@"userActionId"];
//        
//        [[JDISVPlatformService sharedService] isv_shoppingCart_request:JDCDHTTPSessionRequestTypeGet function:@"se_getCart" parameters:params complete:^(JDCDURLTask * _Nonnull dataTask, NSDictionary * _Nullable responseObject, NSError * _Nullable error) {
//            @strongify(self)
//            if (!error) {
//                NSInteger count = [self cartCountWithRespostObject:responseObject];
//                self.count = @(count);
//                [subscriber sendNext:@(count)];
//            }else {
//                
//                [subscriber sendError:error];
//            }
//            [subscriber sendCompleted];
//        }];
        
        [[OOPNetworkManager sharedManager] POST:@"cart/c/getAllSkuSpeciesNum?apiCode=b2c.cbff.cart.c.getAllSkuSpeciesNum" parameters:@{} headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
            if (!error) {
                NSUInteger count = [responseObject[@"data"][@"allSkuSpeciesNum"] integerValue];
                self.count = @(count);
                [subscriber sendNext:@(count)];
            } else {
                [subscriber sendError:error];
            }
            [subscriber sendCompleted];
        }];
        
        return nil;
    }];
    return signal;
}

- (RACSignal *)getCartCount {
    return [self forceGetCartCount:NO];
}

- (NSInteger)cartCountWithRespostObject:(NSDictionary *)responseObject {
//    NSInteger count = 0;
//    @try {
//        NSNumber* numberCount = responseObject[@"data"][@"subtotal"][@"info"][@"HJM-D#subtotal&number"][@"productKindNum"];
//        count = numberCount.integerValue;
//    } @catch (NSException *exception) {
//        
//    }
//    return count;
    
    
    return 3;
}

- (NSDictionary *)fixParams {
    if (!_fixParams) {
        _fixParams = @{
            @"commonParam": @{
                @"isReturnCart": @1,
                @"addressId": @1347
            },
            @"lbsParam": @{
                @"coordinateParam": @{
                      @"longitude": @(116.36685),
                      @"latitude": @(39.94031)
                    },
                @"provinceId": @"110000",
                @"cityId": @"110100",
                @"districtId": @"110102",
                @"townId": @"110102003",
                @"fullAddress": @"",
                @"provinceName": @"北京市",
                @"cityName": @"北京市",
                @"districtName": @"西城区",
                @"townName": @"新街口街道",
                @"latitude": @(39.94031),
                @"longitude": @(116.36685)
            }
        };
    }
    return _fixParams;
}

@end
