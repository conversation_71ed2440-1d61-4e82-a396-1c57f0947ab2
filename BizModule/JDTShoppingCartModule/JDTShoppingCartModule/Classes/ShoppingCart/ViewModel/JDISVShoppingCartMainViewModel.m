//
//  JDISVShoppingCartMainViewModel.m
//  JDISVShoppingCartSDKModule
//
//  Created by 罗静 on 2021/6/18.
//

#import "JDISVShoppingCartMainViewModel.h"

#import <JDISVYYModelModule/YYModel.h>
#import <JDISVReactiveObjCModule/ReactiveObjC.h>
#import <JDISVIQKeyboardManagerModule/IQKeyboardManager.h>
#import <JDISVFloorRenderModule/JDISVFloorConfigManager.h>
#import <JDISVCategoryModule/NSDictionary+JDCDExtend.h>
#import <JDISVPlatformModule/JDISVPlatformService.h>
#import "JDISVShoppingTracker.h"
#import "JDISVShoppingCartTool.h"
#import "JDISVShoppingCartMainModel.h"
#import "JDISVShoppingCartService.h"
#import "KAShoppingCartProductModel.h"
#import <objc/runtime.h>
#import "CARTMainModel.h"

@interface JDISVShoppingCartMainViewModel ()

//暂存IQKeyboard相关设置
@property (nonatomic, assign) BOOL keyboardEnable;
@property (nonatomic, assign) BOOL keyboardEnableAutoToolbar;
@property (nonatomic, assign) BOOL keyboardShouldResignOnTouchOutside;

@end

@implementation JDISVShoppingCartMainViewModel

- (RACSignal *)loadData {
    
    @weakify(self);
    BOOL islogin = [[JDISVPlatformService sharedService] getUserIsLogin];
//    if (!islogin) {
//        RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
//            @strongify(self);
//            [self propressEmptyCartFloorData];
//            [subscriber sendCompleted];
//            return nil;
//        }];
//        return signal;
//    }
    RACSignal *signal = [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
        [[[JDISVShoppingCartService sharedInstance] getShoppingCart] subscribeNext:^(id  _Nullable responseObject) {
            @strongify(self);
            [self progressResponseObject:responseObject];
            [subscriber sendCompleted];
        } error:^(NSError * _Nullable error) {
            @strongify(self);
            [self progressErrorCartFloorrData];
            [subscriber sendCompleted];
        }];
        return nil;
    }];
    
    return signal;
}

- (void)progressResponseObject:(NSDictionary *)responseObject {
    self.allFloorOriginData = responseObject;
//    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
//    NSString *nameSpace = @"core.trade-DM#cartView.group-M#defaultGroupItem";
//    NSArray *group = [mainModel subItemsModelForNameSpace:nameSpace];
//    NSArray *invalid = [mainModel subItemsModelForNameSpace:@"core.trade-DM#cartView.unusableProduct-M#defaultUnusableProductItem"];
//    if (group.count || invalid.count) {
//        //有数据
//        if(KSAAPP){
//            [self progressKSASuccessCartFloorDataWithResponse:responseObject];
//        }else{
//            [self progressSuccessCartFloorDataWithResponse:responseObject];
//        }
//    }else {
//        //无数据
//        [self propressEmptyCartFloorData];
//    }
    
    CARTMainModel *cartMainModel = [CARTMainModel yy_modelWithDictionary:responseObject];
    if (cartMainModel.venderList.count) {
        [self progressKSASuccessCartFloorDataWithResponse:responseObject];
    } else {
        [self propressEmptyCartFloorData];
    }
}

- (NSArray<NSArray<NSDictionary *> *> *)preloadingFloorData {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KSACartNavBarFloor",
                @"data":@{
                        @"optionEnable":@(0)
                },
                @"ext":@{
                        
                }
            }
        ],
        @[
            @{
                @"floorId":@"KaCartSkeletonFloor",
                @"data":@{
                        
                },
                @"ext":@{
                        
                }
            }
        ],
    ];
    return [data copy];
}

-(NSArray*)getExistsSkus:(NSDictionary*)responseObject{
    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    
    NSMutableArray *skus = [NSMutableArray array];
    for (JDISVShoppingCartItemModel *itemModel in mainModel.rootHierarchy.subList) {
        NSString *nameSpace = @"core.trade-DM#cartView.group-M#defaultGroupItem";
        if ([itemModel.type.nameSpace isEqualToString:nameSpace]) {
            
            for (JDISVShoppingCartItemModel *subItemModel in itemModel.subList) {
                NSNumber* sku;
                if (subItemModel.subList.count) {
                    for (JDISVShoppingCartItemModel *productModel in subItemModel.subList) {
                        sku = productModel.itemDict[@"info"][@"C-M#productItem&core"][@"skuId"];
                    }
                }else {
                    sku = subItemModel.itemDict[@"info"][@"C-M#productItem&core"][@"skuId"];
                }
                if([sku isKindOfClass:NSNumber.class] && sku.integerValue){
                    [skus addObject:sku];
                }
                if([sku isKindOfClass:NSString.class]){
                    if(((NSString*)sku).integerValue){
                        [skus addObject:@(((NSString*)sku).integerValue)];
                    }
                }
            }
            
        }
    }
    
    return skus;
}

- (void)progressKSASuccessCartFloorDataWithResponse:(NSDictionary *)responseObject {
    
    NSMutableArray *result = [NSMutableArray array];
    
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:KaShoppingCartPageId];
    NSArray *floors = config[@"layout"];
    NSDictionary *features = config[@"features"][@"floors"];
    for (NSArray *tempGroup in floors) {
        NSMutableArray *group = [NSMutableArray array];
        for (NSString *floorId in tempGroup) {
            NSMutableDictionary *floor = [NSMutableDictionary dictionary];
            if (floorId) {
                [floor setObject:floorId forKey:@"floorId"];
            }
            if (features[floorId]) {
                if([floorId isEqualToString:@"KaProductRecommendFloor"]){
                    NSArray* skus = [self getExistsSkus:responseObject];
                    NSMutableDictionary* featruesDic =  [features[floorId] mutableCopy];
                    if(!featruesDic){
                        featruesDic = [NSMutableDictionary dictionary];
                    }
                    //WT_TODO_JH不使用此参数
                    if(skus.count){
                        featruesDic[@"se_search_recommend_ignoreSkuIds"] = skus;
                    }
                    [floor setObject:featruesDic forKey:@"ext"];
                }else{
                    [floor setObject:features[floorId] forKey:@"ext"];
                }
            }
            if([floorId isEqualToString:@"KSACartNavBarFloor"]){
                NSDictionary* featureNav =  features[floorId];
                NSNumber* needLoginFloor = featureNav[@"needLoginFloor"];
                if (needLoginFloor.boolValue) {
                    NSDictionary *loginFloor = @{
                        @"floorId":@"KSAShoppingCartLoginFloor",
                        @"data":@{},
                        @"ext":@{}
                    };
                    [group addObject:loginFloor];
                }
            }
            [floor setObject:responseObject forKey:@"data"];
            
            [group addObject:floor];
        }
        
        NSDictionary *deliver = @{
            @"floorId":@"kDeliverFloor",
            @"data":@{
                
            },
            @"ext":@{
                
            }
        };
        [group addObject:deliver];
        
        [result addObject:group];
        
    }
    self.floorGroups = [result copy];
}

- (void)progressSuccessCartFloorDataWithResponse:(NSDictionary *)responseObject {
    
    NSMutableArray *result = [NSMutableArray array];
    
    NSDictionary *config = [[JDISVFloorConfigManager sharedInstance] configWithPageId:KaShoppingCartPageId];
    NSArray *floors = config[@"layout"];
    NSDictionary *features = config[@"features"][@"floors"];
    for (NSArray *tempGroup in floors) {
        NSMutableArray *group = [NSMutableArray array];
        for (NSString *floorId in tempGroup) {
            NSMutableDictionary *floor = [NSMutableDictionary dictionary];
            if (floorId) {
                [floor setObject:floorId forKey:@"floorId"];
            }
            if (features[floorId]) {
                if([floorId isEqualToString:@"KaProductRecommendFloor"]){
                    NSArray* skus = [self getExistsSkus:responseObject];
                    NSMutableDictionary* featruesDic =  [features[floorId] mutableCopy];
                    if(!featruesDic){
                        featruesDic = [NSMutableDictionary dictionary];
                    }
                    //WT_TODO_JH不使用此参数
                    if(skus.count){
                        featruesDic[@"se_search_recommend_ignoreSkuIds"] = skus;
                    }
                    [floor setObject:featruesDic forKey:@"ext"];
                }else{
                    [floor setObject:features[floorId] forKey:@"ext"];
                }
            }
            
            [floor setObject:responseObject forKey:@"data"];
            
            [group addObject:floor];
        }
        
        NSDictionary *deliver = @{
            @"floorId":@"kDeliverFloor",
            @"data":@{
                    
            },
            @"ext":@{
                    
            }
        };
        [group addObject:deliver];
        
        [result addObject:group];
        
    }
    self.floorGroups = [result copy];
}

- (void)propressEmptyCartFloorData {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KSACartNavBarFloor",
                @"data":@{
                        @"optionEnable":@(0)
                },
                @"ext":@{
                    @"Nodata":@(YES)
                }
            },
            @{
                @"floorId":@"KSAShoppingCartLoginFloor",
                @"data":@{
                    @"error":@(0),
                    @"empty":@(1),
                    @"isEmptyPage":@(1)
                },
                @"ext":@{
                    
                }
            }
        ]
        ,
        @[
            @{
                @"floorId":@"KaCartErrorFloor",
                @"data":@{
                        @"error":@(0),
                        @"empty":@(1)
                },
                @"ext":@{
                        
                }
            }
        ]
    ];
    self.floorGroups = data;
}


- (void)progressErrorCartFloorrData {
    NSArray *data = @[
        @[
            @{
                @"floorId":@"KSACartNavBarFloor",
                @"data":@{
                        @"optionEnable":@(0)
                },
                @"ext":@{
                    @"Nodata":@(YES)
                }
            },
            @{
                @"floorId":@"KSAShoppingCartLoginFloor",
                @"data":@{
                    @"error":@(0),
                    @"empty":@(1),
                    @"isEmptyPage":@(1)
                },
                @"ext":@{
                    
                }
            }
        ],
        
        @[
            @{
                @"floorId":@"KaCartErrorFloor",
                @"data":@{
                        @"error":@(1),
                        @"empty":@(0)
                },
                @"ext":@{
                        
                }
            }
        ]
    ];
    self.floorGroups = data;
}

#pragma mark - IQKeyboard
- (void)setIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = YES;
    [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = YES;
}

- (void)storeIQKeyboardSetting {
    self.keyboardEnable = [IQKeyboardManager sharedManager].enable;
    self.keyboardEnableAutoToolbar = [IQKeyboardManager sharedManager].enableAutoToolbar;
    self.keyboardShouldResignOnTouchOutside = [IQKeyboardManager sharedManager].shouldResignOnTouchOutside;
}

- (void)restoreIQKeyboardSetting {
    [IQKeyboardManager sharedManager].enable = self.keyboardEnable;
    [IQKeyboardManager sharedManager].enableAutoToolbar = self.keyboardEnableAutoToolbar;
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = self.keyboardShouldResignOnTouchOutside;
}

#pragma mark - getter
- (NSArray<NSArray<NSDictionary *> *> *)floorGroups {
    if (!_floorGroups) {
        _floorGroups = [NSArray array];
    }
    return _floorGroups;
}


#pragma mark -- MTA




- (void)reportMTA{
   
    NSDictionary* responseObject = [JDISVShoppingCartService sharedInstance].responseObject;

    JDISVShoppingCartMainModel *mainModel = [JDISVShoppingCartMainModel yy_modelWithDictionary:responseObject];
    
    NSMutableArray *shopList = [NSMutableArray array];
    
    for (JDISVShoppingCartItemModel *itemModel in mainModel.rootHierarchy.subList) {
        NSString *nameSpace = @"core.trade-DM#cartView.group-M#defaultGroupItem";
        if ([itemModel.type.nameSpace isEqualToString:nameSpace]) {
            NSMutableDictionary *shopHeader = [NSMutableDictionary dictionary];
            NSDictionary* vendorDic = [itemModel.itemDict[@"info"] jdcd_getDicElementForKey:@"HJM-M#groupModel&vendor"];
            NSNumber* vendorId = [vendorDic jdcd_getNumberElementForKey:@"vendorId"];
            shopHeader[@"shopId"] = vendorId.intValue?vendorId:@(-1);
            
            for (JDISVShoppingCartItemModel *subItemModel in itemModel.subList) {
                NSMutableArray* appandgroup = [NSMutableArray array];
                NSMutableArray* buygiftgroup = [NSMutableArray array];
                
                if([subItemModel.type.nameSpace containsString:@"cartView.promotion-M#defaultPromotionItem"]){
                    NSDictionary* suit =  subItemModel.info[@"HJM-D#virtualSuit&virtualSuit"];
                    if(suit){
                        [self addVirturlSuit:subItemModel parentItem:itemModel forShop:shopHeader];
                        continue;
                    }
                    NSInteger firstProductPos = 0;
                    [self decodeJDISVShoppingCartItemModel:subItemModel
                                               fartherItem:itemModel
                                                   forShop:shopHeader
                                              forAppendant:appandgroup
                                           forBuygiftGroup:buygiftgroup
                                                  firstPos:&firstProductPos];
                    if(appandgroup.count && firstProductPos){
                        NSRange range = NSMakeRange(firstProductPos, [appandgroup count]);
                        NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:range];
                        [shopList insertObjects:appandgroup atIndexes:indexSet];
                    }
                    if(buygiftgroup.count && firstProductPos){
                        NSRange range = NSMakeRange(firstProductPos, [buygiftgroup count]);
                        NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:range];
                        [shopList insertObjects:buygiftgroup atIndexes:indexSet];
                    }
                }else{
                    NSInteger firstProductPos = 0;
                    [self decodeJDISVShoppingCartItemModel:subItemModel
                                               fartherItem:itemModel
                                                   forShop:shopHeader
                                              forAppendant:appandgroup
                                           forBuygiftGroup:buygiftgroup
                                                  firstPos:&firstProductPos];
                }
            }
            NSMutableArray* skuArr = [self skuArrFromShop:shopHeader];
            shopHeader[@"skuinfo"] = skuArr;
            [shopList addObject:shopHeader];
        }
    }
    
    
    //埋点：
    {
        [JDISVShoppingTracker PV:@"Shopcart_Main"
                           param:@{@"shopinfo":shopList,
                                   @"page_id":@"Shopcart_Main"
                                 }];
    }
}


-(void)addProductToshop:(JDISVShoppingCartItemModel*)productModel
             parentItem:(JDISVShoppingCartItemModel*)parentItem
                forShop:(NSMutableDictionary*)shopInfo
           forAppendant:(NSMutableArray*)appendGroup
        forBuygiftGroup:(NSMutableArray*)buygiftGroup
               firstPos:(NSInteger*)firstPosPoint{
    if([productModel.type.nameSpace containsString:@"#cartView.appendant-M#promotionCheapBuyModel"]){
        
        
    }else{
        NSNumber*sku =  productModel.info[@"C-M#productItem&core"][@"skuId"];
        NSNumber* select = productModel.info[@"C-M#productItem&basic"][@"activeCheck"];
        NSMutableArray*  skuArr = [self skuArrFromShop:shopInfo];
        [skuArr addObject:@{@"skuId":sku?:@"",
                            @"tick_status":select?:@(0)
                          }];
        for(JDISVShoppingCartItemModel* buygift in productModel.subList){
            NSString* giftSkuId = buygift.info[@"C-M#abstractAppendantItem&core"][@"id"];
            [skuArr addObject:@{@"skuId":giftSkuId?:@"",
                                @"tick_status":select?:@(0)
                              }];
        }
        
    }
}

-(void) decodeJDISVShoppingCartItemModel:(JDISVShoppingCartItemModel*)subItemModel
                             fartherItem:(JDISVShoppingCartItemModel*)parentItem
                                 forShop:(NSMutableDictionary*)shopInfo
                            forAppendant:(NSMutableArray*)appendGroup
                         forBuygiftGroup:(NSMutableArray*)buygiftGroup
                                firstPos:(NSInteger*)firstPosPoint{
    if ([subItemModel.type.nameSpace containsString:@"cartView.promotion-M#defaultPromotionItem"]) {
        for (JDISVShoppingCartItemModel *productModel in subItemModel.subList) {
            [self addProductToshop:productModel
                        parentItem:subItemModel
                           forShop:shopInfo
                      forAppendant:appendGroup
                   forBuygiftGroup:buygiftGroup
                          firstPos:firstPosPoint];
        }
    }else {
        [self addProductToshop:subItemModel
                    parentItem:parentItem
                       forShop:shopInfo
                  forAppendant:appendGroup
               forBuygiftGroup:buygiftGroup
                      firstPos:firstPosPoint];
    }
}

-(void)addVirturlSuit:(JDISVShoppingCartItemModel*) vsProduct
           parentItem:(JDISVShoppingCartItemModel*)parentItem
              forShop:(NSMutableDictionary*)shopInfo{
    
    NSString* sku = vsProduct.info[@"HJM-D#virtualSuit&virtualSuit"][@"vSkuId"];
    NSNumber *selected = vsProduct.info[@"HJM-D#virtualSuit&virtualSuit"][@"activeCheck"];
    NSMutableArray*  skuArr = [self skuArrFromShop:shopInfo];
    [skuArr addObject:@{@"skuId":sku?:@"",
                        @"tick_status":selected?:@(0)
                      }];
}

char* shopass = "shopass";

-(NSMutableArray*)skuArrFromShop:(NSMutableDictionary*)shop{
    NSMutableArray* result = objc_getAssociatedObject(shop, shopass);
    if(!result){
        result = [NSMutableArray array];
        objc_setAssociatedObject(shop,shopass,result,OBJC_ASSOCIATION_RETAIN);
    }
    return result;
    
}

@end
