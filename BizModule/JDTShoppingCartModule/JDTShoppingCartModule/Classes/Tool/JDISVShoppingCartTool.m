//
//  JDISVShoppingCartTool.m
//  JDISVShoppingCartSDKModule
//
//  Created by 罗静 on 2021/6/15.
//

#import "JDISVShoppingCartTool.h"

#import <JDISVPlatformModule/JDISVPlatformService.h>

@import JDTInfrastructureModule;

@implementation JDISVShoppingCartTool

+ (NSDictionary *)addressDictionary {
    JDTAddressItemModel *addressModel = [[JDISVPlatformService sharedService] getDefaultAddress];

    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if(KSAAPP){
        if (addressModel.provinceId) {
            [params setObject:addressModel.provinceId forKey:@"provinceId"];
        }
        if (addressModel.cityId) {
            [params setObject:addressModel.cityId forKey:@"cityId"];
        }
        [params setObject:@(0) forKey:@"districtId"];
        [params setObject:@(0) forKey:@"townId"];
    }else{
        if (addressModel.provinceId) {
            [params setObject:addressModel.provinceId forKey:@"provinceId"];
        }
        if (addressModel.cityId) {
            [params setObject:addressModel.cityId forKey:@"cityId"];
        }
        if (addressModel.districtId) {
            [params setObject:addressModel.districtId forKey:@"districtId"];
        }
        if (addressModel.townId) {
            [params setObject:addressModel.townId forKey:@"townId"];
        }
    }
    return params;
}

+ (NSString *)addressIds {
    return [self addressIdsWithSpace:@"-"];
}

+ (NSString *)addressIdsWithSpace:(NSString *)space {
    JDTAddressItemModel *addressModel = [[JDISVPlatformService sharedService] getDefaultAddress];
    NSMutableArray *params = [NSMutableArray array];
    if(KSAAPP){
        if (addressModel.provinceId) {
            [params addObject:addressModel.provinceId];
        }
        if (addressModel.cityId) {
            [params addObject:addressModel.cityId];
        }
        [params addObject:@(0)];
        [params addObject:@(0)];
    }else{
        if (addressModel.provinceId) {
            [params addObject:addressModel.provinceId];
        }
        if (addressModel.cityId) {
            [params addObject:addressModel.cityId];
        }
        if (addressModel.districtId) {
            [params addObject:addressModel.districtId];
        }
        if (addressModel.townId) {
            [params addObject:addressModel.townId];
        }
    }
    return [params componentsJoinedByString:space];
}

+ (NSString *)addressDecs {
    
    JDTAddressItemModel *addressModel = [[JDISVPlatformService sharedService] getDefaultAddress];
    
    if (addressModel.addressDetail) {
        return [NSString stringWithFormat:@"%@:%@",ShopingCartL(@"ka_cart_title_address"),addressModel.addressDetail];
    }
    NSMutableArray *params = [NSMutableArray array];
    if (addressModel.provinceName) {
        [params addObject:addressModel.provinceName];
    }
    if (addressModel.cityName) {
        [params addObject:addressModel.cityName];
    }
    if (addressModel.districtName) {
        [params addObject:addressModel.districtName];
    }
    if (addressModel.townName) {
        [params addObject:addressModel.townName];
    }
    return [NSString stringWithFormat:@"%@:%@",ShopingCartL(@"ka_cart_title_address"),[params componentsJoinedByString:@""]];
}

+ (CAShapeLayer *)couponTagLayerWithRect:(CGRect)rect color:(UIColor *)color topRadius:(CGFloat)topRadius bottomRedius:(CGFloat)bottomRedius {
    
    CGFloat height = rect.size.height;
    CGFloat width = rect.size.width;
    CGFloat originX = rect.origin.x;
    CGFloat originY = rect.origin.y;
    UIBezierPath *path = [UIBezierPath bezierPath];
    [path moveToPoint:CGPointMake(originX + topRadius, originY)];
    [path addArcWithCenter:CGPointMake(originX + topRadius, originY + topRadius) radius:topRadius startAngle:- M_PI_2 endAngle: M_PI clockwise:NO];
    [path addLineToPoint:CGPointMake(originX,originY + topRadius)];
    
    [path addLineToPoint:CGPointMake(originX,originY + height)];
    
    [path addLineToPoint:CGPointMake(originX + width - bottomRedius,originY + height)];
    
    [path addArcWithCenter:CGPointMake(originX + width - bottomRedius, originY + height - bottomRedius) radius:bottomRedius startAngle:M_PI_2 endAngle: 0 clockwise:NO];
    
    [path addLineToPoint:CGPointMake(originX + width,originY + height - bottomRedius)];

    [path addLineToPoint:CGPointMake(originX + width,originY)];
    
    [path closePath];
    
    CAShapeLayer *shapeLayer = [CAShapeLayer layer];
    shapeLayer.path = path.CGPath;
    shapeLayer.fillColor = color.CGColor;
    shapeLayer.frame = rect;
    return shapeLayer;
}

@end

@implementation JDISVPlatformService (JDISVShoppingCartColorService)

- (NSURLSessionDataTask *)isv_shoppingCart_request:(JDCDHTTPSessionRequestType)requestType function:(NSString *)functionId parameters:(NSDictionary *)parameters complete:(void (^)(JDCDURLTask * _Nonnull, NSDictionary * _Nullable, NSError * _Nullable))completeBlock {
    
    //"languageCode":"zh","countryCode":"CN","verticalTag":"cn_ybxt_b2c","clientVersion":"1.0.0","client":"mp","area":"510000-510100-510107-510107001","longitude":104.04332,"latitude":30.641918,
   
    NSMutableDictionary *params = [parameters mutableCopy];
    NSString* uuid  = [PlatformService getUUID];
    [params setObject:uuid forKey:@"cartuuid"];
    //加入该字段才会有库存信息
    [params setObject:@{@"stock":@(YES)} forKey:@"extendParamMap"];
    
    JDTAddressItemModel *addressModel = [[JDISVPlatformService sharedService] getDefaultAddress];
    
    if (addressModel.longitude) {
        [params setObject:addressModel.longitude forKey:@"longitude"];
    }
    if (addressModel.latitude) {
        [params setObject:addressModel.latitude forKey:@"latitude"];
    }

//    NSURLSessionDataTask *task = [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeForm paramterType:JDISVColorGateParameterTypeInternalB2C path:@"" function:functionId version:@"1.0" parameters:params complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
//        NSDictionary *data = nil;
////        responseObject = [self mockErrorData];
//        if (responseObject && [responseObject isKindOfClass:NSDictionary.class]) {
//            NSInteger code = [responseObject[@"resultCode"] integerValue];
//            if (0 == code) {
//                data = responseObject[@"resultInfo"];
//            }else {
//                NSString *message = @"";
//                if (responseObject[@"message"]) {
//                    message = responseObject[@"message"];
//                }
//                error = [NSError errorWithDomain:@"shoppingCart.error" code:code userInfo:@{NSLocalizedDescriptionKey:message}];
//            }
//        }
//        
//        if (error) {
//            if (completeBlock) {
//                completeBlock(urlTask,nil,error);
//            }
//        }else {
//            if (completeBlock) {
//                completeBlock(urlTask,data,nil);
//            }
//        }
//    }];
    
    [[OOPNetworkManager sharedManager] POST:functionId parameters:parameters headers:nil completion:^(id  _Nullable responseObject, NSError * _Nullable error) {
        NSDictionary *data = nil;
//        responseObject = [self mockErrorData];
        if (responseObject && [responseObject isKindOfClass:NSDictionary.class]) {
            NSInteger code = [responseObject[@"resultCode"] integerValue];
            if (0 == code) {
                data = responseObject[@"resultInfo"];
            }else {
                NSString *message = @"";
                if (responseObject[@"message"]) {
                    message = responseObject[@"message"];
                }
                error = [NSError errorWithDomain:@"shoppingCart.error" code:code userInfo:@{NSLocalizedDescriptionKey:message}];
            }
        }
        
        if (error) {
            if (completeBlock) {
                completeBlock(nil,nil,error);
            }
        }else {
            if (completeBlock) {
                completeBlock(nil,data,nil);
            }
        }
    }];
    
//    return task;
    return nil;
}

- (NSURLSessionDataTask *)jnos_shoppingCart_request:(JDCDHTTPSessionRequestType)requestType function:(NSString *)functionId parameters:(NSDictionary *)parameters complete:(void (^)(JDCDURLTask * _Nonnull, NSDictionary * _Nullable, NSError * _Nullable))completeBlock {
    
    //"languageCode":"zh","countryCode":"CN","verticalTag":"cn_ybxt_b2c","clientVersion":"1.0.0","client":"mp","area":"510000-510100-510107-510107001","longitude":104.04332,"latitude":30.641918,
   
    NSMutableDictionary *params = [parameters mutableCopy];
    
    //加入该字段才会有库存信息
//    [params setObject:@{@"stock":@(YES)} forKey:@"extendParamMap"];
    
    JDTAddressItemModel *addressModel = [[JDISVPlatformService sharedService] getDefaultAddress];
    
    if (addressModel.longitude) {
        [params setObject:addressModel.longitude forKey:@"longitude"];
    }
    if (addressModel.latitude) {
        [params setObject:addressModel.latitude forKey:@"latitude"];
    }

    NSURLSessionDataTask *task = [[JDISVPlatformService sharedService] request:JDCDHTTPSessionRequestTypePost requestSerializerType:JDCDURLRequestSerializerTypeJson paramterType:JDISVColorGateParameterTypeCustom path:@"" function:functionId version:@"1.0" parameters:params complete:^(JDCDURLTask * _Nonnull urlTask, id  _Nullable responseObject, NSError * _Nullable error) {
        NSDictionary *data = nil;
//        responseObject = [self mockErrorData];
        if (responseObject && [responseObject isKindOfClass:NSDictionary.class]) {
            NSInteger code = [responseObject[@"resultCode"] integerValue];
            if (0 == code) {
                data = responseObject[@"resultInfo"];
            }else {
                NSString *message = @"";
                if (responseObject[@"message"]) {
                    message = responseObject[@"message"];
                }
                error = [NSError errorWithDomain:@"shoppingCart.error" code:code userInfo:@{NSLocalizedDescriptionKey:message}];
            }
        }
        
        if (error) {
            if (completeBlock) {
                completeBlock(urlTask,nil,error);
            }
        }else {
            if (completeBlock) {
                completeBlock(urlTask,data,nil);
            }
        }
    }];
    return task;
}

- (NSDictionary *)mockErrorData {
    NSString *tempData = @"{\"message\":\"failuer\",\"resultCode\":\"0\",\"resultInfo\":{}}";
    NSData *jsonData = [tempData dataUsingEncoding:NSUTF8StringEncoding];
                
    NSError *err;
    
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                             
                                                            options:NSJSONReadingMutableContainers
                             
                                                              error:&err];
    
    return dic;
}

- (NSDictionary *)mockShopData {
    NSString *tempData = @"{\"message\":\"success\",\"resultCode\":\"200\",\"resultInfo\":{\"data\":{\"a246ce8e470d41b696a7ea6f0494cd87\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"a246ce8e470d41b696a7ea6f0494cd87\",\"partList\":[{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":101,\"promoType\":\"10\",\"activeCheck\":1,\"id\":\"************\",\"title\":\"购买1件可优惠换购热销商品\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":0,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"-300\",\"title\":\"不使用活动优惠\"}}}],\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"【健康礼盒】Swisse斯维诗 关爱长辈礼盒（钙维D片150片/瓶+鱼油胶囊400粒/瓶+卵磷脂胶囊150粒/瓶）\",\"imgUrl\":\"jfs/t1/167142/34/17971/264548/607519d9E0e2702fa/44a2c523d9f37730.jpg.dpg\",\"thirdCId\":9202,\"price\":\"488.00\",\"firstCid\":9192,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":9193},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"3\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"0.00\",\"weight\":\"2.340\",\"rePrice\":\"0.00\",\"supplierCode\":\"jdwmfyxgs\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"488.00\",\"priceShow\":\"¥488.00\",\"addCartPrice\":\"199.00\",\"iconMap\":{},\"specialId\":\"37748736\",\"propertyTagMap\":{\"color\":\"关爱长辈礼盒\"},\"brandId\":\"57779\",\"addCartTime\":1620636885471},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"100002481754\"}}},\"c91f77ad96b1486ab0ece6b9cb14a9d5\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"c91f77ad96b1486ab0ece6b9cb14a9d5\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"泰森Tyson 鸡翅中 1kg 鸡肉 鸡翅 中翅 鸡中翅 烧烤食材\",\"imgUrl\":\"jfs/t1/152028/9/3969/136038/5f9a8c40E7f1fb9f2/59b6941378e7cd7c.jpg.dpg\",\"thirdCId\":13587,\"price\":\"159.00\",\"firstCid\":12218,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":13586},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"76.10\",\"weight\":\"1.030\",\"rePrice\":\"0.00\",\"supplierCode\":\"tyson\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"82.90\",\"priceShow\":\"¥82.90\",\"addCartPrice\":\"39.90\",\"iconMap\":{},\"specialId\":\"34080768\",\"propertyTagMap\":{\"color\":\"鸡翅中1kg\"},\"brandId\":\"207238\",\"addCartTime\":1620621524698},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"2136193\"}}},\"f64f67ae762f49d2ab3cae8a353a1374\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"f64f67ae762f49d2ab3cae8a353a1374\",\"partList\":[{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":100,\"promoType\":\"10\",\"activeCheck\":1,\"id\":\"************\",\"title\":\"满99元减30元\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":102,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"************\",\"title\":\"满2件总价9折\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":0,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"-300\",\"title\":\"不使用活动优惠\"}}}],\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"雅诗兰黛（Estee Lauder）新版ANR特润修护小棕瓶精华眼霜 15ml 520礼物送女友品\",\"imgUrl\":\"jfs/t1/194267/37/2315/134931/6098d4b4Efd5b3559/d97958e67467da36.jpg.dpg\",\"thirdCId\":13547,\"price\":\"389.00\",\"firstCid\":1316,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":1381},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"3\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"30.00\",\"weight\":\"0.149\",\"rePrice\":\"0.00\",\"supplierCode\":\"jdwguoyao2\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"359.00\",\"priceShow\":\"¥359.00\",\"addCartPrice\":\"338.00\",\"iconMap\":{},\"specialId\":\"37748736\",\"propertyTagMap\":{\"color\":\"抗蓝光眼霜15ml\"},\"brandId\":\"18972\",\"addCartTime\":1620788915827},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"100000244264\"}}},\"3419878aad2d432daca9b0ca3ae30158\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"3419878aad2d432daca9b0ca3ae30158\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"多格漫 名仕系列 狗零食鸡肉长片 肉干泰迪金毛宠物零食75片\",\"imgUrl\":\"jfs/t1/156954/9/10883/132090/60349bf1E5c5cf83e/1435723865ed17a9.jpg.dpg\",\"thirdCId\":7006,\"price\":\"78.00\",\"firstCid\":6994,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":6996},\"HJM-M#cartProduct&extInfo\":{\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"0.00\",\"weight\":\"0.400\",\"rePrice\":\"0.00\",\"supplierCode\":\"yhdbjhw\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"78.00\",\"priceShow\":\"¥78.00\",\"addCartPrice\":\"59.90\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{},\"brandId\":\"6446\",\"addCartTime\":1620631860178},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"100018777240\"}}},\"48b15e457c4141ac872dda408953693e\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"48b15e457c4141ac872dda408953693e\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"One's Member 甄选鲜鸡蛋 30枚 德青源特供 1号会员店专享款\",\"imgUrl\":\"jfs/t1/136350/10/19957/118818/5fd72a07E85922d14/eebfd0b5c2914202.jpg.dpg\",\"thirdCId\":12250,\"price\":\"36.00\",\"firstCid\":12218,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":13586},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"0.00\",\"weight\":\"2.110\",\"rePrice\":\"0.00\",\"supplierCode\":\"yhddqyny\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"36.00\",\"priceShow\":\"¥36.00\",\"addCartPrice\":\"30.00\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{},\"brandId\":\"638602\",\"addCartTime\":1620621477190},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"100009685141\"}}},\"eb7e2a6476f7425fa238be0e5dff0bf4\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"eb7e2a6476f7425fa238be0e5dff0bf4\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"蒙牛 特仑苏 纯牛奶 品质牛奶 必备  250ml*16 礼盒装\",\"imgUrl\":\"jfs/t1/129404/35/14857/103597/5f86dbe0Ea114e1c4/08639d1859e1f333.jpg.dpg\",\"thirdCId\":9434,\"price\":\"88.90\",\"firstCid\":1320,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":1585},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"2.00\",\"weight\":\"4.660\",\"rePrice\":\"0.00\",\"supplierCode\":\"sksz\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"9999\",\"revertPrice\":\"86.90\",\"priceShow\":\"¥86.90\",\"addCartPrice\":\"59.90\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{\"color\":\"【高端品质】特仑苏纯奶16盒\"},\"brandId\":\"12690\",\"addCartTime\":1620463103140},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"2922989\"}}},\"f893b7c6a5314acc9dc75528506987b3\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"f893b7c6a5314acc9dc75528506987b3\",\"partList\":[{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":101,\"promoType\":\"10\",\"activeCheck\":1,\"id\":\"************\",\"title\":\"购买1件可优惠换购热销商品\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":0,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"-300\",\"title\":\"不使用活动优惠\"}}}],\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"五粮液39度500ml\",\"imgUrl\":\"jfs/t2980/90/2204015186/104920/202610f9/579fee0fNdf4d8940.jpg.dpg\",\"thirdCId\":9435,\"price\":\"799.00\",\"firstCid\":12259,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":12260},\"HJM-M#cartProduct&extInfo\":{\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"40.00\",\"weight\":\"1.680\",\"rePrice\":\"0.00\",\"supplierCode\":\"wlyxls\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"759.00\",\"priceShow\":\"¥759.00\",\"addCartPrice\":\"719.00\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{\"color\":\"五粮液39度500ml\"},\"brandId\":\"17815\",\"addCartTime\":1620458990384},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"无货\",\"stockCode\":1},\"C-M#productItem&core\":{\"skuId\":\"1020639\"}}},\"40104_949303467044065280\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.subtotalDetail-M#subtotalDetailItem\",\"version\":\"1.0.0\"},\"uuid\":\"40104_949303467044065280\",\"info\":{\"C-M#subtotalDetailItem&core\":{\"price\":\"¥0.00\",\"name\":\"商品总额\",\"type\":\"3\",\"seq\":\"0\"}}},\"d6d96e42b7f14f5d99006764223e21fc\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"d6d96e42b7f14f5d99006764223e21fc\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"海名威 冷冻格陵兰比目鱼 400g 4-5片装 核酸已检测 生鲜海鲜水产 鱼类\",\"imgUrl\":\"jfs/t1/160057/10/1688/174363/5ff44694E1a24b7e3/c9009df8fd347710.jpg.dpg\",\"thirdCId\":12241,\"price\":\"89.00\",\"firstCid\":12218,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":12222},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"20.30\",\"weight\":\"0.450\",\"rePrice\":\"0.00\",\"supplierCode\":\"fjbjx\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"68.70\",\"priceShow\":\"¥68.70\",\"iconMap\":{},\"specialId\":\"34080768\",\"propertyTagMap\":{\"color\":\"比目鱼400g\"},\"brandId\":\"202097\",\"addCartTime\":1620720268516},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"3475942\"}}},\"54b45ee9c37e412b9db6893b10d0e2ab\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"54b45ee9c37e412b9db6893b10d0e2ab\",\"partList\":[{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":101,\"promoType\":\"10\",\"activeCheck\":1,\"id\":\"************\",\"title\":\"购买1件可优惠换购热销商品\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":0,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"-300\",\"title\":\"不使用活动优惠\"}}}],\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"湖北秭归伦晚脐橙 春橙 5斤 中果 单果约150-190g 橙子生鲜 新鲜水果\",\"imgUrl\":\"jfs/t1/160495/7/20800/91718/60755bbdE3e189d1e/e30d9835e40f1b97.jpg.dpg\",\"thirdCId\":13555,\"price\":\"52.00\",\"firstCid\":12218,\"num\":\"36\",\"activeCheck\":0,\"secondCid\":12221},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"17.00\",\"weight\":\"2.750\",\"rePrice\":\"0.00\",\"supplierCode\":\"hbzjly\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"35.00\",\"priceShow\":\"¥35.00\",\"addCartPrice\":\"25.60\",\"iconMap\":{},\"specialId\":\"33554432\",\"propertyTagMap\":{},\"brandId\":\"644814\",\"addCartTime\":1620636945445},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"100020642362\"}}},\"89a448e4b4f94be38d7082dcf184dcea\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"89a448e4b4f94be38d7082dcf184dcea\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"轩尼诗（Hennessy）洋酒 VSOP干邑白兰地 700ml\",\"imgUrl\":\"jfs/t1/195987/9/1724/359442/6094e559Eb54bf948/015e1a9571b272d4.jpg.dpg\",\"thirdCId\":14738,\"price\":\"489.00\",\"firstCid\":12259,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":14715},\"HJM-M#cartProduct&extInfo\":{\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"10.00\",\"weight\":\"1.390\",\"rePrice\":\"0.00\",\"supplierCode\":\"bjdlx\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"1000\",\"revertPrice\":\"479.00\",\"priceShow\":\"¥479.00\",\"addCartPrice\":\"379.00\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{\"color\":\"【VSOP700ml】\"},\"brandId\":\"18787\",\"addCartTime\":1620636625051},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"285999\"}}},\"9dda7c3d9735455bad856ae7b6e3ddab\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.group-M#defaultGroupItem\",\"version\":\"1.0.0\"},\"uuid\":\"9dda7c3d9735455bad856ae7b6e3ddab\",\"info\":{\"HJM-M#groupModel&checkbox\":{\"enableCheck\":1,\"activeCheck\":0},\"HJM-M#groupModel&icon\":{\"iconMap\":{\"vendorHead\":\"[{\\\"code\\\":\\\"cart_002\\\",\\\"url\\\":\\\"http://m.360buyimg.com/cc/jfs/t20410/29/2109345308/664/4404f63/5b472be5Nb6057989.png\\\"}]\"}},\"C-M#abstractGroupItem&core\":{\"groupId\":-1},\"HJM-M#groupModel&description\":{\"groupName\":\"自营\",\"groupSubName\":\"1号会员店提供服务\"},\"HJM-M#groupModel&coupon\":{\"hasCoupon\":true},\"HJM-M#groupModel&vendor\":{\"vendorType\":99,\"vendorId\":8888}}},\"3865df26b50f49ef826ff39459bf1cb3\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"3865df26b50f49ef826ff39459bf1cb3\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"都乐Dole 菲律宾进口 非转基因木瓜4只装 单果重350g起 生鲜水果 健康轻食\",\"imgUrl\":\"jfs/t1/170630/25/14521/95575/605aefb1Ecc25e350/638edf6d6a4d28ed.jpg.dpg\",\"thirdCId\":17307,\"price\":\"74.90\",\"firstCid\":12218,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":12221},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"20.00\",\"weight\":\"1.880\",\"rePrice\":\"0.00\",\"supplierCode\":\"dole\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"100\",\"revertPrice\":\"54.90\",\"priceShow\":\"¥54.90\",\"addCartPrice\":\"44.80\",\"iconMap\":{},\"specialId\":\"34080768\",\"propertyTagMap\":{\"color\":\"4只装\"},\"brandId\":\"42116\",\"addCartTime\":1620718916627},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"5662386\"}}},\"d7b99dce45d74987a9e2704a25482411\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"d7b99dce45d74987a9e2704a25482411\",\"partList\":[{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":100,\"promoType\":\"10\",\"activeCheck\":1,\"id\":\"************\",\"title\":\"每满399元减100元\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":101,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"************\",\"title\":\"购买1件可优惠换购热销商品\"}}},{\"type\":{\"namespace\":\"jd_main-DM#cartView.product-P#canSelectPromotionPart\",\"version\":\"1.0.0\"},\"info\":{\"HJM-P#canSelectPromotionPart&canSelectPromotion\":{\"mfmzFullRefundType\":0,\"promoType\":\"10\",\"activeCheck\":0,\"id\":\"-300\",\"title\":\"不使用活动优惠\"}}}],\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"王小卤虎皮凤爪 卤香味200g 肉干肉脯 鸡爪 网红零食休闲食品熟食即食下酒菜办公室零食小吃\",\"imgUrl\":\"jfs/t1/148704/7/11546/185226/5f8fd6bcE72eb3417/47b8fd6c33c9d2ab.jpg.dpg\",\"thirdCId\":1592,\"price\":\"29.80\",\"firstCid\":1320,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":1583},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"0.00\",\"weight\":\"0.230\",\"rePrice\":\"0.00\",\"supplierCode\":\"xydz\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"200\",\"revertPrice\":\"29.80\",\"priceShow\":\"¥29.80\",\"addCartPrice\":\"19.90\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{\"color\":\"王小卤 虎皮凤爪 卤香味200g\"},\"brandId\":\"316623\",\"addCartTime\":1620636624997},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"100014336380\"}}},\"40103_949303468411408411\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.subtotal-M#defaultSubtotalItem\",\"version\":\"1.0.0\"},\"uuid\":\"40103_949303468411408411\",\"info\":{\"HJM-D#subtotal&number\":{\"singleProductLimitNum\":1000,\"noneBookLimitNum\":200,\"selectedProductNum\":\"0\",\"bookLimitNum\":1000,\"productKindLimitNum\":50,\"productKindNum\":14,\"productNum\":\"53\",\"settlementLimitNum\":110},\"HJM-D#subtotal&other\":{\"enableCheck\":0,\"cartClearShow\":true,\"activeCheck\":0,\"cartAddClearGuide\":0,\"settlementGroup\":{}},\"C-M#subtotalItem&core\":{\"totalPrice\":\"0\"},\"HJM-D#subtotal&price\":{\"totalReprice\":\"0\",\"totalDiscount\":\"0\",\"priceShow\":\"¥0.00\"},\"VCRYP-M#YHDPrimeSubtotalModel&yhdPrime\":{\"preferentialSummary\":{\"totalPrice\":\"¥0.00\"}}}},\"34b4e41e906e4425b4c53570b88b4ebc\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"34b4e41e906e4425b4c53570b88b4ebc\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"蒙牛 低脂高钙牛奶 礼品 250ml*24 礼盒装\",\"imgUrl\":\"jfs/t1/57738/34/1284/521186/5cf109d9E39784ea8/0204a2eee8e536f1.jpg.dpg\",\"thirdCId\":9434,\"price\":\"79.00\",\"firstCid\":1320,\"num\":\"1\",\"activeCheck\":0,\"secondCid\":1585},\"HJM-M#cartProduct&extInfo\":{\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"2.20\",\"weight\":\"6.690\",\"rePrice\":\"0.00\",\"supplierCode\":\"sksz\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"5\",\"revertPrice\":\"76.80\",\"priceShow\":\"¥76.80\",\"addCartPrice\":\"52.00\",\"iconMap\":{},\"specialId\":\"34078720\",\"propertyTagMap\":{\"color\":\"【低脂爆款】低脂高钙24盒\"},\"brandId\":\"12690\",\"addCartTime\":1620621517018},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"1837504\"}}},\"eb279836e5334fc89187462dc86e7d50\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.group-M#defaultGroupItem\",\"version\":\"1.0.0\"},\"uuid\":\"eb279836e5334fc89187462dc86e7d50\",\"info\":{\"HJM-M#groupModel&checkbox\":{\"enableCheck\":0,\"activeCheck\":0},\"HJM-M#groupModel&icon\":{\"iconMap\":{\"vendorHead\":\"[{\\\"code\\\":\\\"cart_002\\\",\\\"url\\\":\\\"http://m.360buyimg.com/cc/jfs/t20410/29/2109345308/664/4404f63/5b472be5Nb6057989.png\\\"}]\"}},\"C-M#abstractGroupItem&core\":{\"groupId\":-1},\"HJM-M#groupModel&description\":{\"groupName\":\"跨境进口\",\"groupSubName\":\"京东国际提供服务\"},\"HJM-M#groupModel&coupon\":{\"hasCoupon\":true},\"HJM-M#groupModel&vendor\":{\"vendorType\":3,\"vendorId\":8899}}},\"a29d6f78eab345f4a3b98b980c5d1aa8\":{\"type\":{\"namespace\":\"core.trade-DM#cartView.product-M#productItem\",\"version\":\"1.0.0\"},\"uuid\":\"a29d6f78eab345f4a3b98b980c5d1aa8\",\"info\":{\"C-M#productItem&basic\":{\"skuName\":\"进口牛油果 中果6粒装 单果重130g起 生鲜水果 健康轻食\",\"imgUrl\":\"jfs/t1/84705/35/8574/504204/5e0566e8E55dafba8/f6d258e3c886fba8.jpg.dpg\",\"thirdCId\":13564,\"price\":\"69.90\",\"firstCid\":12218,\"num\":\"5\",\"activeCheck\":0,\"secondCid\":12221},\"HJM-M#cartProduct&extInfo\":{\"overseaPurchaseFlag\":\"0\",\"enableCheck\":0,\"itemType\":1,\"provideServiceFlag\":0,\"discount\":\"10.00\",\"weight\":\"1.200\",\"rePrice\":\"0.00\",\"supplierCode\":\"shjgsp\",\"imgDomain\":\"http://m.360buyimg.com/null\",\"maxBuyNum\":\"100\",\"revertPrice\":\"59.90\",\"priceShow\":\"¥59.90\",\"addCartPrice\":\"36.80\",\"iconMap\":{},\"specialId\":\"34080768\",\"propertyTagMap\":{\"color\":\"经典中果 6粒装\"},\"brandId\":\"331644\",\"addCartTime\":1620467785193},\"HJM-M#cartProduct&stockInfo\":{\"remainNum\":-1,\"stockType\":1,\"stockCodeDesc\":\"有货\",\"stockCode\":0},\"C-M#productItem&core\":{\"skuId\":\"3628240\"}}}},\"hierarchy\":{\"root\":[\"40104_949303467044065280\",\"9dda7c3d9735455bad856ae7b6e3ddab\",\"40103_949303468411408411\",\"eb279836e5334fc89187462dc86e7d50\"],\"a5bb1a2f4e6e4cdc918c4129e36fc1ad\":[\"f893b7c6a5314acc9dc75528506987b3\"],\"9dda7c3d9735455bad856ae7b6e3ddab\":[\"d6d96e42b7f14f5d99006764223e21fc\",\"3865df26b50f49ef826ff39459bf1cb3\",\"48899d423c00451e8a4abacee1f2606d\",\"89a448e4b4f94be38d7082dcf184dcea\",\"a7b34007f9584ea8b3588d9b0ceb681b\",\"3419878aad2d432daca9b0ca3ae30158\",\"c91f77ad96b1486ab0ece6b9cb14a9d5\",\"34b4e41e906e4425b4c53570b88b4ebc\",\"48b15e457c4141ac872dda408953693e\",\"a29d6f78eab345f4a3b98b980c5d1aa8\",\"eb7e2a6476f7425fa238be0e5dff0bf4\",\"a5bb1a2f4e6e4cdc918c4129e36fc1ad\"],\"0c949e57fefc4cdbad950331451844d5\":[\"f64f67ae762f49d2ab3cae8a353a1374\"],\"574c4fcb2797495a8c8f17d9719dd5e6\":[\"a246ce8e470d41b696a7ea6f0494cd87\"],\"a7b34007f9584ea8b3588d9b0ceb681b\":[\"d7b99dce45d74987a9e2704a25482411\"],\"eb279836e5334fc89187462dc86e7d50\":[\"0c949e57fefc4cdbad950331451844d5\",\"574c4fcb2797495a8c8f17d9719dd5e6\"],\"48899d423c00451e8a4abacee1f2606d\":[\"54b45ee9c37e412b9db6893b10d0e2ab\"]}}}";
    
    NSData *jsonData = [tempData dataUsingEncoding:NSUTF8StringEncoding];
                
    NSError *err;
    
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                             
                                                            options:NSJSONReadingMutableContainers
                             
                                                              error:&err];
    
    return dic;
}

@end

@implementation NSBundle (JDISVShoppingCartBundle)

+ (NSBundle *)isv_shoppingCart_bundle {
    return [NSBundle isv_shoppingCart_moduleWithBundleName:@"JDTShoppingCartModule" class:JDISVShoppingCartTool.class];
}

+ (NSBundle *)isv_shoppingCart_moduleWithBundleName:(NSString *)name class:(Class)aClass {
    NSBundle *baseBundle = [NSBundle bundleForClass:aClass];
    NSURL *url = [baseBundle URLForResource:name withExtension:@"bundle"];
    
    if (url) {
        NSBundle *bdl = [NSBundle bundleWithURL:url];
        
        return bdl;
    }
    
    return [NSBundle mainBundle];
}


@end

@implementation UIImage (JDISVShoppingCartImage)

+ (UIImage *)isv_shoppingCart_imageWithName:(NSString *)imageName {
    return [UIImage imageNamed:imageName inBundle:[NSBundle isv_shoppingCart_bundle] compatibleWithTraitCollection:nil];
}

+ (UIImage *)isv_shoppingCart_resizableImageWithName:(NSString *)imageName insets:(UIEdgeInsets)inset {
    UIImage *bgOriginImage = [UIImage imageNamed:imageName inBundle:[NSBundle isv_shoppingCart_bundle] compatibleWithTraitCollection:nil];
    UIImage *bgIamge = [bgOriginImage resizableImageWithCapInsets:inset resizingMode:UIImageResizingModeStretch];
    return bgIamge;
}

@end

@implementation UIWindow (JDISVShoppingCartSafeArea)

+ (UIEdgeInsets)isv_shoppingCart_safeAreaInsets {
    if (@available(iOS 11.0, *)) {
        UIEdgeInsets safeAreaInsets = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
        if (safeAreaInsets.bottom > 0) {
            return safeAreaInsets;
        }
        return UIEdgeInsetsMake(20, 0, 0, 0);
    }
    return UIEdgeInsetsMake(20, 0, 0, 0);
}

+ (CGFloat)isv_shoppingCart_navigationHeight {
    CGFloat statusBarHeight = [self isv_shoppingCart_safeAreaInsets].top;
    return statusBarHeight + 44;
}

+ (CGFloat)isv_shoppingCart_tabbarHeight {
    CGFloat statusBarHeight = [self isv_shoppingCart_safeAreaInsets].bottom;
//    return statusBarHeight + 49;
    return statusBarHeight;
}

@end
