platform :ios, '13.0'

install! 'cocoapods',
  preserve_pod_file_structure: true,    # 保持库的原有目录结构
  generate_multiple_pod_projects: true  # 每个 Pod 独立项目

target 'JDTECommerce' do
  
  use_frameworks! :linkage => :static

  # 基础-JDT
  pod 'JDTCommonToolModule', :path => './BaseModule/JDTCommonToolModule'
  pod 'JDTInfrastructureModule', :path => './BaseModule/JDTInfrastructureModule'
  # 基础-JDISV
  pod 'JDISVAlertModule', :path => './BaseModule/JDISV/JDISVAlertModule'
  pod 'JDISVCategoryModule', :path => './BaseModule/JDISV/JDISVCategoryModule'
  pod 'JDISVDiskCacheModule', :path => './BaseModule/JDISV/JDISVDiskCacheModule'
  pod 'JDISVFloorRenderModule', :path => './BaseModule/JDISV/JDISVFloorRenderModule'
  pod 'JDISVImageModule', :path => './BaseModule/JDISV/JDISVImageModule'
  pod 'JDISVJDVMPlayerModule', :path => './BaseModule/JDISV/JDISVJDVMPlayerModule'
  pod 'JDISVKAIconFontModule', :path => './BaseModule/JDISV/JDISVKAIconFontModule'
  pod 'JDISVKAUIKitModule', :path => './BaseModule/JDISV/JDISVKAUIKitModule'
  pod 'JDISVNetworkModule', :path => './BaseModule/JDISV/JDISVNetworkModule'
  pod 'JDISVPermissionsModule', :path => './BaseModule/JDISV/JDISVPermissionsModule'
  pod 'JDISVPlatformModule', :path => './BaseModule/JDISV/JDISVPlatformModule'
  pod 'JDISVThemeModule', :path => './BaseModule/JDISV/JDISVThemeModule'
  pod 'JDISVTimeServiceModule', :path => './BaseModule/JDISV/JDISVTimeServiceModule'
  pod 'JDISVUIKitModule', :path => './BaseModule/JDISV/JDISVUIKitModule'
  pod 'ZodeThirdPartLib', :path => './BaseModule/ZodeThirdPartLib'
  pod 'ZodeJDB', :path => './BaseModule/JDB'
  
  # 业务
  pod 'JDTProductDetailModule', :path => './BizModule/JDTProductDetailModule'
  pod 'JDTShoppingCartModule', :path => './BizModule/JDTShoppingCartModule'
  pod 'JDTSettlementModule', :path => './BizModule/JDTSettlementModule'
  
  # 三方库
  pod 'LookinServer', :configurations => ['Debug']
  

  post_install do |installer|
    installer.generated_projects.each do |project|
      project.targets.each do |target|
        target.build_configurations.each do |config|
          config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
        end
      end
    end
  end

end
